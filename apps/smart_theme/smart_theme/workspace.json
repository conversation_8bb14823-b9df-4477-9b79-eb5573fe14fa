[{"charts": [], "content": "[{\"id\":\"pWtaBoDWMh\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\">Exchange</span>\",\"col\":12}},{\"id\":\"SePf1pD4Be\",\"type\":\"card\",\"data\":{\"card_name\":\"exchange masters\",\"col\":5}},{\"id\":\"Um4izsfAyS\",\"type\":\"card\",\"data\":{\"card_name\":\"regions and branches\",\"col\":5}},{\"id\":\"oHI3C4sUJf\",\"type\":\"card\",\"data\":{\"card_name\":\"Exchange setting\",\"col\":5}},{\"id\":\"pguOpbXjMg\",\"type\":\"card\",\"data\":{\"card_name\":\"Transfer Operations\",\"col\":5}},{\"id\":\"dxZYG1GNtP\",\"type\":\"card\",\"data\":{\"card_name\":\"Bound and Entry\",\"col\":5}},{\"id\":\"tDHEjz96Wt\",\"type\":\"card\",\"data\":{\"card_name\":\"Payment Entry\",\"col\":5}}]", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "change", "indicator_color": "light-blue", "is_hidden": 0, "label": "Exchange", "links": [{"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "regions and branches", "link_count": 4, "link_to": null, "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Card Break"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Country", "link_count": 0, "link_to": "Country", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "City", "link_count": 0, "link_to": "City", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Regions", "link_count": 0, "link_to": "Ex Region", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Branches", "link_count": 0, "link_to": "Ex Branch", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "exchange masters", "link_count": 3, "link_to": null, "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Card Break"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Customers", "link_count": 0, "link_to": "Customers", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Targets", "link_count": 0, "link_to": "Extarget", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Account <PERSON>ting", "link_count": 0, "link_to": "Exchange Account", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Exchange setting", "link_count": 2, "link_to": null, "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Card Break"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Transfer Types", "link_count": 0, "link_to": "Transfer Types", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Transfer Purpose", "link_count": 0, "link_to": "Transfer Purpose", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Payment Entry", "link_count": 2, "link_to": null, "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Card Break"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Journal Entry", "link_count": 0, "link_to": "Journal Entry", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Payment Entry", "link_count": 0, "link_to": "Payment Entry", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Transfer Operations", "link_count": 2, "link_to": null, "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Card Break"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Receipt / Import Transfer", "link_count": 0, "link_to": "Receipt Transfer", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Pay / Export Transfer", "link_count": 0, "link_to": "Transfer Payment", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Bound and Entry", "link_count": 3, "link_to": null, "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Card Break"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Pay Entry", "link_count": 0, "link_to": "Pay Entry", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Receipt Entry", "link_count": 0, "link_to": "Receipt Entry", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}, {"dependencies": null, "description": null, "hidden": 0, "icon": null, "is_query_report": 0, "label": "Simple Entry", "link_count": 0, "link_to": "Simple Entry", "link_type": "DocType", "onboard": 0, "only_for": null, "parent": "Exchange", "parentfield": "links", "parenttype": "Workspace", "type": "Link"}], "modified": "2024-03-26 07:01:18.927957", "module": "Exchange2024", "name": "Exchange", "number_cards": [], "parent_page": "", "public": 1, "quick_lists": [], "restrict_to_domain": null, "roles": [], "sequence_id": 2.0, "shortcuts": [], "title": "Exchange"}]