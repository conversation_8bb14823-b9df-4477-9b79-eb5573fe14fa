app_name = "smart_theme"
app_title = "Smart ERPNext Theme"
app_publisher = "newsmart.tech"
app_description = "Advanced comprehensive theme for ERPNext with modern features, animations, and responsive design"
app_email = "<EMAIL>"
app_license = "mit"
app_logo_url = "/assets/smart_theme/logo/logoRect.png"
app_version = "2.0.0"

# required_apps = []

# Includes in <head>
# ------------------

# include js, css files in header of desk.html
app_include_css = [
    "/assets/smart_theme/css/smart_theme.css",
    "/assets/smart_theme/css/animations.css",
    "/assets/smart_theme/css/components.css",
    "/assets/smart_theme/css/responsive.css"
]

app_include_js = [
    "/assets/smart_theme/js/smart_theme.js",
    "/assets/smart_theme/js/animations.js",
    "/assets/smart_theme/js/theme_switcher.js"
]

website_context = {
    "splash_image": "/assets/smart_theme/logo/splash.png",
    "favicon": '/assets/smart_theme/favicon.ico',
    "brand_html": '<img class="navbar-brand-img" src="/assets/smart_theme/logo/logoRect.png" alt="Smart Theme">',
}
# include js, css files in header of web template
web_include_css = [
    "/assets/smart_theme/css/website.css",
    "/assets/smart_theme/css/web_animations.css"
]
web_include_js = [
    "/assets/smart_theme/js/website.js",
    "/assets/smart_theme/js/web_interactions.js"
]

# include custom scss in every website theme (without file extension ".scss")
website_theme_scss = "smart_theme/public/scss/website"

# include js, css files in header of web form
webform_include_js = {"doctype": "public/js/webform_enhancements.js"}
webform_include_css = {"doctype": "public/css/webform_styling.css"}

# include js in page
page_js = {
    "setup-wizard": "public/js/setup_wizard.js",
    "dashboard": "public/js/dashboard_enhancements.js"
}

# include js in doctype views
doctype_js = {
    "Customer": "public/js/customer_enhancements.js",
    "Sales Invoice": "public/js/sales_invoice_enhancements.js",
    "Item": "public/js/item_enhancements.js"
}

doctype_list_js = {
    "Customer": "public/js/customer_list.js",
    "Sales Invoice": "public/js/sales_invoice_list.js"
}

doctype_tree_js = {
    "Item Group": "public/js/item_group_tree.js",
    "Customer Group": "public/js/customer_group_tree.js"
}

doctype_calendar_js = {
    "Event": "public/js/event_calendar.js",
    "Task": "public/js/task_calendar.js"
}

# Svg Icons
# ------------------
# include app icons in desk
app_include_icons = "smart_theme/public/icons.svg"

# Home Pages
# ----------

# application home page (will override Website Settings)
home_page = "smart_dashboard"

# website user home page (by Role)
role_home_page = {
    "System Manager": "smart_dashboard",
    "Sales Manager": "sales_dashboard",
    "Purchase Manager": "purchase_dashboard",
    "Accounts Manager": "accounts_dashboard"
}

# Custom Pages
# ------------
# Add custom pages for enhanced dashboard experience
page_js = {
    "smart_dashboard": "public/js/smart_dashboard.js",
    "sales_dashboard": "public/js/sales_dashboard.js",
    "purchase_dashboard": "public/js/purchase_dashboard.js",
    "accounts_dashboard": "public/js/accounts_dashboard.js"
}

# Generators
# ----------

# automatically create page for each record of this doctype
# website_generators = ["Web Page"]

# Jinja
# ----------

# add methods and filters to jinja environment
jinja = {
    "methods": "smart_theme.utils.jinja_methods",
    "filters": "smart_theme.utils.jinja_filters"
}

# Installation
# ------------

before_install = "smart_theme.install.before_install"
after_install = "smart_theme.install.after_install"

# Uninstallation
# ------------

# before_uninstall = "smart_theme.uninstall.before_uninstall"
# after_uninstall = "smart_theme.uninstall.after_uninstall"

# Integration Setup
# ------------------
# To set up dependencies/integrations with other apps
# Name of the app being installed is passed as an argument

# before_app_install = "smart_theme.utils.before_app_install"
# after_app_install = "smart_theme.utils.after_app_install"

# Integration Cleanup
# -------------------
# To clean up dependencies/integrations with other apps
# Name of the app being uninstalled is passed as an argument

# before_app_uninstall = "smart_theme.utils.before_app_uninstall"
# after_app_uninstall = "smart_theme.utils.after_app_uninstall"

# Desk Notifications
# ------------------
# See frappe.core.notifications.get_notification_config

# notification_config = "smart_theme.notifications.get_notification_config"

# Permissions
# -----------
# Permissions evaluated in scripted ways

# permission_query_conditions = {
# 	"Event": "frappe.desk.doctype.event.event.get_permission_query_conditions",
# }
#
# has_permission = {
# 	"Event": "frappe.desk.doctype.event.event.has_permission",
# }

# DocType Class
# ---------------
# Override standard doctype classes

# override_doctype_class = {
# 	"ToDo": "custom_app.overrides.CustomToDo"
# }

# Document Events
# ---------------
# Hook on document methods and events

# doc_events = {
# 	"*": {
# 		"on_update": "method",
# 		"on_cancel": "method",
# 		"on_trash": "method"
# 	}
# }

# Scheduled Tasks
# ---------------

# scheduler_events = {
# 	"all": [
# 		"smart_theme.tasks.all"
# 	],
# 	"daily": [
# 		"smart_theme.tasks.daily"
# 	],
# 	"hourly": [
# 		"smart_theme.tasks.hourly"
# 	],
# 	"weekly": [
# 		"smart_theme.tasks.weekly"
# 	],
# 	"monthly": [
# 		"smart_theme.tasks.monthly"
# 	],
# }

# Testing
# -------

# before_tests = "smart_theme.install.before_tests"

# Overriding Methods
# ------------------------------
#
# override_whitelisted_methods = {
# 	"frappe.desk.doctype.event.event.get_events": "smart_theme.event.get_events"
# }
#
# each overriding function accepts a `data` argument;
# generated from the base implementation of the doctype dashboard,
# along with any modifications made in other Frappe apps
# override_doctype_dashboards = {
# 	"Task": "smart_theme.task.get_dashboard_data"
# }

# exempt linked doctypes from being automatically cancelled
#
# auto_cancel_exempted_doctypes = ["Auto Repeat"]

# Ignore links to specified DocTypes when deleting documents
# -----------------------------------------------------------

# ignore_links_on_delete = ["Communication", "ToDo"]

# Request Events
# ----------------
# before_request = ["smart_theme.utils.before_request"]
# after_request = ["smart_theme.utils.after_request"]

# Job Events
# ----------
# before_job = ["smart_theme.utils.before_job"]
# after_job = ["smart_theme.utils.after_job"]

# User Data Protection
# --------------------

# user_data_fields = [
# 	{
# 		"doctype": "{doctype_1}",
# 		"filter_by": "{filter_by}",
# 		"redact_fields": ["{field_1}", "{field_2}"],
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_2}",
# 		"filter_by": "{filter_by}",
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_3}",
# 		"strict": False,
# 	},
# 	{
# 		"doctype": "{doctype_4}"
# 	}
# ]

# Authentication and authorization
# --------------------------------

# auth_hooks = [
# 	"smart_theme.auth.validate"
# ]

# Automatically update python controller files with type annotations for this app.
# export_python_type_annotations = True

# default_log_clearing_doctypes = {
# 	"Logging DocType Name": 30  # days to retain logs
# }
#fixtures = ['Translation']
#fixtures = [{"dt": "Workspace", "filters": [
#[
#"name", "in", ["Exchange",]
#]
#]},'Translation']
#!/bin/bash
# echo "🧹 تنظيف Smart Theme بالكامل..."

# # إلغاء تثبيت التطبيق
# bench --site site1.local uninstall-app smart_theme

# # مسح الملفات المتبقية
# rm -rf sites/assets/smart_theme
# rm -rf sites/site1.local/public/files/smart_theme

# # مسح Cache
# bench --site site1.local clear-cache
# bench --site site1.local clear-website-cache

# # إعادة بناء الأصول
# bench --site site1.local build --force

# # إعادة تشغيل النظام
# bench restart

# echo "✅ تم تنظيف Smart Theme بالكامل"