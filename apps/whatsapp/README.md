# WhatsApp Integration for ERPNext

This app provides WhatsApp integration for ERPNext without requiring Meta's official API, which means no subscription is required.

## Features

- Send WhatsApp messages automatically on document events (e.g., on submit, on update, on cancel)
- Send images, PDF files, and links when needed (e.g., send a copy of an invoice as PDF)
- Use the open-source whatsapp-web.js library to communicate with WhatsApp without Meta API
- Manage and sync WhatsApp groups within ERPNext
- Support multiple WhatsApp numbers, with the ability to link specific numbers to specific modules

## Installation

### Prerequisites

- Node.js 14+ and npm
- ERPNext 14+
- Frappe Framework 14+

### Steps

1. Install the app using Bench:

```bash
bench get-app https://github.com/your-username/whatsapp
bench --site your-site install-app whatsapp
```

2. Install Node.js dependencies for the WhatsApp server:

```bash
cd /path/to/frappe-bench/apps/whatsapp/whatsapp/whatsapp/server
npm install
```

3. Start the WhatsApp server:

```bash
node server.js
```

4. Or install as a systemd service:

```bash
sudo cp /path/to/frappe-bench/apps/whatsapp/whatsapp/whatsapp/server/whatsapp-server.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable whatsapp-server
sudo systemctl start whatsapp-server
```

## Configuration

1. Go to WhatsApp Settings and enable WhatsApp integration
2. Create a WhatsApp Sender Account
3. Scan the QR code with your WhatsApp mobile app to connect
4. Create Message Templates for different document types
5. Set up Trigger Settings to automatically send messages on document events

## DocTypes

- **WhatsApp Settings**: Global settings for the WhatsApp integration
- **WhatsApp Sender Account**: Manage WhatsApp accounts/numbers
- **WhatsApp Message Template**: Create templates for messages with variables
- **WhatsApp Group**: Manage WhatsApp groups
- **WhatsApp Message Log**: Track sent and received messages
- **WhatsApp Trigger Settings**: Configure automatic message sending on document events

## Usage

### Sending Messages Manually

1. Open any document that has a mobile number field
2. Click on "Actions" > "Send WhatsApp"
3. Select a sender account and message template
4. Click "Send"

### Setting Up Automatic Messages

1. Go to WhatsApp Trigger Settings
2. Create a new trigger with:
   - DocType (e.g., Sales Invoice)
   - Event (e.g., on_submit)
   - Sender Account
   - Recipient Field (e.g., contact_mobile)
   - Message Template
3. Enable the trigger

### Managing Groups

1. Go to WhatsApp Groups
2. Create a new group linked to a sender account
3. Click "Sync Members" to fetch members from WhatsApp
4. Send messages to the group

## License

MIT