# Copyright (c) 2023, moneer and contributors
# For license information, please see license.txt

import frappe
from frappe import _
import os
import shutil
import subprocess

def after_install():
    """Setup WhatsApp integration after app installation"""
    # Create default WhatsApp settings
    if not frappe.db.exists("DocType", "WhatsApp Settings"):
        return
        
    if not frappe.db.get_single_value("WhatsApp Settings", "whatsapp_server_url"):
        settings = frappe.get_doc("WhatsApp Settings")
        settings.enable_whatsapp = 0
        settings.whatsapp_server_url = "http://localhost"
        settings.whatsapp_server_port = 8085
        settings.log_retention_days = 30
        settings.debug_mode = 0
        settings.save()
        
    # Create server directory if it doesn't exist
    app_path = frappe.get_app_path("whatsapp")
    server_dir = os.path.join(app_path, "whatsapp", "server")
    
    if not os.path.exists(server_dir):
        os.makedirs(server_dir, exist_ok=True)
        
    # Create sessions directory
    sessions_dir = os.path.join(server_dir, "sessions")
    if not os.path.exists(sessions_dir):
        os.makedirs(sessions_dir, exist_ok=True)
        
    # Print installation message
    print("\n" + "-" * 80)
    print("WhatsApp Integration has been installed successfully!")
    print("\nTo complete the setup:")
    print("1. Go to WhatsApp Settings and enable WhatsApp integration")
    print("2. Install Node.js dependencies for the WhatsApp server:")
    print(f"   cd {server_dir} && npm install")
    print("3. Start the WhatsApp server:")
    print(f"   cd {server_dir} && node server.js")
    print("4. Or install as a systemd service:")
    print(f"   sudo cp {os.path.join(server_dir, 'whatsapp-server.service')} /etc/systemd/system/")
    print("   sudo systemctl daemon-reload")
    print("   sudo systemctl enable whatsapp-server")
    print("   sudo systemctl start whatsapp-server")
    print("-" * 80 + "\n")
    
def before_uninstall():
    """Cleanup before app uninstallation"""
    # Stop WhatsApp server if running
    try:
        subprocess.run(["sudo", "systemctl", "stop", "whatsapp-server"], 
                      stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        subprocess.run(["sudo", "systemctl", "disable", "whatsapp-server"], 
                      stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        subprocess.run(["sudo", "rm", "/etc/systemd/system/whatsapp-server.service"], 
                      stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        subprocess.run(["sudo", "systemctl", "daemon-reload"], 
                      stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    except Exception as e:
        print(f"Error stopping WhatsApp server: {str(e)}")
        
    print("\n" + "-" * 80)
    print("WhatsApp Integration has been uninstalled.")
    print("You may need to manually remove the WhatsApp server files and sessions.")
    print("-" * 80 + "\n")
