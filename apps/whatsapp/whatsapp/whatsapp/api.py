# Copyright (c) 2023, moneer and contributors
# For license information, please see license.txt

import frappe
from frappe import _
import requests
import json
from datetime import datetime

@frappe.whitelist()
def update_account_status(account_id, status, qr_code=None):
    """Update WhatsApp account status from the server"""
    try:
        account = frappe.get_doc("WhatsApp Sender Account", account_id)
        account.status = status

        if status == "Connected":
            account.last_connected = datetime.now()

        if qr_code and status == "Pending":
            account.qr_code_html = f"""
            <div style="text-align: center; padding: 20px;">
                <img src="{qr_code}" alt="WhatsApp QR Code" style="width: 256px; height: 256px;">
                <p>Scan this QR code with your WhatsApp mobile app</p>
            </div>
            """

        account.save()
        frappe.db.commit()

        return {"success": True}
    except Exception as e:
        frappe.log_error(f"Error updating WhatsApp account status: {str(e)}")
        return {"success": False, "error": str(e)}

@frappe.whitelist()
def log_incoming_message(account_id, message_id, from_number, body, has_media=False):
    """Log incoming WhatsApp message"""
    try:
        # Clean up phone number
        from_number = from_number.replace("@c.us", "")

        # Create message log
        log = frappe.new_doc("WhatsApp Message Log")
        log.sender_account = account_id
        log.recipient = from_number
        log.message = body
        log.status = "Received"
        log.sent_time = datetime.now()
        log.delivered_time = datetime.now()
        log.read_time = datetime.now()

        if has_media:
            log.has_attachments = 1

        log.save()
        frappe.db.commit()

        return {"success": True, "message_log": log.name}
    except Exception as e:
        frappe.log_error(f"Error logging incoming WhatsApp message: {str(e)}")
        return {"success": False, "error": str(e)}

@frappe.whitelist()
def connect_account(account_id):
    """Initialize WhatsApp connection for an account"""
    try:
        account = frappe.get_doc("WhatsApp Sender Account", account_id)
        settings = frappe.get_single("WhatsApp Settings")

        if not settings.enable_whatsapp:
            frappe.throw(_("WhatsApp integration is not enabled"))

        # Get server URL
        server_url = settings.get_server_url()

        try:
            # Initialize client
            response = requests.post(
                f"{server_url}/api/init",
                json={"accountId": account_id},
                timeout=10
            )

            if response.status_code == 200:
                frappe.msgprint(_("WhatsApp server connected successfully"))
            else:
                frappe.log_error(f"Warning: Server returned status {response.status_code}: {response.text}")
        except Exception as server_error:
            frappe.log_error(f"Warning: Could not connect to WhatsApp server: {str(server_error)}")
            frappe.msgprint(_("Warning: Could not connect to WhatsApp server. Using static QR code."))

        # Get QR code URL directly
        qr_code_url = f"{server_url}/whatsapp-qr.png"

        # Update account status and QR code
        account.status = "Pending"
        account.qr_code_html = f"""
        <div style="text-align: center; padding: 20px; background: #f9f9f9; border-radius: 10px;">
            <h3 style="color: #25D366; margin-bottom: 15px;">رمز QR للاتصال بـ WhatsApp</h3>
            <img src="{qr_code_url}" alt="WhatsApp QR Code" style="width: 256px; height: 256px; border: 2px solid #25D366; border-radius: 10px;">
            <p style="margin-top: 15px; color: #666;">امسح رمز QR باستخدام تطبيق WhatsApp على هاتفك</p>
            <p style="font-size: 12px; color: #999;">انتقل إلى WhatsApp > النقاط الثلاث > الأجهزة المرتبطة > ربط جهاز</p>
        </div>
        """
        account.save()

        return {"success": True}
    except Exception as e:
        frappe.log_error(f"Error connecting WhatsApp account: {str(e)}")
        return {"success": False, "error": str(e)}

@frappe.whitelist()
def disconnect_account(account_id):
    """Disconnect WhatsApp account"""
    try:
        account = frappe.get_doc("WhatsApp Sender Account", account_id)
        settings = frappe.get_single("WhatsApp Settings")

        # Get server URL
        server_url = settings.get_server_url()

        # Disconnect client
        response = requests.post(
            f"{server_url}/api/disconnect/{account_id}",
            timeout=30
        )

        # Update account status regardless of server response
        account.status = "Disconnected"
        account.save()

        return {"success": True}
    except Exception as e:
        frappe.log_error(f"Error disconnecting WhatsApp account: {str(e)}")
        return {"success": False, "error": str(e)}

@frappe.whitelist()
def send_message(account_id, to, message, attachment_url=None):
    """Send WhatsApp message"""
    try:
        account = frappe.get_doc("WhatsApp Sender Account", account_id)
        settings = frappe.get_single("WhatsApp Settings")

        if not settings.enable_whatsapp:
            frappe.throw(_("WhatsApp integration is not enabled"))

        if account.status != "Connected":
            frappe.throw(_("WhatsApp account is not connected"))

        # Get server URL
        server_url = settings.get_server_url()

        # Prepare request data
        data = {
            "accountId": account_id,
            "to": to,
            "message": message
        }

        if attachment_url:
            data["attachmentUrl"] = attachment_url

        # Send message
        response = requests.post(
            f"{server_url}/api/send",
            json=data,
            timeout=30
        )

        if response.status_code != 200:
            frappe.throw(_("Failed to send WhatsApp message: {0}").format(response.text))

        # Create message log
        log = frappe.new_doc("WhatsApp Message Log")
        log.sender_account = account_id
        log.recipient = to
        log.message = message
        log.status = "Sent"
        log.sent_time = datetime.now()

        if attachment_url:
            log.has_attachments = 1
            log.append("attachments", {
                "file_url": attachment_url,
                "file_name": attachment_url.split("/")[-1]
            })

        log.save()

        return {"success": True, "message_log": log.name}
    except Exception as e:
        frappe.log_error(f"Error sending WhatsApp message: {str(e)}")
        return {"success": False, "error": str(e)}

@frappe.whitelist()
def get_default_sender_account():
    """Get the default WhatsApp sender account"""
    try:
        # البحث عن أول حساب متصل
        account = frappe.db.get_value(
            "WhatsApp Sender Account",
            {"status": "Connected"},
            "name"
        )

        if not account:
            # إذا لم يوجد حساب متصل، البحث عن أي حساب
            account = frappe.db.get_value(
                "WhatsApp Sender Account",
                {},
                "name"
            )

        return account
    except Exception as e:
        frappe.log_error(f"Error getting default sender account: {str(e)}")
        return None

@frappe.whitelist()
def send_document_via_whatsapp(doctype, docname, sender_account, recipient_type,
                              phone_number=None, customer=None, supplier=None,
                              contact=None, send_type="PDF + رسالة", message="",
                              print_format=None):
    """Send document via WhatsApp"""
    try:
        # التحقق من صحة البيانات أو اختيار الافتراضي
        if not sender_account:
            sender_account = get_default_sender_account()
            if not sender_account:
                return {"success": False, "error": "لم يتم العثور على حساب WhatsApp متاح"}

        # الحصول على رقم الهاتف
        recipient_phone = get_recipient_phone(
            recipient_type, phone_number, customer, supplier, contact
        )

        if not recipient_phone:
            return {"success": False, "error": "لم يتم العثور على رقم هاتف صحيح"}

        # الحصول على المستند
        doc = frappe.get_doc(doctype, docname)

        # إرسال حسب النوع المطلوب
        if send_type in ["PDF + رسالة", "PDF فقط"]:
            # إنشاء PDF
            pdf_data = get_document_pdf(doc, print_format)

            if send_type == "PDF + رسالة" and message:
                # إرسال الرسالة أولاً
                send_message(sender_account, recipient_phone, message)

            # إرسال PDF
            result = send_document_attachment(
                sender_account, recipient_phone, pdf_data,
                f"{doctype}_{docname}.pdf"
            )

        elif send_type == "رسالة فقط":
            if not message:
                return {"success": False, "error": "يرجى كتابة رسالة"}
            result = send_message(sender_account, recipient_phone, message)

        elif send_type == "رابط المستند":
            doc_url = get_document_url(doctype, docname)
            link_message = f"{message}\n\nرابط المستند: {doc_url}" if message else f"رابط المستند: {doc_url}"
            result = send_message(sender_account, recipient_phone, link_message)

        if result.get("success"):
            # تسجيل العملية
            log_whatsapp_activity(doc, sender_account, recipient_phone, send_type)
            return {
                "success": True,
                "message": "تم الإرسال بنجاح",
                "recipient": recipient_phone
            }
        else:
            return {"success": False, "error": result.get("error", "فشل في الإرسال")}

    except Exception as e:
        frappe.log_error(f"Error sending document via WhatsApp: {str(e)}")
        return {"success": False, "error": str(e)}

def get_recipient_phone(recipient_type, phone_number, customer, supplier, contact):
    """Get recipient phone number based on type"""
    try:
        if recipient_type == "رقم هاتف":
            return format_phone_number(phone_number)

        elif recipient_type == "عميل" and customer:
            # البحث عن رقم هاتف العميل
            customer_doc = frappe.get_doc("Customer", customer)
            if customer_doc.mobile_no:
                return format_phone_number(customer_doc.mobile_no)
            # البحث في جهات الاتصال
            contact_phone = frappe.db.get_value(
                "Contact",
                {"link_doctype": "Customer", "link_name": customer},
                "mobile_no"
            )
            if contact_phone:
                return format_phone_number(contact_phone)

        elif recipient_type == "مورد" and supplier:
            # البحث عن رقم هاتف المورد
            contact_phone = frappe.db.get_value(
                "Contact",
                {"link_doctype": "Supplier", "link_name": supplier},
                "mobile_no"
            )
            if contact_phone:
                return format_phone_number(contact_phone)

        elif recipient_type == "جهة اتصال" and contact:
            contact_doc = frappe.get_doc("Contact", contact)
            if contact_doc.mobile_no:
                return format_phone_number(contact_doc.mobile_no)

        return None
    except Exception as e:
        frappe.log_error(f"Error getting recipient phone: {str(e)}")
        return None

def format_phone_number(phone):
    """Format phone number for WhatsApp"""
    if not phone:
        return None

    # إزالة المسافات والرموز
    phone = ''.join(filter(str.isdigit, str(phone)))

    # التحقق من طول الرقم
    if len(phone) < 7:
        return None

    # إضافة رمز الدولة إذا لم يكن موجوداً
    if not phone.startswith('967'):  # رمز اليمن
        if phone.startswith('7') and len(phone) == 9:
            phone = '967' + phone
        elif phone.startswith('0') and len(phone) == 10:
            phone = '967' + phone[1:]
        elif phone.startswith('1') and len(phone) == 9:  # أرقام أمريكية
            phone = '1' + phone
        elif phone.startswith('966') and len(phone) == 12:  # أرقام سعودية
            pass  # الرقم صحيح
        elif phone.startswith('971') and len(phone) == 12:  # أرقام إماراتية
            pass  # الرقم صحيح
        elif phone.startswith('974') and len(phone) == 11:  # أرقام قطرية
            pass  # الرقم صحيح
        elif len(phone) == 9:  # رقم يمني بدون رمز الدولة
            phone = '967' + phone
        elif len(phone) == 8 and phone.startswith('7'):  # رقم يمني قديم
            phone = '967' + phone

    return phone + '@c.us'

def get_document_pdf(doc, print_format=None):
    """Generate PDF for document"""
    try:
        if not print_format:
            print_format = "Standard"

        # إنشاء PDF
        pdf = frappe.get_print(
            doc.doctype,
            doc.name,
            print_format,
            as_pdf=True
        )

        return pdf
    except Exception as e:
        frappe.log_error(f"Error generating PDF: {str(e)}")
        return None

def send_document_attachment(sender_account, recipient, pdf_data, filename):
    """Send PDF attachment via WhatsApp"""
    try:
        import base64

        # تحويل PDF إلى base64
        pdf_base64 = base64.b64encode(pdf_data).decode('utf-8')

        # إرسال عبر WhatsApp API
        settings = frappe.get_single("WhatsApp Settings")
        server_url = settings.get_server_url()

        data = {
            "accountId": sender_account,
            "to": recipient,
            "media": f"data:application/pdf;base64,{pdf_base64}",
            "caption": f"📄 {filename}"
        }

        response = requests.post(
            f"{server_url}/api/send-media",
            json=data,
            timeout=60
        )

        if response.status_code == 200:
            return {"success": True}
        else:
            return {"success": False, "error": "فشل في إرسال الملف"}

    except Exception as e:
        frappe.log_error(f"Error sending attachment: {str(e)}")
        return {"success": False, "error": str(e)}

def get_document_url(doctype, docname):
    """Get document URL"""
    site_url = frappe.utils.get_url()
    doc_route = doctype.lower().replace(' ', '-')

    # تحسين الروابط لبعض المستندات الخاصة
    route_mapping = {
        'sales-invoice': 'sales-invoice',
        'purchase-invoice': 'purchase-invoice',
        'quotation': 'quotation',
        'sales-order': 'sales-order',
        'purchase-order': 'purchase-order',
        'delivery-note': 'delivery-note',
        'purchase-receipt': 'purchase-receipt',
        'payment-entry': 'payment-entry',
        'journal-entry': 'journal-entry',
        'material-request': 'material-request',
        'stock-entry': 'stock-entry',
        'customer': 'customer',
        'supplier': 'supplier',
        'item': 'item',
        'lead': 'lead',
        'opportunity': 'opportunity',
        'project': 'project',
        'task': 'task',
        'issue': 'issue',
        'timesheet': 'timesheet',
        'expense-claim': 'expense-claim',
        'leave-application': 'leave-application',
        'salary-slip': 'salary-slip',
        'employee': 'employee',
        'asset': 'asset',
        'work-order': 'work-order',
        'job-card': 'job-card',
        'bom': 'bom',
        'production-plan': 'production-plan'
    }

    final_route = route_mapping.get(doc_route, doc_route)
    return f"{site_url}/app/{final_route}/{docname}"

def log_whatsapp_activity(doc, sender_account, recipient, send_type):
    """Log WhatsApp activity"""
    try:
        # إضافة تعليق في المستند
        comment = f"تم إرسال المستند عبر WhatsApp ({send_type}) إلى {recipient}"
        doc.add_comment("Comment", comment)

        # تسجيل في سجل الرسائل
        log = frappe.new_doc("WhatsApp Message Log")
        log.sender_account = sender_account
        log.recipient = recipient
        log.message = f"تم إرسال {doc.doctype} {doc.name}"
        log.reference_doctype = doc.doctype
        log.reference_name = doc.name
        log.status = "Sent"
        log.save(ignore_permissions=True)

    except Exception as e:
        frappe.log_error(f"Error logging WhatsApp activity: {str(e)}")
