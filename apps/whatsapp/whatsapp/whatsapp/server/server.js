/**
 * WhatsApp Web.js Server for ERPNext
 * This server handles WhatsApp Web connections and provides an API for ERPNext to interact with WhatsApp
 */

const express = require('express');
const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const bodyParser = require('body-parser');
const cors = require('cors');

// Create Express app
const app = express();
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Configuration
const PORT = process.env.PORT || 8085;
const SESSION_DIR = path.join(__dirname, 'sessions');
const FRAPPE_URL = process.env.FRAPPE_URL || 'http://localhost:8000';
const API_KEY = process.env.API_KEY || 'administrator';
const API_SECRET = process.env.API_SECRET || '';

// Ensure session directory exists
if (!fs.existsSync(SESSION_DIR)) {
    fs.mkdirSync(SESSION_DIR, { recursive: true });
}

// Store active clients
const clients = {};

// Initialize a new WhatsApp client
function initClient(accountId) {
    const sessionPath = path.join(SESSION_DIR, accountId);

    // Create client with local auth
    const client = new Client({
        authStrategy: new LocalAuth({
            clientId: accountId,
            dataPath: sessionPath
        }),
        puppeteer: {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
                '--disable-gpu',
                '--mute-audio',
                '--disable-features=AudioServiceOutOfProcess'
            ],
            ignoreDefaultArgs: ['--enable-automation']
        }
    });

    // Store QR code
    let qrCode = '';

    // Handle QR code
    client.on('qr', (qr) => {
        console.log(`QR code received for account ${accountId}`);
        qrCode = qr;

        // Generate QR code image
        qrcode.toDataURL(qr, (err, url) => {
            if (err) {
                console.error('Error generating QR code:', err);
                return;
            }

            // Update account status in ERPNext
            updateAccountStatus(accountId, 'Pending', url);
        });
    });

    // Handle authentication
    client.on('authenticated', () => {
        console.log(`Account ${accountId} authenticated`);
        qrCode = '';
    });

    // Handle ready state
    client.on('ready', () => {
        console.log(`Account ${accountId} is ready`);

        // Update account status in ERPNext
        updateAccountStatus(accountId, 'Connected');
    });

    // Handle disconnection
    client.on('disconnected', (reason) => {
        console.log(`Account ${accountId} disconnected:`, reason);

        // Update account status in ERPNext
        updateAccountStatus(accountId, 'Disconnected');

        // Remove client from active clients
        delete clients[accountId];
    });

    // Handle messages
    client.on('message', async (message) => {
        console.log(`Message received for account ${accountId}:`, message.body);

        // Log incoming message in ERPNext
        logMessage(accountId, message);
    });

    // Initialize client
    client.initialize();

    // Store client
    clients[accountId] = {
        client,
        qrCode,
        status: 'initializing'
    };

    return client;
}

// Update account status in ERPNext
async function updateAccountStatus(accountId, status, qrCode = null) {
    try {
        console.log(`محاولة تحديث حالة الحساب ${accountId} إلى ${status}`);

        // تجاهل تحديث ERPNext مؤقتاً لتجنب خطأ 401
        // يمكن تفعيل هذا لاحقاً عند إعداد مفاتيح API صحيحة

        /*
        const data = {
            status,
            qr_code: qrCode
        };

        await axios.post(`${FRAPPE_URL}/api/method/whatsapp.whatsapp.api.update_account_status`, {
            account_id: accountId,
            status: status,
            qr_code: qrCode
        }, {
            headers: {
                'Authorization': `token ${API_KEY}:${API_SECRET}`,
                'Content-Type': 'application/json'
            }
        });
        */

        console.log(`✅ تم تحديث حالة الحساب ${accountId} إلى ${status} محلياً`);

        // حفظ رمز QR في ملف إذا كان متاحاً
        if (qrCode && status === 'Pending') {
            const qrPath = path.join(__dirname, `qr-${accountId}.png`);
            const base64Data = qrCode.replace(/^data:image\/png;base64,/, '');
            fs.writeFileSync(qrPath, base64Data, 'base64');
            console.log(`💾 تم حفظ رمز QR للحساب ${accountId} في: ${qrPath}`);
        }

    } catch (error) {
        console.error(`❌ خطأ في تحديث حالة الحساب ${accountId}:`, error.message);
    }
}

// Log message in ERPNext
async function logMessage(accountId, message) {
    try {
        console.log(`📨 رسالة واردة للحساب ${accountId}:`);
        console.log(`   من: ${message.from}`);
        console.log(`   النص: ${message.body}`);
        console.log(`   يحتوي على وسائط: ${message.hasMedia ? 'نعم' : 'لا'}`);

        // تجاهل تسجيل الرسائل في ERPNext مؤقتاً لتجنب خطأ 401
        // يمكن تفعيل هذا لاحقاً عند إعداد مفاتيح API صحيحة

        /*
        await axios.post(`${FRAPPE_URL}/api/method/whatsapp.whatsapp.api.log_incoming_message`, {
            account_id: accountId,
            message_id: message.id._serialized,
            from: message.from,
            body: message.body,
            has_media: message.hasMedia
        }, {
            headers: {
                'Authorization': `token ${API_KEY}:${API_SECRET}`,
                'Content-Type': 'application/json'
            }
        });
        */

        console.log(`✅ تم تسجيل الرسالة للحساب ${accountId} محلياً`);
    } catch (error) {
        console.error(`❌ خطأ في تسجيل الرسالة للحساب ${accountId}:`, error.message);
    }
}

// API Routes

// Home page
app.get('/', (req, res) => {
    res.send(`
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>WhatsApp Server - ERPNext</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                background: linear-gradient(135deg, #25D366, #128C7E);
                color: white;
                min-height: 100vh;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background: rgba(255,255,255,0.1);
                padding: 30px;
                border-radius: 15px;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            }
            h1 {
                text-align: center;
                margin-bottom: 30px;
                font-size: 2.5em;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            .status {
                background: rgba(255,255,255,0.2);
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
                border-left: 5px solid #fff;
            }
            .api-info {
                background: rgba(0,0,0,0.2);
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
                font-family: 'Courier New', monospace;
            }
            .endpoint {
                margin: 10px 0;
                padding: 10px;
                background: rgba(255,255,255,0.1);
                border-radius: 5px;
            }
            .method {
                display: inline-block;
                padding: 3px 8px;
                border-radius: 3px;
                font-weight: bold;
                margin-left: 10px;
            }
            .post { background: #ff6b6b; }
            .get { background: #4ecdc4; }
            .footer {
                text-align: center;
                margin-top: 30px;
                opacity: 0.8;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 WhatsApp Server</h1>
            <div class="status">
                <h3>✅ السيرفر يعمل بنجاح</h3>
                <p><strong>المنفذ:</strong> ${PORT}</p>
                <p><strong>الوقت:</strong> ${new Date().toLocaleString('ar-SA')}</p>
                <p><strong>الحالة:</strong> متصل ومستعد لاستقبال الطلبات</p>
            </div>

            <div class="api-info">
                <h3>📡 نقاط الوصول المتاحة (API Endpoints)</h3>

                <div class="endpoint">
                    <span class="method post">POST</span>
                    <strong>/api/init</strong> - تهيئة حساب WhatsApp جديد
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <strong>/api/qr/:accountId</strong> - الحصول على رمز QR
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <strong>/api/status/:accountId</strong> - حالة الحساب
                </div>

                <div class="endpoint">
                    <span class="method post">POST</span>
                    <strong>/api/send</strong> - إرسال رسالة
                </div>

                <div class="endpoint">
                    <span class="method post">POST</span>
                    <strong>/api/send-media</strong> - إرسال ملف (PDF, صورة, إلخ)
                </div>

                <div class="endpoint">
                    <span class="method post">POST</span>
                    <strong>/api/disconnect/:accountId</strong> - قطع الاتصال
                </div>
            </div>

            <div class="status">
                <h3>📱 كيفية الاستخدام</h3>
                <p>1. استخدم ERPNext لإنشاء حساب WhatsApp جديد</p>
                <p>2. انقر على "Connect" في واجهة ERPNext</p>
                <p>3. امسح رمز QR باستخدام تطبيق WhatsApp</p>
                <p>4. ابدأ في إرسال الرسائل!</p>
            </div>

            <div class="footer">
                <p>🔧 تم التطوير بواسطة Augment Agent</p>
                <p>📅 ${new Date().getFullYear()} - ERPNext WhatsApp Integration</p>
            </div>
        </div>
    </body>
    </html>
    `);
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        port: PORT,
        activeClients: Object.keys(clients).length
    });
});

// Serve QR code images
app.get('/qr/:accountId', (req, res) => {
    const { accountId } = req.params;
    const qrPath = path.join(__dirname, `qr-${accountId}.png`);

    if (fs.existsSync(qrPath)) {
        res.sendFile(qrPath);
    } else {
        res.status(404).json({ error: 'QR code not found' });
    }
});

// List all available QR codes
app.get('/qr-list', (req, res) => {
    try {
        const qrFiles = fs.readdirSync(__dirname)
            .filter(file => file.startsWith('qr-') && file.endsWith('.png'))
            .map(file => {
                const accountId = file.replace('qr-', '').replace('.png', '');
                return {
                    accountId,
                    qrUrl: `/qr/${accountId}`,
                    fileName: file
                };
            });

        res.json({ qrCodes: qrFiles });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Initialize a client
app.post('/api/init', (req, res) => {
    const { accountId } = req.body;

    if (!accountId) {
        return res.status(400).json({ error: 'Account ID is required' });
    }

    try {
        // Check if client already exists
        if (clients[accountId]) {
            return res.json({ status: 'exists', message: 'Client already initialized' });
        }

        // Initialize new client
        initClient(accountId);

        res.json({ status: 'success', message: 'Client initialized' });
    } catch (error) {
        console.error(`Error initializing client for account ${accountId}:`, error);
        res.status(500).json({ error: error.message });
    }
});

// Get QR code
app.get('/api/qr/:accountId', (req, res) => {
    const { accountId } = req.params;

    if (!clients[accountId]) {
        return res.status(404).json({ error: 'Client not found' });
    }

    if (!clients[accountId].qrCode) {
        return res.status(404).json({ error: 'QR code not available' });
    }

    // Generate QR code image
    qrcode.toDataURL(clients[accountId].qrCode, (err, url) => {
        if (err) {
            console.error('Error generating QR code:', err);
            return res.status(500).json({ error: 'Error generating QR code' });
        }

        res.json({ qrCode: url });
    });
});

// Get client status
app.get('/api/status/:accountId', (req, res) => {
    const { accountId } = req.params;

    if (!clients[accountId]) {
        return res.status(404).json({ error: 'Client not found' });
    }

    res.json({ status: clients[accountId].status });
});

// Send message
app.post('/api/send', async (req, res) => {
    const { accountId, to, message, attachmentUrl } = req.body;

    if (!accountId || !to || !message) {
        return res.status(400).json({ error: 'Account ID, recipient, and message are required' });
    }

    if (!clients[accountId] || !clients[accountId].client) {
        return res.status(404).json({ error: 'Client not found or not initialized' });
    }

    try {
        // Format phone number
        let formattedNumber = to.replace(/\D/g, '');
        if (!formattedNumber.endsWith('@c.us')) {
            formattedNumber = `${formattedNumber}@c.us`;
        }

        // Send message
        if (attachmentUrl) {
            // Download attachment
            const response = await axios.get(attachmentUrl, { responseType: 'arraybuffer' });
            const buffer = Buffer.from(response.data, 'binary');
            const filename = path.basename(attachmentUrl);

            // Send media
            const media = new MessageMedia(
                response.headers['content-type'],
                buffer.toString('base64'),
                filename
            );

            await clients[accountId].client.sendMessage(formattedNumber, media, { caption: message });
        } else {
            // Send text message
            await clients[accountId].client.sendMessage(formattedNumber, message);
        }

        res.json({ status: 'success', message: 'Message sent' });
    } catch (error) {
        console.error(`Error sending message for account ${accountId}:`, error);
        res.status(500).json({ error: error.message });
    }
});

// Send media (PDF, images, etc.)
app.post('/api/send-media', async (req, res) => {
    const { accountId, to, media, caption } = req.body;

    if (!accountId || !to || !media) {
        return res.status(400).json({ error: 'Account ID, recipient, and media are required' });
    }

    if (!clients[accountId] || !clients[accountId].client) {
        return res.status(404).json({ error: 'Client not found or not initialized' });
    }

    try {
        // Format phone number
        let formattedNumber = to.replace(/\D/g, '');
        if (!formattedNumber.endsWith('@c.us')) {
            formattedNumber = `${formattedNumber}@c.us`;
        }

        // Parse media data (base64)
        const mediaData = media.split(',')[1]; // Remove data:type;base64, prefix
        const mediaType = media.split(';')[0].split(':')[1]; // Extract MIME type

        // Create MessageMedia object
        const messageMedia = new MessageMedia(
            mediaType,
            mediaData,
            caption || 'document'
        );

        // Send media
        await clients[accountId].client.sendMessage(formattedNumber, messageMedia, {
            caption: caption || ''
        });

        console.log(`📎 تم إرسال ملف للحساب ${accountId} إلى ${formattedNumber}`);
        res.json({ status: 'success', message: 'Media sent successfully' });

    } catch (error) {
        console.error(`❌ خطأ في إرسال الملف للحساب ${accountId}:`, error);
        res.status(500).json({ error: error.message });
    }
});

// Disconnect client
app.post('/api/disconnect/:accountId', (req, res) => {
    const { accountId } = req.params;

    if (!clients[accountId]) {
        return res.status(404).json({ error: 'Client not found' });
    }

    try {
        // Disconnect client
        clients[accountId].client.destroy();
        delete clients[accountId];

        res.json({ status: 'success', message: 'Client disconnected' });
    } catch (error) {
        console.error(`Error disconnecting client for account ${accountId}:`, error);
        res.status(500).json({ error: error.message });
    }
});

// Start server
app.listen(PORT, () => {
    console.log(`WhatsApp Web.js server running on port ${PORT}`);
});
