/**
 * Debug Server
 */

const http = require('http');

// Create HTTP server
const server = http.createServer((req, res) => {
    console.log(`Received request: ${req.method} ${req.url}`);
    
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ status: 'running', url: req.url }));
    
    console.log('Response sent');
});

// Configuration
const PORT = 8089;

// Start server
server.listen(PORT, '0.0.0.0', () => {
    console.log(`Debug server running on port ${PORT}`);
    console.log(`Server is listening on all interfaces`);
});
