# WhatsApp Web.js Server for ERPNext

This server provides an API for ERPNext to interact with WhatsApp Web using the [whatsapp-web.js](https://github.com/pedroslopez/whatsapp-web.js) library.

## Installation

1. Install Node.js and npm
2. Clone this repository
3. Install dependencies:

```bash
npm install
```

## Configuration

Set the following environment variables:

- `PORT`: Server port (default: 8085)
- `FRAPPE_URL`: ERPNext URL (default: http://localhost:8000)
- `API_KEY`: ERPNext API key
- `API_SECRET`: ERPNext API secret

## Running the Server

```bash
npm start
```

## Installing as a systemd Service

1. Copy the `whatsapp-server.service` file to `/etc/systemd/system/`
2. Reload systemd:

```bash
sudo systemctl daemon-reload
```

3. Enable and start the service:

```bash
sudo systemctl enable whatsapp-server
sudo systemctl start whatsapp-server
```

4. Check the status:

```bash
sudo systemctl status whatsapp-server
```

## API Endpoints

### Initialize a Client

```
POST /api/init
```

Request body:
```json
{
  "accountId": "account-name"
}
```

### Get QR Code

```
GET /api/qr/:accountId
```

### Get Client Status

```
GET /api/status/:accountId
```

### Send Message

```
POST /api/send
```

Request body:
```json
{
  "accountId": "account-name",
  "to": "phone-number",
  "message": "Hello, world!",
  "attachmentUrl": "https://example.com/file.pdf" // Optional
}
```

### Disconnect Client

```
POST /api/disconnect/:accountId
```
