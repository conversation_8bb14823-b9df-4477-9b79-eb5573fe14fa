/**
 * Minimal WhatsApp Server
 */

const express = require('express');
const path = require('path');

// Create Express app
const app = express();
app.use(express.static(__dirname));

// Configuration
const PORT = 8087;
const IP_ADDRESS = '*************';

// API Routes

// Home page
app.get('/', (req, res) => {
    res.send(`
    <html>
    <head>
        <title>WhatsApp Minimal Server</title>
    </head>
    <body>
        <h1>WhatsApp Minimal Server</h1>
        <p>Server is running</p>
        <img src="/whatsapp-qr.png" alt="QR Code" width="300">
    </body>
    </html>
    `);
});

// Status API
app.get('/api/status', (req, res) => {
    res.json({ status: 'running' });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
    console.log(`Minimal WhatsApp server running on port ${PORT}`);
    console.log(`Access the server at: http://${IP_ADDRESS}:${PORT}`);
});
