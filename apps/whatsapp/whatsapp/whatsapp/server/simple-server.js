/**
 * Simple WhatsApp Server for testing
 */

const express = require('express');
const qrcode = require('qrcode');
const fs = require('fs');
const path = require('path');
const bodyParser = require('body-parser');
const cors = require('cors');

// Create Express app
const app = express();
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Configuration
const PORT = process.env.PORT || 8085;

// API Routes

// Initialize a client
app.post('/api/init', (req, res) => {
    const { accountId } = req.body;
    
    if (!accountId) {
        return res.status(400).json({ error: 'Account ID is required' });
    }
    
    try {
        // Simulate client initialization
        console.log(`Initializing client for account ${accountId}`);
        
        // Generate a fake QR code
        const qrCode = "fake-qr-code-" + accountId;
        
        // Update account status in ERPNext
        updateAccountStatus(accountId, 'Pending', qrCode);
        
        res.json({ status: 'success', message: 'Client initialized' });
    } catch (error) {
        console.error(`Error initializing client for account ${accountId}:`, error);
        res.status(500).json({ error: error.message });
    }
});

// Get QR code
app.get('/api/qr/:accountId', (req, res) => {
    const { accountId } = req.params;
    
    // Generate QR code image
    qrcode.toDataURL("fake-qr-code-" + accountId, (err, url) => {
        if (err) {
            console.error('Error generating QR code:', err);
            return res.status(500).json({ error: 'Error generating QR code' });
        }
        
        res.json({ qrCode: url });
    });
});

// Get client status
app.get('/api/status/:accountId', (req, res) => {
    const { accountId } = req.params;
    
    res.json({ status: 'pending' });
});

// Simulate updating account status
function updateAccountStatus(accountId, status, qrCode = null) {
    console.log(`Updated account ${accountId} status to ${status}`);
    if (qrCode) {
        console.log(`Generated QR code for account ${accountId}`);
    }
}

// Start server
app.listen(PORT, () => {
    console.log(`Simple WhatsApp server running on port ${PORT}`);
});
