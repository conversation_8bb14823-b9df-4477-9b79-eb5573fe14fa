/**
 * Simple Debug Server
 */

console.log('Starting server...');

const http = require('http');
console.log('HTTP module loaded');

// Create HTTP server
const server = http.createServer((req, res) => {
    console.log(`Received request: ${req.method} ${req.url}`);
    
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('Server is running');
    
    console.log('Response sent');
});
console.log('Server created');

// Configuration
const PORT = 8090;
console.log(`Port set to ${PORT}`);

// Start server
try {
    server.listen(PORT, '0.0.0.0', () => {
        console.log(`Debug server running on port ${PORT}`);
        console.log(`Server is listening on all interfaces`);
    });
    console.log('Server started');
} catch (error) {
    console.error('Error starting server:', error);
}
