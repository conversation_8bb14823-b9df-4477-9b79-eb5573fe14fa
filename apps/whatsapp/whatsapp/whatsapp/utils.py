# Copyright (c) 2023, moneer and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from datetime import datetime, timedelta

def process_whatsapp_triggers(doc, method=None):
    """Process WhatsApp triggers for document events"""
    if not frappe.db.get_single_value("WhatsApp Settings", "enable_whatsapp"):
        return
        
    # Get the event name from the method
    event = method
    if not event:
        # Try to guess the event from the document status
        if doc.docstatus == 1:
            event = "on_submit"
        elif doc.docstatus == 2:
            event = "on_cancel"
        else:
            event = "on_update"
    
    # Get all triggers for this doctype and event
    triggers = frappe.get_all(
        "WhatsApp Trigger Settings",
        filters={
            "reference_doctype": doc.doctype,
            "event": event,
            "enabled": 1
        }
    )
    
    # Process each trigger
    for trigger_name in triggers:
        trigger = frappe.get_doc("WhatsApp Trigger Settings", trigger_name.name)
        trigger.process_document(doc)

def check_whatsapp_connections():
    """Check and update WhatsApp connection status"""
    if not frappe.db.get_single_value("WhatsApp Settings", "enable_whatsapp"):
        return
        
    # Get all connected accounts
    accounts = frappe.get_all(
        "WhatsApp Sender Account",
        filters={"status": "Connected"}
    )
    
    # Check each account's connection
    for account_name in accounts:
        try:
            account = frappe.get_doc("WhatsApp Sender Account", account_name.name)
            
            # This would be implemented with whatsapp-web.js
            # For now, we'll just log the check
            frappe.logger().info(f"Checking WhatsApp connection for {account.name}")
            
            # If the account was last connected more than 24 hours ago, mark as disconnected
            if account.last_connected:
                last_connected = frappe.utils.get_datetime(account.last_connected)
                if datetime.now() - last_connected > timedelta(hours=24):
                    account.status = "Disconnected"
                    account.save()
                    
                    frappe.logger().warning(f"WhatsApp account {account.name} marked as disconnected due to inactivity")
        except Exception as e:
            frappe.logger().error(f"Error checking WhatsApp connection for {account_name.name}: {str(e)}")

def cleanup_message_logs():
    """Clean up old message logs"""
    # Get retention period from settings (default: 30 days)
    retention_days = frappe.db.get_single_value("WhatsApp Settings", "log_retention_days") or 30
    
    # Calculate cutoff date
    cutoff_date = datetime.now() - timedelta(days=retention_days)
    
    # Delete old logs
    frappe.db.delete(
        "WhatsApp Message Log",
        {
            "creation": ["<", cutoff_date.strftime("%Y-%m-%d")]
        }
    )
    
    frappe.logger().info(f"Cleaned up WhatsApp message logs older than {retention_days} days")

def sync_whatsapp_groups():
    """Sync WhatsApp groups"""
    if not frappe.db.get_single_value("WhatsApp Settings", "enable_whatsapp"):
        return
        
    # Get all groups
    groups = frappe.get_all("WhatsApp Group")
    
    # Sync each group
    for group_name in groups:
        try:
            group = frappe.get_doc("WhatsApp Group", group_name.name)
            group.sync_members()
            
            frappe.logger().info(f"Synced WhatsApp group {group.name}")
        except Exception as e:
            frappe.logger().error(f"Error syncing WhatsApp group {group_name.name}: {str(e)}")

def send_whatsapp_message(sender_account, recipient, message, attachments=None, reference_doctype=None, reference_name=None):
    """Send a WhatsApp message"""
    if not frappe.db.get_single_value("WhatsApp Settings", "enable_whatsapp"):
        frappe.throw(_("WhatsApp integration is not enabled"))
        
    # Get the sender account
    if isinstance(sender_account, str):
        sender_account = frappe.get_doc("WhatsApp Sender Account", sender_account)
        
    # Send the message
    return sender_account.send_message(recipient, message, attachments, reference_doctype, reference_name)
