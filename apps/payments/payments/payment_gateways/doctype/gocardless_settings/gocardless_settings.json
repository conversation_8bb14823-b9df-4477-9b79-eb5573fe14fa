{"actions": [], "autoname": "field:gateway_name", "creation": "2018-02-06 16:11:10.028249", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["gateway_name", "section_break_2", "access_token", "webhooks_secret", "use_sandbox"], "fields": [{"fieldname": "gateway_name", "fieldtype": "Data", "in_list_view": 1, "label": "Payment Gateway Name", "reqd": 1, "unique": 1}, {"fieldname": "section_break_2", "fieldtype": "Section Break"}, {"fieldname": "access_token", "fieldtype": "Data", "in_list_view": 1, "label": "Access Token", "reqd": 1}, {"fieldname": "webhooks_secret", "fieldtype": "Data", "label": "Webhooks Secret"}, {"default": "0", "fieldname": "use_sandbox", "fieldtype": "Check", "label": "Use Sandbox"}], "links": [], "modified": "2023-09-22 13:33:42.225243", "modified_by": "Administrator", "module": "Payment Gateways", "name": "GoCardless Settings", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}