{% extends "templates/web.html" %}

{% block title %} Payment {% endblock %}

{%- block header -%}
{% endblock %}

{% block script %}
<script src="https://js.stripe.com/v3/"></script>
<script>{% include "templates/includes/stripe_checkout.js" %}</script>
{% endblock %}

{%- block page_content -%}

<div class="stripe" style="min-height: 400px; padding-bottom: 50px; margin-top:100px;margin-left:250px;">
	<div class="col-sm-10 col-sm-offset-2">
		{% if image %}
			<img src={{image}}>
		{% endif %}
		<h2 class="text-center">{{description}}</h2>
		<form id="payment-form">
			<div class="form-row row">
				<div class="group col-12">
					<div>
						<label>
							<span>{{ _("Name") }}</span>
						<input id="cardholder-name" name="cardholder-name" class="field" placeholder="{{ _('<PERSON>') }}" value="{{payer_name}}"/>
					</label>
					</div>
				</div>
				<div class="group col-12">
					<div>
						<label>
							<span>{{ _("Email") }}</span>
						<input id="cardholder-email" name="cardholder-email" class="field" placeholder="{{ _('<EMAIL>') }}" value="{{payer_email}}"/>
					</label>
					</div>
				</div>
				<div class="group col-12">
					<label>
					<span>{{ _("Card Details") }}</span>
				<div id="card-element" name="card-element" class="field"></div>
				<div id="card-errors" role="alert"></div>
			</label>
			</div>

			</div>
			<button type="submit" class="submit" id="submit">{{_('Pay')}} {{amount}}</button>
			<div class="outcome text-center">
				<div class="error" hidden>{{ _("An error occured during the payment process. Please contact us.") }}</div>
				<div class="success" hidden>{{ _("Your payment has been successfully registered.") }}</div>
			</div>
		</form>
	</div>
</div>


{% endblock %}
