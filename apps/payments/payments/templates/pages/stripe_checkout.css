.StripeElement {
  background-color: white;
  height: 40px;
  padding: 10px 12px;
  border-radius: 4px;
  border: 1px solid transparent;
  box-shadow: 0 1px 3px 0 #e6ebf1;
  -webkit-transition: box-shadow 150ms ease;
  transition: box-shadow 150ms ease;
}

.StripeElement--focus {
  box-shadow: 0 1px 3px 0 #cfd7df;
}

.StripeElement--invalid {
  border-color: #fa755a;
}

.StripeElement--webkit-autofill {
  background-color: #fefde5;
}

.stripe #payment-form {
	margin-top: 80px;
}

.stripe button {
  float: right;
  display: block;
  background: #5e64ff;
  color: white;
  box-shadow: 0 7px 14px 0 rgba(49, 49, 93, 0.10), 0 3px 6px 0 rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  border: 0;
  margin-top: 20px;
  font-size: 15px;
  font-weight: 400;
  max-width: 40%;
  height: 40px;
  line-height: 38px;
  outline: none;
}

.stripe button:hover, .stripe button:focus {
  background: #2b33ff;
  border-color: #0711ff;
}

.stripe button:active {
  background: #5e64ff;
}

.stripe button:disabled {
  background: #515e80;
}

.stripe .group {
  background: white;
  box-shadow: 2px 7px 14px 2px rgba(49, 49, 93, 0.10), 0 3px 6px 0 rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  margin-bottom: 20px;
}

.stripe label {
  position: relative;
  color: #8898AA;
  font-weight: 300;
  height: 40px;
  line-height: 40px;
  margin-left: 20px;
  display: block;
}

.stripe .group label:not(:last-child) {
  border-bottom: 1px solid #F0F5FA;
}

.stripe label>span {
  width: 20%;
  text-align: right;
  float: left;
}

.current-card {
	margin-left: 20px;
}

.field {
  background: transparent;
  font-weight: 300;
  border: 0;
  color: #31325F;
  outline: none;
  padding-right: 10px;
  padding-left: 10px;
  cursor: text;
  width: 70%;
  height: 40px;
  float: right;
}

.field::-webkit-input-placeholder {
  color: #CFD7E0;
}

.field::-moz-placeholder {
  color: #CFD7E0;
}

.field:-ms-input-placeholder {
  color: #CFD7E0;
}
