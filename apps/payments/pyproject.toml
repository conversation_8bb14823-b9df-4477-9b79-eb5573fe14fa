[project]
name = "payments"
authors = [
    { name = "Frappe Technologies Pvt Ltd", email = "<EMAIL>"}
]
description = "Payments app for frappe"
requires-python = ">=3.10"
readme = "README.md"
dynamic = ["version"]
dependencies = [
    "paytmchecksum~=1.7.0",
    "razorpay~=1.2.0",
    "stripe~=2.56.0",
    "braintree~=4.20.0",
    "pycryptodome>=3.18.0,<4.0.0",
    "gocardless-pro~=1.22.0",
]

[build-system]
requires = ["flit_core >=3.4,<4"]
build-backend = "flit_core.buildapi"

[tool.black]
line-length = 99

[tool.isort]
line_length = 99
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
indent = "\t"
