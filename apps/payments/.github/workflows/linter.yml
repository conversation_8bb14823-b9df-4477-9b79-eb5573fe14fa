name: <PERSON><PERSON>

on:
  pull_request:
  workflow_dispatch:
  push:
    branches: [ develop ]

permissions:
  contents: read

concurrency:
  group: commitcheck-frappe-${{ github.event.number }}
  cancel-in-progress: true

jobs:
  commit-lint:
    name: 'Semantic Commits'
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 200
      - uses: actions/setup-node@v3
        with:
          node-version: 16
          check-latest: true

      - name: Check commit titles
        run: |
          npm install @commitlint/cli @commitlint/config-conventional
          npx commitlint --verbose --from ${{ github.event.pull_request.base.sha }} --to ${{ github.event.pull_request.head.sha }}

  linter:
    name: '<PERSON><PERSON><PERSON>'
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      - uses: pre-commit/action@v3.0.0

      - name: Download Semgrep rules
        run: git clone --depth 1 https://github.com/frappe/semgrep-rules.git frappe-semgrep-rules

      - name: Run Semgrep rules
        run: |
          pip install semgrep==0.97.0
          semgrep ci --config ./frappe-semgrep-rules/rules --config r/python.lang.correctness
