frappe.provide("frappe.help.help_links");

const docsUrl = "https://erpnext.com/docs/";

frappe.help.help_links["Form/Rename Tool"] = [
	{
		label: "Bulk Rename",
		url: docsUrl + "user/manual/en/transactions-bulk-rename",
	},
];

//Setup

frappe.help.help_links["List/User"] = [
	{
		label: "New User",
		url: docsUrl + "user/manual/en/adding-users",
	},
	{
		label: "Rename User",
		url: docsUrl + "user/manual/en/renaming-documents",
	},
];

frappe.help.help_links["permission-manager"] = [
	{
		label: "Role Based Permissions",
		url: docsUrl + "user/manual/en/role-based-permissions",
	},
	{
		label: "Managing Perm Level in Permissions Manager",
		url: docsUrl + "user/manual/en/managing-perm-level",
	},
	{
		label: "User Permissions",
		url: docsUrl + "user/manual/en/user-permissions",
	},
	{
		label: "Sharing",
		url: docsUrl + "user/manual/en/sharing",
	},
	{
		label: "Password",
		url: docsUrl + "user/manual/en/change-password",
	},
];

frappe.help.help_links["Form/System Settings"] = [
	{
		label: "System Settings",
		url: docsUrl + "user/manual/en/system-settings",
	},
];

frappe.help.help_links["Form/Data Import"] = [
	{
		label: "Importing and Exporting Data",
		url: docsUrl + "user/manual/en/data-import",
	},
];

frappe.help.help_links["List/Data Import"] = [
	{
		label: "Importing and Exporting Data",
		url: docsUrl + "user/manual/en/data-import",
	},
];

frappe.help.help_links["module_setup"] = [
	{
		label: "Role Permissions Manager",
		url: docsUrl + "user/manual/en/role-based-permissions",
	},
];

frappe.help.help_links["Form/Document Naming Settings"] = [
	{
		label: "Naming Series",
		url: docsUrl + "user/manual/en/document-naming-settings",
	},
];

frappe.help.help_links["Form/Global Defaults"] = [
	{
		label: "Global Settings",
		url: docsUrl + "user/manual/en/global-defaults",
	},
];

frappe.help.help_links["List/Print Heading"] = [
	{
		label: "Print Heading",
		url: docsUrl + "user/manual/en/print-headings",
	},
];

frappe.help.help_links["Form/Print Heading"] = [
	{
		label: "Print Heading",
		url: docsUrl + "user/manual/en/print-headings",
	},
];

frappe.help.help_links["List/Letter Head"] = [
	{
		label: "Letter Head",
		url: docsUrl + "user/manual/en/letter-head",
	},
];

frappe.help.help_links["List/Address Template"] = [
	{
		label: "Address Template",
		url: docsUrl + "user/manual/en/address-template",
	},
];

frappe.help.help_links["List/Terms and Conditions"] = [
	{
		label: "Terms and Conditions",
		url: docsUrl + "user/manual/en/terms-and-conditions",
	},
];

frappe.help.help_links["List/Cheque Print Template"] = [
	{
		label: "Cheque Print Template",
		url: docsUrl + "user/manual/en/cheque-print-template",
	},
];

frappe.help.help_links["List/Email Account"] = [
	{
		label: "Email Account",
		url: docsUrl + "user/manual/en/email-account",
	},
];

frappe.help.help_links["List/Notification"] = [
	{
		label: "Notification",
		url: docsUrl + "user/manual/en/notifications",
	},
];

frappe.help.help_links["Form/Notification"] = [
	{
		label: "Notification",
		url: docsUrl + "user/manual/en/notifications",
	},
];

frappe.help.help_links["Form/Email Digest"] = [
	{
		label: "Email Digest",
		url: docsUrl + "user/manual/en/email-digest",
	},
];

frappe.help.help_links["Form/Email Digest"] = [
	{
		label: "Email Digest",
		url: docsUrl + "user/manual/en/email-digest",
	},
];

frappe.help.help_links["List/Auto Email Report"] = [
	{
		label: "Auto Email Reports",
		url: docsUrl + "user/manual/en/auto-email-reports",
	},
];

frappe.help.help_links["Form/Print Settings"] = [
	{
		label: "Print Settings",
		url: docsUrl + "user/manual/en/print-settings",
	},
];

frappe.help.help_links["print-format-builder"] = [
	{
		label: "Print Format Builder",
		url: docsUrl + "user/manual/en/print-format-builder",
	},
];

//setup-integrations

frappe.help.help_links["Form/PayPal Settings"] = [
	{
		label: "PayPal Settings",
		url: docsUrl + "user/manual/en/paypal-integration",
	},
];

frappe.help.help_links["Form/Razorpay Settings"] = [
	{
		label: "Razorpay Settings",
		url: docsUrl + "user/manual/en/razorpay-integration",
	},
];

frappe.help.help_links["Form/Dropbox Settings"] = [
	{
		label: "Dropbox Settings",
		url: docsUrl + "user/manual/en/dropbox-backup",
	},
];

frappe.help.help_links["Form/LDAP Settings"] = [
	{
		label: "LDAP Settings",
		url: docsUrl + "user/manual/en/ldap-integration",
	},
];

frappe.help.help_links["Form/Stripe Settings"] = [
	{
		label: "Stripe Settings",
		url: docsUrl + "user/manual/en/stripe-integration",
	},
];

//Sales

frappe.help.help_links["Form/Quotation"] = [
	{ label: "Quotation", url: docsUrl + "user/manual/en/quotation" },
	{
		label: "Applying Discount",
		url: docsUrl + "user/manual/en/applying-discount",
	},
	{
		label: "Sales Person",
		url: docsUrl + "user/manual/en/sales-persons-in-the-sales-transactions",
	},
	{
		label: "Applying Margin",
		url: docsUrl + "user/manual/en/adding-margin",
	},
];

frappe.help.help_links["List/Customer"] = [
	{ label: "Customer", url: docsUrl + "user/manual/en/customer" },
	{
		label: "Credit Limit",
		url: docsUrl + "user/manual/en/credit-limit",
	},
];

frappe.help.help_links["Form/Customer"] = [
	{ label: "Customer", url: docsUrl + "user/manual/en/customer" },
	{
		label: "Credit Limit",
		url: docsUrl + "user/manual/en/credit-limit",
	},
];

frappe.help.help_links["List/Sales Taxes and Charges Template"] = [
	{
		label: "Setting Up Taxes",
		url: docsUrl + "user/manual/en/setting-up-taxes",
	},
];

frappe.help.help_links["Form/Sales Taxes and Charges Template"] = [
	{
		label: "Setting Up Taxes",
		url: docsUrl + "user/manual/en/setting-up-taxes",
	},
];

frappe.help.help_links["List/Sales Order"] = [
	{
		label: "Sales Order",
		url: docsUrl + "user/manual/en/sales-order",
	},
	{
		label: "Recurring Sales Order",
		url: docsUrl + "user/manual/en/auto-repeat",
	},
	{
		label: "Applying Discount",
		url: docsUrl + "user/manual/en/applying-discount",
	},
];

frappe.help.help_links["Form/Sales Order"] = [
	{
		label: "Sales Order",
		url: docsUrl + "user/manual/en/sales-order",
	},
	{
		label: "Recurring Sales Order",
		url: docsUrl + "user/manual/en/auto-repeat",
	},
	{
		label: "Applying Discount",
		url: docsUrl + "user/manual/en/applying-discount",
	},
	{
		label: "Drop Shipping",
		url: docsUrl + "user/manual/en/drop-shipping",
	},
	{
		label: "Sales Person",
		url: docsUrl + "user/manual/en/sales-persons-in-the-sales-transactions",
	},
	{
		label: "Close Sales Order",
		url: docsUrl + "user/manual/en/close-sales-order",
	},
	{
		label: "Applying Margin",
		url: docsUrl + "user/manual/en/adding-margin",
	},
];

frappe.help.help_links["Form/Product Bundle"] = [
	{
		label: "Product Bundle",
		url: docsUrl + "user/manual/en/product-bundle",
	},
];

frappe.help.help_links["Form/Selling Settings"] = [
	{
		label: "Selling Settings",
		url: docsUrl + "user/manual/en/selling-settings",
	},
];

//Buying

frappe.help.help_links["List/Supplier"] = [
	{ label: "Supplier", url: docsUrl + "user/manual/en/supplier" },
];

frappe.help.help_links["Form/Supplier"] = [
	{ label: "Supplier", url: docsUrl + "user/manual/en/supplier" },
];

frappe.help.help_links["Form/Request for Quotation"] = [
	{
		label: "Request for Quotation",
		url: docsUrl + "user/manual/en/request-for-quotation",
	},
	{
		label: "RFQ Video",
		url: docsUrl + "user/videos/learn/request-for-quotation.html",
	},
];

frappe.help.help_links["Form/Supplier Quotation"] = [
	{
		label: "Supplier Quotation",
		url: docsUrl + "user/manual/en/supplier-quotation",
	},
];

frappe.help.help_links["Form/Buying Settings"] = [
	{
		label: "Buying Settings",
		url: docsUrl + "user/manual/en/buying-settings",
	},
];

frappe.help.help_links["List/Purchase Order"] = [
	{
		label: "Purchase Order",
		url: docsUrl + "user/manual/en/purchase-order",
	},
	{
		label: "Recurring Purchase Order",
		url: docsUrl + "user/manual/en/auto-repeat",
	},
];

frappe.help.help_links["Form/Purchase Order"] = [
	{
		label: "Purchase Order",
		url: docsUrl + "user/manual/en/purchase-order",
	},
	{
		label: "Item UoM",
		url: docsUrl + "user/manual/en/purchasing-in-different-unit",
	},
	{
		label: "Supplier Item Code",
		url: docsUrl + "user/manual/en/maintaining-suppliers-part-no-in-item",
	},
	{
		label: "Recurring Purchase Order",
		url: docsUrl + "user/manual/en/auto-repeat",
	},
	{
		label: "Subcontracting",
		url: docsUrl + "user/manual/en/subcontracting",
	},
];

frappe.help.help_links["List/Purchase Taxes and Charges Template"] = [
	{
		label: "Setting Up Taxes",
		url: docsUrl + "user/manual/en/setting-up-taxes",
	},
];

frappe.help.help_links["List/Price List"] = [
	{
		label: "Price List",
		url: docsUrl + "user/manual/en/price-lists",
	},
];

frappe.help.help_links["List/Authorization Rule"] = [
	{
		label: "Authorization Rule",
		url: docsUrl + "user/manual/en/authorization-rule",
	},
];

frappe.help.help_links["Form/SMS Settings"] = [
	{
		label: "SMS Settings",
		url: docsUrl + "user/manual/en/sms-setting",
	},
];

frappe.help.help_links["List/Stock Reconciliation"] = [
	{
		label: "Stock Reconciliation",
		url: docsUrl + "user/manual/en/stock-reconciliation",
	},
];

frappe.help.help_links["Tree/Territory"] = [
	{
		label: "Territory",
		url: docsUrl + "user/manual/en/territory",
	},
];

frappe.help.help_links["List/Workflow"] = [
	{ label: "Workflow", url: docsUrl + "user/manual/en/workflows" },
];

frappe.help.help_links["List/Company"] = [
	{
		label: "Company",
		url: docsUrl + "user/manual/en/company-setup",
	},
	{
		label: "Delete All Related Transactions for a Company",
		url: docsUrl + "user/manual/en/delete_company_transactions",
	},
];

//Accounts

frappe.help.help_links["Tree/Account"] = [
	{
		label: "Chart of Accounts",
		url: docsUrl + "user/manual/en/chart-of-accounts",
	},
	{
		label: "Managing Tree Mastes",
		url: docsUrl + "user/manual/en/managing-tree-structure-masters",
	},
];

frappe.help.help_links["Form/Sales Invoice"] = [
	{
		label: "Sales Invoice",
		url: docsUrl + "user/manual/en/sales-invoice",
	},
	{
		label: "Accounts Opening Balance",
		url: docsUrl + "user/manual/en/opening-balance",
	},
	{
		label: "Sales Return",
		url: docsUrl + "user/manual/en/sales-return",
	},
	{
		label: "Recurring Sales Invoice",
		url: docsUrl + "user/manual/en/auto-repeat",
	},
];

frappe.help.help_links["List/Sales Invoice"] = [
	{
		label: "Sales Invoice",
		url: docsUrl + "user/manual/en/sales-invoice",
	},
	{
		label: "Accounts Opening Balance",
		url: docsUrl + "user/manual/en/opening-balance",
	},
	{
		label: "Sales Return",
		url: docsUrl + "user/manual/en/sales-return",
	},
	{
		label: "Recurring Sales Invoice",
		url: docsUrl + "user/manual/en/auto-repeat",
	},
];

frappe.help.help_links["point-of-sale"] = [
	{
		label: "Point of Sale Invoice",
		url: docsUrl + "user/manual/en/point-of-sales",
	},
];

frappe.help.help_links["List/POS Profile"] = [
	{
		label: "Point of Sale Profile",
		url: docsUrl + "user/manual/en/pos-profile",
	},
];

frappe.help.help_links["Form/POS Profile"] = [
	{
		label: "POS Profile",
		url: docsUrl + "user/manual/en/pos-profile",
	},
];

frappe.help.help_links["List/Purchase Invoice"] = [
	{
		label: "Purchase Invoice",
		url: docsUrl + "user/manual/en/purchase-invoice",
	},
	{
		label: "Accounts Opening Balance",
		url: docsUrl + "user/manual/en/opening-balance",
	},
	{
		label: "Recurring Purchase Invoice",
		url: docsUrl + "user/manual/en/auto-repeat",
	},
];

frappe.help.help_links["List/Journal Entry"] = [
	{
		label: "Journal Entry",
		url: docsUrl + "user/manual/en/journal-entry",
	},
	{
		label: "Advance Payment Entry",
		url: docsUrl + "user/manual/en/advance-payment-entry",
	},
	{
		label: "Accounts Opening Balance",
		url: docsUrl + "user/manual/en/opening-balance",
	},
];

frappe.help.help_links["List/Payment Entry"] = [
	{
		label: "Payment Entry",
		url: docsUrl + "user/manual/en/payment-entry",
	},
];

frappe.help.help_links["List/Payment Request"] = [
	{
		label: "Payment Request",
		url: docsUrl + "user/manual/en/payment-request",
	},
];

frappe.help.help_links["List/Asset"] = [
	{
		label: "Managing Fixed Assets",
		url: docsUrl + "user/manual/en/asset",
	},
];

frappe.help.help_links["List/Asset Category"] = [
	{
		label: "Asset Category",
		url: docsUrl + "user/manual/en/asset-category",
	},
];

frappe.help.help_links["Tree/Cost Center"] = [
	{ label: "Budgeting", url: docsUrl + "user/manual/en/budgeting" },
];

//Stock

frappe.help.help_links["List/Item"] = [
	{ label: "Item", url: docsUrl + "user/manual/en/item" },
	{
		label: "Item Price",
		url: docsUrl + "user/manual/en/item-price",
	},
	{
		label: "Barcode",
		url: docsUrl + "user/manual/en/track-items-using-barcode",
	},
	{
		label: "Item Wise Taxation",
		url: docsUrl + "user/manual/en/item-tax-template",
	},
	{
		label: "Managing Fixed Assets",
		url: docsUrl + "user/manual/en/asset",
	},
	{
		label: "Item Codification",
		url: docsUrl + "user/manual/en/item-codification",
	},
	{
		label: "Item Variants",
		url: docsUrl + "user/manual/en/item-variants",
	},
	{
		label: "Item Valuation",
		url:
			docsUrl +
			"user/manual/en/calculation-of-valuation-rate-in-fifo-and-moving-average",
	},
];

frappe.help.help_links["Form/Item"] = [
	{ label: "Item", url: docsUrl + "user/manual/en/item" },
	{
		label: "Item Price",
		url: docsUrl + "user/manual/en/item-price",
	},
	{
		label: "Barcode",
		url: docsUrl + "user/manual/en/track-items-using-barcode",
	},
	{
		label: "Item Wise Taxation",
		url: docsUrl + "user/manual/en/item-tax-template",
	},
	{
		label: "Managing Fixed Assets",
		url: docsUrl + "user/manual/en/asset",
	},
	{
		label: "Item Codification",
		url: docsUrl + "user/manual/en/item-codification",
	},
	{
		label: "Item Variants",
		url: docsUrl + "user/manual/en/item-variants",
	},
	{
		label: "Item Valuation",
		url: docsUrl + "user/manual/en/item-valuation-transactions",
	},
];

frappe.help.help_links["List/Purchase Receipt"] = [
	{
		label: "Purchase Receipt",
		url: docsUrl + "user/manual/en/purchase-receipt",
	},
	{
		label: "Barcode",
		url: docsUrl + "user/manual/en/track-items-using-barcode",
	},
];

frappe.help.help_links["List/Delivery Note"] = [
	{
		label: "Delivery Note",
		url: docsUrl + "user/manual/en/delivery-note",
	},
	{
		label: "Barcode",
		url: docsUrl + "user/manual/en/track-items-using-barcode",
	},
	{
		label: "Sales Return",
		url: docsUrl + "user/manual/en/sales-return",
	},
];

frappe.help.help_links["Form/Delivery Note"] = [
	{
		label: "Delivery Note",
		url: docsUrl + "user/manual/en/delivery-note",
	},
	{
		label: "Sales Return",
		url: docsUrl + "user/manual/en/sales-return",
	},
	{
		label: "Barcode",
		url: docsUrl + "user/manual/en/track-items-using-barcode",
	},
];

frappe.help.help_links["List/Installation Note"] = [
	{
		label: "Installation Note",
		url: docsUrl + "user/manual/en/installation-note",
	},
];

frappe.help.help_links["List/Budget"] = [
	{ label: "Budgeting", url: docsUrl + "user/manual/en/budgeting" },
];

frappe.help.help_links["List/Material Request"] = [
	{
		label: "Material Request",
		url: docsUrl + "user/manual/en/material-request",
	},
	{
		label: "Auto-creation of Material Request",
		url: docsUrl + "user/manual/en/auto-creation-of-material-request",
	},
];

frappe.help.help_links["Form/Material Request"] = [
	{
		label: "Material Request",
		url: docsUrl + "user/manual/en/material-request",
	},
	{
		label: "Auto-creation of Material Request",
		url: docsUrl + "user/manual/en/auto-creation-of-material-request",
	},
];

frappe.help.help_links["Form/Stock Entry"] = [
	{ label: "Stock Entry", url: docsUrl + "user/manual/en/stock-entry" },
	{
		label: "Stock Entry Types",
		url: docsUrl + "user/manual/en/stock-entry-purpose",
	},
	{
		label: "Repack Entry",
		url: docsUrl + "user/manual/en/repack-entry",
	},
	{
		label: "Opening Stock",
		url: docsUrl + "user/manual/en/opening-stock",
	},
	{
		label: "Subcontracting",
		url: docsUrl + "user/manual/en/subcontracting",
	},
];

frappe.help.help_links["List/Stock Entry"] = [
	{ label: "Stock Entry", url: docsUrl + "user/manual/en/stock-entry" },
];

frappe.help.help_links["Tree/Warehouse"] = [
	{ label: "Warehouse", url: docsUrl + "user/manual/en/warehouse" },
];

frappe.help.help_links["List/Serial No"] = [
	{ label: "Serial No", url: docsUrl + "user/manual/en/serial-no" },
];

frappe.help.help_links["Form/Serial No"] = [
	{ label: "Serial No", url: docsUrl + "user/manual/en/serial-no" },
];

frappe.help.help_links["List/Batch"] = [
	{ label: "Batch", url: docsUrl + "user/manual/en/batch" },
];

frappe.help.help_links["Form/Batch"] = [
	{ label: "Batch", url: docsUrl + "user/manual/en/batch" },
];

frappe.help.help_links["Form/Packing Slip"] = [
	{
		label: "Packing Slip",
		url: docsUrl + "user/manual/en/packing-slip",
	},
];

frappe.help.help_links["Form/Quality Inspection"] = [
	{
		label: "Quality Inspection",
		url: docsUrl + "user/manual/en/quality-inspection",
	},
];

frappe.help.help_links["Form/Landed Cost Voucher"] = [
	{
		label: "Landed Cost Voucher",
		url: docsUrl + "user/manual/en/landed-cost-voucher",
	},
];

frappe.help.help_links["Tree/Item Group"] = [
	{
		label: "Item Group",
		url: docsUrl + "user/manual/en/item-group",
	},
];

frappe.help.help_links["Form/Item Attribute"] = [
	{
		label: "Item Attribute",
		url: docsUrl + "user/manual/en/item-attribute",
	},
];

frappe.help.help_links["Form/UOM"] = [
	{
		label: "Fractions in UOM",
		url: docsUrl + "user/manual/en/managing-fractions-in-uom",
	},
];

frappe.help.help_links["Form/Stock Reconciliation"] = [
	{
		label: "Opening Stock Entry",
		url: docsUrl + "user/manual/en/stock-reconciliation",
	},
];

//CRM

frappe.help.help_links["Form/Lead"] = [
	{ label: "Lead", url: docsUrl + "user/manual/en/lead" },
];

frappe.help.help_links["Form/Opportunity"] = [
	{ label: "Opportunity", url: docsUrl + "user/manual/en/opportunity" },
];

frappe.help.help_links["Form/Address"] = [
	{ label: "Address", url: docsUrl + "user/manual/en/address" },
];

frappe.help.help_links["Form/Contact"] = [
	{ label: "Contact", url: docsUrl + "user/manual/en/contact" },
];

frappe.help.help_links["Form/Newsletter"] = [
	{ label: "Newsletter", url: docsUrl + "user/manual/en/newsletter" },
];

frappe.help.help_links["Form/Campaign"] = [
	{ label: "Campaign", url: docsUrl + "user/manual/en/campaign" },
];

frappe.help.help_links["Tree/Sales Person"] = [
	{
		label: "Sales Person",
		url: docsUrl + "user/manual/en/sales-person",
	},
];

frappe.help.help_links["Form/Sales Person"] = [
	{
		label: "Sales Person Target",
		url: docsUrl + "user/manual/en/sales-person-target-allocation",
	},
	{
		label: "Sales Person in Transactions",
		url: docsUrl + "user/manual/en/sales-persons-in-the-sales-transactions",
	},
];

//Manufacturing

frappe.help.help_links["Form/BOM"] = [
	{
		label: "Bill of Material",
		url: docsUrl + "user/manual/en/bill-of-materials",
	},
	{
		label: "Nested BOM Structure",
		url: docsUrl + "user/manual/en/managing-multi-level-bom",
	},
];

frappe.help.help_links["Form/Work Order"] = [
	{
		label: "Work Order",
		url: docsUrl + "user/manual/en/work-order",
	},
];

frappe.help.help_links["Form/Workstation"] = [
	{
		label: "Workstation",
		url: docsUrl + "user/manual/en/workstation",
	},
];

frappe.help.help_links["Form/Operation"] = [
	{
		label: "Operation",
		url: docsUrl + "user/manual/en/operation",
	},
];

frappe.help.help_links["Form/BOM Update Tool"] = [
	{
		label: "BOM Update Tool",
		url: docsUrl + "user/manual/en/bom-update-tool",
	},
];

//Customize

frappe.help.help_links["Form/Customize Form"] = [
	{
		label: "Custom Field",
		url: docsUrl + "user/manual/en/custom-field",
	},
	{
		label: "Customize Field",
		url: docsUrl + "user/manual/en/customize-form",
	},
];

frappe.help.help_links["List/Custom Field"] = [
	{
		label: "Custom Field",
		url: docsUrl + "user/manual/en/custom-field",
	},
];

frappe.help.help_links["Form/Custom Field"] = [
	{
		label: "Custom Field",
		url: docsUrl + "user/manual/en/custom-field",
	},
];
