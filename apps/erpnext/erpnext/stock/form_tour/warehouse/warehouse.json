{"creation": "2021-08-24 14:43:44.465237", "docstatus": 0, "doctype": "Form Tour", "first_document": 0, "idx": 0, "include_name_field": 0, "is_standard": 1, "list_name": "List", "modified": "2023-05-29 13:09:49.920796", "modified_by": "Administrator", "module": "Stock", "name": "Warehouse", "new_document_form": 0, "owner": "Administrator", "reference_doctype": "Warehouse", "save_on_complete": 1, "steps": [{"description": "Select a name for the warehouse. This should reflect its location or purpose.", "fieldname": "warehouse_name", "fieldtype": "Data", "has_next_condition": 1, "hide_buttons": 0, "is_table_field": 0, "label": "Warehouse Name", "modal_trigger": 0, "next_on_click": 0, "next_step_condition": "eval: doc.warehouse_name", "offset_x": 0, "offset_y": 0, "popover_element": 0, "position": "Bottom", "title": "Warehouse Name", "ui_tour": 0}, {"description": "Select an account to set a default account for all transactions with this warehouse.", "fieldname": "account", "fieldtype": "Link", "has_next_condition": 0, "hide_buttons": 0, "is_table_field": 0, "label": "Account", "modal_trigger": 0, "next_on_click": 0, "offset_x": 0, "offset_y": 0, "popover_element": 0, "position": "Top", "title": "Account", "ui_tour": 0}], "title": "Warehouse", "track_steps": 0, "ui_tour": 0}