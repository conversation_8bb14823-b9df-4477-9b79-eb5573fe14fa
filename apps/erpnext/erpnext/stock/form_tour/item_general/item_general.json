{"creation": "2021-12-02 10:37:55.433087", "docstatus": 0, "doctype": "Form Tour", "first_document": 0, "idx": 0, "include_name_field": 0, "is_standard": 1, "modified": "2021-12-02 10:37:55.433087", "modified_by": "Administrator", "module": "Stock", "name": "Item General", "owner": "Administrator", "reference_doctype": "<PERSON><PERSON>", "save_on_complete": 1, "steps": [{"description": "Enter code for the Item", "field": "", "fieldname": "item_code", "fieldtype": "Data", "has_next_condition": 0, "is_table_field": 0, "label": "Item Code", "parent_field": "", "position": "Right", "title": "Item Code"}, {"description": "Enter name for the Item", "field": "", "fieldname": "item_name", "fieldtype": "Data", "has_next_condition": 0, "is_table_field": 0, "label": "Item Name", "parent_field": "", "position": "Right", "title": "Item Name"}, {"description": "Select an Item Group", "field": "", "fieldname": "item_group", "fieldtype": "Link", "has_next_condition": 0, "is_table_field": 0, "label": "Item Group", "parent_field": "", "position": "Right", "title": "Item Group"}, {"description": "This is the default measuring unit that you will use for your product. It could be Nos, Kgs, Meters, etc.", "field": "", "fieldname": "stock_uom", "fieldtype": "Link", "has_next_condition": 0, "is_table_field": 0, "label": "Default Unit of Measure", "parent_field": "", "position": "Right", "title": "Default Unit of Measurement"}, {"description": "When creating an Item, entering a value for this field will automatically create an Item Price at the backend. Entering a value after the Item has been saved will not work. In this case, the Item Price is created from any transactions with the Item.", "field": "", "fieldname": "standard_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "has_next_condition": 0, "is_table_field": 0, "label": "Standard Selling Rate", "parent_field": "", "position": "Left", "title": "Standard Selling Rate"}], "title": "Item General"}