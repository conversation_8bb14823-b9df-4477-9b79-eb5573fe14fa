{"creation": "2021-08-24 17:56:40.754909", "docstatus": 0, "doctype": "Form Tour", "first_document": 0, "idx": 0, "include_name_field": 0, "is_standard": 1, "modified": "2021-11-24 17:59:44.559001", "modified_by": "Administrator", "module": "Stock", "name": "<PERSON><PERSON>", "owner": "Administrator", "reference_doctype": "<PERSON><PERSON>", "save_on_complete": 1, "steps": [{"description": "Enter code for Asset Item", "field": "", "fieldname": "item_code", "fieldtype": "Data", "has_next_condition": 0, "is_table_field": 0, "label": "Item Code", "parent_field": "", "position": "Bottom", "title": "Asset Item Code"}, {"description": "Enter name for Asset Item", "field": "", "fieldname": "item_name", "fieldtype": "Data", "has_next_condition": 0, "is_table_field": 0, "label": "Item Name", "parent_field": "", "position": "Bottom", "title": "<PERSON><PERSON> Item Name"}, {"description": "Select an Item Group", "field": "", "fieldname": "item_group", "fieldtype": "Link", "has_next_condition": 0, "is_table_field": 0, "label": "Item Group", "parent_field": "", "position": "Right", "title": "Item Group"}, {"description": "Check this field to make this an Asset Item", "field": "", "fieldname": "is_fixed_asset", "fieldtype": "Check", "has_next_condition": 1, "is_table_field": 0, "label": "Is Fixed Asset", "next_step_condition": "eval:doc.is_fixed_asset", "parent_field": "", "position": "Bottom", "title": "Is this a Fixed Asset?"}, {"description": "On checking it, the system will create an Asset automatically on purchase", "field": "", "fieldname": "auto_create_assets", "fieldtype": "Check", "has_next_condition": 1, "is_table_field": 0, "label": "Auto Create Assets on Purchase", "next_step_condition": "eval:doc.auto_create_assets", "parent_field": "", "position": "Bottom", "title": "Auto Create Asset on Purchase"}, {"description": "Select an Asset Category for this Asset Item", "field": "", "fieldname": "asset_category", "fieldtype": "Link", "has_next_condition": 0, "is_table_field": 0, "label": "Asset Category", "parent_field": "", "position": "Left", "title": "Asset Category"}, {"description": "Select a naming series which will be used to create an Asset automatically", "field": "", "fieldname": "asset_naming_series", "fieldtype": "Select", "has_next_condition": 0, "is_table_field": 0, "label": "Asset Naming Series", "parent_field": "", "position": "Left", "title": "Asset Naming Series"}], "title": "<PERSON><PERSON>"}