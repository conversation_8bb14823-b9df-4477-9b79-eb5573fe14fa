{"creation": "2021-07-29 12:32:08.929900", "docstatus": 0, "doctype": "Form Tour", "idx": 0, "is_standard": 1, "modified": "2021-10-05 13:11:13.119453", "modified_by": "Administrator", "module": "Stock", "name": "Material Request", "owner": "Administrator", "reference_doctype": "Material Request", "save_on_complete": 1, "steps": [{"description": "The purpose of the material request can be selected here. For now select \"Purchase\" as the purpose.", "field": "", "fieldname": "material_request_type", "fieldtype": "Select", "has_next_condition": 1, "is_table_field": 0, "label": "Purpose", "next_step_condition": "eval: doc.material_request_type == \"Purchase\"", "parent_field": "", "position": "Bottom", "title": "Purpose"}, {"description": "Set the \"Required By\" date for the materials. This sets the \"Required By\" date for all the items.", "field": "", "fieldname": "schedule_date", "fieldtype": "Date", "has_next_condition": 0, "is_table_field": 0, "label": "Required By", "next_step_condition": "", "parent_field": "", "position": "Left", "title": "Required By"}, {"description": "Setting the target warehouse sets it for all the items.", "field": "", "fieldname": "set_warehouse", "fieldtype": "Link", "has_next_condition": 0, "is_table_field": 0, "label": "Set Target Warehouse", "next_step_condition": "", "parent_field": "", "position": "Left", "title": "Target Warehouse"}, {"description": "Items table", "field": "", "fieldname": "items", "fieldtype": "Table", "has_next_condition": 0, "is_table_field": 0, "label": "Items", "parent_field": "", "position": "Bottom", "title": "Items"}, {"child_doctype": "Material Request Item", "description": "Select an Item code. Item details will be fetched automatically.", "field": "", "fieldname": "item_code", "fieldtype": "Link", "has_next_condition": 1, "is_table_field": 1, "label": "Item Code", "next_step_condition": "eval: doc.item_code", "parent_field": "", "parent_fieldname": "items", "position": "Right", "title": "Item Code"}, {"child_doctype": "Material Request Item", "description": "Enter the required quantity for the material.", "field": "", "fieldname": "qty", "fieldtype": "Float", "has_next_condition": 0, "is_table_field": 1, "label": "Quantity", "parent_field": "", "parent_fieldname": "items", "position": "Bottom", "title": "Quantity"}], "title": "Material Request"}