{"creation": "2021-08-24 14:44:46.770952", "docstatus": 0, "doctype": "Form Tour", "first_document": 0, "idx": 0, "include_name_field": 0, "is_standard": 1, "list_name": "List", "modified": "2023-05-29 13:38:27.192177", "modified_by": "Administrator", "module": "Stock", "name": "Stock Reconciliation", "new_document_form": 0, "owner": "Administrator", "reference_doctype": "Stock Reconciliation", "save_on_complete": 1, "steps": [{"description": "Set Purpose to Opening Stock to set the stock opening balance.", "fieldname": "purpose", "fieldtype": "Select", "has_next_condition": 1, "hide_buttons": 0, "is_table_field": 0, "label": "Purpose", "modal_trigger": 0, "next_on_click": 0, "next_step_condition": "eval: doc.purpose === \"Opening Stock\"", "offset_x": 0, "offset_y": 0, "popover_element": 0, "position": "Top", "title": "Purpose", "ui_tour": 0}, {"description": "Edit the Posting Date by clicking on the Edit Posting Date and Time checkbox below.", "fieldname": "posting_date", "fieldtype": "Date", "has_next_condition": 0, "hide_buttons": 0, "is_table_field": 0, "label": "Posting Date", "modal_trigger": 0, "next_on_click": 0, "offset_x": 0, "offset_y": 0, "popover_element": 0, "position": "Bottom", "title": "Posting Date", "ui_tour": 0}, {"description": "Select the items for which the opening stock has to be set.", "fieldname": "items", "fieldtype": "Table", "has_next_condition": 1, "hide_buttons": 0, "is_table_field": 0, "label": "Items", "modal_trigger": 0, "next_on_click": 0, "next_step_condition": "eval: doc.items[0]?.item_code", "offset_x": 0, "offset_y": 0, "popover_element": 0, "position": "Top", "title": "Items", "ui_tour": 0}], "title": "Stock Reconciliation", "track_steps": 0, "ui_tour": 0}