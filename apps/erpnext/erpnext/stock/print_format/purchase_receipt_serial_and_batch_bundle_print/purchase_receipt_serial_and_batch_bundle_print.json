{"absolute_value": 0, "align_labels_right": 0, "creation": "2023-06-01 23:07:25.776606", "custom_format": 0, "disabled": 0, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype": "Print Format", "font_size": 14, "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<div class=\\\"print-heading\\\">\\t\\t\\t\\t<h2><div>Purchase Receipt</div><br><small class=\\\"sub-heading\\\">{{ doc.name }}</small>\\t\\t\\t\\t</h2></div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"supplier_name\", \"print_hide\": 0, \"label\": \"Supplier Name\"}, {\"fieldname\": \"supplier_delivery_note\", \"print_hide\": 0, \"label\": \"Supplier Delivery Note\"}, {\"fieldname\": \"rack\", \"print_hide\": 0, \"label\": \"Rack\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"posting_date\", \"print_hide\": 0, \"label\": \"Date\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"apply_putaway_rule\", \"print_hide\": 0, \"label\": \"Apply Putaway Rule\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Accounting Dimensions\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"region\", \"print_hide\": 0, \"label\": \"Region\"}, {\"fieldname\": \"function\", \"print_hide\": 0, \"label\": \"Function\"}, {\"fieldname\": \"depot\", \"print_hide\": 0, \"label\": \"Depot\"}, {\"fieldname\": \"cost_center\", \"print_hide\": 0, \"label\": \"Cost Center\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"location\", \"print_hide\": 0, \"label\": \"Location\"}, {\"fieldname\": \"country\", \"print_hide\": 0, \"label\": \"Country\"}, {\"fieldname\": \"project\", \"print_hide\": 0, \"label\": \"Project\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Items\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"scan_barcode\", \"print_hide\": 0, \"label\": \"Scan Barcode\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"set_from_warehouse\", \"print_hide\": 0, \"label\": \"Set From Warehouse\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table class=\\\"table table-bordered\\\">\\n\\t<tbody>\\n\\t\\t<tr>\\n\\t\\t\\t<th>Sr</th>\\n\\t\\t\\t<th>Item Name</th>\\n\\t\\t\\t<th>Description</th>\\n\\t\\t\\t<th class=\\\"text-right\\\">Qty</th>\\n\\t\\t\\t<th class=\\\"text-right\\\">Rate</th>\\n\\t\\t\\t<th class=\\\"text-right\\\">Amount</th>\\n\\t\\t</tr>\\n\\t\\t{%- for row in doc.items -%}\\n\\t\\t<tr>\\n\\t\\t    {% set bundle_data = get_serial_or_batch_nos(row.serial_and_batch_bundle) %}\\n\\t\\t    {% set serial_nos = [] %}\\n            {% set batches = {} %}\\n\\n\\t\\t\\t<td style=\\\"width: 4%;\\\">{{ row.idx }}</td>\\n\\t\\t\\t<td style=\\\"width: 20%;\\\">\\n\\t\\t\\t\\t{{ row.item_name }}\\n\\t\\t\\t\\t{% if row.item_code != row.item_name -%}\\n\\t\\t\\t\\t<br>Item Code: {{ row.item_code}}\\n\\t\\t\\t\\t{%- endif %}\\n\\t\\t\\t</td>\\n\\t\\t\\t<td style=\\\"width: 30%;\\\">\\n\\t\\t\\t\\t<div style=\\\"border: 0px;\\\">{{ row.description }}</div></td>\\n\\t\\t\\t<td style=\\\"width: 10%; text-align: right;\\\">{{ row.qty }} {{ row.uom or row.stock_uom }}</td>\\n\\t\\t\\t<td style=\\\"width: 18%; text-align: right;\\\">{{\\n\\t\\t\\t\\trow.get_formatted(\\\"rate\\\", doc) }}</td>\\n\\t\\t\\t<td style=\\\"width: 18%; text-align: right;\\\">{{\\n\\t\\t\\t\\trow.get_formatted(\\\"amount\\\", doc) }}</td>\\n\\t\\t\\t\\n\\t\\t</tr>\\n\\t\\t{%- endfor -%}\\n\\t</tbody>\\n</table>\\n\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"total_qty\", \"print_hide\": 0, \"label\": \"Total Quantity\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"total\", \"print_hide\": 0, \"label\": \"Total\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"taxes\", \"print_hide\": 0, \"label\": \"Purchase Taxes and Charges\", \"visible_columns\": [{\"fieldname\": \"category\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"add_deduct_tax\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"charge_type\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"row_id\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"included_in_print_rate\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"included_in_paid_amount\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"account_head\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"description\", \"print_width\": \"300px\", \"print_hide\": 0}, {\"fieldname\": \"rate\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"region\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"function\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"location\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"cost_center\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"depot\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"country\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"account_currency\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"tax_amount\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"total\", \"print_width\": \"\", \"print_hide\": 0}]}, {\"fieldtype\": \"Section Break\", \"label\": \"Totals\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"grand_total\", \"print_hide\": 0, \"label\": \"Grand Total\"}, {\"fieldname\": \"rounded_total\", \"print_hide\": 0, \"label\": \"Rounded Total\"}, {\"fieldname\": \"in_words\", \"print_hide\": 0, \"label\": \"In Words\"}, {\"fieldname\": \"disable_rounded_total\", \"print_hide\": 0, \"label\": \"Disable Rounded Total\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Supplier Address\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"address_display\", \"print_hide\": 0, \"label\": \"Address\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"contact_display\", \"print_hide\": 0, \"label\": \"Contact\"}, {\"fieldname\": \"contact_mobile\", \"print_hide\": 0, \"label\": \"Mobile No\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Company Billing Address\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"billing_address\", \"print_hide\": 0, \"label\": \"Billing Address\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"billing_address_display\", \"print_hide\": 0, \"label\": \"Billing Address\"}, {\"fieldname\": \"terms\", \"print_hide\": 0, \"label\": \"Terms and Conditions\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table class=\\\"table table-bordered\\\">\\n\\t<tbody>\\n\\t\\t<tr>\\n\\t\\t\\t<th>Sr</th>\\n\\t\\t\\t<th>Item Name</th>\\n\\t\\t\\t<th>Qty</th>\\n\\t\\t\\t<th class=\\\"text-left\\\">Serial Nos</th>\\n\\t\\t\\t<th class=\\\"text-left\\\">Batch Nos (Qty)</th>\\n\\t\\t</tr>\\n\\t\\t{%- for row in doc.items -%}\\n\\t\\t<tr>\\n\\t\\t    {% set bundle_data = frappe.get_all(\\\"Serial and Batch Entry\\\", \\n\\t\\t        fields=[\\\"serial_no\\\", \\\"batch_no\\\", \\\"qty\\\"], \\n\\t\\t        filters={\\\"parent\\\": row.serial_and_batch_bundle}) %}\\n\\t\\t    {% set serial_nos = [] %}\\n            {% set batches = {} %}\\n            \\n            {% if bundle_data %}\\n\\t\\t\\t    {% for data in bundle_data %}\\n\\t\\t\\t        {% if data.serial_no %}\\n\\t\\t\\t            {{ serial_nos.append(data.serial_no) or \\\"\\\" }}\\n\\t\\t\\t        {% endif %}\\n\\t\\t\\t        \\n\\t\\t\\t        {% if data.batch_no %}\\n\\t\\t\\t            {{ batches.update({data.batch_no: data.qty}) or \\\"\\\" }}\\n\\t\\t\\t        {% endif %}\\n\\t\\t\\t    {% endfor %}\\n\\t\\t\\t{% endif %}\\n\\n\\t\\t\\t<td style=\\\"width: 3%;\\\">{{ row.idx }}</td>\\n\\t\\t\\t<td style=\\\"width: 20%;\\\">\\n\\t\\t\\t\\t{{ row.item_name }}\\n\\t\\t\\t\\t{% if row.item_code != row.item_name -%}\\n\\t\\t\\t\\t<br>Item Code: {{ row.item_code}}\\n\\t\\t\\t\\t{%- endif %}\\n\\t\\t\\t</td>\\n\\t\\t\\t<td style=\\\"width: 10%; text-align: right;\\\">{{ row.qty }} {{ row.uom or row.stock_uom }}</td>\\n\\t\\t\\t\\n\\t\\t\\t<td style=\\\"width: 30%; text-align: left;\\\">{{ serial_nos|join(',') }}</td>\\n\\t\\t\\t<td style=\\\"width: 30%;\\\">\\n\\t\\t\\t    {% if batches %}\\n                    {% for batch_no, qty in batches.items() %}\\n                        <p> {{batch_no}} : {{qty}} {{ row.uom or row.stock_uom }} </p>\\n                    {% endfor %}\\n                {% endif %}\\n\\t\\t\\t</td>\\n\\t\\t\\t\\n\\t\\t</tr>\\n\\t\\t{%- endfor -%}\\n\\t</tbody>\\n</table>\\n\"}]", "idx": 0, "line_breaks": 0, "margin_bottom": 15.0, "margin_left": 15.0, "margin_right": 15.0, "margin_top": 15.0, "modified": "2023-06-26 14:51:20.609682", "modified_by": "Administrator", "module": "Stock", "name": "Purchase Receipt Serial and Batch Bundle Print", "owner": "Administrator", "page_number": "<PERSON>de", "print_format_builder": 1, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}