{"charts": [{"chart_name": "Warehouse wise Stock Value", "label": "Warehouse wise Stock Value"}], "content": "[{\"id\":\"BJTnTemGjc\",\"type\":\"onboarding\",\"data\":{\"onboarding_name\":\"Stock\",\"col\":12}},{\"id\":\"WKeeHLcyXI\",\"type\":\"number_card\",\"data\":{\"number_card_name\":\"Total Stock Value\",\"col\":4}},{\"id\":\"6nVoOHuy5w\",\"type\":\"number_card\",\"data\":{\"number_card_name\":\"Total Warehouses\",\"col\":4}},{\"id\":\"OUex5VED7d\",\"type\":\"number_card\",\"data\":{\"number_card_name\":\"Total Active Items\",\"col\":4}},{\"id\":\"A3svBa974t\",\"type\":\"chart\",\"data\":{\"chart_name\":\"Warehouse wise Stock Value\",\"col\":12}},{\"id\":\"wwAoBx30p3\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"LkqrpJHM9X\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Quick Access</b></span>\",\"col\":12}},{\"id\":\"OR8PYiYspy\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Item\",\"col\":3}},{\"id\":\"KP1A22WjDl\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Material Request\",\"col\":3}},{\"id\":\"0EYKOrx6U1\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Stock Entry\",\"col\":3}},{\"id\":\"cqotiphmhZ\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Purchase Receipt\",\"col\":3}},{\"id\":\"Xhjqnm-JxZ\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Delivery Note\",\"col\":3}},{\"id\":\"yxCx6Tay4Z\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Stock Ledger\",\"col\":3}},{\"id\":\"o3sdEnNy34\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Stock Balance\",\"col\":3}},{\"id\":\"m9O0HUUDS5\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Dashboard\",\"col\":3}},{\"id\":\"NwWcNC_xNj\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Learn Inventory Management\",\"col\":3}},{\"id\":\"9AmAh9LnPI\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"3SmmwBbOER\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Masters &amp; Reports</b></span>\",\"col\":12}},{\"id\":\"OAGNH9njt7\",\"type\":\"card\",\"data\":{\"card_name\":\"Items Catalogue\",\"col\":4}},{\"id\":\"jF9eKz0qr0\",\"type\":\"card\",\"data\":{\"card_name\":\"Stock Transactions\",\"col\":4}},{\"id\":\"tyTnQo-MIS\",\"type\":\"card\",\"data\":{\"card_name\":\"Stock Reports\",\"col\":4}},{\"id\":\"dJaJw6YNPU\",\"type\":\"card\",\"data\":{\"card_name\":\"Settings\",\"col\":4}},{\"id\":\"rQf5vK4N_T\",\"type\":\"card\",\"data\":{\"card_name\":\"Serial No and Batch\",\"col\":4}},{\"id\":\"7oM7hFL4v8\",\"type\":\"card\",\"data\":{\"card_name\":\"Tools\",\"col\":4}},{\"id\":\"ve3L6ZifkB\",\"type\":\"card\",\"data\":{\"card_name\":\"Key Reports\",\"col\":4}},{\"id\":\"8Kfvu3umw7\",\"type\":\"card\",\"data\":{\"card_name\":\"Other Reports\",\"col\":4}}]", "creation": "2020-03-02 15:43:10.096528", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "stock", "idx": 0, "is_hidden": 0, "label": "Stock", "links": [{"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Item Group", "link_count": 0, "link_to": "Item Group", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Product Bundle", "link_count": 0, "link_to": "Product Bundle", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Price List", "link_count": 0, "link_to": "Price List", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Shipping Rule", "link_count": 0, "link_to": "Shipping Rule", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Pricing Rule", "link_count": 0, "link_to": "Pricing Rule", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Item Alternative", "link_count": 0, "link_to": "Item Alternative", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Item Manufacturer", "link_count": 0, "link_to": "Item Manufacturer", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Customs Tariff Number", "link_count": 0, "link_to": "Customs Tariff Number", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Stock Transactions", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "Material Request", "link_count": 0, "link_to": "Material Request", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "Stock Entry", "link_count": 0, "link_to": "Stock Entry", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>, Customer", "hidden": 0, "is_query_report": 0, "label": "Delivery Note", "link_count": 0, "link_to": "Delivery Note", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "Item, Supplier", "hidden": 0, "is_query_report": 0, "label": "Purchase Receipt", "link_count": 0, "link_to": "Purchase Receipt", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "Pick List", "link_count": 0, "link_to": "Pick List", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Delivery Trip", "link_count": 0, "link_to": "Delivery Trip", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Settings", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Stock Settings", "link_count": 0, "link_to": "Stock Settings", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Warehouse", "link_count": 0, "link_to": "Warehouse", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Unit of Measure (UOM)", "link_count": 0, "link_to": "UOM", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Brand", "link_count": 0, "link_to": "Brand", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Item Attribute", "link_count": 0, "link_to": "Item Attribute", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "UOM Conversion Factor", "link_count": 0, "link_to": "UOM Conversion Factor", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Serial No and Batch", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "Serial No", "link_count": 0, "link_to": "Serial No", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "Installation Note", "link_count": 0, "link_to": "Installation Note", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "Serial No", "hidden": 0, "is_query_report": 0, "label": "Serial No Service Contract Expiry", "link_count": 0, "link_to": "Serial No Service Contract Expiry", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Serial No", "hidden": 0, "is_query_report": 0, "label": "Serial No Status", "link_count": 0, "link_to": "Serial No Status", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Serial No", "hidden": 0, "is_query_report": 0, "label": "Serial No Warranty Expiry", "link_count": 0, "link_to": "Serial No Warranty Expiry", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Tools", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Stock Reconciliation", "link_count": 0, "link_to": "Stock Reconciliation", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Landed Cost Voucher", "link_count": 0, "link_to": "Landed Cost Voucher", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Packing Slip", "link_count": 0, "link_to": "Packing Slip", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Quality Inspection", "link_count": 0, "link_to": "Quality Inspection", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Quality Inspection Template", "link_count": 0, "link_to": "Quality Inspection Template", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Quick Stock Balance", "link_count": 0, "link_to": "Quick Stock Balance", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Other Reports", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "Material Request", "hidden": 0, "is_query_report": 1, "label": "Requested Items To Be Transferred", "link_count": 0, "link_to": "Requested Items To Be Transferred", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Stock Ledger Entry", "hidden": 0, "is_query_report": 1, "label": "Batch Item Expiry Status", "link_count": 0, "link_to": "Batch Item Expiry Status", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Price List", "hidden": 0, "is_query_report": 1, "label": "Item Prices", "link_count": 0, "link_to": "Item Prices", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 1, "label": "Itemwise Recommended Reorder Level", "link_count": 0, "link_to": "Itemwise Recommended Reorder Level", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 1, "label": "<PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Purchase Order", "hidden": 0, "is_query_report": 1, "label": "Subcontracted Raw Materials To Be Transferred", "link_count": 0, "link_to": "Subcontracted Raw Materials To Be Transferred", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Purchase Order", "hidden": 0, "is_query_report": 1, "label": "Subcontracted Item To Be Received", "link_count": 0, "link_to": "Subcontracted Item To Be Received", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Stock Reports", "link_count": 7, "onboard": 0, "type": "Card Break"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 1, "label": "<PERSON> Ledger", "link_count": 0, "link_to": "<PERSON> Ledger", "link_type": "Report", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 1, "label": "Stock Balance", "link_count": 0, "link_to": "Stock Balance", "link_type": "Report", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 1, "label": "Stock Projected Qty", "link_count": 0, "link_to": "Stock Projected Qty", "link_type": "Report", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "Stock Summary", "link_count": 0, "link_to": "stock-balance", "link_type": "Page", "onboard": 0, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 1, "label": "Stock Ageing", "link_count": 0, "link_to": "Stock Ageing", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 1, "label": "<PERSON>em <PERSON>", "link_count": 0, "link_to": "<PERSON>em <PERSON>", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Warehouse Wise Stock Balance", "link_count": 0, "link_to": "Warehouse Wise Stock Balance", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Items Catalogue", "link_count": 6, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Item Group", "link_count": 0, "link_to": "Item Group", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Product Bundle", "link_count": 0, "link_to": "Product Bundle", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Shipping Rule", "link_count": 0, "link_to": "Shipping Rule", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Item Alternative", "link_count": 0, "link_to": "Item Alternative", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Item Manufacturer", "link_count": 0, "link_to": "Item Manufacturer", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Key Reports", "link_count": 7, "onboard": 0, "type": "Card Break"}, {"dependencies": "Stock Entry", "hidden": 0, "is_query_report": 1, "label": "Stock Analytics", "link_count": 0, "link_to": "Stock Analytics", "link_type": "Report", "onboard": 1, "type": "Link"}, {"dependencies": "Delivery Note", "hidden": 0, "is_query_report": 1, "label": "Delivery Note Trends", "link_count": 0, "link_to": "Delivery Note Trends", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Purchase Receipt", "hidden": 0, "is_query_report": 1, "label": "Purchase Receipt Trends", "link_count": 0, "link_to": "Purchase Receipt Trends", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Sales Order", "hidden": 0, "is_query_report": 1, "label": "Sales Order Analysis", "link_count": 0, "link_to": "Sales Order Analysis", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Purchase Order", "hidden": 0, "is_query_report": 1, "label": "Purchase Order Analysis", "link_count": 0, "link_to": "Purchase Order Analysis", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Bin", "hidden": 0, "is_query_report": 1, "label": "Item Shortage Report", "link_count": 0, "link_to": "Item Shortage Report", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 1, "label": "Batch-Wise Balance History", "link_count": 0, "link_to": "Batch-Wise Balance History", "link_type": "Report", "onboard": 0, "type": "Link"}], "modified": "2023-07-04 14:38:14.988756", "modified_by": "Administrator", "module": "Stock", "name": "Stock", "number_cards": [{"label": "Total Warehouses", "number_card_name": "Total Warehouses"}, {"label": "Total Stock Value", "number_card_name": "Total Stock Value"}, {"label": "Total Active Items", "number_card_name": "Total Active Items"}], "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [], "restrict_to_domain": "", "roles": [], "sequence_id": 7.0, "shortcuts": [{"color": "Green", "format": "{}  Available", "label": "<PERSON><PERSON>", "link_to": "<PERSON><PERSON>", "stats_filter": "{\n    \"disabled\" : 0\n}", "type": "DocType"}, {"color": "Grey", "doc_view": "List", "label": "Learn Inventory Management", "type": "URL", "url": "https://frappe.school/courses/inventory-management?utm_source=in_app"}, {"color": "Yellow", "format": "{} Pending", "label": "Material Request", "link_to": "Material Request", "stats_filter": "{\n    \"company\": [\"like\", '%' + frappe.defaults.get_global_default(\"company\") + '%'],\n    \"status\": \"Pending\"\n}", "type": "DocType"}, {"label": "Stock Entry", "link_to": "Stock Entry", "type": "DocType"}, {"color": "Yellow", "format": "{} To Bill", "label": "Purchase Receipt", "link_to": "Purchase Receipt", "stats_filter": "{\n    \"company\": [\"like\", '%' + frappe.defaults.get_global_default(\"company\") + '%'],\n    \"status\": \"To Bill\"\n}", "type": "DocType"}, {"color": "Yellow", "format": "{} To Bill", "label": "Delivery Note", "link_to": "Delivery Note", "stats_filter": "{\n    \"company\": [\"like\", '%' + frappe.defaults.get_global_default(\"company\") + '%'],\n    \"status\": \"To Bill\"\n}", "type": "DocType"}, {"label": "<PERSON> Ledger", "link_to": "<PERSON> Ledger", "type": "Report"}, {"label": "Stock Balance", "link_to": "Stock Balance", "type": "Report"}, {"label": "Dashboard", "link_to": "Stock", "type": "Dashboard"}], "title": "Stock"}