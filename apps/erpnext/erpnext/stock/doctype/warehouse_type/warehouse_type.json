{"autoname": "Prompt", "creation": "2019-05-21 15:27:06.514511", "doctype": "DocType", "engine": "InnoDB", "field_order": ["description"], "fields": [{"fieldname": "description", "fieldtype": "Small Text", "label": "Description"}], "modified": "2019-05-27 18:35:33.354571", "modified_by": "Administrator", "module": "Stock", "name": "Warehouse Type", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "<PERSON>em Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User", "share": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase User", "share": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Manufacturing User", "share": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "ASC", "track_changes": 1}