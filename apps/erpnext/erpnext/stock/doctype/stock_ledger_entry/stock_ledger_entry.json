{"actions": [], "allow_copy": 1, "autoname": "MAT-SLE-.YYYY.-.#####", "creation": "2013-01-29 19:25:42", "doctype": "DocType", "document_type": "Other", "engine": "InnoDB", "field_order": ["item_code", "warehouse", "posting_date", "posting_time", "posting_datetime", "is_adjustment_entry", "auto_created_serial_and_batch_bundle", "column_break_6", "voucher_type", "voucher_no", "voucher_detail_no", "serial_and_batch_bundle", "dependant_sle_voucher_detail_no", "section_break_11", "recalculate_rate", "actual_qty", "qty_after_transaction", "incoming_rate", "outgoing_rate", "column_break_17", "valuation_rate", "stock_value", "stock_value_difference", "stock_queue", "section_break_21", "company", "stock_uom", "project", "column_break_26", "fiscal_year", "has_batch_no", "has_serial_no", "is_cancelled", "to_rename", "serial_no", "batch_no"], "fields": [{"fieldname": "item_code", "fieldtype": "Link", "in_filter": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "print_width": "100px", "read_only": 1, "search_index": 1, "width": "100px"}, {"fieldname": "serial_no", "fieldtype": "Long Text", "label": "Serial No", "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "batch_no", "fieldtype": "Data", "label": "Batch No", "oldfieldname": "batch_no", "oldfieldtype": "Data", "read_only": 1}, {"fieldname": "warehouse", "fieldtype": "Link", "in_filter": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Warehouse", "oldfieldname": "warehouse", "oldfieldtype": "Link", "options": "Warehouse", "print_width": "100px", "read_only": 1, "search_index": 1, "width": "100px"}, {"fieldname": "posting_date", "fieldtype": "Date", "in_filter": 1, "in_list_view": 1, "label": "Posting Date", "oldfieldname": "posting_date", "oldfieldtype": "Date", "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "posting_time", "fieldtype": "Time", "label": "Posting Time", "oldfieldname": "posting_time", "oldfieldtype": "Time", "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "voucher_type", "fieldtype": "Link", "in_filter": 1, "label": "Voucher Type", "oldfieldname": "voucher_type", "oldfieldtype": "Data", "options": "DocType", "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "voucher_no", "fieldtype": "Dynamic Link", "in_filter": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Voucher No", "oldfieldname": "voucher_no", "oldfieldtype": "Data", "options": "voucher_type", "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "voucher_detail_no", "fieldtype": "Data", "label": "Voucher Detail No", "oldfieldname": "voucher_detail_no", "oldfieldtype": "Data", "print_width": "150px", "read_only": 1, "search_index": 1, "width": "150px"}, {"fieldname": "actual_qty", "fieldtype": "Float", "in_filter": 1, "in_list_view": 1, "label": "Qty Change", "oldfieldname": "actual_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "incoming_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Incoming Rate", "oldfieldname": "incoming_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "outgoing_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Outgoing Rate", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "oldfieldname": "stock_uom", "oldfieldtype": "Data", "options": "UOM", "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "qty_after_transaction", "fieldtype": "Float", "in_filter": 1, "label": "Qty After Transaction", "oldfieldname": "bin_a<PERSON>t", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "valuation_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Valuation Rate", "oldfieldname": "valuation_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "stock_value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Balance Stock Value", "oldfieldname": "stock_value", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "stock_value_difference", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Change in Stock Value", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "stock_queue", "fieldtype": "Text", "label": "FIFO Stock Queue (qty, rate)", "oldfieldname": "fcfs_stack", "oldfieldtype": "Text", "print_hide": 1, "read_only": 1}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project"}, {"fieldname": "company", "fieldtype": "Link", "in_filter": 1, "label": "Company", "oldfieldname": "company", "oldfieldtype": "Data", "options": "Company", "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "fiscal_year", "fieldtype": "Data", "in_filter": 1, "label": "Fiscal Year", "oldfieldname": "fiscal_year", "oldfieldtype": "Data", "print_width": "150px", "read_only": 1, "width": "150px"}, {"default": "0", "fieldname": "is_cancelled", "fieldtype": "Check", "label": "Is Cancelled", "report_hide": 1}, {"default": "1", "fieldname": "to_rename", "fieldtype": "Check", "hidden": 1, "label": "<PERSON>", "search_index": 1}, {"fieldname": "dependant_sle_voucher_detail_no", "fieldtype": "Data", "label": "Dependant SLE Voucher Detail No"}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"fieldname": "section_break_11", "fieldtype": "Section Break"}, {"fieldname": "column_break_17", "fieldtype": "Column Break"}, {"fieldname": "section_break_21", "fieldtype": "Section Break"}, {"fieldname": "column_break_26", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "recalculate_rate", "fieldtype": "Check", "label": "Recalculate Incoming/Outgoing Rate", "no_copy": 1, "read_only": 1}, {"fieldname": "serial_and_batch_bundle", "fieldtype": "Link", "label": "Serial and Batch Bundle", "options": "Serial and Batch Bundle", "search_index": 1}, {"default": "0", "fetch_from": "item_code.has_batch_no", "fieldname": "has_batch_no", "fieldtype": "Check", "label": "Has Batch No"}, {"default": "0", "fetch_from": "item_code.has_serial_no", "fieldname": "has_serial_no", "fieldtype": "Check", "label": "Has Serial No"}, {"default": "0", "fieldname": "is_adjustment_entry", "fieldtype": "Check", "label": "Is Adjustment Entry"}, {"default": "0", "depends_on": "serial_and_batch_bundle", "fieldname": "auto_created_serial_and_batch_bundle", "fieldtype": "Check", "label": "Auto Created Serial and Batch Bundle"}, {"fieldname": "posting_datetime", "fieldtype": "Datetime", "label": "Posting Datetime"}], "hide_toolbar": 1, "icon": "fa fa-list", "idx": 1, "in_create": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2024-02-07 09:18:13.999231", "modified_by": "Administrator", "module": "Stock", "name": "Stock Ledger Entry", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User"}, {"export": 1, "read": 1, "report": 1, "role": "Accounts Manager"}], "sort_field": "modified", "sort_order": "DESC", "states": []}