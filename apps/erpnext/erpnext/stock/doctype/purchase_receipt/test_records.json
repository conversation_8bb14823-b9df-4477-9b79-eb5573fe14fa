[{"buying_price_list": "_Test Price List", "company": "_Test Company", "conversion_rate": 1.0, "currency": "INR", "doctype": "Purchase Receipt", "base_grand_total": 720.0, "naming_series": "_T-Purchase Receipt-", "base_net_total": 500.0, "taxes": [{"account_head": "_Test Account Shipping Charges - _TC", "add_deduct_tax": "Add", "category": "Valuation and Total", "charge_type": "Actual", "description": "Shipping Charges", "doctype": "Purchase Taxes and Charges", "parentfield": "taxes", "rate": 100.0, "tax_amount": 100.0, "cost_center": "Main - _TC"}, {"account_head": "_Test Account VAT - _TC", "add_deduct_tax": "Add", "category": "Total", "charge_type": "Actual", "description": "VAT", "doctype": "Purchase Taxes and Charges", "parentfield": "taxes", "rate": 120.0, "tax_amount": 120.0, "cost_center": "Main - _TC"}, {"account_head": "_Test Account Customs Duty - _TC", "add_deduct_tax": "Add", "category": "Valuation", "charge_type": "Actual", "description": "Customs Duty", "doctype": "Purchase Taxes and Charges", "parentfield": "taxes", "rate": 150.0, "tax_amount": 150.0, "cost_center": "Main - _TC"}], "items": [{"base_amount": 250.0, "conversion_factor": 1.0, "description": "_Test Item", "doctype": "Purchase Receipt Item", "item_code": "_Test Item", "item_name": "_Test Item", "parentfield": "items", "qty": 5.0, "rate": 50.0, "received_qty": 5.0, "rejected_qty": 0.0, "stock_uom": "_Test UOM", "uom": "_Test UOM", "warehouse": "_Test Warehouse - _TC", "cost_center": "Main - _TC"}, {"base_amount": 250.0, "conversion_factor": 1.0, "description": "_Test Item Home Desktop 100", "doctype": "Purchase Receipt Item", "item_code": "_Test Item Home Desktop 100", "item_name": "_Test Item Home Desktop 100", "parentfield": "items", "qty": 5.0, "rate": 50.0, "received_qty": 5.0, "rejected_qty": 0.0, "stock_uom": "_Test UOM", "uom": "_Test UOM", "warehouse": "_Test Warehouse 1 - _TC", "cost_center": "Main - _TC"}], "supplier": "_Test Supplier"}]