{"actions": [], "allow_auto_repeat": 1, "allow_import": 1, "autoname": "naming_series:", "creation": "2013-05-21 16:16:39", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["supplier_section", "column_break0", "title", "naming_series", "supplier", "supplier_name", "supplier_delivery_note", "subcontracting_receipt", "column_break1", "posting_date", "posting_time", "set_posting_time", "column_break_12", "company", "apply_putaway_rule", "is_return", "return_against", "accounting_dimensions_section", "cost_center", "dimension_col_break", "project", "currency_and_price_list", "currency", "conversion_rate", "column_break2", "buying_price_list", "price_list_currency", "plc_conversion_rate", "ignore_pricing_rule", "sec_warehouse", "scan_barcode", "column_break_31", "set_warehouse", "set_from_warehouse", "col_break_warehouse", "rejected_warehouse", "is_subcontracted", "supplier_warehouse", "items_section", "items", "section_break0", "total_qty", "total_net_weight", "column_break_43", "base_total", "base_net_total", "column_break_27", "total", "net_total", "taxes_charges_section", "tax_category", "taxes_and_charges", "shipping_col", "shipping_rule", "column_break_53", "incoterm", "named_place", "taxes_section", "taxes", "totals", "base_taxes_and_charges_added", "base_taxes_and_charges_deducted", "base_total_taxes_and_charges", "column_break3", "taxes_and_charges_added", "taxes_and_charges_deducted", "total_taxes_and_charges", "section_break_46", "base_grand_total", "base_rounding_adjustment", "base_rounded_total", "base_in_words", "column_break_50", "grand_total", "rounding_adjustment", "rounded_total", "in_words", "disable_rounded_total", "section_break_42", "apply_discount_on", "base_discount_amount", "column_break_44", "additional_discount_percentage", "discount_amount", "sec_tax_breakup", "other_charges_calculation", "pricing_rule_details", "pricing_rules", "raw_material_details", "get_current_stock", "supplied_items", "address_and_contact_tab", "section_addresses", "supplier_address", "address_display", "col_break_address", "contact_person", "contact_display", "contact_mobile", "contact_email", "section_break_98", "shipping_address", "column_break_100", "shipping_address_display", "billing_address_section", "billing_address", "column_break_104", "billing_address_display", "terms_tab", "tc_name", "terms", "more_info_tab", "status_section", "status", "column_break4", "per_billed", "per_returned", "subscription_detail", "auto_repeat", "printing_settings", "letter_head", "group_same_items", "column_break_97", "select_print_heading", "language", "transporter_info", "transporter_name", "column_break5", "lr_no", "lr_date", "additional_info_section", "instructions", "is_internal_supplier", "represents_company", "inter_company_reference", "column_break_131", "remarks", "range", "amended_from", "is_old_subcontracting_flow", "other_details", "connections_tab"], "fields": [{"fieldname": "supplier_section", "fieldtype": "Section Break", "options": "fa fa-user"}, {"fieldname": "column_break0", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "print_width": "50%", "width": "50%"}, {"allow_on_submit": 1, "default": "{supplier_name}", "fieldname": "title", "fieldtype": "Data", "hidden": 1, "label": "Title", "no_copy": 1, "print_hide": 1}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "oldfieldname": "naming_series", "oldfieldtype": "Select", "options": "MAT-PRE-.YYYY.-\nMAT-PR-RET-.YYYY.-", "print_hide": 1, "reqd": 1, "set_only_once": 1}, {"bold": 1, "fieldname": "supplier", "fieldtype": "Link", "in_global_search": 1, "label": "Supplier", "oldfieldname": "supplier", "oldfieldtype": "Link", "options": "Supplier", "print_hide": 1, "print_width": "150px", "reqd": 1, "search_index": 1, "width": "150px"}, {"bold": 1, "depends_on": "supplier", "fetch_from": "supplier.supplier_name", "fieldname": "supplier_name", "fieldtype": "Data", "in_global_search": 1, "label": "Supplier Name", "read_only": 1}, {"fieldname": "supplier_delivery_note", "fieldtype": "Data", "label": "Supplier Delivery Note"}, {"fieldname": "column_break1", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "print_width": "50%", "width": "50%"}, {"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "label": "Date", "no_copy": 1, "oldfieldname": "posting_date", "oldfieldtype": "Date", "print_width": "100px", "reqd": 1, "search_index": 1, "width": "100px"}, {"fieldname": "posting_time", "fieldtype": "Time", "label": "Posting Time", "no_copy": 1, "oldfieldname": "posting_time", "oldfieldtype": "Time", "print_hide": 1, "print_width": "100px", "reqd": 1, "width": "100px"}, {"default": "0", "depends_on": "eval:doc.docstatus==0", "fieldname": "set_posting_time", "fieldtype": "Check", "label": "Edit Posting Date and Time", "print_hide": 1}, {"fieldname": "company", "fieldtype": "Link", "in_standard_filter": 1, "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "print_hide": 1, "print_width": "150px", "remember_last_selected_value": 1, "reqd": 1, "width": "150px"}, {"default": "0", "fieldname": "is_return", "fieldtype": "Check", "label": "Is Return", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"depends_on": "is_return", "fieldname": "return_against", "fieldtype": "Link", "label": "Return Against Purchase Receipt", "no_copy": 1, "options": "Purchase Receipt", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "section_addresses", "fieldtype": "Section Break", "label": "Supplier Address"}, {"fieldname": "supplier_address", "fieldtype": "Link", "label": "Supplier Address", "options": "Address", "print_hide": 1}, {"fieldname": "contact_person", "fieldtype": "Link", "label": "Contact Person", "options": "Contact", "print_hide": 1}, {"fieldname": "address_display", "fieldtype": "Small Text", "label": "Address", "read_only": 1}, {"fieldname": "contact_display", "fieldtype": "Small Text", "in_global_search": 1, "label": "Contact", "read_only": 1}, {"fieldname": "contact_mobile", "fieldtype": "Small Text", "label": "Mobile No", "options": "Phone", "read_only": 1}, {"fieldname": "contact_email", "fieldtype": "Small Text", "label": "Contact Email", "options": "Email", "print_hide": 1, "read_only": 1}, {"fieldname": "col_break_address", "fieldtype": "Column Break"}, {"fieldname": "shipping_address", "fieldtype": "Link", "label": "Shipping Address Template", "options": "Address", "print_hide": 1}, {"fieldname": "shipping_address_display", "fieldtype": "Small Text", "label": "Shipping Address", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "currency_and_price_list", "fieldtype": "Section Break", "label": "Currency and Price List", "options": "fa fa-tag"}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "oldfieldname": "currency", "oldfieldtype": "Select", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "reqd": 1}, {"description": "Rate at which supplier's currency is converted to company's base currency", "fieldname": "conversion_rate", "fieldtype": "Float", "label": "Exchange Rate", "oldfieldname": "conversion_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "precision": "9", "print_hide": 1, "reqd": 1}, {"fieldname": "column_break2", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "print_width": "50%", "width": "50%"}, {"fieldname": "buying_price_list", "fieldtype": "Link", "label": "Price List", "options": "Price List", "print_hide": 1}, {"depends_on": "buying_price_list", "fieldname": "price_list_currency", "fieldtype": "Link", "label": "Price List Currency", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"depends_on": "buying_price_list", "fieldname": "plc_conversion_rate", "fieldtype": "Float", "label": "Price List Exchange Rate", "precision": "9", "print_hide": 1}, {"default": "0", "fieldname": "ignore_pricing_rule", "fieldtype": "Check", "label": "Ignore Pricing Rule", "permlevel": 1, "print_hide": 1}, {"fieldname": "sec_warehouse", "fieldtype": "Section Break", "hide_border": 1, "label": "Items"}, {"fieldname": "set_warehouse", "fieldtype": "Link", "label": "Accepted Warehouse", "options": "Warehouse", "print_hide": 1}, {"fieldname": "rejected_warehouse", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Rejected Warehouse", "no_copy": 1, "oldfieldname": "rejected_warehouse", "oldfieldtype": "Link", "options": "Warehouse", "print_hide": 1}, {"fieldname": "col_break_warehouse", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "is_subcontracted", "fieldtype": "Check", "label": "Is Subcontracted", "oldfieldname": "is_subcontracted", "oldfieldtype": "Select", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:doc.is_subcontracted", "fieldname": "supplier_warehouse", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Supplier Warehouse", "no_copy": 1, "oldfieldname": "supplier_warehouse", "oldfieldtype": "Link", "options": "Warehouse", "print_hide": 1, "print_width": "50px", "width": "50px"}, {"fieldname": "items_section", "fieldtype": "Section Break", "hide_border": 1, "oldfieldtype": "Section Break", "options": "fa fa-shopping-cart"}, {"allow_bulk_edit": 1, "fieldname": "items", "fieldtype": "Table", "label": "Items", "oldfieldname": "purchase_receipt_details", "oldfieldtype": "Table", "options": "Purchase Receipt Item", "reqd": 1}, {"collapsible": 1, "fieldname": "pricing_rule_details", "fieldtype": "Section Break", "label": "Pricing Rules"}, {"fieldname": "pricing_rules", "fieldtype": "Table", "label": "Pricing Rule Detail", "options": "Pricing Rule Detail", "read_only": 1}, {"depends_on": "supplied_items", "fieldname": "get_current_stock", "fieldtype": "<PERSON><PERSON>", "label": "Get Current Stock", "oldfieldtype": "<PERSON><PERSON>", "options": "get_current_stock", "print_hide": 1}, {"collapsible": 1, "collapsible_depends_on": "supplied_items", "fieldname": "raw_material_details", "fieldtype": "Section Break", "label": "Raw Materials Consumed", "oldfieldtype": "Section Break", "options": "fa fa-table", "print_hide": 1, "read_only": 1}, {"fieldname": "supplied_items", "fieldtype": "Table", "label": "Consumed Items", "no_copy": 1, "oldfieldname": "pr_raw_material_details", "oldfieldtype": "Table", "options": "Purchase Receipt Item Supplied", "print_hide": 1}, {"fieldname": "section_break0", "fieldtype": "Section Break", "oldfieldtype": "Section Break"}, {"fieldname": "total_qty", "fieldtype": "Float", "label": "Total Quantity", "read_only": 1}, {"fieldname": "base_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total (Company Currency)", "oldfieldname": "net_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "150px", "read_only": 1, "reqd": 1, "width": "150px"}, {"fieldname": "column_break_27", "fieldtype": "Column Break"}, {"fieldname": "total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total", "options": "currency", "read_only": 1}, {"fieldname": "net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total", "oldfieldname": "net_total_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "read_only": 1}, {"depends_on": "total_net_weight", "fieldname": "total_net_weight", "fieldtype": "Float", "label": "Total Net Weight", "print_hide": 1, "read_only": 1}, {"fieldname": "taxes_charges_section", "fieldtype": "Section Break", "hide_border": 1, "label": "Taxes and Charges", "oldfieldtype": "Section Break"}, {"fieldname": "tax_category", "fieldtype": "Link", "label": "Tax Category", "options": "Tax Category", "print_hide": 1}, {"fieldname": "shipping_col", "fieldtype": "Column Break"}, {"fieldname": "shipping_rule", "fieldtype": "Link", "label": "Shipping Rule", "options": "Shipping Rule"}, {"fieldname": "taxes_section", "fieldtype": "Section Break", "hide_border": 1}, {"fieldname": "taxes_and_charges", "fieldtype": "Link", "label": "Purchase Taxes and Charges Template", "oldfieldname": "purchase_other_charges", "oldfieldtype": "Link", "options": "Purchase Taxes and Charges Template", "print_hide": 1}, {"fieldname": "taxes", "fieldtype": "Table", "label": "Purchase Taxes and Charges", "oldfieldname": "purchase_tax_details", "oldfieldtype": "Table", "options": "Purchase Taxes and Charges"}, {"collapsible": 1, "fieldname": "sec_tax_breakup", "fieldtype": "Section Break", "label": "Tax Breakup"}, {"fieldname": "other_charges_calculation", "fieldtype": "Long Text", "label": "Taxes and Charges Calculation", "no_copy": 1, "oldfieldtype": "HTML", "print_hide": 1, "read_only": 1}, {"fieldname": "totals", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "fa fa-money"}, {"fieldname": "base_taxes_and_charges_added", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Added (Company Currency)", "oldfieldname": "other_charges_added", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_taxes_and_charges_deducted", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Deducted (Company Currency)", "oldfieldname": "other_charges_deducted", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Taxes and Charges (Company Currency)", "oldfieldname": "total_tax", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break3", "fieldtype": "Column Break", "print_width": "50%", "width": "50%"}, {"fieldname": "taxes_and_charges_added", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Added", "oldfieldname": "other_charges_added_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "taxes_and_charges_deducted", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Deducted", "oldfieldname": "other_charges_deducted_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Taxes and Charges", "options": "currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "section_break_42", "fieldtype": "Section Break", "label": "Additional Discount"}, {"default": "Grand Total", "fieldname": "apply_discount_on", "fieldtype": "Select", "label": "Apply Additional Discount On", "options": "\nGrand Total\nNet Total", "print_hide": 1}, {"fieldname": "base_discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Discount Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_44", "fieldtype": "Column Break"}, {"fieldname": "additional_discount_percentage", "fieldtype": "Float", "label": "Additional Discount Percentage", "print_hide": 1}, {"fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Discount Amount", "options": "currency", "print_hide": 1}, {"fieldname": "section_break_46", "fieldtype": "Section Break", "label": "Totals"}, {"fieldname": "base_grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Grand Total (Company Currency)", "oldfieldname": "grand_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "base_rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounding Adjustment (Company Currency)", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_in_words", "fieldtype": "Data", "label": "In Words (Company Currency)", "length": 240, "oldfieldname": "in_words", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1}, {"fieldname": "base_rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounded Total (Company Currency)", "oldfieldname": "rounded_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_50", "fieldtype": "Column Break"}, {"fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Grand Total", "oldfieldname": "grand_total_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounding Adjustment", "no_copy": 1, "options": "currency", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounded Total", "no_copy": 1, "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "in_words", "fieldtype": "Data", "label": "In Words", "length": 240, "oldfieldname": "in_words_import", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1}, {"default": "0", "fieldname": "disable_rounded_total", "fieldtype": "Check", "label": "Disable Rounded Total"}, {"fieldname": "tc_name", "fieldtype": "Link", "label": "Terms", "oldfieldname": "tc_name", "oldfieldtype": "Link", "options": "Terms and Conditions", "print_hide": 1}, {"fieldname": "terms", "fieldtype": "Text Editor", "label": "Terms and Conditions", "oldfieldname": "terms", "oldfieldtype": "Text Editor"}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "in_standard_filter": 1, "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Select", "options": "\nDraft\nTo Bill\nCompleted\nReturn Issued\nCancelled\nClosed", "print_hide": 1, "print_width": "150px", "read_only": 1, "reqd": 1, "search_index": 1, "width": "150px"}, {"fieldname": "amended_from", "fieldtype": "Link", "hidden": 1, "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "oldfieldname": "amended_from", "oldfieldtype": "Data", "options": "Purchase Receipt", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "range", "fieldtype": "Data", "hidden": 1, "label": "Range", "oldfieldname": "range", "oldfieldtype": "Data", "print_hide": 1}, {"fieldname": "column_break4", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "print_hide": 1, "print_width": "50%", "width": "50%"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project"}, {"fieldname": "per_billed", "fieldtype": "Percent", "label": "% Amount Billed", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "subscription_detail", "fieldtype": "Section Break", "label": "Auto Repeat"}, {"fieldname": "auto_repeat", "fieldtype": "Link", "label": "Auto Repeat", "no_copy": 1, "options": "Auto Repeat", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "printing_settings", "fieldtype": "Section Break", "label": "Printing Settings"}, {"allow_on_submit": 1, "fieldname": "letter_head", "fieldtype": "Link", "label": "Letter Head", "options": "Letter Head", "print_hide": 1}, {"allow_on_submit": 1, "fieldname": "select_print_heading", "fieldtype": "Link", "label": "Print Heading", "no_copy": 1, "oldfieldname": "select_print_heading", "oldfieldtype": "Link", "options": "Print Heading", "print_hide": 1, "report_hide": 1}, {"fieldname": "language", "fieldtype": "Data", "label": "Print Language", "read_only": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "group_same_items", "fieldtype": "Check", "label": "Group same items", "print_hide": 1}, {"fieldname": "column_break_97", "fieldtype": "Column Break"}, {"fieldname": "other_details", "fieldtype": "HTML", "hidden": 1, "label": "Other Details", "oldfieldtype": "HTML", "options": "<div class=\"columnHeading\">Other Details</div>", "print_hide": 1, "print_width": "30%", "width": "30%"}, {"fieldname": "instructions", "fieldtype": "Small Text", "label": "Instructions", "oldfieldname": "instructions", "oldfieldtype": "Text"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks", "print_hide": 1}, {"collapsible": 1, "collapsible_depends_on": "transporter_name", "fieldname": "transporter_info", "fieldtype": "Section Break", "label": "Transporter", "options": "fa fa-truck"}, {"fieldname": "transporter_name", "fieldtype": "Data", "label": "Transporter Name", "oldfieldname": "transporter_name", "oldfieldtype": "Data"}, {"fieldname": "column_break5", "fieldtype": "Column Break", "print_width": "50%", "width": "50%"}, {"fieldname": "lr_no", "fieldtype": "Data", "label": "Vehicle Number", "no_copy": 1, "oldfieldname": "lr_no", "oldfieldtype": "Data", "print_width": "100px", "width": "100px"}, {"fieldname": "lr_date", "fieldtype": "Date", "label": "Vehicle Date", "no_copy": 1, "oldfieldname": "lr_date", "oldfieldtype": "Date", "print_width": "100px", "width": "100px"}, {"default": "0", "fetch_from": "supplier.is_internal_supplier", "fieldname": "is_internal_supplier", "fieldtype": "Check", "label": "Is Internal Supplier", "read_only": 1}, {"fieldname": "inter_company_reference", "fieldtype": "Link", "label": "Inter Company Reference", "no_copy": 1, "options": "Delivery Note", "print_hide": 1, "read_only": 1}, {"fieldname": "scan_barcode", "fieldtype": "Data", "label": "Scan Barcode", "options": "Barcode"}, {"fieldname": "billing_address", "fieldtype": "Link", "label": "Billing Address", "options": "Address"}, {"fieldname": "billing_address_display", "fieldtype": "Small Text", "label": "Billing Address", "read_only": 1}, {"default": "0", "fieldname": "apply_putaway_rule", "fieldtype": "Check", "label": "Apply Putaway Rule"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "per_returned", "fieldtype": "Percent", "label": "% Returned", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"depends_on": "eval: doc.is_internal_supplier", "fieldname": "set_from_warehouse", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Set From Warehouse", "options": "Warehouse"}, {"fetch_from": "supplier.represents_company", "fieldname": "represents_company", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Represents Company", "options": "Company", "read_only": 1}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "is_old_subcontracting_flow", "fieldtype": "Check", "hidden": 1, "label": "Is Old Subcontracting Flow", "read_only": 1}, {"fieldname": "address_and_contact_tab", "fieldtype": "Tab Break", "label": "Address & Contact"}, {"fieldname": "terms_tab", "fieldtype": "Tab Break", "label": "Terms"}, {"fieldname": "more_info_tab", "fieldtype": "Tab Break", "label": "More Info"}, {"fieldname": "connections_tab", "fieldtype": "Tab Break", "label": "Connections", "show_dashboard": 1}, {"fieldname": "column_break_12", "fieldtype": "Column Break"}, {"fieldname": "column_break_31", "fieldtype": "Column Break"}, {"fieldname": "column_break_43", "fieldtype": "Column Break"}, {"fieldname": "column_break_53", "fieldtype": "Column Break"}, {"fieldname": "section_break_98", "fieldtype": "Section Break", "label": "Company Shipping Address"}, {"fieldname": "billing_address_section", "fieldtype": "Section Break", "label": "Company Billing Address"}, {"collapsible": 1, "fieldname": "status_section", "fieldtype": "Section Break", "label": "Status", "oldfieldtype": "Section Break", "options": "fa fa-file-text"}, {"fieldname": "additional_info_section", "fieldtype": "Section Break", "label": "Additional Info"}, {"fieldname": "column_break_131", "fieldtype": "Column Break"}, {"fieldname": "column_break_100", "fieldtype": "Column Break"}, {"fieldname": "column_break_104", "fieldtype": "Column Break"}, {"fieldname": "incoterm", "fieldtype": "Link", "label": "Incoterm", "options": "Incoterm"}, {"depends_on": "incoterm", "fieldname": "named_place", "fieldtype": "Data", "label": "Named Place"}, {"depends_on": "eval: (doc.is_subcontracted && !doc.is_old_subcontracting_flow)", "fieldname": "subcontracting_receipt", "fieldtype": "Link", "label": "Subcontracting Receipt", "options": "Subcontracting Receipt", "search_index": 1}], "icon": "fa fa-truck", "idx": 261, "is_submittable": 1, "links": [], "modified": "2023-12-18 17:26:41.279663", "modified_by": "Administrator", "module": "Stock", "name": "Purchase Receipt", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase User", "share": 1, "submit": 1, "write": 1}, {"read": 1, "report": 1, "role": "Accounts User"}, {"permlevel": 1, "read": 1, "role": "Stock Manager", "write": 1}], "search_fields": "status, posting_date, supplier", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "timeline_field": "supplier", "title_field": "title", "track_changes": 1}