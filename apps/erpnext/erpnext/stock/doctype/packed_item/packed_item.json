{"actions": [], "creation": "2013-02-22 01:28:00", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["parent_item", "item_code", "item_name", "column_break_5", "description", "section_break_6", "warehouse", "target_warehouse", "conversion_factor", "column_break_9", "qty", "rate", "uom", "section_break_9", "pick_serial_and_batch", "use_serial_batch_fields", "column_break_11", "serial_and_batch_bundle", "section_break_bgys", "serial_no", "column_break_qlha", "batch_no", "actual_batch_qty", "section_break_13", "actual_qty", "projected_qty", "ordered_qty", "packed_qty", "column_break_16", "incoming_rate", "picked_qty", "page_break", "prevdoc_doctype", "parent_detail_docname"], "fields": [{"fieldname": "parent_item", "fieldtype": "Link", "in_list_view": 1, "label": "<PERSON><PERSON>", "oldfieldname": "parent_item", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "read_only": 1}, {"fieldname": "item_code", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "read_only": 1}, {"fieldname": "item_name", "fieldtype": "Data", "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data", "read_only": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "description", "fieldtype": "Text Editor", "in_list_view": 1, "label": "Description", "oldfieldname": "description", "oldfieldtype": "Text", "print_width": "300px", "width": "300px"}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"fieldname": "warehouse", "fieldtype": "Link", "label": "From Warehouse", "oldfieldname": "warehouse", "oldfieldtype": "Link", "options": "Warehouse"}, {"fieldname": "target_warehouse", "fieldtype": "Link", "label": "To Warehouse (Optional)", "options": "Warehouse", "print_hide": 1}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Qty", "oldfieldname": "qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "section_break_9", "fieldtype": "Section Break"}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "serial_no", "fieldtype": "Text", "label": "Serial No"}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"fieldname": "batch_no", "fieldtype": "Link", "label": "Batch No", "options": "<PERSON><PERSON>"}, {"fieldname": "section_break_13", "fieldtype": "Section Break"}, {"allow_on_submit": 1, "fieldname": "actual_qty", "fieldtype": "Float", "label": "Actual Qty", "no_copy": 1, "oldfieldname": "actual_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"allow_on_submit": 1, "fieldname": "projected_qty", "fieldtype": "Float", "label": "Projected Qty", "no_copy": 1, "oldfieldname": "projected_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"fieldname": "uom", "fieldtype": "Link", "label": "UOM", "oldfieldname": "uom", "oldfieldtype": "Link", "options": "UOM", "read_only": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "page_break", "fieldtype": "Check", "label": "Page Break", "oldfieldname": "page_break", "oldfieldtype": "Check", "read_only": 1}, {"fieldname": "prevdoc_doctype", "fieldtype": "Data", "hidden": 1, "label": "Prevdoc DocType", "oldfieldname": "prevdoc_doctype", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1}, {"fieldname": "parent_detail_docname", "fieldtype": "Data", "hidden": 1, "label": "Parent Detail docname", "no_copy": 1, "oldfieldname": "parent_detail_docname", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1}, {"depends_on": "batch_no", "fieldname": "actual_batch_qty", "fieldtype": "Float", "label": "Actual Batch Quantity", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "incoming_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Incoming Rate", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "conversion_factor", "fieldtype": "Float", "label": "Conversion Factor"}, {"fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "options": "currency", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "fieldname": "ordered_qty", "fieldtype": "Float", "label": "Ordered Qty", "no_copy": 1, "read_only": 1}, {"fieldname": "picked_qty", "fieldtype": "Float", "label": "Picked <PERSON><PERSON>", "no_copy": 1, "read_only": 1}, {"default": "0", "depends_on": "eval: doc.packed_qty", "fieldname": "packed_qty", "fieldtype": "Float", "label": "Packed Qty", "no_copy": 1, "non_negative": 1, "read_only": 1}, {"depends_on": "eval:doc.use_serial_batch_fields === 0 || doc.docstatus === 1", "fieldname": "serial_and_batch_bundle", "fieldtype": "Link", "label": "Serial and Batch Bundle", "no_copy": 1, "options": "Serial and Batch Bundle", "print_hide": 1}, {"depends_on": "eval:doc.use_serial_batch_fields === 0 || doc.docstatus === 1", "fieldname": "pick_serial_and_batch", "fieldtype": "<PERSON><PERSON>", "label": "Pick Serial / Batch No"}, {"default": "0", "fieldname": "use_serial_batch_fields", "fieldtype": "Check", "label": "Use Serial No / Batch Fields"}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "section_break_bgys", "fieldtype": "Section Break"}, {"fieldname": "column_break_qlha", "fieldtype": "Column Break"}], "idx": 1, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-02-04 16:30:44.263964", "modified_by": "Administrator", "module": "Stock", "name": "Packed Item", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}