{"actions": [], "autoname": "naming_series:", "creation": "2013-04-30 13:13:03", "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "report_date", "status", "manual_inspection", "column_break_4", "inspection_type", "reference_type", "reference_name", "section_break_7", "item_code", "item_serial_no", "batch_no", "sample_size", "column_break1", "item_name", "description", "bom_no", "specification_details", "quality_inspection_template", "readings", "section_break_14", "inspected_by", "verified_by", "column_break_17", "remarks", "amended_from"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "options": "MAT-QA-.YYYY.-", "reqd": 1, "set_only_once": 1}, {"default": "Today", "fieldname": "report_date", "fieldtype": "Date", "in_list_view": 1, "label": "Report Date", "oldfieldname": "report_date", "oldfieldtype": "Date", "reqd": 1, "search_index": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "inspection_type", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Inspection Type", "oldfieldname": "inspection_type", "oldfieldtype": "Select", "options": "\nIncoming\nOutgoing\nIn Process", "reqd": 1}, {"fieldname": "reference_type", "fieldtype": "Select", "label": "Reference Type", "options": "\nPurchase Receipt\nPurchase Invoice\nSubcontracting Receipt\nDelivery Note\nSales Invoice\nStock Entry\nJob Card", "reqd": 1}, {"fieldname": "reference_name", "fieldtype": "Dynamic Link", "in_global_search": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Reference Name", "oldfieldname": "purchase_receipt_no", "oldfieldtype": "Link", "options": "reference_type", "reqd": 1}, {"fieldname": "section_break_7", "fieldtype": "Section Break"}, {"fieldname": "item_code", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "reqd": 1, "search_index": 1}, {"fieldname": "item_serial_no", "fieldtype": "Link", "label": "Item Serial No", "oldfieldname": "item_serial_no", "oldfieldtype": "Link", "options": "Serial No"}, {"fieldname": "batch_no", "fieldtype": "Link", "label": "Batch No", "oldfieldname": "batch_no", "oldfieldtype": "Link", "options": "<PERSON><PERSON>"}, {"fieldname": "sample_size", "fieldtype": "Float", "label": "Sample Size", "oldfieldname": "sample_size", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "reqd": 1}, {"fieldname": "column_break1", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "width": "50%"}, {"fetch_from": "item_code.item_name", "fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "label": "Item Name", "read_only": 1}, {"fetch_from": "item_code.description", "fieldname": "description", "fieldtype": "Small Text", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Small Text", "width": "300px"}, {"fieldname": "section_break_14", "fieldtype": "Section Break"}, {"default": "user", "fieldname": "inspected_by", "fieldtype": "Link", "label": "Inspected By", "oldfieldname": "inspected_by", "oldfieldtype": "Data", "options": "User", "reqd": 1}, {"fieldname": "verified_by", "fieldtype": "Data", "label": "Verified By", "oldfieldname": "verified_by", "oldfieldtype": "Data"}, {"fieldname": "bom_no", "fieldtype": "Link", "label": "BOM No", "options": "BOM", "read_only": 1}, {"fieldname": "column_break_17", "fieldtype": "Column Break"}, {"fieldname": "remarks", "fieldtype": "Text", "label": "Remarks", "no_copy": 1, "oldfieldname": "remarks", "oldfieldtype": "Text"}, {"fieldname": "amended_from", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "oldfieldname": "amended_from", "oldfieldtype": "Data", "options": "Quality Inspection", "print_hide": 1, "read_only": 1}, {"fieldname": "specification_details", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "Simple"}, {"fieldname": "quality_inspection_template", "fieldtype": "Link", "label": "Quality Inspection Template", "options": "Quality Inspection Template"}, {"fieldname": "readings", "fieldtype": "Table", "label": "Readings", "oldfieldname": "qa_specification_details", "oldfieldtype": "Table", "options": "Quality Inspection Reading"}, {"default": "Accepted", "fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "\nAccepted\nRejected", "reqd": 1}, {"default": "0", "fieldname": "manual_inspection", "fieldtype": "Check", "label": "Manual Inspection"}], "icon": "fa fa-search", "idx": 1, "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2023-08-23 11:56:50.282878", "modified_by": "Administrator", "module": "Stock", "name": "Quality Inspection", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Quality Manager", "share": 1, "submit": 1, "write": 1}], "search_fields": "item_code, report_date, reference_name", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "ASC", "states": []}