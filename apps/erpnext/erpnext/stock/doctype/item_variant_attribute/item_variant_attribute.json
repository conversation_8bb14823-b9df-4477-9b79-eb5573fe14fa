{"actions": [], "creation": "2015-05-19 05:12:30.344797", "doctype": "DocType", "document_type": "Other", "editable_grid": 1, "engine": "InnoDB", "field_order": ["variant_of", "attribute", "column_break_2", "attribute_value", "numeric_values", "section_break_4", "from_range", "increment", "column_break_8", "to_range"], "fields": [{"fieldname": "variant_of", "fieldtype": "Link", "label": "Variant Of", "options": "<PERSON><PERSON>", "search_index": 1}, {"fieldname": "attribute", "fieldtype": "Link", "in_list_view": 1, "label": "Attribute", "options": "Item Attribute", "reqd": 1, "search_index": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "attribute_value", "fieldtype": "Data", "in_list_view": 1, "label": "Attribute Value"}, {"default": "0", "depends_on": "has_variants", "fieldname": "numeric_values", "fieldtype": "Check", "label": "Numeric Values"}, {"depends_on": "numeric_values", "fieldname": "section_break_4", "fieldtype": "Section Break"}, {"fieldname": "from_range", "fieldtype": "Float", "label": "From Range"}, {"fieldname": "increment", "fieldtype": "Float", "label": "Increment"}, {"fieldname": "column_break_8", "fieldtype": "Column Break"}, {"fieldname": "to_range", "fieldtype": "Float", "label": "To Range"}], "istable": 1, "links": [], "modified": "2023-07-14 17:15:19.112119", "modified_by": "Administrator", "module": "Stock", "name": "<PERSON><PERSON> Attribute", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}