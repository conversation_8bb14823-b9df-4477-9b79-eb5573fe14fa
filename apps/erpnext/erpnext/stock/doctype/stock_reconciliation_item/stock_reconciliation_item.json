{"actions": [], "creation": "2015-02-17 01:06:05.072764", "doctype": "DocType", "document_type": "Other", "editable_grid": 1, "engine": "InnoDB", "field_order": ["barcode", "has_item_scanned", "item_code", "item_name", "item_group", "column_break_6", "warehouse", "qty", "valuation_rate", "amount", "allow_zero_valuation_rate", "serial_no_and_batch_section", "add_serial_batch_bundle", "use_serial_batch_fields", "column_break_11", "serial_and_batch_bundle", "current_serial_and_batch_bundle", "section_break_lypk", "serial_no", "column_break_eefq", "batch_no", "section_break_3", "current_qty", "current_amount", "column_break_9", "current_valuation_rate", "current_serial_no", "section_break_14", "quantity_difference", "column_break_16", "amount_difference"], "fields": [{"fieldname": "barcode", "fieldtype": "Data", "label": "Barcode", "print_hide": 1}, {"columns": 3, "fieldname": "item_code", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>", "reqd": 1}, {"fetch_from": "item_code.item_name", "fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "label": "Item Name", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"columns": 3, "fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Warehouse", "options": "Warehouse", "reqd": 1}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"columns": 2, "fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity"}, {"columns": 2, "fieldname": "valuation_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Valuation Rate", "options": "Company:company:default_currency"}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "serial_no_and_batch_section", "fieldtype": "Section Break", "label": "Serial No and Batch"}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "serial_no", "fieldtype": "Long Text", "label": "Serial No"}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"fieldname": "section_break_3", "fieldtype": "Section Break", "label": "Before reconciliation"}, {"default": "0", "fieldname": "current_qty", "fieldtype": "Float", "label": "Current Qty", "read_only": 1}, {"fieldname": "current_serial_no", "fieldtype": "Long Text", "label": "Current Serial No", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "current_valuation_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Current Valuation Rate", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "current_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Current Amount", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "section_break_14", "fieldtype": "Section Break"}, {"fieldname": "quantity_difference", "fieldtype": "Read Only", "label": "Quantity Difference"}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"fieldname": "amount_difference", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount Difference", "options": "Company:company:default_currency", "read_only": 1}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "batch_no", "fieldtype": "Link", "label": "Batch No", "options": "<PERSON><PERSON>", "search_index": 1}, {"default": "0", "depends_on": "allow_zero_valuation_rate", "fieldname": "allow_zero_valuation_rate", "fieldtype": "Check", "label": "Allow Zero Valuation Rate", "print_hide": 1, "read_only": 1}, {"depends_on": "barcode", "fieldname": "has_item_scanned", "fieldtype": "Data", "label": "<PERSON> <PERSON><PERSON>", "read_only": 1}, {"depends_on": "eval:doc.use_serial_batch_fields === 0 || doc.docstatus === 1", "fieldname": "serial_and_batch_bundle", "fieldtype": "Link", "label": "Serial / Batch B<PERSON>le", "no_copy": 1, "options": "Serial and Batch Bundle", "print_hide": 1, "search_index": 1}, {"depends_on": "eval:doc.use_serial_batch_fields === 0 || doc.docstatus === 1", "fieldname": "current_serial_and_batch_bundle", "fieldtype": "Link", "label": "Current Serial / Batch Bundle", "no_copy": 1, "options": "Serial and Batch Bundle", "read_only": 1}, {"depends_on": "eval:doc.use_serial_batch_fields === 0 || doc.docstatus === 1", "fieldname": "add_serial_batch_bundle", "fieldtype": "<PERSON><PERSON>", "label": "Add Serial / Batch No"}, {"fetch_from": "item_code.item_group", "fieldname": "item_group", "fieldtype": "Link", "label": "Item Group", "options": "Item Group"}, {"default": "0", "fieldname": "use_serial_batch_fields", "fieldtype": "Check", "label": "Use Serial No / Batch Fields"}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "section_break_lypk", "fieldtype": "Section Break"}, {"fieldname": "column_break_eefq", "fieldtype": "Column Break"}], "istable": 1, "links": [], "modified": "2024-02-04 16:19:44.576022", "modified_by": "Administrator", "module": "Stock", "name": "Stock Reconciliation Item", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}