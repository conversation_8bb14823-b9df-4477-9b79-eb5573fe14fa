{"actions": [], "creation": "2019-07-11 16:01:22.832885", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "item_name", "column_break_2", "description", "item_group", "section_break_5", "warehouse", "quantity_section", "qty", "stock_qty", "picked_qty", "stock_reserved_qty", "column_break_11", "uom", "conversion_factor", "stock_uom", "serial_no_and_batch_section", "pick_serial_and_batch", "serial_and_batch_bundle", "use_serial_batch_fields", "column_break_20", "section_break_ecxc", "serial_no", "column_break_belw", "batch_no", "column_break_15", "sales_order", "sales_order_item", "product_bundle_item", "material_request", "material_request_item"], "fields": [{"default": "1", "fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Qty", "reqd": 1}, {"fieldname": "picked_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Picked <PERSON><PERSON> (in Stock UOM)"}, {"fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Warehouse", "options": "Warehouse", "read_only": 1}, {"fetch_from": "item_code.item_name", "fieldname": "item_name", "fieldtype": "Data", "label": "Item Name", "read_only": 1}, {"fetch_from": "item_code.description", "fieldname": "description", "fieldtype": "Text", "label": "Description", "read_only": 1}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "serial_no", "fieldtype": "Small Text", "label": "Serial No"}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "batch_no", "fieldtype": "Link", "label": "Batch No", "options": "<PERSON><PERSON>", "search_index": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "section_break_5", "fieldtype": "Section Break"}, {"fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "options": "UOM", "read_only": 1}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"fieldname": "uom", "fieldtype": "Link", "label": "UOM", "options": "UOM"}, {"fieldname": "conversion_factor", "fieldtype": "Float", "label": "UOM Conversion Factor", "read_only": 1}, {"fieldname": "stock_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Stock Qty", "read_only": 1}, {"fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>", "reqd": 1}, {"fieldname": "quantity_section", "fieldtype": "Section Break", "label": "Quantity"}, {"fieldname": "column_break_15", "fieldtype": "Section Break", "label": "Reference"}, {"fieldname": "sales_order", "fieldtype": "Link", "label": "Sales Order", "options": "Sales Order", "read_only": 1}, {"fieldname": "sales_order_item", "fieldtype": "Data", "hidden": 1, "label": "Sales Order Item", "read_only": 1}, {"fieldname": "serial_no_and_batch_section", "fieldtype": "Section Break", "label": "Serial No and Batch"}, {"fieldname": "column_break_20", "fieldtype": "Column Break"}, {"fieldname": "material_request", "fieldtype": "Link", "label": "Material Request", "options": "Material Request", "read_only": 1}, {"fieldname": "material_request_item", "fieldtype": "Data", "label": "Material Request Item", "read_only": 1}, {"fetch_from": "item_code.item_group", "fieldname": "item_group", "fieldtype": "Data", "label": "Item Group", "read_only": 1}, {"description": "product bundle item row's name in sales order. Also indicates that picked item is to be used for a product bundle", "fieldname": "product_bundle_item", "fieldtype": "Data", "hidden": 1, "label": "Product Bundle Item", "read_only": 1}, {"depends_on": "eval:doc.use_serial_batch_fields === 0 || doc.docstatus === 1", "fieldname": "serial_and_batch_bundle", "fieldtype": "Link", "label": "Serial and Batch Bundle", "no_copy": 1, "options": "Serial and Batch Bundle", "print_hide": 1, "search_index": 1}, {"depends_on": "eval:doc.use_serial_batch_fields === 0 || doc.docstatus === 1", "fieldname": "pick_serial_and_batch", "fieldtype": "<PERSON><PERSON>", "label": "Pick Serial / Batch No"}, {"default": "0", "fieldname": "stock_reserved_qty", "fieldtype": "Float", "label": "Stock Reserved Qty (in Stock UOM)", "no_copy": 1, "non_negative": 1, "print_hide": 1, "read_only": 1, "report_hide": 1}, {"default": "0", "fieldname": "use_serial_batch_fields", "fieldtype": "Check", "label": "Use Serial No / Batch Fields"}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "section_break_ecxc", "fieldtype": "Section Break"}, {"fieldname": "column_break_belw", "fieldtype": "Column Break"}], "istable": 1, "links": [], "modified": "2024-02-04 16:12:16.257951", "modified_by": "Administrator", "module": "Stock", "name": "Pick List Item", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}