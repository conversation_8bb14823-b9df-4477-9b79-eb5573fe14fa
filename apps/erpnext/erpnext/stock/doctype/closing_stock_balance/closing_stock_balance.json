{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2023-05-17 09:58:42.086911", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "company", "status", "column_break_p0s0", "from_date", "to_date", "filters_section", "item_code", "item_group", "include_uom", "column_break_rm5w", "warehouse", "warehouse_type", "amended_from"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Naming Series", "options": "CBAL-.#####"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company"}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_preview": 1, "label": "Status", "options": "Draft\nQueued\nIn Progress\nCompleted\nFailed\nCanceled", "read_only": 1}, {"fieldname": "column_break_p0s0", "fieldtype": "Column Break"}, {"fieldname": "from_date", "fieldtype": "Date", "label": "From Date"}, {"fieldname": "to_date", "fieldtype": "Date", "label": "To Date"}, {"collapsible": 1, "fieldname": "filters_section", "fieldtype": "Section Break", "label": "Filters"}, {"fieldname": "item_code", "fieldtype": "Link", "label": "Item Code", "options": "<PERSON><PERSON>"}, {"fieldname": "item_group", "fieldtype": "Link", "label": "Item Group", "options": "Item Group"}, {"fieldname": "column_break_rm5w", "fieldtype": "Column Break"}, {"fieldname": "warehouse", "fieldtype": "Link", "label": "Warehouse", "options": "Warehouse"}, {"fieldname": "warehouse_type", "fieldtype": "Link", "label": "Warehouse Type", "options": "Warehouse Type"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Closing Stock Balance", "print_hide": 1, "read_only": 1}, {"fieldname": "include_uom", "fieldtype": "Link", "label": "Include UOM", "options": "UOM"}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2023-05-18 11:46:04.448220", "modified_by": "Administrator", "module": "Stock", "name": "Closing Stock Balance", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}