{"actions": [], "creation": "2013-06-24 16:37:54", "description": "Default settings for your stock-related transactions", "doctype": "DocType", "engine": "InnoDB", "field_order": ["defaults_tab", "item_defaults_section", "item_naming_by", "valuation_method", "item_group", "column_break_4", "default_warehouse", "sample_retention_warehouse", "stock_uom", "price_list_defaults_section", "auto_insert_price_list_rate_if_missing", "column_break_12", "update_existing_price_list_rate", "conversion_factor_section", "allow_to_edit_stock_uom_qty_for_sales", "column_break_lznj", "allow_to_edit_stock_uom_qty_for_purchase", "stock_validations_tab", "section_break_9", "over_delivery_receipt_allowance", "mr_qty_allowance", "column_break_121", "role_allowed_to_over_deliver_receive", "allow_negative_stock", "show_barcode_field", "clean_description_html", "quality_inspection_settings_section", "action_if_quality_inspection_is_not_submitted", "column_break_23", "action_if_quality_inspection_is_rejected", "stock_reservation_tab", "enable_stock_reservation", "column_break_rx3e", "allow_partial_reservation", "auto_reserve_stock_for_sales_order_on_purchase", "serial_and_batch_reservation_section", "auto_reserve_serial_and_batch", "serial_and_batch_item_settings_tab", "section_break_7", "auto_create_serial_and_batch_bundle_for_outward", "pick_serial_and_batch_based_on", "column_break_mhzc", "disable_serial_no_and_batch_selector", "use_naming_series", "naming_series_prefix", "use_serial_batch_fields", "do_not_update_serial_batch_on_creation_of_auto_bundle", "stock_planning_tab", "auto_material_request", "auto_indent", "column_break_27", "reorder_email_notify", "inter_warehouse_transfer_settings_section", "allow_from_dn", "column_break_31", "allow_from_pr", "stock_closing_tab", "control_historical_stock_transactions_section", "stock_frozen_upto", "stock_frozen_upto_days", "column_break_26", "role_allowed_to_create_edit_back_dated_transactions", "stock_auth_role"], "fields": [{"default": "Item Code", "fieldname": "item_naming_by", "fieldtype": "Select", "in_list_view": 1, "label": "<PERSON><PERSON>", "options": "Item Code\nNaming Series"}, {"fieldname": "item_group", "fieldtype": "Link", "in_list_view": 1, "label": "Default Item Group", "options": "Item Group"}, {"fieldname": "stock_uom", "fieldtype": "Link", "in_list_view": 1, "label": "Default Stock UOM", "options": "UOM"}, {"fieldname": "default_warehouse", "fieldtype": "Link", "label": "Default Warehouse", "options": "Warehouse"}, {"fieldname": "sample_retention_warehouse", "fieldtype": "Link", "label": "Sample Retention Warehouse", "options": "Warehouse"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"documentation_url": "https://docs.erpnext.com/docs/v14/user/manual/en/stock/articles/calculation-of-valuation-rate-in-fifo-and-moving-average", "fieldname": "valuation_method", "fieldtype": "Select", "label": "Default Valuation Method", "options": "FIFO\nMoving Average\nLIFO"}, {"description": "The percentage you are allowed to receive or deliver more against the quantity ordered. For example, if you have ordered 100 units, and your Allowance is 10%, then you are allowed to receive 110 units.", "fieldname": "over_delivery_receipt_allowance", "fieldtype": "Float", "label": "Over Delivery/Receipt Allowance (%)"}, {"default": "Stop", "fieldname": "action_if_quality_inspection_is_not_submitted", "fieldtype": "Select", "label": "Action If Quality Inspection Is Not Submitted", "options": "Stop\nWarn"}, {"default": "1", "fieldname": "show_barcode_field", "fieldtype": "Check", "label": "Show Barcode Field in Stock Transactions"}, {"default": "1", "fieldname": "clean_description_html", "fieldtype": "Check", "label": "Convert Item Description to Clean HTML in Transactions"}, {"fieldname": "section_break_7", "fieldtype": "Section Break", "label": "Serial & Batch Item Settings"}, {"default": "0", "fieldname": "auto_insert_price_list_rate_if_missing", "fieldtype": "Check", "label": "Auto Insert Item Price If Missing"}, {"default": "0", "fieldname": "allow_negative_stock", "fieldtype": "Check", "label": "Allow Negative Stock"}, {"fieldname": "auto_material_request", "fieldtype": "Section Break", "label": "Auto Material Request"}, {"default": "0", "fieldname": "auto_indent", "fieldtype": "Check", "label": "Raise Material Request When Stock Reaches Re-order Level"}, {"default": "0", "fieldname": "reorder_email_notify", "fieldtype": "Check", "label": "Notify by Email on Creation of Automatic Material Request"}, {"description": "No stock transactions can be created or modified before this date.", "fieldname": "stock_frozen_upto", "fieldtype": "Date", "label": "Stock Frozen Upto"}, {"description": "Stock transactions that are older than the mentioned days cannot be modified.", "fieldname": "stock_frozen_upto_days", "fieldtype": "Int", "label": "Freeze Stocks Older Than (Days)"}, {"depends_on": "eval:(doc.stock_frozen_upto || doc.stock_frozen_upto_days)", "description": "The users with this Role are allowed to create/modify a stock transaction, even though the transaction is frozen.", "fieldname": "stock_auth_role", "fieldtype": "Link", "label": "Role Allowed to <PERSON>", "options": "Role"}, {"default": "0", "fieldname": "use_naming_series", "fieldtype": "Check", "label": "Have Default Naming Series for Batch ID?"}, {"default": "BATCH-", "depends_on": "eval:doc.use_naming_series==1", "fieldname": "naming_series_prefix", "fieldtype": "Data", "label": "Naming Series Prefix"}, {"fieldname": "inter_warehouse_transfer_settings_section", "fieldtype": "Section Break", "label": "Inter Warehouse Transfer Settings"}, {"default": "0", "fieldname": "allow_from_dn", "fieldtype": "Check", "label": "Allow Material Transfer from Delivery Note to Sales Invoice"}, {"default": "0", "fieldname": "allow_from_pr", "fieldtype": "Check", "label": "Allow Material Transfer from Purchase Receipt to Purchase Invoice"}, {"description": "If mentioned, the system will allow only the users with this Role to create or modify any stock transaction earlier than the latest stock transaction for a specific item and warehouse. If set as blank, it allows all users to create/edit back-dated transactions.", "fieldname": "role_allowed_to_create_edit_back_dated_transactions", "fieldtype": "Link", "label": "Role Allowed to Create/Edit Back-dated Transactions", "options": "Role"}, {"fieldname": "column_break_26", "fieldtype": "Column Break"}, {"fieldname": "control_historical_stock_transactions_section", "fieldtype": "Section Break", "label": "Control Historical Stock Transactions"}, {"default": "0", "fieldname": "disable_serial_no_and_batch_selector", "fieldtype": "Check", "label": "Disable Serial No And Batch Selector"}, {"description": "Users with this role are allowed to over deliver/receive against orders above the allowance percentage", "fieldname": "role_allowed_to_over_deliver_receive", "fieldtype": "Link", "label": "Role Allowed to Over Deliver/Receive", "options": "Role"}, {"fieldname": "item_defaults_section", "fieldtype": "Section Break", "label": "<PERSON><PERSON>"}, {"fieldname": "section_break_9", "fieldtype": "Section Break", "label": "Stock Transactions Settings"}, {"fieldname": "column_break_12", "fieldtype": "Column Break"}, {"fieldname": "column_break_27", "fieldtype": "Column Break"}, {"fieldname": "column_break_31", "fieldtype": "Column Break"}, {"fieldname": "quality_inspection_settings_section", "fieldtype": "Section Break", "label": "Quality Inspection Settings"}, {"default": "Stop", "fieldname": "action_if_quality_inspection_is_rejected", "fieldtype": "Select", "label": "Action If Quality Inspection Is Rejected", "options": "Stop\nWarn"}, {"description": "The percentage you are allowed to transfer more against the quantity ordered. For example, if you have ordered 100 units, and your Allowance is 10%, then you are allowed transfer 110 units.", "fieldname": "mr_qty_allowance", "fieldtype": "Float", "label": "Over Transfer Allowance"}, {"default": "0", "depends_on": "auto_insert_price_list_rate_if_missing", "fieldname": "update_existing_price_list_rate", "fieldtype": "Check", "label": "Update Existing Price List Rate"}, {"fieldname": "defaults_tab", "fieldtype": "Tab Break", "label": "De<PERSON>ults"}, {"fieldname": "stock_validations_tab", "fieldtype": "Tab Break", "label": "Stock Validations"}, {"fieldname": "stock_planning_tab", "fieldtype": "Tab Break", "label": "Stock Planning"}, {"fieldname": "stock_closing_tab", "fieldtype": "Tab Break", "label": "Stock Closing"}, {"fieldname": "serial_and_batch_item_settings_tab", "fieldtype": "Tab Break", "label": "Serial & Batch Item"}, {"fieldname": "column_break_23", "fieldtype": "Column Break"}, {"fieldname": "price_list_defaults_section", "fieldtype": "Section Break", "label": "Price List Defaults"}, {"fieldname": "column_break_121", "fieldtype": "Column Break"}, {"fieldname": "stock_reservation_tab", "fieldtype": "Tab Break", "label": "Stock Reservation"}, {"default": "0", "description": "Allows to keep aside a specific quantity of inventory for a particular order.", "fieldname": "enable_stock_reservation", "fieldtype": "Check", "label": "Enable Stock Reservation"}, {"fieldname": "column_break_rx3e", "fieldtype": "Column Break"}, {"default": "1", "depends_on": "eval: doc.enable_stock_reservation", "description": "Partial stock can be reserved. For example, If you have a Sales Order of 100 units and the Available Stock is 90 units then a Stock Reservation Entry will be created for 90 units. ", "fieldname": "allow_partial_reservation", "fieldtype": "Check", "label": "Allow Partial Reservation"}, {"fieldname": "column_break_mhzc", "fieldtype": "Column Break"}, {"default": "FIFO", "depends_on": "auto_create_serial_and_batch_bundle_for_outward", "fieldname": "pick_serial_and_batch_based_on", "fieldtype": "Select", "label": "Pick Serial / Batch Based On", "mandatory_depends_on": "auto_create_serial_and_batch_bundle_for_outward", "options": "FIFO\nLIFO\nExpiry"}, {"default": "1", "fieldname": "auto_create_serial_and_batch_bundle_for_outward", "fieldtype": "Check", "label": "Auto Create Serial and Batch Bundle For Outward"}, {"default": "1", "depends_on": "eval: doc.enable_stock_reservation", "description": "Serial and Batch Nos will be auto-reserved based on <b>Pick Serial / Batch Based On</b>", "fieldname": "auto_reserve_serial_and_batch", "fieldtype": "Check", "label": "Auto Reserve Serial and Batch Nos"}, {"fieldname": "serial_and_batch_reservation_section", "fieldtype": "Section Break", "label": "Serial and Batch Reservation"}, {"fieldname": "conversion_factor_section", "fieldtype": "Section Break", "label": "Stock UOM Quantity"}, {"fieldname": "column_break_lznj", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "allow_to_edit_stock_uom_qty_for_sales", "fieldtype": "Check", "label": "Allow to Edit Stock UOM Qty for Sales Documents"}, {"default": "0", "fieldname": "allow_to_edit_stock_uom_qty_for_purchase", "fieldtype": "Check", "label": "Allow to Edit Stock UOM Qty for Purchase Documents"}, {"default": "0", "depends_on": "eval: doc.enable_stock_reservation", "description": "Stock will be reserved on submission of <b>Purchase Receipt</b> created against Material Receipt for Sales Order.", "fieldname": "auto_reserve_stock_for_sales_order_on_purchase", "fieldtype": "Check", "label": "Auto Reserve Stock for Sales Order on Purchase"}, {"default": "1", "description": "On submission of the stock transaction, system will auto create the Serial and Batch Bundle based on the Serial No / Batch fields.", "fieldname": "use_serial_batch_fields", "fieldtype": "Check", "label": "Use Serial / Batch Fields"}, {"default": "1", "depends_on": "use_serial_batch_fields", "description": "If enabled, do not update serial / batch values in the stock transactions on creation of auto Serial \n / Batch Bundle. ", "fieldname": "do_not_update_serial_batch_on_creation_of_auto_bundle", "fieldtype": "Check", "label": "Do Not Update Serial / Batch on Creation of Auto Bundle"}], "icon": "icon-cog", "idx": 1, "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2024-02-25 16:32:01.084453", "modified_by": "Administrator", "module": "Stock", "name": "Stock Settings", "owner": "Administrator", "permissions": [{"create": 1, "email": 1, "print": 1, "read": 1, "role": "Stock Manager", "share": 1, "write": 1}, {"read": 1, "role": "Sales User"}], "quick_entry": 1, "sort_field": "modified", "sort_order": "ASC", "states": [], "track_changes": 1}