{"actions": [], "autoname": "hash", "creation": "2013-05-24 19:29:10", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["barcode", "has_item_scanned", "section_break_2", "item_code", "product_bundle", "supplier_part_no", "column_break_2", "item_name", "section_break_4", "description", "brand", "image_column", "item_group", "image", "image_view", "received_and_accepted", "received_qty", "qty", "rejected_qty", "col_break2", "uom", "stock_uom", "conversion_factor", "retain_sample", "sample_quantity", "tracking_section", "received_stock_qty", "col_break_tracking_section", "stock_qty", "returned_qty", "rate_and_amount", "price_list_rate", "col_break3", "base_price_list_rate", "discount_and_margin_section", "margin_type", "margin_rate_or_amount", "rate_with_margin", "column_break_37", "discount_percentage", "discount_amount", "base_rate_with_margin", "sec_break1", "rate", "amount", "col_break4", "base_rate", "base_amount", "pricing_rules", "stock_uom_rate", "is_free_item", "section_break_29", "net_rate", "net_amount", "item_tax_template", "column_break_32", "base_net_rate", "base_net_amount", "valuation_rate", "item_tax_amount", "rm_supp_cost", "landed_cost_voucher_amount", "rate_difference_with_purchase_invoice", "billed_amt", "warehouse_and_reference", "warehouse", "rejected_warehouse", "from_warehouse", "material_request", "purchase_order", "purchase_invoice", "column_break_40", "allow_zero_valuation_rate", "is_fixed_asset", "asset_location", "asset_category", "schedule_date", "quality_inspection", "material_request_item", "purchase_order_item", "purchase_invoice_item", "purchase_receipt_item", "delivery_note_item", "putaway_rule", "section_break_45", "add_serial_batch_bundle", "serial_and_batch_bundle", "use_serial_batch_fields", "col_break5", "add_serial_batch_for_rejected_qty", "rejected_serial_and_batch_bundle", "section_break_3vxt", "serial_no", "rejected_serial_no", "column_break_tolu", "batch_no", "subcontract_bom_section", "include_exploded_items", "bom", "item_weight_details", "weight_per_unit", "total_weight", "column_break_41", "weight_uom", "manufacture_details", "manufacturer", "column_break_16", "manufacturer_part_no", "accounting_details_section", "expense_account", "item_tax_rate", "wip_composite_asset", "column_break_102", "provisional_expense_account", "accounting_dimensions_section", "project", "dimension_col_break", "cost_center", "section_break_80", "page_break", "sales_order", "sales_order_item", "subcontracting_receipt_item"], "fields": [{"fieldname": "barcode", "fieldtype": "Data", "label": "Barcode"}, {"fieldname": "section_break_2", "fieldtype": "Section Break"}, {"bold": 1, "columns": 3, "fieldname": "item_code", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "print_width": "100px", "reqd": 1, "search_index": 1, "width": "100px"}, {"fieldname": "supplier_part_no", "fieldtype": "Data", "hidden": 1, "label": "Supplier Part Number", "read_only": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data", "print_hide": 1, "reqd": 1}, {"collapsible": 1, "fieldname": "section_break_4", "fieldtype": "Section Break", "label": "Description"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Text", "print_width": "300px", "width": "300px"}, {"fetch_from": "item_code.image", "fieldname": "image", "fieldtype": "Attach", "hidden": 1, "label": "Image"}, {"fieldname": "image_view", "fieldtype": "Image", "label": "Image View", "options": "image", "print_hide": 1}, {"fieldname": "received_and_accepted", "fieldtype": "Section Break", "label": "Received and Accepted"}, {"bold": 1, "default": "0", "fieldname": "received_qty", "fieldtype": "Float", "label": "Received Quantity", "no_copy": 1, "oldfieldname": "received_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "print_width": "100px", "read_only": 1, "reqd": 1, "width": "100px"}, {"columns": 2, "fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Accepted Quantity", "oldfieldname": "qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_width": "100px", "width": "100px"}, {"columns": 1, "fieldname": "rejected_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Rejected Quantity", "oldfieldname": "rejected_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "print_width": "100px", "width": "100px"}, {"fieldname": "col_break2", "fieldtype": "Column Break", "print_hide": 1}, {"fieldname": "uom", "fieldtype": "Link", "label": "UOM", "oldfieldname": "uom", "oldfieldtype": "Link", "options": "UOM", "print_width": "100px", "reqd": 1, "width": "100px"}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "oldfieldname": "stock_uom", "oldfieldtype": "Data", "options": "UOM", "print_hide": 1, "print_width": "100px", "read_only": 1, "reqd": 1, "width": "100px"}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "conversion_factor", "fieldtype": "Float", "label": "Conversion Factor", "oldfieldname": "conversion_factor", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "print_width": "100px", "reqd": 1, "width": "100px"}, {"default": "0", "fetch_from": "item_code.retain_sample", "fieldname": "retain_sample", "fieldtype": "Check", "label": "<PERSON><PERSON>", "read_only": 1}, {"depends_on": "retain_sample", "fetch_from": "item_code.sample_quantity", "fieldname": "sample_quantity", "fieldtype": "Int", "label": "Sample Quantity"}, {"fieldname": "rate_and_amount", "fieldtype": "Section Break", "label": "Rate and Amount"}, {"fieldname": "price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price List Rate", "options": "currency", "print_hide": 1}, {"depends_on": "price_list_rate", "fieldname": "discount_percentage", "fieldtype": "Percent", "label": "Discount on Price List Rate (%)", "print_hide": 1}, {"depends_on": "price_list_rate", "fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Discount Amount", "options": "currency"}, {"fieldname": "col_break3", "fieldtype": "Column Break"}, {"fieldname": "base_price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price List Rate (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "sec_break1", "fieldtype": "Section Break"}, {"bold": 1, "columns": 2, "fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "oldfieldname": "import_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_width": "100px", "width": "100px"}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "oldfieldname": "import_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1}, {"fieldname": "col_break4", "fieldtype": "Column Break"}, {"fieldname": "base_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate (Company Currency)", "oldfieldname": "purchase_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "100px", "read_only": 1, "reqd": 1, "width": "100px"}, {"fieldname": "base_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount (Company Currency)", "oldfieldname": "amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "pricing_rules", "fieldtype": "Small Text", "hidden": 1, "label": "Pricing Rules", "print_hide": 1, "read_only": 1}, {"default": "0", "fieldname": "is_free_item", "fieldtype": "Check", "label": "Is Free Item", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_29", "fieldtype": "Section Break"}, {"fieldname": "net_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Rate", "options": "currency", "print_hide": 1, "read_only": 1}, {"columns": 2, "fieldname": "net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Net Amount", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_32", "fieldtype": "Column Break"}, {"fieldname": "base_net_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Rate (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "item_weight_details", "fieldtype": "Section Break", "label": "Item Weight Details"}, {"fieldname": "weight_per_unit", "fieldtype": "Float", "label": "Weight Per Unit"}, {"fieldname": "total_weight", "fieldtype": "Float", "label": "Total Weight", "read_only": 1}, {"fieldname": "column_break_41", "fieldtype": "Column Break"}, {"fieldname": "weight_uom", "fieldtype": "Link", "label": "Weight UOM", "options": "UOM"}, {"fieldname": "warehouse_and_reference", "fieldtype": "Section Break", "label": "Warehouse and Reference"}, {"bold": 1, "fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Accepted Warehouse", "oldfieldname": "warehouse", "oldfieldtype": "Link", "options": "Warehouse", "print_hide": 1, "print_width": "100px", "width": "100px"}, {"fieldname": "rejected_warehouse", "fieldtype": "Link", "label": "Rejected Warehouse", "no_copy": 1, "oldfieldname": "rejected_warehouse", "oldfieldtype": "Link", "options": "Warehouse", "print_hide": 1, "print_width": "100px", "width": "100px"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "quality_inspection", "fieldtype": "Link", "label": "Quality Inspection", "no_copy": 1, "oldfieldname": "qa_no", "oldfieldtype": "Link", "options": "Quality Inspection", "print_hide": 1}, {"fieldname": "column_break_40", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "is_fixed_asset", "fieldtype": "Check", "hidden": 1, "label": "Is Fixed Asset", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "purchase_order", "fieldtype": "Link", "label": "Purchase Order", "no_copy": 1, "oldfieldname": "prevdoc_docname", "oldfieldtype": "Link", "options": "Purchase Order", "print_width": "150px", "read_only": 1, "search_index": 1, "width": "150px"}, {"fieldname": "schedule_date", "fieldtype": "Date", "label": "Required By", "oldfieldname": "schedule_date", "oldfieldtype": "Date", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "stock_qty", "fieldtype": "Float", "label": "Accepted Qty in Stock UOM", "no_copy": 1, "oldfieldname": "stock_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "section_break_45", "fieldtype": "Section Break", "label": "Serial and Batch No"}, {"fieldname": "item_tax_template", "fieldtype": "Link", "label": "Item Tax Template", "options": "Item Tax Template", "print_hide": 1}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project", "print_hide": 1}, {"default": ":Company", "depends_on": "eval:cint(erpnext.is_perpetual_inventory_enabled(parent.company))", "fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center", "print_hide": 1}, {"fieldname": "purchase_order_item", "fieldtype": "Data", "hidden": 1, "label": "Purchase Order Item", "no_copy": 1, "oldfieldname": "prevdoc_detail_docname", "oldfieldtype": "Data", "print_hide": 1, "print_width": "150px", "read_only": 1, "search_index": 1, "width": "150px"}, {"fieldname": "col_break5", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "allow_zero_valuation_rate", "fieldtype": "Check", "label": "Allow Zero Valuation Rate", "no_copy": 1, "print_hide": 1}, {"depends_on": "eval:parent.is_old_subcontracting_flow", "fieldname": "bom", "fieldtype": "Link", "label": "BOM", "no_copy": 1, "options": "BOM", "print_hide": 1, "read_only": 1, "read_only_depends_on": "eval:!parent.is_old_subcontracting_flow"}, {"default": "0", "depends_on": "eval:parent.is_subcontracted", "fieldname": "include_exploded_items", "fieldtype": "Check", "label": "Include Exploded Items", "print_hide": 1, "read_only": 1}, {"fieldname": "billed_amt", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON>", "no_copy": 1, "options": "currency", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "fieldname": "landed_cost_voucher_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Landed Cost Voucher Amount", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fetch_from": "item_code.brand", "fieldname": "brand", "fieldtype": "Link", "hidden": 1, "label": "Brand", "oldfieldname": "brand", "oldfieldtype": "Link", "options": "Brand", "print_hide": 1, "read_only": 1}, {"fetch_from": "item_code.item_group", "fieldname": "item_group", "fieldtype": "Link", "label": "Item Group", "oldfieldname": "item_group", "oldfieldtype": "Link", "options": "Item Group", "print_hide": 1, "read_only": 1}, {"fieldname": "rm_supp_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "Raw Materials Supplied Cost", "no_copy": 1, "oldfieldname": "rm_supp_cost", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "item_tax_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "Item Tax Amount Included in Value", "no_copy": 1, "oldfieldname": "item_tax_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"allow_on_submit": 1, "fieldname": "valuation_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "Valuation Rate", "no_copy": 1, "oldfieldname": "valuation_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "precision": "6", "print_hide": 1, "print_width": "80px", "read_only": 1, "width": "80px"}, {"description": "Tax detail table fetched from item master as a string and stored in this field.\nUsed for Taxes and Charges", "fieldname": "item_tax_rate", "fieldtype": "Code", "hidden": 1, "label": "Item Tax Rate", "oldfieldname": "item_tax_rate", "oldfieldtype": "Small Text", "print_hide": 1, "read_only": 1, "report_hide": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "page_break", "fieldtype": "Check", "label": "Page Break", "oldfieldname": "page_break", "oldfieldtype": "Check", "print_hide": 1}, {"fieldname": "section_break_80", "fieldtype": "Section Break"}, {"fieldname": "material_request", "fieldtype": "Link", "label": "Material Request", "options": "Material Request", "read_only": 1}, {"fieldname": "material_request_item", "fieldtype": "Data", "hidden": 1, "label": "Material Request Item", "read_only": 1}, {"fieldname": "expense_account", "fieldtype": "Link", "label": "Expense Account", "options": "Account"}, {"fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"collapsible": 1, "fieldname": "manufacture_details", "fieldtype": "Section Break", "label": "Manufacture"}, {"fieldname": "manufacturer", "fieldtype": "Link", "label": "Manufacturer", "options": "Manufacturer"}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"fieldname": "manufacturer_part_no", "fieldtype": "Data", "label": "Manufacturer Part Number"}, {"depends_on": "is_fixed_asset", "fieldname": "asset_location", "fieldtype": "Link", "label": "Asset Location", "options": "Location"}, {"depends_on": "is_fixed_asset", "fetch_from": "item_code.asset_category", "fieldname": "asset_category", "fieldtype": "Link", "label": "Asset Category", "options": "Asset Category", "read_only": 1}, {"depends_on": "eval:parent.is_internal_supplier", "fieldname": "from_warehouse", "fieldtype": "Link", "hidden": 1, "ignore_user_permissions": 1, "label": "From Warehouse", "options": "Warehouse"}, {"fieldname": "purchase_receipt_item", "fieldtype": "Data", "hidden": 1, "label": "Purchase Receipt Item", "no_copy": 1, "print_hide": 1, "read_only": 1, "search_index": 1}, {"collapsible": 1, "fieldname": "image_column", "fieldtype": "Column Break"}, {"fieldname": "putaway_rule", "fieldtype": "Link", "label": "Putaway Rule", "no_copy": 1, "options": "Putaway Rule", "print_hide": 1, "read_only": 1}, {"fieldname": "tracking_section", "fieldtype": "Section Break", "label": "Qty as Per Stock UOM"}, {"fieldname": "col_break_tracking_section", "fieldtype": "Column Break"}, {"depends_on": "doc.returned_qty", "fieldname": "returned_qty", "fieldtype": "Float", "label": "Returned <PERSON><PERSON> in Stock UOM", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "received_stock_qty", "fieldtype": "Float", "label": "Received <PERSON><PERSON> in Stock UOM", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"depends_on": "eval: doc.uom != doc.stock_uom", "fieldname": "stock_uom_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate of Stock UOM", "no_copy": 1, "options": "currency", "read_only": 1}, {"fieldname": "delivery_note_item", "fieldtype": "Data", "label": "Delivery Note Item", "no_copy": 1, "print_hide": 1, "read_only": 1, "search_index": 1}, {"collapsible": 1, "collapsible_depends_on": "eval: doc.margin_type || doc.discount_amount", "fieldname": "discount_and_margin_section", "fieldtype": "Section Break", "label": "Discount and Margin"}, {"depends_on": "price_list_rate", "fieldname": "margin_type", "fieldtype": "Select", "label": "Margin Type", "options": "\nPercentage\nAmount", "print_hide": 1}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate", "fieldname": "margin_rate_or_amount", "fieldtype": "Float", "label": "Margin Rate or Amount", "print_hide": 1}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate && doc.margin_rate_or_amount", "fieldname": "rate_with_margin", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate With Margin", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_37", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate && doc.margin_rate_or_amount", "fieldname": "base_rate_with_margin", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate With Margin (Company Currency)", "options": "Company:company:default_currency"}, {"fieldname": "purchase_invoice", "fieldtype": "Link", "label": "Purchase Invoice", "options": "Purchase Invoice", "read_only": 1}, {"fieldname": "purchase_invoice_item", "fieldtype": "Data", "hidden": 1, "label": "Purchase Invoice Item", "no_copy": 1, "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "product_bundle", "fieldtype": "Link", "label": "Product Bundle", "options": "Product Bundle", "read_only": 1}, {"fieldname": "provisional_expense_account", "fieldtype": "Link", "label": "Provisional Expense Account", "options": "Account"}, {"fieldname": "accounting_details_section", "fieldtype": "Section Break", "label": "Accounting Details"}, {"fieldname": "column_break_102", "fieldtype": "Column Break"}, {"default": "0", "depends_on": "barcode", "fieldname": "has_item_scanned", "fieldtype": "Check", "label": "<PERSON> <PERSON><PERSON>", "read_only": 1}, {"fieldname": "rate_difference_with_purchase_invoice", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate Difference with Purchase Invoice", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"depends_on": "eval:doc.use_serial_batch_fields === 0 || doc.docstatus === 1", "fieldname": "serial_and_batch_bundle", "fieldtype": "Link", "label": "Serial and Batch Bundle", "no_copy": 1, "options": "Serial and Batch Bundle", "print_hide": 1, "search_index": 1}, {"depends_on": "eval:parent.is_old_subcontracting_flow", "fieldname": "subcontract_bom_section", "fieldtype": "Section Break", "label": "Subcontract BOM"}, {"fieldname": "serial_no", "fieldtype": "Text", "label": "Serial No"}, {"fieldname": "rejected_serial_no", "fieldtype": "Text", "label": "Rejected Serial No"}, {"fieldname": "batch_no", "fieldtype": "Link", "label": "Batch No", "options": "<PERSON><PERSON>", "search_index": 1}, {"depends_on": "eval:doc.use_serial_batch_fields === 0 || doc.docstatus === 1", "fieldname": "rejected_serial_and_batch_bundle", "fieldtype": "Link", "label": "Rejected Serial and <PERSON><PERSON>", "no_copy": 1, "options": "Serial and Batch Bundle"}, {"depends_on": "eval:doc.use_serial_batch_fields === 0", "fieldname": "add_serial_batch_for_rejected_qty", "fieldtype": "<PERSON><PERSON>", "label": "Add Serial / Batch No (Rejected <PERSON><PERSON>)"}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "section_break_3vxt", "fieldtype": "Section Break"}, {"fieldname": "column_break_tolu", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.use_serial_batch_fields === 0", "fieldname": "add_serial_batch_bundle", "fieldtype": "<PERSON><PERSON>", "label": "Add Serial / Batch No"}, {"fieldname": "wip_composite_asset", "fieldtype": "Link", "label": "WIP Composite Asset", "options": "<PERSON><PERSON>"}, {"fieldname": "sales_order", "fieldtype": "Link", "label": "Sales Order", "no_copy": 1, "options": "Sales Order", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "sales_order_item", "fieldtype": "Data", "hidden": 1, "label": "Sales Order Item", "no_copy": 1, "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "subcontracting_receipt_item", "fieldtype": "Data", "hidden": 1, "label": "Subcontracting Receipt Item", "no_copy": 1, "print_hide": 1, "read_only": 1, "report_hide": 1, "search_index": 1}, {"default": "0", "fieldname": "use_serial_batch_fields", "fieldtype": "Check", "label": "Use Serial No / Batch Fields"}], "idx": 1, "istable": 1, "links": [], "modified": "2024-02-04 11:48:06.653771", "modified_by": "Administrator", "module": "Stock", "name": "Purchase Receipt Item", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}