{"actions": [], "allow_import": 1, "autoname": "field:serial_no", "creation": "2013-05-16 10:59:15", "description": "Distinct unit of an Item", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["details", "column_break0", "serial_no", "item_code", "batch_no", "warehouse", "purchase_rate", "column_break1", "status", "item_name", "description", "item_group", "brand", "asset_details", "asset", "asset_status", "column_break_24", "location", "employee", "warranty_amc_details", "column_break6", "warranty_expiry_date", "amc_expiry_date", "column_break7", "maintenance_status", "warranty_period", "more_info", "company", "column_break_2cmm", "work_order", "purchase_document_no"], "fields": [{"fieldname": "details", "fieldtype": "Section Break", "oldfieldtype": "Section Break"}, {"fieldname": "column_break0", "fieldtype": "Column Break"}, {"fieldname": "serial_no", "fieldtype": "Data", "label": "Serial No", "no_copy": 1, "oldfieldname": "serial_no", "oldfieldtype": "Data", "reqd": 1, "unique": 1}, {"fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "reqd": 1}, {"fieldname": "column_break1", "fieldtype": "Column Break"}, {"fetch_from": "item_code.item_name", "fetch_if_empty": 1, "fieldname": "item_name", "fieldtype": "Data", "label": "Item Name", "read_only": 1}, {"fetch_from": "item_code.description", "fieldname": "description", "fieldtype": "Text", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Text", "read_only": 1, "width": "300px"}, {"fieldname": "item_group", "fieldtype": "Link", "label": "Item Group", "oldfieldname": "item_group", "oldfieldtype": "Link", "options": "Item Group", "read_only": 1}, {"fieldname": "brand", "fieldtype": "Link", "label": "Brand", "oldfieldname": "brand", "oldfieldtype": "Link", "options": "Brand", "read_only": 1}, {"fieldname": "asset_details", "fieldtype": "Section Break", "label": "Asset Details"}, {"fieldname": "asset", "fieldtype": "Link", "label": "<PERSON><PERSON>", "no_copy": 1, "options": "<PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"depends_on": "asset", "fieldname": "asset_status", "fieldtype": "Select", "label": "Asset Status", "options": "\nIssue\nReceipt\nTransfer", "read_only": 1}, {"fieldname": "column_break_24", "fieldtype": "Column Break"}, {"fieldname": "location", "fieldtype": "Link", "label": "Location", "options": "Location", "read_only": 1}, {"fieldname": "employee", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Employee", "options": "Employee", "read_only": 1}, {"fieldname": "warranty_amc_details", "fieldtype": "Section Break", "label": "Warranty / AMC Details"}, {"fieldname": "column_break6", "fieldtype": "Column Break", "width": "50%"}, {"fieldname": "maintenance_status", "fieldtype": "Select", "label": "Maintenance Status", "oldfieldname": "maintenance_status", "oldfieldtype": "Select", "options": "\nUnder Warranty\nOut of Warranty\nUnder AMC\nOut of AMC", "read_only": 1, "search_index": 1, "width": "150px"}, {"fetch_from": "item_code.warranty_period", "fieldname": "warranty_period", "fieldtype": "Int", "label": "Warranty Period (Days)", "oldfieldname": "warranty_period", "oldfieldtype": "Int", "read_only": 1, "width": "150px"}, {"fieldname": "column_break7", "fieldtype": "Column Break", "width": "50%"}, {"fieldname": "warranty_expiry_date", "fieldtype": "Date", "label": "Warranty Expiry Date", "oldfieldname": "warranty_expiry_date", "oldfieldtype": "Date", "width": "150px"}, {"fieldname": "amc_expiry_date", "fieldtype": "Date", "label": "AMC Expiry Date", "oldfieldname": "amc_expiry_date", "oldfieldtype": "Date", "width": "150px"}, {"fieldname": "more_info", "fieldtype": "Section Break", "label": "More Information"}, {"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Company", "options": "Company", "remember_last_selected_value": 1, "reqd": 1, "search_index": 1, "set_only_once": 1}, {"fieldname": "work_order", "fieldtype": "Link", "label": "Work Order", "options": "Work Order"}, {"fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Warehouse", "options": "Warehouse", "read_only": 1}, {"fieldname": "batch_no", "fieldtype": "Link", "label": "Batch No", "options": "<PERSON><PERSON>", "read_only": 1}, {"fieldname": "purchase_rate", "fieldtype": "Float", "label": "Incoming Rate", "read_only": 1}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "\nActive\nInactive\nDelivered\nExpired", "read_only": 1}, {"fieldname": "column_break_2cmm", "fieldtype": "Column Break"}, {"fieldname": "purchase_document_no", "fieldtype": "Data", "label": "Creation Document No", "no_copy": 1, "read_only": 1}], "icon": "fa fa-barcode", "idx": 1, "links": [], "modified": "2023-12-17 10:52:55.767839", "modified_by": "Administrator", "module": "Stock", "name": "Serial No", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "<PERSON>em Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "write": 1}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User"}], "search_fields": "item_code", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}