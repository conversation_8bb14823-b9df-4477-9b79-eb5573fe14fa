{"actions": [], "autoname": "naming_series:", "creation": "2019-07-11 16:03:13.681045", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "company", "purpose", "customer", "customer_name", "work_order", "material_request", "for_qty", "column_break_4", "parent_warehouse", "consider_rejected_warehouses", "get_item_locations", "section_break_6", "scan_barcode", "column_break_13", "scan_mode", "prompt_qty", "section_break_15", "locations", "amended_from", "print_settings_section", "group_same_items", "status"], "fields": [{"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"depends_on": "eval:!doc.docstatus", "fieldname": "section_break_6", "fieldtype": "Section Break"}, {"description": "Items under this warehouse will be suggested", "fieldname": "parent_warehouse", "fieldtype": "Link", "label": "Warehouse", "options": "Warehouse"}, {"depends_on": "eval:doc.purpose==='Delivery'", "fieldname": "customer", "fieldtype": "Link", "in_list_view": 1, "label": "Customer", "options": "Customer"}, {"depends_on": "eval:doc.purpose==='Material Transfer for Manufacture'", "fieldname": "work_order", "fieldtype": "Link", "label": "Work Order", "options": "Work Order"}, {"allow_on_submit": 1, "fieldname": "locations", "fieldtype": "Table", "label": "Item Locations", "options": "Pick List Item"}, {"depends_on": "eval:doc.purpose==='Material Transfer for Manufacture'", "description": "Qty of raw materials will be decided based on the qty of the Finished Goods Item", "fieldname": "for_qty", "fieldtype": "Float", "label": "Qty of Finished Goods Item", "read_only": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Pick List", "print_hide": 1, "read_only": 1}, {"default": "Material Transfer for Manufacture", "fieldname": "purpose", "fieldtype": "Select", "label": "Purpose", "options": "Material Transfer for Manufacture\nMaterial Transfer\nDelivery"}, {"depends_on": "eval:['Material Transfer', 'Material Issue'].includes(doc.purpose)", "fieldname": "material_request", "fieldtype": "Link", "label": "Material Request", "options": "Material Request"}, {"depends_on": "eval:doc.docstatus===0", "fieldname": "get_item_locations", "fieldtype": "<PERSON><PERSON>", "label": "Get Item Locations"}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "STO-PICK-.YYYY.-", "reqd": 1, "set_only_once": 1}, {"collapsible": 1, "fieldname": "print_settings_section", "fieldtype": "Section Break", "label": "Print Settings"}, {"allow_on_submit": 1, "default": "0", "fieldname": "group_same_items", "fieldtype": "Check", "label": "Group Same Items", "print_hide": 1}, {"fieldname": "section_break_15", "fieldtype": "Section Break"}, {"fieldname": "scan_barcode", "fieldtype": "Data", "label": "Scan Barcode", "options": "Barcode"}, {"fieldname": "column_break_13", "fieldtype": "Column Break"}, {"default": "0", "description": "If checked, picked qty won't automatically be fulfilled on submit of pick list.", "fieldname": "scan_mode", "fieldtype": "Check", "label": "Scan <PERSON>"}, {"default": "0", "fieldname": "prompt_qty", "fieldtype": "Check", "label": "Prompt Qty"}, {"depends_on": "eval:doc.purpose==='Delivery' && doc.customer", "fetch_from": "customer.customer_name", "fieldname": "customer_name", "fieldtype": "Data", "label": "Customer Name", "read_only": 1}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "hidden": 1, "in_standard_filter": 1, "label": "Status", "no_copy": 1, "options": "Draft\nOpen\nCompleted\nCancelled", "print_hide": 1, "read_only": 1, "report_hide": 1, "reqd": 1, "search_index": 1}, {"default": "0", "description": "Enable it if users want to consider rejected materials to dispatch.", "fieldname": "consider_rejected_warehouses", "fieldtype": "Check", "label": "Consider Rejected Warehouses"}], "is_submittable": 1, "links": [], "modified": "2024-02-02 16:17:44.877426", "modified_by": "Administrator", "module": "Stock", "name": "Pick List", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Manufacturing Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Manufacturing User", "share": 1, "submit": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}