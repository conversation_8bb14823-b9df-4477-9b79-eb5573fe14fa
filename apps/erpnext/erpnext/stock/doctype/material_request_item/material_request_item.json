{"actions": [], "autoname": "hash", "creation": "2013-02-22 01:28:02", "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "item_name", "col_break1", "schedule_date", "section_break_4", "description", "column_break_12", "item_group", "brand", "image", "quantity_and_warehouse", "qty", "stock_uom", "from_warehouse", "warehouse", "col_break2", "uom", "conversion_factor", "stock_qty", "qty_info_sec_break", "min_order_qty", "projected_qty", "qty_info_col_break", "actual_qty", "ordered_qty", "received_qty", "rate_and_amount_section_break", "rate", "price_list_rate", "col_break3", "amount", "accounting_details_section", "expense_account", "column_break_glru", "wip_composite_asset", "manufacture_details", "manufacturer", "manufacturer_part_no", "col_break_mfg", "bom_no", "accounting_dimensions_section", "project", "dimension_col_break", "cost_center", "more_info", "lead_time_date", "sales_order", "sales_order_item", "col_break4", "production_plan", "material_request_plan_item", "job_card_item", "section_break_46", "page_break"], "fields": [{"bold": 1, "columns": 3, "fieldname": "item_code", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "print_width": "100px", "reqd": 1, "search_index": 1, "width": "100px"}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data", "print_hide": 1, "print_width": "100px", "search_index": 1, "width": "100px"}, {"collapsible": 1, "fieldname": "section_break_4", "fieldtype": "Section Break", "label": "Description"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Text", "print_width": "250px", "width": "250px"}, {"fetch_from": "item_code.image", "fieldname": "image", "fieldtype": "Attach Image", "label": "Image", "print_hide": 1, "read_only": 1}, {"fieldname": "quantity_and_warehouse", "fieldtype": "Section Break", "label": "Quantity and Warehouse"}, {"columns": 1, "fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity", "oldfieldname": "qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_width": "80px", "reqd": 1, "width": "80px"}, {"columns": 1, "fieldname": "uom", "fieldtype": "Link", "in_list_view": 1, "label": "UOM", "oldfieldname": "uom", "oldfieldtype": "Link", "options": "UOM", "print_width": "70px", "reqd": 1, "width": "70px"}, {"fieldname": "conversion_factor", "fieldtype": "Float", "label": "UOM Conversion Factor", "print_hide": 1, "reqd": 1}, {"fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "options": "UOM", "print_hide": 1, "read_only": 1, "reqd": 1}, {"columns": 3, "fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Target Warehouse", "oldfieldname": "warehouse", "oldfieldtype": "Link", "options": "Warehouse", "print_width": "100px", "width": "100px"}, {"fieldname": "col_break2", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "bold": 1, "columns": 2, "fieldname": "schedule_date", "fieldtype": "Date", "in_list_view": 1, "label": "Required By", "oldfieldname": "schedule_date", "oldfieldtype": "Date", "print_width": "100px", "reqd": 1, "width": "100px"}, {"fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate", "options": "Company:company:default_currency", "print_hide": 1}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "stock_qty", "fieldtype": "Float", "label": "Stock Qty", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "more_info", "fieldtype": "Section Break", "label": "More Information"}, {"fieldname": "item_group", "fieldtype": "Link", "label": "Item Group", "oldfieldname": "item_group", "oldfieldtype": "Link", "options": "Item Group", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "brand", "fieldtype": "Link", "label": "Brand", "oldfieldname": "brand", "oldfieldtype": "Link", "options": "Brand", "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "lead_time_date", "fieldtype": "Date", "label": "Lead Time Date", "no_copy": 1, "oldfieldname": "lead_time_date", "oldfieldtype": "Date", "print_hide": 1, "read_only": 1}, {"fieldname": "sales_order", "fieldtype": "Link", "label": "Sales Order", "options": "Sales Order", "print_hide": 1, "read_only": 1}, {"fieldname": "sales_order_item", "fieldtype": "Data", "hidden": 1, "label": "Sales Order Item", "no_copy": 1, "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project"}, {"fieldname": "production_plan", "fieldtype": "Link", "label": "Production Plan", "no_copy": 1, "options": "Production Plan", "print_hide": 1, "read_only": 1}, {"fieldname": "material_request_plan_item", "fieldtype": "Data", "label": "Material Request Plan Item", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "col_break3", "fieldtype": "Column Break"}, {"fieldname": "min_order_qty", "fieldtype": "Float", "label": "Min Order Qty", "no_copy": 1, "oldfieldname": "min_order_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "print_width": "70px", "read_only": 1, "width": "70px"}, {"allow_on_submit": 1, "fieldname": "projected_qty", "fieldtype": "Float", "label": "Projected Qty", "no_copy": 1, "oldfieldname": "projected_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "print_width": "70px", "read_only": 1, "width": "70px"}, {"fieldname": "actual_qty", "fieldtype": "Float", "label": "Actual Qty", "no_copy": 1, "print_hide": 1, "read_only": 1, "report_hide": 1}, {"depends_on": "eval:doc.docstatus==1", "fieldname": "ordered_qty", "fieldtype": "Float", "label": "Completed Qty", "no_copy": 1, "oldfieldname": "ordered_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"fieldname": "expense_account", "fieldtype": "Link", "label": "Expense Account", "options": "Account", "print_hide": 1}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center", "print_hide": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "page_break", "fieldtype": "Check", "label": "Page Break", "no_copy": 1, "oldfieldname": "page_break", "oldfieldtype": "Check", "print_hide": 1}, {"depends_on": "eval:doc.docstatus==1", "fieldname": "received_qty", "fieldtype": "Float", "label": "Received Qty", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"collapsible": 1, "depends_on": "eval:in_list([\"Manufacture\", \"Purchase\"], parent.material_request_type)", "fieldname": "manufacture_details", "fieldtype": "Section Break", "label": "Manufacture"}, {"fieldname": "manufacturer", "fieldtype": "Link", "label": "Manufacturer", "options": "Manufacturer"}, {"fieldname": "column_break_12", "fieldtype": "Column Break"}, {"fieldname": "manufacturer_part_no", "fieldtype": "Data", "label": "Manufacturer Part Number", "read_only": 1}, {"fieldname": "rate_and_amount_section_break", "fieldtype": "Section Break"}, {"fieldname": "col_break4", "fieldtype": "Column Break"}, {"depends_on": "eval:parent.material_request_type == \"Material Transfer\"", "fieldname": "from_warehouse", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Source Warehouse", "options": "Warehouse"}, {"allow_on_submit": 1, "fieldname": "bom_no", "fieldtype": "Link", "label": "BOM No", "no_copy": 1, "options": "BOM", "print_hide": 1}, {"fieldname": "section_break_46", "fieldtype": "Section Break"}, {"fieldname": "col_break_mfg", "fieldtype": "Column Break"}, {"fieldname": "qty_info_sec_break", "fieldtype": "Section Break"}, {"fieldname": "qty_info_col_break", "fieldtype": "Column Break"}, {"fieldname": "job_card_item", "fieldtype": "Data", "hidden": 1, "label": "Job Card Item", "no_copy": 1, "print_hide": 1}, {"fieldname": "accounting_details_section", "fieldtype": "Section Break", "label": "Accounting Details"}, {"fieldname": "column_break_glru", "fieldtype": "Column Break"}, {"fieldname": "wip_composite_asset", "fieldtype": "Link", "label": "WIP Composite Asset", "options": "<PERSON><PERSON>"}, {"fieldname": "price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "Price List Rate", "options": "currency", "print_hide": 1, "read_only": 1}], "idx": 1, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-02-26 18:30:03.684872", "modified_by": "Administrator", "module": "Stock", "name": "Material Request Item", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}