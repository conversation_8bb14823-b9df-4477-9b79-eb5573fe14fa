{"actions": [], "allow_import": 1, "autoname": "field:batch_id", "creation": "2013-03-05 14:50:38", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["sb_disabled", "disabled", "column_break_24", "use_batchwise_valuation", "sb_batch", "batch_id", "item", "item_name", "image", "parent_batch", "manufacturing_date", "column_break_3", "batch_qty", "stock_uom", "expiry_date", "source", "supplier", "column_break_9", "reference_doctype", "reference_name", "section_break_7", "description", "manufacturing_section", "qty_to_produce", "column_break_23", "produced_qty"], "fields": [{"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"depends_on": "eval:doc.__islocal", "fieldname": "batch_id", "fieldtype": "Data", "in_list_view": 1, "label": "Batch ID", "no_copy": 1, "oldfieldname": "batch_id", "oldfieldtype": "Data", "reqd": 1, "unique": 1}, {"fieldname": "item", "fieldtype": "Link", "in_standard_filter": 1, "label": "<PERSON><PERSON>", "oldfieldname": "item", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "read_only_depends_on": "eval:!doc.__islocal", "reqd": 1}, {"fieldname": "image", "fieldtype": "Attach Image", "hidden": 1, "label": "image"}, {"depends_on": "eval:doc.parent_batch", "fieldname": "parent_batch", "fieldtype": "Link", "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>", "read_only": 1}, {"default": "Today", "fieldname": "manufacturing_date", "fieldtype": "Date", "label": "Manufacturing Date"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "expiry_date", "fieldtype": "Date", "label": "Expiry Date", "oldfieldname": "expiry_date", "oldfieldtype": "Date"}, {"fieldname": "source", "fieldtype": "Section Break", "label": "Source"}, {"fieldname": "supplier", "fieldtype": "Link", "label": "Supplier", "options": "Supplier", "read_only": 1}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "reference_doctype", "fieldtype": "Link", "label": "Source Document Type", "options": "DocType", "read_only": 1}, {"fieldname": "reference_name", "fieldtype": "Dynamic Link", "label": "Source Document Name", "options": "reference_doctype", "read_only": 1}, {"fieldname": "section_break_7", "fieldtype": "Section Break"}, {"fieldname": "description", "fieldtype": "Small Text", "label": "Batch Description", "oldfieldname": "description", "oldfieldtype": "Small Text", "width": "300px"}, {"fieldname": "sb_disabled", "fieldtype": "Section Break"}, {"fieldname": "sb_batch", "fieldtype": "Section Break", "label": "Batch Details"}, {"fetch_from": "item.item_name", "fieldname": "item_name", "fieldtype": "Data", "in_list_view": 1, "label": "Item Name", "read_only": 1}, {"fieldname": "batch_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Batch Quantity", "read_only": 1}, {"fetch_from": "item.stock_uom", "fieldname": "stock_uom", "fieldtype": "Link", "label": "Batch UOM", "options": "UOM", "read_only": 1}, {"fieldname": "manufacturing_section", "fieldtype": "Section Break", "label": "Manufacturing"}, {"fieldname": "qty_to_produce", "fieldtype": "Float", "label": "Qty To Produce", "read_only": 1}, {"fieldname": "column_break_23", "fieldtype": "Column Break"}, {"fieldname": "produced_qty", "fieldtype": "Float", "label": "Produced Qty", "read_only": 1}, {"fieldname": "column_break_24", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "use_batchwise_valuation", "fieldtype": "Check", "label": "Use Batch-wise Valuation", "read_only": 1, "set_only_once": 1}], "icon": "fa fa-archive", "idx": 1, "image_field": "image", "links": [], "max_attachments": 5, "modified": "2023-11-09 12:17:28.339975", "modified_by": "Administrator", "module": "Stock", "name": "<PERSON><PERSON>", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "<PERSON>em Manager", "set_user_permissions": 1, "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "batch_id", "track_changes": 1}