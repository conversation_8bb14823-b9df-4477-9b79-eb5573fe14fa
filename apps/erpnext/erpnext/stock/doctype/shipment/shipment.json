{"actions": [], "autoname": "SHIPMENT-.#####", "creation": "2020-07-09 10:58:52.508703", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["heading_pickup_from", "pickup_from_type", "pickup_company", "pickup_customer", "pickup_supplier", "pickup", "pickup_address_name", "pickup_address", "pickup_contact_person", "pickup_contact_name", "pickup_contact_email", "pickup_contact", "column_break_2", "heading_delivery_to", "delivery_to_type", "delivery_company", "delivery_customer", "delivery_supplier", "delivery_to", "delivery_address_name", "delivery_address", "delivery_contact_name", "delivery_contact_email", "delivery_contact", "parcels_section", "shipment_parcel", "parcel_template", "add_template", "column_break_28", "shipment_delivery_note", "shipment_details_section", "pallets", "value_of_goods", "pickup_date", "pickup_from", "pickup_to", "column_break_36", "shipment_type", "pickup_type", "incoterm", "description_of_content", "section_break_40", "shipment_information_section", "service_provider", "shipment_id", "shipment_amount", "status", "tracking_url", "column_break_55", "carrier", "carrier_service", "awb_number", "tracking_status", "tracking_status_info", "amended_from"], "fields": [{"fieldname": "heading_pickup_from", "fieldtype": "Heading", "label": "Pickup from"}, {"default": "Company", "fieldname": "pickup_from_type", "fieldtype": "Select", "label": "Pickup from", "options": "Company\nCustomer\nSupplier"}, {"depends_on": "eval:doc.pickup_from_type == 'Company'", "fieldname": "pickup_company", "fieldtype": "Link", "label": "Company", "options": "Company"}, {"depends_on": "eval:doc.pickup_from_type == 'Customer'", "fieldname": "pickup_customer", "fieldtype": "Link", "label": "Customer", "options": "Customer"}, {"depends_on": "eval:doc.pickup_from_type == 'Supplier'", "fieldname": "pickup_supplier", "fieldtype": "Link", "label": "Supplier", "options": "Supplier"}, {"fieldname": "pickup", "fieldtype": "Data", "hidden": 1, "in_list_view": 1, "label": "Pickup From", "read_only": 1}, {"depends_on": "eval: doc.pickup_customer || doc.pickup_supplier || doc.pickup_from_type == \"Company\"", "fieldname": "pickup_address_name", "fieldtype": "Link", "label": "Address", "options": "Address", "reqd": 1}, {"fieldname": "pickup_address", "fieldtype": "Small Text", "read_only": 1}, {"depends_on": "eval: doc.pickup_customer || doc.pickup_supplier || doc.pickup_from_type !== \"Company\"", "fieldname": "pickup_contact_name", "fieldtype": "Link", "label": "Contact", "mandatory_depends_on": "eval: doc.pickup_from_type !== 'Company'", "options": "Contact"}, {"fieldname": "pickup_contact_email", "fieldtype": "Data", "hidden": 1, "label": "Contact Email", "read_only": 1}, {"fieldname": "pickup_contact", "fieldtype": "Small Text", "read_only": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "heading_delivery_to", "fieldtype": "Heading", "label": "Delivery to"}, {"default": "Customer", "fieldname": "delivery_to_type", "fieldtype": "Select", "label": "Delivery to", "options": "Company\nCustomer\nSupplier"}, {"depends_on": "eval:doc.delivery_to_type == 'Company'", "fieldname": "delivery_company", "fieldtype": "Link", "label": "Company", "options": "Company"}, {"depends_on": "eval:doc.delivery_to_type == 'Customer'", "fieldname": "delivery_customer", "fieldtype": "Link", "label": "Customer", "options": "Customer"}, {"depends_on": "eval:doc.delivery_to_type == 'Supplier'", "fieldname": "delivery_supplier", "fieldtype": "Link", "label": "Supplier", "options": "Supplier"}, {"fieldname": "delivery_to", "fieldtype": "Data", "hidden": 1, "in_list_view": 1, "label": "Delivery To", "read_only": 1}, {"depends_on": "eval: doc.delivery_customer || doc.delivery_supplier || doc.delivery_to_type == \"Company\"", "fieldname": "delivery_address_name", "fieldtype": "Link", "label": "Address", "options": "Address", "reqd": 1}, {"fieldname": "delivery_address", "fieldtype": "Small Text", "read_only": 1}, {"depends_on": "eval: doc.delivery_customer || doc.delivery_supplier || doc.delivery_to_type == \"Company\"", "fieldname": "delivery_contact_name", "fieldtype": "Link", "label": "Contact", "mandatory_depends_on": "eval: doc.delivery_from_type !== 'Company'", "options": "Contact"}, {"fieldname": "delivery_contact_email", "fieldtype": "Data", "hidden": 1, "label": "Contact Email", "read_only": 1}, {"depends_on": "eval:doc.delivery_contact_name", "fieldname": "delivery_contact", "fieldtype": "Small Text", "read_only": 1}, {"fieldname": "parcels_section", "fieldtype": "Section Break", "label": "<PERSON><PERSON><PERSON>"}, {"fieldname": "shipment_parcel", "fieldtype": "Table", "label": "Shipment Parcel", "options": "Shipment Parcel"}, {"fieldname": "parcel_template", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON>", "options": "Shipment Parcel Template"}, {"depends_on": "eval:doc.docstatus !== 1\n", "fieldname": "add_template", "fieldtype": "<PERSON><PERSON>", "label": "Add Template"}, {"fieldname": "column_break_28", "fieldtype": "Column Break"}, {"fieldname": "shipment_details_section", "fieldtype": "Section Break", "label": "Shipment details"}, {"default": "No", "fieldname": "pallets", "fieldtype": "Select", "label": "<PERSON><PERSON><PERSON>", "options": "No\nYes"}, {"fieldname": "value_of_goods", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Value of Goods", "precision": "2", "reqd": 1}, {"allow_on_submit": 1, "fieldname": "pickup_date", "fieldtype": "Date", "in_list_view": 1, "label": "Pickup Date", "reqd": 1}, {"allow_on_submit": 1, "default": "09:00", "fieldname": "pickup_from", "fieldtype": "Time", "label": "Pickup from", "reqd": 1}, {"allow_on_submit": 1, "default": "17:00", "fieldname": "pickup_to", "fieldtype": "Time", "label": "Pickup to", "reqd": 1}, {"fieldname": "column_break_36", "fieldtype": "Column Break"}, {"default": "Goods", "fieldname": "shipment_type", "fieldtype": "Select", "label": "Shipment Type", "options": "Goods\nDocuments"}, {"default": "Pickup", "fieldname": "pickup_type", "fieldtype": "Select", "label": "Pickup Type", "options": "Pickup\nSelf delivery"}, {"fieldname": "description_of_content", "fieldtype": "Small Text", "label": "Description of Content", "reqd": 1}, {"fieldname": "section_break_40", "fieldtype": "Section Break"}, {"fieldname": "shipment_information_section", "fieldtype": "Section Break", "label": "Shipment Information"}, {"fieldname": "service_provider", "fieldtype": "Data", "label": "Service Provider", "no_copy": 1, "print_hide": 1}, {"fieldname": "shipment_id", "fieldtype": "Data", "label": "Shipment ID", "no_copy": 1, "print_hide": 1}, {"fieldname": "shipment_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Shipment Amount", "no_copy": 1, "precision": "2", "print_hide": 1}, {"fieldname": "status", "fieldtype": "Select", "label": "Status", "no_copy": 1, "options": "Draft\nSubmitted\nBooked\nCancelled\nCompleted", "print_hide": 1, "read_only": 1}, {"fieldname": "tracking_url", "fieldtype": "Small Text", "hidden": 1, "label": "Tracking URL", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "carrier", "fieldtype": "Data", "label": "Carrier", "no_copy": 1, "print_hide": 1}, {"fieldname": "carrier_service", "fieldtype": "Data", "label": "Carrier Service", "no_copy": 1, "print_hide": 1}, {"fieldname": "awb_number", "fieldtype": "Data", "label": "AWB Number", "no_copy": 1, "print_hide": 1}, {"fieldname": "tracking_status", "fieldtype": "Select", "label": "Tracking Status", "no_copy": 1, "options": "\nIn Progress\nDelivered\nReturned\nLost", "print_hide": 1}, {"fieldname": "tracking_status_info", "fieldtype": "Data", "label": "Tracking Status Info", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "hidden": 1, "label": "Amended From", "no_copy": 1, "options": "Shipment", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_55", "fieldtype": "Column Break"}, {"fieldname": "incoterm", "fieldtype": "Link", "label": "Incoterm", "options": "Incoterm"}, {"fieldname": "shipment_delivery_note", "fieldtype": "Table", "label": "Shipment Delivery Note", "options": "Shipment Delivery Note"}, {"depends_on": "eval:doc.pickup_from_type === 'Company'", "fieldname": "pickup_contact_person", "fieldtype": "Link", "label": "Pickup Contact Person", "mandatory_depends_on": "eval:doc.pickup_from_type === 'Company'", "options": "User"}], "is_submittable": 1, "links": [], "modified": "2022-11-17 17:23:27.025802", "modified_by": "Administrator", "module": "Stock", "name": "Shipment", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "share": 1, "submit": 1, "write": 1}, {"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}