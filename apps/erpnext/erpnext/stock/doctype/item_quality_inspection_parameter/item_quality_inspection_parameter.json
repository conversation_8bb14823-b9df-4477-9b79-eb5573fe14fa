{"actions": [], "autoname": "hash", "creation": "2013-02-22 01:28:01", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["specification", "parameter_group", "value", "numeric", "column_break_3", "min_value", "max_value", "formula_based_criteria", "acceptance_formula"], "fields": [{"fieldname": "specification", "fieldtype": "Link", "in_list_view": 1, "label": "Parameter", "oldfieldname": "specification", "oldfieldtype": "Data", "options": "Quality Inspection Parameter", "print_width": "200px", "reqd": 1, "width": "100px"}, {"depends_on": "eval:(!doc.formula_based_criteria && !doc.numeric)", "fieldname": "value", "fieldtype": "Data", "in_list_view": 1, "label": "Acceptance Criteria Value", "oldfieldname": "value", "oldfieldtype": "Data"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"depends_on": "formula_based_criteria", "description": "Simple Python formula applied on Reading fields.<br> Numeric eg. 1: <b>reading_1 &gt; 0.2 and reading_1 &lt; 0.5</b><br>\nNumeric eg. 2: <b>mean &gt; 3.5</b> (mean of populated fields)<br>\nValue based eg.:  <b>reading_value in (\"A\", \"B\", \"C\")</b>", "fieldname": "acceptance_formula", "fieldtype": "Code", "label": "Acceptance Criteria Formula", "options": "PythonExpression"}, {"default": "0", "fieldname": "formula_based_criteria", "fieldtype": "Check", "label": "Formula Based Criteria"}, {"depends_on": "eval:(!doc.formula_based_criteria && doc.numeric)", "fieldname": "min_value", "fieldtype": "Float", "in_list_view": 1, "label": "Minimum Value"}, {"depends_on": "eval:(!doc.formula_based_criteria && doc.numeric)", "fieldname": "max_value", "fieldtype": "Float", "in_list_view": 1, "label": "Maximum Value"}, {"default": "1", "fieldname": "numeric", "fieldtype": "Check", "in_list_view": 1, "label": "Numeric", "width": "80px"}, {"fetch_from": "specification.parameter_group", "fieldname": "parameter_group", "fieldtype": "Link", "label": "Parameter Group", "options": "Quality Inspection Parameter Group", "read_only": 1}], "idx": 1, "istable": 1, "links": [], "modified": "2021-08-06 15:08:20.911338", "modified_by": "Administrator", "module": "Stock", "name": "Item Quality Inspection Parameter", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC"}