[{"description": "_Test Item 1", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 0, "inspection_required": 0, "is_stock_item": 1, "is_sub_contracted_item": 0, "item_code": "_Test Item", "item_group": "_Test Item Group", "item_name": "_Test Item", "apply_warehouse_wise_reorder_level": 1, "opening_stock": 10, "valuation_rate": 100, "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}], "reorder_levels": [{"material_request_type": "Purchase", "warehouse": "_Test Warehouse - _TC", "warehouse_reorder_level": 20, "warehouse_reorder_qty": 20}], "uoms": [{"uom": "_Test UOM", "conversion_factor": 1.0}, {"uom": "_Test UOM 1", "conversion_factor": 10.0}], "stock_uom": "_Test UOM"}, {"description": "_Test Item 2", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 0, "inspection_required": 0, "is_stock_item": 1, "is_sub_contracted_item": 0, "item_code": "_Test Item 2", "item_group": "_Test Item Group", "item_name": "_Test Item 2", "stock_uom": "_Test UOM", "opening_stock": 10, "valuation_rate": 100, "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}]}, {"description": "_Test Item Home Desktop 100 3", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 0, "inspection_required": 0, "is_stock_item": 1, "is_sub_contracted_item": 0, "item_code": "_Test Item Home Desktop 100", "item_group": "_Test Item Group Desktops", "item_name": "_Test Item Home Desktop 100", "valuation_rate": 100, "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}], "taxes": [{"doctype": "Item Tax", "parentfield": "taxes", "item_tax_template": "_Test Account Excise Duty @ 10 - _TC"}], "stock_uom": "_Test UOM 1"}, {"description": "_Test Item Home Desktop 200 4", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 0, "inspection_required": 0, "is_sub_contracted_item": 0, "item_code": "_Test Item Home Desktop 200", "item_group": "_Test Item Group Desktops", "item_name": "_Test Item Home Desktop 200", "stock_uom": "_Test UOM 1", "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}]}, {"description": "_Test Product Bundle Item 5", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 0, "inspection_required": 0, "is_stock_item": 0, "is_sub_contracted_item": 0, "item_code": "_Test Product Bundle Item", "item_group": "_Test Item Group Desktops", "item_name": "_Test Product Bundle Item", "stock_uom": "_Test UOM", "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}]}, {"description": "_Test FG Item 6", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 0, "inspection_required": 0, "is_stock_item": 1, "is_sub_contracted_item": 1, "item_code": "_Test FG Item", "item_group": "_Test Item Group Desktops", "item_name": "_Test FG Item", "stock_uom": "_Test UOM", "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}]}, {"description": "_Test Non Stock Item 7", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 0, "inspection_required": 0, "is_stock_item": 0, "is_sub_contracted_item": 0, "item_code": "_Test Non Stock Item", "item_group": "_Test Item Group Desktops", "item_name": "_Test Non Stock Item", "stock_uom": "_Test UOM", "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}]}, {"description": "_Test Serialized Item 8", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 1, "inspection_required": 0, "is_stock_item": 1, "is_sub_contracted_item": 0, "item_code": "_Test Serialized Item", "item_group": "_Test Item Group Desktops", "item_name": "_Test Serialized Item", "stock_uom": "_Test UOM", "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}]}, {"description": "_Test Serialized Item 9", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 1, "inspection_required": 0, "is_stock_item": 1, "is_sub_contracted_item": 0, "item_code": "_Test Serialized Item With Series", "item_group": "_Test Item Group Desktops", "item_name": "_Test Serialized Item With Series", "serial_no_series": "ABCD.#####", "stock_uom": "_Test UOM", "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}]}, {"description": "_Test Item Home Desktop Manufactured 10", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 0, "inspection_required": 0, "is_stock_item": 1, "is_sub_contracted_item": 0, "item_code": "_Test Item Home Desktop Manufactured", "item_group": "_Test Item Group Desktops", "item_name": "_Test Item Home Desktop Manufactured", "stock_uom": "_Test UOM", "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}]}, {"description": "_Test FG Item 2 11", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 0, "inspection_required": 0, "is_stock_item": 1, "is_sub_contracted_item": 1, "item_code": "_Test FG Item 2", "item_group": "_Test Item Group Desktops", "item_name": "_Test FG Item 2", "stock_uom": "_Test UOM", "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}]}, {"description": "_Test Variant Item 12", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 0, "inspection_required": 0, "is_stock_item": 1, "is_sub_contracted_item": 1, "item_code": "_Test V<PERSON>t <PERSON>", "item_group": "_Test Item Group Desktops", "item_name": "_Test V<PERSON>t <PERSON>", "stock_uom": "_Test UOM", "has_variants": 1, "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}], "attributes": [{"attribute": "Test Size"}], "apply_warehouse_wise_reorder_level": 1, "reorder_levels": [{"material_request_type": "Purchase", "warehouse": "_Test Warehouse - _TC", "warehouse_reorder_level": 20, "warehouse_reorder_qty": 20}]}, {"description": "_Test Item 1", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 0, "inspection_required": 0, "is_stock_item": 1, "is_sub_contracted_item": 0, "item_code": "_Test Item Warehouse Group Wise Reorder", "item_group": "_Test Item Group", "item_name": "_Test Item Warehouse Group Wise Reorder", "apply_warehouse_wise_reorder_level": 1, "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse Group-C1 - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}], "reorder_levels": [{"warehouse_group": "_Test Warehouse Group - _TC", "material_request_type": "Purchase", "warehouse": "_Test Warehouse Group-C1 - _TC", "warehouse_reorder_level": 20, "warehouse_reorder_qty": 20}], "stock_uom": "_Test UOM"}, {"description": "_Test Item With Item Tax Template", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 0, "inspection_required": 0, "is_stock_item": 1, "is_sub_contracted_item": 0, "item_code": "_Test Item With Item Tax Template", "item_group": "_Test Item Group", "item_name": "_Test Item With Item Tax Template", "stock_uom": "_Test UOM", "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}], "taxes": [{"doctype": "Item Tax", "parentfield": "taxes", "item_tax_template": "_Test Account Excise Duty @ 10 - _TC"}, {"doctype": "Item Tax", "parentfield": "taxes", "item_tax_template": "_Test Account Excise Duty @ 12 - _TC", "tax_category": "_Test Tax Category 1"}]}, {"description": "_Test Item Inherit Group Item Tax Template 1", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 0, "inspection_required": 0, "is_stock_item": 1, "is_sub_contracted_item": 0, "item_code": "_Test Item Inherit Group Item Tax Template 1", "item_group": "_Test Item Group Tax Parent", "item_name": "_Test Item Inherit Group Item Tax Template 1", "stock_uom": "_Test UOM", "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}]}, {"description": "_Test Item Inherit Group Item Tax Template 2", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 0, "inspection_required": 0, "is_stock_item": 1, "is_sub_contracted_item": 0, "item_code": "_Test Item Inherit Group Item Tax Template 2", "item_group": "_Test Item Group Tax Child Override", "item_name": "_Test Item Inherit Group Item Tax Template 2", "stock_uom": "_Test UOM", "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}]}, {"description": "_Test Item Override Group Item Tax Template", "doctype": "<PERSON><PERSON>", "has_batch_no": 0, "has_serial_no": 0, "inspection_required": 0, "is_stock_item": 1, "is_sub_contracted_item": 0, "item_code": "_Test Item Override Group Item Tax Template", "item_group": "_Test Item Group Tax Child Override", "item_name": "_Test Item Override Group Item Tax Template", "stock_uom": "_Test UOM", "item_defaults": [{"company": "_Test Company", "default_warehouse": "_Test Warehouse - _TC", "expense_account": "_Test Account Cost for Goods Sold - _TC", "buying_cost_center": "_Test Cost Center - _TC", "selling_cost_center": "_Test Cost Center - _TC", "income_account": "Sales - _TC"}], "taxes": [{"doctype": "Item Tax", "parentfield": "taxes", "item_tax_template": "_Test Account Excise Duty @ 20 - _TC"}, {"doctype": "Item Tax", "parentfield": "taxes", "tax_category": "_Test Tax Category 1", "item_tax_template": "_Test Item Tax Template 1 - _TC"}]}, {"description": "_Test", "doctype": "<PERSON><PERSON>", "is_stock_item": 1, "item_code": "138-<PERSON><PERSON>", "item_group": "_Test Item Group", "item_name": "138-<PERSON><PERSON>", "stock_uom": "_Test UOM"}]