{"actions": [], "allow_guest_to_view": 1, "allow_import": 1, "allow_rename": 1, "autoname": "field:item_code", "creation": "2013-05-03 10:45:46", "description": "A Product or a Service that is bought, sold or kept in stock.", "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "engine": "InnoDB", "field_order": ["details", "naming_series", "item_code", "item_name", "item_group", "stock_uom", "column_break0", "disabled", "allow_alternative_item", "is_stock_item", "has_variants", "opening_stock", "valuation_rate", "standard_rate", "is_fixed_asset", "auto_create_assets", "is_grouped_asset", "asset_category", "asset_naming_series", "over_delivery_receipt_allowance", "over_billing_allowance", "image", "section_break_11", "description", "brand", "dashboard_tab", "inventory_section", "inventory_settings_section", "shelf_life_in_days", "end_of_life", "default_material_request_type", "valuation_method", "column_break1", "warranty_period", "weight_per_unit", "weight_uom", "allow_negative_stock", "sb_barcodes", "barcodes", "reorder_section", "reorder_levels", "unit_of_measure_conversion", "uoms", "serial_nos_and_batches", "has_batch_no", "create_new_batch", "batch_number_series", "has_expiry_date", "retain_sample", "sample_quantity", "column_break_37", "has_serial_no", "serial_no_series", "variants_section", "variant_of", "variant_based_on", "attributes", "accounting", "deferred_accounting_section", "enable_deferred_expense", "no_of_months_exp", "column_break_9s9o", "enable_deferred_revenue", "no_of_months", "section_break_avcp", "item_defaults", "purchasing_tab", "purchase_uom", "min_order_qty", "safety_stock", "is_purchase_item", "purchase_details_cb", "lead_time_days", "last_purchase_rate", "is_customer_provided_item", "customer", "supplier_details", "delivered_by_supplier", "column_break2", "supplier_items", "foreign_trade_details", "country_of_origin", "column_break_59", "customs_tariff_number", "sales_details", "sales_uom", "grant_commission", "is_sales_item", "column_break3", "max_discount", "customer_details", "customer_items", "item_tax_section_break", "taxes", "quality_tab", "inspection_required_before_purchase", "quality_inspection_template", "inspection_required_before_delivery", "manufacturing", "include_item_in_manufacturing", "is_sub_contracted_item", "default_bom", "column_break_74", "customer_code", "default_item_manufacturer", "default_manufacturer_part_no", "total_projected_qty"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "STO-ITEM-.YYYY.-", "set_only_once": 1}, {"bold": 1, "fieldname": "item_code", "fieldtype": "Data", "in_global_search": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Data", "reqd": 1, "unique": 1}, {"depends_on": "variant_of", "description": "If item is a variant of another item then description, image, pricing, taxes etc will be set from the template unless explicitly specified", "fieldname": "variant_of", "fieldtype": "Link", "ignore_user_permissions": 1, "in_standard_filter": 1, "label": "Variant Of", "options": "<PERSON><PERSON>", "read_only": 1, "search_index": 1, "set_only_once": 1}, {"bold": 1, "fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data", "search_index": 1}, {"fieldname": "item_group", "fieldtype": "Link", "in_list_view": 1, "in_preview": 1, "in_standard_filter": 1, "label": "Item Group", "oldfieldname": "item_group", "oldfieldtype": "Link", "options": "Item Group", "reqd": 1, "search_index": 1}, {"fieldname": "stock_uom", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Default Unit of Measure", "oldfieldname": "stock_uom", "oldfieldtype": "Link", "options": "UOM", "reqd": 1}, {"fieldname": "column_break0", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled", "search_index": 1}, {"default": "0", "fieldname": "allow_alternative_item", "fieldtype": "Check", "label": "Allow Alternative Item"}, {"allow_in_quick_entry": 1, "bold": 1, "default": "1", "depends_on": "eval:!doc.is_fixed_asset", "fieldname": "is_stock_item", "fieldtype": "Check", "label": "Maintain Stock", "oldfieldname": "is_stock_item", "oldfieldtype": "Select"}, {"default": "1", "depends_on": "eval:!doc.is_fixed_asset", "fieldname": "include_item_in_manufacturing", "fieldtype": "Check", "label": "Include Item In Manufacturing"}, {"bold": 1, "depends_on": "eval:(doc.__islocal&&doc.is_stock_item && !doc.has_serial_no && !doc.has_batch_no)", "fieldname": "opening_stock", "fieldtype": "Float", "label": "Opening Stock"}, {"depends_on": "is_stock_item", "fieldname": "valuation_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Valuation Rate"}, {"bold": 1, "depends_on": "eval:doc.__islocal", "fieldname": "standard_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Standard Selling Rate"}, {"allow_in_quick_entry": 1, "default": "0", "fieldname": "is_fixed_asset", "fieldtype": "Check", "label": "Is Fixed Asset", "set_only_once": 1}, {"allow_in_quick_entry": 1, "depends_on": "is_fixed_asset", "fieldname": "asset_category", "fieldtype": "Link", "label": "Asset Category", "mandatory_depends_on": "is_fixed_asset", "options": "Asset Category"}, {"depends_on": "is_fixed_asset", "fieldname": "asset_naming_series", "fieldtype": "Select", "label": "Asset Naming Series"}, {"fieldname": "image", "fieldtype": "Attach Image", "hidden": 1, "in_preview": 1, "label": "Image", "options": "image", "print_hide": 1}, {"collapsible": 1, "fieldname": "section_break_11", "fieldtype": "Section Break", "label": "Description"}, {"fieldname": "brand", "fieldtype": "Link", "label": "Brand", "oldfieldname": "brand", "oldfieldtype": "Link", "options": "Brand", "print_hide": 1}, {"fieldname": "description", "fieldtype": "Text Editor", "in_preview": 1, "label": "Description", "oldfieldname": "description", "oldfieldtype": "Text"}, {"fieldname": "sb_barcodes", "fieldtype": "Section Break", "label": "Barcodes"}, {"fieldname": "barcodes", "fieldtype": "Table", "label": "Barcodes", "options": "Item Barcode"}, {"collapsible": 1, "collapsible_depends_on": "is_stock_item", "depends_on": "is_stock_item", "fieldname": "inventory_section", "fieldtype": "Tab Break", "label": "Inventory", "oldfieldtype": "Section Break", "options": "fa fa-truck"}, {"fieldname": "shelf_life_in_days", "fieldtype": "Int", "label": "<PERSON><PERSON> Life In Days", "mandatory_depends_on": "eval:doc.has_batch_no && doc.has_expiry_date"}, {"default": "2099-12-31", "depends_on": "is_stock_item", "fieldname": "end_of_life", "fieldtype": "Date", "label": "End of Life", "oldfieldname": "end_of_life", "oldfieldtype": "Date"}, {"default": "Purchase", "fieldname": "default_material_request_type", "fieldtype": "Select", "label": "Default Material Request Type", "options": "Purchase\nMaterial Transfer\nMaterial Issue\nManufacture\nCustomer Provided"}, {"depends_on": "is_stock_item", "fieldname": "valuation_method", "fieldtype": "Select", "label": "Valuation Method", "options": "\nFIFO\nMoving Average\nLIFO"}, {"depends_on": "is_stock_item", "fieldname": "column_break1", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "width": "50%"}, {"depends_on": "eval:doc.is_stock_item", "fieldname": "warranty_period", "fieldtype": "Data", "label": "Warranty Period (in days)", "oldfieldname": "warranty_period", "oldfieldtype": "Data"}, {"depends_on": "is_stock_item", "fieldname": "weight_per_unit", "fieldtype": "Float", "label": "Weight Per Unit"}, {"depends_on": "eval:doc.is_stock_item", "fieldname": "weight_uom", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Weight UOM", "options": "UOM"}, {"collapsible": 1, "depends_on": "is_stock_item", "fieldname": "reorder_section", "fieldtype": "Section Break", "label": "Auto re-order", "options": "fa fa-rss"}, {"description": "Will also apply for variants unless overrridden", "fieldname": "reorder_levels", "fieldtype": "Table", "label": "Reorder level based on Warehouse", "options": "<PERSON><PERSON>"}, {"collapsible": 1, "fieldname": "unit_of_measure_conversion", "fieldtype": "Section Break", "label": "Units of Measure"}, {"description": "Will also apply for variants", "fieldname": "uoms", "fieldtype": "Table", "label": "UOMs", "oldfieldname": "uom_conversion_details", "oldfieldtype": "Table", "options": "UOM Conversion Detail"}, {"collapsible": 1, "collapsible_depends_on": "eval:doc.has_batch_no || doc.has_serial_no", "depends_on": "eval:doc.is_stock_item", "fieldname": "serial_nos_and_batches", "fieldtype": "Section Break", "label": "Serial Nos and Batches"}, {"default": "0", "depends_on": "eval:doc.is_stock_item", "fieldname": "has_batch_no", "fieldtype": "Check", "label": "Has Batch No", "oldfieldname": "has_batch_no", "oldfieldtype": "Select"}, {"default": "0", "depends_on": "has_batch_no", "fieldname": "create_new_batch", "fieldtype": "Check", "label": "Automatically Create New Batch"}, {"depends_on": "eval:doc.has_batch_no==1 && doc.create_new_batch==1", "description": "Example: ABCD.#####. If series is set and Batch No is not mentioned in transactions, then automatic batch number will be created based on this series. If you always want to explicitly mention Batch No for this item, leave this blank. Note: this setting will take priority over the Naming Series Prefix in Stock Settings.", "fieldname": "batch_number_series", "fieldtype": "Data", "label": "Batch Number Series", "translatable": 1}, {"default": "0", "depends_on": "has_batch_no", "fieldname": "has_expiry_date", "fieldtype": "Check", "label": "Has Expiry Date"}, {"default": "0", "depends_on": "has_batch_no", "fieldname": "retain_sample", "fieldtype": "Check", "label": "<PERSON><PERSON>"}, {"depends_on": "eval: (doc.retain_sample && doc.has_batch_no)", "description": "Maximum sample quantity that can be retained", "fieldname": "sample_quantity", "fieldtype": "Int", "label": "<PERSON> Quantity"}, {"fieldname": "column_break_37", "fieldtype": "Column Break"}, {"default": "0", "depends_on": "eval:doc.is_stock_item", "fieldname": "has_serial_no", "fieldtype": "Check", "label": "Has Serial No", "oldfieldname": "has_serial_no", "oldfieldtype": "Select"}, {"depends_on": "has_serial_no", "description": "Example: ABCD.#####\nIf series is set and Serial No is not mentioned in transactions, then automatic serial number will be created based on this series. If you always want to explicitly mention Serial Nos for this item. leave this blank.", "fieldname": "serial_no_series", "fieldtype": "Data", "label": "Serial Number Series"}, {"collapsible": 1, "collapsible_depends_on": "attributes", "depends_on": "eval:!doc.is_fixed_asset", "fieldname": "variants_section", "fieldtype": "Tab Break", "label": "Variants"}, {"default": "0", "depends_on": "eval:!doc.variant_of", "description": "If this item has variants, then it cannot be selected in sales orders etc.", "fieldname": "has_variants", "fieldtype": "Check", "in_standard_filter": 1, "label": "<PERSON>"}, {"default": "Item Attribute", "depends_on": "has_variants", "fieldname": "variant_based_on", "fieldtype": "Select", "label": "Variant Based On", "options": "Item Attribute\nManufacturer"}, {"depends_on": "eval:(doc.has_variants || doc.variant_of) && doc.variant_based_on==='Item Attribute'", "fieldname": "attributes", "fieldtype": "Table", "hidden": 1, "label": "V<PERSON><PERSON> Attributes", "mandatory_depends_on": "eval:(doc.has_variants || doc.variant_of) && doc.variant_based_on==='Item Attribute'", "options": "<PERSON><PERSON> Attribute"}, {"fieldname": "item_defaults", "fieldtype": "Table", "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>"}, {"default": "1", "fieldname": "is_purchase_item", "fieldtype": "Check", "label": "Allow Purchase"}, {"fieldname": "purchase_uom", "fieldtype": "Link", "label": "Default Purchase Unit of Measure", "options": "UOM"}, {"default": "0.00", "depends_on": "is_stock_item", "description": "Minimum quantity should be as per Stock UOM", "fieldname": "min_order_qty", "fieldtype": "Float", "label": "Minimum Order Qty", "oldfieldname": "min_order_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "safety_stock", "fieldtype": "Float", "label": "Safety Stock"}, {"fieldname": "purchase_details_cb", "fieldtype": "Column Break"}, {"description": "Average time taken by the supplier to deliver", "fieldname": "lead_time_days", "fieldtype": "Int", "label": "Lead Time in days", "oldfieldname": "lead_time_days", "oldfieldtype": "Int"}, {"fieldname": "last_purchase_rate", "fieldtype": "Float", "label": "Last Purchase Rate", "no_copy": 1, "oldfieldname": "last_purchase_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"default": "0", "fieldname": "is_customer_provided_item", "fieldtype": "Check", "label": "Is Customer Provided Item"}, {"depends_on": "eval:doc.is_customer_provided_item==1", "fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customer"}, {"collapsible": 1, "depends_on": "eval:!doc.is_fixed_asset", "fieldname": "supplier_details", "fieldtype": "Section Break", "label": "Supplier Details"}, {"default": "0", "fieldname": "delivered_by_supplier", "fieldtype": "Check", "label": "Delivered by <PERSON><PERSON><PERSON> (Drop Ship)", "print_hide": 1}, {"fieldname": "column_break2", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "width": "50%"}, {"fieldname": "supplier_items", "fieldtype": "Table", "label": "Supplier Items", "options": "<PERSON>em Su<PERSON>lier"}, {"collapsible": 1, "fieldname": "foreign_trade_details", "fieldtype": "Section Break", "label": "Foreign Trade Details"}, {"fieldname": "country_of_origin", "fieldtype": "Link", "label": "Country of Origin", "options": "Country"}, {"fieldname": "column_break_59", "fieldtype": "Column Break"}, {"fieldname": "customs_tariff_number", "fieldtype": "Link", "label": "Customs Tariff Number", "options": "Customs Tariff Number"}, {"collapsible": 1, "fieldname": "sales_details", "fieldtype": "Tab Break", "label": "Sales", "oldfieldtype": "Section Break", "options": "fa fa-tag"}, {"fieldname": "sales_uom", "fieldtype": "Link", "label": "Default Sales Unit of Measure", "options": "UOM"}, {"default": "1", "fieldname": "is_sales_item", "fieldtype": "Check", "label": "Allow Sales"}, {"fieldname": "column_break3", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "width": "50%"}, {"fieldname": "max_discount", "fieldtype": "Float", "label": "Max Discount (%)", "oldfieldname": "max_discount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"default": "0", "fieldname": "enable_deferred_revenue", "fieldtype": "Check", "label": "Enable Deferred Revenue"}, {"depends_on": "enable_deferred_revenue", "fieldname": "no_of_months", "fieldtype": "Int", "label": "No of Months (Revenue)"}, {"default": "0", "fieldname": "enable_deferred_expense", "fieldtype": "Check", "label": "Enable Deferred Expense"}, {"depends_on": "enable_deferred_expense", "fieldname": "no_of_months_exp", "fieldtype": "Int", "label": "No of Months (Expense)"}, {"collapsible": 1, "depends_on": "eval:!doc.is_fixed_asset", "fieldname": "customer_details", "fieldtype": "Section Break", "label": "Customer Details"}, {"fieldname": "customer_items", "fieldtype": "Table", "label": "Customer Items", "options": "Item Customer Detail"}, {"collapsible": 1, "collapsible_depends_on": "taxes", "fieldname": "item_tax_section_break", "fieldtype": "Tab Break", "label": "Tax", "oldfieldtype": "Section Break", "options": "fa fa-money"}, {"description": "Will also apply for variants", "fieldname": "taxes", "fieldtype": "Table", "label": "Taxes", "oldfieldname": "item_tax", "oldfieldtype": "Table", "options": "Item Tax"}, {"default": "0", "fieldname": "inspection_required_before_purchase", "fieldtype": "Check", "label": "Inspection Required before Pur<PERSON>", "oldfieldname": "inspection_required", "oldfieldtype": "Select"}, {"default": "0", "fieldname": "inspection_required_before_delivery", "fieldtype": "Check", "label": "Inspection Required before Delivery"}, {"fieldname": "quality_inspection_template", "fieldtype": "Link", "label": "Quality Inspection Template", "options": "Quality Inspection Template", "print_hide": 1}, {"collapsible": 1, "depends_on": "is_stock_item", "fieldname": "manufacturing", "fieldtype": "Tab Break", "label": "Manufacturing", "oldfieldtype": "Section Break", "options": "fa fa-cogs"}, {"fieldname": "default_bom", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Default BOM", "no_copy": 1, "oldfieldname": "default_bom", "oldfieldtype": "Link", "options": "BOM", "read_only": 1}, {"default": "0", "description": "If subcontracted to a vendor", "fieldname": "is_sub_contracted_item", "fieldtype": "Check", "label": "Supply Raw Materials for Purchase", "oldfieldname": "is_sub_contracted_item", "oldfieldtype": "Select"}, {"fieldname": "column_break_74", "fieldtype": "Column Break"}, {"fieldname": "customer_code", "fieldtype": "Small Text", "hidden": 1, "label": "Customer Code", "no_copy": 1, "print_hide": 1}, {"fieldname": "total_projected_qty", "fieldtype": "Float", "hidden": 1, "label": "Total Projected Qty", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:!doc.__islocal && !doc.is_fixed_asset", "fieldname": "over_delivery_receipt_allowance", "fieldtype": "Float", "label": "Over Delivery/Receipt Allowance (%)", "oldfieldname": "tolerance", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"depends_on": "eval:!doc.__islocal && !doc.is_fixed_asset", "fieldname": "over_billing_allowance", "fieldtype": "Float", "label": "Over Billing Allowance (%)"}, {"default": "0", "depends_on": "is_fixed_asset", "fieldname": "auto_create_assets", "fieldtype": "Check", "label": "Auto Create Assets on Purchase"}, {"fieldname": "default_item_manufacturer", "fieldtype": "Link", "label": "Default Item Manufacturer", "options": "Manufacturer", "read_only": 1}, {"fieldname": "default_manufacturer_part_no", "fieldtype": "Data", "label": "Default Manufacturer Part No", "read_only": 1}, {"default": "1", "fieldname": "grant_commission", "fieldtype": "Check", "label": "Grant Commission"}, {"default": "0", "depends_on": "auto_create_assets", "fieldname": "is_grouped_asset", "fieldtype": "Check", "label": "Create Grouped Asset"}, {"default": "0", "fieldname": "allow_negative_stock", "fieldtype": "Check", "label": "Allow Negative Stock"}, {"fieldname": "inventory_settings_section", "fieldtype": "Section Break", "label": "Inventory Settings"}, {"fieldname": "purchasing_tab", "fieldtype": "Tab Break", "label": "Purchasing"}, {"fieldname": "quality_tab", "fieldtype": "Tab Break", "label": "Quality"}, {"fieldname": "details", "fieldtype": "Tab Break", "label": "Details", "oldfieldtype": "Section Break", "options": "fa fa-flag"}, {"fieldname": "dashboard_tab", "fieldtype": "Tab Break", "label": "Dashboard", "show_dashboard": 1}, {"depends_on": "eval:!doc.is_fixed_asset", "fieldname": "accounting", "fieldtype": "Tab Break", "label": "Accounting"}, {"fieldname": "column_break_9s9o", "fieldtype": "Column Break"}, {"fieldname": "section_break_avcp", "fieldtype": "Section Break"}, {"collapsible": 1, "fieldname": "deferred_accounting_section", "fieldtype": "Section Break", "label": "Deferred Accounting"}], "icon": "fa fa-tag", "idx": 2, "image_field": "image", "index_web_pages_for_search": 1, "links": [], "make_attachments_public": 1, "modified": "2024-01-08 18:09:30.225085", "modified_by": "Administrator", "module": "Stock", "name": "<PERSON><PERSON>", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "<PERSON>em Manager", "share": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager"}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User"}, {"read": 1, "role": "Sales User"}, {"read": 1, "role": "Purchase User"}, {"read": 1, "role": "Maintenance User"}, {"read": 1, "role": "Accounts User"}, {"read": 1, "role": "Manufacturing User"}, {"email": 1, "export": 1, "print": 1, "report": 1, "role": "Desk User", "select": 1, "share": 1}], "quick_entry": 1, "search_fields": "item_name,description,item_group,customer_code", "show_name_in_global_search": 1, "show_preview_popup": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "item_name", "track_changes": 1}