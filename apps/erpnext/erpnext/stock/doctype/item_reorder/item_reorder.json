{"allow_copy": 0, "allow_import": 0, "allow_rename": 0, "autoname": "hash", "beta": 0, "creation": "2013-03-07 11:42:59", "custom": 0, "docstatus": 0, "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "fields": [{"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "warehouse_group", "fieldtype": "Link", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "Check in (group)", "length": 0, "no_copy": 0, "options": "Warehouse", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "warehouse", "fieldtype": "Link", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "Request for", "length": 0, "no_copy": 0, "options": "Warehouse", "permlevel": 0, "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 1, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "warehouse_reorder_level", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "Re-order Level", "length": 0, "no_copy": 0, "permlevel": 0, "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "warehouse_reorder_qty", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "Re-order Qty", "length": 0, "no_copy": 0, "permlevel": 0, "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "material_request_type", "fieldtype": "Select", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "Material Request Type", "length": 0, "no_copy": 0, "options": "Purchase\nTransfer\nMaterial Issue\nManufacture", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 1, "search_index": 0, "set_only_once": 0, "unique": 0}], "hide_heading": 0, "hide_toolbar": 0, "idx": 1, "image_view": 0, "in_create": 1, "is_submittable": 0, "issingle": 0, "istable": 1, "max_attachments": 0, "modified": "2023-06-21 15:13:38.270046", "modified_by": "Administrator", "module": "Stock", "name": "<PERSON><PERSON>", "owner": "Administrator", "permissions": [], "quick_entry": 0, "read_only": 0, "read_only_onload": 0, "sort_order": "ASC", "track_seen": 0}