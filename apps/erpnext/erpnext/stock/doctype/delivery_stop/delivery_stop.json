{"actions": [], "creation": "2017-10-16 16:46:28.166950", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["customer", "address", "locked", "column_break_6", "customer_address", "visited", "order_information_section", "delivery_note", "cb_order", "grand_total", "section_break_7", "contact", "email_sent_to", "column_break_7", "customer_contact", "section_break_9", "distance", "estimated_arrival", "lat", "column_break_19", "uom", "lng", "more_information_section", "details"], "fields": [{"columns": 2, "fieldname": "customer", "fieldtype": "Link", "in_list_view": 1, "label": "Customer", "options": "Customer"}, {"fieldname": "address", "fieldtype": "Link", "in_list_view": 1, "label": "Address Name", "options": "Address", "print_hide": 1, "reqd": 1}, {"default": "0", "fieldname": "locked", "fieldtype": "Check", "in_list_view": 1, "label": "Locked"}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"fieldname": "customer_address", "fieldtype": "Small Text", "label": "Customer Address", "read_only": 1}, {"allow_on_submit": 1, "default": "0", "depends_on": "eval:doc.docstatus==1", "fieldname": "visited", "fieldtype": "Check", "label": "Visited", "no_copy": 1, "print_hide": 1}, {"fieldname": "order_information_section", "fieldtype": "Section Break", "label": "Order Information"}, {"fieldname": "delivery_note", "fieldtype": "Link", "in_list_view": 1, "label": "Delivery Note", "no_copy": 1, "options": "Delivery Note", "print_hide": 1, "read_only": 1}, {"fieldname": "cb_order", "fieldtype": "Column Break"}, {"fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Grand Total", "read_only": 1}, {"fieldname": "section_break_7", "fieldtype": "Section Break", "label": "Contact Information"}, {"fieldname": "contact", "fieldtype": "Link", "label": "Contact Name", "options": "Contact", "print_hide": 1}, {"fieldname": "email_sent_to", "fieldtype": "Data", "label": "Email sent to", "read_only": 1}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fieldname": "customer_contact", "fieldtype": "Small Text", "label": "Customer Contact", "read_only": 1}, {"fieldname": "section_break_9", "fieldtype": "Section Break", "label": "Dispatch Information"}, {"fieldname": "distance", "fieldtype": "Float", "label": "Distance", "precision": "2", "read_only": 1}, {"fieldname": "estimated_arrival", "fieldtype": "Datetime", "in_list_view": 1, "label": "Estimated Arrival"}, {"fieldname": "lat", "fieldtype": "Float", "hidden": 1, "label": "Latitude"}, {"fieldname": "column_break_19", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.distance", "fieldname": "uom", "fieldtype": "Link", "label": "UOM", "options": "UOM", "read_only": 1}, {"fieldname": "lng", "fieldtype": "Float", "hidden": 1, "label": "Longitude"}, {"fieldname": "more_information_section", "fieldtype": "Section Break", "label": "More Information"}, {"fieldname": "details", "fieldtype": "Text Editor", "label": "Details"}], "istable": 1, "links": [], "modified": "2023-09-29 09:22:53.435161", "modified_by": "Administrator", "module": "Stock", "name": "Delivery Stop", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}