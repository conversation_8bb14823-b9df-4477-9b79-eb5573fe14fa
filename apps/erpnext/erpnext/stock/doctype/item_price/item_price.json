{"actions": [], "allow_import": 1, "autoname": "hash", "creation": "2013-05-02 16:29:48", "description": "Log the selling and buying rate of an Item", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["item_code", "uom", "packing_unit", "column_break_17", "item_name", "brand", "item_description", "price_list_details", "price_list", "customer", "supplier", "batch_no", "column_break_3", "buying", "selling", "item_details", "currency", "col_br_1", "price_list_rate", "section_break_15", "valid_from", "lead_time_days", "column_break_18", "valid_upto", "section_break_24", "note", "reference"], "fields": [{"fieldname": "item_code", "fieldtype": "Link", "in_filter": 1, "in_global_search": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Item Code", "oldfieldname": "price_list_name", "oldfieldtype": "Select", "options": "<PERSON><PERSON>", "reqd": 1, "search_index": 1}, {"fieldname": "uom", "fieldtype": "Link", "label": "UOM", "options": "UOM"}, {"default": "0", "description": "Quantity  that must be bought or sold per UOM", "fieldname": "packing_unit", "fieldtype": "Int", "label": "Packing Unit"}, {"fieldname": "column_break_17", "fieldtype": "Column Break"}, {"fieldname": "item_name", "fieldtype": "Data", "in_list_view": 1, "label": "Item Name", "read_only": 1}, {"fetch_from": "item_code.brand", "fieldname": "brand", "fieldtype": "Link", "in_list_view": 1, "label": "Brand", "options": "Brand", "read_only": 1}, {"fieldname": "item_description", "fieldtype": "Text", "label": "Item Description", "read_only": 1}, {"fieldname": "price_list_details", "fieldtype": "Section Break", "label": "Price List", "options": "fa fa-tags"}, {"fieldname": "price_list", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Price List", "options": "Price List", "reqd": 1}, {"bold": 1, "depends_on": "eval:doc.selling == 1", "fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customer"}, {"depends_on": "eval:doc.buying == 1", "fieldname": "supplier", "fieldtype": "Link", "label": "Supplier", "options": "Supplier"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "buying", "fieldtype": "Check", "label": "Buying", "read_only": 1}, {"default": "0", "fieldname": "selling", "fieldtype": "Check", "label": "Selling", "read_only": 1}, {"fieldname": "item_details", "fieldtype": "Section Break", "options": "fa fa-tag"}, {"bold": 1, "fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "col_br_1", "fieldtype": "Column Break"}, {"fieldname": "price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_filter": 1, "in_global_search": 1, "in_list_view": 1, "label": "Rate", "oldfieldname": "ref_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "reqd": 1}, {"fieldname": "section_break_15", "fieldtype": "Section Break"}, {"default": "Today", "fieldname": "valid_from", "fieldtype": "Date", "label": "<PERSON><PERSON>"}, {"default": "0", "fieldname": "lead_time_days", "fieldtype": "Int", "label": "Lead Time in days"}, {"fieldname": "column_break_18", "fieldtype": "Column Break"}, {"fieldname": "valid_upto", "fieldtype": "Date", "label": "<PERSON><PERSON>"}, {"fieldname": "section_break_24", "fieldtype": "Section Break"}, {"fieldname": "note", "fieldtype": "Text", "label": "Note"}, {"fieldname": "reference", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Reference"}, {"fieldname": "batch_no", "fieldtype": "Link", "label": "Batch No", "options": "<PERSON><PERSON>"}], "icon": "fa fa-flag", "idx": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2024-01-30 14:02:19.304854", "modified_by": "Administrator", "module": "Stock", "name": "<PERSON><PERSON>", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Master Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase Master Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "ASC", "states": [], "title_field": "item_name", "track_changes": 1}