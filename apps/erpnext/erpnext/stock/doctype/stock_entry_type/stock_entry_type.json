{"actions": [], "autoname": "Prompt", "creation": "2019-03-13 16:23:46.636769", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["purpose", "add_to_transit"], "fields": [{"default": "Material Issue", "fieldname": "purpose", "fieldtype": "Select", "in_list_view": 1, "label": "Purpose", "options": "\nMaterial Issue\nMaterial Receipt\nMaterial Transfer\nMaterial Transfer for Manufacture\nMaterial Consumption for Manufacture\nManufacture\nRepack\nSend to Subcontractor", "reqd": 1, "set_only_once": 1}, {"default": "0", "depends_on": "eval: doc.purpose == 'Material Transfer'", "fieldname": "add_to_transit", "fieldtype": "Check", "label": "Add to Transit"}], "links": [], "modified": "2021-05-21 11:27:01.144110", "modified_by": "Administrator", "module": "Stock", "name": "Stock Entry Type", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Manufacturing Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "ASC", "track_changes": 1}