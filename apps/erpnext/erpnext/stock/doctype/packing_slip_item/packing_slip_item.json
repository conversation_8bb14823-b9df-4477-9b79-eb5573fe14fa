{"actions": [], "autoname": "hash", "creation": "2013-04-08 13:10:16", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "column_break_2", "item_name", "batch_no", "desc_section", "description", "quantity_section", "qty", "net_weight", "column_break_10", "stock_uom", "weight_uom", "page_break", "dn_detail", "pi_detail"], "fields": [{"fieldname": "item_code", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>", "print_width": "100px", "reqd": 1, "width": "100px"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fetch_from": "item_code.item_name", "fieldname": "item_name", "fieldtype": "Data", "in_list_view": 1, "label": "Item Name", "print_width": "200px", "read_only": 1, "width": "200px"}, {"fieldname": "batch_no", "fieldtype": "Link", "label": "Batch No", "options": "<PERSON><PERSON>"}, {"collapsible": 1, "fieldname": "desc_section", "fieldtype": "Section Break", "label": "Description"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description"}, {"fieldname": "quantity_section", "fieldtype": "Section Break", "label": "Quantity"}, {"fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity", "print_width": "100px", "reqd": 1, "width": "100px"}, {"fieldname": "net_weight", "fieldtype": "Float", "in_list_view": 1, "label": "Net Weight", "print_width": "100px", "width": "100px"}, {"fieldname": "column_break_10", "fieldtype": "Column Break"}, {"fieldname": "stock_uom", "fieldtype": "Link", "label": "UOM", "options": "UOM", "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "weight_uom", "fieldtype": "Link", "label": "Weight UOM", "options": "UOM", "print_width": "100px", "width": "100px"}, {"allow_on_submit": 1, "default": "0", "fieldname": "page_break", "fieldtype": "Check", "in_list_view": 1, "label": "Page Break"}, {"fieldname": "dn_detail", "fieldtype": "Data", "hidden": 1, "in_list_view": 1, "label": "Delivery Note Item", "no_copy": 1, "read_only": 1}, {"fieldname": "pi_detail", "fieldtype": "Data", "hidden": 1, "label": "Delivery Note Packed Item", "no_copy": 1, "read_only": 1}], "idx": 1, "istable": 1, "links": [], "modified": "2023-04-28 15:00:14.079306", "modified_by": "Administrator", "module": "Stock", "name": "Packing Slip <PERSON>em", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}