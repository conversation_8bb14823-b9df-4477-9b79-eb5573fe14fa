{"actions": [], "creation": "2013-02-22 01:28:02", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["receipt_document_type", "receipt_document", "supplier", "col_break1", "posting_date", "grand_total"], "fields": [{"fieldname": "receipt_document_type", "fieldtype": "Select", "in_list_view": 1, "label": "Receipt Document Type", "options": "\nPurchase Invoice\nPurchase Receipt", "reqd": 1}, {"fieldname": "receipt_document", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Receipt Document", "oldfieldname": "purchase_receipt_no", "oldfieldtype": "Link", "options": "receipt_document_type", "print_width": "220px", "reqd": 1, "width": "220px"}, {"fieldname": "supplier", "fieldtype": "Link", "in_list_view": 1, "label": "Supplier", "options": "Supplier", "read_only": 1}, {"fieldname": "col_break1", "fieldtype": "Column Break", "width": "50%"}, {"fieldname": "posting_date", "fieldtype": "Date", "label": "Posting Date", "read_only": 1}, {"fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Grand Total", "options": "Company:company:default_currency", "read_only": 1}], "idx": 1, "istable": 1, "links": [], "modified": "2024-02-26 18:41:06.281750", "modified_by": "Administrator", "module": "Stock", "name": "Landed Cost Purchase Receipt", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "ASC", "states": []}