{"actions": [], "creation": "2014-07-11 11:51:00.453717", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["expense_account", "account_currency", "exchange_rate", "description", "col_break3", "amount", "base_amount"], "fields": [{"fieldname": "description", "fieldtype": "Small Text", "in_list_view": 1, "label": "Description", "reqd": 1}, {"fieldname": "col_break3", "fieldtype": "Column Break", "width": "50%"}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "options": "account_currency", "reqd": 1}, {"depends_on": "eval:cint(erpnext.is_perpetual_inventory_enabled(parent.company))", "fieldname": "expense_account", "fieldtype": "Link", "in_list_view": 1, "label": "Expense Account", "mandatory_depends_on": "eval:cint(erpnext.is_perpetual_inventory_enabled(parent.company))", "options": "Account"}, {"fieldname": "account_currency", "fieldtype": "Link", "label": "Account <PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "exchange_rate", "fieldtype": "Float", "label": "Exchange Rate", "precision": "9"}, {"fieldname": "base_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount (Company Currency)", "options": "Company:company:default_currency", "read_only": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2021-05-17 13:57:10.807980", "modified_by": "Administrator", "module": "Stock", "name": "Landed Cost Taxes and Charges", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC"}