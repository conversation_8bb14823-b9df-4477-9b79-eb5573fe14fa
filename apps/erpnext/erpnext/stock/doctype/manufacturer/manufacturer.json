{"allow_import": 1, "allow_rename": 1, "autoname": "field:short_name", "creation": "2016-01-17 11:04:52.761731", "description": "Manufacturers used in Items", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["short_name", "full_name", "website", "country", "logo", "address_contacts", "address_html", "column_break_8", "contact_html", "section_break_10", "notes"], "fields": [{"description": "Limited to 12 characters", "fieldname": "short_name", "fieldtype": "Data", "label": "Short Name", "reqd": 1, "unique": 1}, {"fieldname": "full_name", "fieldtype": "Data", "in_list_view": 1, "label": "Full Name"}, {"fieldname": "website", "fieldtype": "Data", "label": "Website"}, {"fieldname": "country", "fieldtype": "Link", "label": "Country", "options": "Country"}, {"fieldname": "logo", "fieldtype": "Attach Image", "label": "Logo"}, {"fieldname": "notes", "fieldtype": "Small Text", "label": "Notes"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "address_contacts", "fieldtype": "Section Break", "label": "Address and Contacts", "options": "fa fa-map-marker"}, {"fieldname": "address_html", "fieldtype": "HTML", "label": "Address HTML", "read_only": 1}, {"fieldname": "column_break_8", "fieldtype": "Column Break"}, {"fieldname": "contact_html", "fieldtype": "HTML", "label": "Contact HTML", "read_only": 1}, {"fieldname": "section_break_10", "fieldtype": "Section Break"}], "icon": "fa fa-certificate", "modified": "2019-07-06 13:06:47.237014", "modified_by": "Administrator", "module": "Stock", "name": "Manufacturer", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User", "share": 1}], "search_fields": "short_name, full_name", "show_name_in_global_search": 1, "sort_order": "DESC", "title_field": "short_name"}