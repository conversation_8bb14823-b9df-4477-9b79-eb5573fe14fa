{"actions": [], "autoname": "PUT-.####", "creation": "2020-11-09 11:39:46.489501", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["disable", "item_code", "item_name", "warehouse", "priority", "col_break_capacity", "company", "capacity", "uom", "conversion_factor", "stock_uom", "stock_capacity"], "fields": [{"fieldname": "item_code", "fieldtype": "Link", "in_standard_filter": 1, "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>", "reqd": 1}, {"fetch_from": "item_code.item_name", "fieldname": "item_name", "fieldtype": "Data", "label": "Item Name", "read_only": 1}, {"fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Warehouse", "options": "Warehouse", "reqd": 1}, {"fieldname": "col_break_capacity", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "capacity", "fieldtype": "Float", "in_list_view": 1, "label": "Capacity", "reqd": 1}, {"fetch_from": "item_code.stock_uom", "fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "options": "UOM", "read_only": 1}, {"default": "1", "fieldname": "priority", "fieldtype": "Int", "in_list_view": 1, "label": "Priority"}, {"fieldname": "company", "fieldtype": "Link", "in_standard_filter": 1, "label": "Company", "options": "Company", "reqd": 1}, {"default": "0", "depends_on": "eval:!doc.__islocal", "fieldname": "disable", "fieldtype": "Check", "label": "Disable"}, {"fieldname": "uom", "fieldtype": "Link", "label": "UOM", "no_copy": 1, "options": "UOM"}, {"fieldname": "stock_capacity", "fieldtype": "Float", "label": "Capacity in Stock UOM", "no_copy": 1, "read_only": 1}, {"default": "1", "fieldname": "conversion_factor", "fieldtype": "Float", "label": "Conversion Factor", "no_copy": 1, "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2020-11-25 20:39:19.973437", "modified_by": "Administrator", "module": "Stock", "name": "Putaway Rule", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User", "share": 1, "write": 1}, {"email": 1, "export": 1, "permlevel": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "title_field": "item_code", "track_changes": 1}