{"actions": [], "autoname": "hash", "creation": "2013-04-22 13:15:44", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["barcode", "has_item_scanned", "item_code", "item_name", "col_break1", "customer_item_code", "section_break_6", "description", "brand", "item_group", "image_section", "image", "image_view", "quantity_and_rate", "qty", "stock_uom", "col_break2", "uom", "conversion_factor", "stock_qty_sec_break", "stock_qty", "stock_qty_col_break", "returned_qty", "section_break_17", "price_list_rate", "base_price_list_rate", "discount_and_margin", "margin_type", "margin_rate_or_amount", "rate_with_margin", "column_break_19", "discount_percentage", "discount_amount", "base_rate_with_margin", "section_break_1", "rate", "amount", "col_break3", "base_rate", "base_amount", "pricing_rules", "stock_uom_rate", "is_free_item", "grant_commission", "section_break_25", "net_rate", "net_amount", "item_tax_template", "column_break_28", "base_net_rate", "base_net_amount", "billed_amt", "incoming_rate", "item_weight_details", "weight_per_unit", "total_weight", "column_break_21", "weight_uom", "warehouse_and_reference", "warehouse", "target_warehouse", "quality_inspection", "col_break4", "allow_zero_valuation_rate", "against_sales_order", "so_detail", "against_sales_invoice", "si_detail", "dn_detail", "pick_list_item", "section_break_40", "pick_serial_and_batch", "serial_and_batch_bundle", "use_serial_batch_fields", "column_break_eaoe", "section_break_qyjv", "serial_no", "column_break_rxvc", "batch_no", "available_qty_section", "actual_batch_qty", "actual_qty", "installed_qty", "item_tax_rate", "column_break_atna", "packed_qty", "received_qty", "accounting_details_section", "expense_account", "column_break_71", "internal_transfer_section", "material_request", "purchase_order", "column_break_82", "purchase_order_item", "material_request_item", "accounting_dimensions_section", "cost_center", "dimension_col_break", "project", "section_break_72", "page_break"], "fields": [{"fieldname": "barcode", "fieldtype": "Data", "label": "Barcode", "print_hide": 1}, {"fieldname": "item_code", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "print_width": "150px", "reqd": 1, "search_index": 1, "width": "150px"}, {"fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data", "print_hide": 1, "print_width": "150px", "reqd": 1, "width": "150px"}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"fieldname": "customer_item_code", "fieldtype": "Data", "hidden": 1, "label": "Customer's Item Code", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "section_break_6", "fieldtype": "Section Break", "label": "Description"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Small Text", "print_width": "300px", "width": "300px"}, {"fetch_from": "item_code.image", "fieldname": "image", "fieldtype": "Attach", "hidden": 1, "label": "Image"}, {"fieldname": "image_view", "fieldtype": "Image", "label": "Image View", "options": "image", "print_hide": 1}, {"fieldname": "quantity_and_rate", "fieldtype": "Section Break", "label": "Quantity and Rate"}, {"fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity", "oldfieldname": "qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_width": "100px", "reqd": 1, "width": "100px"}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "oldfieldname": "stock_uom", "oldfieldtype": "Data", "options": "UOM", "print_hide": 1, "print_width": "50px", "read_only": 1, "reqd": 1, "width": "50px"}, {"fieldname": "col_break2", "fieldtype": "Column Break"}, {"fieldname": "uom", "fieldtype": "Link", "in_list_view": 1, "label": "UOM", "options": "UOM", "reqd": 1}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "conversion_factor", "fieldtype": "Float", "label": "UOM Conversion Factor", "print_hide": 1, "read_only": 1, "reqd": 1}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "stock_qty", "fieldtype": "Float", "label": "Qty in Stock UOM", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_17", "fieldtype": "Section Break"}, {"fieldname": "price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price List Rate", "oldfieldname": "ref_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "base_price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price List Rate (Company Currency)", "oldfieldname": "base_ref_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"collapsible": 1, "collapsible_depends_on": "eval: doc.margin_type || doc.discount_amount", "fieldname": "discount_and_margin", "fieldtype": "Section Break", "label": "Discount and Margin"}, {"depends_on": "price_list_rate", "fieldname": "margin_type", "fieldtype": "Select", "label": "Margin Type", "options": "\nPercentage\nAmount", "print_hide": 1}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate", "fieldname": "margin_rate_or_amount", "fieldtype": "Float", "label": "Margin Rate or Amount", "print_hide": 1}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate && doc.margin_rate_or_amount", "fieldname": "rate_with_margin", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate With Margin", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_19", "fieldtype": "Column Break"}, {"depends_on": "price_list_rate", "fieldname": "discount_percentage", "fieldtype": "Float", "label": "Discount (%) on Price List Rate with <PERSON><PERSON>", "oldfieldname": "adj_rate", "oldfieldtype": "Float", "print_hide": 1, "print_width": "100px", "width": "100px"}, {"depends_on": "price_list_rate", "fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Discount Amount", "options": "currency"}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate && doc.margin_rate_or_amount", "fieldname": "base_rate_with_margin", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate With Margin (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_1", "fieldtype": "Section Break"}, {"fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "oldfieldname": "export_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_width": "150px", "width": "150px"}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "oldfieldname": "export_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "col_break3", "fieldtype": "Column Break"}, {"fieldname": "base_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate (Company Currency)", "oldfieldname": "basic_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "base_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount (Company Currency)", "oldfieldname": "amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "pricing_rules", "fieldtype": "Small Text", "hidden": 1, "label": "Pricing Rules", "print_hide": 1, "read_only": 1}, {"default": "0", "fieldname": "is_free_item", "fieldtype": "Check", "label": "Is Free Item", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_25", "fieldtype": "Section Break"}, {"fieldname": "net_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Rate", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Amount", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_28", "fieldtype": "Column Break"}, {"fieldname": "base_net_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Rate (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "item_weight_details", "fieldtype": "Section Break", "label": "Item Weight Details"}, {"fieldname": "weight_per_unit", "fieldtype": "Float", "label": "Weight Per Unit"}, {"fieldname": "total_weight", "fieldtype": "Float", "label": "Total Weight", "read_only": 1}, {"fieldname": "column_break_21", "fieldtype": "Column Break", "print_hide": 1}, {"fieldname": "weight_uom", "fieldtype": "Link", "label": "Weight UOM", "options": "UOM"}, {"fieldname": "warehouse_and_reference", "fieldtype": "Section Break", "label": "Warehouse and Reference"}, {"fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Warehouse", "oldfieldname": "warehouse", "oldfieldtype": "Link", "options": "Warehouse", "print_hide": 1, "print_width": "100px", "width": "100px"}, {"depends_on": "eval:parent.is_internal_customer || doc.target_warehouse", "fieldname": "target_warehouse", "fieldtype": "Link", "hidden": 1, "ignore_user_permissions": 1, "label": "Target Warehouse", "no_copy": 1, "options": "Warehouse", "print_hide": 1}, {"depends_on": "eval:!doc.__islocal", "fieldname": "quality_inspection", "fieldtype": "Link", "label": "Quality Inspection", "no_copy": 1, "options": "Quality Inspection", "print_hide": 1}, {"fieldname": "section_break_40", "fieldtype": "Section Break", "label": "Serial and Batch No"}, {"allow_on_submit": 1, "fieldname": "actual_qty", "fieldtype": "Float", "label": "Available Qty at From Warehouse", "no_copy": 1, "oldfieldname": "actual_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"allow_on_submit": 1, "depends_on": "batch_no", "fieldname": "actual_batch_qty", "fieldtype": "Float", "label": "Available Batch Qty at From Warehouse", "no_copy": 1, "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "item_group", "fieldtype": "Link", "hidden": 1, "label": "Item Group", "oldfieldname": "item_group", "oldfieldtype": "Link", "options": "Item Group", "print_hide": 1, "read_only": 1}, {"fieldname": "brand", "fieldtype": "Link", "hidden": 1, "label": "Brand Name", "oldfieldname": "brand", "oldfieldtype": "Link", "options": "Brand", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "item_tax_rate", "fieldtype": "Small Text", "hidden": 1, "label": "Item Tax Rate", "oldfieldname": "item_tax_rate", "oldfieldtype": "Small Text", "print_hide": 1, "read_only": 1}, {"fieldname": "col_break4", "fieldtype": "Column Break"}, {"fieldname": "expense_account", "fieldtype": "Link", "label": "Expense Account", "no_copy": 1, "options": "Account", "print_hide": 1, "width": "120px"}, {"fieldname": "item_tax_template", "fieldtype": "Link", "label": "Item Tax Template", "options": "Item Tax Template", "print_hide": 1}, {"default": ":Company", "fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "no_copy": 1, "options": "Cost Center", "print_hide": 1, "width": "120px"}, {"default": "0", "fieldname": "allow_zero_valuation_rate", "fieldtype": "Check", "label": "Allow Zero Valuation Rate", "no_copy": 1, "print_hide": 1}, {"fieldname": "against_sales_order", "fieldtype": "Link", "label": "Against Sales Order", "no_copy": 1, "options": "Sales Order", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "against_sales_invoice", "fieldtype": "Link", "label": "Against Sales Invoice", "no_copy": 1, "options": "Sales Invoice", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "so_detail", "fieldtype": "Data", "hidden": 1, "label": "Against Sales Order Item", "no_copy": 1, "oldfieldname": "prevdoc_detail_docname", "oldfieldtype": "Data", "print_hide": 1, "print_width": "150px", "read_only": 1, "search_index": 1, "width": "150px"}, {"fieldname": "si_detail", "fieldtype": "Data", "hidden": 1, "label": "Against Sales Invoice Item", "no_copy": 1, "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "installed_qty", "fieldtype": "Float", "label": "Installed Qty", "no_copy": 1, "oldfieldname": "installed_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "billed_amt", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON>", "no_copy": 1, "options": "currency", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "page_break", "fieldtype": "Check", "label": "Page Break", "oldfieldname": "page_break", "oldfieldtype": "Check", "print_hide": 1}, {"fieldname": "accounting_details_section", "fieldtype": "Section Break", "label": "Accounting Details"}, {"fieldname": "section_break_72", "fieldtype": "Section Break"}, {"collapsible": 1, "fieldname": "image_section", "fieldtype": "Section Break", "label": "Image"}, {"fieldname": "column_break_71", "fieldtype": "Column Break"}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project"}, {"fieldname": "dn_detail", "fieldtype": "Data", "hidden": 1, "label": "Against Delivery Note Item", "no_copy": 1, "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "stock_qty_sec_break", "fieldtype": "Section Break"}, {"fieldname": "stock_qty_col_break", "fieldtype": "Column Break"}, {"depends_on": "returned_qty", "fieldname": "returned_qty", "fieldtype": "Float", "label": "Returned <PERSON><PERSON> in Stock UOM", "no_copy": 1, "read_only": 1}, {"fieldname": "incoming_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Incoming Rate", "no_copy": 1, "precision": "6", "print_hide": 1, "read_only": 1}, {"depends_on": "eval: doc.uom != doc.stock_uom", "fieldname": "stock_uom_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate of Stock UOM", "no_copy": 1, "options": "currency", "read_only": 1}, {"default": "0", "fetch_from": "item_code.grant_commission", "fieldname": "grant_commission", "fieldtype": "Check", "label": "Grant Commission", "read_only": 1}, {"fieldname": "pick_list_item", "fieldtype": "Data", "hidden": 1, "label": "Pick List Item", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"collapsible": 1, "depends_on": "eval:parent.is_internal_customer == 1", "fieldname": "internal_transfer_section", "fieldtype": "Section Break", "label": "Internal Transfer"}, {"fieldname": "purchase_order", "fieldtype": "Link", "label": "Purchase Order", "options": "Purchase Order", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_82", "fieldtype": "Column Break"}, {"fieldname": "purchase_order_item", "fieldtype": "Data", "label": "Purchase Order Item", "print_hide": 1, "read_only": 1}, {"default": "0", "depends_on": "barcode", "fieldname": "has_item_scanned", "fieldtype": "Check", "label": "<PERSON> <PERSON><PERSON>", "read_only": 1}, {"fieldname": "material_request", "fieldtype": "Link", "label": "Material Request", "options": "Material Request"}, {"fieldname": "material_request_item", "fieldtype": "Data", "label": "Material Request Item"}, {"fieldname": "column_break_atna", "fieldtype": "Column Break"}, {"depends_on": "eval: parent.is_internal_customer", "fieldname": "received_qty", "fieldtype": "Float", "label": "Received Qty", "no_copy": 1, "print_hide": 1, "read_only": 1, "report_hide": 1}, {"default": "0", "depends_on": "eval: doc.packed_qty", "fieldname": "packed_qty", "fieldtype": "Float", "label": "Packed Qty", "no_copy": 1, "non_negative": 1, "read_only": 1}, {"depends_on": "eval:doc.use_serial_batch_fields === 0 || doc.docstatus === 1", "fieldname": "serial_and_batch_bundle", "fieldtype": "Link", "label": "Serial and Batch Bundle", "no_copy": 1, "options": "Serial and Batch Bundle", "print_hide": 1, "search_index": 1}, {"depends_on": "eval:doc.use_serial_batch_fields === 0 || doc.docstatus === 1", "fieldname": "pick_serial_and_batch", "fieldtype": "<PERSON><PERSON>", "label": "Pick Serial / Batch No"}, {"collapsible": 1, "fieldname": "available_qty_section", "fieldtype": "Section Break", "label": "Available Qty"}, {"fieldname": "column_break_eaoe", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "serial_no", "fieldtype": "Text", "label": "Serial No"}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "batch_no", "fieldtype": "Link", "label": "Batch No", "options": "<PERSON><PERSON>", "search_index": 1}, {"default": "0", "fieldname": "use_serial_batch_fields", "fieldtype": "Check", "label": "Use Serial No / Batch Fields"}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "section_break_qyjv", "fieldtype": "Section Break"}, {"fieldname": "column_break_rxvc", "fieldtype": "Column Break"}], "idx": 1, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-02-04 14:10:31.750340", "modified_by": "Administrator", "module": "Stock", "name": "Delivery Note Item", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}