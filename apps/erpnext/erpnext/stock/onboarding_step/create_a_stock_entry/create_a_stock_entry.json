{"action": "Create Entry", "action_label": "Create a Material Transfer Entry", "creation": "2020-05-15 03:20:16.277043", "description": "# Manage Stock Movements\nStock entry allows you to register the movement of stock for various purposes like transfer, received, issues, repacked, etc. To address issues related to theft and pilferages,  you can always ensure that the movement of goods happens against a document reference Stock Entry in ERPNext.\n\nLet’s get a quick walk-through on the various scenarios covered in Stock Entry by watching [*this video*](https://www.youtube.com/watch?v=Njt107hlY3I).", "docstatus": 0, "doctype": "Onboarding Step", "idx": 0, "is_complete": 0, "is_single": 0, "is_skipped": 0, "modified": "2023-05-29 14:39:04.066547", "modified_by": "Administrator", "name": "Create a Stock Entry", "owner": "Administrator", "reference_document": "Stock Entry", "show_form_tour": 1, "show_full_form": 1, "title": "Manage Stock Movements", "validate_action": 1}