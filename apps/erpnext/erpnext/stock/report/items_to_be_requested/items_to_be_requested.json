{"add_total_row": 0, "apply_user_permissions": 1, "creation": "2013-08-20 15:08:10", "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 3, "is_standard": "Yes", "modified": "2017-02-24 20:06:00.761719", "modified_by": "Administrator", "module": "Stock", "name": "Items To Be Requested", "owner": "Administrator", "query": "SELECT\n    tabBin.item_code as \"Item:Link/Item:120\",\n    tabBin.warehouse as \"Warehouse:Link/Warehouse:120\",\n    tabBin.actual_qty as \"Actual:Float:90\",\n    tabBin.indented_qty as \"Requested:Float:90\",\n    tabBin.reserved_qty as \"Reserved:Float:90\",\n    tabBin.ordered_qty as \"Ordered:Float:90\",\n    tabBin.projected_qty as \"Projected:Float:90\"\nFROM\n    tabBin, tabItem\nWHERE\n    tabBin.item_code = tabItem.name\n   AND tabBin.projected_qty < 0\nORDER BY\n    tabBin.projected_qty ASC", "ref_doctype": "Material Request", "report_name": "Items To Be Requested", "report_type": "Query Report", "roles": [{"role": "Purchase Manager"}, {"role": "Stock Manager"}, {"role": "Stock User"}, {"role": "Purchase User"}]}