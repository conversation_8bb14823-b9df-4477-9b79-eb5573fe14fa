{"add_total_row": 0, "creation": "2013-08-20 13:43:30", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 3, "is_standard": "Yes", "json": "{\"add_total_row\": 0, \"sort_by\": \"Bin.projected_qty\", \"sort_order\": \"asc\", \"sort_by_next\": \"\", \"filters\": [[\"Bin\", \"projected_qty\", \"<\", \"0\"]], \"sort_order_next\": \"desc\", \"columns\": [[\"warehouse\", \"Bin\"], [\"item_code\", \"Bin\"], [\"actual_qty\", \"Bin\"], [\"ordered_qty\", \"Bin\"], [\"planned_qty\", \"Bin\"], [\"reserved_qty\", \"Bin\"], [\"projected_qty\", \"Bin\"]]}", "modified": "2020-05-14 12:32:07.158991", "modified_by": "Administrator", "module": "Stock", "name": "Item Shortage Report", "owner": "Administrator", "prepared_report": 0, "query": "", "ref_doctype": "Bin", "report_name": "Item Shortage Report", "report_type": "Script Report", "roles": [{"role": "Sales User"}, {"role": "Purchase User"}, {"role": "Stock User"}]}