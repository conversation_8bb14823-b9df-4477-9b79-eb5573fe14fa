{"add_total_row": 1, "apply_user_permissions": 1, "creation": "2013-05-13 16:23:05", "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 3, "is_standard": "Yes", "modified": "2017-02-24 20:07:57.880462", "modified_by": "Administrator", "module": "Stock", "name": "Requested Items To Be Transferred", "owner": "Administrator", "query": "select \n    mr.name as \"Material Request:Link/Material Request:120\",\n\tmr.transaction_date as \"Date:Date:100\",\n\tmr_item.item_code as \"Item Code:Link/Item:120\",\n\tmr_item.qty as \"Qty:Float:100\",\n\tmr_item.ordered_qty as \"Transferred Qty:Float:100\", \n\t(mr_item.qty - ifnull(mr_item.ordered_qty, 0)) as \"Qty to Transfer:Float:100\",\n\tmr_item.item_name as \"Item Name::150\",\n\tmr_item.description as \"Description::200\",\n\tmr.company as \"Company:Link/Company:\"\nfrom\n\t`tabMaterial Request` mr, `tabMaterial Request Item` mr_item\nwhere\n\tmr_item.parent = mr.name\n\tand mr.material_request_type in (\"Material Transfer\", \"Material Issue\")\n\tand mr.docstatus = 1\n\tand mr.status != \"Stopped\"\n\tand ifnull(mr_item.ordered_qty, 0) < ifnull(mr_item.qty, 0)\norder by mr.transaction_date asc", "ref_doctype": "Stock Entry", "report_name": "Requested Items To Be Transferred", "report_type": "Query Report", "roles": [{"role": "Stock User"}, {"role": "Manufacturing User"}, {"role": "Manufacturing Manager"}, {"role": "Stock Manager"}]}