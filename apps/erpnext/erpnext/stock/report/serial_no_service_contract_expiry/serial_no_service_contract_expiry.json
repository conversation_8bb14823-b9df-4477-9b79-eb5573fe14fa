{"add_total_row": 0, "apply_user_permissions": 1, "creation": "2013-01-14 10:52:58", "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 3, "is_standard": "Yes", "json": "{\"add_total_row\": 0, \"sort_by\": \"Serial No.modified\", \"sort_order\": \"desc\", \"sort_by_next\": null, \"filters\": [[\"Serial No\", \"delivery_document_type\", \"in\", [\"Delivery Note\", \"Sales Invoice\"]], [\"Serial No\", \"warehouse\", \"=\", \"\"]], \"sort_order_next\": \"desc\", \"columns\": [[\"name\", \"Serial No\"], [\"item_code\", \"Serial No\"], [\"amc_expiry_date\", \"Serial No\"], [\"maintenance_status\", \"Serial No\"], [\"delivery_document_no\", \"Serial No\"], [\"customer\", \"Serial No\"], [\"customer_name\", \"Serial No\"], [\"item_name\", \"Serial No\"], [\"description\", \"Serial No\"], [\"item_group\", \"Serial No\"], [\"brand\", \"Serial No\"]]}", "modified": "2017-02-24 20:02:00.706889", "modified_by": "Administrator", "module": "Stock", "name": "Serial No Service Contract Expiry", "owner": "Administrator", "ref_doctype": "Serial No", "report_name": "Serial No Service Contract Expiry", "report_type": "Report Builder", "roles": [{"role": "<PERSON>em Manager"}, {"role": "Stock Manager"}, {"role": "Stock User"}]}