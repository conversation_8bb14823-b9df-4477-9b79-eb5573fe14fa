{"add_total_row": 0, "creation": "2013-09-25 10:21:15", "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 3, "is_standard": "Yes", "json": "{\"fields\": [[\"name\", \"Item Price\"], [\"price_list\", \"Item Price\"], [\"currency\", \"Item Price\"], [\"item_code\", \"Item Price\"], [\"brand\", \"Item Price\"], [\"price_list_rate\", \"Item Price\"], [\"docstatus\", \"Item Price\"]], \"order_by\": \"`tabItem Price`.`modified` asc\", \"add_total_row\": 0, \"filters\": []}", "modified": "2018-05-03 12:48:39.802088", "modified_by": "Administrator", "module": "Stock", "name": "Item-wise Price List Rate", "owner": "Administrator", "ref_doctype": "<PERSON><PERSON>", "report_name": "Item-wise Price List Rate", "report_type": "Report Builder", "roles": [{"role": "Sales Master Manager"}, {"role": "Purchase Master Manager"}]}