{"add_total_row": 0, "apply_user_permissions": 1, "creation": "2018-02-17 19:52:16.370979", "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "modified": "2018-02-19 12:03:53.902167", "modified_by": "Administrator", "module": "Stock", "name": "<PERSON><PERSON> (Simple)", "owner": "Administrator", "query": "select a.item_code as \"Item:Link/Item:120\",\n\t  a.item_name as \"Item Name::150\",\n\t  a.item_group as \"Item Group:Link/Item Group:120\",\n\t  a.brand as \"Brand:Link/Brand:120\",\n\t  a.description as \"Description::150\",\n\t  b.warehouse as \"Warehouse:Link/Warehouse:120\",\n\t  b.actual_qty as \"Balance Qty:Float:140\"\n from `tabItem` a left join `tabBin` b\n ON a.item_code = b.item_code", "ref_doctype": "Bin", "report_name": "<PERSON><PERSON> (Simple)", "report_type": "Query Report", "roles": [{"role": "Sales User"}, {"role": "Purchase User"}, {"role": "Stock User"}]}