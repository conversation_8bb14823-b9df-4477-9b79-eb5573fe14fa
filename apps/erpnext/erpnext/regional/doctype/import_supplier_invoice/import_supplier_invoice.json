{"actions": [], "creation": "2019-10-15 12:33:21.845329", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["invoice_series", "company", "item_code", "column_break_5", "supplier_group", "tax_account", "default_buying_price_list", "upload_xml_invoices_section", "zip_file", "import_invoices", "status"], "fields": [{"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>", "reqd": 1}, {"fieldname": "supplier_group", "fieldtype": "Link", "in_list_view": 1, "label": "Supplier Group", "options": "Supplier Group", "reqd": 1}, {"fieldname": "tax_account", "fieldtype": "Link", "in_list_view": 1, "label": "Tax Account", "options": "Account", "reqd": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "zip_file", "fieldtype": "Attach", "label": "Zip File"}, {"description": "Click on Import Invoices button once the zip file has been attached to the document. Any errors related to processing will be shown in the Error Log.", "fieldname": "import_invoices", "fieldtype": "<PERSON><PERSON>", "label": "Import Invoices", "options": "process_file_data"}, {"fieldname": "status", "fieldtype": "Data", "label": "Status", "read_only": 1}, {"fieldname": "invoice_series", "fieldtype": "Select", "label": "Invoice Series", "options": "ACC-PINV-.YYYY.-", "reqd": 1}, {"fieldname": "default_buying_price_list", "fieldtype": "Link", "label": "Default Buying Price List", "options": "Price List", "reqd": 1}, {"depends_on": "eval:!doc.__islocal", "fieldname": "upload_xml_invoices_section", "fieldtype": "Section Break", "label": "Upload XML Invoices"}], "links": [], "modified": "2021-04-24 10:33:12.250687", "modified_by": "Administrator", "module": "Regional", "name": "Import Supplier Invoice", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}