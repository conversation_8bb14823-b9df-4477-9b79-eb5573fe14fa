{"align_labels_right": 0, "creation": "2020-11-09 16:01:26.096002", "css": "", "custom_format": 1, "default_print_language": "en", "disabled": 0, "doc_type": "Supplier", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<div class=\\\"print-heading\\\">\\t\\t\\t\\t<h2>TAX Invoice<br><small>{{ doc.name }}</small>\\t\\t\\t\\t</h2></div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"customer_name\", \"label\": \"Customer Name\"}, {\"print_hide\": 0, \"fieldname\": \"customer_name_in_arabic\", \"label\": \"Customer Name in Arabic\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"posting_date\", \"label\": \"Date\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Address\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"company\", \"label\": \"Company\"}, {\"print_hide\": 0, \"fieldname\": \"company_trn\", \"label\": \"Company TRN\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"company_address_display\", \"label\": \"Company Address\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"visible_columns\": [{\"print_hide\": 0, \"fieldname\": \"item_code\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"description\", \"print_width\": \"200px\"}, {\"print_hide\": 0, \"fieldname\": \"uom\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"tax_code\", \"print_width\": \"\"}], \"print_hide\": 0, \"fieldname\": \"items\", \"label\": \"Items\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"total\", \"label\": \"Total\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"visible_columns\": [{\"print_hide\": 0, \"fieldname\": \"charge_type\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"row_id\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"account_head\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"cost_center\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"description\", \"print_width\": \"300px\"}, {\"print_hide\": 0, \"fieldname\": \"rate\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"tax_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"total\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"tax_amount_after_discount_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"base_tax_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"base_total\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"base_tax_amount_after_discount_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"item_wise_tax_detail\", \"print_width\": \"\"}], \"print_hide\": 0, \"fieldname\": \"taxes\", \"label\": \"Sales Taxes and Charges\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"grand_total\", \"label\": \"Grand Total\"}, {\"print_hide\": 0, \"fieldname\": \"rounded_total\", \"label\": \"Rounded Total\"}, {\"print_hide\": 0, \"fieldname\": \"in_words\", \"align\": \"left\", \"label\": \"In Words\"}]", "html": "<div id=\"copy_a\" style=\"position: relative; top:0cm; width:17cm;height:28.0cm;\">\n  <table>\n    <tbody>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs bbs\" style=\"width:86mm\" colspan=\"4\" ; rowspan=\"3\">PAYER'S name, street address,\n          city or town, state or province, country, ZIP<br>or foreign postal code, and telephone no.<br>\n          {{ company or \"\" }}<br>\n          {{ payer_street_address or \"\" }}\n        </td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:35mm\">1 Rents</td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:25mm\" rowspan=\"2\">OMB No. 1545-0115<br>\n          <yone>{{ fiscal_year[:2] }}</yone>\n          <ytwo>{{ fiscal_year[-2:] }}</ytwo><br>Form 1099-MISC\n        </td>\n        <td class=\"lbs bbs\" style=\"width:38mm\" colspan=\"2\" rowspan=\"2\">Miscellaneous Income</td>\n      </tr>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs bbs\" style=\"width:35mm\">2 Royalties</td>\n      </tr>\n      <tr style=\"height:9mm\">\n        <td class=\"tbs rbs lbs bbs\">3 Other Income<br>{{ payments or \"\" }}</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">4 Federal Income tax withheld</td>\n        <td class=\"tbs lbs bbs\" style=\"width:29mm\" rowspan=\"2\">Copy A<br>For<br>Internal Revenue<br>Service\n          Center<br><br>File with Form 1096</td>\n      </tr>\n      <tr style=\"height:16mm\">\n        <td class=\"tbs rbs lbs bbs\" style=\"width:43mm\">PAYER'S TIN<br>{{ company_tin or \"\" }}</td>\n\n        <td class=\"tbs rbs lbs bbs\" colspan=\"3\">RECIPIENT'S TIN<br><br>{{ tax_id or \"None\" }}</td>\n        <td class=\"tbs rbs lbs bbs\">Fishing boat proceeds</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">6 Medical and health care payments</td>\n      </tr>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"4\">RECIPIENT'S name <br>{{ supplier or \"\" }}</td>\n        <td class=\"tbs rbs lbs bbs\">7 Nonemployee compensation<br>\n        </td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">Substitute payments in lieu of dividends or interest</td>\n        <td class=\"tbs lbs bbs\" rowspan=\"6\">For Privacy Act<br>and Paperwork<br>Reduction Act<br>Notice, see\n          the<br>2018 General<br>Instructions for<br>Certain<br>Information<br>Returns.</td>\n      </tr>\n      <tr style=\"height:6mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"4\" rowspan=\"2\">Street address (including apt. no.)<br>\n          {{ recipient_street_address or \"\" }}\n        </td>\n        <td class=\"tbs rbs lbs bbs\">$___________</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">$___________</td>\n      </tr>\n      <tr style=\"height:7mm\">\n        <td class=\"tbs rbs lbs bbs\" rowspan=\"2\">9 Payer made direct sales of<br>$5,000 or more of consumer\n          products<br>to a buyer<br>(recipient) for resale</td>\n        <td class=\"tbs rbs lbs\" colspan=\"2\">10 Crop insurance proceeds</td>\n      </tr>\n      <tr style=\"height:5mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"4\" rowspan=\"2\">City or town, state or province, country, and ZIP or\n          foreign postal code<br>\n          {{ recipient_city_state or \"\" }}\n        </td>\n        <td style=\"vertical-align:bottom\" class=\" rbs lbs bbs\" colspan=\"2\">$___________</td>\n      </tr>\n      <tr style=\"height:9mm\">\n        <td class=\"tbs rbs lbs bbs\">11</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=2>12</td>\n      </tr>\n      <tr style=\"height:13mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">Account number (see instructions)</td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:16mm\">FACTA filing<br>requirement</td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:14mm\">2nd TIN not.</td>\n        <td class=\"tbs rbs lbs bbs\">13 Excess golden parachute payments<br>$___________</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">14 Gross proceeds paid to an<br>attorney<br>$___________</td>\n      </tr>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs \">15a Section 409A deferrals</td>\n        <td class=\"tbs rbs lbs \" colspan=\"3\">15b Section 409 income</td>\n        <td class=\"tbs rbs lbs \">16 State tax withheld</td>\n        <td class=\"tbs rbs lbs \" colspan=\"2\">17 State/Payer's state no.</td>\n        <td class=\"tbs lbs\">18 State income</td>\n      </tr>\n      <tr>\n        <td class=\"lbs rbs bbs\">$</td>\n        <td class=\"lbs rbs bbs\" colspan=\"3\">$</td>\n        <td class=\"lbs rbs bbs tbd\">$</td>\n        <td class=\"lbs rbs bbs tbd\" colspan=\"2\"></td>\n        <td class=\"lbs bbs tbd\">$</td>\n      </tr>\n\n      <tr style=\"height:8mm\">\n        <td class=\"tbs\" colspan=\"8\">Form 1099-MISC Cat. No. 14425J www.irs.gov/Form1099MISC Department of the\n          Treasury - Internal Revenue Service</td>\n      </tr>\n\n    </tbody>\n  </table>\n</div>\n<div id=\"copy_1\" style=\"position: relative; top:0cm; width:17cm;height:28.0cm;\">\n  <table>\n    <tbody>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs bbs\" style=\"width:86mm\" colspan=\"4\" ; rowspan=\"3\">PAYER'S name, street address,\n          city or town, state or province, country, ZIP<br>or foreign postal code, and telephone no.<br>\n          {{ company or \"\"}}<b r>\n          {{ payer_street_address or \"\" }}\n        </td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:35mm\">1 Rents</td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:25mm\" rowspan=\"2\">OMB No. 1545-0115<br>\n          <yone>{{ fiscal_year[:2] }}</yone>\n          <ytwo>{{ fiscal_year[-2:] }}</ytwo><br>Form 1099-MISC\n        </td>\n        <td class=\"lbs bbs\" style=\"width:38mm\" colspan=\"2\" rowspan=\"2\">Miscellaneous Income</td>\n      </tr>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs bbs\" style=\"width:35mm\">2 Royalties</td>\n      </tr>\n      <tr style=\"height:9mm\">\n        <td class=\"tbs rbs lbs bbs\">3 Other Income<br>\n          {{ payments or \"\" }}\n        </td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">4 Federal Income tax withheld</td>\n        <td class=\"tbs lbs bbs\" style=\"width:29mm\" rowspan=\"2\">Copy 1<br>For State Tax<br>Department</td>\n      </tr>\n      <tr style=\"height:16mm\">\n        <td class=\"tbs rbs lbs bbs\" style=\"width:43mm\">PAYER'S TIN<br>\n          {{ company_tin or \"\" }}\n        </td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"3\">RECIPIENT'S TIN<br>\n          {{ tax_id or \"\" }}\n        </td>\n        <td class=\"tbs rbs lbs bbs\">Fishing boat proceeds</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">6 Medical and health care payments</td>\n      </tr>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"4\">RECIPIENT'S name</td>\n        {{ supplier or \"\" }}\n        <td class=\"tbs rbs lbs bbs\">7 Nonemployee compensation<br>\n        </td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">Substitute payments in lieu of dividends or interest</td>\n        <td class=\"tbs lbs bbs\" rowspan=\"6\"></td>\n      </tr>\n      <tr style=\"height:6mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"4\" rowspan=\"2\">Street address (including apt. no.)<br>\n          {{ recipient_street_address or \"\" }}\n        </td>\n        <td class=\"tbs rbs lbs bbs\">$___________</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">$___________</td>\n      </tr>\n      <tr style=\"height:7mm\">\n        <td class=\"tbs rbs lbs bbs\" rowspan=\"2\">9 Payer made direct sales of<br>$5,000 or more of consumer\n          products<br>to a buyer<br>(recipient) for resale</td>\n        <td class=\"tbs rbs lbs\" colspan=\"2\">10 Crop insurance proceeds</td>\n      </tr>\n      <tr style=\"height:5mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"4\" rowspan=\"2\">City or town, state or province, country, and ZIP or\n          foreign postal code<br>\n          {{ recipient_city_state or \"\" }}\n        </td>\n        <td style=\"vertical-align:bottom\" class=\" rbs lbs bbs\" colspan=\"2\">$___________</td>\n      </tr>\n      <tr style=\"height:9mm\">\n        <td class=\"tbs rbs lbs bbs\">11</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=2>12</td>\n      </tr>\n      <tr style=\"height:13mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">Account number (see instructions)</td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:16mm\">FACTA filing<br>requirement</td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:14mm\">2nd TIN not.</td>\n        <td class=\"tbs rbs lbs bbs\">13 Excess golden parachute payments<br>$___________</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">14 Gross proceeds paid to an<br>attorney<br>$___________</td>\n      </tr>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs \">15a Section 409A deferrals</td>\n        <td class=\"tbs rbs lbs \" colspan=\"3\">15b Section 409 income</td>\n        <td class=\"tbs rbs lbs \">16 State tax withheld</td>\n        <td class=\"tbs rbs lbs \" colspan=\"2\">17 State/Payer's state no.</td>\n        <td class=\"tbs lbs\">18 State income</td>\n      </tr>\n      <tr>\n        <td class=\"lbs rbs bbs\">$</td>\n        <td class=\"lbs rbs bbs\" colspan=\"3\">$</td>\n        <td class=\"lbs rbs bbs tbd\">$</td>\n        <td class=\"lbs rbs bbs tbd\" colspan=\"2\"></td>\n        <td class=\"lbs bbs tbd\">$</td>\n      </tr>\n\n      <tr style=\"height:8mm\">\n        <td class=\"tbs\" colspan=\"8\">Form 1099-MISC Cat. No. 14425J www.irs.gov/Form1099MISC Department of the\n          Treasury - Internal Revenue Service</td>\n      </tr>\n\n    </tbody>\n  </table>\n</div>\n<style>\n  body {\n    font-family: 'Helvetica', sans-serif;\n    font-size: 5.66pt;\n  }\n\n  yone {\n    font-family: 'Helvetica', sans-serif;\n    font-size: 14pt;\n    color: black;\n    -webkit-text-fill-color: white;\n    /* Will override color (regardless of order) */\n    -webkit-text-stroke-width: 1px;\n    -webkit-text-stroke-color: black;\n  }\n\n  ytwo {\n    font-family: 'Helvetica', sans-serif;\n    font-size: 14pt;\n    color: black;\n    -webkit-text-stroke-width: 1px;\n    -webkit-text-stroke-color: black;\n  }\n\n  table,\n  th,\n  td {\n    font-family: 'Helvetica', sans-serif;\n    font-size: 5.66pt;\n    border: none;\n  }\n\n  .tbs {\n    border-top: 1px solid black;\n  }\n\n  .bbs {\n    border-bottom: 1px solid black;\n  }\n\n  .lbs {\n    border-left: 1px solid black;\n  }\n\n  .rbs {\n    border-right: 1px solid black;\n  }\n\n  .allBorder {\n    border-top: 1px solid black;\n    border-right: 1px solid black;\n    border-left: 1px solid black;\n    border-bottom: 1px solid black;\n  }\n\n  .bottomBorderOnlyDashed {\n    border-bottom: 1px dashed black;\n  }\n\n  .tbd {\n    border-top: 1px dashed black;\n  }\n\n  .address {\n    vertical-align: bottom;\n  }\n</style>", "idx": 0, "line_breaks": 0, "modified": "2021-01-19 07:25:16.333666", "modified_by": "Administrator", "module": "Regional", "name": "IRS 1099 Form", "owner": "Administrator", "print_format_builder": 1, "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "No"}