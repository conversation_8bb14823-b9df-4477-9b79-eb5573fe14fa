{"align_labels_right": 0, "creation": "2019-10-16 00:47:08.877767", "custom_format": 0, "disabled": 1, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<div class=\\\"print-heading\\\">\\t\\t\\t\\t<h2>Purchase Invoice<br><small>{{ doc.name }}</small>\\t\\t\\t\\t</h2></div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table border=\\\"1\\\" width=\\\"100%\\\">\\n<head>\\n<th>\\nCedente/prestatore (fornitore)\\n</th>\\n<th>\\nCessionario/committente (cliente)\\n</th>\\n</head>\\n<body>\\n<tr>\\n<td style=\\\"width: 50%;white-space:nowrap;\\\">\\n<p>Identificstivo fiscale ai fini IVA: {{frappe.db.get_value(\\\"Supplier\\\", doc.supplier, \\\"tax_id\\\")}}</p>\\n<p>Codice fiscale: {{frappe.db.get_value(\\\"Supplier\\\", doc.supplier, \\\"fiscal_code\\\")}}</p>\\n<p>Denominazione: {{frappe.db.get_value(\\\"Supplier\\\", doc.supplier, \\\"supplier_name\\\")}}</p>\\n<p>Regime fiscale: {{frappe.db.get_value(\\\"Supplier\\\", doc.supplier, \\\"fiscal_regime\\\")}}</p>\\n<p>Indrizo: {{frappe.db.get_value(\\\"Address\\\", doc.supplier_address, \\\"address_line1\\\")}}</p>\\n<p>Commune: {{frappe.db.get_value(\\\"Address\\\", doc.supplier_address, \\\"city\\\")}} Provincia: {{frappe.db.get_value(\\\"Address\\\", doc.supplier_address, \\\"state_code\\\")}}</p>\\n<p>Cap: {{(frappe.db.get_value(\\\"Address\\\", doc.supplier_address, \\\"pincode\\\")) or \\\" \\\"}} Nazione: {{frappe.db.get_value(\\\"Address\\\", doc.supplier_address, \\\"country\\\")}}</p>\\n</td>\\n<td style=\\\"width: 50%;white-space:nowrap;\\\">\\n<p>Identificstivo fiscale ai fini IVA: {{frappe.db.get_value(\\\"Company\\\", doc.company, \\\"tax_id\\\")}}</p>\\n<p>Codice fiscale: {{frappe.db.get_value(\\\"Company\\\", doc.company, \\\"fiscal_code\\\")}}</p>\\n<p>Denominazione: {{doc.company}}</p>\\n<p>Indrizo: {{frappe.db.get_value(\\\"Address\\\", doc.shipping_address, \\\"address_line1\\\")}}</p>\\n<p>Commune: {{frappe.db.get_value(\\\"Address\\\", doc.shipping_address, \\\"city\\\")}} Provincia: {{frappe.db.get_value(\\\"Address\\\", doc.shipping_address, \\\"state_code\\\")}}</p>\\n<p>Cap: {{frappe.db.get_value(\\\"Address\\\", doc.shipping_address, \\\"pincode\\\")}} Nazione: {{frappe.db.get_value(\\\"Address\\\", doc.shipping_address, \\\"country\\\")}}</p>\\n</td>\\n</td>\\n</tr>\\n</body>\\n</table>\\n<br>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table border=\\\"1\\\" width=\\\"100%\\\">\\n<head>\\n<th>\\nTipologla\\n</th>\\n<th>\\nArt. 73\\n</th>\\n<th>\\nNumero documento\\n</th>\\n<th>\\nData documento\\n</th>\\n<th>\\nCodice destinatario\\n</th>\\n</head>\\n<body>\\n<tr>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{doc.document_type or \\\" \\\"}}\\n</td>\\n<td style=\\\"width: 10%;white-space:nowrap;\\\">\\n{{\\\" \\\"}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{doc.bill_no or \\\" \\\"}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{doc.get_formatted(\\\"bill_date\\\") or \\\" \\\"}}\\n</td>\\n<td style=\\\"width: 30%;white-space:nowrap;\\\">\\n{{doc.destination_code or \\\" \\\"}}\\n</td>\\n</tr>\\n</body>\\n</table>\\n<br>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table border=\\\"1\\\" width=\\\"100%\\\">\\n<head>\\n<th>\\nDescrizione\\n</th>\\n<th>\\nQuantita\\n</th>\\n<th>\\nPrezzo unitario\\n</th>\\n<th>\\nUM\\n</th>\\n<th>\\n%IVA\\n</th>\\n<th>\\nPrezzo totale\\n</th>\\n</head>\\n\\n<body>\\n{%- for row in doc.items -%}\\n<tr>\\n<td style=\\\"width: 30%;\\\">\\n{{row.description or \\\" \\\"}}\\n</td>\\n<td style=\\\"width: 15%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"qty\\\", doc)}}\\n</td>\\n<td style=\\\"width: 15%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"rate\\\", doc)}}\\n</td>\\n<td style=\\\"width: 5%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"uom\\\", doc)}}\\n</td>\\n<td style=\\\"width: 15%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"tax_rate\\\", doc)}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"amount\\\", doc)}}\\n</td>\\n{%- endfor -%}\\n</body>\\n</table>\\n<br>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table border=\\\"1\\\" width=\\\"100%\\\">\\n<head>\\n<th>\\nesigibilita immediata / riferimenti normativi\\n</th>\\n<th>\\n%IVA\\n</th>\\n<th>\\nSpese accessorie\\n</th>\\n<th>\\nArr.\\n</th>\\n<th>\\nTotale imponibile\\n</th>\\n<th>\\nTotale Imposta\\n</th>\\n</head>\\n\\n<body>\\n{%- for row in doc.taxes -%}\\n<tr>\\n<td style=\\\"width: 30%;white-space:nowrap;\\\">\\n{% if 'None' in row.description %}\\n    {{ \\\" \\\" }}\\n{% else %}\\n{{row.description}}\\n{% endif %}\\n</td>\\n<td style=\\\"width: 10%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"tax_rate\\\", doc)}}\\n</td>\\n<td style=\\\"width: 10%;white-space:nowrap;\\\">\\n{{\\\"0,00\\\"}}\\n</td>\\n<td style=\\\"width: 10%;white-space:nowrap;\\\">\\n{{\\\" \\\"}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{doc.get_formatted(\\\"base_net_total\\\")}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"tax_amount\\\", doc)}}\\n</td>\\n{%- endfor -%}\\n</body>\\n</table>\\n<br>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table border=\\\"1\\\" width=\\\"100%\\\">\\n<head>\\n<th>\\nImporto bolio\\n</th>\\n<th>\\nSconto/Magglorazione\\n</th>\\n<th>\\nArr.\\n</th>\\n<th>\\nTotale documento\\n</th>\\n</head>\\n\\n<body>\\n<tr>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{\\\" \\\"}}\\n</td>\\n<td style=\\\"width: 30%;white-space:nowrap;\\\">\\n{{\\\" \\\"}}\\n</td>\\n<td style=\\\"width: 10%;white-space:nowrap;\\\">\\n{{\\\" \\\"}}\\n</td>\\n<td style=\\\"width: 40%;white-space:nowrap;\\\">\\n{{doc.get_formatted(\\\"base_grand_total\\\")}}\\n</td>\\n</body>\\n</table>\\n<br>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table border=\\\"1\\\" width=\\\"100%\\\">\\n<head>\\n<th>\\nModalita pagamento\\n</th>\\n<th>\\nIBAN\\n</th>\\n<th>\\nInstituto\\n</th>\\n<th>\\nData scadenza\\n</th>\\n<th>\\nImporto\\n</th>\\n</head>\\n\\n<body>\\n{%- for row in doc.payment_schedule -%}\\n<tr>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"mode_of_payment_code\\\",doc)}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"bank_account_iban\\\",doc)}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{\\\" \\\"}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"due_date\\\",doc)}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"payment_amount\\\",doc)}}\\n</td>\\n{%- endfor -%}\\n</body>\\n</table>\"}]", "idx": 0, "line_breaks": 0, "modified": "2019-10-16 23:32:37.709344", "modified_by": "Administrator", "module": "Regional", "name": "Purchase eInvoice", "owner": "Administrator", "print_format_builder": 1, "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}