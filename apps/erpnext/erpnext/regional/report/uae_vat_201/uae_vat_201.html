{%
	var report_columns = report.get_columns_for_print();
	report_columns = report_columns.filter(col => !col.hidden);
%}
<style>
	.print-format {
		padding: 10mm;
		font-size: 8.0pt !important;
		font-family: Tahoma, sans-serif;
	}
</style>

<h1  style="margin-top:0; text-align: center;">{%= __(report.report_name) %}</h1>

<h3  style="margin-top:0; font-weight:500">{%= __("VAT on Sales and All Other Outputs") %}</h2>

<table class="table table-bordered">

    <thead>
        <th style="width: 13">{%= report_columns[0].label %}</th>
        <th style="width: {%= 100 - (report_columns.length - 1) * 13%}%">{%= report_columns[1].label %}</th>

        {% for (let i=2; i<report_columns.length; i++) { %}
            <th style="width: 13">{%= report_columns[i].label %}</th>
        {% } %}
    </thead>

    <tbody>
        {% for (let j=1; j<12; j++)  { %}
        {%
            var row = data[j];
        %}
        <tr >
            {% for (let i=0; i<report_columns.length; i++) { %}
                <td >
                    {% const fieldname = report_columns[i].fieldname; %}
                    {% if (!is_null(row[fieldname])) { %}
                        {%= frappe.format(row[fieldname], report_columns[i], {}, row) %}
                    {% } %}
                </td>
            {% } %}
        </tr>
        {% } %}
    </tbody>
</table>

<h3 style="margin-top:0; font-weight:500">{%= __("VAT on Expenses and All Other Inputs") %}</h2>

<table  class="table table-bordered">
    <thead>
        <th style="width: 13">{%= report_columns[0].label %}</th>
        <th style="width: {%= 100 - (report_columns.length - 1) * 13%}%">{%= report_columns[1].label %}</th>

        {% for (let i=2; i<report_columns.length; i++) { %}
            <th style="width: 13">{%= report_columns[i].label %}</th>
        {% } %}
    </thead>

    <tbody>
        {% for (let j=14; j<data.length; j++)  { %}
        {%
            var row = data[j];
        %}
        <tr >
            {% for (let i=0; i<report_columns.length; i++) { %}
                <td >
                    {% const fieldname = report_columns[i].fieldname; %}
                    {% if (!is_null(row[fieldname])) { %}
                        {%= frappe.format(row[fieldname], report_columns[i], {}, row) %}
                    {% } %}
                </td>
            {% } %}
        </tr>
        {% } %}
    </tbody>

</table>
