{"creation": "2019-10-07 18:49:00.737806", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["company", "asset", "source_location", "from_employee", "column_break_2", "asset_name", "target_location", "to_employee"], "fields": [{"fieldname": "asset", "fieldtype": "Link", "in_list_view": 1, "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>", "reqd": 1}, {"fetch_from": "asset.asset_name", "fieldname": "asset_name", "fieldtype": "Data", "label": "Asset Name", "read_only": 1}, {"fieldname": "source_location", "fieldtype": "Link", "in_list_view": 1, "label": "Source Location", "options": "Location"}, {"fieldname": "target_location", "fieldtype": "Link", "in_list_view": 1, "label": "Target Location", "options": "Location"}, {"fieldname": "from_employee", "fieldtype": "Link", "ignore_user_permissions": 1, "in_list_view": 1, "label": "From Employee", "options": "Employee"}, {"fieldname": "to_employee", "fieldtype": "Link", "ignore_user_permissions": 1, "in_list_view": 1, "label": "To Employee", "options": "Employee"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "company", "fieldtype": "Link", "hidden": 1, "label": "Company", "options": "Company", "read_only": 1}], "istable": 1, "modified": "2019-10-09 15:59:08.265141", "modified_by": "Administrator", "module": "Assets", "name": "Asset Movement Item", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}