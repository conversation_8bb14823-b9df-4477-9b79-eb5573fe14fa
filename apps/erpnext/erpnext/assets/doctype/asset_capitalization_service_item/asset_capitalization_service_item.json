{"actions": [], "creation": "2021-09-06 13:32:08.642060", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "item_name", "column_break_3", "expense_account", "section_break_6", "qty", "uom", "column_break_9", "rate", "amount", "accounting_dimensions_section", "cost_center", "dimension_col_break"], "fields": [{"bold": 1, "fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>"}, {"fetch_from": "item_code.item_name", "fieldname": "item_name", "fieldtype": "Data", "label": "Item Name", "read_only": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "expense_account", "fieldtype": "Link", "in_list_view": 1, "label": "Expense Account", "options": "Account", "reqd": 1}, {"fieldname": "section_break_6", "fieldtype": "Section Break", "label": "Qty and Rate"}, {"columns": 1, "default": "1", "fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Qty", "non_negative": 1}, {"columns": 1, "fetch_from": "item_code.stock_uom", "fieldname": "uom", "fieldtype": "Link", "in_list_view": 1, "label": "UOM", "options": "UOM"}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "options": "Company:company:default_currency"}, {"default": "0", "fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "options": "Company:company:default_currency", "read_only": 1}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-03-05 11:23:40.766844", "modified_by": "Administrator", "module": "Assets", "name": "Asset Capitalization Service Item", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}