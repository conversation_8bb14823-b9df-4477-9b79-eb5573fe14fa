{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:location_name", "creation": "2018-05-07 12:49:22.595974", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["location_name", "parent_location", "cb_details", "is_container", "is_group", "sb_location_details", "latitude", "longitude", "cb_latlong", "area", "area_uom", "sb_geolocation", "location", "tree_details", "lft", "rgt", "old_parent"], "fields": [{"fieldname": "location_name", "fieldtype": "Data", "in_list_view": 1, "label": "Location Name", "no_copy": 1, "reqd": 1, "unique": 1}, {"fieldname": "parent_location", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Parent Location", "options": "Location", "search_index": 1}, {"fieldname": "cb_details", "fieldtype": "Column Break"}, {"default": "0", "description": "Check if it is a hydroponic unit", "fieldname": "is_container", "fieldtype": "Check", "label": "Is Container"}, {"bold": 1, "default": "0", "fieldname": "is_group", "fieldtype": "Check", "in_list_view": 1, "label": "Is Group"}, {"fieldname": "sb_location_details", "fieldtype": "Section Break", "label": "Location Details"}, {"fetch_from": "parent_location.latitude", "fieldname": "latitude", "fieldtype": "Float", "label": "Latitude"}, {"fetch_from": "parent_location.longitude", "fieldname": "longitude", "fieldtype": "Float", "label": "Longitude"}, {"fieldname": "cb_latlong", "fieldtype": "Column Break"}, {"fieldname": "area", "fieldtype": "Float", "label": "Area", "read_only": 1}, {"depends_on": "eval:doc.area", "fieldname": "area_uom", "fieldtype": "Link", "label": "Area UOM", "options": "UOM"}, {"fieldname": "sb_geolocation", "fieldtype": "Section Break"}, {"fieldname": "location", "fieldtype": "Geolocation", "label": "Location"}, {"fieldname": "tree_details", "fieldtype": "Section Break", "hidden": 1, "label": "Tree Details"}, {"fieldname": "lft", "fieldtype": "Int", "hidden": 1, "label": "lft", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "rgt", "fieldtype": "Int", "hidden": 1, "label": "rgt", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "old_parent", "fieldtype": "Data", "hidden": 1, "label": "Old Parent", "no_copy": 1, "print_hide": 1, "read_only": 1}], "is_tree": 1, "links": [], "modified": "2023-08-29 12:49:33.290527", "modified_by": "Administrator", "module": "Assets", "name": "Location", "naming_rule": "By fieldname", "nsm_parent_field": "parent_location", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Agriculture Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Agriculture User", "share": 1, "write": 1}], "quick_entry": 1, "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}