{"actions": [], "creation": "2018-05-11 00:22:43.695151", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["company", "asset", "asset_category", "column_break_4", "date", "finance_book", "amended_from", "value_details_section", "current_asset_value", "new_asset_value", "column_break_11", "difference_amount", "journal_entry", "accounting_dimensions_section", "cost_center", "dimension_col_break"], "fields": [{"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company"}, {"fieldname": "asset", "fieldtype": "Link", "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>", "reqd": 1}, {"fetch_from": "asset.asset_category", "fieldname": "asset_category", "fieldtype": "Read Only", "label": "Asset Category"}, {"fieldname": "finance_book", "fieldtype": "Link", "in_list_view": 1, "label": "Finance Book", "options": "Finance Book"}, {"fieldname": "journal_entry", "fieldtype": "Link", "label": "Journal Entry", "options": "Journal Entry", "read_only": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "date", "fieldtype": "Date", "label": "Date", "reqd": 1}, {"fieldname": "current_asset_value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Current Asset Value", "read_only": 1, "reqd": 1}, {"fieldname": "new_asset_value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "New Asset Value", "reqd": 1}, {"fieldname": "difference_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Difference Amount", "no_copy": 1, "read_only": 1}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Asset Value Adjustment", "print_hide": 1, "read_only": 1}, {"fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"fieldname": "value_details_section", "fieldtype": "Section Break", "label": "Value Details"}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2021-01-22 14:10:23.085181", "modified_by": "Administrator", "module": "Assets", "name": "Asset Value Adjustment", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "submit": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "title_field": "asset", "track_changes": 1}