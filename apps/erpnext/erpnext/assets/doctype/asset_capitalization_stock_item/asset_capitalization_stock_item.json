{"actions": [], "creation": "2021-09-05 15:23:23.492310", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "item_name", "column_break_3", "warehouse", "section_break_6", "stock_qty", "stock_uom", "actual_qty", "column_break_9", "valuation_rate", "amount", "batch_and_serial_no_section", "serial_and_batch_bundle", "use_serial_batch_fields", "column_break_13", "section_break_bfqc", "serial_no", "column_break_mbuv", "batch_no", "accounting_dimensions_section", "cost_center", "dimension_col_break"], "fields": [{"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Warehouse", "options": "Warehouse", "reqd": 1}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "batch_no", "fieldtype": "Link", "label": "Batch No", "no_copy": 1, "options": "<PERSON><PERSON>", "print_hide": 1}, {"fieldname": "section_break_6", "fieldtype": "Section Break", "label": "Qty and Rate"}, {"columns": 1, "fieldname": "stock_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Qty", "non_negative": 1}, {"columns": 1, "fetch_from": "item_code.stock_uom", "fieldname": "stock_uom", "fieldtype": "Link", "in_list_view": 1, "label": "Stock UOM", "options": "UOM", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "valuation_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Valuation Rate", "options": "Company:company:default_currency", "read_only": 1}, {"default": "0", "fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "batch_and_serial_no_section", "fieldtype": "Section Break", "label": "Batch and Serial No"}, {"fieldname": "column_break_13", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "serial_no", "fieldtype": "Text", "hidden": 1, "label": "Serial No", "print_hide": 1}, {"fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>", "reqd": 1}, {"fetch_from": "item_code.item_name", "fieldname": "item_name", "fieldtype": "Data", "label": "Item Name", "read_only": 1}, {"fieldname": "actual_qty", "fieldtype": "Float", "label": "Actual Qty in Warehouse", "no_copy": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.use_serial_batch_fields === 0 || doc.docstatus === 1", "fieldname": "serial_and_batch_bundle", "fieldtype": "Link", "label": "Serial and Batch Bundle", "no_copy": 1, "options": "Serial and Batch Bundle", "print_hide": 1}, {"default": "0", "fieldname": "use_serial_batch_fields", "fieldtype": "Check", "label": "Use Serial No / Batch Fields"}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "section_break_bfqc", "fieldtype": "Section Break"}, {"fieldname": "column_break_mbuv", "fieldtype": "Column Break"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-03-05 11:22:57.346889", "modified_by": "Administrator", "module": "Assets", "name": "Asset Capitalization Stock Item", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}