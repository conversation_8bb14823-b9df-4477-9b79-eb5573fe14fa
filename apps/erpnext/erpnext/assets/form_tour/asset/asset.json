{"creation": "2021-08-24 16:55:10.923434", "docstatus": 0, "doctype": "Form Tour", "idx": 0, "is_standard": 1, "modified": "2021-08-24 16:55:10.923434", "modified_by": "Administrator", "module": "Assets", "name": "<PERSON><PERSON>", "owner": "Administrator", "reference_doctype": "<PERSON><PERSON>", "save_on_complete": 0, "steps": [{"description": "Select Naming Series based on which Asset ID will be generated", "field": "", "fieldname": "naming_series", "fieldtype": "Select", "has_next_condition": 0, "is_table_field": 0, "label": "Naming Series", "parent_field": "", "position": "Bottom", "title": "Naming Series"}, {"description": "Select an Asset Item", "field": "", "fieldname": "item_code", "fieldtype": "Link", "has_next_condition": 0, "is_table_field": 0, "label": "Item Code", "parent_field": "", "position": "Bottom", "title": "Item Code"}, {"description": "Select a Location", "field": "", "fieldname": "location", "fieldtype": "Link", "has_next_condition": 0, "is_table_field": 0, "label": "Location", "parent_field": "", "position": "Bottom", "title": "Location"}, {"description": "Check Is Existing Asset", "field": "", "fieldname": "is_existing_asset", "fieldtype": "Check", "has_next_condition": 0, "is_table_field": 0, "label": "Is Existing Asset", "parent_field": "", "position": "Bottom", "title": "Is Existing Asset?"}, {"description": "Set Available for use date", "field": "", "fieldname": "available_for_use_date", "fieldtype": "Date", "has_next_condition": 0, "is_table_field": 0, "label": "Available-for-use Date", "parent_field": "", "position": "Bottom", "title": "Available For Use Date"}, {"description": "Set Gross purchase amount", "field": "", "fieldname": "gross_purchase_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "has_next_condition": 0, "is_table_field": 0, "label": "Gross Purchase Amount", "parent_field": "", "position": "Bottom", "title": "Gross Purchase Amount"}, {"description": "Set Purchase Date", "field": "", "fieldname": "purchase_date", "fieldtype": "Date", "has_next_condition": 0, "is_table_field": 0, "label": "Purchase Date", "parent_field": "", "position": "Bottom", "title": "Purchase Date"}, {"description": "Check Calculate Depreciation", "field": "", "fieldname": "calculate_depreciation", "fieldtype": "Check", "has_next_condition": 0, "is_table_field": 0, "label": "Calculate Depreciation", "parent_field": "", "position": "Bottom", "title": "Calculate Depreciation"}, {"description": "Enter depreciation which has already been booked for this asset", "field": "", "fieldname": "opening_accumulated_depreciation", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "has_next_condition": 0, "is_table_field": 0, "label": "Opening Accumulated Depreciation", "parent_field": "", "position": "Bottom", "title": "Accumulated Depreciation"}], "title": "<PERSON><PERSON>"}