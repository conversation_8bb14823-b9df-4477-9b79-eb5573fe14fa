{"actions": [], "autoname": "field:goal", "creation": "2018-10-02 12:17:41.727541", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["goal", "frequency", "cb_00", "procedure", "weekday", "date", "sb_01", "objectives"], "fields": [{"default": "None", "fieldname": "frequency", "fieldtype": "Select", "in_list_view": 1, "label": "Monitoring Frequency", "options": "None\nDaily\nWeekly\nMonthly\nQuarterly"}, {"fieldname": "procedure", "fieldtype": "Link", "in_list_view": 1, "label": "Procedure", "options": "Quality Procedure"}, {"depends_on": "eval:doc.frequency == 'Monthly' || doc.frequency == 'Quarterly';", "fieldname": "date", "fieldtype": "Select", "label": "Date", "options": "1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n27\n28\n29\n30"}, {"depends_on": "eval:doc.frequency == 'Weekly';", "fieldname": "weekday", "fieldtype": "Select", "label": "Weekday", "options": "Monday\nTuesday\nWednesday\nThursday\nFriday\nSaturday"}, {"fieldname": "cb_00", "fieldtype": "Column Break"}, {"fieldname": "sb_01", "fieldtype": "Section Break", "label": "Objectives"}, {"fieldname": "objectives", "fieldtype": "Table", "label": "Objectives", "options": "Quality Goal Objective"}, {"fieldname": "goal", "fieldtype": "Data", "label": "Goal", "reqd": 1, "unique": 1}], "index_web_pages_for_search": 1, "links": [{"group": "Review", "link_doctype": "Quality Review", "link_fieldname": "goal"}], "modified": "2023-08-28 22:33:27.718899", "modified_by": "Administrator", "module": "Quality Management", "name": "Quality Goal", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Desk User", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}