{"actions": [], "autoname": "format:QA-NC-{#####}", "creation": "2020-10-21 14:49:50.350136", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["subject", "procedure", "process_owner", "full_name", "column_break_4", "status", "section_break_4", "details", "corrective_action", "preventive_action"], "fields": [{"fieldname": "subject", "fieldtype": "Data", "in_list_view": 1, "label": "Subject", "reqd": 1}, {"fieldname": "procedure", "fieldtype": "Link", "in_list_view": 1, "label": "Procedure", "options": "Quality Procedure", "reqd": 1}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Open\nResolved\nCancelled", "reqd": 1}, {"fieldname": "section_break_4", "fieldtype": "Section Break"}, {"fieldname": "details", "fieldtype": "Text Editor", "label": "Details"}, {"fetch_from": "procedure.process_owner", "fieldname": "process_owner", "fieldtype": "Data", "label": "Process Owner", "read_only": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fetch_from": "procedure.process_owner_full_name", "fieldname": "full_name", "fieldtype": "Data", "read_only": 1, "label": "Full Name"}, {"fieldname": "corrective_action", "fieldtype": "Text Editor", "label": "Corrective Action"}, {"fieldname": "preventive_action", "fieldtype": "Text Editor", "label": "Preventive Action"}], "index_web_pages_for_search": 1, "links": [], "modified": "2023-07-31 08:10:47.247814", "modified_by": "Administrator", "module": "Quality Management", "name": "Non Conformance", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Employee", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}