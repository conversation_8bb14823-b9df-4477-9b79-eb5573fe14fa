{"actions": [], "autoname": "hash", "creation": "2013-02-22 01:28:05", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "item_name", "column_break_3", "scheduled_date", "actual_date", "section_break_6", "sales_person", "column_break_8", "completion_status", "section_break_10", "serial_no", "item_reference"], "fields": [{"columns": 2, "fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "read_only": 1, "search_index": 1}, {"fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data", "read_only": 1}, {"columns": 2, "fieldname": "scheduled_date", "fieldtype": "Date", "in_list_view": 1, "label": "Scheduled Date", "oldfieldname": "scheduled_date", "oldfieldtype": "Date", "reqd": 1, "search_index": 1}, {"fieldname": "actual_date", "fieldtype": "Date", "in_list_view": 1, "label": "Actual Date", "no_copy": 1, "oldfieldname": "actual_date", "oldfieldtype": "Date", "print_hide": 1, "read_only": 1, "report_hide": 1}, {"allow_on_submit": 1, "columns": 2, "fieldname": "sales_person", "fieldtype": "Link", "in_list_view": 1, "label": "Sales Person", "oldfieldname": "incharge_name", "oldfieldtype": "Link", "options": "Sales Person", "read_only_depends_on": "eval:doc.completion_status != \"Pending\""}, {"fieldname": "serial_no", "fieldtype": "Small Text", "in_list_view": 1, "label": "Serial No", "oldfieldname": "serial_no", "oldfieldtype": "Small Text", "print_width": "160px", "read_only": 1, "width": "160px"}, {"allow_on_submit": 1, "columns": 2, "default": "Pending", "fieldname": "completion_status", "fieldtype": "Select", "in_list_view": 1, "label": "Completion Status", "options": "Pending\nPartially Completed\nFully Completed"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"fieldname": "column_break_8", "fieldtype": "Column Break"}, {"fieldname": "section_break_10", "fieldtype": "Section Break"}, {"fieldname": "item_reference", "fieldtype": "Link", "hidden": 1, "label": "Item Reference", "options": "Maintenance Schedule Item", "read_only": 1}], "idx": 1, "istable": 1, "links": [], "modified": "2021-09-16 21:25:22.506485", "modified_by": "Administrator", "module": "Maintenance", "name": "Maintenance Schedule Detail", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}