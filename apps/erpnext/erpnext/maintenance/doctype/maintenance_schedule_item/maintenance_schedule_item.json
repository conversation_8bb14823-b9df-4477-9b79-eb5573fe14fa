{"actions": [], "autoname": "hash", "creation": "2013-02-22 01:28:05", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "item_name", "description", "column_break_4", "start_date", "end_date", "periodicity", "schedule_details", "no_of_visits", "column_break_10", "sales_person", "reference", "serial_no", "sales_order", "column_break_ugqr", "serial_and_batch_bundle"], "fields": [{"columns": 2, "fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "reqd": 1, "search_index": 1}, {"columns": 1, "fetch_from": "item_code.item_name", "fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "in_list_view": 1, "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data", "read_only": 1}, {"fetch_from": "item_code.description", "fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Data", "print_width": "300px", "read_only": 1, "width": "300px"}, {"fieldname": "schedule_details", "fieldtype": "Section Break"}, {"columns": 2, "fieldname": "start_date", "fieldtype": "Date", "in_list_view": 1, "label": "Start Date", "oldfieldname": "start_date", "oldfieldtype": "Date", "reqd": 1, "search_index": 1}, {"columns": 2, "fieldname": "end_date", "fieldtype": "Date", "label": "End Date", "oldfieldname": "end_date", "oldfieldtype": "Date", "reqd": 1, "search_index": 1}, {"columns": 1, "fieldname": "periodicity", "fieldtype": "Select", "in_list_view": 1, "label": "Periodicity", "oldfieldname": "periodicity", "oldfieldtype": "Select", "options": "\nWeekly\nMonthly\nQuarterly\nHalf Yearly\nYearly\nRandom"}, {"columns": 1, "fieldname": "no_of_visits", "fieldtype": "Int", "in_list_view": 1, "label": "No of Visits", "oldfieldname": "no_of_visits", "oldfieldtype": "Int", "reqd": 1}, {"fieldname": "sales_person", "fieldtype": "Link", "label": "Sales Person", "oldfieldname": "incharge_name", "oldfieldtype": "Link", "options": "Sales Person"}, {"fieldname": "reference", "fieldtype": "Section Break", "label": "Reference"}, {"fieldname": "serial_no", "fieldtype": "Small Text", "label": "Serial No", "oldfieldname": "serial_no", "oldfieldtype": "Small Text", "read_only": 1}, {"fieldname": "sales_order", "fieldtype": "Link", "label": "Sales Order", "no_copy": 1, "oldfieldname": "prevdoc_docname", "oldfieldtype": "Data", "options": "Sales Order", "print_hide": 1, "print_width": "150px", "read_only": 1, "search_index": 1, "width": "150px"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "column_break_10", "fieldtype": "Column Break"}, {"fieldname": "column_break_ugqr", "fieldtype": "Column Break"}, {"fieldname": "serial_and_batch_bundle", "fieldtype": "Link", "label": "Serial and Batch Bundle", "no_copy": 1, "options": "Serial and Batch Bundle", "print_hide": 1}], "idx": 1, "istable": 1, "links": [], "modified": "2023-03-22 18:44:36.816037", "modified_by": "Administrator", "module": "Maintenance", "name": "Maintenance Schedule Item", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}