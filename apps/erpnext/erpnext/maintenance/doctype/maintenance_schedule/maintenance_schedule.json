{"actions": [], "autoname": "naming_series:", "creation": "2013-01-10 16:34:30", "doctype": "DocType", "document_type": "Document", "engine": "InnoDB", "field_order": ["customer_details", "naming_series", "customer", "column_break0", "status", "transaction_date", "items_section", "items", "schedule", "generate_schedule", "schedules", "contact_info", "customer_name", "contact_person", "contact_mobile", "contact_email", "contact_display", "column_break_17", "customer_address", "address_display", "territory", "customer_group", "company", "amended_from"], "fields": [{"fieldname": "customer_details", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "fa fa-user"}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "options": "MAT-MSH-.YYYY.-", "print_hide": 1, "reqd": 1, "set_only_once": 1}, {"fieldname": "customer", "fieldtype": "Link", "in_standard_filter": 1, "label": "Customer", "oldfieldname": "customer", "oldfieldtype": "Link", "options": "Customer", "print_hide": 1, "search_index": 1}, {"fieldname": "column_break0", "fieldtype": "Column Break", "oldfieldtype": "Column Break"}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "in_standard_filter": 1, "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Select", "options": "\nDraft\nSubmitted\nCancelled", "read_only": 1, "reqd": 1}, {"fieldname": "transaction_date", "fieldtype": "Date", "label": "Transaction Date", "oldfieldname": "transaction_date", "oldfieldtype": "Date", "reqd": 1}, {"fieldname": "items_section", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "fa fa-shopping-cart"}, {"fieldname": "items", "fieldtype": "Table", "label": "Items", "oldfieldname": "item_maintenance_detail", "oldfieldtype": "Table", "options": "Maintenance Schedule Item", "reqd": 1}, {"fieldname": "schedule", "fieldtype": "Section Break", "label": "Schedule", "oldfieldtype": "Section Break", "options": "fa fa-time"}, {"fieldname": "generate_schedule", "fieldtype": "<PERSON><PERSON>", "label": "Generate Schedule", "oldfieldtype": "<PERSON><PERSON>"}, {"fieldname": "schedules", "fieldtype": "Table", "label": "Schedules", "oldfieldname": "schedules", "oldfieldtype": "Table", "options": "Maintenance Schedule Detail"}, {"fieldname": "contact_info", "fieldtype": "Section Break", "label": "Contact Info"}, {"bold": 1, "depends_on": "customer", "fieldname": "customer_name", "fieldtype": "Data", "in_global_search": 1, "in_list_view": 1, "label": "Customer Name", "oldfieldname": "customer_name", "oldfieldtype": "Data", "read_only": 1}, {"depends_on": "customer", "fieldname": "contact_person", "fieldtype": "Link", "label": "Contact Person", "options": "Contact", "print_hide": 1}, {"depends_on": "customer", "fieldname": "contact_mobile", "fieldtype": "Data", "hidden": 1, "label": "Mobile No", "options": "Phone", "read_only": 1}, {"depends_on": "customer", "fieldname": "contact_email", "fieldtype": "Data", "hidden": 1, "label": "Contact Email", "options": "Email", "print_hide": 1, "read_only": 1}, {"fieldname": "contact_display", "fieldtype": "Small Text", "hidden": 1, "in_global_search": 1, "label": "Contact", "read_only": 1}, {"fieldname": "column_break_17", "fieldtype": "Column Break"}, {"depends_on": "customer", "fieldname": "customer_address", "fieldtype": "Link", "label": "Customer Address", "options": "Address", "print_hide": 1}, {"fieldname": "address_display", "fieldtype": "Small Text", "hidden": 1, "label": "Address", "read_only": 1}, {"depends_on": "customer", "fieldname": "territory", "fieldtype": "Link", "label": "Territory", "oldfieldname": "territory", "oldfieldtype": "Link", "options": "Territory"}, {"depends_on": "customer", "fieldname": "customer_group", "fieldtype": "Link", "label": "Customer Group", "options": "Customer Group"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "remember_last_selected_value": 1, "reqd": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Maintenance Schedule", "print_hide": 1, "read_only": 1}], "icon": "fa fa-calendar", "idx": 1, "is_submittable": 1, "links": [{"group": "Visits", "link_doctype": "Maintenance Visit", "link_fieldname": "maintenance_schedule"}], "modified": "2023-06-03 16:15:43.958072", "modified_by": "Administrator", "module": "Maintenance", "name": "Maintenance Schedule", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Maintenance Manager", "share": 1, "submit": 1, "write": 1}], "search_fields": "status,customer,customer_name", "sort_field": "modified", "sort_order": "DESC", "states": [], "timeline_field": "customer"}