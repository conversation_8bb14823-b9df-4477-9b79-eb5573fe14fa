{"actions": [], "autoname": "hash", "creation": "2013-02-22 01:28:06", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "item_name", "column_break_3", "service_person", "serial_no", "section_break_6", "description", "work_details", "work_done", "prevdoc_doctype", "prevdoc_docname", "maintenance_schedule_detail"], "fields": [{"fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>"}, {"fetch_from": "item_code.item_name", "fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "in_list_view": 1, "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data", "read_only": 1}, {"fieldname": "serial_no", "fieldtype": "Link", "label": "Serial No", "oldfieldname": "serial_no", "oldfieldtype": "Small Text", "options": "Serial No"}, {"fetch_from": "item_code.description", "fetch_if_empty": 1, "fieldname": "description", "fieldtype": "Text Editor", "in_list_view": 1, "label": "Description", "oldfieldname": "description", "oldfieldtype": "Small Text", "print_width": "300px", "width": "300px"}, {"fieldname": "work_details", "fieldtype": "Section Break"}, {"fieldname": "service_person", "fieldtype": "Link", "in_list_view": 1, "label": "Sales Person", "oldfieldname": "service_person", "oldfieldtype": "Link", "options": "Sales Person", "reqd": 1}, {"fieldname": "work_done", "fieldtype": "Small Text", "in_list_view": 1, "label": "Work Done", "oldfieldname": "work_done", "oldfieldtype": "Small Text", "reqd": 1}, {"fieldname": "prevdoc_doctype", "fieldtype": "Link", "hidden": 1, "label": "Document Type", "options": "DocType"}, {"fieldname": "prevdoc_docname", "fieldtype": "Dynamic Link", "hidden": 1, "label": "Against Document No", "options": "prevdoc_doctype"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"fieldname": "maintenance_schedule_detail", "fieldtype": "Data", "hidden": 1, "label": "Maintenance Schedule Detail", "options": "Maintenance Schedule Detail"}], "idx": 1, "istable": 1, "links": [], "modified": "2024-01-05 21:46:53.239830", "modified_by": "Administrator", "module": "Maintenance", "name": "Maintenance Visit Purpose", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}