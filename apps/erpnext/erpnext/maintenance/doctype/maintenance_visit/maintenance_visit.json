{"actions": [], "autoname": "naming_series:", "creation": "2013-01-10 16:34:31", "doctype": "DocType", "document_type": "Document", "engine": "InnoDB", "field_order": ["customer_details", "column_break0", "naming_series", "customer", "customer_name", "address_display", "contact_display", "contact_mobile", "contact_email", "maintenance_schedule", "maintenance_schedule_detail", "column_break1", "mntc_date", "mntc_time", "maintenance_details", "completion_status", "column_break_14", "maintenance_type", "section_break0", "purposes", "more_info", "customer_feedback", "col_break3", "status", "amended_from", "company", "contact_info_section", "customer_address", "contact_person", "col_break4", "territory", "customer_group"], "fields": [{"fieldname": "customer_details", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "fa fa-user"}, {"fieldname": "column_break0", "fieldtype": "Column Break", "oldfieldtype": "Column Break"}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "options": "MAT-MVS-.YYYY.-", "print_hide": 1, "reqd": 1, "set_only_once": 1}, {"fieldname": "customer", "fieldtype": "Link", "in_global_search": 1, "label": "Customer", "oldfieldname": "customer", "oldfieldtype": "Link", "options": "Customer", "print_hide": 1, "reqd": 1}, {"bold": 1, "fieldname": "customer_name", "fieldtype": "Data", "hidden": 1, "in_global_search": 1, "label": "Customer Name", "read_only": 1}, {"fieldname": "address_display", "fieldtype": "Small Text", "hidden": 1, "label": "Address", "read_only": 1}, {"fieldname": "contact_display", "fieldtype": "Small Text", "hidden": 1, "in_global_search": 1, "label": "Contact", "read_only": 1}, {"fieldname": "contact_mobile", "fieldtype": "Data", "hidden": 1, "label": "Mobile No", "options": "Phone", "read_only": 1}, {"fieldname": "contact_email", "fieldtype": "Data", "hidden": 1, "label": "Contact Email", "options": "Email", "read_only": 1}, {"fieldname": "column_break1", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "width": "50%"}, {"default": "Today", "fieldname": "mntc_date", "fieldtype": "Date", "label": "Maintenance Date", "no_copy": 1, "oldfieldname": "mntc_date", "oldfieldtype": "Date", "reqd": 1}, {"fieldname": "mntc_time", "fieldtype": "Time", "label": "Maintenance Time", "no_copy": 1, "oldfieldname": "mntc_time", "oldfieldtype": "Time"}, {"fieldname": "maintenance_details", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "fa fa-wrench"}, {"fieldname": "completion_status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Completion Status", "oldfieldname": "completion_status", "oldfieldtype": "Select", "options": "\nPartially Completed\nFully Completed", "reqd": 1}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"default": "Unscheduled", "fieldname": "maintenance_type", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Maintenance Type", "oldfieldname": "maintenance_type", "oldfieldtype": "Select", "options": "\nScheduled\nUnscheduled\nBreakdown", "reqd": 1}, {"fieldname": "section_break0", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "fa fa-wrench"}, {"fieldname": "purposes", "fieldtype": "Table", "label": "Purposes", "oldfieldname": "maintenance_visit_details", "oldfieldtype": "Table", "options": "Maintenance Visit Purpose"}, {"fieldname": "more_info", "fieldtype": "Section Break", "label": "More Information", "oldfieldtype": "Section Break", "options": "fa fa-file-text"}, {"fieldname": "customer_feedback", "fieldtype": "Small Text", "label": "Customer <PERSON><PERSON><PERSON>", "oldfieldname": "customer_feedback", "oldfieldtype": "Small Text"}, {"fieldname": "col_break3", "fieldtype": "Column Break"}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Data", "options": "\nDraft\nCancelled\nSubmitted", "read_only": 1, "reqd": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "oldfieldname": "amended_from", "oldfieldtype": "Data", "options": "Maintenance Visit", "print_hide": 1, "read_only": 1, "width": "150px"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "oldfieldname": "company", "oldfieldtype": "Select", "options": "Company", "print_hide": 1, "remember_last_selected_value": 1, "reqd": 1}, {"depends_on": "customer", "fieldname": "contact_info_section", "fieldtype": "Section Break", "label": "Contact Info", "options": "fa fa-bullhorn"}, {"fieldname": "customer_address", "fieldtype": "Link", "label": "Customer Address", "options": "Address", "print_hide": 1}, {"fieldname": "contact_person", "fieldtype": "Link", "label": "Contact Person", "options": "Contact", "print_hide": 1}, {"fieldname": "col_break4", "fieldtype": "Column Break"}, {"fieldname": "territory", "fieldtype": "Link", "label": "Territory", "options": "Territory", "print_hide": 1}, {"fieldname": "customer_group", "fieldtype": "Link", "label": "Customer Group", "options": "Customer Group", "print_hide": 1}, {"fieldname": "maintenance_schedule", "fieldtype": "Link", "label": "Maintenance Schedule", "options": "Maintenance Schedule", "read_only": 1}, {"fieldname": "maintenance_schedule_detail", "fieldtype": "Link", "hidden": 1, "label": "Maintenance Schedule Detail", "options": "Maintenance Schedule Detail"}], "icon": "fa fa-file-text", "idx": 1, "is_submittable": 1, "links": [], "modified": "2023-06-03 16:19:07.902723", "modified_by": "Administrator", "module": "Maintenance", "name": "Maintenance Visit", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Maintenance User", "share": 1, "submit": 1, "write": 1}], "search_fields": "status,maintenance_type,customer,customer_name,mntc_date,company", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "timeline_field": "customer", "title_field": "customer_name"}