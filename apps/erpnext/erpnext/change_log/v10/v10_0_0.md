- Account Numbers
	- Default Chart of accounts with account number
	- Set account number for your existing chart of accounts
	
- Payment Terms
	- Create Sales Invoice with multiple due dates and payment slab

- Agriculture (New Domain)
	- Manage Crop, Crop Cycle, Land Unit, Disease and Fertilizer records
	- Maintain records of Plant / Soil / Water Analysis

- Non Profits (New Domain)
	- Manage records of Members, Donors, Volunteers and Chapters
	- Portal for Grant Application

- Delivery Trip
	- Track each of your delivery trips with their associated stops and timing

- Item Variants Update
	- Create variants from Quick Entry dialog
	- Create multiple variants from a single screen

- Shipping Rule
	- Now available in Buying cycle
	- Apply based on Net Weight / Fixed Amount / Net Total

- Updated POS
	- Single POS Profile for multiple Users
	- Sales Payment Summary (X & Z) report
	- Fixed multiple bugs

- Employee Advance
	- Manage Advances given to your employee and adjust with Expense Claim

- Payroll Entry
	- Maintain records of each payroll processing
	- Deprecated Process Payroll tool

- Schools to Education
	- School module is renamed to Education

- Opening Invoice Tool
	- A new tool to create opening invoices

- Asset Maintenance
	- Maintain records of Asset Maintenance and Asset Repair

- Employee Tree
	- Employee document now has a tree view, so you can create your organisation chart based on it

- Task Tree
	- Task also has a Tree view now

- Education module update
	- Course Scheduling Tool

- Batch selection based on earlier expiry date of the batch

- Invoice GL Entry based on rounded total (instead of grand total)

- Multiple UOM in Material Request