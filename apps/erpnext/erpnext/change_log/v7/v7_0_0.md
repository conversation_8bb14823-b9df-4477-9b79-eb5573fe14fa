#### New POS
- Offline
- Multiple Payment Modes
- Standard documents cannot be edited in POS view

#### Payment Entry
- Dedicated form for managing Payments
- Designed for normal users who do not have accounting background

#### Request for Quotation
- Updated workflow: Material Request -> **Request for Quotation** -> Supplier Quotation -> Purchase Order

#### Fixed Asset Management
- Manage fixed asset records and their depreciation

#### Improved Navigation
- Heatmaps
- Centralized navigation from Masters like <PERSON><PERSON>, Customer, Supplier, Employee etc.

#### Timesheets
- New grid
- Multiple time logs in one timesheets
- Linked to Payroll and Billing

#### Graphs in Reports
- Added graphs in some important reports like Balance Sheet, Accounts Receivable etc.

#### Sub-warehouse
- Tree view for Warehouse

#### New Portal Design
- New Homepage Design
- Sidebar in Portal View
- New Cart View

#### Collaborative Project Management
- Web View
- Customers/Suppliers can add/edit issues and view timesheets

#### Budget
- Dedicated budget form
- Budget can be assigned against Cost Center Group

#### Check Printing Format
- Ability to customize Cheque Printing Format for any bank

#### Schools application is now part of ERPNext

#### Minor

- Selling Price calculation based on Margin defined in the Pricing Rule
- Document flow-chart on Sales / Purchase Transactions
- Domain specific desktop views
- Add opening Stock and Rate while creating a new Item
- Book payments and update stock directly from Purchase Invoice
- List view for Products on Website
- Features Setup is deprecated, settings moved to individual module setup views
- Added Safety Stock to Item Master
