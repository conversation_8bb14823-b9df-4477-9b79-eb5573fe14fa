# Version 13.5.0 Release Notes

### Features & Enhancements

- Tax deduction against advance payments ([#25831](https://github.com/frappe/erpnext/pull/25831))
- Cost-center wise period closing entry ([#25766](https://github.com/frappe/erpnext/pull/25766))
- Create Quality Inspections from account and stock documents ([#25221](https://github.com/frappe/erpnext/pull/25221))
- Item Taxes based on net rate ([#25961](https://github.com/frappe/erpnext/pull/25961))
- Enable/disable gl entry posting for change given in pos ([#25822](https://github.com/frappe/erpnext/pull/25822))
- Add Inactive status to Employee ([#26029](https://github.com/frappe/erpnext/pull/26029))
- Added check box to combine items with same BOM ([#25478](https://github.com/frappe/erpnext/pull/25478))
- Item Tax Templates for Germany ([#25858](https://github.com/frappe/erpnext/pull/25858))
- Refactored leave balance report ([#25771](https://github.com/frappe/erpnext/pull/25771))
- Refactored Vehicle Expenses Report ([#25727](https://github.com/frappe/erpnext/pull/25727))
- Refactored maintenance schedule and visit document ([#25358](https://github.com/frappe/erpnext/pull/25358))

### Fixes

- Cannot add same item with different rates ([#25849](https://github.com/frappe/erpnext/pull/25849))
- Show only company addresses for ITC reversal entry ([#25866](https://github.com/frappe/erpnext/pull/25866))
- Hiding Rounding Adjustment field ([#25380](https://github.com/frappe/erpnext/pull/25380))
- Auto tax calculations in Payment Entry ([#26055](https://github.com/frappe/erpnext/pull/26055))
- Not able to select the item code in work order ([#25915](https://github.com/frappe/erpnext/pull/25915))
- Cannot reset plaid link for a bank account ([#25869](https://github.com/frappe/erpnext/pull/25869))
- Student invalid password reset link ([#25826](https://github.com/frappe/erpnext/pull/25826))
- Multiple pos issues ([#25928](https://github.com/frappe/erpnext/pull/25928))
- Add Product Bundles to POS ([#25860](https://github.com/frappe/erpnext/pull/25860))
- Enable Parallel tests ([#25862](https://github.com/frappe/erpnext/pull/25862))
- Service item check on e-Invoicing ([#25986](https://github.com/frappe/erpnext/pull/25986))
- Choose correct Salary Structure Assignment when getting data for formula eval ([#25981](https://github.com/frappe/erpnext/pull/25981))
- Ignore internal transfer invoices from GST Reports ([#25969](https://github.com/frappe/erpnext/pull/25969))
- Taxable value for invoices with additional discount ([#26056](https://github.com/frappe/erpnext/pull/26056))
- Validate negative allocated amount in Payment Entry ([#25799](https://github.com/frappe/erpnext/pull/25799))
- Allow all System Managers to delete company transactions ([#25834](https://github.com/frappe/erpnext/pull/25834))
- Wrong round off gl entry posted in case of purchase invoice ([#25775](https://github.com/frappe/erpnext/pull/25775))
- Use dictionary filter instead of list ([#25874](https://github.com/frappe/erpnext/pull/25874))
- Ageing error in PSOA ([#25855](https://github.com/frappe/erpnext/pull/25855))
- On click of duplicate button system has not copied the difference account ([#25988](https://github.com/frappe/erpnext/pull/25988))
- Assign Product Bundle's conversion_factor to Pack… ([#25840](https://github.com/frappe/erpnext/pull/25840))
- Rename Loan Management workspace to Loans ([#25856](https://github.com/frappe/erpnext/pull/25856))
- Fix stock quantity calculation when negative_stock_allowe… ([#25859](https://github.com/frappe/erpnext/pull/25859))
- Update cost center from pos profile ([#25971](https://github.com/frappe/erpnext/pull/25971))
- Ensure website theme is applied correctly ([#25863](https://github.com/frappe/erpnext/pull/25863))
- Only display GST card in Accounting Workspace if it's in India ([#26000](https://github.com/frappe/erpnext/pull/26000))
- Incorrect gstin fetched incase of branch company address ([#25841](https://github.com/frappe/erpnext/pull/25841))
- Sort account balances by account name ([#26009](https://github.com/frappe/erpnext/pull/26009))
- Custom conversion factor field not mapped from job card to stock entry ([#25956](https://github.com/frappe/erpnext/pull/25956))
- Chart of accounts importer always error ([#25882](https://github.com/frappe/erpnext/pull/25882))
- Create POS Invoice for Product Bundles ([#25847](https://github.com/frappe/erpnext/pull/25847))
- Wrap dates in getdate for leave application ([#25899](https://github.com/frappe/erpnext/pull/25899))
- Closing entry shows incorrect expected amount ([#25868](https://github.com/frappe/erpnext/pull/25868))
- Add Hold status column in the Issue Summary Report ([#25828](https://github.com/frappe/erpnext/pull/25828))
- Rendering of broken image on pos ([#25872](https://github.com/frappe/erpnext/pull/25872))
- Timeout error in the repost item valuation ([#25854](https://github.com/frappe/erpnext/pull/25854))