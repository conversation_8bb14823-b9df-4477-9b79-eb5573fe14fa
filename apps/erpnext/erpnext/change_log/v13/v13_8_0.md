# Version 13.8.0 Release Notes

### Features & Enhancements
- Report to show COGS by item groups ([#26222](https://github.com/frappe/erpnext/pull/26222))
- Enhancements in TDS ([#26677](https://github.com/frappe/erpnext/pull/26677))
- API Endpoint to update halted Razorpay subscriptions ([#26564](https://github.com/frappe/erpnext/pull/26564))

### Fixes
- Incorrect bom name ([#26600](https://github.com/frappe/erpnext/pull/26600))
- Exchange rate revaluation posting date and precision fixes ([#26651](https://github.com/frappe/erpnext/pull/26651))
- POS item cart dom updates ([#26460](https://github.com/frappe/erpnext/pull/26460))
- General Ledger report not working with filter group by ([#26439](https://github.com/frappe/erpnext/pull/26438))
- Tax calculation for Recurring additional salary ([#24206](https://github.com/frappe/erpnext/pull/24206))
- Validation check for batch for stock reconciliation type in stock entry ([#26487](https://github.com/frappe/erpnext/pull/26487))
- Improved UX for additional discount field ([#26502](https://github.com/frappe/erpnext/pull/26502))
- Add missing cess amount in GSTR-3B report ([#26644](https://github.com/frappe/erpnext/pull/26644))
- Optimized code for reposting item valuation ([#26431](https://github.com/frappe/erpnext/pull/26431))
- FG item not fetched in manufacture entry ([#26508](https://github.com/frappe/erpnext/pull/26508))
- Errors on parallel requests creation of company for India  ([#26420](https://github.com/frappe/erpnext/pull/26420))
- Incorrect valuation rate calculation in gross profit report ([#26558](https://github.com/frappe/erpnext/pull/26558))
- Empty "against account" in Purchase Receipt GLE ([#26712](https://github.com/frappe/erpnext/pull/26712))
- Remove cancelled entries from Stock and Account Value comparison report ([#26721](https://github.com/frappe/erpnext/pull/26721))
- Remove manual permission checking ([#26691](https://github.com/frappe/erpnext/pull/26691))
- Delete child docs when parent doc is deleted ([#26518](https://github.com/frappe/erpnext/pull/26518))
- GST Reports timeout issue ([#26646](https://github.com/frappe/erpnext/pull/26646))
- Parent condition in pricing rules ([#26727](https://github.com/frappe/erpnext/pull/26727))
- Added Company filters for Loan ([#26294](https://github.com/frappe/erpnext/pull/26294))
- Incorrect discount amount on amended document ([#26292](https://github.com/frappe/erpnext/pull/26292))
- Exchange gain loss not set for advances linked with invoices ([#26436](https://github.com/frappe/erpnext/pull/26436))
- Unallocated amount in Payment Entry after taxes ([#26412](https://github.com/frappe/erpnext/pull/26412))
- Wrong operation time in Work Order ([#26613](https://github.com/frappe/erpnext/pull/26613))
- Serial No and Batch validation ([#26614](https://github.com/frappe/erpnext/pull/26614))
- Gl Entries for exchange gain loss ([#26734](https://github.com/frappe/erpnext/pull/26734))
- TDS computation summary shows cancelled invoices ([#26485](https://github.com/frappe/erpnext/pull/26485))
- Price List rate not fetched for return sales invoice fixed ([#26560](https://github.com/frappe/erpnext/pull/26560))
- Included company in link document type filters for contact ([#26576](https://github.com/frappe/erpnext/pull/26576))
- Ignore mandatory fields while creating payment reconciliation Journal Entry ([#26643](https://github.com/frappe/erpnext/pull/26643))
- Unable to download GSTR-1 json ([#26418](https://github.com/frappe/erpnext/pull/26418))
- Paging buttons not working on item group portal page ([#26498](https://github.com/frappe/erpnext/pull/26498))
