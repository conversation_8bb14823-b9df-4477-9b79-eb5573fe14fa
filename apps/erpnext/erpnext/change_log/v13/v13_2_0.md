# Version 13.2.0 Release Notes

### Features & Enhancements

- Employee Hours Utilization Report ([#25209](https://github.com/frappe/erpnext/pull/25209))
- Delayed Tasks Summary Report ([#25024](https://github.com/frappe/erpnext/pull/25024))
- Project Profitability Report ([#24944](https://github.com/frappe/erpnext/pull/24944))
- Timer in LMS Quiz ([#24246](https://github.com/frappe/erpnext/pull/24246))
- Role to allow over billing, delivery, receipt ([#24854](https://github.com/frappe/erpnext/pull/24854))
- Auto calculate distance for e-way bill generations ([#25480](https://github.com/frappe/erpnext/pull/25480))
- Add total available stock field in PO ([#24878](https://github.com/frappe/erpnext/pull/24878))
- Refactored Setup Taxes and Charges ([#24805](https://github.com/frappe/erpnext/pull/24805))
- Inpatient Occupancy Table Editable for Healthcare Admin ([#24989](https://github.com/frappe/erpnext/pull/24989))
- Added Disable Rounded Total in sales transactions ([#25362](https://github.com/frappe/erpnext/pull/25362))


### Fixes

- Incorrect GL Entry validation ([#25474](https://github.com/frappe/erpnext/pull/25474))
- Cannot create item variants ([#25433](https://github.com/frappe/erpnext/pull/25433))
- Leave policy in leave allocation ([#25334](https://github.com/frappe/erpnext/pull/25334))
- Let Administrator delete company transactions ([#25300](https://github.com/frappe/erpnext/pull/25300))
- Display reconcile tool when closing balance 0 ([#25417](https://github.com/frappe/erpnext/pull/25417))
- Bulk Salary Structure Assignment ([#25389](https://github.com/frappe/erpnext/pull/25389))
- Payment amount showing in foreign currency ([#25518](https://github.com/frappe/erpnext/pull/25518))
- Commit changes to shipment status in database ([#25374](https://github.com/frappe/erpnext/pull/25374))
- Add amend perm for loan and system manager for loan doctypes ([#25393](https://github.com/frappe/erpnext/pull/25393))
- Cashier query in POS Opening/Closing Entry ([#25398](https://github.com/frappe/erpnext/pull/25398))
- Apply single transaction threshold on net_total instead of supplier credit amount ([#25243](https://github.com/frappe/erpnext/pull/25243))
- Update allocated amount after paid amount is changed in PE ([#25528](https://github.com/frappe/erpnext/pull/25528))
- Remove non-standard module cards from Home Workspace ([#25391](https://github.com/frappe/erpnext/pull/25391))
- Cannot scan spacebar character in pos ([#25479](https://github.com/frappe/erpnext/pull/25479))
- Permission error after submitting exchange rate revaluation ([#25432](https://github.com/frappe/erpnext/pull/25432))
- Equality check instead of assignment in cart ([#25372](https://github.com/frappe/erpnext/pull/25372))
- Disable auto naming of customer during import ([#25152](https://github.com/frappe/erpnext/pull/25152))
- Additional Salary component amount not getting set ([#25355](https://github.com/frappe/erpnext/pull/25355))
- Round off values near to zero ([#25304](https://github.com/frappe/erpnext/pull/25304))
- Allow to cancel loan with cancelled repayment entry ([#25508](https://github.com/frappe/erpnext/pull/25508))
- Currency symbol in bank transaction list view ([#25336](https://github.com/frappe/erpnext/pull/25336))
- Incorrect batch picked in subcontracted purchase receipt ([#25186](https://github.com/frappe/erpnext/pull/25186))
- Issue in project custom status ([#25452](https://github.com/frappe/erpnext/pull/25452))
- Shipment pickup_to, pickup_from functionality. ([#25359](https://github.com/frappe/erpnext/pull/25359))
- Stock ledger entry created against draft stock entry ([#25539](https://github.com/frappe/erpnext/pull/25539))
- Ageing errors in PSOA ([#25529](https://github.com/frappe/erpnext/pull/25529))
- Permission error while adding weekly holidays ([#25450](https://github.com/frappe/erpnext/pull/25450))
- Filter for employees in salary slip ([#25360](https://github.com/frappe/erpnext/pull/25360))
- Backward compatibility for GSTR-1 report ([#25444](https://github.com/frappe/erpnext/pull/25444))
- Incorrect incoming rate for the sales return ([#25145](https://github.com/frappe/erpnext/pull/25145))
- POS print receipt ([#25328](https://github.com/frappe/erpnext/pull/25328))
- Laboratory Module patch ([#25431](https://github.com/frappe/erpnext/pull/25431))
- Performance: fetching exchange rate on every line item slows down PO ([#25345](https://github.com/frappe/erpnext/pull/25345))
- Presentation currency in statement of accounts ([#25367](https://github.com/frappe/erpnext/pull/25367))
- Serial No not updated correctly via Inter Company Stock Transfer ([#25006](https://github.com/frappe/erpnext/pull/25006))
- Ignore Customer Group Perm on All Products page ([#25396](https://github.com/frappe/erpnext/pull/25396))
- Change subcontracted item display ([#25425](https://github.com/frappe/erpnext/pull/25425))
- Add company validation for e-invoicing ([#25348](https://github.com/frappe/erpnext/pull/25348))
