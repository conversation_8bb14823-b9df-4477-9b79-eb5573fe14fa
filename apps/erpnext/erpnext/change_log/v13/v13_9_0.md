# Version 13.9.0 Release Notes

### Features & Enhancements
- Organizational Chart ([#26261](https://github.com/frappe/erpnext/pull/26261))
- Enable discount accounting ([#26579](https://github.com/frappe/erpnext/pull/26579))
- Added multi-select fields in promotional scheme to create multiple pricing rules ([#25622](https://github.com/frappe/erpnext/pull/25622))
- Over transfer allowance for material transfers ([#26814](https://github.com/frappe/erpnext/pull/26814))
- Enhancements in Tax Withholding Category ([#26661](https://github.com/frappe/erpnext/pull/26661))

### Fixes
- Sales Return cancellation if linked with Payment Entry ([#26883](https://github.com/frappe/erpnext/pull/26883))
- Production plan not fetching sales order of a variant ([#25845](https://github.com/frappe/erpnext/pull/25845))
- Stock Analytics Report must consider warehouse during calculation ([#26908](https://github.com/frappe/erpnext/pull/26908))
- Incorrect date difference calculation ([#26805](https://github.com/frappe/erpnext/pull/26805))
- Tax calculation for Recurring additional salary ([#24206](https://github.com/frappe/erpnext/pull/24206))
- Cannot cancel payment entry if linked with invoices ([#26703](https://github.com/frappe/erpnext/pull/26703))
- Included company in link document type filters for contact ([#26576](https://github.com/frappe/erpnext/pull/26576))
- Fetch Payment Terms from linked Sales/Purchase Order ([#26723](https://github.com/frappe/erpnext/pull/26723))
- Let all System Managers be able to delete Company transactions ([#26819](https://github.com/frappe/erpnext/pull/26819))
- Bank remittance report issue ([#26398](https://github.com/frappe/erpnext/pull/26398))
- Faulty Gl Entry for Asset LCVs ([#26803](https://github.com/frappe/erpnext/pull/26803))
- Clean Serial No input on Server Side ([#26878](https://github.com/frappe/erpnext/pull/26878))
- Supplier invoice importer fix v13 ([#26633](https://github.com/frappe/erpnext/pull/26633))
- POS payment modes displayed wrong total ([#26808](https://github.com/frappe/erpnext/pull/26808))
- Fetching of item tax from hsn code ([#26736](https://github.com/frappe/erpnext/pull/26736))
- Cannot cancel invoice if IRN cancelled on portal ([#26879](https://github.com/frappe/erpnext/pull/26879))
- Validate python expressions ([#26856](https://github.com/frappe/erpnext/pull/26856))
- POS Item Cart non-stop scroll issue ([#26693](https://github.com/frappe/erpnext/pull/26693))
- Add mandatory depends on condition for export type field ([#26958](https://github.com/frappe/erpnext/pull/26958))
- Cannot generate IRNs for standalone credit notes ([#26824](https://github.com/frappe/erpnext/pull/26824))
- Added progress bar in Repost Item Valuation to check the status of reposting ([#26630](https://github.com/frappe/erpnext/pull/26630))
- TDS calculation for first threshold breach for TDS category 194Q ([#26710](https://github.com/frappe/erpnext/pull/26710))
- Student category mapping from the program enrollment tool ([#26739](https://github.com/frappe/erpnext/pull/26739))
- Cost center & account validation in Sales/Purchase Taxes and Charges ([#26881](https://github.com/frappe/erpnext/pull/26881))
- Reset weight_per_unit on replacing Item ([#26791](https://github.com/frappe/erpnext/pull/26791))
- Do not fetch fully return issued purchase receipts ([#26825](https://github.com/frappe/erpnext/pull/26825))
- Incorrect amount in work order required items table.  ([#26585](https://github.com/frappe/erpnext/pull/26585))
- Additional discount calculations in Invoices ([#26553](https://github.com/frappe/erpnext/pull/26553))
- Refactored Asset Repair ([#26415](https://github.com/frappe/erpnext/pull/25798))
- Exchange rate revaluation posting date and precision fixes ([#26650](https://github.com/frappe/erpnext/pull/26650))
- POS Invoice consolidated Sales Invoice field set to no copy ([#26768](https://github.com/frappe/erpnext/pull/26768))
- Consider grand total for threshold check ([#26683](https://github.com/frappe/erpnext/pull/26683))
- Budget variance missing values ([#26966](https://github.com/frappe/erpnext/pull/26966))
- GL Entries for exchange gain loss ([#26728](https://github.com/frappe/erpnext/pull/26728))
- Add missing cess amount in GSTR-3B report ([#26544](https://github.com/frappe/erpnext/pull/26544))
- GST Reports timeout issue ([#26575](https://github.com/frappe/erpnext/pull/26575))