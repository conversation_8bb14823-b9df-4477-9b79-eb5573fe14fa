# Version 13.7.0 Release Notes

### Features & Enhancements
- Optionally allow rejected quality inspection on submission ([#26133](https://github.com/frappe/erpnext/pull/26133))
- Bootstrapped GST Setup for India ([#25415](https://github.com/frappe/erpnext/pull/25415))
- Fetching details from supplier/customer groups ([#26454](https://github.com/frappe/erpnext/pull/26454))
- Provision to make subcontracted purchase order from the production plan ([#26240](https://github.com/frappe/erpnext/pull/26240))
- Optimized code for reposting item valuation ([#26432](https://github.com/frappe/erpnext/pull/26432))

### Fixes
- Auto process deferred accounting for multi-company setup ([#26277](https://github.com/frappe/erpnext/pull/26277))
- Error while fetching item taxes ([#26218](https://github.com/frappe/erpnext/pull/26218))
- Validation check for batch for stock reconciliation type in stock entry(bp #26370 ) ([#26488](https://github.com/frappe/erpnext/pull/26488))
- Error popup for COA errors ([#26358](https://github.com/frappe/erpnext/pull/26358))
- Precision for expected values in payment entry test ([#26394](https://github.com/frappe/erpnext/pull/26394))
- Bank statement import ([#26287](https://github.com/frappe/erpnext/pull/26287))
- LMS progress issue ([#26253](https://github.com/frappe/erpnext/pull/26253))
- Paging buttons not working on item group portal page ([#26497](https://github.com/frappe/erpnext/pull/26497))
- Omit item discount amount for e-invoicing ([#26353](https://github.com/frappe/erpnext/pull/26353))
- Validate LCV for Invoices without Update Stock ([#26333](https://github.com/frappe/erpnext/pull/26333))
- Remove cancelled entries in consolidated financial statements ([#26331](https://github.com/frappe/erpnext/pull/26331))
- Fetching employee in payroll entry ([#26271](https://github.com/frappe/erpnext/pull/26271))
- To fetch the correct field in Tax Rule ([#25927](https://github.com/frappe/erpnext/pull/25927))
- Order and time of operations in multilevel BOM work order ([#25886](https://github.com/frappe/erpnext/pull/25886))
- Fixed Budget Variance Graph color from all black to default ([#26368](https://github.com/frappe/erpnext/pull/26368))
- TDS computation summary shows cancelled invoices (#26456) ([#26486](https://github.com/frappe/erpnext/pull/26486))
- Do not consider cancelled entries in party dashboard ([#26231](https://github.com/frappe/erpnext/pull/26231))
- Add validation for 'for_qty' else throws errors ([#25829](https://github.com/frappe/erpnext/pull/25829))
- Move the rename abbreviation job to long queue (#26434) ([#26462](https://github.com/frappe/erpnext/pull/26462))
- Query for Training Event ([#26388](https://github.com/frappe/erpnext/pull/26388))
- Item group portal issues (backport) ([#26493](https://github.com/frappe/erpnext/pull/26493))
- When lead is created with mobile_no, mobile_no value gets lost ([#26298](https://github.com/frappe/erpnext/pull/26298))
- WIP needs to be set before submit on skip_transfer (bp #26499) ([#26507](https://github.com/frappe/erpnext/pull/26507))
- Incorrect valuation rate in stock reconciliation ([#26259](https://github.com/frappe/erpnext/pull/26259))
- Precision rate for packed items in internal transfers ([#26046](https://github.com/frappe/erpnext/pull/26046))
- Changed profitability analysis report width ([#26165](https://github.com/frappe/erpnext/pull/26165))
- Unable to download GSTR-1 json ([#26468](https://github.com/frappe/erpnext/pull/26468))
- Unallocated amount in Payment Entry after taxes ([#26472](https://github.com/frappe/erpnext/pull/26472))
- Include Stock Reco logic in `update_qty_in_future_sle` ([#26158](https://github.com/frappe/erpnext/pull/26158))
- Update cost not working in the draft BOM ([#26279](https://github.com/frappe/erpnext/pull/26279))
- Cancellation of Loan Security Pledges ([#26252](https://github.com/frappe/erpnext/pull/26252))
- fix(e-invoicing): allow export invoice even if no taxes applied (#26363) ([#26405](https://github.com/frappe/erpnext/pull/26405))
- Delete accounts (an empty file) ([#25323](https://github.com/frappe/erpnext/pull/25323))
- Errors on parallel requests creation of company for India  ([#26470](https://github.com/frappe/erpnext/pull/26470))
- Incorrect bom no added for non-variant items on variant boms ([#26320](https://github.com/frappe/erpnext/pull/26320))
- Incorrect discount amount on amended document ([#26466](https://github.com/frappe/erpnext/pull/26466))
- Added a message to enable appointment booking if disabled ([#26334](https://github.com/frappe/erpnext/pull/26334))
- fix(pos): taxes amount in pos item cart ([#26411](https://github.com/frappe/erpnext/pull/26411))
- Track changes on batch ([#26382](https://github.com/frappe/erpnext/pull/26382))
- Stock entry with putaway rule not working ([#26350](https://github.com/frappe/erpnext/pull/26350))
- Only "Tax" type accounts should be shown for selection in GST Settings ([#26300](https://github.com/frappe/erpnext/pull/26300))
- Added permission for employee to book appointment ([#26255](https://github.com/frappe/erpnext/pull/26255))
- Allow to make job card without employee ([#26312](https://github.com/frappe/erpnext/pull/26312))
- Project Portal Enhancements ([#26290](https://github.com/frappe/erpnext/pull/26290))
- BOM stock report not working ([#26332](https://github.com/frappe/erpnext/pull/26332))
- Order Items by weightage in the web items query ([#26284](https://github.com/frappe/erpnext/pull/26284))
- Removed values out of sync validation from stock transactions ([#26226](https://github.com/frappe/erpnext/pull/26226))
- Payroll-entry minor fix ([#26349](https://github.com/frappe/erpnext/pull/26349))
- Allow user to change the To Date in the blanket order even after submit of order ([#26241](https://github.com/frappe/erpnext/pull/26241))
- Value fetching for custom field in POS ([#26367](https://github.com/frappe/erpnext/pull/26367))
- Iteration through accounts only when accounts exist ([#26391](https://github.com/frappe/erpnext/pull/26391))
- Employee Inactive status implications ([#26244](https://github.com/frappe/erpnext/pull/26244))
- Multi-currency issue ([#26458](https://github.com/frappe/erpnext/pull/26458))
- FG item not fetched in manufacture entry ([#26509](https://github.com/frappe/erpnext/pull/26509))
- Set query for training events ([#26303](https://github.com/frappe/erpnext/pull/26303))
- Fetch batch items in stock reconciliation ([#26213](https://github.com/frappe/erpnext/pull/26213))
- Employee selection not working in payroll entry ([#26278](https://github.com/frappe/erpnext/pull/26278))
- POS item cart dom updates (#26459) ([#26461](https://github.com/frappe/erpnext/pull/26461))
- dunning calculation of grand total when rate of interest is 0% ([#26285](https://github.com/frappe/erpnext/pull/26285))