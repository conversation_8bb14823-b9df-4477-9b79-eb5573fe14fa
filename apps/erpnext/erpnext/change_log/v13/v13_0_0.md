# Version 13.0.0 Release Notes

### Accounting
- [New and refreshed POS](https://github.com/frappe/erpnext/pull/20789)
- [GST E-invoicing for India](https://docs.erpnext.com/docs/user/manual/en/regional/india/setup-e-invoicing)
- [Distributed Cost Center](https://docs.erpnext.com/docs/user/manual/en/accounts/distributed-cost-center)
- [Process Bulk Statement Of Accounts](https://docs.erpnext.com/docs/user/manual/en/accounts/process-statement-of-accounts)
- [More controlled deferred revenue booking](https://docs.erpnext.com/docs/user/manual/en/accounts/process-deferred-accounting)
- [Dunning](https://docs.erpnext.com/docs/user/manual/en/accounts/dunning)
- [Journal Entry Template](https://docs.erpnext.com/docs/user/manual/en/accounts/journal-entry-template)
- [POS Register report](https://github.com/frappe/erpnext/pull/23313)
- [UAE VAT 201 Report](https://github.com/frappe/erpnext/pull/23447)


### Loan Management
- [Loan Application](https://docs.erpnext.com/docs/user/manual/en/loan-management/loan-application)
- [Loan](https://docs.erpnext.com/docs/user/manual/en/loan-management/loan)
- [Loan Security Pledge](https://docs.erpnext.com/docs/user/manual/en/loan-management/loan-security-pledge)
- [Loan Disbursement](https://docs.erpnext.com/docs/user/manual/en/loan-management/loan-disbursement)
- [Loan Repayment](https://docs.erpnext.com/docs/user/manual/en/loan-management/loan-repayment)
- [Loan Interest Accrual](https://docs.erpnext.com/docs/user/manual/en/loan-management/loan-interest-accrual)
- [Loan Write Off](https://docs.erpnext.com/docs/user/manual/en/loan-management/loan-write-off)

### Healthcare
- [Refactored Healthcare Module](https://docs.erpnext.com/docs/user/manual/en/healthcare)
- [Rehabilitation Module](https://docs.erpnext.com/docs/user/manual/en/healthcare/exercise_type)
- [Laboratory Module](https://docs.erpnext.com/docs/user/manual/en/healthcare/setup_laboratory)
- [Patient Progress Page](https://github.com/frappe/erpnext/pull/22474)
- [Inpatient Medication Order and Entry](https://docs.erpnext.com/docs/user/manual/en/healthcare/inpatient_medication_entry)
- [Therapy Plan Template](https://docs.erpnext.com/docs/user/manual/en/healthcare/therapy_plan)
- [Multi company support in Healthcare](https://github.com/frappe/erpnext/pull/21290)
- [Inpatient Medication Orders Script Report](https://github.com/frappe/erpnext/pull/23984)
- [Patient History Enhancements](https://github.com/frappe/erpnext/pull/24033)


### Stock
- [Putaway](https://docs.erpnext.com/docs/user/manual/en/stock/putaway-rule)
- [More accurate stock valuation in case of back-dated stock transactions](https://github.com/frappe/erpnext/pull/24183)
- [Repost item costing via background job](https://github.com/frappe/erpnext/pull/24183)
- [Item valuation for internal stock transfers](https://github.com/frappe/erpnext/pull/24200)
- [Multi currency in Landed Cost Voucher](https://github.com/frappe/erpnext/pull/24127)
- [Formula based Quality Inspection](https://docs.erpnext.com/docs/user/manual/en/stock/quality-inspection)
- [Value Based and Numeric Quality Inspection](https://github.com/frappe/erpnext/pull/24181)
- [Shipment](https://github.com/frappe/erpnext/pull/22914)
- [Return tracking in PR/DN](https://github.com/frappe/erpnext/pull/22859)

### Manufacturing
- [Production forecasting using Exponential Smoothing method](https://docs.erpnext.com/docs/user/manual/en/manufacturing/reports/demand-driven-forecasting)
- [BOM Template](https://docs.erpnext.com/docs/user/manual/en/manufacturing/bill-of-materials#34-bom-template)
- [Downtime Entry](https://docs.erpnext.com/docs/user/manual/en/manufacturing/downtime-entry)
- [Quality Inspection on Job Card](https://github.com/frappe/erpnext/pull/23964)
- New Reports
  - Production Planning Report ([#21763](https://github.com/frappe/erpnext/pull/21763))
  - BOM Operations Time ([#21763](https://github.com/frappe/erpnext/pull/21763))
  - Work Order Summary ([#21430](https://github.com/frappe/erpnext/pull/21430))
  - Job card Summary ([#21430](https://github.com/frappe/erpnext/pull/21430))
  - Downtime Analysis ([#21430](https://github.com/frappe/erpnext/pull/21430))
  - Quality Inspection ([#21430](https://github.com/frappe/erpnext/pull/21430))

### HR
- [Leave policy assignment](https://github.com/frappe/erpnext/pull/23112)
- [In and Out time in attendance](https://github.com/frappe/erpnext/pull/21547)
- [Shift management](https://docs.erpnext.com/docs/user/manual/en/human-resources/shift-management)
- [Recruitment analytics](https://github.com/frappe/erpnext/pull/21732)
- [Bulk Mark Attendance](https://github.com/frappe/erpnext/pull/20062)
- [Leave type with partial payment](https://github.com/frappe/erpnext/pull/23173)
- New and enhanced reports
    - Employee Analytics ([#21705](https://github.com/frappe/erpnext/pull/21705))
    - Employee Leave Balance ([#20754](https://github.com/frappe/erpnext/pull/20754))
    - Employee Leave Balance Summary ([#20754](https://github.com/frappe/erpnext/pull/20754))

### Payroll
- [Multi-currency payroll](https://github.com/frappe/erpnext/pull/23519)
- [Payroll based on attendance](https://github.com/frappe/erpnext/pull/21258)
- [Payroll based on employee cost center](https://github.com/frappe/erpnext/pull/21609)
- [Recurring Additional Salary](https://github.com/frappe/erpnext/pull/20936)
- [Compute Year to Date for Salary Slip components](https://github.com/frappe/erpnext/pull/24362)
- New Reports
  - Income Tax Deductions
  - Professional Tax Deductions
  - Provident Fund Deductions
  - Total Salary Payments Based on Payment Mode
  - Salary Payments via ECS

### CRM
- [Social Media Post](https://docs.erpnext.com/docs/user/manual/en/CRM/social-media-post)
- [Make Quotation against Blanket Order](https://docs.erpnext.com/docs/user/manual/en/selling/blanket-order)
- [Calendar View for Opportunity](https://github.com/frappe/erpnext/pull/21280)

### Selling
- [Batch wise item pricing](https://github.com/frappe/erpnext/pull/24470)
- [Refreshed shopping cart](https://github.com/frappe/erpnext/pull/22617)
- [Territory-wise Sales Report](https://github.com/frappe/erpnext/pull/20428)

#### Buying
- [Multi UOM support in Request for Quotation](https://github.com/frappe/erpnext/pull/22249)
- [Provision to make RFQ against Opportunity](https://github.com/frappe/erpnext/pull/22765)
- [Item Rate in Stock UOM in purchase cycle](https://github.com/frappe/erpnext/pull/24315)
- New Reports
  - Requested Items To Order ([#21611](https://github.com/frappe/erpnext/pull/21611))
  - Purchase Order Analysis ([#21611](https://github.com/frappe/erpnext/pull/21611))
  - Supplier Quotation Comparison report ([#23323](https://github.com/frappe/erpnext/pull/23323))

### Project
- [Project template with dependent tasks](https://github.com/frappe/erpnext/pull/24092)
- [Project Summary Report](https://github.com/frappe/erpnext/pull/21587)

### Support
- [Help Articles on support portal](https://github.com/frappe/erpnext/pull/22194)
- [Issue Metrics and SLA Enhancements](https://github.com/frappe/erpnext/pull/21617)
- [Issue Summary Script Report](https://docs.erpnext.com/docs/user/manual/en/support/support_reports)
- [Issue Analytics Script Report](https://docs.erpnext.com/docs/user/manual/en/support/support_reports)

### Non-Profits
- [80G Certificates and Donations](https://docs.erpnext.com/docs/user/manual/en/non_profit/tax_exemption_80g_certificate)

#### Integrations
- [Woocommerce Integration](https://docs.erpnext.com/docs/user/manual/en/erpnext_integration/woocommerce_integration)
- [Taxjar Integration](https://github.com/frappe/erpnext/pull/21047)
- [M-pesa Integration](https://docs.erpnext.com/docs/user/manual/en/erpnext_integration/mpesa-integration)
- [Telephony feature using Twillio](https://github.com/frappe/erpnext/pull/24032)
- [Voice Call Settings](https://github.com/frappe/erpnext/pull/24126)


#### Other Enhancements and Fixes
- Accounting Dimensions in Budget Variance Report ([#19973](https://github.com/frappe/erpnext/pull/19973))
- "Sync Now" option in Plaid Settings ([#23602](https://github.com/frappe/erpnext/pull/23602))
- Custom Fields in POS ([#19876](https://github.com/frappe/erpnext/pull/19876))
- [Inter Warehouse Stock Transfer in Purchase Receipt](https://docs.erpnext.com/docs/user/manual/en/stock/articles/material-transfer-from-delivery-note)
- [Accounts Payable Report based on Payment Terms](https://docs.erpnext.com/docs/user/manual/en/accounts/accounting-reports)
- Configurable accounting dimension filters and validations ([#23912](https://github.com/frappe/erpnext/pull/23912))
- Territory tree in Customer Acquisition and Loyalty report ([#21668](https://github.com/frappe/erpnext/pull/21668))
- Allow Purchase Invoice Creation Without Purchase Order Checkbox in Supplier ([#20864](https://github.com/frappe/erpnext/pull/20864))
- Gross Profit In Quotation ([#21795](https://github.com/frappe/erpnext/pull/21795))
- Notify credit controller users for credit limit extension via Email ([#22213](https://github.com/frappe/erpnext/pull/22213))
- Run MRP at parent level in the production plan and make material transfer based upon materials availability ([#21545](https://github.com/frappe/erpnext/pull/21545))
- Balance Serial Nos in Stock Ledger report ([#23675](https://github.com/frappe/erpnext/pull/23675))
- Youtube interactions via Video  ([#22867](https://github.com/frappe/erpnext/pull/22867))
- Consider Holiday List in Student Leave Application and Attendance ([#23388](https://github.com/frappe/erpnext/pull/23388))
- Patient appointment status changes ([#24201](https://github.com/frappe/erpnext/pull/24201))
- Sales order status filter added for production plan ([#23805](https://github.com/frappe/erpnext/pull/23805))
- Monthly attendance sheet report group by Department, Designation, Employee Grade and Branch ([#21331](https://github.com/frappe/erpnext/pull/21331))
- Upload Attendance template now have pre-filled holiday status ([#20947](https://github.com/frappe/erpnext/pull/20947))
- Provision to disable serial no and batch selector ([#24398](https://github.com/frappe/erpnext/pull/24398))

<details>
<summary>More</summary>

- Fetch Items from BOM in Stock Entry([#19498](https://github.com/frappe/erpnext/pull/19498))
- Supplier Sourced Items in BOM ([#23557](https://github.com/frappe/erpnext/pull/23557))
- Close Production Plan ([#23728](https://github.com/frappe/erpnext/pull/23728))
- Button to create Stock Entry for Drug Shortage ([#24012](https://github.com/frappe/erpnext/pull/24012))
- Added column cost center in Accounts Receivable report ([#23835](https://github.com/frappe/erpnext/pull/23835))
- Added jinja templating in Contract Template ([#24046](https://github.com/frappe/erpnext/pull/24046))
- Make account number length configurable ([#23845](https://github.com/frappe/erpnext/pull/23845))
- Add company and correct filter in bank reconciliation statement ([#23614](https://github.com/frappe/erpnext/pull/23614))
- Added Condition field in Pricing Rule ([#23014](https://github.com/frappe/erpnext/pull/23014))
- Open lead status on next contact date ([#23445](https://github.com/frappe/erpnext/pull/23445))
- [Tax Category in POS Profile](https://docs.erpnext.com/docs/user/manual/en/accounts/pos-profile)
- Added phone field in product Inquiry ([#23170](https://github.com/frappe/erpnext/pull/23170))
- Allow Discharge despite Unbilled Healthcare Services ([#24281](https://github.com/frappe/erpnext/pull/24281))
- Do Not Bill Patient Encounters for Inpatients ([#24355](https://github.com/frappe/erpnext/pull/24355))
- Autofill Supplier pop-up when only 1 Supplier in RFQ ([#22512](https://github.com/frappe/erpnext/pull/22512))
- Accounting entries for service item in Purchase receipt ([#22223](https://github.com/frappe/erpnext/pull/22223))
- Added Project in Sales Analytics report ([#23309](https://github.com/frappe/erpnext/pull/23309))
- Added all companies option in employee tree to view employee across all companies ([#22573](https://github.com/frappe/erpnext/pull/22573))
- Email Group Option In Email Campaign ([#22731](https://github.com/frappe/erpnext/pull/22731))
- Stock Report Enhancements ([#21727](https://github.com/frappe/erpnext/pull/21727))
- Added range for age in stock ageing ([#22622](https://github.com/frappe/erpnext/pull/22622))
- Report Summary in Financial Statement([#20876](https://github.com/frappe/erpnext/pull/20876))
- Added sequence id in routing for the completion of operations sequentially ([#23641](https://github.com/frappe/erpnext/pull/23641))
- Nested Set filtering for Accounting Dimension
- Add/Remove Items from submitted Sales/Purchase Order
- Provision to edit Item Details from Marketplace
- Scan Barcode in Purchase Receipt
- Disable Rounded Totals Checkbox for Salary Slips in HR Settings

- Renamed Loan Management to Loan on Desk Page ([#21877](https://github.com/frappe/erpnext/pull/21877))
- Added Expense Approver field in Employee master ([#22244](https://github.com/frappe/erpnext/pull/22244))
- Bill all hours by default on Timesheet ([#22155](https://github.com/frappe/erpnext/pull/22155))
- Unable to cancel employee advance ([#22374](https://github.com/frappe/erpnext/pull/22374))
- Status error in purchase invoice ([#22351](https://github.com/frappe/erpnext/pull/22351))
- Item-wise sales and purchase register export ([#22184](https://github.com/frappe/erpnext/pull/22184))
- Billing address in for Purchase documents ([#22233](https://github.com/frappe/erpnext/pull/22233))
- Handle canceled entries in financial statements ([#22231](https://github.com/frappe/erpnext/pull/22231))
- Default period start date and period end date for financial statements ([#22011](https://github.com/frappe/erpnext/pull/22011))
- Update Packed Items via Update Items in Sales Order ([#22392](https://github.com/frappe/erpnext/pull/22392))
- Hide delete company transactions button if not system manager ([#21839](https://github.com/frappe/erpnext/pull/21839))
- Skipping total row for tree-view reports ([#22350](https://github.com/frappe/erpnext/pull/22350))
- Cancelled entries in tds payable monthly report ([#22131](https://github.com/frappe/erpnext/pull/22131))
- Inter-company Invoice currency for multicurrency transactions ([#21984](https://github.com/frappe/erpnext/pull/21984))
- Filter batches based on item and warehouse in Pick List (develop) ([#21780](https://github.com/frappe/erpnext/pull/21780))
- Set cost center in Expense Claim child based on parent (if missing) ([#22175](https://github.com/frappe/erpnext/pull/22175))
- Item wise backdated stock entry posting for immutable ledger ([#22366](https://github.com/frappe/erpnext/pull/22366))
- Shopping cart UI fixes ([#22137](https://github.com/frappe/erpnext/pull/22137))
- Filter Leave Type based on allocation for a particular employee ([#22050](https://github.com/frappe/erpnext/pull/22050))
- Party validation for inter-warehouse transaction ([#22186](https://github.com/frappe/erpnext/pull/22186))
- Manufacturing dashboard and work order summary chart ([#21946](https://github.com/frappe/erpnext/pull/21946))
- IP Admission and Discharge, Minor fixes ([#21817](https://github.com/frappe/erpnext/pull/21817))
- Validation of Purchase Order against Material Request missing ([#22192](https://github.com/frappe/erpnext/pull/22192))
- Staffing Plan validation ([#22379](https://github.com/frappe/erpnext/pull/22379))
- Do not allow backdated stock transactions in previous fiscal year ([#21967](https://github.com/frappe/erpnext/pull/21967))
- Employee Advance Return not working ([#21812](https://github.com/frappe/erpnext/pull/21812))
- Added card for reports on education desk ([#21853](https://github.com/frappe/erpnext/pull/21853))
- Refactored project summary report  ([#21943](https://github.com/frappe/erpnext/pull/21943))
- Revenue and Customer Count only in date range in Customer Acquitition Report ([#22210](https://github.com/frappe/erpnext/pull/22210))
- Alternative item not working for subcontract ([#22386](https://github.com/frappe/erpnext/pull/22386))
- Unable to create batched Item ([#22393](https://github.com/frappe/erpnext/pull/22393))
- Filters for the manufacturing reports ([#21960](https://github.com/frappe/erpnext/pull/21960))
- Raw material warehouse in Production Planning Report ([#21982](https://github.com/frappe/erpnext/pull/21982))
- Allowed LWP leave types to select in Leave Application even if there is no allocation against them ([#22197](https://github.com/frappe/erpnext/pull/22197))
- Report not working on parameter Grade ([#21951](https://github.com/frappe/erpnext/pull/21951))
- Allow to enter Relieving date if employee status is Left ([#22242](https://github.com/frappe/erpnext/pull/22242))
- Resetting lost reason in opportunity and quotation ([#22378](https://github.com/frappe/erpnext/pull/22378))
- Filtering issues in opening invoice creation tool ([#21969](https://github.com/frappe/erpnext/pull/21969))
- Set default reference Id for "On Previous Row Amount" and "On Previous Row Total" ([#22346](https://github.com/frappe/erpnext/pull/22346))
- UX date range field separated in from and to date fields. ([#21765](https://github.com/frappe/erpnext/pull/21765))
- Enable show_configure_button when shopping cart is enabled ([#22468](https://github.com/frappe/erpnext/pull/22468))
- Setup status indicators for Job Offer and Job Applicant (develop) ([#22445](https://github.com/frappe/erpnext/pull/22445))
- Item-wise sales history report ([#22783](https://github.com/frappe/erpnext/pull/22783))
- Setting filter for project in kanban board ([#22717](https://github.com/frappe/erpnext/pull/22717))
- Dashboard For Timesheet ([#22750](https://github.com/frappe/erpnext/pull/22750))
- Handle custom statuses for the pause SLA configuration ([#22349](https://github.com/frappe/erpnext/pull/22349))
- Quality Feedback and Template ([#22571](https://github.com/frappe/erpnext/pull/22571))
- Unable to change link from new lead to existing customer ([#22787](https://github.com/frappe/erpnext/pull/22787))
- Move Issue List actions under 'Actions' dropdown (ux) ([#22710](https://github.com/frappe/erpnext/pull/22710))
- Cost center should only show option of selected company ([#22598](https://github.com/frappe/erpnext/pull/22598))
- Serial No Rename does not affect  Stock Ledger Entry ([#22746](https://github.com/frappe/erpnext/pull/22746))
- Descriptions not copied while creating Fees from Fee Structure ([#22792](https://github.com/frappe/erpnext/pull/22792))
- Company filter for cost_center and expense_account in all sales and purchase transactions ([#22478](https://github.com/frappe/erpnext/pull/22478))
- Arrangements of filters for reports accounts payable & receivable  ([#22636](https://github.com/frappe/erpnext/pull/22636))
- Update the project after task deletion so that the % completed shows correct value ([#22591](https://github.com/frappe/erpnext/pull/22591))
- Block Invalid Serial No updates in Maintenance Schedule ([#22665](https://github.com/frappe/erpnext/pull/22665))
- Fetch item price in sales invoice based on it's validity ([#22563](https://github.com/frappe/erpnext/pull/22563))
- Add view ledger button for cancelled docs ([#22432](https://github.com/frappe/erpnext/pull/22432))
- Allow creating SLA documents even if SLA tracking is not enabled ([#22608](https://github.com/frappe/erpnext/pull/22608))
- Quotation list view blank if quotation_to field not set as a standard filter ([#22672](https://github.com/frappe/erpnext/pull/22672))
- Salary deductions report fixes ([#22397](https://github.com/frappe/erpnext/pull/22397))
22727))
- Incorrect delivered qty in Supplier-Wise Sales Analytics ([#22631](https://github.com/frappe/erpnext/pull/22631))
- Moved parent warehouse to top section also added a section break ([#22708](https://github.com/frappe/erpnext/pull/22708))
- Skip Progress and Completed by fields on Task Duplication ([#22565](https://github.com/frappe/erpnext/pull/22565))
- Incorrect stock after merging the items ([#22526](https://github.com/frappe/erpnext/pull/22526))
- Letter head not found in opening invoice creation tool ([#22488](https://github.com/frappe/erpnext/pull/22488))
- Cannot cancel asset and asset movement ([#22441](https://github.com/frappe/erpnext/pull/22441))
- Fetch project-related info in Timesheet ([#22423](https://github.com/frappe/erpnext/pull/22423))
- Currency symbol not showing as per company currency in stock balance report ([#22724](https://github.com/frappe/erpnext/pull/22724))
- Add default cost center in payment reconciliation JV ([#22614](https://github.com/frappe/erpnext/pull/22614))
- Stock Reconciliation Invalid Quantity for Batched Item ([#22726](https://github.com/frappe/erpnext/pull/22726))
- Project link not set in accounts other than profit and loss accounts ([#22051](https://github.com/frappe/erpnext/pull/22051))
- Buying price for non stock item in gross profit report ([#22616](https://github.com/frappe/erpnext/pull/22616))
- Multi currency payment reconciliation ([#22738](https://github.com/frappe/erpnext/pull/22738))
- Cannot cancel assets with repair pending ([#22440](https://github.com/frappe/erpnext/pull/22440))
- Reset homepage to home after unchecking products page ([#22736](https://github.com/frappe/erpnext/pull/22736))
- Generic Message in previous doc validation for buying and selling ([#22546](https://github.com/frappe/erpnext/pull/22546))
- Expense claim outstanding while making payment entry ([#22735](https://github.com/frappe/erpnext/pull/22735))
- Take parent cost center for child if no cost center at child in expense claim ([#22496](https://github.com/frappe/erpnext/pull/22496))
- Consider company fiscal year for getting balance ([#22577](https://github.com/frappe/erpnext/pull/22577))
- Pick List empty table and Serial-Batch items handling ([#22426](https://github.com/frappe/erpnext/pull/22426))
- Show total row in print format of financial statement ([#22693](https://github.com/frappe/erpnext/pull/22693))
- Set Root as Parent if no parent in new tree view node ([#22497](https://github.com/frappe/erpnext/pull/22497))
- Multiple pos issues ([#23725](https://github.com/frappe/erpnext/pull/23725))
- Calculate taxes if tax is based on item quantity and inclusive on item price ([#23001](https://github.com/frappe/erpnext/pull/23001))
- Contact us button not visible in the website for the non variant items ([#23217](https://github.com/frappe/erpnext/pull/23217))
- Not able to make Material Request from Sales Order ([#23669](https://github.com/frappe/erpnext/pull/23669))
- Capture advance payments in payment order ([#23256](https://github.com/frappe/erpnext/pull/23256))
- Program and Course Enrollment fixes ([#23333](https://github.com/frappe/erpnext/pull/23333))
- Cannot create asset if cwip disabled and account not set ([#23580](https://github.com/frappe/erpnext/pull/23580))
- Cannot merge pos invoices with inclusive tax ([#23541](https://github.com/frappe/erpnext/pull/23541))
- Do not allow Company as accounting dimension ([#23755](https://github.com/frappe/erpnext/pull/23755))
- Set value of wrong Bank Account field in Payment Entry ([#22302](https://github.com/frappe/erpnext/pull/22302))
- Reverse journal entry for multi-currency ([#23165](https://github.com/frappe/erpnext/pull/23165))
- Updated integrations desk page ([#23772](https://github.com/frappe/erpnext/pull/23772))
- Assessment Result child table not visible when accessed via Assessment Plan dashboard ([#22880](https://github.com/frappe/erpnext/pull/22880))
- Conversion factor fixes in Stock Entry ([#23407](https://github.com/frappe/erpnext/pull/23407))
- Total calculations for multi-currency RCM invoices ([#23072](https://github.com/frappe/erpnext/pull/23072))
- Show accounts in financial statements upto level 20 ([#23718](https://github.com/frappe/erpnext/pull/23718))
- Consolidated financial statement sums values into wrong parent ([#23288](https://github.com/frappe/erpnext/pull/23288))
- Set SLA variance in seconds for Duration fieldtype ([#23765](https://github.com/frappe/erpnext/pull/23765))
- Added missing reports on selling desk ([#23548](https://github.com/frappe/erpnext/pull/23548))
- Fixed heading in the mobile view ([#23145](https://github.com/frappe/erpnext/pull/23145))
- Misleading filters on Item tax Template Link field ([#22918](https://github.com/frappe/erpnext/pull/22918))
- Do not consider opening entries for TDS calculation ([#23597](https://github.com/frappe/erpnext/pull/23597))
- Attendance calendar map fix ([#23245](https://github.com/frappe/erpnext/pull/23245))
- Post cancellation accounting entry on posting date instead of current ([#23361](https://github.com/frappe/erpnext/pull/23361))
- Set Customer only if Contact is present ([#23704](https://github.com/frappe/erpnext/pull/23704))
- Add Delivery Note Count in Sales Invoice Dashboard ([#23161](https://github.com/frappe/erpnext/pull/23161))
- Breadcrumbs for Maintenance Visit and Schedule ([#23369](https://github.com/frappe/erpnext/pull/23369))
- Raise Error on over receipt/consumption for sub-contracted PR ([#23195](https://github.com/frappe/erpnext/pull/23195))
- Validate if company not set in the Payment Entry ([#23419](https://github.com/frappe/erpnext/pull/23419))
- Ignore company and bank account doctype while deleting company transactions ([#22953](https://github.com/frappe/erpnext/pull/22953))
- Sales funnel data is inconsistent ([#23110](https://github.com/frappe/erpnext/pull/23110))
- Credit Limit Email not working ([#23059](https://github.com/frappe/erpnext/pull/23059))
- Add Company in list fields to fetch for Expense Claim ([#23007](https://github.com/frappe/erpnext/pull/23007))
- Issue form cleaned up and renamed Minutes to First Response field ([#23066](https://github.com/frappe/erpnext/pull/23066))
- Quotation lost reason options fix ([#22814](https://github.com/frappe/erpnext/pull/22814))
- Tax amounts in HSN Wise Outward summary ([#23076](https://github.com/frappe/erpnext/pull/23076))
- Patient Appointment not able to save ([#23434](https://github.com/frappe/erpnext/pull/23434))
- Removed Working Hours field from Company ([#23009](https://github.com/frappe/erpnext/pull/23009))
- Added check-in time validation in the Inpatient Record - Transfer ([#22958](https://github.com/frappe/erpnext/pull/22958))
- Handle Blank from/to range in Numeric Item Attribute ([#23483](https://github.com/frappe/erpnext/pull/23483))
- Sequence Matcher error in Bank Reconciliation ([#23539](https://github.com/frappe/erpnext/pull/23539))
- Fixed Conversion Factor rate for the BOM Exploded Item ([#23151](https://github.com/frappe/erpnext/pull/23151))
- Payment Schedule not fetching ([#23476](https://github.com/frappe/erpnext/pull/23476))
- Validate if removed Item Attributes exist in variant items ([#22911](https://github.com/frappe/erpnext/pull/22911))
- Set default billing address for purchase documents ([#22950](https://github.com/frappe/erpnext/pull/22950))
- Added help link in navbar settings ([#22943](https://github.com/frappe/erpnext/pull/22943))
- Apply TDS on Purchase Invoice creation from Purchase Order and Purchase Receipt ([#23282](https://github.com/frappe/erpnext/pull/23282))
- Education Module fixes ([#23714](https://github.com/frappe/erpnext/pull/23714))
- Filter out cancelled entries in customer ledger summary ([#23205](https://github.com/frappe/erpnext/pull/23205))
- Fiscal Year and Tax Rates for Italy ([#23623](https://github.com/frappe/erpnext/pull/23623))
- Production Plan incorrect Work Order qty ([#23264](https://github.com/frappe/erpnext/pull/23264))
- Added new filters in the Batch-wise Balance History report ([#23676](https://github.com/frappe/erpnext/pull/23676))
- Update state code and union territory for Daman and Diu ([#22988](https://github.com/frappe/erpnext/pull/22988))
- Set Stock UOM in item while creating Material Request from Stock Entry ([#23436](https://github.com/frappe/erpnext/pull/23436))
- Sales Order to Purchase Order flow improvement ([#23357](https://github.com/frappe/erpnext/pull/23357))
- Student Admission and Student Applicant fixes ([#23515](https://github.com/frappe/erpnext/pull/23515))
- Loan disbursement amount validation ([#24000](https://github.com/frappe/erpnext/pull/24000))
- Making company address read-only in delivery note ([#23890](https://github.com/frappe/erpnext/pull/23890))
- BOM stock report color showing always red ([#23994](https://github.com/frappe/erpnext/pull/23994))
- Added filter for customer field in Issue ([#24051](https://github.com/frappe/erpnext/pull/24051))
- Added project link in timesheet form ([#23764](https://github.com/frappe/erpnext/pull/23764))
- Update integrations desk page ([#23767](https://github.com/frappe/erpnext/pull/23767))
- Place of supply change on address change ([#23941](https://github.com/frappe/erpnext/pull/23941))
- TDS calculation, skip invoices with "Apply Tax Withholding Amount" has disabled ([#23672](https://github.com/frappe/erpnext/pull/23672))
- Auto fetch serial nos with modified conversion factor ([#23854](https://github.com/frappe/erpnext/pull/23854))
- Default cost center in item master not set in stock entry ([#23877](https://github.com/frappe/erpnext/pull/23877))
- Incorrect de-link serial no and batch ([#23947](https://github.com/frappe/erpnext/pull/23947))
- Accounting for internal transfer invoices within same company ([#24021](https://github.com/frappe/erpnext/pull/24021))
- Multiple pricing rule with margin type as Percentage is not working ([#24205](https://github.com/frappe/erpnext/pull/24205))
- Added Purchase Order to Global Search ([#24055](https://github.com/frappe/erpnext/pull/24055))
- Cannot expand row in update items dialog ([#23839](https://github.com/frappe/erpnext/pull/23839))
- Maintain stock can't be changed it there is product bundle ([#23989](https://github.com/frappe/erpnext/pull/23989))
- SO to PO Mapping Issue ([#23820](https://github.com/frappe/erpnext/pull/23820))
- Asset with value zero doesn't show up in fixed asset register ([#24091](https://github.com/frappe/erpnext/pull/24091))
- Cannot save customer email & phone ([#23797](https://github.com/frappe/erpnext/pull/23797))
- Incorrect balance value in stock balance report ([#24048](https://github.com/frappe/erpnext/pull/24048))
- Payment Terms not fetched in Purchase Invoice from Purchase Receipt ([#23735](https://github.com/frappe/erpnext/pull/23735))
- Fix for LMS Sign Up link ([#23743](https://github.com/frappe/erpnext/pull/23743))
- Incorrect stock quantity if 'Allow Multiple Material Consumption… ([#24116](https://github.com/frappe/erpnext/pull/24116))
- Added wrong absent days calculation in salary slip ([#23897](https://github.com/frappe/erpnext/pull/23897))
- Purchase receipt to purchase invoice bill date mapping ([#23967](https://github.com/frappe/erpnext/pull/23967))
- Overriding po ([#24022](https://github.com/frappe/erpnext/pull/24022))
- Do not cancel reference document on Quality Inspection cancellation ([#24198](https://github.com/frappe/erpnext/pull/24198))
- Get formatted value in 'taxes' print template ([#24035](https://github.com/frappe/erpnext/pull/24035))
- Don't overrule Item Price via Pricing Rule Rate if 0 ([#23636](https://github.com/frappe/erpnext/pull/23636))
- Job card error handling for operations field ([#23991](https://github.com/frappe/erpnext/pull/23991))
- Validation for journal entry with 0 debit and credit values ([#23975](https://github.com/frappe/erpnext/pull/23975))
- Check if customer exists in product listing ([#24030](https://github.com/frappe/erpnext/pull/24030))
- Asset finance book posting date fix ([#23778](https://github.com/frappe/erpnext/pull/23778))
- Same source and target tables in Status Updater's update query ([#24110](https://github.com/frappe/erpnext/pull/24110))
- Asset finance book depreciation posting date fix ([#23833](https://github.com/frappe/erpnext/pull/23833))
- Ignore exception during leave ledger creation from patch ([#24005](https://github.com/frappe/erpnext/pull/24005))
- Added link of bank reconciliation and clearance in accounting desk page ([#23850](https://github.com/frappe/erpnext/pull/23850))
- Sales invoice add button from sales order dashboard ([#24077](https://github.com/frappe/erpnext/pull/24077))
- Incorrect calculation for consumed qty for subcontract item ([#23257](https://github.com/frappe/erpnext/pull/23257))
- Incorrect required_qty in Production Planning Report ([#24074](https://github.com/frappe/erpnext/pull/24074))
- Email digest user not found ([#23949](https://github.com/frappe/erpnext/pull/23949))
- Delete Receive at Warehouse entry on cancellation of Send to War… ([#24115](https://github.com/frappe/erpnext/pull/24115))
- Added TDS Payable account number and an error message ([#24065](https://github.com/frappe/erpnext/pull/24065))
- Override field_map for job card gantt ([#24155](https://github.com/frappe/erpnext/pull/24155))
- Old shopify order syncing date ([#23990](https://github.com/frappe/erpnext/pull/23990))
- Shipping chanrges not sync in erpnext from shopify ([#24114](https://github.com/frappe/erpnext/pull/24114))
- GSTR B2C report ([#24039](https://github.com/frappe/erpnext/pull/24039))
- Ignore cancelled entries in stock balance report ([#23757](https://github.com/frappe/erpnext/pull/23757))
- Stock ageing report not working ([#23923](https://github.com/frappe/erpnext/pull/23923))
- Incorrect assign to in Maintenance Schedule  ([#23831](https://github.com/frappe/erpnext/pull/23831))
- Improve UX of DATEV report ([#23892](https://github.com/frappe/erpnext/pull/23892))
- Set SLA variance in seconds for Duration fieldtype ([#23765](https://github.com/frappe/erpnext/pull/23765))
- dDouble exception in payroll ([#24078](https://github.com/frappe/erpnext/pull/24078))
- Make asset dashboard charts public ([#23751](https://github.com/frappe/erpnext/pull/23751))
- Don't copy terms and discount from SO to PO ([#23903](https://github.com/frappe/erpnext/pull/23903))
- Ignore doctypes on company transaction delete ([#23864](https://github.com/frappe/erpnext/pull/23864))
- Error handling in Upload Attendance  ([#23907](https://github.com/frappe/erpnext/pull/23907))
- Tax template update on customer address change ([#24160](https://github.com/frappe/erpnext/pull/24160))
- Not able to save bom ([#23910](https://github.com/frappe/erpnext/pull/23910))
- Enable Allow Auto Repeat for standard doctypes having auto_repeat field ([#23776](https://github.com/frappe/erpnext/pull/23776))
- Place of Supply fix in Sales Invoices ([#23785](https://github.com/frappe/erpnext/pull/23785))
- Opening invoices in GSTR-1 report ([#24117](https://github.com/frappe/erpnext/pull/24117))
- Partial serial no return issue ([#24208](https://github.com/frappe/erpnext/pull/24208))
- Import taxjar globally in the taxjar_integration module ([#24027](https://github.com/frappe/erpnext/pull/24027))
- Payroll attendance error ([#23887](https://github.com/frappe/erpnext/pull/23887))
- Loan application link on creating loan ([#23937](https://github.com/frappe/erpnext/pull/23937))
- POS item search includes non stock items ([#23914](https://github.com/frappe/erpnext/pull/23914))
- Paid amount in Sales Invoice POS return resets to 0 ([#24057](https://github.com/frappe/erpnext/pull/24057))
- Fiscal year can be shorter than 12 months ([#23838](https://github.com/frappe/erpnext/pull/23838))
- Loan repayment type option remove ([#23582](https://github.com/frappe/erpnext/pull/23582))
- Item wise tax calculation ([#23744](https://github.com/frappe/erpnext/pull/23744))
- Enabling track changes for stock settings ([#23982](https://github.com/frappe/erpnext/pull/23982))
- Added link of bank reconciliation and clearance in accounting desk page ([#23809](https://github.com/frappe/erpnext/pull/23809))
- Location data on Asset to use command(make_demo) ([#23825](https://github.com/frappe/erpnext/pull/23825))
- Handle Account and Item None not found in Opening Invoice Creation Tool ([#23559](https://github.com/frappe/erpnext/pull/23559))
- Multiple subcontracting issues ([#23662](https://github.com/frappe/erpnext/pull/23662))
- Sequence id override with workstation column ([#23810](https://github.com/frappe/erpnext/pull/23810))
- Leave policy dashboard fix and roles ([#24170](https://github.com/frappe/erpnext/pull/24170))
- Scan barcode does not update barcode item field in sales order ([#24090](https://github.com/frappe/erpnext/pull/24090))
- Item price duplicate checking ([#23408](https://github.com/frappe/erpnext/pull/23408))
- Tax template update on supplier change for India ([#24060](https://github.com/frappe/erpnext/pull/24060))
- Consumed qty logic for subcontracted raw materials ([#23314](https://github.com/frappe/erpnext/pull/23314))
- Finance book not getting added in journal Entry of asset value adjustment ([#24100](https://github.com/frappe/erpnext/pull/24100))
- Set proper state code in ewaybill JSON when GST category is SEZ ([#23953](https://github.com/frappe/erpnext/pull/23953))
- Copying po no when mapping doc ([#23729](https://github.com/frappe/erpnext/pull/23729))
- Duplicate items validation for POS Invoice when allow multiple items is disabled ([#23896](https://github.com/frappe/erpnext/pull/23896))
- Do not allow Company as accounting dimension ([#23749](https://github.com/frappe/erpnext/pull/23749))
- Validation for duplicate Tax Category ([#23978](https://github.com/frappe/erpnext/pull/23978))
- Therapy plan and session fixes ([#23817](https://github.com/frappe/erpnext/pull/23817))
- Pricing rule with transaction not working for additional product ([#24053](https://github.com/frappe/erpnext/pull/24053))
- Inpatient Medication Order and Entry fixes ([#23799](https://github.com/frappe/erpnext/pull/23799))
- Avoid using SQL query to get fiscal year dates ([#24050](https://github.com/frappe/erpnext/pull/24050))
- Auto Statewise gst tax template ([#23832](https://github.com/frappe/erpnext/pull/23832))
- On save sequence id column override with workstation ([#23812](https://github.com/frappe/erpnext/pull/23812))
- Multiple pricing rules are not working on selling side ([#22711](https://github.com/frappe/erpnext/pull/22711))
- Salary slip popup error ([#24192](https://github.com/frappe/erpnext/pull/24192))
- Multiple pricing rule with margin type as Percentage is not working ([#24204](https://github.com/frappe/erpnext/pull/24204))
- Allow statistical component in salary structure. ([#24424](https://github.com/frappe/erpnext/pull/24424))
- Set current asset value before calculating difference amount ([#24119](https://github.com/frappe/erpnext/pull/24119))
- To use Stock UoM in BOM Stock Report ([#24339](https://github.com/frappe/erpnext/pull/24339))
- Accounting entries of asset when submitting purchase receipt ([#24191](https://github.com/frappe/erpnext/pull/24191))
- Batch/Serial Selector for Scanned Batched Item ([#24338](https://github.com/frappe/erpnext/pull/24338))
- Link timesheets with corresponding projects ([#24346](https://github.com/frappe/erpnext/pull/24346))
- Material request wrong status issue ([#24019](https://github.com/frappe/erpnext/pull/24019))
- UX issues in e-invoicing ([#24358](https://github.com/frappe/erpnext/pull/24358))
- Company Wise Valuation Rate for RM in BOM ([#24324](https://github.com/frappe/erpnext/pull/24324))
- Stock ageing should not take cancelled stock entries. ([#24437](https://github.com/frappe/erpnext/pull/24437))
- Partial loan security unpledging ([#24252](https://github.com/frappe/erpnext/pull/24252))
- Asset depreciation ledger ([#24226](https://github.com/frappe/erpnext/pull/24226))
- Back Update from QC based on Batch No ([#24329](https://github.com/frappe/erpnext/pull/24329))
- Fix for not having fiscal year while creating new company ([#24130](https://github.com/frappe/erpnext/pull/24130))
- E-invoice print format not showing other charges ([#24474](https://github.com/frappe/erpnext/pull/24474))
- Tax template update on customer address change ([#24146](https://github.com/frappe/erpnext/pull/24146))
- Do not manufacture same serial no multiple times ([#24164](https://github.com/frappe/erpnext/pull/24164))
- Ignore group cost center validation for period closing voucher ([#24375](https://github.com/frappe/erpnext/pull/24375))
- Partial serial no return issue ([#24207](https://github.com/frappe/erpnext/pull/24207))
- GSTR-1 double entry issue ([#24376](https://github.com/frappe/erpnext/pull/24376))
- Not able to create dunning from sales invoice ([#24349](https://github.com/frappe/erpnext/pull/24349))
- Set company in leave allocation and leave ledger entry ([#24296](https://github.com/frappe/erpnext/pull/24296))
- Allow leave policy assignment to be canceled. ([#24265](https://github.com/frappe/erpnext/pull/24265))
- Removed all day event from shift assignment calendar ([#24397](https://github.com/frappe/erpnext/pull/24397))
- Tax calculation on salary slip for the first month ([#24272](https://github.com/frappe/erpnext/pull/24272))
- Validate tax template for tax category ([#24402](https://github.com/frappe/erpnext/pull/24402))
- Numeric/Non-numeric QI UX ([#24517](https://github.com/frappe/erpnext/pull/24517))
- Finished good produced qty validation ([#24220](https://github.com/frappe/erpnext/pull/24220))
- Incorrect serial no in the subcontracted purchase receipt ([#24354](https://github.com/frappe/erpnext/pull/24354))
- Don't validate warehouse values between Material Request and Stock Entry ([#24294](https://github.com/frappe/erpnext/pull/24294))
- Don't cancel job card if manufacturing entry has made ([#24063](https://github.com/frappe/erpnext/pull/24063))
- Subscription prepaid date validation ([#24356](https://github.com/frappe/erpnext/pull/24356))
- Payment Period based on invoice date report fix/refactor ([#24378](https://github.com/frappe/erpnext/pull/24378))
- Drop ship partial order fixed ([#24072](https://github.com/frappe/erpnext/pull/24072))
- Payment entry multi-currency issue ([#24332](https://github.com/frappe/erpnext/pull/24332))
- Multiple pricing rule issue ([#24515](https://github.com/frappe/erpnext/pull/24515))
- Last purchase rate not updating when voucher cancelled if only one voucher is present ([#24322](https://github.com/frappe/erpnext/pull/24322))
- Do not cancel reference document on Quality Inspection cancellation ([#24197](https://github.com/frappe/erpnext/pull/24197))
- Refactored fetching & validating address from erpnext rather than gst portal ([#24297](https://github.com/frappe/erpnext/pull/24297))
- Opportunity Status fix ([#22944](https://github.com/frappe/erpnext/pull/22944))
- Fixed stock and account balance syncing ([#24644](https://github.com/frappe/erpnext/pull/24644))
- Fixed incorrect stock ledger qty in the stock ledger report and bin ([#24649](https://github.com/frappe/erpnext/pull/24649))
- Fixed Consolidated Financial Statement report ([#24580](https://github.com/frappe/erpnext/pull/24580))
- Repost incompleted backdated transactions ([#24991](https://github.com/frappe/erpnext/pull/24991))
- Unequal debit and credit issue on RCM Invoice ([#24838](https://github.com/frappe/erpnext/pull/24838))
- Period list for exponential smoothing forecasting report ([#24983](https://github.com/frappe/erpnext/pull/24983))
- POS Opening Entry with empty balance detail rows ([#24891](https://github.com/frappe/erpnext/pull/24891))
- Use account_name only in consolidated report ([#24840](https://github.com/frappe/erpnext/pull/24840))
- Validation of job card in stock entry ([#24882](https://github.com/frappe/erpnext/pull/24882))
- Incorrect Nil Exempt and Non GST amount in GSTR3B report ([#24918](https://github.com/frappe/erpnext/pull/24918))
- TDS check getting checked after reload ([#24973](https://github.com/frappe/erpnext/pull/24973))
- Membership and Donation API fixes ([#24900](https://github.com/frappe/erpnext/pull/24900))
- Allow zero valuation in stock reconciliation ([#24985](https://github.com/frappe/erpnext/pull/24985))
- Simplified logic for additional salary ([#24907](https://github.com/frappe/erpnext/pull/24907))
- Allow to select item code in batch naming ([#24825](https://github.com/frappe/erpnext/pull/24825))
- Membership renewal validation (#24963) ([#24964](https://github.com/frappe/erpnext/pull/24964))
</details>