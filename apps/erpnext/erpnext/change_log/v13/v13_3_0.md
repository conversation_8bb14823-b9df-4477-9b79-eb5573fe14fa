# Version 13.3.0 Release Notes

### Features & Enhancements

- Purchase receipt creation from purchase invoice ([#25126](https://github.com/frappe/erpnext/pull/25126))
- New Document Transaction Deletion ([#25354](https://github.com/frappe/erpnext/pull/25354))
- Employee Referral ([#24997](https://github.com/frappe/erpnext/pull/24997))
- Add Create Expense Claim button in Delivery Trip ([#25526](https://github.com/frappe/erpnext/pull/25526))
- Reduced rate of asset depreciation as per IT Act ([#25648](https://github.com/frappe/erpnext/pull/25648))
- Improve DATEV export ([#25238](https://github.com/frappe/erpnext/pull/25238))
- Add pick batch button ([#25413](https://github.com/frappe/erpnext/pull/25413))
- Enable custom field search on POS ([#25421](https://github.com/frappe/erpnext/pull/25421))
- New check field in subscriptions for (not) submitting invoices ([#25394](https://github.com/frappe/erpnext/pull/25394))
- Show POS reserved stock in stock projected qty report ([#25593](https://github.com/frappe/erpnext/pull/25593))
- e-way bill validity field ([#25555](https://github.com/frappe/erpnext/pull/25555))
- Significant reduction in time taken to save sales documents ([#25475](https://github.com/frappe/erpnext/pull/25475))

### Fixes

- Bank statement import via google sheet ([#25677](https://github.com/frappe/erpnext/pull/25677))
- Invoices not getting fetched during payment reconciliation ([#25598](https://github.com/frappe/erpnext/pull/25598))
- Error on applying TDS without party ([#25632](https://github.com/frappe/erpnext/pull/25632))
- Allow to cancel loan with cancelled repayment entry ([#25507](https://github.com/frappe/erpnext/pull/25507))
- Can't open general ledger from consolidated financial report ([#25542](https://github.com/frappe/erpnext/pull/25542))
- Add 'Partially Received' to Status drop-down list in Material Request ([#24857](https://github.com/frappe/erpnext/pull/24857))
- Updated item filters for material request ([#25531](https://github.com/frappe/erpnext/pull/25531))
- Added validation in stock entry to check duplicate serial nos ([#25611](https://github.com/frappe/erpnext/pull/25611))
- Update shopify api version ([#25600](https://github.com/frappe/erpnext/pull/25600))
- Dialog variable assignment after definition in POS ([#25680](https://github.com/frappe/erpnext/pull/25680))
- Added tax_types list ([#25587](https://github.com/frappe/erpnext/pull/25587))
- Include search fields in Project Link field query ([#25505](https://github.com/frappe/erpnext/pull/25505))
- Item stock levels displaying inconsistently ([#25506](https://github.com/frappe/erpnext/pull/25506))
- Change today to now to get data for reposting ([#25703](https://github.com/frappe/erpnext/pull/25703))
- Parameter for get_filtered_list_for_consolidated_report in consolidated balance sheet ([#25700](https://github.com/frappe/erpnext/pull/25700))
- Minor fixes in loan ([#25546](https://github.com/frappe/erpnext/pull/25546))
- Fieldname when updating docfield property ([#25516](https://github.com/frappe/erpnext/pull/25516))
- Use get_serial_nos for splitting ([#25590](https://github.com/frappe/erpnext/pull/25590))
- Show item's full name on hover over item in POS ([#25554](https://github.com/frappe/erpnext/pull/25554))
- Stock ledger entry created against draft stock entry ([#25540](https://github.com/frappe/erpnext/pull/25540))
- Incorrect expense account set in pos invoice ([#25543](https://github.com/frappe/erpnext/pull/25543))
- Stock balance and batch-wise balance history report showing different closing stock ([#25575](https://github.com/frappe/erpnext/pull/25575))
- Make strings translatable ([#25521](https://github.com/frappe/erpnext/pull/25521))
- Serial no changed after saving stock reconciliation ([#25541](https://github.com/frappe/erpnext/pull/25541))
- Ignore fraction difference while making round off gl entry ([#25438](https://github.com/frappe/erpnext/pull/25438))
- Sync shopify customer addresses ([#25481](https://github.com/frappe/erpnext/pull/25481))
- Total stock summary report not working ([#25551](https://github.com/frappe/erpnext/pull/25551))
- Rename field has not updated value of deposit and withdrawal fields ([#25545](https://github.com/frappe/erpnext/pull/25545))
- Unexpected keyword argument 'merge_logs' ([#25489](https://github.com/frappe/erpnext/pull/25489))
- Validation message of quality inspection in purchase receipt ([#25667](https://github.com/frappe/erpnext/pull/25667))
- Added is_stock_item filter ([#25530](https://github.com/frappe/erpnext/pull/25530))
- Fetch total stock at company in PO ([#25532](https://github.com/frappe/erpnext/pull/25532))
- Updated filters for process statement of accounts ([#25384](https://github.com/frappe/erpnext/pull/25384))
- Incorrect expense account set in pos invoice ([#25571](https://github.com/frappe/erpnext/pull/25571))
- Client script breaking while settings tax labels ([#25653](https://github.com/frappe/erpnext/pull/25653))
- Empty payment term column in accounts receivable report ([#25556](https://github.com/frappe/erpnext/pull/25556))
- Designation insufficient permission on lead doctype. ([#25331](https://github.com/frappe/erpnext/pull/25331))
- Force https for shopify webhook registration ([#25630](https://github.com/frappe/erpnext/pull/25630))
- Patch regional fields for old companies ([#25673](https://github.com/frappe/erpnext/pull/25673))
- Woocommerce order sync issue ([#25692](https://github.com/frappe/erpnext/pull/25692))
- Allow to receive same serial numbers multiple times ([#25471](https://github.com/frappe/erpnext/pull/25471))
- Update Allocated amount after Paid Amount is changed in PE ([#25515](https://github.com/frappe/erpnext/pull/25515))
- Updating Standard Notification's channel field ([#25564](https://github.com/frappe/erpnext/pull/25564))
- Report summary showing inflated values when values are accumulated in Group Company ([#25577](https://github.com/frappe/erpnext/pull/25577))
- UI fixes related to overflowing payment section ([#25652](https://github.com/frappe/erpnext/pull/25652))
- List invoices in Payment Reconciliation Payment ([#25524](https://github.com/frappe/erpnext/pull/25524))
- Ageing errors in PSOA ([#25490](https://github.com/frappe/erpnext/pull/25490))
- Prevent spurious defaults for items when making prec from dnote ([#25559](https://github.com/frappe/erpnext/pull/25559))
- Stock reconciliation getting time out error during submission ([#25557](https://github.com/frappe/erpnext/pull/25557))
- Timesheet filter date exclusive issue ([#25626](https://github.com/frappe/erpnext/pull/25626))
- Update cost center in the item table fetched from POS Profile ([#25609](https://github.com/frappe/erpnext/pull/25609))
- Updated modified time in purchase invoice to pull new fields ([#25678](https://github.com/frappe/erpnext/pull/25678))
- Stock and Accounts Settings form refactor ([#25534](https://github.com/frappe/erpnext/pull/25534))
- Payment amount showing in foreign currency ([#25292](https://github.com/frappe/erpnext/pull/25292))