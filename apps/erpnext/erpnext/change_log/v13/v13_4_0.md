# Version 13.4.0 Release Notes

### Features & Enhancements

- Multiple GST enhancement and fixes ([#25249](https://github.com/frappe/erpnext/pull/25249))
- Linking supplier with an item group for filtering items ([#25683](https://github.com/frappe/erpnext/pull/25683))
- Leave Policy Assignment Refactor ([#24327](https://github.com/frappe/erpnext/pull/24327))
- Dimension-wise Accounts Balance Report ([#25260](https://github.com/frappe/erpnext/pull/25260))
- Show net values in Party Accounts ([#25714](https://github.com/frappe/erpnext/pull/25714))
- Add pending qty section to batch/serial selector dialog ([#25519](https://github.com/frappe/erpnext/pull/25519))
- enhancements in Training Event ([#25782](https://github.com/frappe/erpnext/pull/25782))
- Refactored timesheet ([#25701](https://github.com/frappe/erpnext/pull/25701))

### Fixes

- Process Statement of Accounts formatting ([#25777](https://github.com/frappe/erpnext/pull/25777))
- Removed serial no validation for sales invoice ([#25817](https://github.com/frappe/erpnext/pull/25817))
- Fetch email id from dialog box in pos past order summary ([#25808](https://github.com/frappe/erpnext/pull/25808))
- Don't map set warehouse from delivery note to purchase receipt ([#25672](https://github.com/frappe/erpnext/pull/25672))
- Apply permission while selecting projects ([#25765](https://github.com/frappe/erpnext/pull/25765))
- Error on adding bank account to plaid ([#25658](https://github.com/frappe/erpnext/pull/25658))
- Set disable rounded total if it is globally enabled ([#25789](https://github.com/frappe/erpnext/pull/25789))
- Wrong amount on CR side in general ledger report for customer when different account currencies are involved ([#25654](https://github.com/frappe/erpnext/pull/25654))
- Stock move dialog duplicate submit actions (V13) ([#25486](https://github.com/frappe/erpnext/pull/25486))
- Cashflow mapper not showing data ([#25815](https://github.com/frappe/erpnext/pull/25815))
- Ignore rounding diff while importing JV using data import ([#25816](https://github.com/frappe/erpnext/pull/25816))
- Woocommerce order sync issue ([#25688](https://github.com/frappe/erpnext/pull/25688))
- Expected amount in pos closing payments table ([#25737](https://github.com/frappe/erpnext/pull/25737))
- Show only company addresses for ITC reversal entry ([#25867](https://github.com/frappe/erpnext/pull/25867))
- Timeout error while loading warehouse tree ([#25694](https://github.com/frappe/erpnext/pull/25694))
- Plaid Withdrawals and Deposits are recorded incorrectly ([#25784](https://github.com/frappe/erpnext/pull/25784))
- Return case for item with available qty equal to one ([#25760](https://github.com/frappe/erpnext/pull/25760))
- The status of repost item valuation showing In Progress since long time ([#25754](https://github.com/frappe/erpnext/pull/25754))
- Updated applicable charges form in landed cost voucher ([#25732](https://github.com/frappe/erpnext/pull/25732))
- Rearrange buttons for Company DocType ([#25617](https://github.com/frappe/erpnext/pull/25617))
- Show uom for item in selector dialog ([#25697](https://github.com/frappe/erpnext/pull/25697))
- Warehouse not found in stock entry ([#25776](https://github.com/frappe/erpnext/pull/25776))
- Use dictionary filter instead of list (bp #25874 pre-release) ([#25875](https://github.com/frappe/erpnext/pull/25875))
- Send emails on rfq submit ([#25695](https://github.com/frappe/erpnext/pull/25695))
- Cannot bypass e-invoicing for non gst item invoices ([#25759](https://github.com/frappe/erpnext/pull/25759))
- Validation message of quality inspection in purchase receipt ([#25666](https://github.com/frappe/erpnext/pull/25666))
- Dialog variable assignment after definition in POS ([#25681](https://github.com/frappe/erpnext/pull/25681))
- Wrong quantity after transaction for parallel stock transactions ([#25779](https://github.com/frappe/erpnext/pull/25779))
- Item Variant Details Report ([#25797](https://github.com/frappe/erpnext/pull/25797))
- Duplicate stock entry on multiple click ([#25742](https://github.com/frappe/erpnext/pull/25742))
- Bank statement import via google sheet ([#25676](https://github.com/frappe/erpnext/pull/25676))
- Change today to now to get data for reposting ([#25702](https://github.com/frappe/erpnext/pull/25702))
- Parameter for get_filtered_list_for_consolidated_report in consolidated balance sheet ([#25698](https://github.com/frappe/erpnext/pull/25698))
- Ageing error in PSOA ([#25857](https://github.com/frappe/erpnext/pull/25857))
- Breaking cost center validation ([#25660](https://github.com/frappe/erpnext/pull/25660))
- Project filter for Kanban Board ([#25744](https://github.com/frappe/erpnext/pull/25744))
- Show allow zero valuation only when auto checked ([#25778](https://github.com/frappe/erpnext/pull/25778))
- Missing cost center message on creating gl entries ([#25755](https://github.com/frappe/erpnext/pull/25755))
- Address template with upper filter throws jinja error ([#25756](https://github.com/frappe/erpnext/pull/25756))
