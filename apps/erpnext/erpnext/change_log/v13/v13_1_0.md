# Version 13.1.0 Release Notes

### Features

- Recursive pricing rule ([#24922](https://github.com/frappe/erpnext/pull/24922))
- Discount configuration on early payments ([#24586](https://github.com/frappe/erpnext/pull/24586))
- Bulk e-invoice generation ([#24969](https://github.com/frappe/erpnext/pull/24969))
- Employee Self Service ([#24408](https://github.com/frappe/erpnext/pull/24408))
- Share doc with employee approvers if they don't have access ([#25190](https://github.com/frappe/erpnext/pull/25190))
- Price margin in buying ([#24685](https://github.com/frappe/erpnext/pull/24685))
- Allow changing Work Stations in Work Order & Job Card ([#24897](https://github.com/frappe/erpnext/pull/24897))
- Add document type field for e-invoicing (Italy) ([#25256](https://github.com/frappe/erpnext/pull/25256))
- Add checkbox for disabling leave notification in HR Settings ([#24877](https://github.com/frappe/erpnext/pull/24877))
- Enhancements in Material Request Plan Item in Production Plan ([#25025](https://github.com/frappe/erpnext/pull/25025))


### Fixes and Enhancements
- Mode of payments disappear on loading draft pos invoice ([#24917](https://github.com/frappe/erpnext/pull/24917))
- Sales order not saving due type mismatch in promo scheme (#24748) ([#25222](https://github.com/frappe/erpnext/pull/25222))
- Zero amount completed delivery notes being shown in Sales Invoice get items ([#25317](https://github.com/frappe/erpnext/pull/25317))
- Incorrect status creating PR from PO after creating PI ([#25109](https://github.com/frappe/erpnext/pull/25109))
- Precision and formatted document for stock level in item dashboard. ([#24921](https://github.com/frappe/erpnext/pull/24921))
- Precision issues while allocating advance amount ([#25086](https://github.com/frappe/erpnext/pull/25086))
- Round off final tax amount instead of current tax amount ([#25188](https://github.com/frappe/erpnext/pull/25188))
- Redesign fixes ([#24896](https://github.com/frappe/erpnext/pull/24896))
- TDS check getting checked after reload ([#24972](https://github.com/frappe/erpnext/pull/24972))
- Github Action not failing when tests fail ([#24867](https://github.com/frappe/erpnext/pull/24867))
- Calculate 80g certificate amount on validate for memberships ([#24925](https://github.com/frappe/erpnext/pull/24925))
- Purchase from registered composition dealer ([#25040](https://github.com/frappe/erpnext/pull/25040))
- Reduce number of queries for checking if future SL entry exists ([#24881](https://github.com/frappe/erpnext/pull/24881))
- Remove unwanted parameter in calculate_rate_and_amount ([#24883](https://github.com/frappe/erpnext/pull/24883))
- Membership renewal validation ([#24963](https://github.com/frappe/erpnext/pull/24963))
- Not able to save material request ([#25112](https://github.com/frappe/erpnext/pull/25112))
- POS print receipt ([#25330](https://github.com/frappe/erpnext/pull/25330))
- Supplier was not able to Submit RFQ due to insufficient permission ([#24622](https://github.com/frappe/erpnext/pull/24622))
- Unequal debit and credit issue on RCM Invoice ([#24836](https://github.com/frappe/erpnext/pull/24836))
- Picked Qty conversion from Stock Qty to Qty while creating DN from Pick List ([#25105](https://github.com/frappe/erpnext/pull/25105))
- Salary Structure object has no attribute set_totals ([#25113](https://github.com/frappe/erpnext/pull/25113))
- Incorrect Nil Exempt and Non GST amount in GSTR3B report ([#24916](https://github.com/frappe/erpnext/pull/24916))
- Add method for regional round off account back ([#24893](https://github.com/frappe/erpnext/pull/24893))
- Employee profile pic upload access for erpnext user ([#25022](https://github.com/frappe/erpnext/pull/25022))
- Make filters for payroll entry ([#25386](https://github.com/frappe/erpnext/pull/25386))
- Fix dynamically changing grid properties ([#25310](https://github.com/frappe/erpnext/pull/25310))
- Consider paid repayment entries in subsequent loan repayments ([#25271](https://github.com/frappe/erpnext/pull/25271))
- Allow duplicate additional salaries ([#24842](https://github.com/frappe/erpnext/pull/24842))
- Object referencing the same address issue ([#25159](https://github.com/frappe/erpnext/pull/25159))
- Validating party currency with doc currency ([#24318](https://github.com/frappe/erpnext/pull/24318))
- Non Profit fixes ([#25060](https://github.com/frappe/erpnext/pull/25060))
- Additional Salary component amount not getting set ([#25356](https://github.com/frappe/erpnext/pull/25356))
- Allow user to update exchange rate in Multi-currency LCV ([#24912](https://github.com/frappe/erpnext/pull/24912))
- Allow creating stock entry based on work order for customer provided items ([#24885](https://github.com/frappe/erpnext/pull/24885))
- Create property setters for shorter naming series on setup ([#25128](https://github.com/frappe/erpnext/pull/25128))
- Add GST category field in Delivery Note ([#25053](https://github.com/frappe/erpnext/pull/25053))
- Ignore Permission for Leave Ledger Entry ([#25172](https://github.com/frappe/erpnext/pull/25172))
- Pending shortfall update  on processing loan security shortfall ([#24971](https://github.com/frappe/erpnext/pull/24971))
- Added flag for dont_fetch_price_list_rate in transaction ([#25041](https://github.com/frappe/erpnext/pull/25041))
- Exchange Rate not getting set in Salary Slip ([#25004](https://github.com/frappe/erpnext/pull/25004))
- Repost not completed backdated transactions ([#24980](https://github.com/frappe/erpnext/pull/24980))
- frappe.whitelist for doc methods ([#25230](https://github.com/frappe/erpnext/pull/25230))
- Opportunity-quotation mapping order status ([#25001](https://github.com/frappe/erpnext/pull/25001))
- GST on freight charge in e-invoicing ([#25000](https://github.com/frappe/erpnext/pull/25000))
- Role to override maintain same rate check in transactions ([#25193](https://github.com/frappe/erpnext/pull/25193))
- Added blank option for status in report related to issue ([#25082](https://github.com/frappe/erpnext/pull/25082))
- Cashier query in POS Opening/Closing Entry ([#25399](https://github.com/frappe/erpnext/pull/25399))
- Lead Source's module ([#24583](https://github.com/frappe/erpnext/pull/24583))
- Hide alt tag if item is not shown in website ([#24937](https://github.com/frappe/erpnext/pull/24937))
- Ignore Customer Group Perm on All Products page ([#25397](https://github.com/frappe/erpnext/pull/25397))
- Give first preference to loan security on repayment ([#25212](https://github.com/frappe/erpnext/pull/25212))
- Add shortfall ratio in Loan Security Shortfall ([#25138](https://github.com/frappe/erpnext/pull/25138))
- Condition for SLA status banner ([#25261](https://github.com/frappe/erpnext/pull/25261))
- Component amount calculation based on formula with abbr not working ([#25117](https://github.com/frappe/erpnext/pull/25117))
- Remove gst name validation for purchase Invoice ([#25235](https://github.com/frappe/erpnext/pull/25235))
- Do not fetch stopped MR in production plan ([#25063](https://github.com/frappe/erpnext/pull/25063))
- Backport missing commits to develop branch ([#25305](https://github.com/frappe/erpnext/pull/25305))
- UOM length unit in global setup list is empty ([#24855](https://github.com/frappe/erpnext/pull/24855))
- Round total quantity in job card ([#25240](https://github.com/frappe/erpnext/pull/25240))
- Default total_estimated_cost to zero ([#24939](https://github.com/frappe/erpnext/pull/24939))
- Serial no refresh issue ([#25127](https://github.com/frappe/erpnext/pull/25127))
- Correct calculation for discount amount when margin is set ([#25179](https://github.com/frappe/erpnext/pull/25179))
- Get correct holiday list when calculating dates; test fixes ([#24901](https://github.com/frappe/erpnext/pull/24901))
- POS print receipt ([#24924](https://github.com/frappe/erpnext/pull/24924))
- Condition for setting agreement status ([#25255](https://github.com/frappe/erpnext/pull/25255))
- Loan Repayment entry cancellation on salary slip cancel ([#24879](https://github.com/frappe/erpnext/pull/24879))
- Add company validation for e-invoicing ([#25349](https://github.com/frappe/erpnext/pull/25349))
- Query values incorrectly escaped while back updating Quality Inspection ([#25118](https://github.com/frappe/erpnext/pull/25118))
- Update Bin via Update Item on Purchase/Sales Order  ([#23509](https://github.com/frappe/erpnext/pull/23509))
- Declare data before assigning ([#25287](https://github.com/frappe/erpnext/pull/25287))
- Do not set standard link in Sales Invoice as custom ([#25096](https://github.com/frappe/erpnext/pull/25096))
- Hide serial and batch selector in Stock Entry ([#25107](https://github.com/frappe/erpnext/pull/25107))
- Taxable value including Freight and Forwarding charges in GSTR-1 Report ([#25290](https://github.com/frappe/erpnext/pull/25290))
- Remove nonexistent method from pick list ([#25279](https://github.com/frappe/erpnext/pull/25279))
- Allow zero valuation in stock reconciliation ([#24888](https://github.com/frappe/erpnext/pull/24888))
- Place of supply of e-invoicing ([#25148](https://github.com/frappe/erpnext/pull/25148))
- Delivery note print error ([#25080](https://github.com/frappe/erpnext/pull/25080))
- Fix Payment references from disappearing on adding Cost Center in Payment Entry ([#24831](https://github.com/frappe/erpnext/pull/24831))
- Company field in Warehouse ([#25196](https://github.com/frappe/erpnext/pull/25196))
- Available employee for selection ([#25378](https://github.com/frappe/erpnext/pull/25378))
- Cannot set qty to less than zero ([#25258](https://github.com/frappe/erpnext/pull/25258))
- Don't delete mode of payment account details while deleting comp… ([#25217](https://github.com/frappe/erpnext/pull/25217))
- Exclude current doc while validation. ([#24914](https://github.com/frappe/erpnext/pull/24914))
- POS Opening Entry with empty balance detail rows ([#24876](https://github.com/frappe/erpnext/pull/24876))
- Unable to submit stock entry ([#25033](https://github.com/frappe/erpnext/pull/25033))
- BOM cost test case ([#25242](https://github.com/frappe/erpnext/pull/25242))
- Filter for employees in salary slip ([#25361](https://github.com/frappe/erpnext/pull/25361))
- Added correct path in hooks ([#24862](https://github.com/frappe/erpnext/pull/24862))
- Patch regional fields for old companies ([#24988](https://github.com/frappe/erpnext/pull/24988))
- consolidated sales invoice posting date ([#25119](https://github.com/frappe/erpnext/pull/25119))
- Don't set "Company:company:default_currency" as default for currency link fields ([#25095](https://github.com/frappe/erpnext/pull/25095))
- Healthcare lab module rename fields ([#25276](https://github.com/frappe/erpnext/pull/25276))
- Error message compensatory leave request ([#25206](https://github.com/frappe/erpnext/pull/25206))
- Adding company link to e invoice settings patch condition ([#25301](https://github.com/frappe/erpnext/pull/25301))
- Membership and Donation API fixes ([#24900](https://github.com/frappe/erpnext/pull/24900))
- Set correct ack no. on irn generation ([#25251](https://github.com/frappe/erpnext/pull/25251))
- Report Issue Summary fix for zero issues ([#24934](https://github.com/frappe/erpnext/pull/24934))
- Validation msg for TransDocNo e-invoicing ([#25121](https://github.com/frappe/erpnext/pull/25121))
- Correct state code for 'Other Territory' ([#24993](https://github.com/frappe/erpnext/pull/24993))
- Commit individual SLE rename for large datasets (develop) ([#25084](https://github.com/frappe/erpnext/pull/25084))
- Remove shipping address GSTIN validation for e-invoice ([#25153](https://github.com/frappe/erpnext/pull/25153))
- Period list for exponential smoothing forecasting report ([#24982](https://github.com/frappe/erpnext/pull/24982))
- Customer creation from shopping cart ([#25136](https://github.com/frappe/erpnext/pull/25136))
- Simplified logic for additional salary ([#24824](https://github.com/frappe/erpnext/pull/24824))
- Item wise tax rate for consolidated POS invoice ([#25029](https://github.com/frappe/erpnext/pull/25029))
- Column width in Recruitment analytics report ([#25003](https://github.com/frappe/erpnext/pull/25003))
- Filter Bank Account drop-down list in Bank Reconciliation Tool ([#24873](https://github.com/frappe/erpnext/pull/24873))
- Payroll issues ([#24540](https://github.com/frappe/erpnext/pull/24540))
- PO not created against all selected suppliers (drop shipping) ([#24863](https://github.com/frappe/erpnext/pull/24863))
- Can't multiply sequence by non-int of type 'float' ([#25092](https://github.com/frappe/erpnext/pull/25092))
- Make Discharge Schedule Date as Datetime ([#24940](https://github.com/frappe/erpnext/pull/24940))
- Serial no trim issue ([#24949](https://github.com/frappe/erpnext/pull/24949))
