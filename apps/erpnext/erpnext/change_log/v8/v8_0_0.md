#### Enhanced POS
- Based on community inputs, we have enhanced the POS feature.
- Now, POS has a dedicated numeric keypad to ease the calculations.
- Customer can be added or modified directly from the POS screen. 
- Items can also be filtered based on Item Group.

#### Employee Loan
- This feature enables a company to manage employee loans.
- Employees can request loans, which are then reviewed and approved.
- For the approved loans, repayment schedule for the entire loan cycle can be generated and automatic deduction from salary can also be set up.

#### Multiple UOMs in Selling
- Now, you can have different UOMs for selling an item.
- For instance, if you have a pencil for sell, then you can store it in Nos and sell it in boxes.

#### Accrual system in Payroll and Expense Claim
- Accounting of Payroll and Expense Claim is now based on accrual system.

#### Customer Feedback
- This feature will allow you to ask a customer to rate your service.
- You can configure Feedback Trigger, just like we setup an Notification. It will send an email to customer asking for feedback. Customer's Feedback will be updated in the relevant.
- You can also check Feedback Rating report for daily average rating and trend.

#### School Assessment Module
- Make Assessment Plan with defined assessment rules
- Track Assessment Results against the Assessment Plan

#### Minor Enhancements
- Optimised the logic of GL Entry reposting for future stock transactions
- Half Day leave can be marked now while applying leaves for multiple days
- In Purchase Order, get items from material requests based on possible supplier
- A dedicated page for marking Student Attendance and Monthly Attendance Sheet report

