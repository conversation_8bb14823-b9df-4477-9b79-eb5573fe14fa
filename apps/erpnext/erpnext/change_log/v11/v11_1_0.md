- Enhancements

	- Accounting

		- Company Tree
		- Multiple Finance Books
		- Bank Statement Upload
		- Tax Withholding
		- Subscriptions
		- Deferred Revenue and Expenses
		- Exchange Rate Revaluation
		- Inter Company Transactions
		- Standalone Credit / Debit Note
		- Reverse Journal Entry
		- Enhanced Bank Guarantee
		- Cost Center Numbering
		- Loyalty Points Management
		- Enhanced General ledger report

	- Human Resources

		- Department Hierarchy
		- Leave Management
		- Leave Period
		- New Leave Types
		- Leave Encashment
		- Compensatory Leave
		- Attendance Request
		- Enhanced Payroll
		- New Salary Structure
		- Additional Salary
		- Payroll Period
		- Employee Benefits
		- Employee Tax Exemptions
		- Auto Calculation of Tax Deduction
		- Enhanced Salary Processing
		- Employee Onboarding
		- Employee Separation
		- Employee Transfer
		- Employee Promotion
		- Employee Incentive
		- Retention Bonus
		- Shift Planning
		- Staffing Plan

	- Asset Management

		- Capital-Work-in-Progress (CWIP) Accounting
		- Multiple depreciation schedule based on finance book
		- Asset Value Adjustment
		- Improved Asset Movement between location or employee
		- New Depreciation method Written Down Value (WDV)

	- POS

		- Allow draft mode print in online POS
		- Allowed print before pay
		- Save the invoice before print

	- Other Features

		- A free marketplace where any ERPNext user can list their products and be discovered by thousands of other companies using ERPNext
		- Add/Update quantity in Sales & Purchase Order without amending document
		- Enhanced Item Price (Based on UOM, Party, Min. Qty, etc.)
		- Shareholder Management
		- Production Plan and Job Card
		- Delivery Trip
		- Updated Timesheets
		- Lead Notes
		- Better Sales / Purchase / Stock Analytics report
		- Currency exchange API is changed to frankfurter public domain

	- GST (India)
		- Auto selection of GST tax template based on company and shipping address
		- GSTR-1 based on the address
		- HSN-wise summary of outwards supplies

- Changes have been made to ensure ERPNext is compatible with Python 3
- Better documentation is now available with support for more languages
- A lot of other fixes have been done to ensure a better overall user experience
