{"beta": 1, "creation": "2018-07-10 14:48:16.757030", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["status", "application_settings", "client_id", "redirect_url", "token_endpoint", "application_column_break", "client_secret", "scope", "api_endpoint", "authorization_settings", "authorization_endpoint", "refresh_token", "code", "authorization_column_break", "authorization_url", "access_token", "quickbooks_company_id", "company_settings", "company", "default_shipping_account", "default_warehouse", "company_column_break", "default_cost_center", "undeposited_funds_account"], "fields": [{"fieldname": "status", "fieldtype": "Select", "hidden": 1, "label": "Status", "options": "Connecting to QuickBooks\nConnected to QuickBooks\nIn Progress\nComplete\nFailed"}, {"collapsible": 1, "collapsible_depends_on": "eval:doc.client_id && doc.client_secret && doc.redirect_url", "fieldname": "application_settings", "fieldtype": "Section Break", "label": "Application Settings"}, {"fieldname": "client_id", "fieldtype": "Data", "in_list_view": 1, "label": "Client ID", "reqd": 1}, {"fieldname": "redirect_url", "fieldtype": "Data", "in_list_view": 1, "label": "Redirect URL", "reqd": 1}, {"default": "https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer", "fieldname": "token_endpoint", "fieldtype": "Data", "label": "Token Endpoint", "read_only": 1, "reqd": 1}, {"fieldname": "application_column_break", "fieldtype": "Column Break"}, {"fieldname": "client_secret", "fieldtype": "Data", "in_list_view": 1, "label": "Client Secret", "reqd": 1}, {"default": "com.intuit.quickbooks.accounting", "fieldname": "scope", "fieldtype": "Data", "in_list_view": 1, "label": "<PERSON><PERSON>", "read_only": 1, "reqd": 1}, {"default": "https://quickbooks.api.intuit.com/v3", "fieldname": "api_endpoint", "fieldtype": "Data", "label": "API Endpoint", "read_only": 1, "reqd": 1}, {"collapsible": 1, "fieldname": "authorization_settings", "fieldtype": "Section Break", "label": "Authorization Settings"}, {"default": "https://appcenter.intuit.com/connect/oauth2", "fieldname": "authorization_endpoint", "fieldtype": "Data", "label": "Authorization Endpoint", "read_only": 1, "reqd": 1}, {"fieldname": "refresh_token", "fieldtype": "Small Text", "hidden": 1, "label": "Refresh <PERSON>"}, {"fieldname": "code", "fieldtype": "Data", "hidden": 1, "label": "Code"}, {"fieldname": "authorization_column_break", "fieldtype": "Column Break"}, {"fieldname": "authorization_url", "fieldtype": "Data", "label": "Authorization URL", "read_only": 1, "reqd": 1}, {"fieldname": "access_token", "fieldtype": "Small Text", "hidden": 1, "label": "Access Token"}, {"fieldname": "quickbooks_company_id", "fieldtype": "Data", "hidden": 1, "label": "Quickbooks Company ID"}, {"fieldname": "company_settings", "fieldtype": "Section Break", "hidden": 1, "label": "Company Settings"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company"}, {"fieldname": "default_shipping_account", "fieldtype": "Link", "hidden": 1, "label": "Default Shipping Account", "options": "Account"}, {"fieldname": "default_warehouse", "fieldtype": "Link", "hidden": 1, "label": "Default Warehouse", "options": "Warehouse"}, {"fieldname": "company_column_break", "fieldtype": "Column Break"}, {"fieldname": "default_cost_center", "fieldtype": "Link", "hidden": 1, "label": "Default Cost Center", "options": "Cost Center"}, {"fieldname": "undeposited_funds_account", "fieldtype": "Link", "hidden": 1, "label": "Undeposited Funds Account", "options": "Account"}], "issingle": 1, "modified": "2019-08-07 15:26:00.653433", "modified_by": "Administrator", "module": "ERPNext Integrations", "name": "<PERSON><PERSON><PERSON>s Migrator", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC"}