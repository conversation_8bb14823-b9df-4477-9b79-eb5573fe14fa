{"add_total_row": 0, "creation": "2018-12-31 16:30:57.188837", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "json": "{\"order_by\": \"`tabOpportunity`.`modified` desc\", \"filters\": [[\"Opportunity\", \"status\", \"=\", \"Lost\"]], \"fields\": [[\"name\", \"Opportunity\"], [\"opportunity_from\", \"Opportunity\"], [\"party_name\", \"Opportunity\"], [\"customer_name\", \"Opportunity\"], [\"opportunity_type\", \"Opportunity\"], [\"status\", \"Opportunity\"], [\"docstatus\", \"Opportunity\"], [\"lost_reason\", \"Lost Reason Detail\"]], \"add_totals_row\": 0, \"add_total_row\": 0, \"page_length\": 20}", "modified": "2022-06-04 15:49:02.848845", "modified_by": "Administrator", "module": "CRM", "name": "Lost Opportunity", "owner": "Administrator", "prepared_report": 0, "ref_doctype": "Opportunity", "report_name": "Lost Opportunity", "report_type": "Script Report", "roles": [{"role": "Sales User"}, {"role": "Sales Manager"}]}