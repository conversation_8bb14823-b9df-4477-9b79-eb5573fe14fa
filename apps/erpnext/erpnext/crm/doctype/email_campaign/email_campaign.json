{"actions": [], "autoname": "format:MAIL-CAMP-{YYYY}-{#####}", "creation": "2019-06-30 16:05:30.015615", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["campaign_name", "email_campaign_for", "recipient", "sender", "column_break_4", "start_date", "end_date", "status"], "fields": [{"fieldname": "campaign_name", "fieldtype": "Link", "in_list_view": 1, "label": "Campaign", "options": "Campaign", "reqd": 1}, {"fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "\nScheduled\nIn Progress\nCompleted\nUnsubscribed", "read_only": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "start_date", "fieldtype": "Date", "label": "Start Date", "reqd": 1}, {"fieldname": "end_date", "fieldtype": "Date", "label": "End Date", "read_only": 1}, {"default": "Lead", "fieldname": "email_campaign_for", "fieldtype": "Select", "in_list_view": 1, "label": "Email Campaign For ", "options": "\nLead\nContact\nEmail Group", "reqd": 1}, {"fieldname": "recipient", "fieldtype": "Dynamic Link", "label": "Recipient", "options": "email_campaign_for", "reqd": 1}, {"default": "__user", "fieldname": "sender", "fieldtype": "Link", "label": "Sender", "options": "User"}], "links": [], "modified": "2020-07-15 12:43:25.548682", "modified_by": "Administrator", "module": "CRM", "name": "Email Campaign", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}