{"actions": [], "allow_events_in_timeline": 1, "autoname": "field:company_name", "creation": "2021-08-19 00:21:06.995448", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["overview_tab", "company_name", "customer_group", "no_of_employees", "annual_revenue", "column_break_4", "market_segment", "industry", "territory", "column_break_6", "prospect_owner", "website", "fax", "company", "address_and_contact_section", "column_break_16", "contacts_tab", "address_html", "column_break_18", "contact_html", "leads_section", "leads", "opportunities_tab", "opportunities", "activities_tab", "open_activities_html", "all_activities_section", "all_activities_html", "notes_section", "notes_html", "notes"], "fields": [{"fieldname": "company_name", "fieldtype": "Data", "label": "Company Name", "unique": 1}, {"fieldname": "industry", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Industry", "options": "Industry Type"}, {"fieldname": "market_segment", "fieldtype": "Link", "label": "Market Segment", "options": "Market Segment"}, {"fieldname": "customer_group", "fieldtype": "Link", "label": "Customer Group", "options": "Customer Group"}, {"fieldname": "territory", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Territory", "options": "Territory"}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"fieldname": "no_of_employees", "fieldtype": "Select", "label": "No. of Employees", "options": "1-10\n11-50\n51-200\n201-500\n501-1000\n1000+"}, {"fieldname": "annual_revenue", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Annual Revenue", "options": "currency"}, {"fieldname": "fax", "fieldtype": "Data", "label": "Fax", "options": "Phone"}, {"fieldname": "website", "fieldtype": "Data", "label": "Website"}, {"fieldname": "prospect_owner", "fieldtype": "Link", "label": "Prospect Owner", "options": "User"}, {"fieldname": "leads_section", "fieldtype": "Tab Break", "label": "Leads"}, {"fieldname": "address_html", "fieldtype": "HTML", "label": "Address HTML"}, {"fieldname": "contact_html", "fieldtype": "HTML", "label": "Contact HTML"}, {"collapsible": 1, "depends_on": "eval:!doc.__islocal", "fieldname": "notes_section", "fieldtype": "Tab Break", "label": "Comments"}, {"depends_on": "eval: !doc.__islocal", "fieldname": "address_and_contact_section", "fieldtype": "Section Break", "label": "Address"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "opportunities_tab", "fieldtype": "Tab Break", "label": "Opportunities"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "activities_tab", "fieldtype": "Tab Break", "label": "Activities"}, {"fieldname": "notes_html", "fieldtype": "HTML", "label": "Notes HTML"}, {"fieldname": "opportunities", "fieldtype": "Table", "label": "Opportunities", "options": "Prospect Opportunity"}, {"fieldname": "contacts_tab", "fieldtype": "Tab Break", "label": "Address & Contact"}, {"fieldname": "column_break_18", "fieldtype": "Column Break"}, {"fieldname": "leads", "fieldtype": "Table", "options": "Prospect Lead"}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"fieldname": "overview_tab", "fieldtype": "Tab Break", "label": "Overview"}, {"fieldname": "open_activities_html", "fieldtype": "HTML", "label": "Open Activities HTML"}, {"fieldname": "all_activities_section", "fieldtype": "Section Break", "label": "All Activities"}, {"fieldname": "all_activities_html", "fieldtype": "HTML", "label": "All Activities HTML"}, {"fieldname": "notes", "fieldtype": "Table", "hidden": 1, "label": "Notes", "no_copy": 1, "options": "CRM Note"}], "index_web_pages_for_search": 1, "links": [], "modified": "2022-10-13 12:29:33.674561", "modified_by": "Administrator", "module": "CRM", "name": "Prospect", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "company_name", "track_changes": 1}