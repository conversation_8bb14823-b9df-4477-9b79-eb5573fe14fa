{"actions": [], "creation": "2021-09-09 17:03:22.754446", "description": "Settings for <PERSON><PERSON>", "doctype": "DocType", "document_type": "Other", "engine": "InnoDB", "field_order": ["section_break_5", "campaign_naming_by", "allow_lead_duplication_based_on_emails", "column_break_4", "auto_creation_of_contact", "opportunity_section", "close_opportunity_after_days", "column_break_9", "quotation_section", "default_valid_till", "section_break_13", "carry_forward_communication_and_comments"], "fields": [{"fieldname": "campaign_naming_by", "fieldtype": "Select", "in_list_view": 1, "label": "Campaign Naming By", "options": "Campaign Name\nNaming Series"}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "default_valid_till", "fieldtype": "Data", "label": "Default Quotation Validity Days"}, {"fieldname": "section_break_5", "fieldtype": "Section Break", "label": "Lead"}, {"default": "0", "fieldname": "allow_lead_duplication_based_on_emails", "fieldtype": "Check", "label": "Allow Lead Duplication based on Emails"}, {"default": "1", "fieldname": "auto_creation_of_contact", "fieldtype": "Check", "label": "Auto Creation of Contact"}, {"fieldname": "opportunity_section", "fieldtype": "Section Break", "label": "Opportunity"}, {"default": "15", "description": "Auto close Opportunity Replied after the no. of days mentioned above", "fieldname": "close_opportunity_after_days", "fieldtype": "Int", "label": "Close Replied Opportunity After Days"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "quotation_section", "fieldtype": "Section Break", "label": "Quotation"}, {"fieldname": "section_break_13", "fieldtype": "Section Break", "label": "Other Settings"}, {"default": "0", "description": "All the Comments and Emails will be copied from one document to another newly created document(Lead -> Opportunity -> Quotation) throughout the CRM documents.", "fieldname": "carry_forward_communication_and_comments", "fieldtype": "Check", "label": "Carry Forward Communication and Comments"}], "icon": "fa fa-cog", "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2022-06-06 11:22:08.464253", "modified_by": "Administrator", "module": "CRM", "name": "CRM Settings", "owner": "Administrator", "permissions": [{"create": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "Sales Master Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}