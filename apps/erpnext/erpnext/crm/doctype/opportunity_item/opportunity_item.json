{"actions": [], "creation": "2013-02-22 01:27:51", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "item_name", "col_break1", "uom", "qty", "section_break_6", "brand", "item_group", "description", "column_break_8", "image", "image_view", "quantity_and_rate_section", "base_rate", "base_amount", "column_break_16", "rate", "amount"], "fields": [{"fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>"}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"default": "1", "fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Qty", "oldfieldname": "qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "item_group", "fieldtype": "Link", "hidden": 1, "label": "Item Group", "oldfieldname": "item_group", "oldfieldtype": "Link", "options": "Item Group", "print_hide": 1}, {"fieldname": "brand", "fieldtype": "Link", "hidden": 1, "label": "Brand", "oldfieldname": "brand", "oldfieldtype": "Link", "options": "Brand", "print_hide": 1}, {"collapsible": 1, "fieldname": "section_break_6", "fieldtype": "Section Break", "label": "Description"}, {"fieldname": "uom", "fieldtype": "Link", "label": "UOM", "oldfieldname": "uom", "oldfieldtype": "Link", "options": "UOM"}, {"fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Text", "print_width": "300px", "width": "300px"}, {"fieldname": "column_break_8", "fieldtype": "Column Break"}, {"fetch_from": "item_code.image", "fieldname": "image", "fieldtype": "Attach", "hidden": 1, "label": "Image"}, {"fieldname": "image_view", "fieldtype": "Image", "label": "Image View", "options": "image", "print_hide": 1}, {"fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "options": "currency", "reqd": 1}, {"fieldname": "quantity_and_rate_section", "fieldtype": "Section Break", "label": "Quantity and Rate"}, {"fieldname": "base_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "reqd": 1}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "options": "currency", "read_only": 1, "reqd": 1}, {"fieldname": "base_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate (Company Currency)", "oldfieldname": "basic_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "reqd": 1}], "idx": 1, "istable": 1, "links": [], "modified": "2023-11-14 18:35:30.887278", "modified_by": "Administrator", "module": "CRM", "name": "Opportunity Item", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}