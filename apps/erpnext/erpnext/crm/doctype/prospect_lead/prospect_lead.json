{"actions": [], "creation": "2021-08-19 00:14:14.857421", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["lead", "lead_name", "email", "column_break_4", "mobile_no", "lead_owner", "status"], "fields": [{"columns": 2, "fieldname": "lead", "fieldtype": "Link", "in_list_view": 1, "label": "Lead", "options": "Lead", "reqd": 1}, {"columns": 2, "fetch_from": "lead.lead_name", "fieldname": "lead_name", "fieldtype": "Data", "in_list_view": 1, "label": "Lead Name", "read_only": 1}, {"columns": 1, "fetch_from": "lead.status", "fieldname": "status", "fieldtype": "Data", "in_list_view": 1, "label": "Status", "read_only": 1}, {"columns": 2, "fetch_from": "lead.email_id", "fieldname": "email", "fieldtype": "Data", "in_list_view": 1, "label": "Email", "options": "Email", "read_only": 1}, {"columns": 2, "fetch_from": "lead.mobile_no", "fieldname": "mobile_no", "fieldtype": "Data", "in_list_view": 1, "label": "Mobile No", "options": "Phone", "read_only": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"columns": 1, "fetch_from": "lead.lead_owner", "fieldname": "lead_owner", "fieldtype": "Data", "in_list_view": 1, "label": "Lead Owner"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2022-04-28 20:27:58.805970", "modified_by": "Administrator", "module": "CRM", "name": "Prospect Lead", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}