{"actions": [], "autoname": "format:APMT-{customer_name}-{####}", "creation": "2019-08-27 10:48:27.926283", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["scheduled_time", "status", "customer_details_section", "customer_name", "customer_phone_number", "customer_skype", "customer_email", "col_br_2", "customer_details", "linked_docs_section", "appointment_with", "party", "col_br_3", "calendar_event"], "fields": [{"fieldname": "customer_details_section", "fieldtype": "Section Break", "label": "Customer Details"}, {"fieldname": "customer_name", "fieldtype": "Data", "in_list_view": 1, "label": "Name", "reqd": 1}, {"fieldname": "customer_phone_number", "fieldtype": "Data", "label": "Phone Number"}, {"fieldname": "customer_skype", "fieldtype": "Data", "label": "Skype ID"}, {"fieldname": "customer_details", "fieldtype": "Long Text", "label": "Details"}, {"fieldname": "scheduled_time", "fieldtype": "Datetime", "in_list_view": 1, "label": "Scheduled Time", "reqd": 1}, {"fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Open\nUnverified\nClosed", "reqd": 1}, {"fieldname": "calendar_event", "fieldtype": "Link", "label": "Calendar Event", "options": "Event"}, {"fieldname": "col_br_2", "fieldtype": "Column Break"}, {"fieldname": "customer_email", "fieldtype": "Data", "label": "Email", "reqd": 1}, {"fieldname": "linked_docs_section", "fieldtype": "Section Break", "label": "Linked Documents"}, {"fieldname": "col_br_3", "fieldtype": "Column Break"}, {"fieldname": "appointment_with", "fieldtype": "Link", "label": "Appointment With", "options": "DocType"}, {"fieldname": "party", "fieldtype": "Dynamic Link", "label": "Party", "options": "appointment_with"}], "links": [], "modified": "2022-12-15 11:11:02.131986", "modified_by": "Administrator", "module": "CRM", "name": "Appointment", "name_case": "UPPER CASE", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Employee", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}