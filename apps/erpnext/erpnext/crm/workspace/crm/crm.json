{"charts": [{"chart_name": "Territory Wise Sales", "label": "Territory Wise Sales"}], "content": "[{\"id\":\"Cj2TyhgiWy\",\"type\":\"chart\",\"data\":{\"chart_name\":\"Territory Wise Sales\",\"col\":12}},{\"id\":\"LAKRmpYMRA\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"XGIwEUStw_\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Your Shortcuts</b></span>\",\"col\":12}},{\"id\":\"69RN0XsiJK\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Lead\",\"col\":3}},{\"id\":\"t6PQ0vY-Iw\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Opportunity\",\"col\":3}},{\"id\":\"VOFE0hqXRD\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Customer\",\"col\":3}},{\"id\":\"0ik53fuemG\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Sales Analytics\",\"col\":3}},{\"id\":\"wdROEmB_XG\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Dashboard\",\"col\":3}},{\"id\":\"-I9HhcgUKE\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"ttpROKW9vk\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Reports &amp; Masters</b></span>\",\"col\":12}},{\"id\":\"-76QPdbBHy\",\"type\":\"card\",\"data\":{\"card_name\":\"Sales Pipeline\",\"col\":4}},{\"id\":\"_YmGwzVWRr\",\"type\":\"card\",\"data\":{\"card_name\":\"Masters\",\"col\":4}},{\"id\":\"Bma1PxoXk3\",\"type\":\"card\",\"data\":{\"card_name\":\"Reports\",\"col\":4}},{\"id\":\"80viA0R83a\",\"type\":\"card\",\"data\":{\"card_name\":\"Campaign\",\"col\":4}},{\"id\":\"Buo5HtKRFN\",\"type\":\"card\",\"data\":{\"card_name\":\"Settings\",\"col\":4}},{\"id\":\"sLS_x4FMK2\",\"type\":\"card\",\"data\":{\"card_name\":\"Maintenance\",\"col\":4}}]", "creation": "2020-01-23 14:48:30.183272", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "crm", "idx": 0, "is_hidden": 0, "label": "CRM", "links": [{"hidden": 0, "is_query_report": 0, "label": "Reports", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "Lead", "hidden": 0, "is_query_report": 1, "label": "Lead Details", "link_count": 0, "link_to": "Lead Details", "link_type": "Report", "onboard": 1, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "Sales Pipeline Analytics", "link_count": 0, "link_to": "Sales Pipeline Analytics", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "Opportunity Summary by Sales Stage", "link_count": 0, "link_to": "Opportunity Summary by Sales Stage", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Sales Funnel", "link_count": 0, "link_to": "sales-funnel", "link_type": "Page", "onboard": 1, "type": "Link"}, {"dependencies": "Lead", "hidden": 0, "is_query_report": 1, "label": "Prospects Engaged But Not Converted", "link_count": 0, "link_to": "Prospects Engaged But Not Converted", "link_type": "Report", "onboard": 1, "type": "Link"}, {"dependencies": "Opportunity", "hidden": 0, "is_query_report": 1, "label": "First Response Time for Opportunity", "link_count": 0, "link_to": "First Response Time for Opportunity", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Sales Order", "hidden": 0, "is_query_report": 1, "label": "Inactive Customers", "link_count": 0, "link_to": "Inactive Customers", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Lead", "hidden": 0, "is_query_report": 1, "label": "Campaign Efficiency", "link_count": 0, "link_to": "Campaign Efficiency", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Lead", "hidden": 0, "is_query_report": 1, "label": "Lead Owner Efficiency", "link_count": 0, "link_to": "Lead Owner Efficiency", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Maintenance", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Maintenance Schedule", "link_count": 0, "link_to": "Maintenance Schedule", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Maintenance Visit", "link_count": 0, "link_to": "Maintenance Visit", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON><PERSON>", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Masters", "link_count": 7, "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Territory", "link_count": 0, "link_to": "Territory", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Customer Group", "link_count": 0, "link_to": "Customer Group", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Contact", "link_count": 0, "link_to": "Contact", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Prospect", "link_count": 0, "link_to": "Prospect", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Sales Person", "link_count": 0, "link_to": "Sales Person", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Sales Stage", "link_count": 0, "link_to": "Sales Stage", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Lead Source", "link_count": 0, "link_to": "Lead Source", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Sales Pipeline", "link_count": 7, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Lead", "link_count": 0, "link_to": "Lead", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Opportunity", "link_count": 0, "link_to": "Opportunity", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Customer", "link_count": 0, "link_to": "Customer", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Contract", "link_count": 0, "link_to": "Contract", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Appointment", "link_count": 0, "link_to": "Appointment", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Newsletter", "link_count": 0, "link_to": "Newsletter", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Communication", "link_count": 0, "link_to": "Communication", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Settings", "link_count": 2, "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "CRM Settings", "link_count": 0, "link_to": "CRM Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "SMS Settings", "link_count": 0, "link_to": "SMS Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Campaign", "link_count": 5, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Campaign", "link_count": 0, "link_to": "Campaign", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Email Campaign", "link_count": 0, "link_to": "Email Campaign", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "SMS Center", "link_count": 0, "link_to": "SMS Center", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "SMS Log", "link_count": 0, "link_to": "SMS Log", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Email Group", "link_count": 0, "link_to": "Email Group", "link_type": "DocType", "onboard": 0, "type": "Link"}], "modified": "2023-09-14 12:11:03.968048", "modified_by": "Administrator", "module": "CRM", "name": "CRM", "number_cards": [], "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [], "restrict_to_domain": "", "roles": [], "sequence_id": 17.0, "shortcuts": [{"color": "Blue", "format": "{} Open", "label": "Lead", "link_to": "Lead", "stats_filter": "{\"status\":\"Open\"}", "type": "DocType"}, {"color": "Blue", "format": "{} Assigned", "label": "Opportunity", "link_to": "Opportunity", "stats_filter": "{\"_assign\": [\"like\", '%' + frappe.session.user + '%']}", "type": "DocType"}, {"label": "Customer", "link_to": "Customer", "type": "DocType"}, {"label": "Sales Analytics", "link_to": "Sales Analytics", "type": "Report"}, {"label": "Dashboard", "link_to": "CRM", "type": "Dashboard"}], "title": "CRM"}