{"chart_name": "Opportunities via Campaigns", "chart_type": "Group By", "creation": "2020-07-20 20:17:15.705402", "custom_options": "{\"truncateLegends\": 1, \"maxSlices\": 8}", "docstatus": 0, "doctype": "Dashboard Chart", "document_type": "Opportunity", "dynamic_filters_json": "[[\"Opportunity\",\"company\",\"=\",\"frappe.defaults.get_user_default(\\\"Company\\\")\"]]", "filters_json": "[]", "group_by_based_on": "campaign", "group_by_type": "Count", "idx": 0, "is_public": 1, "is_standard": 1, "last_synced_on": "2020-07-22 15:45:32.572011", "modified": "2020-07-22 16:10:02.497726", "modified_by": "Administrator", "module": "CRM", "name": "Opportunities via Campaigns", "number_of_groups": 0, "owner": "Administrator", "timeseries": 0, "type": "Pie", "use_report_chart": 0, "y_axis": []}