{"actions": [], "autoname": "hash", "creation": "2022-04-13 16:05:55.395695", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "column_break_2", "item_name", "is_scrap_item", "section_break_4", "description", "brand", "image_column", "image", "image_view", "received_and_accepted", "received_qty", "qty", "rejected_qty", "returned_qty", "col_break2", "stock_uom", "conversion_factor", "rate_and_amount", "rate", "amount", "column_break_19", "rm_cost_per_qty", "service_cost_per_qty", "additional_cost_per_qty", "scrap_cost_per_qty", "rm_supp_cost", "warehouse_and_reference", "warehouse", "subcontracting_order", "subcontracting_order_item", "subcontracting_receipt_item", "column_break_40", "rejected_warehouse", "bom", "include_exploded_items", "quality_inspection", "schedule_date", "reference_name", "section_break_45", "serial_and_batch_bundle", "use_serial_batch_fields", "col_break5", "rejected_serial_and_batch_bundle", "section_break_jshh", "serial_no", "rejected_serial_no", "column_break_henr", "batch_no", "manufacture_details", "manufacturer", "column_break_16", "manufacturer_part_no", "accounting_details_section", "expense_account", "accounting_dimensions_section", "cost_center", "dimension_col_break", "project", "section_break_80", "page_break", "purchase_order", "purchase_order_item"], "fields": [{"bold": 1, "columns": 3, "fieldname": "item_code", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>", "print_width": "100px", "reqd": 1, "search_index": 1, "width": "100px"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fetch_from": "item_code.item_name", "fetch_if_empty": 1, "fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "label": "Item Name", "print_hide": 1}, {"collapsible": 1, "fieldname": "section_break_4", "fieldtype": "Section Break", "label": "Description"}, {"fetch_from": "item_code.description", "fetch_if_empty": 1, "fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "print_width": "300px", "width": "300px"}, {"fetch_from": "item_code.image", "fieldname": "image", "fieldtype": "Attach", "hidden": 1, "label": "Image"}, {"fieldname": "image_view", "fieldtype": "Image", "label": "Image View", "options": "image", "print_hide": 1}, {"fieldname": "received_and_accepted", "fieldtype": "Section Break", "label": "Received and Accepted"}, {"bold": 1, "default": "0", "fieldname": "received_qty", "fieldtype": "Float", "label": "Received Quantity", "no_copy": 1, "print_hide": 1, "print_width": "100px", "read_only": 1, "reqd": 1, "width": "100px"}, {"columns": 2, "fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Accepted Quantity", "no_copy": 1, "print_width": "100px", "width": "100px"}, {"columns": 1, "depends_on": "eval: !parent.is_return", "fieldname": "rejected_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Rejected Quantity", "no_copy": 1, "print_hide": 1, "print_width": "100px", "read_only_depends_on": "eval: doc.is_scrap_item", "width": "100px"}, {"fieldname": "col_break2", "fieldtype": "Column Break", "print_hide": 1}, {"fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "options": "UOM", "print_hide": 1, "print_width": "100px", "read_only": 1, "reqd": 1, "width": "100px"}, {"default": "1", "fieldname": "conversion_factor", "fieldtype": "Float", "hidden": 1, "label": "Conversion Factor", "read_only": 1}, {"fieldname": "rate_and_amount", "fieldtype": "Section Break", "label": "Rate and Amount"}, {"bold": 1, "columns": 2, "fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "options": "currency", "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "options": "currency", "read_only": 1}, {"fieldname": "column_break_19", "fieldtype": "Column Break"}, {"default": "0", "depends_on": "eval: !doc.is_scrap_item", "fieldname": "rm_cost_per_qty", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Raw Material Cost <PERSON>", "no_copy": 1, "read_only": 1}, {"default": "0", "depends_on": "eval: !doc.is_scrap_item", "fieldname": "service_cost_per_qty", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Service Cost Per <PERSON>", "read_only": 1, "reqd": 1}, {"default": "0", "depends_on": "eval: !doc.is_scrap_item", "fieldname": "additional_cost_per_qty", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Cost <PERSON>", "read_only": 1}, {"fieldname": "warehouse_and_reference", "fieldtype": "Section Break", "label": "Warehouse and Reference"}, {"bold": 1, "fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Accepted Warehouse", "options": "Warehouse", "print_hide": 1, "print_width": "100px", "width": "100px"}, {"depends_on": "eval: !parent.is_return", "fieldname": "rejected_warehouse", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Rejected Warehouse", "no_copy": 1, "options": "Warehouse", "print_hide": 1, "print_width": "100px", "read_only_depends_on": "eval: doc.is_scrap_item", "width": "100px"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "quality_inspection", "fieldtype": "Link", "label": "Quality Inspection", "no_copy": 1, "options": "Quality Inspection", "print_hide": 1}, {"fieldname": "column_break_40", "fieldtype": "Column Break"}, {"fieldname": "subcontracting_order", "fieldtype": "Link", "label": "Subcontracting Order", "no_copy": 1, "options": "Subcontracting Order", "print_width": "150px", "read_only": 1, "search_index": 1, "width": "150px"}, {"fieldname": "schedule_date", "fieldtype": "Date", "label": "Required By", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_45", "fieldtype": "Section Break", "label": "Serial and Batch Details"}, {"depends_on": "eval:!doc.is_fixed_asset && doc.use_serial_batch_fields === 1", "fieldname": "serial_no", "fieldtype": "Small Text", "label": "Serial No", "no_copy": 1}, {"depends_on": "eval:!doc.is_fixed_asset && doc.use_serial_batch_fields === 1", "fieldname": "batch_no", "fieldtype": "Link", "label": "Batch No", "no_copy": 1, "options": "<PERSON><PERSON>", "print_hide": 1}, {"depends_on": "eval: !parent.is_return", "fieldname": "rejected_serial_no", "fieldtype": "Small Text", "label": "Rejected Serial No", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "subcontracting_order_item", "fieldtype": "Data", "hidden": 1, "label": "Subcontracting Order Item", "no_copy": 1, "print_hide": 1, "print_width": "150px", "read_only": 1, "search_index": 1, "width": "150px"}, {"fieldname": "col_break5", "fieldtype": "Column Break"}, {"fieldname": "bom", "fieldtype": "Link", "label": "BOM", "no_copy": 1, "options": "BOM", "print_hide": 1, "read_only_depends_on": "eval: doc.is_scrap_item"}, {"fetch_from": "item_code.brand", "fieldname": "brand", "fieldtype": "Link", "hidden": 1, "label": "Brand", "options": "Brand", "print_hide": 1, "read_only": 1}, {"fieldname": "rm_supp_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "Raw Materials Supplied Cost", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "expense_account", "fieldtype": "Link", "label": "Expense Account", "options": "Account"}, {"collapsible": 1, "fieldname": "manufacture_details", "fieldtype": "Section Break", "label": "Manufacture"}, {"fieldname": "manufacturer", "fieldtype": "Link", "label": "Manufacturer", "options": "Manufacturer"}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"fieldname": "manufacturer_part_no", "fieldtype": "Data", "label": "Manufacturer Part Number"}, {"fieldname": "subcontracting_receipt_item", "fieldtype": "Data", "hidden": 1, "label": "Subcontracting Receipt Item", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "image_column", "fieldtype": "Column Break"}, {"fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project", "print_hide": 1}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"default": ":Company", "depends_on": "eval:cint(erpnext.is_perpetual_inventory_enabled(parent.company))", "fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center", "print_hide": 1}, {"fieldname": "section_break_80", "fieldtype": "Section Break"}, {"allow_on_submit": 1, "default": "0", "fieldname": "page_break", "fieldtype": "Check", "label": "Page Break", "print_hide": 1}, {"default": "0", "depends_on": "returned_qty", "fieldname": "returned_qty", "fieldtype": "Float", "label": "Returned <PERSON><PERSON>", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "accounting_details_section", "fieldtype": "Section Break", "label": "Accounting Details"}, {"depends_on": "eval:doc.use_serial_batch_fields === 0 || doc.docstatus === 1", "fieldname": "serial_and_batch_bundle", "fieldtype": "Link", "label": "Serial and Batch Bundle", "no_copy": 1, "options": "Serial and Batch Bundle", "print_hide": 1}, {"depends_on": "eval:doc.use_serial_batch_fields === 0 || doc.docstatus === 1", "fieldname": "rejected_serial_and_batch_bundle", "fieldtype": "Link", "label": "Rejected Serial and <PERSON><PERSON>", "no_copy": 1, "options": "Serial and Batch Bundle", "print_hide": 1}, {"default": "0", "depends_on": "eval: !doc.bom", "fieldname": "is_scrap_item", "fieldtype": "Check", "label": "Is <PERSON>", "no_copy": 1, "print_hide": 1, "read_only_depends_on": "eval: doc.bom"}, {"default": "0", "depends_on": "eval: !doc.is_scrap_item", "fieldname": "scrap_cost_per_qty", "fieldtype": "Float", "label": "Scrap Cost <PERSON>", "no_copy": 1, "non_negative": 1, "read_only": 1}, {"fieldname": "reference_name", "fieldtype": "Data", "hidden": 1, "label": "Reference Name", "no_copy": 1, "read_only": 1}, {"fieldname": "purchase_order_item", "fieldtype": "Data", "hidden": 1, "label": "Purchase Order Item", "no_copy": 1, "read_only": 1, "search_index": 1}, {"fieldname": "purchase_order", "fieldtype": "Link", "hidden": 1, "label": "Purchase Order", "no_copy": 1, "options": "Purchase Order", "read_only": 1, "search_index": 1}, {"default": "0", "fieldname": "include_exploded_items", "fieldtype": "Check", "label": "Include Exploded Items", "print_hide": 1}, {"default": "0", "fieldname": "use_serial_batch_fields", "fieldtype": "Check", "label": "Use Serial No / Batch Fields"}, {"depends_on": "eval:doc.use_serial_batch_fields === 1", "fieldname": "section_break_jshh", "fieldtype": "Section Break"}, {"fieldname": "column_break_henr", "fieldtype": "Column Break"}], "idx": 1, "istable": 1, "links": [], "modified": "2024-02-04 16:23:30.374865", "modified_by": "Administrator", "module": "Subcontracting", "name": "Subcontracting Receipt Item", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}