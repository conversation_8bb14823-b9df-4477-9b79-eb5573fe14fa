{"actions": [], "creation": "2022-04-01 19:29:30.923800", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["main_item_code", "rm_item_code", "column_break_3", "stock_uom", "conversion_factor", "reserve_warehouse", "column_break_6", "bom_detail_no", "reference_name", "section_break_9", "rate", "column_break_11", "amount", "section_break_13", "required_qty", "supplied_qty", "column_break_16", "consumed_qty", "returned_qty", "total_supplied_qty"], "fields": [{"columns": 2, "fieldname": "main_item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>", "read_only": 1}, {"columns": 2, "fieldname": "rm_item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Raw Material Item Code", "options": "<PERSON><PERSON>", "read_only": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock <PERSON>", "options": "UOM", "read_only": 1}, {"default": "1", "fieldname": "conversion_factor", "fieldtype": "Float", "hidden": 1, "label": "Conversion Factor", "read_only": 1}, {"columns": 2, "fieldname": "reserve_warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Reserve Warehouse", "options": "Warehouse"}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"fieldname": "bom_detail_no", "fieldtype": "Data", "label": "BOM Detail No", "read_only": 1}, {"fieldname": "reference_name", "fieldtype": "Data", "label": "Reference Name", "read_only": 1}, {"fieldname": "section_break_9", "fieldtype": "Section Break"}, {"columns": 2, "fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "options": "Company:company:default_currency"}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "section_break_13", "fieldtype": "Section Break"}, {"columns": 2, "fieldname": "required_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Required <PERSON><PERSON>", "read_only": 1}, {"fieldname": "supplied_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Supplied <PERSON>ty", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"fieldname": "consumed_qty", "fieldtype": "Float", "label": "Consumed Qty", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "returned_qty", "fieldtype": "Float", "label": "Returned <PERSON><PERSON>", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "total_supplied_qty", "fieldtype": "Float", "hidden": 1, "label": "Total Supplied Qty", "no_copy": 1, "print_hide": 1, "read_only": 1}], "hide_toolbar": 1, "istable": 1, "links": [], "modified": "2022-08-26 16:04:56.125951", "modified_by": "Administrator", "module": "Subcontracting", "name": "Subcontracting Order Supplied Item", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}