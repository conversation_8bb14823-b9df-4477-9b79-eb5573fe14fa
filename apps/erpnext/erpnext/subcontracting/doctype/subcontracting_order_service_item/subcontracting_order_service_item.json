{"actions": [], "autoname": "hash", "creation": "2022-04-01 19:23:05.728354", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "column_break_2", "item_name", "section_break_4", "qty", "column_break_6", "rate", "column_break_8", "amount", "section_break_10", "fg_item", "column_break_12", "fg_item_qty", "purchase_order_item", "section_break_kphn", "material_request", "column_break_piqi", "material_request_item"], "fields": [{"bold": 1, "columns": 2, "fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>", "reqd": 1, "search_index": 1}, {"fetch_from": "item_code.item_name", "fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "in_list_view": 1, "label": "Item Name", "print_hide": 1, "reqd": 1}, {"bold": 1, "columns": 1, "fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity", "print_width": "60px", "reqd": 1, "width": "60px"}, {"bold": 1, "columns": 2, "fetch_from": "item_code.standard_rate", "fetch_if_empty": 1, "fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "options": "currency", "reqd": 1}, {"columns": 2, "fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "options": "currency", "read_only": 1, "reqd": 1}, {"fieldname": "fg_item", "fieldtype": "Link", "label": "Finished Good Item", "options": "<PERSON><PERSON>", "reqd": 1}, {"default": "1", "fieldname": "fg_item_qty", "fieldtype": "Float", "label": "Finished Good Item Quantity", "reqd": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "section_break_4", "fieldtype": "Section Break"}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"fieldname": "column_break_8", "fieldtype": "Column Break"}, {"fieldname": "section_break_10", "fieldtype": "Section Break"}, {"fieldname": "column_break_12", "fieldtype": "Column Break"}, {"fieldname": "purchase_order_item", "fieldtype": "Data", "hidden": 1, "label": "Purchase Order Item", "no_copy": 1, "read_only": 1, "search_index": 1}, {"collapsible": 1, "fieldname": "section_break_kphn", "fieldtype": "Section Break", "label": "Reference"}, {"fieldname": "material_request", "fieldtype": "Link", "label": "Material Request", "no_copy": 1, "options": "Material Request", "read_only": 1}, {"fieldname": "column_break_piqi", "fieldtype": "Column Break"}, {"fieldname": "material_request_item", "fieldtype": "Data", "label": "Material Request Item", "no_copy": 1, "read_only": 1}], "istable": 1, "links": [], "modified": "2023-11-30 13:29:31.017440", "modified_by": "Administrator", "module": "Subcontracting", "name": "Subcontracting Order Service Item", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "quick_entry": 1, "search_fields": "item_name", "sort_field": "modified", "sort_order": "DESC", "states": []}