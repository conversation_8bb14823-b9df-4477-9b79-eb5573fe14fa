{"charts": [{"chart_name": "Project Summary", "label": "Open Projects"}], "content": "[{\"id\":\"VDMms0hapk\",\"type\":\"chart\",\"data\":{\"chart_name\":\"Open Projects\",\"col\":12}},{\"id\":\"7Mbx6I5JUf\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"nyuMo9byw7\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Your Shortcuts</b></span>\",\"col\":12}},{\"id\":\"dILbX_r0ve\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Task\",\"col\":3}},{\"id\":\"JT8ntrqRiJ\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Project\",\"col\":3}},{\"id\":\"RsafDhm1MS\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Timesheet\",\"col\":3}},{\"id\":\"cVJH-gD0CR\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Project Billing Summary\",\"col\":3}},{\"id\":\"DbctrdmAy1\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Dashboard\",\"col\":3}},{\"id\":\"jx5aPK9aXN\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Learn Project Management\",\"col\":3}},{\"id\":\"ncIHWGQQvX\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"oGhjvYjfv-\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Reports &amp; Masters</b></span>\",\"col\":12}},{\"id\":\"TdsgJyG3EI\",\"type\":\"card\",\"data\":{\"card_name\":\"Projects\",\"col\":4}},{\"id\":\"nIc0iyvf1T\",\"type\":\"card\",\"data\":{\"card_name\":\"Time Tracking\",\"col\":4}},{\"id\":\"8G1if4jsQ7\",\"type\":\"card\",\"data\":{\"card_name\":\"Reports\",\"col\":4}},{\"id\":\"o7qTNRXZI8\",\"type\":\"card\",\"data\":{\"card_name\":\"Settings\",\"col\":4}}]", "creation": "2020-03-02 15:46:04.874669", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "project", "idx": 0, "is_hidden": 0, "label": "Projects", "links": [{"hidden": 0, "is_query_report": 0, "label": "Projects", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Project", "link_count": 0, "link_to": "Project", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Task", "link_count": 0, "link_to": "Task", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Project Template", "link_count": 0, "link_to": "Project Template", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Project Type", "link_count": 0, "link_to": "Project Type", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "Project", "hidden": 0, "is_query_report": 0, "label": "Project Update", "link_count": 0, "link_to": "Project Update", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Time Tracking", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Timesheet", "link_count": 0, "link_to": "Timesheet", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Activity Type", "link_count": 0, "link_to": "Activity Type", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "Activity Type", "hidden": 0, "is_query_report": 0, "label": "Activity Cost", "link_count": 0, "link_to": "Activity Cost", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Reports", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "Timesheet", "hidden": 0, "is_query_report": 1, "label": "Daily Timesheet Summary", "link_count": 0, "link_to": "Daily Timesheet Summary", "link_type": "Report", "onboard": 1, "type": "Link"}, {"dependencies": "Project", "hidden": 0, "is_query_report": 1, "label": "Project wise Stock Tracking", "link_count": 0, "link_to": "Project wise Stock Tracking", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Project", "hidden": 0, "is_query_report": 1, "label": "Project Billing Summary", "link_count": 0, "link_to": "Project Billing Summary", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Task", "hidden": 0, "is_query_report": 1, "label": "Delayed Tasks Summary", "link_count": 0, "link_to": "Delayed Tasks Summary", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Settings", "link_count": 1, "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Projects Settings", "link_count": 0, "link_to": "Projects Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}], "modified": "2023-07-04 14:39:08.935853", "modified_by": "Administrator", "module": "Projects", "name": "Projects", "number_cards": [], "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [], "restrict_to_domain": "", "roles": [], "sequence_id": 11.0, "shortcuts": [{"color": "Grey", "doc_view": "List", "label": "Learn Project Management", "type": "URL", "url": "https://frappe.school/courses/project-management?utm_source=in_app"}, {"color": "Blue", "format": "{} Assigned", "label": "Task", "link_to": "Task", "stats_filter": "{\n    \"_assign\": [\"like\", '%' + frappe.session.user + '%'],\n    \"status\": \"Open\"\n}", "type": "DocType"}, {"color": "Blue", "format": "{} Open", "label": "Project", "link_to": "Project", "stats_filter": "{\n    \"status\": \"Open\"\n}", "type": "DocType"}, {"label": "Timesheet", "link_to": "Timesheet", "type": "DocType"}, {"label": "Project Billing Summary", "link_to": "Project Billing Summary", "type": "Report"}, {"label": "Dashboard", "link_to": "Project", "type": "Dashboard"}], "title": "Projects"}