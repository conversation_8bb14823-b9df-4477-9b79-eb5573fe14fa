{"accept_payment": 0, "allow_comments": 1, "allow_delete": 1, "allow_edit": 1, "allow_incomplete": 0, "allow_multiple": 1, "allow_print": 0, "amount": 0.0, "amount_based_on_field": 0, "breadcrumbs": "[{\"title\":\"Tasks\", \"name\":\"tasks\"}]", "creation": "2016-06-24 15:50:33.091287", "doc_type": "Task", "docstatus": 0, "doctype": "Web Form", "idx": 0, "introduction_text": "", "is_standard": 1, "login_required": 1, "max_attachment_size": 0, "modified": "2018-07-24 11:32:07.805956", "modified_by": "Administrator", "module": "Projects", "name": "tasks", "owner": "Administrator", "payment_button_label": "Buy Now", "published": 1, "route": "tasks", "show_sidebar": 1, "sidebar_items": [], "success_url": "", "title": "Task", "web_form_fields": [{"fieldname": "project", "fieldtype": "Link", "hidden": 0, "label": "Project", "max_length": 0, "max_value": 0, "options": "Project", "read_only": 1, "reqd": 1}, {"fieldname": "subject", "fieldtype": "Data", "hidden": 0, "label": "Subject", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 1}, {"fieldname": "status", "fieldtype": "Select", "hidden": 0, "label": "Status", "max_length": 0, "max_value": 0, "options": "Open\nWorking\nPending Review\nOverdue\nClosed\nCancelled", "read_only": 0, "reqd": 0}, {"fieldname": "description", "fieldtype": "Text Editor", "hidden": 0, "label": "Details", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0}, {"fieldname": "priority", "fieldtype": "Select", "hidden": 0, "label": "Priority", "max_length": 0, "max_value": 0, "options": "Low\nMedium\nHigh\nUrgent", "read_only": 0, "reqd": 0}, {"fieldname": "exp_start_date", "fieldtype": "Date", "hidden": 0, "label": "Expected Start Date", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0}, {"fieldname": "exp_end_date", "fieldtype": "Date", "hidden": 0, "label": "Expected End Date", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0}]}