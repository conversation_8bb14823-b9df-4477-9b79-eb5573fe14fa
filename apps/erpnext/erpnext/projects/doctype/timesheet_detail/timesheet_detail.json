{"actions": [], "creation": "2013-03-05 09:11:06", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["activity_type", "from_time", "description", "section_break_3", "expected_hours", "to_time", "hours", "completed", "project_details", "project", "project_name", "column_break_2", "task", "section_break_6", "is_billable", "sales_invoice", "column_break_8", "billing_hours", "section_break_11", "base_billing_rate", "base_billing_amount", "base_costing_rate", "base_costing_amount", "column_break_14", "billing_rate", "billing_amount", "costing_rate", "costing_amount"], "fields": [{"fieldname": "activity_type", "fieldtype": "Link", "in_list_view": 1, "label": "Activity Type", "options": "Activity Type"}, {"columns": 2, "fieldname": "from_time", "fieldtype": "Datetime", "in_list_view": 1, "label": "From Time"}, {"fieldname": "section_break_3", "fieldtype": "Column Break"}, {"fieldname": "expected_hours", "fieldtype": "Float", "label": "Expected Hrs"}, {"columns": 1, "fieldname": "hours", "fieldtype": "Float", "in_list_view": 1, "label": "Hrs"}, {"fieldname": "to_time", "fieldtype": "Datetime", "label": "To Time"}, {"default": "0", "fieldname": "completed", "fieldtype": "Check", "label": "Completed"}, {"fieldname": "project_details", "fieldtype": "Section Break"}, {"columns": 3, "fieldname": "project", "fieldtype": "Link", "in_list_view": 1, "label": "Project", "options": "Project", "read_only_depends_on": "eval: parent.parent_project"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "task", "fieldtype": "Link", "label": "Task", "options": "Task", "remember_last_selected_value": 1}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"fieldname": "column_break_8", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "depends_on": "is_billable", "fieldname": "billing_hours", "fieldtype": "Float", "label": "Billing Hours", "permlevel": 1}, {"depends_on": "is_billable", "fieldname": "section_break_11", "fieldtype": "Section Break"}, {"fieldname": "billing_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Billing Rate", "options": "currency", "permlevel": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "billing_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Billing Amount", "options": "currency", "permlevel": 1, "read_only": 1}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"fieldname": "costing_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Costing Rate", "options": "currency", "permlevel": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "costing_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Costing Amount", "options": "currency", "permlevel": 1, "read_only": 1}, {"allow_on_submit": 1, "fieldname": "sales_invoice", "fieldtype": "Link", "label": "Sales Invoice", "no_copy": 1, "options": "Sales Invoice", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "columns": 1, "default": "0", "fieldname": "is_billable", "fieldtype": "Check", "in_list_view": 1, "label": "Is Billable", "print_hide": 1}, {"fetch_from": "project.project_name", "fieldname": "project_name", "fieldtype": "Data", "label": "Project Name", "read_only": 1}, {"fieldname": "description", "fieldtype": "Small Text", "label": "Description"}, {"fieldname": "base_billing_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Billing Rate", "print_hide": 1, "read_only": 1}, {"fieldname": "base_billing_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Billing Amount", "print_hide": 1, "read_only": 1}, {"fieldname": "base_costing_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Costing Rate", "print_hide": 1, "read_only": 1}, {"fieldname": "base_costing_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Costing Amount", "print_hide": 1, "read_only": 1}], "idx": 1, "istable": 1, "links": [], "modified": "2022-02-17 16:53:34.878798", "modified_by": "Administrator", "module": "Projects", "name": "Timesheet Detail", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "ASC", "states": []}