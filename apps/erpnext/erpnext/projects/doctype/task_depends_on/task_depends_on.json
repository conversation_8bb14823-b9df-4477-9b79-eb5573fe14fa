{"actions": [], "creation": "2015-04-29 04:52:48.868079", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["task", "column_break_2", "subject", "project"], "fields": [{"fieldname": "task", "fieldtype": "Link", "in_list_view": 1, "label": "Task", "options": "Task"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fetch_from": "task.subject", "fetch_if_empty": 1, "fieldname": "subject", "fieldtype": "Text", "in_list_view": 1, "label": "Subject", "read_only": 1}, {"fieldname": "project", "fieldtype": "Text", "label": "Project", "read_only": 1}], "istable": 1, "links": [], "modified": "2023-10-17 12:45:21.536165", "modified_by": "Administrator", "module": "Projects", "name": "Task Depends On", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}