{"actions": [], "allow_import": 1, "autoname": "naming_series:", "creation": "2013-02-28 17:57:33", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["title", "naming_series", "company", "customer", "currency", "exchange_rate", "sales_invoice", "column_break_3", "status", "parent_project", "employee_detail", "employee", "employee_name", "department", "column_break_9", "user", "start_date", "end_date", "section_break_5", "time_logs", "working_hours", "total_hours", "billing_details", "total_billable_hours", "base_total_billable_amount", "base_total_billed_amount", "base_total_costing_amount", "column_break_10", "total_billed_hours", "total_billable_amount", "total_billed_amount", "total_costing_amount", "per_billed", "section_break_18", "note", "amended_from"], "fields": [{"allow_on_submit": 1, "default": "{employee_name}", "fieldname": "title", "fieldtype": "Data", "hidden": 1, "label": "Title", "no_copy": 1, "print_hide": 1}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "TS-.YYYY.-", "reqd": 1, "set_only_once": 1}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "remember_last_selected_value": 1}, {"fieldname": "sales_invoice", "fieldtype": "Link", "label": "Sales Invoice", "no_copy": 1, "options": "Sales Invoice", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "in_standard_filter": 1, "label": "Status", "no_copy": 1, "options": "Draft\nSubmitted\nBilled\nPayslip\nCompleted\nCancelled", "print_hide": 1, "read_only": 1}, {"fieldname": "employee_detail", "fieldtype": "Section Break", "label": "Employee Detail"}, {"fieldname": "employee", "fieldtype": "Link", "in_standard_filter": 1, "label": "Employee", "options": "Employee"}, {"depends_on": "employee", "fetch_from": "employee.employee_name", "fieldname": "employee_name", "fieldtype": "Data", "in_global_search": 1, "label": "Employee Name", "print_hide": 1, "read_only": 1}, {"fetch_from": "employee.department", "fieldname": "department", "fieldtype": "Link", "label": "Department", "options": "Department", "read_only": 1}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "user", "fieldtype": "Link", "in_global_search": 1, "label": "User", "options": "User", "read_only": 1}, {"fieldname": "start_date", "fieldtype": "Date", "in_list_view": 1, "label": "Start Date", "print_hide": 1, "read_only": 1}, {"fieldname": "end_date", "fieldtype": "Date", "label": "End Date", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_5", "fieldtype": "Section Break"}, {"fieldname": "time_logs", "fieldtype": "Table", "label": "Time Sheets", "options": "Timesheet Detail", "reqd": 1}, {"fieldname": "working_hours", "fieldtype": "Section Break"}, {"allow_on_submit": 1, "default": "0", "fieldname": "total_hours", "fieldtype": "Float", "label": "Total Working Hours", "read_only": 1}, {"collapsible": 1, "fieldname": "billing_details", "fieldtype": "Section Break", "label": "Billing Details", "permlevel": 1}, {"allow_on_submit": 1, "fieldname": "total_billable_hours", "fieldtype": "Float", "label": "Total Billable Hours", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "fieldname": "total_billed_hours", "fieldtype": "Float", "label": "Total Billed Hours", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "fieldname": "total_costing_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Costing Amount", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_10", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "default": "0", "fieldname": "total_billable_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Billable Amount", "options": "currency", "read_only": 1}, {"allow_on_submit": 1, "fieldname": "total_billed_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Billed Amount", "options": "currency", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "fieldname": "per_billed", "fieldtype": "Percent", "in_list_view": 1, "label": "% Amount Billed", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_18", "fieldtype": "Section Break"}, {"fieldname": "note", "fieldtype": "Text Editor", "label": "Note"}, {"fieldname": "amended_from", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "options": "Timesheet", "print_hide": 1, "read_only": 1}, {"fieldname": "parent_project", "fieldtype": "Link", "label": "Project", "options": "Project"}, {"fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customer"}, {"fetch_from": "customer.default_currency", "fetch_if_empty": 1, "fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "base_total_costing_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Base Total Costing Amount", "print_hide": 1, "read_only": 1}, {"fieldname": "base_total_billable_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Base Total Billable Amount", "print_hide": 1, "read_only": 1}, {"fieldname": "base_total_billed_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Base Total Billed Amount", "print_hide": 1, "read_only": 1}, {"default": "1", "fieldname": "exchange_rate", "fieldtype": "Float", "label": "Exchange Rate"}], "icon": "fa fa-clock-o", "idx": 1, "is_submittable": 1, "links": [], "modified": "2023-04-20 15:59:11.107831", "modified_by": "Administrator", "module": "Projects", "name": "Timesheet", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Projects User", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR User", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Manufacturing User", "share": 1, "submit": 1, "write": 1}, {"create": 1, "read": 1, "role": "Employee", "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "read": 1, "report": 1, "role": "Accounts User", "submit": 1, "write": 1}, {"permlevel": 1, "read": 1, "role": "Accounts User", "write": 1}], "sort_field": "modified", "sort_order": "ASC", "states": [], "title_field": "title"}