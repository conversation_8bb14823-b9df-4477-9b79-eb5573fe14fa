{"actions": [], "autoname": "naming_series:", "creation": "2018-01-18 09:44:47.565494", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "project", "sent", "column_break_2", "date", "time", "section_break_5", "users", "amended_from"], "fields": [{"fieldname": "naming_series", "fieldtype": "Data", "hidden": 1, "label": "Series", "options": "PROJ-UPD-.YYYY.-"}, {"fieldname": "project", "fieldtype": "Link", "in_list_view": 1, "label": "Project", "options": "Project", "reqd": 1}, {"default": "0", "fieldname": "sent", "fieldtype": "Check", "label": "<PERSON><PERSON>", "read_only": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "date", "fieldtype": "Date", "label": "Date", "read_only": 1, "search_index": 1}, {"fieldname": "time", "fieldtype": "Time", "label": "Time", "read_only": 1}, {"fieldname": "section_break_5", "fieldtype": "Section Break"}, {"fieldname": "users", "fieldtype": "Table", "label": "Users", "options": "Project User"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Project Update", "print_hide": 1, "read_only": 1}], "is_submittable": 1, "links": [], "modified": "2023-06-28 18:59:50.678917", "modified_by": "Administrator", "module": "Projects", "name": "Project Update", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Projects User", "share": 1, "submit": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}