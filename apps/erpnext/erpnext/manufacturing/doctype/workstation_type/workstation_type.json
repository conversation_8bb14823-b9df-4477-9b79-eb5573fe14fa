{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:workstation_type", "creation": "2022-11-04 17:03:23.334818", "default_view": "List", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["workstation_type", "over_heads", "hour_rate_electricity", "hour_rate_consumable", "column_break_5", "hour_rate_rent", "hour_rate_labour", "section_break_8", "hour_rate", "description_tab", "description"], "fields": [{"fieldname": "workstation_type", "fieldtype": "Data", "in_list_view": 1, "label": "Workstation Type", "oldfieldname": "workstation_name", "oldfieldtype": "Data", "reqd": 1, "unique": 1}, {"fieldname": "over_heads", "fieldtype": "Section Break", "label": "Operating Costs", "oldfieldtype": "Section Break"}, {"description": "per hour", "fieldname": "hour_rate_electricity", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Electricity Cost", "oldfieldname": "hour_rate_electricity", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"description": "per hour", "fieldname": "hour_rate_consumable", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Consumable Cost", "oldfieldname": "hour_rate_consumable", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"description": "per hour", "fieldname": "hour_rate_rent", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rent Cost", "oldfieldname": "hour_rate_rent", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"description": "Wages per hour", "fieldname": "hour_rate_labour", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Wages", "oldfieldname": "hour_rate_labour", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"description": "per hour", "fieldname": "hour_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Hour Rate", "oldfieldname": "hour_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "description", "fieldtype": "Small Text", "in_list_view": 1, "label": "Description", "oldfieldname": "description", "oldfieldtype": "Text", "width": "300px"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"collapsible": 1, "fieldname": "description_tab", "fieldtype": "Tab Break", "label": "Description"}, {"fieldname": "section_break_8", "fieldtype": "Section Break"}], "icon": "icon-wrench", "links": [], "modified": "2022-11-16 23:11:36.224249", "modified_by": "Administrator", "module": "Manufacturing", "name": "Workstation Type", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Manufacturing User", "share": 1, "write": 1}], "quick_entry": 1, "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "ASC", "states": [], "track_changes": 1}