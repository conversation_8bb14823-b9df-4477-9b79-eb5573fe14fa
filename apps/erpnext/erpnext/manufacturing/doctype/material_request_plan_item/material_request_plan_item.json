{"actions": [], "creation": "2017-12-01 12:12:55.048691", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "from_warehouse", "warehouse", "item_name", "material_request_type", "quantity", "required_bom_qty", "column_break_4", "schedule_date", "uom", "conversion_factor", "item_details", "description", "min_order_qty", "section_break_8", "sales_order", "bin_qty_section", "actual_qty", "requested_qty", "reserved_qty_for_production", "column_break_yhelv", "ordered_qty", "projected_qty", "safety_stock"], "fields": [{"columns": 2, "fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>", "reqd": 1, "search_index": 1}, {"fieldname": "item_name", "fieldtype": "Data", "label": "Item Name"}, {"columns": 2, "fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "For Warehouse", "options": "Warehouse", "reqd": 1, "search_index": 1}, {"columns": 1, "fieldname": "material_request_type", "fieldtype": "Select", "in_list_view": 1, "label": "Type", "options": "\nPurchase\nMaterial Transfer\nMaterial Issue\nManufacture\nCustomer Provided"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"columns": 2, "fieldname": "quantity", "fieldtype": "Float", "in_list_view": 1, "label": "Plan to Request Qty", "no_copy": 1, "reqd": 1}, {"fieldname": "projected_qty", "fieldtype": "Float", "label": "Projected Qty", "read_only": 1}, {"columns": 1, "default": "0", "fieldname": "actual_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Qty In Stock", "no_copy": 1, "read_only": 1}, {"fieldname": "min_order_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Minimum Order Quantity", "read_only": 1}, {"collapsible": 1, "fieldname": "section_break_8", "fieldtype": "Section Break", "label": "Reference"}, {"fieldname": "sales_order", "fieldtype": "Link", "label": "Sales Order", "options": "Sales Order", "read_only": 1}, {"fieldname": "requested_qty", "fieldtype": "Float", "label": "Requested <PERSON><PERSON>", "read_only": 1}, {"collapsible": 1, "fieldname": "item_details", "fieldtype": "Section Break", "label": "Item Description"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description"}, {"fieldname": "uom", "fieldtype": "Link", "label": "UOM", "options": "UOM", "read_only": 1}, {"depends_on": "eval:doc.material_request_type == 'Material Transfer'", "fieldname": "from_warehouse", "fieldtype": "Link", "label": "From Warehouse", "options": "Warehouse", "search_index": 1}, {"fetch_from": "item_code.safety_stock", "fieldname": "safety_stock", "fieldtype": "Float", "label": "Safety Stock", "no_copy": 1, "read_only": 1}, {"fieldname": "ordered_qty", "fieldtype": "Float", "label": "Ordered Qty", "no_copy": 1, "read_only": 1}, {"fieldname": "reserved_qty_for_production", "fieldtype": "Float", "label": "Reserved Qty for Production", "no_copy": 1, "read_only": 1}, {"columns": 2, "fieldname": "required_bom_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Qty As Per BOM", "no_copy": 1, "read_only": 1}, {"fieldname": "conversion_factor", "fieldtype": "Float", "label": "Conversion Factor", "read_only": 1}, {"columns": 1, "fieldname": "schedule_date", "fieldtype": "Date", "in_list_view": 1, "label": "Required By"}, {"fieldname": "bin_qty_section", "fieldtype": "Section Break", "label": "BIN Qty"}, {"fieldname": "column_break_yhelv", "fieldtype": "Column Break"}], "istable": 1, "links": [], "modified": "2024-02-11 16:21:11.977018", "modified_by": "Administrator", "module": "Manufacturing", "name": "Material Request Plan Item", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}