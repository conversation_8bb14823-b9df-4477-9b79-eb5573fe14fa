{% $.each(stock_summary, (idx, row) => { %}
<div class="row" style="border-bottom:1px solid var(--border-color); padding:4px 5px; margin-top: 3px;margin-bottom: 3px;">
	<div class="col-sm-1">
		{% if(row.image) { %}
			<img style="width:50px;height:50px;" src="{{row.image}}">
		{% } else { %}
			<div style="width:50px;height:50px;background-color:var(--control-bg);text-align:center;padding-top:15px">{{frappe.get_abbr(row.item_code, 2)}}</div>
		{% } %}
	</div>
	<div class="col-sm-3">
		{% if (row.item_code === row.item_name) { %}
			{{row.item_link}}
		{% } else { %}
			{{row.item_link}}
			<p>
				{{row.item_name}}
			</p>
		{% } %}

	</div>
	<div class="col-sm-1" title="{{ __('Actual Qty') }}">
		{{ frappe.format(row.actual_qty, { fieldtype: "Float"})}}
	</div>
	<div class="col-sm-1" title="{{ __('Reserved Stock') }}">
		{{ frappe.format(row.reserved_stock, { fieldtype: "Float"})}}
	</div>
	<div class="col-sm-4 small">
		<span class="inline-graph">
			<span class="inline-graph-half" title="{{ __("Reserved Qty") }}">
				<span class="inline-graph-count">{{ row.total_reserved }}</span>
				<span class="inline-graph-bar">
					<span class="inline-graph-bar-inner"
						style="width: {{ cint(Math.abs(row.total_reserved)/row.max_count * 100) || 5 }}%">
					</span>
				</span>
			</span>
			<span class="inline-graph-half" title="{{ __("Actual Qty {0} / Waiting Qty {1}", [row.actual_qty, row.pending_qty]) }}">
				<span class="inline-graph-count">
					{{ row.actual_qty }} {{ (row.pending_qty > 0) ? ("(" + row.pending_qty+ ")") : "" }}
				</span>
				<span class="inline-graph-bar">
					<span class="inline-graph-bar-inner dark"
						style="width: {{ cint(row.actual_qty/row.max_count * 100) }}%">
					</span>
					{% if row.pending_qty > 0 %}
					<span class="inline-graph-bar-inner" title="{{ __("Projected Qty") }}"
						style="width: {{ cint(row.pending_qty/row.max_count * 100) }}%">
					</span>
					{% endif %}
				</span>
			</span>
		</span>
	</div>
	<div class="col-sm-1">
		<button style="margin-left: 7px;" class="btn btn-default btn-xs btn-add" data-item-code="{{ escape(row.item_code) }}">Add</button>
	</div>
	<div class="col-sm-1">
		<button style="margin-left: 7px;" class="btn btn-default btn-xs btn-move" data-item-code="{{ escape(row.item_code) }}">Move</button>
	</div>
</div>
{% }); %}