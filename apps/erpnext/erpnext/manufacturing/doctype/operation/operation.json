{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "Prompt", "creation": "2014-11-07 16:20:30.683186", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["workstation", "data_2", "is_corrective_operation", "job_card_section", "create_job_card_based_on_batch_size", "quality_inspection_template", "column_break_6", "batch_size", "sub_operations_section", "sub_operations", "total_operation_time", "section_break_4", "description"], "fields": [{"fieldname": "workstation", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Default Workstation", "options": "Workstation"}, {"collapsible": 1, "fieldname": "section_break_4", "fieldtype": "Section Break", "label": "Operation Description"}, {"fieldname": "description", "fieldtype": "Text", "label": "Description"}, {"collapsible": 1, "fieldname": "sub_operations_section", "fieldtype": "Section Break", "label": "Sub Operations"}, {"fieldname": "sub_operations", "fieldtype": "Table", "options": "Sub Operation"}, {"description": "Time in mins.", "fieldname": "total_operation_time", "fieldtype": "Float", "label": "Total Operation Time", "read_only": 1}, {"fieldname": "data_2", "fieldtype": "Column Break"}, {"default": "1", "depends_on": "create_job_card_based_on_batch_size", "fieldname": "batch_size", "fieldtype": "Int", "label": "<PERSON><PERSON> Si<PERSON>", "mandatory_depends_on": "create_job_card_based_on_batch_size"}, {"default": "0", "fieldname": "create_job_card_based_on_batch_size", "fieldtype": "Check", "label": "Create Job Card based on <PERSON><PERSON> Si<PERSON>"}, {"collapsible": 1, "fieldname": "job_card_section", "fieldtype": "Section Break", "label": "Job Card"}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "is_corrective_operation", "fieldtype": "Check", "label": "Is Corrective Operation"}, {"fieldname": "quality_inspection_template", "fieldtype": "Link", "label": "Quality Inspection Template", "options": "Quality Inspection Template"}], "icon": "fa fa-wrench", "index_web_pages_for_search": 1, "links": [], "modified": "2021-11-24 19:15:24.357187", "modified_by": "Administrator", "module": "Manufacturing", "name": "Operation", "naming_rule": "Set by user", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "export": 1, "import": 1, "read": 1, "role": "Manufacturing User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "export": 1, "import": 1, "read": 1, "report": 1, "role": "Manufacturing Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}