{"actions": [], "autoname": "hash", "creation": "2013-03-07 11:42:57", "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "item_name", "cb", "source_warehouse", "operation", "section_break_3", "description", "column_break_2", "image", "image_view", "section_break_4", "stock_qty", "rate", "qty_consumed_per_unit", "column_break_8", "stock_uom", "amount", "include_item_in_manufacturing", "sourced_by_supplier"], "fields": [{"fieldname": "item_code", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "read_only": 1, "search_index": 1}, {"fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "in_list_view": 1, "label": "Item Name", "read_only": 1}, {"fieldname": "cb", "fieldtype": "Column Break"}, {"fieldname": "source_warehouse", "fieldtype": "Link", "label": "Source Warehouse", "options": "Warehouse", "read_only": 1}, {"fieldname": "operation", "fieldtype": "Link", "label": "Operation", "options": "Operation", "read_only": 1}, {"fieldname": "section_break_3", "fieldtype": "Section Break"}, {"fieldname": "description", "fieldtype": "Text Editor", "in_list_view": 1, "label": "Description", "oldfieldname": "description", "oldfieldtype": "Text", "print_width": "300px", "read_only": 1, "width": "300px"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fetch_from": "item_code.image", "fieldname": "image", "fieldtype": "Attach", "hidden": 1, "label": "Image", "print_hide": 1}, {"fieldname": "image_view", "fieldtype": "Image", "label": "Image View", "options": "image"}, {"fieldname": "section_break_4", "fieldtype": "Section Break"}, {"fieldname": "stock_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Stock Qty", "oldfieldname": "qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "oldfieldname": "standard_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "qty_consumed_per_unit", "fieldtype": "Float", "in_list_view": 1, "label": "Qty Consumed Per Unit", "read_only": 1}, {"fieldname": "column_break_8", "fieldtype": "Column Break"}, {"fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "oldfieldname": "stock_uom", "oldfieldtype": "Link", "options": "UOM", "read_only": 1}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "oldfieldname": "amount_as_per_sr", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "read_only": 1}, {"default": "0", "fieldname": "include_item_in_manufacturing", "fieldtype": "Check", "label": "Include Item In Manufacturing", "read_only": 1}, {"default": "0", "fieldname": "sourced_by_supplier", "fieldtype": "Check", "label": "Sourced by Supplier", "read_only": 1}], "idx": 1, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-01-02 13:49:36.211586", "modified_by": "Administrator", "module": "Manufacturing", "name": "BOM Explosion Item", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}