{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:workstation_name", "creation": "2013-01-10 16:34:17", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["dashboard_tab", "workstation_dashboard", "details_tab", "workstation_name", "workstation_type", "plant_floor", "column_break_3", "production_capacity", "warehouse", "production_capacity_section", "parts_per_hour", "workstation_status_tab", "status", "column_break_glcv", "illustration_section", "on_status_image", "column_break_etmc", "off_status_image", "over_heads", "hour_rate_electricity", "hour_rate_consumable", "column_break_11", "hour_rate_rent", "hour_rate_labour", "section_break_11", "hour_rate", "workstaion_description", "description", "working_hours_section", "holiday_list", "working_hours", "total_working_hours", "connections_tab"], "fields": [{"fieldname": "workstation_name", "fieldtype": "Data", "in_list_view": 1, "label": "Workstation Name", "oldfieldname": "workstation_name", "oldfieldtype": "Data", "reqd": 1, "unique": 1}, {"fieldname": "description", "fieldtype": "Text", "in_list_view": 1, "label": "Description", "oldfieldname": "description", "oldfieldtype": "Text", "width": "300px"}, {"fieldname": "over_heads", "fieldtype": "Tab Break", "label": "Operating Costs", "oldfieldtype": "Section Break"}, {"bold": 1, "description": "per hour", "fieldname": "hour_rate_electricity", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Electricity Cost", "oldfieldname": "hour_rate_electricity", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"bold": 1, "description": "per hour", "fieldname": "hour_rate_consumable", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Consumable Cost", "oldfieldname": "hour_rate_consumable", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"bold": 1, "description": "per hour", "fieldname": "hour_rate_rent", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rent Cost", "oldfieldname": "hour_rate_rent", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"bold": 1, "description": "Wages per hour", "fieldname": "hour_rate_labour", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Wages", "oldfieldname": "hour_rate_labour", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"description": "per hour", "fieldname": "hour_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Hour Rate", "oldfieldname": "hour_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "working_hours_section", "fieldtype": "Tab Break", "label": "Working Hours"}, {"fieldname": "working_hours", "fieldtype": "Table", "label": "Working Hours", "options": "Workstation Working Hour"}, {"fieldname": "holiday_list", "fieldtype": "Link", "label": "Holiday List", "options": "Holiday List"}, {"default": "1", "description": "Run parallel job cards in a workstation", "fieldname": "production_capacity", "fieldtype": "Int", "label": "Job Capacity", "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"collapsible": 1, "fieldname": "workstaion_description", "fieldtype": "Tab Break", "label": "Description"}, {"bold": 1, "fieldname": "workstation_type", "fieldtype": "Link", "label": "Workstation Type", "options": "Workstation Type"}, {"fieldname": "section_break_11", "fieldtype": "Section Break"}, {"fieldname": "plant_floor", "fieldtype": "Link", "label": "Plant Floor", "options": "Plant Floor"}, {"fieldname": "workstation_status_tab", "fieldtype": "Tab Break", "label": "Workstation Status"}, {"fieldname": "illustration_section", "fieldtype": "Section Break", "label": "Status Illustration"}, {"fieldname": "column_break_etmc", "fieldtype": "Column Break"}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Production\nOff\nIdle\nProblem\nMaintenance\nSetup"}, {"fieldname": "column_break_glcv", "fieldtype": "Column Break"}, {"fieldname": "on_status_image", "fieldtype": "Attach Image", "label": "Active Status"}, {"fieldname": "off_status_image", "fieldtype": "Attach Image", "label": "Inactive Status"}, {"fieldname": "warehouse", "fieldtype": "Link", "label": "Warehouse", "options": "Warehouse"}, {"fieldname": "production_capacity_section", "fieldtype": "Section Break", "label": "Production Capacity"}, {"fieldname": "parts_per_hour", "fieldtype": "Float", "label": "Parts Per Hour"}, {"fieldname": "total_working_hours", "fieldtype": "Float", "label": "Total Working Hours"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "dashboard_tab", "fieldtype": "Tab Break", "label": "Job Cards"}, {"fieldname": "details_tab", "fieldtype": "Tab Break", "label": "Details"}, {"fieldname": "connections_tab", "fieldtype": "Tab Break", "label": "Connections", "show_dashboard": 1}, {"fieldname": "workstation_dashboard", "fieldtype": "HTML", "label": "Workstation Dashboard"}], "icon": "icon-wrench", "idx": 1, "image_field": "on_status_image", "links": [], "modified": "2023-11-30 12:43:35.808845", "modified_by": "Administrator", "module": "Manufacturing", "name": "Workstation", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Manufacturing User", "share": 1, "write": 1}], "quick_entry": 1, "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "ASC", "states": [], "track_changes": 1}