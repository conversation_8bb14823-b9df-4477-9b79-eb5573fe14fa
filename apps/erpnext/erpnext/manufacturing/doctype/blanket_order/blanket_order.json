{"actions": [], "autoname": "naming_series:", "creation": "2018-05-24 07:18:08.256060", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "blanket_order_type", "customer", "customer_name", "supplier", "supplier_name", "column_break_8", "from_date", "to_date", "company", "section_break_12", "items", "amended_from", "terms_and_conditions_section", "tc_name", "terms"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "MFG-BLR-.YYYY.-", "reqd": 1}, {"fieldname": "blanket_order_type", "fieldtype": "Select", "in_list_view": 1, "label": "Order Type", "options": "\nSelling\n<PERSON>", "reqd": 1}, {"depends_on": "eval:doc.blanket_order_type == \"Selling\"", "fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customer"}, {"depends_on": "eval:doc.blanket_order_type == \"Selling\"", "fetch_from": "customer.customer_name", "fieldname": "customer_name", "fieldtype": "Data", "label": "Customer Name", "read_only": 1}, {"depends_on": "eval:doc.blanket_order_type == \"Purchasing\"", "fieldname": "supplier", "fieldtype": "Link", "label": "Supplier", "options": "Supplier"}, {"depends_on": "eval:doc.blanket_order_type == \"Purchasing\"", "fetch_from": "supplier.supplier_name", "fieldname": "supplier_name", "fieldtype": "Data", "label": "Supplier Name", "read_only": 1}, {"fieldname": "column_break_8", "fieldtype": "Column Break"}, {"fieldname": "from_date", "fieldtype": "Date", "label": "From Date", "reqd": 1}, {"allow_on_submit": 1, "fieldname": "to_date", "fieldtype": "Date", "label": "To Date", "reqd": 1}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "reqd": 1, "search_index": 1}, {"fieldname": "section_break_12", "fieldtype": "Section Break"}, {"fieldname": "items", "fieldtype": "Table", "label": "<PERSON><PERSON>", "options": "Blanket Order Item", "reqd": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Blanket Order", "print_hide": 1, "read_only": 1}, {"fieldname": "terms_and_conditions_section", "fieldtype": "Section Break", "label": "Terms and Conditions"}, {"fieldname": "tc_name", "fieldtype": "Link", "label": "Terms", "options": "Terms and Conditions"}, {"fieldname": "terms", "fieldtype": "Text Editor", "label": "Terms and Conditions Details"}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2021-06-29 00:30:30.621636", "modified_by": "Administrator", "module": "Manufacturing", "name": "Blanket Order", "owner": "Administrator", "permissions": [{"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}], "quick_entry": 1, "search_fields": "blanket_order_type, to_date", "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}