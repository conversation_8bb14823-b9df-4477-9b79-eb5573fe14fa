{"actions": [], "autoname": "BOM-UPDT-LOG-.#####", "creation": "2022-03-16 14:23:35.210155", "description": "BOM Update Tool Log with job status maintained", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["update_type", "status", "column_break_3", "current_bom", "new_bom", "error_log", "progress_section", "current_level", "processed_boms", "bom_batches", "amended_from"], "fields": [{"fieldname": "current_bom", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Current BOM", "options": "BOM"}, {"fieldname": "new_bom", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "New BOM", "options": "BOM"}, {"depends_on": "eval:doc.update_type === \"Replace BOM\"", "fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "update_type", "fieldtype": "Select", "in_list_view": 1, "label": "Update Type", "options": "Replace BOM\nUpdate Cost"}, {"fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Queued\nIn Progress\nCompleted\nFailed"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "BOM Update Log", "print_hide": 1, "read_only": 1}, {"fieldname": "error_log", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON>"}, {"collapsible": 1, "depends_on": "eval: doc.update_type == \"Update Cost\"", "fieldname": "progress_section", "fieldtype": "Section Break", "label": "Progress"}, {"fieldname": "processed_boms", "fieldtype": "Long Text", "hidden": 1, "label": "Processed BOMs"}, {"fieldname": "bom_batches", "fieldtype": "Table", "options": "BOM Update Batch"}, {"depends_on": "eval:doc.status !== \"Completed\"", "fieldname": "current_level", "fieldtype": "Int", "label": "Current Level"}], "in_create": 1, "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2022-06-20 15:43:55.696388", "modified_by": "Administrator", "module": "Manufacturing", "name": "BOM Update Log", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Manufacturing Manager", "share": 1, "submit": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}