{"actions": [], "autoname": "naming_series:", "creation": "2018-07-09 17:23:29.518745", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "work_order", "bom_no", "production_item", "employee", "column_break_4", "posting_date", "company", "for_quantity", "total_completed_qty", "process_loss_qty", "scheduled_time_section", "expected_start_date", "time_required", "column_break_jkir", "expected_end_date", "section_break_05am", "scheduled_time_logs", "timing_detail", "time_logs", "section_break_13", "actual_start_date", "total_time_in_mins", "column_break_15", "actual_end_date", "production_section", "operation", "wip_warehouse", "column_break_12", "workstation_type", "workstation", "quality_inspection_section", "quality_inspection_template", "column_break_fcmp", "quality_inspection", "section_break_21", "sub_operations", "section_break_8", "items", "scrap_items_section", "scrap_items", "corrective_operation_section", "for_job_card", "is_corrective_job_card", "column_break_33", "hour_rate", "for_operation", "more_information", "project", "item_name", "transferred_qty", "requested_qty", "status", "column_break_20", "operation_row_number", "operation_id", "sequence_id", "remarks", "serial_and_batch_bundle", "batch_no", "serial_no", "barcode", "job_started", "started_time", "current_time", "amended_from", "connections_tab"], "fields": [{"fieldname": "work_order", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Work Order", "options": "Work Order", "reqd": 1, "search_index": 1}, {"fetch_from": "work_order.bom_no", "fieldname": "bom_no", "fieldtype": "Link", "label": "BOM No", "options": "BOM", "read_only": 1}, {"fieldname": "workstation", "fieldtype": "Link", "in_list_view": 1, "label": "Workstation", "options": "Workstation", "reqd": 1}, {"fieldname": "operation", "fieldtype": "Link", "in_list_view": 1, "label": "Operation", "options": "Operation", "reqd": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "label": "Posting Date"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "for_quantity", "fieldtype": "Float", "in_list_view": 1, "label": "Qty To Manufacture"}, {"fieldname": "wip_warehouse", "fieldtype": "Link", "label": "WIP Warehouse", "options": "Warehouse", "reqd": 1}, {"fieldname": "timing_detail", "fieldtype": "Section Break", "label": "Actual Time"}, {"allow_bulk_edit": 1, "fieldname": "time_logs", "fieldtype": "Table", "label": "Time Logs", "options": "Job Card Time Log"}, {"fieldname": "section_break_13", "fieldtype": "Section Break", "hide_border": 1}, {"default": "0", "fieldname": "total_completed_qty", "fieldtype": "Float", "label": "Total Completed Qty", "read_only": 1}, {"fieldname": "column_break_15", "fieldtype": "Column Break"}, {"fieldname": "total_time_in_mins", "fieldtype": "Float", "label": "Total Time in Mins", "read_only": 1}, {"fieldname": "section_break_8", "fieldtype": "Tab Break", "label": "Raw Materials"}, {"fieldname": "items", "fieldtype": "Table", "label": "Items", "options": "Job Card Item"}, {"collapsible": 1, "fieldname": "more_information", "fieldtype": "Tab Break", "label": "More Information"}, {"fieldname": "operation_id", "fieldtype": "Data", "hidden": 1, "label": "Operation ID", "read_only": 1}, {"default": "0", "fieldname": "transferred_qty", "fieldtype": "Float", "label": "FG Qty from Transferred Raw Materials", "read_only": 1}, {"default": "0", "fieldname": "requested_qty", "fieldtype": "Float", "label": "Requested <PERSON><PERSON>", "read_only": 1}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks"}, {"fieldname": "column_break_20", "fieldtype": "Column Break"}, {"default": "Open", "fieldname": "status", "fieldtype": "Select", "label": "Status", "no_copy": 1, "options": "Open\nWork In Progress\nMaterial Transferred\nOn Hold\nSubmitted\nCancelled\nCompleted", "read_only": 1}, {"default": "0", "fieldname": "job_started", "fieldtype": "Check", "hidden": 1, "label": "Job Started", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "started_time", "fieldtype": "Datetime", "hidden": 1, "label": "Started Time", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Job Card", "print_hide": 1, "read_only": 1}, {"default": "PO-JOB.#####", "fieldname": "naming_series", "fieldtype": "Select", "label": "Naming Series", "options": "PO-JOB.#####", "reqd": 1}, {"fieldname": "production_section", "fieldtype": "Tab Break", "label": "Operation & Workstation"}, {"fieldname": "column_break_12", "fieldtype": "Column Break"}, {"fetch_from": "work_order.production_item", "fieldname": "production_item", "fieldtype": "Link", "label": "Production Item", "options": "<PERSON><PERSON>", "read_only": 1}, {"fieldname": "barcode", "fieldtype": "Barcode", "label": "Barcode", "read_only": 1}, {"fetch_from": "production_item.item_name", "fetch_if_empty": 1, "fieldname": "item_name", "fieldtype": "Read Only", "label": "Item Name"}, {"fieldname": "current_time", "fieldtype": "Int", "hidden": 1, "label": "Current Time", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "operation_row_number", "fieldtype": "Select", "label": "Operation Row Number"}, {"fieldname": "sequence_id", "fieldtype": "Int", "label": "Sequence Id", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:!doc.__islocal;", "fieldname": "quality_inspection", "fieldtype": "Link", "label": "Quality Inspection", "no_copy": 1, "options": "Quality Inspection"}, {"allow_bulk_edit": 1, "fieldname": "sub_operations", "fieldtype": "Table", "label": "Sub Operations", "options": "Job Card Operation", "read_only": 1}, {"fieldname": "section_break_21", "fieldtype": "Tab Break", "hide_border": 1, "label": "Sub Operations"}, {"depends_on": "is_corrective_job_card", "fieldname": "hour_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Hour Rate"}, {"collapsible": 1, "depends_on": "is_corrective_job_card", "fieldname": "corrective_operation_section", "fieldtype": "Tab Break", "label": "Corrective Operation"}, {"default": "0", "fieldname": "is_corrective_job_card", "fieldtype": "Check", "label": "Is Corrective Job Card", "read_only": 1}, {"fieldname": "column_break_33", "fieldtype": "Column Break"}, {"fieldname": "for_job_card", "fieldtype": "Link", "label": "For Job Card", "options": "Job Card", "read_only": 1}, {"fetch_from": "for_job_card.operation", "fetch_if_empty": 1, "fieldname": "for_operation", "fieldtype": "Link", "label": "For Operation", "options": "Operation"}, {"fieldname": "employee", "fieldtype": "Table MultiSelect", "label": "Employee", "options": "Job Card Time Log"}, {"fieldname": "serial_no", "fieldtype": "Small Text", "hidden": 1, "label": "Serial No", "read_only": 1}, {"fieldname": "batch_no", "fieldtype": "Link", "hidden": 1, "label": "Batch No", "options": "<PERSON><PERSON>", "read_only": 1}, {"collapsible": 1, "fieldname": "scrap_items_section", "fieldtype": "Tab Break", "label": "Scrap Items"}, {"fieldname": "scrap_items", "fieldtype": "Table", "label": "Scrap Items", "no_copy": 1, "options": "Job Card Scrap Item", "print_hide": 1}, {"fetch_from": "operation.quality_inspection_template", "fieldname": "quality_inspection_template", "fieldtype": "Link", "label": "Quality Inspection Template", "options": "Quality Inspection Template"}, {"fieldname": "workstation_type", "fieldtype": "Link", "label": "Workstation Type", "options": "Workstation Type"}, {"fieldname": "expected_start_date", "fieldtype": "Datetime", "label": "Expected Start Date"}, {"fieldname": "expected_end_date", "fieldtype": "Datetime", "label": "Expected End Date"}, {"fieldname": "serial_and_batch_bundle", "fieldtype": "Link", "label": "Serial and Batch Bundle", "no_copy": 1, "options": "Serial and Batch Bundle", "print_hide": 1}, {"depends_on": "process_loss_qty", "fieldname": "process_loss_qty", "fieldtype": "Float", "label": "Process Loss Qty", "read_only": 1}, {"fieldname": "connections_tab", "fieldtype": "Tab Break", "label": "Connections", "show_dashboard": 1}, {"fieldname": "scheduled_time_section", "fieldtype": "Section Break", "label": "Scheduled Time"}, {"fieldname": "column_break_jkir", "fieldtype": "Column Break"}, {"fieldname": "time_required", "fieldtype": "Float", "label": "Expected Time Required (In Mins)"}, {"fieldname": "section_break_05am", "fieldtype": "Section Break"}, {"fieldname": "scheduled_time_logs", "fieldtype": "Table", "label": "Scheduled Time Logs", "options": "Job Card Scheduled Time", "read_only": 1}, {"fieldname": "actual_start_date", "fieldtype": "Datetime", "label": "Actual Start Date", "read_only": 1}, {"fieldname": "actual_end_date", "fieldtype": "Datetime", "label": "Actual End Date", "read_only": 1}, {"fieldname": "quality_inspection_section", "fieldtype": "Section Break", "label": "Quality Inspection"}, {"fieldname": "column_break_fcmp", "fieldtype": "Column Break"}], "is_submittable": 1, "links": [], "modified": "2023-06-28 19:23:14.345214", "modified_by": "Administrator", "module": "Manufacturing", "name": "Job Card", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Manufacturing User", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Manufacturing Manager", "share": 1, "submit": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "operation", "track_changes": 1}