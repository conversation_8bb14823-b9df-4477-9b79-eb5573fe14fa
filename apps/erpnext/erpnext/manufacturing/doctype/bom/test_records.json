[{"items": [{"amount": 5000.0, "doctype": "BOM Item", "item_code": "_Test Serialized Item With Series", "parentfield": "items", "qty": 1.0, "rate": 5000.0, "uom": "_Test UOM", "stock_uom": "_Test UOM", "source_warehouse": "_Test Warehouse - _TC", "include_item_in_manufacturing": 1}, {"amount": 2000.0, "doctype": "BOM Item", "item_code": "_Test Item 2", "parentfield": "items", "qty": 2.0, "rate": 1000.0, "uom": "_Test UOM", "stock_uom": "_Test UOM", "source_warehouse": "_Test Warehouse - _TC", "include_item_in_manufacturing": 1}], "docstatus": 1, "doctype": "BOM", "currency": "USD", "is_active": 1, "is_default": 1, "item": "_Test Item Home Desktop Manufactured", "company": "_Test Company", "quantity": 1.0}, {"scrap_items": [{"amount": 2000.0, "doctype": "BOM Scrap Item", "item_code": "_Test Item Home Desktop 100", "parentfield": "scrap_items", "stock_qty": 1.0, "rate": 2000.0, "stock_uom": "_Test UOM"}], "items": [{"amount": 10000.0, "doctype": "BOM Item", "item_code": "_Test Item", "parentfield": "items", "qty": 1.0, "rate": 5000.0, "uom": "_Test UOM", "stock_uom": "_Test UOM", "source_warehouse": "_Test Warehouse - _TC", "include_item_in_manufacturing": 1}, {"amount": 2000.0, "doctype": "BOM Item", "item_code": "_Test Item Home Desktop 100", "parentfield": "items", "qty": 2.0, "rate": 1000.0, "uom": "_Test UOM", "stock_uom": "_Test UOM", "source_warehouse": "_Test Warehouse - _TC", "include_item_in_manufacturing": 1}], "docstatus": 1, "doctype": "BOM", "is_active": 1, "is_default": 1, "currency": "USD", "item": "_Test FG Item", "quantity": 1.0}, {"operations": [{"operation": "_Test Operation 1", "description": "_Test", "workstation": "_Test Workstation 1", "hour_rate": 100, "time_in_mins": 60, "operating_cost": 100}], "items": [{"amount": 5000.0, "doctype": "BOM Item", "item_code": "_Test Item", "parentfield": "items", "qty": 1.0, "rate": 5000.0, "uom": "_Test UOM", "stock_uom": "_Test UOM", "source_warehouse": "_Test Warehouse - _TC", "include_item_in_manufacturing": 1}, {"amount": 3000.0, "bom_no": "BOM-_Test Item Home Desktop Manufactured-001", "doctype": "BOM Item", "item_code": "_Test Item Home Desktop Manufactured", "parentfield": "items", "qty": 3.0, "rate": 1000.0, "uom": "_Test UOM", "stock_uom": "_Test UOM", "source_warehouse": "_Test Warehouse - _TC", "include_item_in_manufacturing": 1}], "docstatus": 1, "doctype": "BOM", "is_active": 1, "is_default": 1, "currency": "USD", "conversion_rate": 60, "company": "_Test Company", "item": "_Test FG Item 2", "quantity": 1.0, "with_operations": 1}, {"operations": [{"operation": "_Test Operation 1", "description": "_Test", "workstation": "_Test Workstation 1", "time_in_mins": 60, "operating_cost": 140}], "items": [{"amount": 5000.0, "doctype": "BOM Item", "item_code": "_Test Item", "parentfield": "items", "qty": 2.0, "rate": 3000.0, "uom": "_Test UOM", "stock_uom": "_Test UOM", "source_warehouse": "_Test Warehouse - _TC", "include_item_in_manufacturing": 1}], "docstatus": 1, "doctype": "BOM", "is_active": 1, "is_default": 1, "currency": "USD", "item": "_Test V<PERSON>t <PERSON>", "quantity": 1.0, "with_operations": 1}, {"items": [{"amount": 5000.0, "doctype": "BOM Item", "item_code": "_Test Item", "parentfield": "items", "qty": 2.0, "rate": 3000.0, "uom": "_Test UOM", "stock_uom": "_Test UOM", "source_warehouse": "_Test Warehouse - _TC", "include_item_in_manufacturing": 1}], "docstatus": 1, "doctype": "BOM", "is_active": 1, "is_default": 1, "currency": "USD", "item": "_Test V<PERSON>t <PERSON>", "quantity": 1.0, "with_operations": 0, "fg_based_operating_cost": 1, "operating_cost_per_bom_quantity": 140}]