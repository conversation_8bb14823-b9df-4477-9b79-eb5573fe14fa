{"actions": [], "creation": "2018-05-24 07:20:04.255236", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "item_name", "party_item_code", "column_break_3", "qty", "rate", "ordered_qty", "section_break_7", "terms_and_conditions"], "fields": [{"fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>", "reqd": 1, "search_index": 1}, {"fetch_from": "item_code.item_name", "fieldname": "item_name", "fieldtype": "Data", "label": "Item Name"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity"}, {"fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "reqd": 1}, {"fieldname": "ordered_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Ordered Quantity", "no_copy": 1, "read_only": 1}, {"fieldname": "section_break_7", "fieldtype": "Section Break"}, {"fieldname": "terms_and_conditions", "fieldtype": "Text", "label": "Terms and Conditions"}, {"fieldname": "party_item_code", "fieldtype": "Data", "label": "Party Item Code", "read_only": 1}], "istable": 1, "links": [], "modified": "2024-02-14 18:25:26.479672", "modified_by": "Administrator", "module": "Manufacturing", "name": "Blanket Order Item", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}