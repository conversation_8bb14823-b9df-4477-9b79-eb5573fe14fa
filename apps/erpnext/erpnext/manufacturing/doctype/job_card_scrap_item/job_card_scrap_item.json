{"actions": [], "creation": "2021-09-14 00:30:28.533884", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "item_name", "column_break_3", "description", "quantity_and_rate", "stock_qty", "column_break_6", "stock_uom"], "fields": [{"fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Scrap Item Code", "options": "<PERSON><PERSON>", "reqd": 1}, {"fetch_from": "item_code.item_name", "fieldname": "item_name", "fieldtype": "Data", "in_list_view": 1, "label": "Scrap Item Name"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fetch_from": "item_code.description", "fieldname": "description", "fieldtype": "Small Text", "label": "Description", "read_only": 1}, {"fieldname": "quantity_and_rate", "fieldtype": "Section Break", "label": "Quantity and Rate"}, {"fieldname": "stock_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Qty", "reqd": 1}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"fetch_from": "item_code.stock_uom", "fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "options": "UOM", "read_only": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2021-09-14 01:20:48.588052", "modified_by": "Administrator", "module": "Manufacturing", "name": "Job Card Scrap Item", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}