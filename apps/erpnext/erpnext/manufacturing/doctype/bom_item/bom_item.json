{"actions": [], "creation": "2013-02-22 01:27:49", "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "item_name", "operation", "column_break_3", "do_not_explode", "bom_no", "source_warehouse", "allow_alternative_item", "is_stock_item", "section_break_5", "description", "col_break1", "image", "image_view", "quantity_and_rate", "qty", "uom", "col_break2", "stock_qty", "stock_uom", "conversion_factor", "rate_amount_section", "rate", "base_rate", "column_break_21", "amount", "base_amount", "section_break_18", "qty_consumed_per_unit", "section_break_27", "has_variants", "include_item_in_manufacturing", "original_item", "column_break_33", "sourced_by_supplier"], "fields": [{"columns": 3, "fieldname": "item_code", "fieldtype": "Link", "in_filter": 1, "in_list_view": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "reqd": 1, "search_index": 1}, {"columns": 3, "fieldname": "item_name", "fieldtype": "Data", "label": "Item Name"}, {"depends_on": "eval:parent.with_operations == 1", "fieldname": "operation", "fieldtype": "Link", "label": "Item operation", "options": "Operation"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"depends_on": "eval:!doc.do_not_explode", "fieldname": "bom_no", "fieldtype": "Link", "in_filter": 1, "label": "BOM No", "oldfieldname": "bom_no", "oldfieldtype": "Link", "options": "BOM", "print_width": "150px", "search_index": 1, "width": "150px"}, {"fieldname": "source_warehouse", "fieldtype": "Link", "label": "Source Warehouse", "options": "Warehouse"}, {"collapsible": 1, "fieldname": "section_break_5", "fieldtype": "Section Break", "label": "Description"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Item Description", "oldfieldname": "description", "oldfieldtype": "Text", "print_width": "250px", "width": "250px"}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"fetch_from": "item_code.image", "fieldname": "image", "fieldtype": "Attach", "hidden": 1, "label": "Image", "print_hide": 1}, {"fieldname": "image_view", "fieldtype": "Image", "label": "Image View", "options": "image"}, {"fieldname": "quantity_and_rate", "fieldtype": "Section Break", "label": "Quantity and Rate"}, {"columns": 2, "fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Qty", "oldfieldname": "qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "reqd": 1}, {"columns": 1, "fieldname": "uom", "fieldtype": "Link", "in_list_view": 1, "label": "UOM", "options": "UOM", "reqd": 1}, {"fieldname": "col_break2", "fieldtype": "Column Break"}, {"fieldname": "stock_qty", "fieldtype": "Float", "label": "Stock Qty", "oldfieldname": "stock_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "oldfieldname": "stock_uom", "oldfieldtype": "Data", "options": "UOM", "read_only": 1}, {"fieldname": "conversion_factor", "fieldtype": "Float", "label": "Conversion Factor"}, {"fieldname": "rate_amount_section", "fieldtype": "Section Break", "label": "Rate & Amount"}, {"fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "options": "currency", "read_only_depends_on": "eval:doc.is_stock_item == 1", "reqd": 1}, {"fieldname": "base_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Basic Rate (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_21", "fieldtype": "Column Break"}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "oldfieldname": "amount_as_per_mar", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "base_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_18", "fieldtype": "Section Break"}, {"fieldname": "qty_consumed_per_unit", "fieldtype": "Float", "hidden": 1, "label": "Qty Consumed Per Unit", "oldfieldname": "qty_consumed_per_unit", "oldfieldtype": "Float", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_27", "fieldtype": "Section Break"}, {"default": "0", "fieldname": "allow_alternative_item", "fieldtype": "Check", "label": "Allow Alternative Item"}, {"default": "0", "fetch_from": "item_code.include_item_in_manufacturing", "fieldname": "include_item_in_manufacturing", "fieldtype": "Check", "label": "Include Item In Manufacturing"}, {"fieldname": "original_item", "fieldtype": "Link", "hidden": 1, "label": "Original Item", "options": "<PERSON><PERSON>", "read_only": 1}, {"default": "0", "fetch_from": "item_code.has_variants", "fieldname": "has_variants", "fieldtype": "Check", "label": "<PERSON>", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_33", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "sourced_by_supplier", "fieldtype": "Check", "label": "Sourced by Supplier"}, {"default": "0", "fieldname": "do_not_explode", "fieldtype": "Check", "label": "Do Not Explode"}, {"default": "0", "fetch_from": "item_code.is_stock_item", "fieldname": "is_stock_item", "fieldtype": "Check", "label": "Is Stock Item", "read_only": 1}], "idx": 1, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2023-12-20 16:21:55.477883", "modified_by": "Administrator", "module": "Manufacturing", "name": "BOM Item", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}