{"attach_print": 0, "channel": "Email", "condition": "doc.status == \"Received\" or doc.status == \"Partially Received\"", "creation": "2019-04-29 11:53:23.981418", "days_in_advance": 0, "docstatus": 0, "doctype": "Notification", "document_type": "Material Request", "enabled": 1, "event": "Value Change", "idx": 0, "is_standard": 1, "message": "<b>Material Request Type</b>: {{ doc.material_request_type }}<br>\n<b>Company</b>: {{ doc.company }}\n\n<h3>Order Summary</h3>\n\n<table border=2 >\n    <tr align=\"center\">\n        <th>Item Name</th>\n        <th>Received Quantity</th>\n    </tr>\n    {% for item in doc.items %}\n        {% if frappe.utils.flt(item.received_qty, 2) > 0.0 %}\n            <tr align=\"center\">\n                <td>{{ item.item_code }}</td>\n                <td>{{ frappe.utils.flt(item.received_qty, 2) }}</td>\n            </tr>\n        {% endif %}\n    {% endfor %}\n</table>", "method": "", "modified": "2019-05-01 18:02:51.090037", "modified_by": "Administrator", "module": "Manufacturing", "name": "Material Request Receipt Notification", "owner": "Administrator", "recipients": [{"email_by_document_field": "requested_by"}], "sender_email": "", "subject": "{{ doc.name }} has been received", "value_changed": "status"}