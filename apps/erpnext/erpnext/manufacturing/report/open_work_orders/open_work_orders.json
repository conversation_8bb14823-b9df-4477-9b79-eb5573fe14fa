{"add_total_row": 0, "apply_user_permissions": 1, "creation": "2013-08-12 12:32:30", "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 3, "is_standard": "Yes", "modified": "2018-02-13 04:58:29.780472", "modified_by": "Administrator", "module": "Manufacturing", "name": "Open Work Orders", "owner": "Administrator", "query": "SELECT\n  `tabWork Order`.name as \"Work Order:Link/Work Order:200\",\n  `tabWork Order`.creation as \"Date:Date:120\",\n  `tabWork Order`.production_item as \"Item:Link/Item:150\",\n  `tabWork Order`.qty as \"To Produce:Int:100\",\n  `tabWork Order`.produced_qty as \"Produced:Int:100\",\n  `tabWork Order`.company as \"Company:Link/Company:\"\nFROM\n  `tabWork Order`\nWHERE\n  `tabWork Order`.docstatus=1\n  AND ifnull(`tabWork Order`.produced_qty,0) < `tabWork Order`.qty\n  AND NOT EXISTS (SELECT name from `tabStock Entry` where work_order =`tabWork Order`.name) ", "ref_doctype": "Work Order", "report_name": "Open Work Orders", "report_type": "Query Report", "roles": [{"role": "Manufacturing User"}, {"role": "Stock User"}]}