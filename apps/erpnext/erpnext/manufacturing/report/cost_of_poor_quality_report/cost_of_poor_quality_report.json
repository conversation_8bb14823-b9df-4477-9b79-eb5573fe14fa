{"add_total_row": 0, "columns": [], "creation": "2021-01-11 11:10:58.292896", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "filters": [], "idx": 0, "is_standard": "Yes", "json": "{}", "modified": "2021-01-11 11:11:03.594242", "modified_by": "Administrator", "module": "Manufacturing", "name": "Cost of Poor Quality Report", "owner": "Administrator", "prepared_report": 0, "ref_doctype": "Job Card", "report_name": "Cost of Poor Quality Report", "report_type": "Script Report", "roles": [{"role": "System Manager"}, {"role": "Manufacturing User"}, {"role": "Manufacturing Manager"}]}