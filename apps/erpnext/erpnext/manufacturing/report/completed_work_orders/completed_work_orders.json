{"add_total_row": 0, "columns": [], "creation": "2013-08-12 12:44:27", "disabled": 0, "docstatus": 0, "doctype": "Report", "filters": [], "idx": 3, "is_standard": "Yes", "letterhead": null, "modified": "2024-02-21 14:35:14.301848", "modified_by": "Administrator", "module": "Manufacturing", "name": "Completed Work Orders", "owner": "Administrator", "prepared_report": 0, "query": "SELECT\n  `tabWork Order`.name as \"Work Order:Link/Work Order:200\",\n  `tabWork Order`.creation as \"Date:Date:120\",\n  `tabWork Order`.production_item as \"Item:Link/Item:150\",\n  `tabWork Order`.qty as \"To Produce:Int:100\",\n  `tabWork Order`.produced_qty as \"Produced:Int:100\",\n  `tabWork Order`.company as \"Company:Link/Company:\"\nFROM\n  `tabWork Order`\nWHERE\n  `tabWork Order`.docstatus=1\n  AND ifnull(`tabWork Order`.produced_qty,0) >= `tabWork Order`.qty", "ref_doctype": "Work Order", "report_name": "Completed Work Orders", "report_type": "Query Report", "roles": [{"role": "Manufacturing User"}, {"role": "Stock User"}]}