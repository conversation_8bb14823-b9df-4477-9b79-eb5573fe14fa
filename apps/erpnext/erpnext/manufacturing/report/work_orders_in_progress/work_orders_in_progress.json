{"add_total_row": 0, "creation": "2013-08-12 12:43:47", "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 3, "is_standard": "Yes", "modified": "2018-09-18 12:24:38.684995", "modified_by": "Administrator", "module": "Manufacturing", "name": "Work Orders in Progress", "owner": "Administrator", "prepared_report": 0, "query": "SELECT\n  `tabWork Order`.name as \"Work Order:Link/Work Order:200\",\n  `tabWork Order`.creation as \"Date:Date:120\",\n  `tabWork Order`.production_item as \"Item:Link/Item:150\",\n  `tabWork Order`.qty as \"To Produce:Int:100\",\n  `tabWork Order`.produced_qty as \"Produced:Int:100\",\n  `tabWork Order`.company as \"Company:Link/Company:\"\nFROM\n  `tabWork Order`\nWHERE\n  `tabWork Order`.docstatus=1\n  AND `tabWork Order`.status != 'Stopped'\n  AND ifnull(`tabWork Order`.produced_qty,0) < `tabWork Order`.qty\n  AND EXISTS (SELECT name from `tabStock Entry` where work_order =`tabWork Order`.name) ", "ref_doctype": "Work Order", "report_name": "Work Orders in Progress", "report_type": "Query Report", "roles": [{"role": "Manufacturing User"}, {"role": "Stock User"}]}