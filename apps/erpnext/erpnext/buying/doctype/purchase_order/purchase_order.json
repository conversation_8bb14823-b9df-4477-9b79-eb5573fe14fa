{"actions": [], "allow_auto_repeat": 1, "allow_import": 1, "autoname": "naming_series:", "creation": "2013-05-21 16:16:39", "doctype": "DocType", "document_type": "Document", "engine": "InnoDB", "field_order": ["supplier_section", "title", "naming_series", "supplier", "supplier_name", "order_confirmation_no", "order_confirmation_date", "get_items_from_open_material_requests", "column_break_7", "transaction_date", "schedule_date", "column_break1", "company", "apply_tds", "tax_withholding_category", "is_subcontracted", "supplier_warehouse", "amended_from", "accounting_dimensions_section", "cost_center", "dimension_col_break", "project", "currency_and_price_list", "currency", "conversion_rate", "cb_price_list", "buying_price_list", "price_list_currency", "plc_conversion_rate", "ignore_pricing_rule", "before_items_section", "scan_barcode", "set_from_warehouse", "items_col_break", "set_warehouse", "items_section", "items", "sb_last_purchase", "total_qty", "total_net_weight", "column_break_40", "base_total", "base_net_total", "column_break_26", "total", "net_total", "tax_withholding_net_total", "base_tax_withholding_net_total", "section_break_48", "pricing_rules", "raw_material_details", "set_reserve_warehouse", "supplied_items", "taxes_section", "tax_category", "taxes_and_charges", "column_break_53", "shipping_rule", "column_break_50", "incoterm", "named_place", "section_break_52", "taxes", "totals", "base_taxes_and_charges_added", "base_taxes_and_charges_deducted", "base_total_taxes_and_charges", "column_break_39", "taxes_and_charges_added", "taxes_and_charges_deducted", "total_taxes_and_charges", "totals_section", "base_grand_total", "base_rounding_adjustment", "base_in_words", "base_rounded_total", "column_break4", "grand_total", "rounding_adjustment", "rounded_total", "disable_rounded_total", "in_words", "advance_paid", "discount_section", "apply_discount_on", "base_discount_amount", "column_break_45", "additional_discount_percentage", "discount_amount", "sec_tax_breakup", "other_charges_calculation", "address_and_contact_tab", "section_addresses", "supplier_address", "address_display", "col_break_address", "contact_person", "contact_display", "contact_mobile", "contact_email", "shipping_address_section", "shipping_address", "column_break_99", "shipping_address_display", "company_billing_address_section", "billing_address", "column_break_103", "billing_address_display", "drop_ship", "customer", "customer_name", "column_break_19", "customer_contact_person", "customer_contact_display", "customer_contact_mobile", "customer_contact_email", "terms_tab", "payment_schedule_section", "payment_terms_template", "payment_schedule", "terms_section_break", "tc_name", "terms", "more_info_tab", "tracking_section", "status", "column_break_75", "per_billed", "per_received", "column_break5", "letter_head", "group_same_items", "column_break_86", "select_print_heading", "language", "subscription_section", "from_date", "to_date", "column_break_97", "auto_repeat", "update_auto_repeat_reference", "additional_info_section", "is_internal_supplier", "represents_company", "ref_sq", "column_break_74", "party_account_currency", "inter_company_order_reference", "is_old_subcontracting_flow", "connections_tab"], "fields": [{"fieldname": "supplier_section", "fieldtype": "Section Break", "options": "fa fa-user"}, {"allow_on_submit": 1, "default": "{supplier_name}", "fieldname": "title", "fieldtype": "Data", "hidden": 1, "label": "Title", "no_copy": 1, "print_hide": 1, "reqd": 1}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "oldfieldname": "naming_series", "oldfieldtype": "Select", "options": "PUR-ORD-.YYYY.-", "print_hide": 1, "reqd": 1, "set_only_once": 1}, {"bold": 1, "fieldname": "supplier", "fieldtype": "Link", "in_global_search": 1, "in_standard_filter": 1, "label": "Supplier", "oldfieldname": "supplier", "oldfieldtype": "Link", "options": "Supplier", "print_hide": 1, "reqd": 1, "search_index": 1}, {"depends_on": "eval:doc.supplier && doc.docstatus===0 && (!(doc.items && doc.items.length) || (doc.items.length==1 && !doc.items[0].item_code))", "description": "Fetch items based on Default Supplier.", "fieldname": "get_items_from_open_material_requests", "fieldtype": "<PERSON><PERSON>", "label": "Get Items from Open Material Requests"}, {"bold": 1, "fetch_from": "supplier.supplier_name", "fieldname": "supplier_name", "fieldtype": "Data", "in_global_search": 1, "label": "Supplier Name", "read_only": 1}, {"fieldname": "company", "fieldtype": "Link", "in_standard_filter": 1, "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "print_hide": 1, "remember_last_selected_value": 1, "reqd": 1}, {"fieldname": "column_break1", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "print_width": "50%", "width": "50%"}, {"default": "Today", "fieldname": "transaction_date", "fieldtype": "Date", "in_list_view": 1, "label": "Date", "oldfieldname": "transaction_date", "oldfieldtype": "Date", "reqd": 1, "search_index": 1}, {"allow_on_submit": 1, "fieldname": "schedule_date", "fieldtype": "Date", "label": "Required By"}, {"allow_on_submit": 1, "depends_on": "eval:doc.docstatus===1", "fieldname": "order_confirmation_no", "fieldtype": "Data", "label": "Order Confirmation No"}, {"allow_on_submit": 1, "depends_on": "eval:doc.order_confirmation_no", "fieldname": "order_confirmation_date", "fieldtype": "Date", "label": "Order Confirmation Date"}, {"fieldname": "amended_from", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "oldfieldname": "amended_from", "oldfieldtype": "Data", "options": "Purchase Order", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:doc.customer", "fieldname": "drop_ship", "fieldtype": "Tab Break", "label": "Drop Ship"}, {"fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customer", "read_only": 1}, {"bold": 1, "fieldname": "customer_name", "fieldtype": "Data", "label": "Customer Name", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_19", "fieldtype": "Column Break"}, {"fieldname": "customer_contact_person", "fieldtype": "Link", "label": "Customer Contact", "options": "Contact"}, {"fieldname": "customer_contact_display", "fieldtype": "Small Text", "label": "Customer Contact", "print_hide": 1}, {"fieldname": "customer_contact_mobile", "fieldtype": "Small Text", "hidden": 1, "label": "Customer Mobile No", "options": "Phone", "print_hide": 1}, {"fieldname": "customer_contact_email", "fieldtype": "Code", "hidden": 1, "label": "Customer Contact Email", "options": "Email", "print_hide": 1}, {"fieldname": "section_addresses", "fieldtype": "Section Break", "label": "Supplier Address"}, {"fieldname": "supplier_address", "fieldtype": "Link", "label": "Supplier Address", "options": "Address", "print_hide": 1}, {"fieldname": "contact_person", "fieldtype": "Link", "label": "Supplier Contact", "options": "Contact", "print_hide": 1}, {"fieldname": "address_display", "fieldtype": "Small Text", "label": "Supplier Address Details", "read_only": 1}, {"fieldname": "contact_display", "fieldtype": "Small Text", "in_global_search": 1, "label": "Contact Name", "read_only": 1}, {"fieldname": "contact_mobile", "fieldtype": "Small Text", "label": "Contact Mobile No", "options": "Phone", "read_only": 1}, {"fieldname": "contact_email", "fieldtype": "Small Text", "label": "Contact Email", "options": "Email", "print_hide": 1, "read_only": 1}, {"fieldname": "col_break_address", "fieldtype": "Column Break"}, {"fieldname": "shipping_address", "fieldtype": "Link", "label": "Shipping Address", "options": "Address", "print_hide": 1}, {"fieldname": "shipping_address_display", "fieldtype": "Small Text", "label": "Shipping Address Details", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "currency_and_price_list", "fieldtype": "Section Break", "label": "Currency and Price List", "options": "fa fa-tag"}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "oldfieldname": "currency", "oldfieldtype": "Select", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "reqd": 1}, {"fieldname": "conversion_rate", "fieldtype": "Float", "label": "Exchange Rate", "oldfieldname": "conversion_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "precision": "9", "print_hide": 1, "reqd": 1}, {"fieldname": "cb_price_list", "fieldtype": "Column Break"}, {"fieldname": "buying_price_list", "fieldtype": "Link", "label": "Price List", "options": "Price List", "print_hide": 1}, {"fieldname": "price_list_currency", "fieldtype": "Link", "label": "Price List Currency", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"fieldname": "plc_conversion_rate", "fieldtype": "Float", "label": "Price List Exchange Rate", "precision": "9", "print_hide": 1}, {"default": "0", "fieldname": "ignore_pricing_rule", "fieldtype": "Check", "label": "Ignore Pricing Rule", "permlevel": 1, "print_hide": 1}, {"fieldname": "set_warehouse", "fieldtype": "Link", "label": "Set Target Warehouse", "options": "Warehouse", "print_hide": 1}, {"default": "0", "fieldname": "is_subcontracted", "fieldtype": "Check", "label": "Is Subcontracted", "print_hide": 1}, {"depends_on": "eval:doc.is_subcontracted", "fieldname": "supplier_warehouse", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Supplier Warehouse", "options": "Warehouse"}, {"fieldname": "items_section", "fieldtype": "Section Break", "hide_border": 1, "oldfieldtype": "Section Break", "options": "fa fa-shopping-cart"}, {"fieldname": "scan_barcode", "fieldtype": "Data", "label": "Scan Barcode", "options": "Barcode"}, {"allow_bulk_edit": 1, "fieldname": "items", "fieldtype": "Table", "label": "Items", "oldfieldname": "po_details", "oldfieldtype": "Table", "options": "Purchase Order Item", "reqd": 1}, {"collapsible": 1, "fieldname": "section_break_48", "fieldtype": "Section Break", "label": "Pricing Rules"}, {"fieldname": "pricing_rules", "fieldtype": "Table", "label": "Purchase Order Pricing Rule", "options": "Pricing Rule Detail", "read_only": 1}, {"collapsible_depends_on": "supplied_items", "fieldname": "raw_material_details", "fieldtype": "Section Break", "label": "Raw Materials Supplied"}, {"fieldname": "supplied_items", "fieldtype": "Table", "label": "Supplied Items", "no_copy": 1, "oldfieldname": "po_raw_material_details", "oldfieldtype": "Table", "options": "Purchase Order Item Supplied", "print_hide": 1, "read_only": 1}, {"fieldname": "sb_last_purchase", "fieldtype": "Section Break"}, {"fieldname": "total_qty", "fieldtype": "Float", "label": "Total Quantity", "read_only": 1}, {"fieldname": "base_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total (Company Currency)", "no_copy": 1, "oldfieldname": "net_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_26", "fieldtype": "Column Break"}, {"fieldname": "total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total", "options": "currency", "read_only": 1}, {"fieldname": "net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total", "oldfieldname": "net_total_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "read_only": 1}, {"depends_on": "total_net_weight", "fieldname": "total_net_weight", "fieldtype": "Float", "label": "Total Net Weight", "print_hide": 1, "read_only": 1}, {"fieldname": "taxes_section", "fieldtype": "Section Break", "hide_border": 1, "label": "Taxes and Charges", "oldfieldtype": "Section Break", "options": "fa fa-money"}, {"fieldname": "taxes_and_charges", "fieldtype": "Link", "label": "Purchase Taxes and Charges Template", "oldfieldname": "purchase_other_charges", "oldfieldtype": "Link", "options": "Purchase Taxes and Charges Template", "print_hide": 1}, {"fieldname": "column_break_50", "fieldtype": "Column Break"}, {"fieldname": "shipping_rule", "fieldtype": "Link", "label": "Shipping Rule", "options": "Shipping Rule", "print_hide": 1}, {"fieldname": "section_break_52", "fieldtype": "Section Break", "hide_border": 1}, {"fieldname": "taxes", "fieldtype": "Table", "label": "Purchase Taxes and Charges", "oldfieldname": "purchase_tax_details", "oldfieldtype": "Table", "options": "Purchase Taxes and Charges"}, {"collapsible": 1, "fieldname": "sec_tax_breakup", "fieldtype": "Section Break", "label": "Tax Breakup"}, {"fieldname": "other_charges_calculation", "fieldtype": "Long Text", "label": "Taxes and Charges Calculation", "no_copy": 1, "oldfieldtype": "HTML", "print_hide": 1, "read_only": 1}, {"fieldname": "totals", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "fa fa-money"}, {"depends_on": "base_taxes_and_charges_added", "fieldname": "base_taxes_and_charges_added", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Added (Company Currency)", "oldfieldname": "other_charges_added", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"depends_on": "base_taxes_and_charges_deducted", "fieldname": "base_taxes_and_charges_deducted", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Deducted (Company Currency)", "oldfieldname": "other_charges_deducted", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"depends_on": "base_total_taxes_and_charges", "fieldname": "base_total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Taxes and Charges (Company Currency)", "no_copy": 1, "oldfieldname": "total_tax", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_39", "fieldtype": "Column Break"}, {"depends_on": "taxes_and_charges_added", "fieldname": "taxes_and_charges_added", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Added", "oldfieldname": "other_charges_added_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "read_only": 1}, {"depends_on": "taxes_and_charges_deducted", "fieldname": "taxes_and_charges_deducted", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Deducted", "oldfieldname": "other_charges_deducted_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Taxes and Charges", "options": "currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "discount_section", "fieldtype": "Section Break", "label": "Additional Discount"}, {"default": "Grand Total", "fieldname": "apply_discount_on", "fieldtype": "Select", "label": "Apply Additional Discount On", "options": "\nGrand Total\nNet Total", "print_hide": 1}, {"fieldname": "base_discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Discount Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_45", "fieldtype": "Column Break"}, {"fieldname": "additional_discount_percentage", "fieldtype": "Float", "label": "Additional Discount Percentage", "print_hide": 1}, {"fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Discount Amount", "options": "currency", "print_hide": 1}, {"fieldname": "totals_section", "fieldtype": "Section Break", "label": "Totals"}, {"fieldname": "base_grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Grand Total (Company Currency)", "no_copy": 1, "oldfieldname": "grand_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "base_rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounding Adjustment (Company Currency)", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_in_words", "fieldtype": "Data", "label": "In Words (Company Currency)", "length": 240, "oldfieldname": "in_words", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1}, {"fieldname": "base_rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounded Total (Company Currency)", "oldfieldname": "rounded_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break4", "fieldtype": "Column Break", "oldfieldtype": "Column Break"}, {"fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Grand Total", "oldfieldname": "grand_total_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounding Adjustment", "no_copy": 1, "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounded Total", "options": "currency", "read_only": 1}, {"default": "0", "fieldname": "disable_rounded_total", "fieldtype": "Check", "label": "Disable Rounded Total"}, {"fieldname": "in_words", "fieldtype": "Data", "label": "In Words", "length": 240, "oldfieldname": "in_words_import", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1}, {"fieldname": "advance_paid", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Advance Paid", "no_copy": 1, "options": "party_account_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "payment_schedule_section", "fieldtype": "Section Break", "label": "Payment Terms"}, {"fieldname": "payment_terms_template", "fieldtype": "Link", "label": "Payment Terms Template", "options": "Payment Terms Template"}, {"fieldname": "payment_schedule", "fieldtype": "Table", "label": "Payment Schedule", "no_copy": 1, "options": "Payment Schedule", "print_hide": 1}, {"collapsible_depends_on": "terms", "fieldname": "terms_section_break", "fieldtype": "Section Break", "label": "Terms & Conditions", "oldfieldtype": "Section Break", "options": "fa fa-legal"}, {"fieldname": "tc_name", "fieldtype": "Link", "label": "Terms", "oldfieldname": "tc_name", "oldfieldtype": "Link", "options": "Terms and Conditions", "print_hide": 1}, {"fieldname": "terms", "fieldtype": "Text Editor", "label": "Terms and Conditions", "oldfieldname": "terms", "oldfieldtype": "Text Editor"}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "in_standard_filter": 1, "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Select", "options": "\nDraft\nOn Hold\nTo Receive and Bill\nTo Bill\nTo Receive\nCompleted\nCancelled\nClosed\nDelivered", "print_hide": 1, "read_only": 1, "reqd": 1, "search_index": 1}, {"fieldname": "ref_sq", "fieldtype": "Link", "label": "Supplier Quotation", "no_copy": 1, "oldfieldname": "ref_sq", "oldfieldtype": "Data", "options": "Supplier Quotation", "print_hide": 1, "read_only": 1}, {"fieldname": "party_account_currency", "fieldtype": "Link", "hidden": 1, "label": "Party Account <PERSON><PERSON><PERSON><PERSON>", "no_copy": 1, "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"fieldname": "inter_company_order_reference", "fieldtype": "Link", "label": "Inter Company Order Reference", "options": "Sales Order", "read_only": 1}, {"fieldname": "column_break_74", "fieldtype": "Column Break"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "per_received", "fieldtype": "Percent", "in_list_view": 1, "label": "% Received", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"depends_on": "eval:!doc.__islocal", "fieldname": "per_billed", "fieldtype": "Percent", "in_list_view": 1, "label": "% Billed", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "column_break5", "fieldtype": "Section Break", "label": "Printing Settings", "oldfieldtype": "Column Break", "print_hide": 1, "print_width": "50%", "width": "50%"}, {"allow_on_submit": 1, "fieldname": "letter_head", "fieldtype": "Link", "label": "Letter Head", "oldfieldname": "letter_head", "oldfieldtype": "Select", "options": "Letter Head", "print_hide": 1}, {"allow_on_submit": 1, "fieldname": "select_print_heading", "fieldtype": "Link", "label": "Print Heading", "no_copy": 1, "oldfieldname": "select_print_heading", "oldfieldtype": "Link", "options": "Print Heading", "print_hide": 1, "report_hide": 1}, {"fieldname": "column_break_86", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "default": "0", "fieldname": "group_same_items", "fieldtype": "Check", "label": "Group same items", "print_hide": 1}, {"fieldname": "language", "fieldtype": "Data", "label": "Print Language", "print_hide": 1}, {"collapsible": 1, "fieldname": "subscription_section", "fieldtype": "Section Break", "label": "Auto Repeat"}, {"allow_on_submit": 1, "fieldname": "from_date", "fieldtype": "Date", "label": "From Date", "no_copy": 1, "print_hide": 1}, {"allow_on_submit": 1, "fieldname": "to_date", "fieldtype": "Date", "label": "To Date", "no_copy": 1, "print_hide": 1}, {"fieldname": "column_break_97", "fieldtype": "Column Break"}, {"fieldname": "auto_repeat", "fieldtype": "Link", "label": "Auto Repeat", "no_copy": 1, "options": "Auto Repeat", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "depends_on": "eval: doc.auto_repeat", "fieldname": "update_auto_repeat_reference", "fieldtype": "<PERSON><PERSON>", "label": "Update Auto Repeat Reference"}, {"fieldname": "tax_category", "fieldtype": "Link", "label": "Tax Category", "options": "Tax Category"}, {"depends_on": "supplied_items", "fieldname": "set_reserve_warehouse", "fieldtype": "Link", "label": "Set Reserve Warehouse", "options": "Warehouse"}, {"collapsible": 1, "fieldname": "tracking_section", "fieldtype": "Section Break", "label": "Order Status"}, {"fieldname": "column_break_75", "fieldtype": "Column Break"}, {"fieldname": "billing_address", "fieldtype": "Link", "label": "Company Billing Address", "options": "Address"}, {"fieldname": "billing_address_display", "fieldtype": "Small Text", "label": "Billing Address Details", "read_only": 1}, {"fieldname": "before_items_section", "fieldtype": "Section Break", "hide_border": 1}, {"fieldname": "items_col_break", "fieldtype": "Column Break"}, {"default": "0", "fetch_from": "supplier.is_internal_supplier", "fieldname": "is_internal_supplier", "fieldtype": "Check", "label": "Is Internal Supplier", "read_only": 1}, {"fetch_from": "supplier.represents_company", "fieldname": "represents_company", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Represents Company", "options": "Company", "read_only": 1}, {"default": "0", "fieldname": "apply_tds", "fieldtype": "Check", "label": "Apply Tax Withholding Amount"}, {"depends_on": "eval: doc.apply_tds", "fieldname": "tax_withholding_category", "fieldtype": "Link", "label": "Tax Withholding Category", "options": "Tax Withholding Category"}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions "}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project"}, {"default": "0", "fieldname": "is_old_subcontracting_flow", "fieldtype": "Check", "hidden": 1, "label": "Is Old Subcontracting Flow", "read_only": 1}, {"depends_on": "is_internal_supplier", "fieldname": "set_from_warehouse", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Set From Warehouse", "options": "Warehouse"}, {"fieldname": "terms_tab", "fieldtype": "Tab Break", "label": "Terms"}, {"fieldname": "more_info_tab", "fieldtype": "Tab Break", "label": "More Info"}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fieldname": "column_break_40", "fieldtype": "Column Break"}, {"fieldname": "column_break_53", "fieldtype": "Column Break"}, {"fieldname": "address_and_contact_tab", "fieldtype": "Tab Break", "label": "Address & Contact"}, {"fieldname": "company_billing_address_section", "fieldtype": "Section Break", "label": "Company Billing Address"}, {"collapsible": 1, "fieldname": "additional_info_section", "fieldtype": "Section Break", "label": "Additional Info", "oldfieldtype": "Section Break"}, {"default": "0", "depends_on": "apply_tds", "fieldname": "tax_withholding_net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "Tax Withholding Net Total", "no_copy": 1, "options": "currency", "read_only": 1}, {"depends_on": "apply_tds", "fieldname": "base_tax_withholding_net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "Base Tax Withholding Net Total", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_99", "fieldtype": "Column Break"}, {"fieldname": "column_break_103", "fieldtype": "Column Break"}, {"fieldname": "incoterm", "fieldtype": "Link", "label": "Incoterm", "options": "Incoterm"}, {"depends_on": "incoterm", "fieldname": "named_place", "fieldtype": "Data", "label": "Named Place"}, {"fieldname": "shipping_address_section", "fieldtype": "Section Break", "label": "Shipping Address"}, {"fieldname": "connections_tab", "fieldtype": "Tab Break", "label": "Connections", "show_dashboard": 1}], "icon": "fa fa-file-text", "idx": 105, "is_submittable": 1, "links": [], "modified": "2023-10-01 20:58:07.851037", "modified_by": "Administrator", "module": "Buying", "name": "Purchase Order", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"read": 1, "report": 1, "role": "Stock User"}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase User", "share": 1, "submit": 1, "write": 1}, {"permlevel": 1, "read": 1, "role": "Purchase Manager", "write": 1}], "search_fields": "status, transaction_date, supplier, grand_total", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "timeline_field": "supplier", "title_field": "supplier_name", "track_changes": 1}