{"actions": [], "autoname": "hash", "creation": "2016-02-25 08:04:02.452958", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "supplier_part_no", "column_break_3", "item_name", "schedule_date", "section_break_5", "description", "item_group", "brand", "image_section", "image", "image_view", "quantity", "qty", "stock_uom", "col_break2", "uom", "conversion_factor", "stock_qty", "warehouse_and_reference", "warehouse", "col_break4", "material_request", "material_request_item", "section_break_24", "project_name", "section_break_23", "page_break"], "fields": [{"bold": 1, "columns": 2, "fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "print_hide": 1, "reqd": 1, "search_index": 1}, {"fieldname": "supplier_part_no", "fieldtype": "Data", "hidden": 1, "label": "Supplier Part No", "no_copy": 1, "read_only": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data", "search_index": 1}, {"collapsible": 1, "fieldname": "section_break_5", "fieldtype": "Section Break", "label": "Description"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Small Text", "print_width": "300px", "width": "300px"}, {"fetch_from": "item_code.image", "fieldname": "image", "fieldtype": "Attach", "hidden": 1, "label": "Image"}, {"fieldname": "image_view", "fieldtype": "Image", "label": "Image View", "options": "image", "print_hide": 1}, {"fieldname": "quantity", "fieldtype": "Section Break", "label": "Quantity & Stock"}, {"bold": 1, "columns": 2, "fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity", "oldfieldname": "qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_width": "60px", "reqd": 1, "width": "60px"}, {"fieldname": "col_break2", "fieldtype": "Column Break"}, {"columns": 2, "default": "Today", "fieldname": "schedule_date", "fieldtype": "Date", "in_list_view": 1, "label": "Required Date", "reqd": 1}, {"fieldname": "uom", "fieldtype": "Link", "in_list_view": 1, "label": "UOM", "oldfieldname": "uom", "oldfieldtype": "Link", "options": "UOM", "print_width": "100px", "reqd": 1, "width": "100px"}, {"fieldname": "warehouse_and_reference", "fieldtype": "Section Break", "label": "Warehouse and Reference"}, {"columns": 2, "fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Warehouse", "oldfieldname": "warehouse", "oldfieldtype": "Link", "options": "Warehouse", "print_hide": 1}, {"fieldname": "project_name", "fieldtype": "Link", "label": "Project", "options": "Project", "print_hide": 1}, {"fieldname": "material_request", "fieldtype": "Link", "label": "Material Request", "options": "Material Request", "print_hide": 1, "read_only": 1}, {"fieldname": "col_break4", "fieldtype": "Column Break"}, {"fieldname": "material_request_item", "fieldtype": "Data", "hidden": 1, "label": "Material Request Item", "print_hide": 1}, {"fieldname": "brand", "fieldtype": "Link", "label": "Brand", "oldfieldname": "brand", "oldfieldtype": "Link", "options": "Brand", "print_hide": 1, "read_only": 1}, {"fieldname": "item_group", "fieldtype": "Link", "label": "Item Group", "oldfieldname": "item_group", "oldfieldtype": "Link", "options": "Item Group", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "page_break", "fieldtype": "Check", "label": "Page Break", "no_copy": 1, "oldfieldname": "page_break", "oldfieldtype": "Check", "print_hide": 1}, {"collapsible": 1, "fieldname": "image_section", "fieldtype": "Section Break", "label": "Image"}, {"fieldname": "section_break_23", "fieldtype": "Section Break"}, {"fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "options": "UOM", "print_hide": 1, "read_only": 1, "reqd": 1}, {"fieldname": "conversion_factor", "fieldtype": "Float", "label": "UOM Conversion Factor", "print_hide": 1, "read_only": 1, "reqd": 1}, {"fieldname": "stock_qty", "fieldtype": "Float", "label": "Qty as per Stock UOM", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "section_break_24", "fieldtype": "Section Break", "label": "Accounting Dimensions"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2023-11-14 18:34:48.327224", "modified_by": "Administrator", "module": "Buying", "name": "Request for Quotation Item", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}