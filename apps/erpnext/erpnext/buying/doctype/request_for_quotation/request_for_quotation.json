{"actions": [], "allow_import": 1, "autoname": "naming_series:", "creation": "2016-02-25 01:24:07.224790", "doctype": "DocType", "document_type": "Document", "engine": "InnoDB", "field_order": ["naming_series", "company", "billing_address", "billing_address_display", "vendor", "column_break1", "transaction_date", "schedule_date", "status", "amended_from", "suppliers_section", "suppliers", "items_section", "items", "supplier_response_section", "email_template", "preview", "col_break_email_1", "html_llwp", "send_attached_files", "send_document_print", "sec_break_email_2", "message_for_supplier", "terms_section_break", "incoterm", "named_place", "tc_name", "terms", "printing_settings", "select_print_heading", "letter_head", "more_info", "opportunity"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "in_list_view": 1, "label": "Series", "no_copy": 1, "oldfieldname": "naming_series", "oldfieldtype": "Select", "options": "PUR-RFQ-.YYYY.-", "print_hide": 1, "reqd": 1, "set_only_once": 1}, {"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "print_hide": 1, "remember_last_selected_value": 1, "reqd": 1, "search_index": 1}, {"description": "For individual supplier", "fieldname": "vendor", "fieldtype": "Link", "hidden": 1, "in_standard_filter": 1, "label": "Supplier", "no_copy": 1, "options": "Supplier", "read_only": 1}, {"fieldname": "column_break1", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "print_width": "50%", "width": "50%"}, {"default": "Today", "fieldname": "transaction_date", "fieldtype": "Date", "in_list_view": 1, "label": "Date", "oldfieldname": "transaction_date", "oldfieldtype": "Date", "reqd": 1, "search_index": 1}, {"fieldname": "suppliers_section", "fieldtype": "Section Break"}, {"fieldname": "suppliers", "fieldtype": "Table", "label": "Suppliers", "options": "Request for Quotation Supplier", "print_hide": 1, "reqd": 1}, {"fieldname": "items_section", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "fa fa-shopping-cart"}, {"fieldname": "items", "fieldtype": "Table", "label": "Items", "oldfieldname": "po_details", "oldfieldtype": "Table", "options": "Request for Quotation Item", "reqd": 1}, {"fieldname": "supplier_response_section", "fieldtype": "Section Break", "label": "Email Details"}, {"fieldname": "email_template", "fieldtype": "Link", "label": "<PERSON>ail Te<PERSON>late", "options": "<PERSON>ail Te<PERSON>late", "print_hide": 1}, {"allow_on_submit": 1, "fetch_from": "email_template.response", "fetch_if_empty": 1, "fieldname": "message_for_supplier", "fieldtype": "Text Editor", "in_list_view": 1, "label": "Message for Supplier", "print_hide": 1, "reqd": 1}, {"collapsible": 1, "collapsible_depends_on": "terms", "fieldname": "terms_section_break", "fieldtype": "Section Break", "label": "Terms and Conditions", "oldfieldtype": "Section Break", "options": "fa fa-legal"}, {"fieldname": "tc_name", "fieldtype": "Link", "label": "Terms", "oldfieldname": "tc_name", "oldfieldtype": "Link", "options": "Terms and Conditions", "print_hide": 1}, {"fieldname": "terms", "fieldtype": "Text Editor", "label": "Terms and Conditions", "oldfieldname": "terms", "oldfieldtype": "Text Editor"}, {"collapsible": 1, "fieldname": "printing_settings", "fieldtype": "Section Break", "label": "Printing Settings"}, {"allow_on_submit": 1, "fieldname": "select_print_heading", "fieldtype": "Link", "label": "Print Heading", "no_copy": 1, "oldfieldname": "select_print_heading", "oldfieldtype": "Link", "options": "Print Heading", "print_hide": 1, "report_hide": 1}, {"allow_on_submit": 1, "fieldname": "letter_head", "fieldtype": "Link", "label": "Letter Head", "oldfieldname": "letter_head", "oldfieldtype": "Select", "options": "Letter Head", "print_hide": 1}, {"collapsible": 1, "fieldname": "more_info", "fieldtype": "Section Break", "label": "More Information", "oldfieldtype": "Section Break", "options": "fa fa-file-text", "print_hide": 1}, {"fieldname": "opportunity", "fieldtype": "Link", "label": "Opportunity", "options": "Opportunity", "print_hide": 1, "read_only": 1}, {"fieldname": "status", "fieldtype": "Select", "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Select", "options": "\nDraft\nSubmitted\nCancelled", "print_hide": 1, "read_only": 1, "reqd": 1, "search_index": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Request for Quotation", "print_hide": 1, "read_only": 1}, {"fieldname": "col_break_email_1", "fieldtype": "Column Break"}, {"depends_on": "eval:!doc.docstatus==1", "fieldname": "preview", "fieldtype": "<PERSON><PERSON>", "label": "Preview Email"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "sec_break_email_2", "fieldtype": "Section Break", "hide_border": 1}, {"fieldname": "schedule_date", "fieldtype": "Date", "label": "Required Date"}, {"fieldname": "incoterm", "fieldtype": "Link", "label": "Incoterm", "options": "Incoterm"}, {"depends_on": "incoterm", "fieldname": "named_place", "fieldtype": "Data", "label": "Named Place"}, {"fieldname": "html_llwp", "fieldtype": "HTML", "options": "<p>In your <b>Email Template</b>, you can use the following special variables:\n</p>\n<ul>\n        <li>\n            <code>{{ update_password_link }}</code>: A link where your supplier can set a new password to log into your portal.\n        </li>\n        <li>\n            <code>{{ portal_link }}</code>: A link to this RFQ in your supplier portal.\n        </li>\n        <li>\n            <code>{{ supplier_name }}</code>: The company name of your supplier.\n        </li>\n        <li>\n            <code>{{ contact.salutation }} {{ contact.last_name }}</code>: The contact person of your supplier.\n        </li><li>\n            <code>{{ user_fullname }}</code>: Your full name.\n        </li>\n    </ul>\n<p></p>\n<p>Apart from these, you can access all values in this RFQ, like <code>{{ message_for_supplier }}</code> or <code>{{ terms }}</code>.</p>", "print_hide": 1, "read_only": 1, "report_hide": 1}, {"default": "1", "description": "If enabled, all files attached to this document will be attached to each email", "fieldname": "send_attached_files", "fieldtype": "Check", "label": "Send Attached Files"}, {"default": "0", "description": "If enabled, a print of this document will be attached to each email", "fieldname": "send_document_print", "fieldtype": "Check", "label": "Send Document Print", "print_hide": 1}, {"fieldname": "billing_address", "fieldtype": "Link", "label": "Company Billing Address", "options": "Address"}, {"fieldname": "billing_address_display", "fieldtype": "Small Text", "label": "Billing Address Details", "read_only": 1}], "icon": "fa fa-shopping-cart", "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2023-11-06 12:45:28.898706", "modified_by": "Administrator", "module": "Buying", "name": "Request for Quotation", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Manufacturing Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase User", "share": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User"}, {"permlevel": 1, "read": 1, "role": "Purchase Manager", "write": 1}, {"permlevel": 1, "read": 1, "role": "All"}], "search_fields": "status, transaction_date", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}