{"actions": [], "creation": "2013-02-22 01:27:42", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["main_item_code", "rm_item_code", "item_name", "bom_detail_no", "col_break1", "description", "stock_uom", "conversion_factor", "reference_name", "secbreak_1", "rate", "col_break2", "amount", "secbreak_2", "required_qty", "col_break3", "consumed_qty", "current_stock", "secbreak_3", "batch_no", "col_break4", "serial_no", "purchase_order"], "fields": [{"fieldname": "main_item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "oldfieldname": "main_item_code", "oldfieldtype": "Data", "options": "<PERSON><PERSON>", "read_only": 1}, {"fieldname": "rm_item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Raw Material Item Code", "oldfieldname": "rm_item_code", "oldfieldtype": "Data", "options": "<PERSON><PERSON>", "read_only": 1}, {"fieldname": "description", "fieldtype": "Text Editor", "in_global_search": 1, "label": "Description", "oldfieldname": "description", "oldfieldtype": "Data", "print_width": "300px", "read_only": 1, "width": "300px"}, {"fieldname": "batch_no", "fieldtype": "Link", "label": "Batch No", "no_copy": 1, "options": "<PERSON><PERSON>"}, {"fieldname": "serial_no", "fieldtype": "Text", "label": "Serial No", "no_copy": 1}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"fieldname": "required_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Available Qty For Consumption", "oldfieldname": "required_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"columns": 2, "fieldname": "consumed_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Qty to Be Consumed", "oldfieldname": "consumed_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "reqd": 1}, {"fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock <PERSON>", "oldfieldname": "stock_uom", "oldfieldtype": "Data", "options": "UOM", "read_only": 1}, {"fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate", "oldfieldname": "rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount", "oldfieldname": "amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "conversion_factor", "fieldtype": "Float", "hidden": 1, "label": "Conversion Factor", "oldfieldname": "conversion_factor", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "current_stock", "fieldtype": "Float", "in_list_view": 1, "label": "Current Stock", "oldfieldname": "current_stock", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "reference_name", "fieldtype": "Data", "hidden": 1, "in_list_view": 1, "label": "Reference Name", "oldfieldname": "reference_name", "oldfieldtype": "Data", "read_only": 1}, {"fieldname": "bom_detail_no", "fieldtype": "Data", "hidden": 1, "in_list_view": 1, "label": "BOM Detail No", "oldfieldname": "bom_detail_no", "oldfieldtype": "Data", "read_only": 1}, {"fieldname": "secbreak_1", "fieldtype": "Section Break"}, {"fieldname": "col_break2", "fieldtype": "Column Break"}, {"fieldname": "secbreak_2", "fieldtype": "Section Break"}, {"fieldname": "col_break3", "fieldtype": "Column Break"}, {"fieldname": "secbreak_3", "fieldtype": "Section Break"}, {"fieldname": "col_break4", "fieldtype": "Column Break"}, {"fieldname": "item_name", "fieldtype": "Data", "label": "Item Name", "read_only": 1}, {"fieldname": "purchase_order", "fieldtype": "Link", "hidden": 1, "label": "Purchase Order", "no_copy": 1, "options": "Purchase Order", "print_hide": 1, "read_only": 1}], "idx": 1, "istable": 1, "links": [], "modified": "2021-06-19 19:33:04.431213", "modified_by": "Administrator", "module": "Buying", "name": "Purchase Receipt Item Supplied", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}