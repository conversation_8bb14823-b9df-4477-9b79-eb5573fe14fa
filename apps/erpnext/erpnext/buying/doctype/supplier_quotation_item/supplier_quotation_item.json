{"actions": [], "autoname": "hash", "creation": "2013-05-22 12:43:10", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "supplier_part_no", "item_name", "column_break_3", "lead_time_days", "expected_delivery_date", "is_free_item", "section_break_5", "description", "item_group", "brand", "col_break1", "image", "image_view", "quantity_and_rate", "qty", "stock_uom", "col_break2", "uom", "conversion_factor", "stock_qty", "sec_break_price_list", "price_list_rate", "discount_percentage", "discount_amount", "col_break_price_list", "base_price_list_rate", "sec_break1", "rate", "amount", "item_tax_template", "col_break3", "base_rate", "base_amount", "pricing_rules", "section_break_24", "net_rate", "net_amount", "column_break_27", "base_net_rate", "base_net_amount", "item_weight_details", "weight_per_unit", "total_weight", "column_break_23", "weight_uom", "warehouse_and_reference", "warehouse", "prevdoc_doctype", "material_request", "sales_order", "request_for_quotation", "col_break4", "material_request_item", "request_for_quotation_item", "item_tax_rate", "manufacture_details", "manufacturer", "column_break_15", "manufacturer_part_no", "ad_sec_break", "cost_center", "dimension_col_break", "project", "section_break_44", "page_break"], "fields": [{"bold": 1, "columns": 2, "fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "reqd": 1, "search_index": 1}, {"fieldname": "supplier_part_no", "fieldtype": "Data", "hidden": 1, "label": "Supplier Part Number", "print_hide": 1, "read_only": 1}, {"fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data", "print_hide": 1, "search_index": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "lead_time_days", "fieldtype": "Int", "label": "Supplier Lead Time (days)"}, {"collapsible": 1, "fieldname": "section_break_5", "fieldtype": "Section Break", "label": "Description"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Small Text", "print_width": "300px", "width": "300px"}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"fieldname": "image", "fieldtype": "Attach", "hidden": 1, "label": "Image"}, {"fieldname": "image_view", "fieldtype": "Image", "label": "Image View", "options": "image", "print_hide": 1}, {"fieldname": "quantity_and_rate", "fieldtype": "Section Break", "label": "Quantity and Rate"}, {"bold": 1, "fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity", "oldfieldname": "qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_width": "60px", "reqd": 1, "width": "60px"}, {"fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "options": "UOM", "print_hide": 1, "read_only": 1, "reqd": 1}, {"fieldname": "price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price List Rate", "options": "currency", "print_hide": 1}, {"depends_on": "price_list_rate", "fieldname": "discount_percentage", "fieldtype": "Percent", "label": "Discount on Price List Rate (%)"}, {"depends_on": "price_list_rate", "fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Discount Amount", "options": "currency"}, {"fieldname": "col_break2", "fieldtype": "Column Break"}, {"fieldname": "uom", "fieldtype": "Link", "in_list_view": 1, "label": "UOM", "options": "UOM", "print_hide": 1, "print_width": "100px", "reqd": 1, "width": "100px"}, {"fieldname": "conversion_factor", "fieldtype": "Float", "label": "UOM Conversion Factor", "print_hide": 1, "read_only": 1, "reqd": 1}, {"fieldname": "stock_qty", "fieldtype": "Float", "label": "Qty as per Stock UOM", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "base_price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price List Rate (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "sec_break1", "fieldtype": "Section Break"}, {"bold": 1, "columns": 2, "fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "oldfieldname": "import_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency"}, {"columns": 2, "fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "oldfieldname": "import_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1}, {"fieldname": "col_break3", "fieldtype": "Column Break"}, {"fieldname": "base_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate (Company Currency)", "oldfieldname": "purchase_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "100px", "read_only": 1, "reqd": 1, "width": "100px"}, {"fieldname": "base_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount (Company Currency)", "oldfieldname": "amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "reqd": 1}, {"fieldname": "pricing_rules", "fieldtype": "Small Text", "hidden": 1, "label": "Pricing Rules", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_24", "fieldtype": "Section Break"}, {"fieldname": "net_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Rate", "print_hide": 1, "read_only": 1}, {"fieldname": "net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Amount", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_27", "fieldtype": "Column Break"}, {"fieldname": "base_net_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Rate (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "item_weight_details", "fieldtype": "Section Break", "label": "Item Weight Details"}, {"fieldname": "weight_per_unit", "fieldtype": "Float", "label": "Weight Per Unit", "read_only": 1}, {"fieldname": "total_weight", "fieldtype": "Float", "label": "Total Weight", "read_only": 1}, {"fieldname": "column_break_23", "fieldtype": "Column Break", "print_hide": 1}, {"fieldname": "weight_uom", "fieldtype": "Link", "label": "Weight UOM", "options": "UOM", "read_only": 1}, {"fieldname": "warehouse_and_reference", "fieldtype": "Section Break", "label": "Warehouse and Reference"}, {"fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Warehouse", "oldfieldname": "warehouse", "oldfieldtype": "Link", "options": "Warehouse", "print_hide": 1}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project", "print_hide": 1}, {"fieldname": "prevdoc_doctype", "fieldtype": "Data", "hidden": 1, "label": "Reference Document Type", "no_copy": 1, "oldfieldname": "prevdoc_doctype", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1}, {"fieldname": "material_request", "fieldtype": "Link", "label": "Material Request", "oldfieldname": "prevdoc_docname", "oldfieldtype": "Link", "options": "Material Request", "print_hide": 1, "print_width": "120px", "read_only": 1, "search_index": 1, "width": "120px"}, {"fieldname": "sales_order", "fieldtype": "Link", "label": "Sales Order", "options": "Sales Order", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "request_for_quotation", "fieldtype": "Link", "label": "Request for Quotation", "no_copy": 1, "options": "Request for Quotation", "print_hide": 1, "read_only": 1}, {"fieldname": "col_break4", "fieldtype": "Column Break"}, {"fieldname": "item_tax_template", "fieldtype": "Link", "label": "Item Tax Template", "options": "Item Tax Template", "print_hide": 1}, {"fieldname": "material_request_item", "fieldtype": "Data", "hidden": 1, "label": "Material Request Item", "oldfieldname": "prevdoc_detail_docname", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "request_for_quotation_item", "fieldtype": "Data", "hidden": 1, "label": "Request for Quotation Item", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "brand", "fieldtype": "Link", "label": "Brand", "oldfieldname": "brand", "oldfieldtype": "Link", "options": "Brand", "print_hide": 1, "read_only": 1}, {"fieldname": "item_group", "fieldtype": "Link", "label": "Item Group", "oldfieldname": "item_group", "oldfieldtype": "Link", "options": "Item Group", "print_hide": 1, "read_only": 1}, {"description": "Tax detail table fetched from item master as a string and stored in this field.\nUsed for Taxes and Charges", "fieldname": "item_tax_rate", "fieldtype": "Code", "hidden": 1, "label": "Item Tax Rate", "oldfieldname": "item_tax_rate", "oldfieldtype": "Small Text", "print_hide": 1, "read_only": 1, "report_hide": 1}, {"fieldname": "section_break_44", "fieldtype": "Section Break"}, {"allow_on_submit": 1, "default": "0", "fieldname": "page_break", "fieldtype": "Check", "label": "Page Break", "no_copy": 1, "oldfieldname": "page_break", "oldfieldtype": "Check", "print_hide": 1}, {"collapsible": 1, "fieldname": "manufacture_details", "fieldtype": "Section Break", "label": "Manufacture"}, {"fieldname": "manufacturer", "fieldtype": "Link", "label": "Manufacturer", "options": "Manufacturer"}, {"fieldname": "manufacturer_part_no", "fieldtype": "Data", "label": "Manufacturer Part Number"}, {"fieldname": "column_break_15", "fieldtype": "Column Break"}, {"fieldname": "sec_break_price_list", "fieldtype": "Section Break"}, {"fieldname": "col_break_price_list", "fieldtype": "Column Break"}, {"collapsible": 1, "fieldname": "ad_sec_break", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"default": "0", "depends_on": "is_free_item", "fieldname": "is_free_item", "fieldtype": "Check", "label": "Is Free Item", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "bold": 1, "fieldname": "expected_delivery_date", "fieldtype": "Date", "label": "Expected Delivery Date"}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}], "idx": 1, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2023-11-17 12:25:26.235367", "modified_by": "Administrator", "module": "Buying", "name": "Supplier Quotation Item", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}