{"actions": [], "creation": "2013-02-22 01:27:42", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["main_item_code", "rm_item_code", "column_break_3", "stock_uom", "reserve_warehouse", "conversion_factor", "column_break_6", "bom_detail_no", "reference_name", "section_break2", "rate", "col_break2", "amount", "section_break1", "required_qty", "supplied_qty", "col_break1", "consumed_qty", "returned_qty", "total_supplied_qty"], "fields": [{"columns": 2, "fieldname": "main_item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "oldfieldname": "main_item_code", "oldfieldtype": "Data", "options": "<PERSON><PERSON>", "read_only": 1}, {"columns": 2, "fieldname": "rm_item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Raw Material Item Code", "oldfieldname": "rm_item_code", "oldfieldtype": "Data", "options": "<PERSON><PERSON>", "read_only": 1}, {"columns": 2, "fieldname": "required_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Required <PERSON><PERSON>", "oldfieldname": "required_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"columns": 2, "fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "oldfieldname": "rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency"}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount", "oldfieldname": "amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"fieldname": "bom_detail_no", "fieldtype": "Data", "label": "BOM Detail No", "oldfieldname": "bom_detail_no", "oldfieldtype": "Data", "read_only": 1}, {"fieldname": "reference_name", "fieldtype": "Data", "label": "Reference Name", "oldfieldname": "reference_name", "oldfieldtype": "Data", "read_only": 1}, {"fieldname": "conversion_factor", "fieldtype": "Float", "hidden": 1, "label": "Conversion Factor", "oldfieldname": "conversion_factor", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock <PERSON>", "oldfieldname": "stock_uom", "oldfieldtype": "Data", "options": "UOM", "read_only": 1}, {"columns": 2, "fieldname": "reserve_warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Reserve Warehouse", "options": "Warehouse"}, {"fieldname": "supplied_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Supplied <PERSON>ty", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "section_break1", "fieldtype": "Section Break"}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"fieldname": "section_break2", "fieldtype": "Section Break"}, {"fieldname": "col_break2", "fieldtype": "Column Break"}, {"fieldname": "consumed_qty", "fieldtype": "Float", "label": "Consumed Qty", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "returned_qty", "fieldtype": "Float", "label": "Returned <PERSON><PERSON>", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "total_supplied_qty", "fieldtype": "Float", "hidden": 1, "label": "Total Supplied Qty", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}], "hide_toolbar": 1, "idx": 1, "istable": 1, "links": [], "modified": "2021-06-09 15:17:58.128242", "modified_by": "Administrator", "module": "Buying", "name": "Purchase Order Item Supplied", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC"}