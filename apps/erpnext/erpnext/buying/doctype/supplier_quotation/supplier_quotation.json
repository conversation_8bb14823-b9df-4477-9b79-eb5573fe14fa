{"actions": [], "allow_auto_repeat": 1, "allow_import": 1, "autoname": "naming_series:", "creation": "2013-05-21 16:16:45", "doctype": "DocType", "document_type": "Document", "engine": "InnoDB", "field_order": ["supplier_section", "title", "naming_series", "supplier", "supplier_name", "company", "column_break1", "status", "transaction_date", "valid_till", "quotation_number", "amended_from", "accounting_dimensions_section", "cost_center", "dimension_col_break", "project", "currency_and_price_list", "currency", "conversion_rate", "cb_price_list", "buying_price_list", "price_list_currency", "plc_conversion_rate", "ignore_pricing_rule", "items_section", "items", "section_break_22", "total_qty", "total_net_weight", "column_break_26", "base_total", "base_net_total", "column_break_24", "total", "net_total", "taxes_section", "tax_category", "taxes_and_charges", "column_break_34", "shipping_rule", "column_break_36", "incoterm", "named_place", "section_break_38", "taxes", "totals", "base_taxes_and_charges_added", "base_taxes_and_charges_deducted", "base_total_taxes_and_charges", "column_break_37", "taxes_and_charges_added", "taxes_and_charges_deducted", "total_taxes_and_charges", "section_break_41", "apply_discount_on", "base_discount_amount", "column_break_43", "additional_discount_percentage", "discount_amount", "section_break_46", "base_grand_total", "base_rounding_adjustment", "base_rounded_total", "base_in_words", "column_break4", "grand_total", "rounding_adjustment", "rounded_total", "in_words", "disable_rounded_total", "tax_breakup", "other_charges_calculation", "pricing_rule_details", "pricing_rules", "address_and_contact_tab", "supplier_address_section", "supplier_address", "address_display", "column_break_72", "contact_person", "contact_display", "contact_mobile", "contact_email", "shipping_address_section", "shipping_address", "column_break_zjaq", "shipping_address_display", "company_billing_address_section", "billing_address", "column_break_gcth", "billing_address_display", "terms_tab", "tc_name", "terms", "more_info_tab", "printing_settings", "letter_head", "group_same_items", "column_break_85", "select_print_heading", "language", "subscription_section", "auto_repeat", "update_auto_repeat_reference", "more_info", "is_subcontracted", "column_break_57", "opportunity", "connections_tab"], "fields": [{"fieldname": "supplier_section", "fieldtype": "Section Break", "options": "fa fa-user"}, {"default": "{supplier_name}", "fieldname": "title", "fieldtype": "Data", "hidden": 1, "label": "Title", "no_copy": 1, "print_hide": 1, "report_hide": 1}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "oldfieldname": "naming_series", "oldfieldtype": "Select", "options": "PUR-SQTN-.YYYY.-", "print_hide": 1, "reqd": 1, "set_only_once": 1}, {"bold": 1, "fieldname": "supplier", "fieldtype": "Link", "in_standard_filter": 1, "label": "Supplier", "oldfieldname": "supplier", "oldfieldtype": "Link", "options": "Supplier", "print_hide": 1, "reqd": 1, "search_index": 1}, {"bold": 1, "fetch_from": "supplier.supplier_name", "fieldname": "supplier_name", "fieldtype": "Data", "in_global_search": 1, "label": "Supplier Name", "read_only": 1}, {"fieldname": "column_break1", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "print_width": "50%", "width": "50%"}, {"default": "Today", "fieldname": "transaction_date", "fieldtype": "Date", "in_list_view": 1, "label": "Date", "oldfieldname": "transaction_date", "oldfieldtype": "Date", "reqd": 1, "search_index": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "hidden": 1, "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "oldfieldname": "amended_from", "oldfieldtype": "Data", "options": "Supplier Quotation", "print_hide": 1, "read_only": 1}, {"fieldname": "company", "fieldtype": "Link", "in_standard_filter": 1, "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "print_hide": 1, "remember_last_selected_value": 1, "reqd": 1, "search_index": 1}, {"fieldname": "supplier_address", "fieldtype": "Link", "label": "Supplier Address", "options": "Address", "print_hide": 1}, {"fieldname": "contact_person", "fieldtype": "Link", "label": "Contact Person", "options": "Contact", "print_hide": 1}, {"fieldname": "address_display", "fieldtype": "Small Text", "label": "Address", "read_only": 1}, {"fieldname": "contact_display", "fieldtype": "Small Text", "in_global_search": 1, "label": "Contact", "read_only": 1}, {"fieldname": "contact_mobile", "fieldtype": "Small Text", "label": "Mobile No", "options": "Phone", "read_only": 1}, {"fieldname": "contact_email", "fieldtype": "Data", "label": "Contact Email", "options": "Email", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "currency_and_price_list", "fieldtype": "Section Break", "label": "Currency and Price List", "options": "fa fa-tag"}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "oldfieldname": "currency", "oldfieldtype": "Select", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "reqd": 1}, {"fieldname": "conversion_rate", "fieldtype": "Float", "label": "Exchange Rate", "oldfieldname": "conversion_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "precision": "9", "print_hide": 1, "reqd": 1}, {"fieldname": "cb_price_list", "fieldtype": "Column Break", "print_width": "50%", "width": "50%"}, {"fieldname": "buying_price_list", "fieldtype": "Link", "label": "Price List", "options": "Price List", "print_hide": 1}, {"depends_on": "buying_price_list", "fieldname": "price_list_currency", "fieldtype": "Link", "label": "Price List Currency", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"depends_on": "buying_price_list", "fieldname": "plc_conversion_rate", "fieldtype": "Float", "label": "Price List Exchange Rate", "precision": "9", "print_hide": 1}, {"default": "0", "fieldname": "ignore_pricing_rule", "fieldtype": "Check", "label": "Ignore Pricing Rule", "no_copy": 1, "permlevel": 1, "print_hide": 1}, {"fieldname": "items_section", "fieldtype": "Section Break", "hide_border": 1, "oldfieldtype": "Section Break", "options": "fa fa-shopping-cart"}, {"allow_bulk_edit": 1, "fieldname": "items", "fieldtype": "Table", "label": "Items", "oldfieldname": "po_details", "oldfieldtype": "Table", "options": "Supplier Quotation Item", "reqd": 1}, {"fieldname": "pricing_rule_details", "fieldtype": "Section Break", "label": "Pricing Rules"}, {"fieldname": "pricing_rules", "fieldtype": "Table", "label": "Pricing Rule Detail", "options": "Pricing Rule Detail", "read_only": 1}, {"fieldname": "section_break_22", "fieldtype": "Section Break"}, {"fieldname": "total_qty", "fieldtype": "Float", "label": "Total Quantity", "read_only": 1}, {"fieldname": "base_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total (Company Currency)", "no_copy": 1, "oldfieldname": "net_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_24", "fieldtype": "Column Break"}, {"fieldname": "total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total", "options": "currency", "read_only": 1}, {"fieldname": "net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total", "oldfieldname": "net_total_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "total_net_weight", "fieldtype": "Float", "label": "Total Net Weight", "print_hide": 1, "read_only": 1}, {"fieldname": "taxes_section", "fieldtype": "Section Break", "hide_border": 1, "label": "Taxes and Charges", "oldfieldtype": "Section Break", "options": "fa fa-money"}, {"fieldname": "tax_category", "fieldtype": "Link", "label": "Tax Category", "options": "Tax Category", "print_hide": 1}, {"fieldname": "column_break_36", "fieldtype": "Column Break"}, {"fieldname": "shipping_rule", "fieldtype": "Link", "label": "Shipping Rule", "options": "Shipping Rule"}, {"fieldname": "section_break_38", "fieldtype": "Section Break", "hide_border": 1}, {"fieldname": "taxes_and_charges", "fieldtype": "Link", "label": "Purchase Taxes and Charges Template", "no_copy": 1, "oldfieldname": "purchase_other_charges", "oldfieldtype": "Link", "options": "Purchase Taxes and Charges Template", "print_hide": 1}, {"fieldname": "taxes", "fieldtype": "Table", "label": "Purchase Taxes and Charges", "oldfieldname": "purchase_tax_details", "oldfieldtype": "Table", "options": "Purchase Taxes and Charges"}, {"collapsible": 1, "fieldname": "tax_breakup", "fieldtype": "Section Break", "label": "Tax Breakup"}, {"fieldname": "other_charges_calculation", "fieldtype": "Long Text", "label": "Taxes and Charges Calculation", "no_copy": 1, "oldfieldtype": "HTML", "print_hide": 1, "read_only": 1}, {"fieldname": "totals", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "fa fa-money"}, {"fieldname": "base_taxes_and_charges_added", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Added (Company Currency)", "oldfieldname": "other_charges_added", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_taxes_and_charges_deducted", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Deducted (Company Currency)", "oldfieldname": "other_charges_deducted", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Taxes and Charges (Company Currency)", "no_copy": 1, "oldfieldname": "total_tax", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_37", "fieldtype": "Column Break"}, {"fieldname": "taxes_and_charges_added", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Added", "oldfieldname": "other_charges_added_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "taxes_and_charges_deducted", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Taxes and Charges Deducted", "oldfieldname": "other_charges_deducted_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Taxes and Charges", "options": "currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "section_break_41", "fieldtype": "Section Break", "label": "Additional Discount"}, {"default": "Grand Total", "fieldname": "apply_discount_on", "fieldtype": "Select", "label": "Apply Additional Discount On", "options": "\nGrand Total\nNet Total", "print_hide": 1}, {"fieldname": "base_discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Discount Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_43", "fieldtype": "Column Break"}, {"fieldname": "additional_discount_percentage", "fieldtype": "Float", "label": "Additional Discount Percentage", "print_hide": 1}, {"fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Discount Amount", "options": "currency", "print_hide": 1}, {"fieldname": "section_break_46", "fieldtype": "Section Break", "label": "Totals"}, {"fieldname": "base_grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Grand Total (Company Currency)", "no_copy": 1, "oldfieldname": "grand_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "base_rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounding Adjustment (Company Currency", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_in_words", "fieldtype": "Data", "label": "In Words (Company Currency)", "length": 240, "oldfieldname": "in_words", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1}, {"fieldname": "base_rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounded Total (Company Currency)", "oldfieldname": "rounded_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break4", "fieldtype": "Column Break", "oldfieldtype": "Column Break"}, {"fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Grand Total", "oldfieldname": "grand_total_import", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounding Adjustment", "no_copy": 1, "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounded Total", "options": "currency", "read_only": 1}, {"fieldname": "in_words", "fieldtype": "Data", "label": "In Words", "length": 240, "oldfieldname": "in_words_import", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1}, {"default": "0", "fieldname": "disable_rounded_total", "fieldtype": "Check", "label": "Disable Rounded Total"}, {"fieldname": "tc_name", "fieldtype": "Link", "label": "Terms Template", "oldfieldname": "tc_name", "oldfieldtype": "Link", "options": "Terms and Conditions", "print_hide": 1}, {"fieldname": "terms", "fieldtype": "Text Editor", "label": "Terms and Conditions", "oldfieldname": "terms", "oldfieldtype": "Text Editor"}, {"collapsible": 1, "fieldname": "printing_settings", "fieldtype": "Section Break", "label": "Printing Settings"}, {"allow_on_submit": 1, "fieldname": "select_print_heading", "fieldtype": "Link", "label": "Print Heading", "no_copy": 1, "oldfieldname": "select_print_heading", "oldfieldtype": "Link", "options": "Print Heading", "print_hide": 1, "report_hide": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "group_same_items", "fieldtype": "Check", "label": "Group same items", "print_hide": 1}, {"fieldname": "column_break_72", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "fieldname": "letter_head", "fieldtype": "Link", "label": "Letter Head", "oldfieldname": "letter_head", "oldfieldtype": "Select", "options": "Letter Head", "print_hide": 1}, {"fieldname": "language", "fieldtype": "Data", "label": "Print Language", "print_hide": 1, "read_only": 1}, {"fieldname": "subscription_section", "fieldtype": "Section Break", "label": "Auto Repeat"}, {"fieldname": "auto_repeat", "fieldtype": "Link", "label": "Auto Repeat", "no_copy": 1, "options": "Auto Repeat", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "depends_on": "eval: doc.auto_repeat", "fieldname": "update_auto_repeat_reference", "fieldtype": "<PERSON><PERSON>", "label": "Update Auto Repeat Reference"}, {"collapsible": 1, "fieldname": "more_info", "fieldtype": "Section Break", "label": "Additional Info", "oldfieldtype": "Section Break", "options": "fa fa-file-text"}, {"fieldname": "status", "fieldtype": "Select", "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Select", "options": "\nDraft\nSubmitted\nStopped\nCancelled\nExpired", "print_hide": 1, "read_only": 1, "reqd": 1, "search_index": 1}, {"fieldname": "column_break_57", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "is_subcontracted", "fieldtype": "Check", "label": "Is Subcontracted", "print_hide": 1}, {"fieldname": "opportunity", "fieldtype": "Link", "label": "Opportunity", "no_copy": 1, "options": "Opportunity", "print_hide": 1, "read_only": 1}, {"fieldname": "valid_till", "fieldtype": "Date", "in_list_view": 1, "label": "<PERSON><PERSON>"}, {"fieldname": "quotation_number", "fieldtype": "Data", "label": "Quotation Number"}, {"fieldname": "address_and_contact_tab", "fieldtype": "Tab Break", "label": "Address & Contact"}, {"fieldname": "terms_tab", "fieldtype": "Tab Break", "label": "Terms"}, {"fieldname": "more_info_tab", "fieldtype": "Tab Break", "label": "More Info"}, {"fieldname": "connections_tab", "fieldtype": "Tab Break", "label": "Connections", "show_dashboard": 1}, {"fieldname": "column_break_26", "fieldtype": "Column Break"}, {"fieldname": "column_break_34", "fieldtype": "Column Break"}, {"fieldname": "column_break_85", "fieldtype": "Column Break"}, {"fieldname": "incoterm", "fieldtype": "Link", "label": "Incoterm", "options": "Incoterm"}, {"depends_on": "incoterm", "fieldname": "named_place", "fieldtype": "Data", "label": "Named Place"}, {"fieldname": "shipping_address", "fieldtype": "Link", "label": "Shipping Address", "options": "Address", "print_hide": 1}, {"fieldname": "column_break_zjaq", "fieldtype": "Column Break"}, {"fieldname": "shipping_address_display", "fieldtype": "Small Text", "label": "Shipping Address Details", "print_hide": 1, "read_only": 1}, {"fieldname": "shipping_address_section", "fieldtype": "Section Break", "label": "Shipping Address"}, {"fieldname": "supplier_address_section", "fieldtype": "Section Break", "label": "Supplier Address"}, {"fieldname": "company_billing_address_section", "fieldtype": "Section Break", "label": "Company Billing Address"}, {"fieldname": "billing_address", "fieldtype": "Link", "label": "Company Billing Address", "options": "Address"}, {"fieldname": "column_break_gcth", "fieldtype": "Column Break"}, {"fieldname": "billing_address_display", "fieldtype": "Small Text", "label": "Billing Address Details", "read_only": 1}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}], "icon": "fa fa-shopping-cart", "idx": 29, "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2023-11-20 11:15:30.083077", "modified_by": "Administrator", "module": "Buying", "name": "Supplier Quotation", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Manufacturing Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase User", "share": 1, "submit": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User"}, {"permlevel": 1, "read": 1, "role": "Purchase Manager", "write": 1}], "search_fields": "status, transaction_date, supplier,grand_total", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "timeline_field": "supplier", "title_field": "title"}