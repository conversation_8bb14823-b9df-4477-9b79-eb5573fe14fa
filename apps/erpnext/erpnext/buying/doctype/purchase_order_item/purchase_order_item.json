{"actions": [], "autoname": "hash", "creation": "2013-05-24 19:29:06", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["fg_item", "fg_item_qty", "item_code", "supplier_part_no", "item_name", "brand", "product_bundle", "column_break_4", "schedule_date", "expected_delivery_date", "item_group", "section_break_5", "description", "col_break1", "image", "image_view", "quantity_and_rate", "qty", "stock_uom", "col_break2", "uom", "conversion_factor", "stock_qty", "sec_break1", "price_list_rate", "last_purchase_rate", "col_break3", "base_price_list_rate", "discount_and_margin_section", "margin_type", "margin_rate_or_amount", "rate_with_margin", "column_break_28", "discount_percentage", "discount_amount", "base_rate_with_margin", "sec_break2", "rate", "amount", "item_tax_template", "col_break4", "base_rate", "base_amount", "pricing_rules", "stock_uom_rate", "is_free_item", "apply_tds", "section_break_29", "net_rate", "net_amount", "column_break_32", "base_net_rate", "base_net_amount", "warehouse_and_reference", "from_warehouse", "warehouse", "column_break_54", "actual_qty", "company_total_stock", "references_section", "material_request", "material_request_item", "sales_order", "sales_order_item", "sales_order_packed_item", "supplier_quotation", "supplier_quotation_item", "col_break5", "delivered_by_supplier", "against_blanket_order", "blanket_order", "blanket_order_rate", "section_break_56", "received_qty", "returned_qty", "column_break_60", "billed_amt", "accounting_details", "expense_account", "column_break_fyqr", "wip_composite_asset", "manufacture_details", "manufacturer", "manufacturer_part_no", "column_break_14", "bom", "include_exploded_items", "item_weight_details", "weight_per_unit", "total_weight", "column_break_40", "weight_uom", "accounting_dimensions_section", "project", "dimension_col_break", "cost_center", "more_info_section_break", "is_fixed_asset", "item_tax_rate", "section_break_72", "production_plan", "production_plan_item", "production_plan_sub_assembly_item", "page_break"], "fields": [{"bold": 1, "columns": 2, "fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "reqd": 1}, {"fieldname": "supplier_part_no", "fieldtype": "Data", "hidden": 1, "label": "Supplier Part Number", "print_hide": 1, "read_only": 1}, {"fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data", "print_hide": 1, "reqd": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "bold": 1, "columns": 2, "fieldname": "schedule_date", "fieldtype": "Date", "in_list_view": 1, "label": "Required By", "oldfieldname": "schedule_date", "oldfieldtype": "Date", "print_hide": 1, "reqd": 1}, {"allow_on_submit": 1, "bold": 1, "fieldname": "expected_delivery_date", "fieldtype": "Date", "label": "Expected Delivery Date", "search_index": 1}, {"collapsible": 1, "fieldname": "section_break_5", "fieldtype": "Section Break", "label": "Description"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Small Text", "print_width": "300px", "width": "300px"}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"fetch_from": "item_code.image", "fieldname": "image", "fieldtype": "Attach", "hidden": 1, "label": "Image"}, {"fieldname": "image_view", "fieldtype": "Image", "label": "Image View", "options": "image", "print_hide": 1}, {"fieldname": "quantity_and_rate", "fieldtype": "Section Break", "label": "Quantity and Rate"}, {"bold": 1, "columns": 1, "fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity", "non_negative": 1, "oldfieldname": "qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_width": "60px", "reqd": 1, "width": "60px"}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "oldfieldname": "stock_uom", "oldfieldtype": "Data", "options": "UOM", "print_width": "100px", "read_only": 1, "reqd": 1, "width": "100px"}, {"fieldname": "col_break2", "fieldtype": "Column Break", "print_hide": 1}, {"columns": 1, "fieldname": "uom", "fieldtype": "Link", "in_list_view": 1, "label": "UOM", "oldfieldname": "uom", "oldfieldtype": "Link", "options": "UOM", "print_width": "100px", "reqd": 1}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "conversion_factor", "fieldtype": "Float", "label": "UOM Conversion Factor", "oldfieldname": "conversion_factor", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "print_width": "100px", "reqd": 1, "width": "100px"}, {"fieldname": "sec_break1", "fieldtype": "Section Break"}, {"fieldname": "price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price List Rate", "options": "currency", "print_hide": 1}, {"depends_on": "price_list_rate", "fieldname": "discount_percentage", "fieldtype": "Percent", "label": "Discount on Price List Rate (%)"}, {"depends_on": "price_list_rate", "fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Discount Amount", "options": "currency"}, {"fieldname": "col_break3", "fieldtype": "Column Break"}, {"fieldname": "last_purchase_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Last Purchase Rate", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price List Rate (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "sec_break2", "fieldtype": "Section Break"}, {"bold": 1, "columns": 2, "fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "oldfieldname": "import_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency"}, {"columns": 2, "fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "oldfieldname": "import_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1}, {"fieldname": "col_break4", "fieldtype": "Column Break"}, {"fieldname": "base_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate (Company Currency)", "oldfieldname": "purchase_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "100px", "read_only": 1, "reqd": 1, "width": "100px"}, {"fieldname": "base_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount (Company Currency)", "oldfieldname": "amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "reqd": 1}, {"fieldname": "pricing_rules", "fieldtype": "Small Text", "hidden": 1, "label": "Pricing Rules", "read_only": 1}, {"default": "0", "depends_on": "is_free_item", "fieldname": "is_free_item", "fieldtype": "Check", "label": "Is Free Item", "read_only": 1}, {"fieldname": "section_break_29", "fieldtype": "Section Break"}, {"fieldname": "net_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Rate", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Amount", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_32", "fieldtype": "Column Break"}, {"fieldname": "base_net_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Rate (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "item_weight_details", "fieldtype": "Section Break", "label": "Item Weight Details"}, {"fieldname": "weight_per_unit", "fieldtype": "Float", "label": "Weight Per Unit", "read_only": 1}, {"fieldname": "total_weight", "fieldtype": "Float", "label": "Total Weight", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_40", "fieldtype": "Column Break"}, {"fieldname": "weight_uom", "fieldtype": "Link", "label": "Weight UOM", "options": "UOM", "read_only": 1}, {"fieldname": "warehouse_and_reference", "fieldtype": "Section Break", "label": "Warehouse Settings"}, {"fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Target Warehouse", "oldfieldname": "warehouse", "oldfieldtype": "Link", "options": "Warehouse", "print_hide": 1}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project", "print_hide": 1}, {"fieldname": "material_request", "fieldtype": "Link", "label": "Material Request", "mandatory_depends_on": "eval: doc.material_request_item", "no_copy": 1, "oldfieldname": "prevdoc_docname", "oldfieldtype": "Link", "options": "Material Request", "print_hide": 1, "print_width": "120px", "read_only": 1, "search_index": 1, "width": "120px"}, {"fieldname": "material_request_item", "fieldtype": "Data", "hidden": 1, "label": "Material Request Item", "mandatory_depends_on": "eval: doc.material_request", "no_copy": 1, "oldfieldname": "prevdoc_detail_docname", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "sales_order", "fieldtype": "Link", "label": "Sales Order", "no_copy": 1, "options": "Sales Order", "print_hide": 1, "search_index": 1}, {"fieldname": "sales_order_item", "fieldtype": "Data", "hidden": 1, "label": "Sales Order Item", "no_copy": 1, "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "supplier_quotation", "fieldtype": "Link", "label": "Supplier Quotation", "no_copy": 1, "options": "Supplier Quotation", "read_only": 1}, {"fieldname": "supplier_quotation_item", "fieldtype": "Link", "hidden": 1, "label": "Supplier Quotation Item", "no_copy": 1, "options": "Supplier Quotation Item", "read_only": 1}, {"default": "0", "depends_on": "delivered_by_supplier", "fieldname": "delivered_by_supplier", "fieldtype": "Check", "label": "To be Delivered to Customer", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:doc.against_blanket_order", "fieldname": "blanket_order", "fieldtype": "Link", "label": "Blanket Order", "options": "Blanket Order"}, {"depends_on": "eval:doc.against_blanket_order", "fieldname": "blanket_order_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Blanket Order Rate", "print_hide": 1, "read_only": 1}, {"fieldname": "col_break5", "fieldtype": "Column Break"}, {"fieldname": "item_group", "fieldtype": "Link", "hidden": 1, "label": "Item Group", "oldfieldname": "item_group", "oldfieldtype": "Link", "options": "Item Group", "print_hide": 1, "read_only": 1}, {"fieldname": "brand", "fieldtype": "Link", "hidden": 1, "label": "Brand", "oldfieldname": "brand", "oldfieldtype": "Link", "options": "Brand", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:parent.is_old_subcontracting_flow", "fieldname": "bom", "fieldtype": "Link", "label": "BOM", "options": "BOM", "print_hide": 1, "read_only": 1, "read_only_depends_on": "eval:!parent.is_old_subcontracting_flow"}, {"default": "0", "depends_on": "eval:parent.is_old_subcontracting_flow", "fieldname": "include_exploded_items", "fieldtype": "Check", "label": "Include Exploded Items", "print_hide": 1}, {"fieldname": "section_break_56", "fieldtype": "Section Break", "label": "Billed, Received & Returned"}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "stock_qty", "fieldtype": "Float", "label": "Qty in Stock UOM", "no_copy": 1, "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"depends_on": "received_qty", "fieldname": "received_qty", "fieldtype": "Float", "label": "Received Qty", "no_copy": 1, "oldfieldname": "received_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"depends_on": "returned_qty", "fieldname": "returned_qty", "fieldtype": "Float", "label": "Returned <PERSON><PERSON>", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_60", "fieldtype": "Column Break"}, {"depends_on": "billed_amt", "fieldname": "billed_amt", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Billed Amount", "no_copy": 1, "options": "currency", "print_hide": 1, "read_only": 1}, {"description": "Tax detail table fetched from item master as a string and stored in this field.\nUsed for Taxes and Charges", "fieldname": "item_tax_rate", "fieldtype": "Code", "hidden": 1, "label": "Item Tax Rate", "oldfieldname": "item_tax_rate", "oldfieldtype": "Small Text", "print_hide": 1, "read_only": 1, "report_hide": 1}, {"collapsible": 1, "fieldname": "accounting_details", "fieldtype": "Section Break", "label": "Accounting Details"}, {"fieldname": "expense_account", "fieldtype": "Link", "label": "Expense Account", "options": "Account", "print_hide": 1}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center", "print_hide": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "page_break", "fieldtype": "Check", "label": "Page Break", "no_copy": 1, "oldfieldname": "page_break", "oldfieldtype": "Check", "print_hide": 1}, {"fieldname": "item_tax_template", "fieldtype": "Link", "label": "Item Tax Template", "options": "Item Tax Template"}, {"fieldname": "section_break_72", "fieldtype": "Section Break"}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions "}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"collapsible": 1, "fieldname": "manufacture_details", "fieldtype": "Section Break", "label": "Manufacture"}, {"fieldname": "manufacturer", "fieldtype": "Link", "label": "Manufacturer", "options": "Manufacturer"}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"fieldname": "manufacturer_part_no", "fieldtype": "Data", "label": "Manufacturer Part Number"}, {"default": "0", "fieldname": "against_blanket_order", "fieldtype": "Check", "label": "Against Blanket Order"}, {"default": "0", "depends_on": "is_fixed_asset", "fetch_from": "item_code.is_fixed_asset", "fieldname": "is_fixed_asset", "fieldtype": "Check", "label": "Is Fixed Asset", "read_only": 1}, {"fieldname": "more_info_section_break", "fieldtype": "Section Break", "label": "More Information"}, {"depends_on": "eval: doc.uom != doc.stock_uom", "fieldname": "stock_uom_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate of Stock UOM", "no_copy": 1, "options": "currency", "read_only": 1}, {"allow_on_submit": 1, "fieldname": "actual_qty", "fieldtype": "Float", "label": "Available Qty at Target Warehouse", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "fieldname": "company_total_stock", "fieldtype": "Float", "label": "Available Qty at Company", "no_copy": 1, "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "eval: doc.margin_type || doc.discount_amount", "fieldname": "discount_and_margin_section", "fieldtype": "Section Break", "label": "Discount and Margin"}, {"depends_on": "price_list_rate", "fieldname": "margin_type", "fieldtype": "Select", "label": "Margin Type", "options": "\nPercentage\nAmount", "print_hide": 1}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate", "fieldname": "margin_rate_or_amount", "fieldtype": "Float", "label": "Margin Rate or Amount", "print_hide": 1}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate && doc.margin_rate_or_amount", "fieldname": "rate_with_margin", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate With Margin", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_28", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate && doc.margin_rate_or_amount", "fieldname": "base_rate_with_margin", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate With Margin (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "production_plan", "fieldtype": "Link", "label": "Production Plan", "options": "Production Plan", "print_hide": 1, "read_only": 1}, {"fieldname": "production_plan_item", "fieldtype": "Data", "hidden": 1, "label": "Production Plan Item", "no_copy": 1, "read_only": 1}, {"fieldname": "production_plan_sub_assembly_item", "fieldtype": "Data", "hidden": 1, "label": "Production Plan Sub Assembly Item", "no_copy": 1, "read_only": 1}, {"fieldname": "product_bundle", "fieldtype": "Link", "label": "Product Bundle", "options": "Product Bundle", "read_only": 1}, {"fieldname": "sales_order_packed_item", "fieldtype": "Data", "label": "Sales Order Packed Item", "no_copy": 1, "print_hide": 1}, {"depends_on": "eval:parent.is_subcontracted && !parent.is_old_subcontracting_flow", "fieldname": "fg_item", "fieldtype": "Link", "label": "Finished Good", "mandatory_depends_on": "eval:parent.is_subcontracted && !parent.is_old_subcontracting_flow", "options": "<PERSON><PERSON>"}, {"default": "1", "depends_on": "eval:parent.is_subcontracted && !parent.is_old_subcontracting_flow", "fieldname": "fg_item_qty", "fieldtype": "Float", "label": "Finished Good Qty", "mandatory_depends_on": "eval:parent.is_subcontracted && !parent.is_old_subcontracting_flow"}, {"depends_on": "eval:parent.is_internal_supplier", "fieldname": "from_warehouse", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "From Warehouse", "options": "Warehouse"}, {"collapsible": 1, "fieldname": "references_section", "fieldtype": "Section Break", "label": "References"}, {"fieldname": "column_break_54", "fieldtype": "Column Break"}, {"default": "1", "fieldname": "apply_tds", "fieldtype": "Check", "label": "Apply TDS"}, {"fieldname": "wip_composite_asset", "fieldtype": "Link", "label": "WIP Composite Asset", "options": "<PERSON><PERSON>"}, {"fieldname": "column_break_fyqr", "fieldtype": "Column Break"}], "idx": 1, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-02-05 11:23:24.859435", "modified_by": "Administrator", "module": "Buying", "name": "Purchase Order Item", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "quick_entry": 1, "search_fields": "item_name", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}