{"actions": [], "creation": "2016-03-29 05:59:11.896885", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["supplier", "contact", "quote_status", "column_break_3", "supplier_name", "email_id", "send_email", "email_sent"], "fields": [{"allow_on_submit": 1, "columns": 2, "default": "1", "fieldname": "send_email", "fieldtype": "Check", "in_list_view": 1, "label": "Send Email"}, {"allow_on_submit": 1, "default": "0", "depends_on": "eval:doc.docstatus >= 1", "fieldname": "email_sent", "fieldtype": "Check", "label": "<PERSON><PERSON>", "no_copy": 1, "read_only": 1}, {"columns": 2, "fieldname": "supplier", "fieldtype": "Link", "in_list_view": 1, "label": "Supplier", "options": "Supplier", "reqd": 1}, {"allow_on_submit": 1, "columns": 2, "fieldname": "contact", "fieldtype": "Link", "in_list_view": 1, "label": "Contact", "no_copy": 1, "options": "Contact"}, {"allow_on_submit": 1, "depends_on": "eval:doc.docstatus >= 1", "fieldname": "quote_status", "fieldtype": "Select", "label": "Quote Status", "options": "Pending\nReceived", "read_only": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"bold": 1, "fetch_from": "supplier.supplier_name", "fieldname": "supplier_name", "fieldtype": "Read Only", "in_global_search": 1, "label": "Supplier Name"}, {"columns": 3, "fetch_from": "contact.email_id", "fieldname": "email_id", "fieldtype": "Data", "in_list_view": 1, "label": "Email <PERSON>d", "no_copy": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2020-11-04 22:01:43.832942", "modified_by": "Administrator", "module": "Buying", "name": "Request for Quotation Supplier", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}