{"add_total_row": 1, "creation": "2018-10-05 16:08:24.156448", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "modified": "2019-02-12 14:32:29.107109", "modified_by": "Administrator", "module": "Buying", "name": "Purchase Analytics", "owner": "Administrator", "prepared_report": 0, "ref_doctype": "Purchase Order", "report_name": "Purchase Analytics", "report_type": "Script Report", "roles": [{"role": "Purchase Manager"}, {"role": "Purchase User"}]}