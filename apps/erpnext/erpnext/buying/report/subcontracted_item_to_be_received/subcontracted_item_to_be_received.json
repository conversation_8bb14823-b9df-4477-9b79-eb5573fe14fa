{"add_total_row": 1, "creation": "2019-05-03 11:25:03.685247", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "modified": "2019-05-03 11:25:03.685247", "modified_by": "Administrator", "module": "Buying", "name": "Subcontracted Item To Be Received", "owner": "Administrator", "prepared_report": 0, "ref_doctype": "Subcontracting Order", "report_name": "Subcontracted Item To Be Received", "report_type": "Script Report", "roles": [{"role": "Stock User"}, {"role": "Purchase Manager"}, {"role": "Purchase User"}]}