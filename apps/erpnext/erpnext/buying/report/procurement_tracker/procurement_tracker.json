{"add_total_row": 1, "creation": "2019-03-29 17:05:45.196949", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "modified": "2019-07-21 23:24:21.094269", "modified_by": "Administrator", "module": "Buying", "name": "Procurement Tracker", "owner": "Administrator", "prepared_report": 0, "ref_doctype": "Purchase Order", "report_name": "Procurement Tracker", "report_type": "Script Report", "roles": [{"role": "Purchase Manager"}, {"role": "Purchase User"}]}