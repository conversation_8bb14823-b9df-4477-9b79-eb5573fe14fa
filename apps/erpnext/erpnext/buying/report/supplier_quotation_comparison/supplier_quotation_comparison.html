
<head>


	<link href="/assets/frappe/css/c3.min.css" rel="stylesheet" type="text/css">


	<script type="text/javascript" src="/assets/frappe/js/lib/d3.min.js"></script>
	<script type="text/javascript" src="/assets/frappe/js/lib/c3.min.js"></script>
    <script type="text/javascript">

		onReady("#chart_div", function() {
			var chartData = [];
			{% var q = 0; %}
			{% for(var j=0, m=data.length+1; j<m; j++) { %}

				var tempData{%=j%} = [];

				{% for(var i=1, l=report.columns.length; i<l; i++) { %}
					{% if(__(report.columns[i].label) != __("Quotation")) { %}

						{% if(j == 0) { %}
							{% if(i == 1) { %}
								tempData{%=j%}[{%=i%}-1] = \"x\";
							{% } else { %}

								tempData{%=j%}[{%=i%}-1] = Math.log(parseInt(\"{%= report.columns[i].label %}\".replace(\"Qty: \",\"\"))) / Math.LN10;
							{% } %}
						{% } else { %}
							{% if(i == 1) { %}
								tempData{%=j%}[{%=i%}-1] = \"{%= data[j-1][report.columns[i].fieldname] %} \";
							{% } else { %}
							tempData{%=j%}[{%=i%}-1] = {% if(data[j-1][report.columns[i].fieldname] == "") { if (i > 2) { %}
										tempData{%=j%}[{%=i%}-2]
									{% } else { %}
										0
								{% } } else { %}
									{%= data[j-1][report.columns[i].fieldname] %}
							{% } %};
							{% } %};
						{% } %}
					{% } else { %}
						{% if(j == 0) { %}
							{% if(i < l-1) { %}
								tempData{%=j%}[{%=i%}-1] = Math.log(parseInt(\"{%= report.columns[i+1].label %}\".replace(\"Qty: \",\"\"))-1) / Math.LN10;
							{% } else { %}
								tempData{%=j%}[{%=i%}-1] = Math.log(2*parseInt(\"{%= report.columns[i-1].label %}\".replace(\"Qty: \",\"\"))) / Math.LN10;
							{% } %}
						{% } else { %}

							tempData{%=j%}[{%=i%}-1] = tempData{%=j%}[{%=i%}-2];
						{% } %}

					{% } %}

				{% } %}
				chartData[{%=j%}] = tempData{%=j%};
			{% } %}
			console.log(chartData);

			hold = {
			bindto: "#chart_div" ,data: {
				x: "x",
				columns: chartData
			},
			axis: {
				x: {
					tick: {
					   format: function (x22) { return Math.pow(10,x22).toFixed(0); },
						culling: {
							max: {%=report.columns.length%} / 2
						}

					}
				}
			},
			point: {
			  show: false
			}
			};
			console.log(hold);
			var chart = c3.generate(hold);
		});

		function onReady(selector, callback) {
        var intervalID = window.setInterval(function() {
          if (document.querySelector(selector) !== undefined) {
            window.clearInterval(intervalID);
            callback.call(this);
          }
        }, 500);}


    </script>

 </head>
<div style="margin-bottom: 7px;" class="text-center">
	{%= frappe.boot.letter_heads[frappe.defaults.get_default("letter_head")] %}
</div>
<h2 class="text-center">{%= __(report.report_name) %}</h2>
<h4 class="text-center">{%= filters.item %} </h4>

<hr>
<table class="table table-bordered">
	<thead>
		<tr>
			{% for(var i=0, l=report.columns.length; i<l; i++) { %}
				<th style="width: 15%">{%= report.columns[i].label %}</th>
			{% } %}

		</tr>
	</thead>
	<tbody>

		{% for(var i=0, l=data.length; i<l; i++) { %}
			<tr>

				{% for(var j=0,m=report.columns.length; j<m; j++) { %}
					<td style="width: 15%">{%= data[i][report.columns[j].fieldname] %}</td>
				{% } %}

		</tr>
		{% } %}
	</tbody>
</table>

<h4 class="text-center"> Analysis Chart </h4>
<div id="chart_div"></div>



<p class="text-right text-muted">Printed On {%= frappe.datetime.str_to_user(frappe.datetime.get_datetime_as_string()) %}</p>
