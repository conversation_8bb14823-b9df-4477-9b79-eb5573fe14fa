{"charts": [{"chart_name": "Purchase Order Trends", "label": "Purchase Order Trends"}], "content": "[{\"id\":\"I3JijHOxil\",\"type\":\"onboarding\",\"data\":{\"onboarding_name\":\"Buying\",\"col\":12}},{\"id\":\"j3dJGo8Ok6\",\"type\":\"chart\",\"data\":{\"chart_name\":\"Purchase Order Trends\",\"col\":12}},{\"id\":\"oN7lXSwQji\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"Ivw1PI_wEJ\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>اختصاراتك</b></span>\",\"col\":12}},{\"id\":\"RrWFEi4kCf\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Item\",\"col\":3}},{\"id\":\"RFIakryyJP\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Material Request\",\"col\":3}},{\"id\":\"bM10abFmf6\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Purchase Order\",\"col\":3}},{\"id\":\"lR0Hw_37Pu\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Purchase Analytics\",\"col\":3}},{\"id\":\"_HN0Ljw1lX\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Purchase Order Analysis\",\"col\":3}},{\"id\":\"kuLuiMRdnX\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Dashboard\",\"col\":3}},{\"id\":\"tQFeiKptW2\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Learn Procurement\",\"col\":3}},{\"id\":\"0NiuFE_EGS\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"Xe2GVLOq8J\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Reports &amp; Masters</b></span>\",\"col\":12}},{\"id\":\"QwqyG6XuUt\",\"type\":\"card\",\"data\":{\"card_name\":\"Buying\",\"col\":3}},{\"id\":\"bTPjOxC_N_\",\"type\":\"card\",\"data\":{\"card_name\":\"Items & Pricing\",\"col\":3}},{\"id\":\"EDOsBOmwgw\",\"type\":\"card\",\"data\":{\"card_name\":\"المورد\",\"col\":3}},{\"id\":\"oWNNIiNb2i\",\"type\":\"card\",\"data\":{\"card_name\":\"بطاقة أداء المورد\",\"col\":3}}]", "creation": "2020-01-28 11:50:26.195467", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "buying", "idx": 0, "is_hidden": 0, "label": "Buying", "links": [{"hidden": 0, "is_query_report": 0, "label": "Buying", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "Material Request", "link_count": 0, "link_to": "Material Request", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "Item, Supplier", "hidden": 0, "is_query_report": 0, "label": "Purchase Order", "link_count": 0, "link_to": "Purchase Order", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "Item, Supplier", "hidden": 0, "is_query_report": 0, "label": "Purchase Invoice", "link_count": 0, "link_to": "Purchase Invoice", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "Item, Supplier", "hidden": 0, "is_query_report": 0, "label": "Request for Quotation", "link_count": 0, "link_to": "Request for Quotation", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "Item, Supplier", "hidden": 0, "is_query_report": 0, "label": "Supplier Quotation", "link_count": 0, "link_to": "Supplier Quotation", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "بطاقة أداء المورد", "link_count": 4, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "بطاقة أداء المورد", "link_count": 0, "link_to": "Supplier Scorecard", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "مورد بطاقة الأداء المتغير", "link_count": 0, "link_to": "Supplier Scorecard Variable", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "معايير بطاقة تقييم الموردين", "link_count": 0, "link_to": "Supplier Scorecard Criteria", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "المورد بطاقة الأداء الدائمة", "link_count": 0, "link_to": "Supplier Scorecard Standing", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "المورد", "link_count": 4, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "المورد", "link_count": 0, "link_to": "Supplier", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "مجموعة الموردين", "link_count": 0, "link_to": "Supplier Group", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "اتصال", "link_count": 0, "link_to": "Contact", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "عنوان", "link_count": 0, "link_to": "Address", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Items & Pricing", "link_count": 7, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "السلعة", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "سعر الصنف", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "قائمة الأسعار", "link_count": 0, "link_to": "Price List", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "حزم المنتجات", "link_count": 0, "link_to": "Product Bundle", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "مجموعة الصنف", "link_count": 0, "link_to": "Item Group", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "مخطط ترويجي", "link_count": 0, "link_to": "Promotional Scheme", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "قاعدة التسعير", "link_count": 0, "link_to": "Pricing Rule", "link_type": "DocType", "onboard": 0, "type": "Link"}], "modified": "2025-03-18 02:02:06.593867", "modified_by": "Administrator", "module": "Buying", "name": "Buying", "number_cards": [], "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [], "restrict_to_domain": "", "roles": [], "sequence_id": 5.0, "shortcuts": [{"color": "Green", "format": "{} Available", "label": "<PERSON><PERSON>", "link_to": "<PERSON><PERSON>", "stats_filter": "{\n    \"disabled\": 0\n}", "type": "DocType"}, {"color": "Grey", "doc_view": "List", "label": "Learn Procurement", "type": "URL", "url": "https://frappe.school/courses/procurement?utm_source=in_app"}, {"color": "Yellow", "format": "{} Pending", "label": "Material Request", "link_to": "Material Request", "stats_filter": "{\n    \"company\": [\"like\", '%' + frappe.defaults.get_global_default(\"company\") + '%'],\n    \"status\": \"Pending\"\n}", "type": "DocType"}, {"color": "Yellow", "format": "{}  To Receive", "label": "Purchase Order", "link_to": "Purchase Order", "stats_filter": "{\n    \"company\": [\"like\", '%' + frappe.defaults.get_global_default(\"company\") + '%'],\n    \"status\":[\"in\", [\"To Receive\", \"To Receive and Bill\"]]\n}", "type": "DocType"}, {"label": "Purchase Analytics", "link_to": "Purchase Analytics", "type": "Report"}, {"label": "Purchase Order Analysis", "link_to": "Purchase Order Analysis", "type": "Report"}, {"label": "Dashboard", "link_to": "Buying", "type": "Dashboard"}], "title": "Buying"}