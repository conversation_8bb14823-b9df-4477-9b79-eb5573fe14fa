{"creation": "2021-07-29 14:11:58.271113", "docstatus": 0, "doctype": "Form Tour", "idx": 0, "is_standard": 1, "modified": "2021-10-05 13:11:31.436135", "modified_by": "Administrator", "module": "Buying", "name": "Purchase Order", "owner": "Administrator", "reference_doctype": "Purchase Order", "save_on_complete": 1, "steps": [{"description": "Select a Supplier", "field": "", "fieldname": "supplier", "fieldtype": "Link", "has_next_condition": 0, "is_table_field": 0, "label": "Supplier", "parent_field": "", "position": "Right", "title": "Supplier"}, {"description": "Set the \"Required By\" date for the materials. This sets the \"Required By\" date for all the items.", "field": "", "fieldname": "schedule_date", "fieldtype": "Date", "has_next_condition": 0, "is_table_field": 0, "label": "Required By", "parent_field": "", "position": "Left", "title": "Required By"}, {"description": "Items to be purchased can be added here.", "field": "", "fieldname": "items", "fieldtype": "Table", "has_next_condition": 0, "is_table_field": 0, "label": "Items", "parent_field": "", "position": "Bottom", "title": "Items Table"}, {"child_doctype": "Purchase Order Item", "description": "Enter the Item Code.", "field": "", "fieldname": "item_code", "fieldtype": "Link", "has_next_condition": 1, "is_table_field": 1, "label": "Item Code", "next_step_condition": "eval: doc.item_code", "parent_field": "", "parent_fieldname": "items", "position": "Right", "title": "Item Code"}, {"child_doctype": "Purchase Order Item", "description": "Enter the required quantity for the material.", "field": "", "fieldname": "qty", "fieldtype": "Float", "has_next_condition": 0, "is_table_field": 1, "label": "Quantity", "parent_field": "", "parent_fieldname": "items", "position": "Bottom", "title": "Quantity"}], "title": "Purchase Order"}