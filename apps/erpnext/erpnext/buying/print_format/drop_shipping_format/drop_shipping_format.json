{"creation": "2015-12-04 13:11:16.992997", "custom_format": 1, "disabled": 0, "doc_type": "Purchase Order", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "html": "<div class=\"page-break\">\n    <div class=\"print-heading\">\n\t\t<h2>Purchase Order<br>\n\t        <small>{{doc.name}}</small>\n        </h2>\n    </div>\n    <div class=\"row section-break\">\n        <div class=\"col-xs-6 column-break\">\n                <div class=\"row\">\n\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t<label>Supplier Name</label>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t\t{{doc.supplier_name}}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t{% if doc.address_display %}\n\t                <div class=\"row\">\n\t\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t\t<label>Address</label>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t\t\t{{doc.address_display}}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t{%- endif -%}\n\t\t\t\t{% if doc.contact_display %}\n\t                <div class=\"row\">\n\t\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t\t<label>Contact</label>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t\t\t{{doc.contact_display}}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t{%- endif -%}\n\t\t\t\t{% if doc.contact_mobile %}\n\t                <div class=\"row\">\n\t\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t\t<label>Mobile No</label>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t\t\t{{doc.contact_mobile}}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t{%- endif -%}\n        </div>\n        \n        <div class=\"col-xs-6 column-break\">\n            <div class=\"row\">\n\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t<label>Date</label>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t{{doc.transaction_date}}\n\t\t\t\t</div>\n\t\t\t</div>\n            <div class=\"row\">\n\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t<label>Customer Name</label>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t{{doc.customer_name}}\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t{% if doc.shipping_address_display %}\n\t            <div class=\"row\">\n\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t<label>Customer Address</label>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t\t{{doc.shipping_address_display}}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t{%- endif -%}\n\t\t\t{% if doc.customer_contact_display %}\n\t            <div class=\"row\">\n\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t<label>Customer Contact</label>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t\t{{doc.customer_contact_display}}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t{%- endif -%}\n\t\t\t{% if doc.customer_contact_mobile %}\n\t            <div class=\"row\">\n\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t<label>Customer Mobile No</label>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t\t{{doc.customer_contact_mobile}}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t{%- endif -%}\n        </div>\n    </div>\n\t<table class=\"table table-bordered\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<th>Sr</th>\n\t\t\t\t<th>Item Name</th>\n\t\t\t\t<th>Description</th>\n\t\t\t\t<th class=\"text-right\">Qty</th>\n\t\t\t\t<th class=\"text-right\">Rate</th>\n\t\t\t\t<th class=\"text-right\">Amount</th>\n\t\t\t</tr>\n\t\t\t{%- for row in doc.items -%}\n\t\t\t<tr>\n\t\t\t\t<td style=\"width: 3%;\">{{ row.idx }}</td>\n\t\t\t\t<td style=\"width: 20%;\">\n\t\t\t\t\t{{ row.item_name }}\n\t\t\t\t\t{% if row.item_code != row.item_name -%}\n\t\t\t\t\t<br>Item Code: {{ row.item_code}}\n\t\t\t\t\t{%- endif %}\n\t\t\t\t</td>\n\t\t\t\t<td style=\"width: 37%;\">\n\t\t\t\t\t<div style=\"border: 0px;\">{{ row.description }}</div></td>\n\t\t\t\t<td style=\"width: 10%; text-align: right;\">{{ row.qty }} {{ row.uom or row.stock_uom }}</td>\n\t\t\t\t<td style=\"width: 15%; text-align: right;\">{{\n\t\t\t\t\trow.get_formatted(\"rate\", doc) }}</td>\n\t\t\t\t<td style=\"width: 15%; text-align: right;\">{{\n\t\t\t\t\trow.get_formatted(\"amount\", doc) }}</td>\n\t\t\t</tr>\n\t\t\t{%- endfor -%}\n\t\t</tbody>\n\t</table>\n    <div class=\"row section-break\">\n        <div class=\"col-xs-6 column-break\">\n        </div>\n        <div class=\"col-xs-6 column-break\">\n                <div class=\"row\">\n\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t<label>Total</label>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"col-xs-7 text-right value\">\n\t\t\t\t\t\t{{doc.total}}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n                <div class=\"row\">\n\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t<label>Grand Total</label>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"col-xs-7 text-right value\">\n\t\t\t\t\t\t{{doc.grand_total}}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n                <div class=\"row\">\n\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t<label>In Words</label>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"col-xs-7 text-right value\">\n\t\t\t\t\t\t{{doc.in_words}}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n        </div>\n    </div>\n</div>", "idx": 0, "modified": "2016-03-07 20:49:39.443328", "modified_by": "Administrator", "name": "Drop Shipping Format", "owner": "Administrator", "print_format_builder": 0, "print_format_type": "<PERSON><PERSON>", "standard": "Yes"}