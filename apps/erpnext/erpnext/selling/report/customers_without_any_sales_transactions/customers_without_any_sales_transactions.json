{"add_total_row": 0, "apply_user_permissions": 0, "creation": "2018-05-17 15:25:06.015111", "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "modified": "2018-05-21 11:06:11.920622", "modified_by": "Administrator", "module": "Selling", "name": "Customers Without Any Sales Transactions", "owner": "Administrator", "query": "SELECT\n\t`tabCustomer`.name as \"Customer:Link/Customer:120\",\n\t`tabCustomer`.customer_name as \"Customer Name::120\",\n\t`tabCustomer`.territory as \"Territory:Link/Territory:120\",\n\t`tabCustomer`.customer_group as \"Customer Group:Link/Customer Group:120\"\nFROM\n\t`tabCustomer`\nWHERE\n\tnot exists(select name from `tabSales Invoice` where `tabCustomer`.name = `tabSales Invoice`.customer and `tabSales Invoice`.docstatus=1 limit 1)\n\tand not exists(select name from `tabSales Order` where `tabCustomer`.name = `tabSales Order`.customer and `tabSales Order`.docstatus=1 limit 1)", "ref_doctype": "Sales Invoice", "report_name": "Customers Without Any Sales Transactions", "report_type": "Query Report", "roles": [{"role": "Sales User"}, {"role": "Sales Manager"}, {"role": "System Manager"}]}