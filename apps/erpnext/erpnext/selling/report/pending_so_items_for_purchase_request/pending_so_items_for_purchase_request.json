{"add_total_row": 0, "creation": "2018-11-12 14:08:27.241332", "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "modified": "2018-11-12 14:08:27.241332", "modified_by": "Administrator", "module": "Selling", "name": "Pending SO Items For Purchase Request", "owner": "Administrator", "prepared_report": 0, "ref_doctype": "Sales Order", "report_name": "Pending SO Items For Purchase Request", "report_type": "Script Report", "roles": [{"role": "Stock User"}, {"role": "Sales Manager"}, {"role": "Maintenance User"}, {"role": "Accounts User"}, {"role": "Sales User"}]}