{"creation": "2021-06-29 20:39:19.408763", "docstatus": 0, "doctype": "Form Tour", "idx": 0, "is_standard": 1, "modified": "2022-07-11 20:49:01.359489", "modified_by": "Administrator", "module": "Selling", "name": "Selling <PERSON>", "owner": "Administrator", "reference_doctype": "Selling <PERSON>", "save_on_complete": 0, "steps": [{"description": "By default, the Customer Name is set as per the Full Name entered. If you want Customers to be named by a <a href=\"https://docs.erpnext.com/docs/user/manual/en/setting-up/settings/naming-series\" target=\"_blank\">Naming Series</a>. Choose the 'Naming Series' option.", "field": "", "fieldname": "cust_master_name", "fieldtype": "Select", "has_next_condition": 0, "is_table_field": 0, "label": "Customer Naming By", "parent_field": "", "position": "Right", "title": "Customer Naming By"}, {"description": "If this option is configured 'Yes', ERPNext will prevent you from creating a Sales Invoice or Delivery Note without creating a Sales Order first. This configuration can be overridden for a particular Customer by enabling the 'Allow Sales Invoice Creation Without Sales Order' checkbox in the Customer master.", "field": "", "fieldname": "so_required", "fieldtype": "Select", "has_next_condition": 0, "is_table_field": 0, "label": "Is Sales Order Required for Sales Invoice & Delivery Note Creation?", "parent_field": "", "position": "Right", "title": "Sales Order Required for Sales Invoice & Delivery Note Creation"}, {"description": "If this option is configured 'Yes', ERPNext will prevent you from creating a Sales Invoice without creating a Delivery Note first. This configuration can be overridden for a particular Customer by enabling the 'Allow Sales Invoice Creation Without Delivery Note' checkbox in the Customer master.", "field": "", "fieldname": "dn_required", "fieldtype": "Select", "has_next_condition": 0, "is_table_field": 0, "label": "Is Delivery Note Required for Sales Invoice Creation?", "parent_field": "", "position": "Right", "title": "Delivery Note Required for Sales Invoice Creation"}, {"description": "Configure the default Price List when creating a new Sales transaction. Item prices will be fetched from this Price List.", "field": "", "fieldname": "selling_price_list", "fieldtype": "Link", "has_next_condition": 0, "is_table_field": 0, "label": "Default Price List", "parent_field": "", "position": "Right", "title": "De<PERSON><PERSON>lling Price List"}], "title": "Selling <PERSON>"}