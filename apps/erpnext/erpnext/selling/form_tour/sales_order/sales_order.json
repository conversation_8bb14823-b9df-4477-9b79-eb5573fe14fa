{"creation": "2021-06-29 21:13:36.089054", "docstatus": 0, "doctype": "Form Tour", "idx": 0, "is_standard": 1, "modified": "2021-06-29 21:13:36.089054", "modified_by": "Administrator", "module": "Selling", "name": "Sales Order", "owner": "Administrator", "reference_doctype": "Sales Order", "save_on_complete": 1, "steps": [{"description": "Select a customer.", "field": "", "fieldname": "customer", "fieldtype": "Link", "has_next_condition": 1, "is_table_field": 0, "label": "Customer", "next_step_condition": "customer", "parent_field": "", "position": "Right", "title": "Select Customer"}, {"description": "You can add items here.", "field": "", "fieldname": "items", "fieldtype": "Table", "has_next_condition": 0, "is_table_field": 0, "label": "Items", "parent_field": "", "position": "Bottom", "title": "List of items"}, {"child_doctype": "Sales Order Item", "description": "Select an item.", "field": "", "fieldname": "item_code", "fieldtype": "Link", "has_next_condition": 1, "is_table_field": 1, "label": "Item Code", "next_step_condition": "eval: doc.item_code", "parent_field": "", "parent_fieldname": "items", "position": "Right", "title": "Select Item"}, {"child_doctype": "Sales Order Item", "description": "Enter quantity.", "field": "", "fieldname": "qty", "fieldtype": "Float", "has_next_condition": 0, "is_table_field": 1, "label": "Quantity", "parent_field": "", "parent_fieldname": "items", "position": "Right", "title": "Enter Quantity"}, {"child_doctype": "Sales Order Item", "description": "Enter rate of the item.", "field": "", "fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "has_next_condition": 0, "is_table_field": 1, "label": "Rate", "parent_field": "", "parent_fieldname": "items", "position": "Right", "title": "Enter Rate"}, {"description": "You can add sales taxes and charges here.", "field": "", "fieldname": "taxes", "fieldtype": "Table", "has_next_condition": 0, "is_table_field": 0, "label": "Sales Taxes and Charges", "parent_field": "", "position": "Bottom", "title": "Add Sales Taxes and Charges"}], "title": "Sales Order"}