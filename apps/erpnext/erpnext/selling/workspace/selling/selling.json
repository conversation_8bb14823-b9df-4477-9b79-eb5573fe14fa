{"charts": [{"chart_name": "Sales Order Trends", "label": "Sales Order Trends"}], "content": "[\n  {\n    \"type\": \"link\",\n    \"label\": \"💰 فاتورة نقدية\",\n    \"link_type\": \"Page\",\n    \"link_to\": \"فاتورة نقدية\",\n    \"dependencies\": \"\",\n    \"description\": \"مساحة عمل مخصصة للفواتير النقدية\",\n    \"onboard\": 0,\n    \"is_query_report\": 0,\n    \"icon\": \"money\",\n    \"hidden\": 0\n  },\n  {\n    \"type\": \"link\",\n    \"label\": \"🔄 مرتجع مبيعات\",\n    \"link_type\": \"Page\",\n    \"link_to\": \"مرتجع مبيعات\",\n    \"dependencies\": \"\",\n    \"description\": \"مساحة عمل مخصصة لمرتجعات المبيعات\",\n    \"onboard\": 0,\n    \"is_query_report\": 0,\n    \"icon\": \"return\",\n    \"hidden\": 0\n  },\n  {\n    \"id\": \"ow595dYDrI\",\n    \"type\": \"onboarding\",\n    \"data\": {\n      \"onboarding_name\": \"Selling\",\n      \"col\": 12\n    }\n  },\n  {\n    \"id\": \"vBSf8Vi9U8\",\n    \"type\": \"chart\",\n    \"data\": {\n      \"chart_name\": \"Sales Order Trends\",\n      \"col\": 12\n    }\n  },\n  {\n    \"id\": \"aW2i5R5GRP\",\n    \"type\": \"spacer\",\n    \"data\": {\n      \"col\": 12\n    }\n  },\n  {\n    \"id\": \"1it3dCOnm6\",\n    \"type\": \"header\",\n    \"data\": {\n      \"text\": \"<span class=\\\"h4\\\"><b>الوصول السريع</b></span>\",\n      \"col\": 12\n    }\n  },\n  {\n    \"id\": \"x7pLl-spS4\",\n    \"type\": \"shortcut\",\n    \"data\": {\n      \"shortcut_name\": \"Item\",\n      \"col\": 3\n    }\n  },\n  {\n    \"id\": \"SSGrXWmY-H\",\n    \"type\": \"shortcut\",\n    \"data\": {\n      \"shortcut_name\": \"Sales Order\",\n      \"col\": 3\n    }\n  },\n  {\n    \"id\": \"-5J_yLxDaS\",\n    \"type\": \"shortcut\",\n    \"data\": {\n      \"shortcut_name\": \"Sales Analytics\",\n      \"col\": 3\n    }\n  },\n  {\n    \"id\": \"6YEYpnIBKV\",\n    \"type\": \"shortcut\",\n    \"data\": {\n      \"shortcut_name\": \"Point of Sale\",\n      \"col\": 3\n    }\n  },\n  {\n    \"id\": \"c_GjZuZ2oN\",\n    \"type\": \"shortcut\",\n    \"data\": {\n      \"shortcut_name\": \"Dashboard\",\n      \"col\": 3\n    }\n  },\n  {\n    \"id\": \"mX-9DJSyT2\",\n    \"type\": \"shortcut\",\n    \"data\": {\n      \"shortcut_name\": \"Learn Sales Management\",\n      \"col\": 3\n    }\n  },\n  {\n    \"id\": \"vaTWM7c_zZ\",\n    \"type\": \"shortcut\",\n    \"data\": {\n      \"shortcut_name\": \"فاتورة  نقدية\",\n      \"col\": 3\n    }\n  },\n  {\n    \"id\": \"oNjjNbnUHp\",\n    \"type\": \"spacer\",\n    \"data\": {\n      \"col\": 12\n    }\n  },\n  {\n    \"id\": \"0BcePLg0g1\",\n    \"type\": \"header\",\n    \"data\": {\n      \"text\": \"<span class=\\\"h4\\\"><b>Reports &amp; Masters</b></span>\",\n      \"col\": 12\n    }\n  },\n  {\n    \"id\": \"uze5dJ1ipL\",\n    \"type\": \"card\",\n    \"data\": {\n      \"card_name\": \"المبيعات\",\n      \"col\": 4\n    }\n  },\n  {\n    \"id\": \"3j2fYwMAkq\",\n    \"type\": \"card\",\n    \"data\": {\n      \"card_name\": \"Point of Sale\",\n      \"col\": 4\n    }\n  },\n  {\n    \"id\": \"xImm8NepFt\",\n    \"type\": \"card\",\n    \"data\": {\n      \"card_name\": \"السلع والتسعيرات\",\n      \"col\": 4\n    }\n  }\n]", "creation": "2025-07-04 00:15:15.630122", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "sell", "idx": 0, "is_hidden": 0, "label": "Selling", "links": [{"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Customer", "link_count": 0, "link_to": "Customer", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>, Customer", "hidden": 0, "is_query_report": 0, "label": "Quotation", "link_count": 0, "link_to": "Quotation", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>, Customer", "hidden": 0, "is_query_report": 0, "label": "Sales Order", "link_count": 0, "link_to": "Sales Order", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>, Customer", "hidden": 0, "is_query_report": 0, "label": "Sales Invoice", "link_count": 0, "link_to": "Sales Invoice", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>, Customer", "hidden": 0, "is_query_report": 0, "label": "Blanket Order", "link_count": 0, "link_to": "Blanket Order", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "Sales Partner", "link_count": 0, "link_to": "Sales Partner", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "<PERSON><PERSON>, Customer", "hidden": 0, "is_query_report": 0, "label": "Sales Person", "link_count": 0, "link_to": "Sales Person", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>, Price List", "hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Price List", "link_count": 0, "link_to": "Price List", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Item Group", "link_count": 0, "link_to": "Item Group", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "Product Bundle", "link_count": 0, "link_to": "Product Bundle", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Promotional Scheme", "link_count": 0, "link_to": "Promotional Scheme", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "Pricing Rule", "link_count": 0, "link_to": "Pricing Rule", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Shipping Rule", "link_count": 0, "link_to": "Shipping Rule", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Coupon Code", "link_count": 0, "link_to": "Coupon Code", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Selling <PERSON>", "link_count": 0, "link_to": "Selling <PERSON>", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Terms and Conditions Template", "link_count": 0, "link_to": "Terms and Conditions", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Sales Taxes and Charges Template", "link_count": 0, "link_to": "Sales Taxes and Charges Template", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Lead Source", "link_count": 0, "link_to": "Lead Source", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Customer Group", "link_count": 0, "link_to": "Customer Group", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Contact", "link_count": 0, "link_to": "Contact", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Address", "link_count": 0, "link_to": "Address", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Territory", "link_count": 0, "link_to": "Territory", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Campaign", "link_count": 0, "link_to": "Campaign", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Point of Sale", "link_count": 6, "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Point-of-Sale Profile", "link_count": 0, "link_to": "POS Profile", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "POS Settings", "link_count": 0, "link_to": "POS Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "POS Opening Entry", "link_count": 0, "link_to": "POS Opening Entry", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "POS Closing Entry", "link_count": 0, "link_to": "POS Closing Entry", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Loyalty Program", "link_count": 0, "link_to": "Loyalty Program", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Loyalty Point Entry", "link_count": 0, "link_to": "Loyalty Point Entry", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "المبيعات", "link_count": 10, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "العميل", "link_count": 0, "link_to": "Customer", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>, Customer", "hidden": 0, "is_query_report": 0, "label": "عر<PERSON> أسعار", "link_count": 0, "link_to": "Quotation", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>, Customer", "hidden": 0, "is_query_report": 0, "label": "طلب المبيعات", "link_count": 0, "link_to": "Sales Order", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>, Customer", "hidden": 0, "is_query_report": 0, "label": "فاتورة مبيعات", "link_count": 0, "link_to": "Sales Invoice", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>, Customer", "hidden": 0, "is_query_report": 0, "label": "أ<PERSON>ر بطانية", "link_count": 0, "link_to": "Blanket Order", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "شريك المبيعات", "link_count": 0, "link_to": "Sales Partner", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "<PERSON><PERSON>, Customer", "hidden": 0, "is_query_report": 0, "label": "مندوب مبيعات", "link_count": 0, "link_to": "Sales Person", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "السلعة", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>, Price List", "hidden": 0, "is_query_report": 0, "label": "سعر الصنف", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "قائمة الأسعار", "link_count": 0, "link_to": "Price List", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "السلع والتسعيرات", "link_count": 9, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "السلعة", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>, Price List", "hidden": 0, "is_query_report": 0, "label": "سعر الصنف", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "قائمة الأسعار", "link_count": 0, "link_to": "Price List", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "مجموعة الصنف", "link_count": 0, "link_to": "Item Group", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "حزم المنتجات", "link_count": 0, "link_to": "Product Bundle", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "مخطط ترويجي", "link_count": 0, "link_to": "Promotional Scheme", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "قاعدة التسعير", "link_count": 0, "link_to": "Pricing Rule", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "قواعد الشحن", "link_count": 0, "link_to": "Shipping Rule", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON>ز الكوبون", "link_count": 0, "link_to": "Coupon Code", "link_type": "DocType", "onboard": 0, "type": "Link"}], "modified": "2025-07-18 00:52:01.175823", "modified_by": "Administrator", "module": "Selling", "name": "Selling", "number_cards": [], "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [], "restrict_to_domain": "", "roles": [], "sequence_id": 3.0, "shortcuts": [{"color": "Grey", "doc_view": "List", "format": "{}", "label": "فاتورة  نقدية", "link_to": "sales-invoice/new-sa", "stats_filter": "[[\"Sales Invoice\",\"is_cash\",\"=\",1,false]]", "type": "Page"}, {"color": "Grey", "doc_view": "List", "label": "Learn Sales Management", "type": "URL", "url": "https://frappe.school/courses/sales-management-course?utm_source=in_app"}, {"label": "Point of Sale", "link_to": "point-of-sale", "type": "Page"}, {"color": "Grey", "format": "{} Available", "label": "<PERSON><PERSON>", "link_to": "<PERSON><PERSON>", "stats_filter": "{\n    \"disabled\":0\n}", "type": "DocType"}, {"color": "Yellow", "format": "{}  To Deliver", "label": "Sales Order", "link_to": "Sales Order", "stats_filter": "{\n    \"company\": [\"like\", '%' + frappe.defaults.get_global_default(\"company\") + '%'],\n    \"status\":[\"in\", [\"To Deliver\", \"To Deliver and Bill\"]]\n}", "type": "DocType"}, {"color": "Grey", "format": "{} Open", "label": "Sales Analytics", "link_to": "Sales Analytics", "report_ref_doctype": "Sales Order", "stats_filter": "{ \"Status\": \"Open\" }", "type": "Report"}, {"label": "Dashboard", "link_to": "Selling", "type": "Dashboard"}], "title": "Selling"}