{"actions": [], "autoname": "naming_series:", "creation": "2013-04-30 13:13:06", "doctype": "DocType", "document_type": "Document", "engine": "InnoDB", "field_order": ["installation_note", "column_break0", "naming_series", "customer", "customer_address", "contact_person", "customer_name", "address_display", "contact_display", "contact_mobile", "contact_email", "territory", "customer_group", "column_break1", "inst_date", "inst_time", "status", "company", "amended_from", "remarks", "item_details", "items"], "fields": [{"fieldname": "installation_note", "fieldtype": "Section Break", "label": "Installation Note", "oldfieldtype": "Section Break"}, {"fieldname": "column_break0", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "width": "50%"}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "oldfieldname": "naming_series", "oldfieldtype": "Select", "options": "MAT-INS-.YYYY.-", "reqd": 1, "set_only_once": 1}, {"fieldname": "customer", "fieldtype": "Link", "in_global_search": 1, "in_standard_filter": 1, "label": "Customer", "oldfieldname": "customer", "oldfieldtype": "Link", "options": "Customer", "print_hide": 1, "reqd": 1, "search_index": 1}, {"depends_on": "customer", "fieldname": "customer_address", "fieldtype": "Link", "label": "Customer Address", "options": "Address", "print_hide": 1}, {"depends_on": "customer", "fieldname": "contact_person", "fieldtype": "Link", "label": "Contact Person", "options": "Contact", "print_hide": 1}, {"bold": 1, "fieldname": "customer_name", "fieldtype": "Data", "label": "Name", "oldfieldname": "customer_name", "oldfieldtype": "Data", "read_only": 1}, {"fieldname": "address_display", "fieldtype": "Small Text", "hidden": 1, "label": "Address", "read_only": 1}, {"fieldname": "contact_display", "fieldtype": "Small Text", "hidden": 1, "in_global_search": 1, "label": "Contact", "read_only": 1}, {"depends_on": "customer", "fieldname": "contact_mobile", "fieldtype": "Small Text", "in_global_search": 1, "label": "Mobile No", "options": "Phone", "read_only": 1}, {"depends_on": "customer", "fieldname": "contact_email", "fieldtype": "Data", "label": "Contact Email", "options": "Email", "print_hide": 1, "read_only": 1}, {"depends_on": "customer", "fieldname": "territory", "fieldtype": "Link", "label": "Territory", "options": "Territory", "print_hide": 1, "reqd": 1, "search_index": 1}, {"depends_on": "customer", "fieldname": "customer_group", "fieldtype": "Link", "label": "Customer Group", "options": "Customer Group", "print_hide": 1}, {"fieldname": "column_break1", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "width": "50%"}, {"fieldname": "inst_date", "fieldtype": "Date", "label": "Installation Date", "oldfieldname": "inst_date", "oldfieldtype": "Date", "reqd": 1, "search_index": 1}, {"fieldname": "inst_time", "fieldtype": "Time", "label": "Installation Time", "oldfieldname": "inst_time", "oldfieldtype": "Time"}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "in_standard_filter": 1, "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Select", "options": "Draft\nSubmitted\nCancelled", "print_hide": 1, "read_only": 1, "reqd": 1}, {"fieldname": "company", "fieldtype": "Link", "in_standard_filter": 1, "label": "Company", "oldfieldname": "company", "oldfieldtype": "Select", "options": "Company", "print_hide": 1, "remember_last_selected_value": 1, "reqd": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "oldfieldname": "amended_from", "oldfieldtype": "Data", "options": "Installation Note", "print_hide": 1, "read_only": 1}, {"fieldname": "remarks", "fieldtype": "Small Text", "in_list_view": 1, "label": "Remarks", "oldfieldname": "remarks", "oldfieldtype": "Small Text", "print_hide": 1}, {"fieldname": "item_details", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "Simple"}, {"fieldname": "items", "fieldtype": "Table", "label": "Items", "oldfieldname": "installed_item_details", "oldfieldtype": "Table", "options": "Installation Note Item", "reqd": 1}], "icon": "fa fa-wrench", "idx": 1, "is_submittable": 1, "links": [], "modified": "2023-06-03 16:31:08.386961", "modified_by": "Administrator", "module": "Selling", "name": "Installation Note", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "submit": 1, "write": 1}, {"permlevel": 1, "read": 1, "report": 1, "role": "Sales User"}], "sort_field": "modified", "sort_order": "DESC", "states": [], "timeline_field": "customer", "title_field": "customer_name"}