{"actions": [], "creation": "2013-03-07 11:42:57", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "customer_item_code", "col_break1", "item_name", "section_break_5", "description", "item_group", "brand", "image_section", "image", "image_view", "quantity_and_rate", "qty", "stock_uom", "col_break2", "uom", "conversion_factor", "stock_qty", "section_break_16", "price_list_rate", "base_price_list_rate", "discount_and_margin", "margin_type", "margin_rate_or_amount", "rate_with_margin", "column_break_18", "discount_percentage", "discount_amount", "base_rate_with_margin", "section_break1", "rate", "net_rate", "amount", "net_amount", "item_tax_template", "col_break3", "base_rate", "base_net_rate", "base_amount", "base_net_amount", "pricing_rules", "stock_uom_rate", "is_free_item", "is_alternative", "has_alternative_item", "section_break_43", "valuation_rate", "column_break_45", "gross_profit", "item_weight_details", "weight_per_unit", "total_weight", "column_break_20", "weight_uom", "reference", "warehouse", "against_blanket_order", "blanket_order", "blanket_order_rate", "column_break_30", "prevdoc_doctype", "prevdoc_docname", "item_balance", "projected_qty", "actual_qty", "col_break4", "stock_balance", "item_tax_rate", "shopping_cart_section", "additional_notes", "section_break_61", "page_break"], "fields": [{"bold": 1, "columns": 4, "fieldname": "item_code", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "print_width": "150px", "search_index": 1, "width": "150px"}, {"fieldname": "customer_item_code", "fieldtype": "Data", "hidden": 1, "label": "Customer's Item Code", "print_hide": 1, "read_only": 1}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data", "print_hide": 1, "print_width": "150px", "reqd": 1, "width": "150px"}, {"collapsible": 1, "fieldname": "section_break_5", "fieldtype": "Section Break", "label": "Description"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Small Text", "print_width": "300px", "width": "300px"}, {"fetch_from": "item_code.image", "fieldname": "image", "fieldtype": "Attach", "hidden": 1, "label": "Image"}, {"fieldname": "image_view", "fieldtype": "Image", "label": "Image View", "options": "image", "print_hide": 1}, {"fieldname": "quantity_and_rate", "fieldtype": "Section Break", "label": "Quantity and Rate"}, {"bold": 1, "columns": 2, "fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity", "oldfieldname": "qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_width": "100px", "reqd": 1, "width": "100px"}, {"fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "oldfieldname": "stock_uom", "oldfieldtype": "Data", "options": "UOM", "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "col_break2", "fieldtype": "Column Break"}, {"fieldname": "uom", "fieldtype": "Link", "label": "UOM", "options": "UOM", "reqd": 1}, {"fieldname": "conversion_factor", "fieldtype": "Float", "label": "UOM Conversion Factor", "print_hide": 1, "read_only": 1, "reqd": 1}, {"fieldname": "stock_qty", "fieldtype": "Float", "label": "Qty as per Stock UOM", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_16", "fieldtype": "Section Break"}, {"fieldname": "price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price List Rate", "oldfieldname": "ref_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "base_price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price List Rate (Company Currency)", "oldfieldname": "base_ref_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"collapsible": 1, "collapsible_depends_on": "eval: doc.margin_type || doc.discount_amount", "fieldname": "discount_and_margin", "fieldtype": "Section Break", "label": "Discount and Margin"}, {"depends_on": "price_list_rate", "fieldname": "margin_type", "fieldtype": "Select", "label": "Margin Type", "options": "\nPercentage\nAmount", "print_hide": 1}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate", "fieldname": "margin_rate_or_amount", "fieldtype": "Float", "label": "Margin Rate or Amount", "print_hide": 1}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate && doc.margin_rate_or_amount", "fieldname": "rate_with_margin", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate With Margin", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_18", "fieldtype": "Column Break"}, {"depends_on": "price_list_rate", "fieldname": "discount_percentage", "fieldtype": "Percent", "label": "Discount (%) on Price List Rate with <PERSON><PERSON>", "oldfieldname": "adj_rate", "oldfieldtype": "Float", "print_hide": 1, "print_width": "100px", "width": "100px"}, {"depends_on": "price_list_rate", "fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Discount Amount", "options": "currency"}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate && doc.margin_rate_or_amount", "fieldname": "base_rate_with_margin", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate With Margin (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break1", "fieldtype": "Section Break"}, {"bold": 1, "columns": 2, "fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "oldfieldname": "export_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_width": "100px", "width": "100px"}, {"fieldname": "net_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Rate", "options": "currency", "print_hide": 1, "read_only": 1}, {"columns": 2, "fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "oldfieldname": "export_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Amount", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "col_break3", "fieldtype": "Column Break"}, {"fieldname": "base_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate (Company Currency)", "oldfieldname": "basic_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "base_net_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Rate (Company Currency)", "print_hide": 1, "read_only": 1}, {"fieldname": "base_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount (Company Currency)", "oldfieldname": "amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "base_net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "pricing_rules", "fieldtype": "Small Text", "hidden": 1, "label": "Pricing Rules", "print_hide": 1, "read_only": 1}, {"default": "0", "fieldname": "is_free_item", "fieldtype": "Check", "label": "Is Free Item", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "item_weight_details", "fieldtype": "Section Break", "label": "Item Weight Details"}, {"fieldname": "weight_per_unit", "fieldtype": "Float", "label": "Weight Per Unit", "print_hide": 1, "read_only": 1}, {"fieldname": "total_weight", "fieldtype": "Float", "label": "Total Weight", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_20", "fieldtype": "Column Break", "print_hide": 1}, {"fieldname": "weight_uom", "fieldtype": "Link", "label": "Weight UOM", "options": "UOM", "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "eval:doc.warehouse", "fieldname": "item_balance", "fieldtype": "Section Break", "label": "Planning"}, {"fieldname": "warehouse", "fieldtype": "Link", "label": "Warehouse", "options": "Warehouse", "print_hide": 1, "report_hide": 1}, {"fieldname": "column_break_30", "fieldtype": "Column Break"}, {"fieldname": "projected_qty", "fieldtype": "Float", "label": "Projected Qty", "print_hide": 1, "read_only": 1, "report_hide": 1}, {"fieldname": "actual_qty", "fieldtype": "Float", "label": "Actual Qty", "no_copy": 1, "print_hide": 1, "read_only": 1, "report_hide": 1}, {"fieldname": "stock_balance", "fieldtype": "<PERSON><PERSON>", "label": "Stock Balance"}, {"fieldname": "reference", "fieldtype": "Section Break", "label": "Warehouse and Reference"}, {"fieldname": "prevdoc_doctype", "fieldtype": "Link", "hidden": 1, "label": "Against Doctype", "no_copy": 1, "oldfieldname": "prevdoc_doctype", "oldfieldtype": "Data", "options": "DocType", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "prevdoc_docname", "fieldtype": "Dynamic Link", "label": "Against Docname", "no_copy": 1, "oldfieldname": "prevdoc_docname", "oldfieldtype": "Data", "options": "prevdoc_doctype", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "item_tax_rate", "fieldtype": "Code", "hidden": 1, "label": "Item Tax Rate", "oldfieldname": "item_tax_rate", "oldfieldtype": "Small Text", "print_hide": 1, "read_only": 1, "report_hide": 1}, {"fieldname": "col_break4", "fieldtype": "Column Break"}, {"fieldname": "item_tax_template", "fieldtype": "Link", "label": "Item Tax Template", "options": "Item Tax Template", "print_hide": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "page_break", "fieldtype": "Check", "label": "Page Break", "no_copy": 1, "oldfieldname": "page_break", "oldfieldtype": "Check", "print_hide": 1, "report_hide": 1}, {"fieldname": "item_group", "fieldtype": "Link", "hidden": 1, "label": "Item Group", "oldfieldname": "item_group", "oldfieldtype": "Link", "options": "Item Group", "print_hide": 1, "read_only": 1}, {"fieldname": "brand", "fieldtype": "Link", "hidden": 1, "label": "Brand", "oldfieldname": "brand", "oldfieldtype": "Link", "options": "Brand", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"collapsible": 1, "fieldname": "shopping_cart_section", "fieldtype": "Section Break", "label": "Shopping Cart"}, {"fieldname": "additional_notes", "fieldtype": "Text", "label": "Additional Notes"}, {"fieldname": "section_break_61", "fieldtype": "Section Break"}, {"collapsible": 1, "fieldname": "image_section", "fieldtype": "Section Break", "label": "Image"}, {"depends_on": "eval:doc.against_blanket_order", "fieldname": "blanket_order", "fieldtype": "Link", "label": "Blanket Order", "no_copy": 1, "options": "Blanket Order", "print_hide": 1}, {"depends_on": "eval:doc.against_blanket_order", "fieldname": "blanket_order_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Blanket Order Rate", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"default": "0", "fieldname": "against_blanket_order", "fieldtype": "Check", "label": "Against Blanket Order", "no_copy": 1, "print_hide": 1}, {"fieldname": "section_break_43", "fieldtype": "Section Break"}, {"fieldname": "valuation_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Valuation Rate", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "report_hide": 1}, {"fieldname": "column_break_45", "fieldtype": "Column Break"}, {"fieldname": "gross_profit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Gross Profit", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "report_hide": 1}, {"depends_on": "eval: doc.uom != doc.stock_uom", "fieldname": "stock_uom_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate of Stock UOM", "no_copy": 1, "options": "currency", "read_only": 1}, {"default": "0", "fieldname": "is_alternative", "fieldtype": "Check", "label": "Is Alternative", "print_hide": 1}, {"default": "0", "fieldname": "has_alternative_item", "fieldtype": "Check", "hidden": 1, "label": "Has Alternative Item", "print_hide": 1, "read_only": 1}], "idx": 1, "istable": 1, "links": [], "modified": "2023-11-14 18:24:24.619832", "modified_by": "Administrator", "module": "Selling", "name": "Quotation Item", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}