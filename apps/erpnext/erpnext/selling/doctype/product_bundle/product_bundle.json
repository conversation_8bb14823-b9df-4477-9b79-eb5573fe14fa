{"actions": [], "allow_import": 1, "creation": "2013-06-20 11:53:21", "description": "Aggregate a group of Items into another Item. This is useful if you are maintaining the stock of the packed items and not the bundled item", "doctype": "DocType", "engine": "InnoDB", "field_order": ["basic_section", "new_item_code", "description", "column_break_eonk", "disabled", "item_section", "items", "section_break_4", "about"], "fields": [{"fieldname": "basic_section", "fieldtype": "Section Break"}, {"fieldname": "new_item_code", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "label": "<PERSON><PERSON>", "no_copy": 1, "oldfieldname": "new_item_code", "oldfieldtype": "Data", "options": "<PERSON><PERSON>", "reqd": 1}, {"fieldname": "description", "fieldtype": "Data", "in_list_view": 1, "label": "Description"}, {"description": "List items that form the package.", "fieldname": "item_section", "fieldtype": "Section Break", "label": "Items"}, {"fieldname": "items", "fieldtype": "Table", "label": "Items", "oldfieldname": "sales_bom_items", "oldfieldtype": "Table", "options": "Product Bundle Item", "reqd": 1}, {"fieldname": "section_break_4", "fieldtype": "Section Break"}, {"fieldname": "about", "fieldtype": "HTML", "options": "<h3>About Product Bundle</h3>\n\n<p>Aggregate group of <b>Items</b> into another <b>Item</b>. This is useful if you are bundling a certain <b>Items</b> into a package and you maintain stock of the packed <b>Items</b> and not the aggregate <b>Item</b>.</p>\n<p>The package <b>Item</b> will have <code>Is Stock Item</code> as <b>No</b> and <code>Is Sales Item</code> as <b>Yes</b>.</p>\n<h4>Example:</h4>\n<p>If you are selling Laptops and Backpacks separately and have a special price if the customer buys both, then the Laptop + Backpack will be a new Product Bundle Item.</p>"}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"fieldname": "column_break_eonk", "fieldtype": "Column Break"}], "icon": "fa fa-sitemap", "idx": 1, "links": [], "modified": "2024-01-30 13:57:04.951788", "modified_by": "Administrator", "module": "Selling", "name": "Product Bundle", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "share": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User"}, {"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "ASC", "states": []}