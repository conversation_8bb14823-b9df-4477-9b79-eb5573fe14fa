[{"customer_group": "_Test Customer Group", "customer_name": "_Test Customer With Template", "customer_type": "Individual", "doctype": "Customer", "territory": "_Test Territory"}, {"customer_group": "_Test Customer Group", "customer_name": "_Test Customer P", "customer_type": "Individual", "doctype": "Customer", "territory": "_Test Territory"}, {"customer_group": "_Test Customer Group", "customer_name": "_Test Customer", "customer_type": "Individual", "doctype": "Customer", "territory": "_Test Territory"}, {"customer_group": "_Test Customer Group", "customer_name": "_Test Customer 1", "customer_type": "Individual", "doctype": "Customer", "territory": "_Test Territory"}, {"customer_group": "_Test Customer Group", "customer_name": "_Test Customer 2", "customer_type": "Individual", "doctype": "Customer", "territory": "_Test Territory"}, {"customer_group": "_Test Customer Group", "customer_name": "_Test Customer 3", "customer_type": "Individual", "doctype": "Customer", "territory": "_Test Territory"}, {"customer_group": "_Test Customer Group", "customer_name": "_Test Customer USD", "customer_type": "Individual", "doctype": "Customer", "territory": "_Test Territory", "accounts": [{"company": "_Test Company", "account": "_Test Receivable USD - _TC"}]}, {"customer_group": "_Test Customer Group", "customer_name": "_Test Customer With Tax Category", "customer_type": "Individual", "doctype": "Customer", "territory": "_Test Territory", "tax_category": "_Test Tax Category 1"}]