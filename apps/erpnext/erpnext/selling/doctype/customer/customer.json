{"actions": [], "allow_events_in_timeline": 1, "allow_import": 1, "allow_rename": 1, "autoname": "naming_series:", "creation": "2013-06-11 14:26:44", "description": "Buyer of Goods and Services.", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["basic_info", "naming_series", "salutation", "customer_name", "customer_type", "customer_group", "column_break0", "territory", "gender", "lead_name", "opportunity_name", "account_manager", "image", "defaults_tab", "default_currency", "default_bank_account", "column_break_14", "default_price_list", "internal_customer_section", "is_internal_customer", "represents_company", "column_break_70", "companies", "more_info", "market_segment", "industry", "customer_pos_id", "website", "language", "column_break_45", "customer_details", "dashboard_tab", "contact_and_address_tab", "address_contacts", "address_html", "column_break1", "contact_html", "primary_address_and_contact_detail", "column_break_26", "customer_primary_address", "primary_address", "column_break_nwor", "customer_primary_contact", "mobile_no", "email_id", "tax_tab", "taxation_section", "tax_id", "column_break_21", "tax_category", "tax_withholding_category", "accounting_tab", "credit_limit_section", "payment_terms", "credit_limits", "default_receivable_accounts", "accounts", "loyalty_points_tab", "loyalty_program", "column_break_54", "loyalty_program_tier", "sales_team_tab", "sales_team", "sales_team_section", "default_sales_partner", "column_break_66", "default_commission_rate", "settings_tab", "so_required", "dn_required", "column_break_53", "is_frozen", "disabled", "portal_users_tab", "portal_users"], "fields": [{"fieldname": "basic_info", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "fa fa-user"}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "options": "CUST-.YYYY.-", "set_only_once": 1}, {"depends_on": "eval:doc.customer_type!='Company'", "fieldname": "salutation", "fieldtype": "Link", "label": "Salutation", "options": "Salutation"}, {"bold": 1, "fieldname": "customer_name", "fieldtype": "Data", "in_global_search": 1, "label": "Customer Name", "no_copy": 1, "oldfieldname": "customer_name", "oldfieldtype": "Data", "reqd": 1, "search_index": 1}, {"depends_on": "eval:doc.customer_type != 'Company'", "fieldname": "gender", "fieldtype": "Link", "label": "Gender", "options": "Gender"}, {"default": "Company", "fieldname": "customer_type", "fieldtype": "Select", "label": "Customer Type", "oldfieldname": "customer_type", "oldfieldtype": "Select", "options": "Company\nIndividual\nProprietorship\nPartnership", "reqd": 1}, {"fieldname": "default_bank_account", "fieldtype": "Link", "label": "Default Company Bank Account", "options": "Bank Account"}, {"fieldname": "lead_name", "fieldtype": "Link", "label": "From Lead", "no_copy": 1, "oldfieldname": "lead_name", "oldfieldtype": "Link", "options": "Lead", "print_hide": 1, "report_hide": 1}, {"fieldname": "image", "fieldtype": "Attach Image", "hidden": 1, "label": "Image", "print_hide": 1}, {"fieldname": "column_break0", "fieldtype": "Column Break", "width": "50%"}, {"fieldname": "account_manager", "fieldtype": "Link", "label": "Account Manager", "options": "User"}, {"fieldname": "customer_group", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Customer Group", "oldfieldname": "customer_group", "oldfieldtype": "Link", "options": "Customer Group", "search_index": 1}, {"fieldname": "territory", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Territory", "oldfieldname": "territory", "oldfieldtype": "Link", "options": "Territory", "print_hide": 1}, {"fieldname": "tax_id", "fieldtype": "Data", "label": "Tax ID"}, {"fieldname": "tax_category", "fieldtype": "Link", "label": "Tax Category", "options": "Tax Category"}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"default": "0", "fieldname": "is_internal_customer", "fieldtype": "Check", "label": "Is Internal Customer"}, {"depends_on": "is_internal_customer", "fieldname": "represents_company", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Represents Company", "options": "Company", "unique": 1}, {"depends_on": "represents_company", "fieldname": "companies", "fieldtype": "Table", "label": "Allowed To Transact With", "options": "Allowed To Transact With"}, {"fieldname": "default_currency", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Billing <PERSON><PERSON><PERSON>cy", "no_copy": 1, "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "default_price_list", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Default Price List", "options": "Price List"}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"fieldname": "language", "fieldtype": "Link", "label": "Print Language", "options": "Language"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "address_contacts", "fieldtype": "Section Break", "label": "Address and Contact", "options": "fa fa-map-marker"}, {"depends_on": "eval: !doc.__islocal", "fieldname": "address_html", "fieldtype": "HTML", "label": "Address HTML", "read_only": 1}, {"fieldname": "website", "fieldtype": "Data", "label": "Website"}, {"fieldname": "column_break1", "fieldtype": "Column Break", "width": "50%"}, {"depends_on": "eval: !doc.__islocal", "fieldname": "contact_html", "fieldtype": "HTML", "label": "Contact HTML", "oldfieldtype": "HTML", "read_only": 1}, {"description": "Select, to make the customer searchable with these fields", "fieldname": "primary_address_and_contact_detail", "fieldtype": "Section Break", "label": "Primary Address and Contact"}, {"description": "Reselect, if the chosen contact is edited after save", "fieldname": "customer_primary_contact", "fieldtype": "Link", "label": "Customer Primary Contact", "options": "Contact"}, {"fetch_from": "customer_primary_contact.mobile_no", "fieldname": "mobile_no", "fieldtype": "Read Only", "label": "Mobile No"}, {"fetch_from": "customer_primary_contact.email_id", "fieldname": "email_id", "fieldtype": "Read Only", "label": "Email <PERSON>d"}, {"fieldname": "column_break_26", "fieldtype": "Column Break"}, {"description": "Reselect, if the chosen address is edited after save", "fieldname": "customer_primary_address", "fieldtype": "Link", "label": "Customer Primary Address", "options": "Address"}, {"fieldname": "primary_address", "fieldtype": "Text", "label": "Primary Address", "read_only": 1}, {"fieldname": "default_receivable_accounts", "fieldtype": "Section Break", "label": "Default Accounts"}, {"description": "Mention if non-standard Receivable account", "fieldname": "accounts", "fieldtype": "Table", "label": "Accounts", "options": "Party Account"}, {"fieldname": "credit_limit_section", "fieldtype": "Section Break", "label": "Credit Limit and Payment Terms"}, {"fieldname": "payment_terms", "fieldtype": "Link", "label": "Default Payment Terms Template", "options": "Payment Terms Template"}, {"collapsible": 1, "collapsible_depends_on": "customer_details", "fieldname": "more_info", "fieldtype": "Section Break", "label": "More Information", "oldfieldtype": "Section Break", "options": "fa fa-file-text"}, {"description": "Additional information regarding the customer.", "fieldname": "customer_details", "fieldtype": "Text", "label": "Customer Details", "oldfieldname": "customer_details", "oldfieldtype": "Code"}, {"fieldname": "column_break_45", "fieldtype": "Column Break"}, {"fieldname": "market_segment", "fieldtype": "Link", "label": "Market Segment", "options": "Market Segment"}, {"fieldname": "industry", "fieldtype": "Link", "label": "Industry", "options": "Industry Type"}, {"default": "0", "fieldname": "is_frozen", "fieldtype": "Check", "label": "Is Frozen"}, {"fieldname": "loyalty_program", "fieldtype": "Link", "label": "Loyalty Program", "no_copy": 1, "options": "Loyalty Program"}, {"fieldname": "loyalty_program_tier", "fieldtype": "Data", "label": "Loyalty Program Tier", "no_copy": 1, "read_only": 1}, {"fieldname": "default_sales_partner", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Sales Partner", "oldfieldname": "default_sales_partner", "oldfieldtype": "Link", "options": "Sales Partner"}, {"fieldname": "default_commission_rate", "fieldtype": "Float", "label": "Commission Rate", "oldfieldname": "default_commission_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"collapsible": 1, "collapsible_depends_on": "sales_team", "fieldname": "sales_team_section", "fieldtype": "Section Break"}, {"fieldname": "sales_team", "fieldtype": "Table", "label": "Sales Team", "oldfieldname": "sales_team", "oldfieldtype": "Table", "options": "Sales Team"}, {"fieldname": "customer_pos_id", "fieldtype": "Data", "label": "Customer POS id", "no_copy": 1, "print_hide": 1, "read_only": 1, "report_hide": 1}, {"fieldname": "credit_limits", "fieldtype": "Table", "label": "Credit Limit", "options": "Customer Credit Limit"}, {"default": "0", "fieldname": "so_required", "fieldtype": "Check", "label": "Allow Sales Invoice Creation Without Sales Order"}, {"default": "0", "fieldname": "dn_required", "fieldtype": "Check", "label": "Allow Sales Invoice Creation Without Delivery Note"}, {"fieldname": "tax_withholding_category", "fieldtype": "Link", "label": "Tax Withholding Category", "options": "Tax Withholding Category"}, {"fieldname": "opportunity_name", "fieldtype": "Link", "label": "From Opportunity", "no_copy": 1, "options": "Opportunity", "print_hide": 1}, {"fieldname": "contact_and_address_tab", "fieldtype": "Tab Break", "label": "Contact & Address"}, {"fieldname": "defaults_tab", "fieldtype": "Section Break", "label": "De<PERSON>ults"}, {"fieldname": "settings_tab", "fieldtype": "Tab Break", "label": "Settings"}, {"collapsible": 1, "collapsible_depends_on": "default_sales_partner", "fieldname": "sales_team_tab", "fieldtype": "Tab Break", "label": "Sales Team", "oldfieldtype": "Section Break", "options": "fa fa-group"}, {"fieldname": "column_break_66", "fieldtype": "Column Break"}, {"fieldname": "column_break_21", "fieldtype": "Column Break"}, {"fieldname": "dashboard_tab", "fieldtype": "Tab Break", "label": "Dashboard", "show_dashboard": 1}, {"fieldname": "column_break_53", "fieldtype": "Column Break"}, {"collapsible": 1, "fieldname": "loyalty_points_tab", "fieldtype": "Section Break", "label": "Loyalty Points"}, {"fieldname": "taxation_section", "fieldtype": "Section Break"}, {"fieldname": "accounting_tab", "fieldtype": "Tab Break", "label": "Accounting"}, {"fieldname": "tax_tab", "fieldtype": "Tab Break", "label": "Tax"}, {"collapsible": 1, "collapsible_depends_on": "is_internal_customer", "fieldname": "internal_customer_section", "fieldtype": "Section Break", "label": "Internal Customer"}, {"fieldname": "column_break_70", "fieldtype": "Column Break"}, {"fieldname": "column_break_54", "fieldtype": "Column Break"}, {"fieldname": "portal_users_tab", "fieldtype": "Tab Break", "label": "Portal Users"}, {"fieldname": "portal_users", "fieldtype": "Table", "label": "Customer Portal Users", "options": "Portal User"}, {"fieldname": "column_break_nwor", "fieldtype": "Column Break"}], "icon": "fa fa-user", "idx": 363, "image_field": "image", "index_web_pages_for_search": 1, "links": [{"group": "Allowed Items", "link_doctype": "Party Specific Item", "link_fieldname": "party"}], "modified": "2023-12-28 13:15:36.298369", "modified_by": "Administrator", "module": "Selling", "name": "Customer", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"permlevel": 1, "read": 1, "role": "Sales User"}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager"}, {"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Master Manager", "share": 1, "write": 1}, {"permlevel": 1, "read": 1, "role": "Sales Master Manager", "write": 1}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User"}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager"}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User"}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager"}], "quick_entry": 1, "search_fields": "customer_name,customer_group,territory, mobile_no,primary_address", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "customer_name", "track_changes": 1}