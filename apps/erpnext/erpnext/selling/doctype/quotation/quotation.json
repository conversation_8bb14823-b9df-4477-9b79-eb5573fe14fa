{"actions": [], "allow_auto_repeat": 1, "allow_import": 1, "autoname": "naming_series:", "creation": "2013-05-24 19:29:08", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["customer_section", "title", "naming_series", "quotation_to", "party_name", "customer_name", "column_break_7", "transaction_date", "valid_till", "column_break1", "order_type", "company", "amended_from", "currency_and_price_list", "currency", "conversion_rate", "column_break2", "selling_price_list", "price_list_currency", "plc_conversion_rate", "ignore_pricing_rule", "items_section", "scan_barcode", "items", "sec_break23", "total_qty", "total_net_weight", "column_break_28", "base_total", "base_net_total", "column_break_31", "total", "net_total", "taxes_section", "tax_category", "taxes_and_charges", "column_break_36", "shipping_rule", "column_break_34", "incoterm", "named_place", "section_break_36", "taxes", "section_break_39", "base_total_taxes_and_charges", "column_break_42", "total_taxes_and_charges", "totals", "base_grand_total", "base_rounding_adjustment", "base_rounded_total", "base_in_words", "column_break3", "grand_total", "rounding_adjustment", "rounded_total", "in_words", "section_break_44", "apply_discount_on", "base_discount_amount", "coupon_code", "column_break_46", "additional_discount_percentage", "discount_amount", "referral_sales_partner", "sec_tax_breakup", "other_charges_calculation", "bundle_items_section", "packed_items", "pricing_rule_details", "pricing_rules", "address_and_contact_tab", "billing_address_section", "customer_address", "address_display", "col_break98", "contact_person", "contact_display", "contact_mobile", "contact_email", "shipping_address_section", "shipping_address_name", "column_break_81", "shipping_address", "company_address_section", "company_address", "column_break_87", "company_address_display", "terms_tab", "payment_schedule_section", "payment_terms_template", "payment_schedule", "terms_section_break", "tc_name", "terms", "more_info_tab", "subscription_section", "auto_repeat", "update_auto_repeat_reference", "print_settings", "letter_head", "group_same_items", "column_break_73", "select_print_heading", "language", "lost_reasons_section", "lost_reasons", "competitors", "column_break_117", "order_lost_reason", "additional_info_section", "status", "customer_group", "territory", "column_break_108", "campaign", "source", "column_break4", "opportunity", "supplier_quotation", "enq_det", "connections_tab"], "fields": [{"fieldname": "customer_section", "fieldtype": "Section Break", "options": "fa fa-user"}, {"allow_on_submit": 1, "default": "{customer_name}", "fieldname": "title", "fieldtype": "Data", "hidden": 1, "label": "Title", "no_copy": 1, "print_hide": 1}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "oldfieldname": "naming_series", "oldfieldtype": "Select", "options": "SAL-QTN-.YYYY.-", "print_hide": 1, "reqd": 1, "set_only_once": 1}, {"default": "Customer", "fieldname": "quotation_to", "fieldtype": "Link", "in_standard_filter": 1, "label": "Quotation To", "oldfieldname": "quotation_to", "oldfieldtype": "Select", "options": "DocType", "print_hide": 1, "reqd": 1}, {"bold": 1, "fieldname": "party_name", "fieldtype": "Dynamic Link", "in_global_search": 1, "in_standard_filter": 1, "label": "Party", "oldfieldname": "customer", "oldfieldtype": "Link", "options": "quotation_to", "print_hide": 1, "search_index": 1}, {"bold": 1, "fieldname": "customer_name", "fieldtype": "Data", "hidden": 1, "in_global_search": 1, "label": "Customer Name", "read_only": 1}, {"fieldname": "column_break1", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "width": "50%"}, {"fieldname": "amended_from", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "oldfieldname": "amended_from", "oldfieldtype": "Data", "options": "Quotation", "print_hide": 1, "read_only": 1, "width": "150px"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "print_hide": 1, "remember_last_selected_value": 1, "reqd": 1, "width": "150px"}, {"default": "Today", "fieldname": "transaction_date", "fieldtype": "Date", "in_list_view": 1, "in_standard_filter": 1, "label": "Date", "no_copy": 1, "oldfieldname": "transaction_date", "oldfieldtype": "Date", "reqd": 1, "search_index": 1, "width": "100px"}, {"fieldname": "valid_till", "fieldtype": "Date", "label": "<PERSON><PERSON>"}, {"default": "Sales", "fieldname": "order_type", "fieldtype": "Select", "in_standard_filter": 1, "label": "Order Type", "oldfieldname": "order_type", "oldfieldtype": "Select", "options": "\nSales\nMaintenance\nShopping Cart", "print_hide": 1, "reqd": 1}, {"fieldname": "customer_address", "fieldtype": "Link", "label": "Customer Address", "options": "Address", "print_hide": 1}, {"fieldname": "address_display", "fieldtype": "Small Text", "label": "Address", "oldfieldname": "customer_address", "oldfieldtype": "Small Text", "read_only": 1}, {"fieldname": "contact_person", "fieldtype": "Link", "label": "Contact Person", "oldfieldname": "contact_person", "oldfieldtype": "Link", "options": "Contact", "print_hide": 1}, {"fieldname": "contact_display", "fieldtype": "Small Text", "in_global_search": 1, "label": "Contact", "read_only": 1}, {"fieldname": "contact_mobile", "fieldtype": "Small Text", "label": "Mobile No", "options": "Phone", "read_only": 1}, {"fieldname": "contact_email", "fieldtype": "Data", "hidden": 1, "label": "Contact Email", "options": "Email", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:(doc.quotation_to=='Customer' && doc.party_name)", "fieldname": "col_break98", "fieldtype": "Column Break", "width": "50%"}, {"fieldname": "shipping_address_name", "fieldtype": "Link", "label": "Shipping Address", "options": "Address", "print_hide": 1}, {"fieldname": "shipping_address", "fieldtype": "Small Text", "label": "Shipping Address", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:(doc.quotation_to=='Customer' && doc.party_name)", "fieldname": "customer_group", "fieldtype": "Link", "hidden": 1, "label": "Customer Group", "oldfieldname": "customer_group", "oldfieldtype": "Link", "options": "Customer Group", "print_hide": 1}, {"fieldname": "territory", "fieldtype": "Link", "label": "Territory", "options": "Territory", "print_hide": 1}, {"collapsible": 1, "fieldname": "currency_and_price_list", "fieldtype": "Section Break", "label": "Currency and Price List", "options": "fa fa-tag"}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "oldfieldname": "currency", "oldfieldtype": "Select", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "reqd": 1, "width": "100px"}, {"description": "Rate at which customer's currency is converted to company's base currency", "fieldname": "conversion_rate", "fieldtype": "Float", "label": "Exchange Rate", "oldfieldname": "conversion_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "precision": "9", "print_hide": 1, "reqd": 1, "width": "100px"}, {"fieldname": "column_break2", "fieldtype": "Column Break", "width": "50%"}, {"fieldname": "selling_price_list", "fieldtype": "Link", "label": "Price List", "oldfieldname": "price_list_name", "oldfieldtype": "Select", "options": "Price List", "print_hide": 1, "reqd": 1, "width": "100px"}, {"fieldname": "price_list_currency", "fieldtype": "Link", "label": "Price List Currency", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1, "reqd": 1}, {"description": "Rate at which Price list currency is converted to company's base currency", "fieldname": "plc_conversion_rate", "fieldtype": "Float", "label": "Price List Exchange Rate", "precision": "9", "print_hide": 1, "reqd": 1}, {"default": "0", "fieldname": "ignore_pricing_rule", "fieldtype": "Check", "label": "Ignore Pricing Rule", "permlevel": 1, "print_hide": 1}, {"fieldname": "items_section", "fieldtype": "Section Break", "hide_border": 1, "oldfieldtype": "Section Break", "options": "fa fa-shopping-cart"}, {"allow_bulk_edit": 1, "fieldname": "items", "fieldtype": "Table", "label": "Items", "oldfieldname": "quotation_details", "oldfieldtype": "Table", "options": "Quotation Item", "reqd": 1, "width": "40px"}, {"collapsible": 1, "fieldname": "pricing_rule_details", "fieldtype": "Section Break", "label": "Pricing Rules"}, {"fieldname": "pricing_rules", "fieldtype": "Table", "label": "Pricing Rule Detail", "options": "Pricing Rule Detail", "read_only": 1}, {"fieldname": "sec_break23", "fieldtype": "Section Break"}, {"fieldname": "total_qty", "fieldtype": "Float", "label": "Total Quantity", "read_only": 1}, {"fieldname": "base_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total (Company Currency)", "oldfieldname": "net_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "width": "100px"}, {"fieldname": "column_break_28", "fieldtype": "Column Break"}, {"fieldname": "total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total", "options": "currency", "read_only": 1}, {"fieldname": "net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total", "options": "currency", "print_hide": 1, "read_only": 1}, {"depends_on": "total_net_weight", "fieldname": "total_net_weight", "fieldtype": "Float", "label": "Total Net Weight", "print_hide": 1, "read_only": 1}, {"fieldname": "taxes_section", "fieldtype": "Section Break", "hide_border": 1, "label": "Taxes and Charges", "oldfieldtype": "Section Break", "options": "fa fa-money"}, {"fieldname": "tax_category", "fieldtype": "Link", "label": "Tax Category", "options": "Tax Category", "print_hide": 1}, {"fieldname": "column_break_34", "fieldtype": "Column Break"}, {"fieldname": "shipping_rule", "fieldtype": "Link", "label": "Shipping Rule", "oldfieldtype": "<PERSON><PERSON>", "options": "Shipping Rule", "print_hide": 1}, {"fieldname": "section_break_36", "fieldtype": "Section Break", "hide_border": 1}, {"fieldname": "taxes_and_charges", "fieldtype": "Link", "label": "Sales Taxes and Charges Template", "oldfieldname": "charge", "oldfieldtype": "Link", "options": "Sales Taxes and Charges Template", "print_hide": 1}, {"fieldname": "taxes", "fieldtype": "Table", "label": "Sales Taxes and Charges", "oldfieldname": "other_charges", "oldfieldtype": "Table", "options": "Sales Taxes and Charges"}, {"collapsible": 1, "fieldname": "sec_tax_breakup", "fieldtype": "Section Break", "label": "Tax Breakup"}, {"fieldname": "other_charges_calculation", "fieldtype": "Long Text", "label": "Taxes and Charges Calculation", "no_copy": 1, "oldfieldtype": "HTML", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_39", "fieldtype": "Section Break"}, {"fieldname": "base_total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Taxes and Charges (Company Currency)", "oldfieldname": "other_charges_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_42", "fieldtype": "Column Break"}, {"fieldname": "total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Taxes and Charges", "options": "currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "section_break_44", "fieldtype": "Section Break", "label": "Additional Discount"}, {"fieldname": "coupon_code", "fieldtype": "Link", "label": "Coupon Code", "options": "Coupon Code"}, {"fieldname": "referral_sales_partner", "fieldtype": "Link", "label": "Referral Sales Partner", "options": "Sales Partner"}, {"default": "Grand Total", "fieldname": "apply_discount_on", "fieldtype": "Select", "label": "Apply Additional Discount On", "options": "\nGrand Total\nNet Total", "print_hide": 1}, {"fieldname": "base_discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Discount Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_46", "fieldtype": "Column Break"}, {"fieldname": "additional_discount_percentage", "fieldtype": "Float", "label": "Additional Discount Percentage", "print_hide": 1}, {"fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Discount Amount", "options": "currency", "print_hide": 1}, {"fieldname": "totals", "fieldtype": "Section Break", "label": "Totals", "oldfieldtype": "Section Break", "options": "fa fa-money", "print_hide": 1}, {"fieldname": "base_grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Grand Total (Company Currency)", "oldfieldname": "grand_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "width": "200px"}, {"fieldname": "base_rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounding Adjustment (Company Currency)", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_in_words", "fieldtype": "Data", "label": "In Words (Company Currency)", "length": 240, "oldfieldname": "in_words", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1}, {"fieldname": "base_rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounded Total (Company Currency)", "oldfieldname": "rounded_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "width": "200px"}, {"fieldname": "column_break3", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "print_hide": 1, "width": "50%"}, {"fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Grand Total", "oldfieldname": "grand_total_export", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1, "width": "200px"}, {"fieldname": "rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounding Adjustment", "no_copy": 1, "options": "currency", "print_hide": 1, "read_only": 1}, {"bold": 1, "fieldname": "rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounded Total", "oldfieldname": "rounded_total_export", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1, "width": "200px"}, {"fieldname": "in_words", "fieldtype": "Data", "label": "In Words", "length": 240, "oldfieldname": "in_words_export", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1, "width": "200px"}, {"fieldname": "payment_schedule_section", "fieldtype": "Section Break", "label": "Payment Terms"}, {"fieldname": "payment_terms_template", "fieldtype": "Link", "label": "Payment Terms Template", "options": "Payment Terms Template", "print_hide": 1}, {"fieldname": "payment_schedule", "fieldtype": "Table", "label": "Payment Schedule", "no_copy": 1, "options": "Payment Schedule", "print_hide": 1}, {"collapsible_depends_on": "terms", "fieldname": "terms_section_break", "fieldtype": "Section Break", "label": "Terms and Conditions", "oldfieldtype": "Section Break", "options": "fa fa-legal"}, {"fieldname": "tc_name", "fieldtype": "Link", "label": "Terms", "oldfieldname": "tc_name", "oldfieldtype": "Link", "options": "Terms and Conditions", "print_hide": 1, "report_hide": 1}, {"fieldname": "terms", "fieldtype": "Text Editor", "label": "Term Details", "oldfieldname": "terms", "oldfieldtype": "Text Editor"}, {"collapsible": 1, "fieldname": "print_settings", "fieldtype": "Section Break", "label": "Print Settings"}, {"allow_on_submit": 1, "fieldname": "letter_head", "fieldtype": "Link", "label": "Letter Head", "oldfieldname": "letter_head", "oldfieldtype": "Select", "options": "Letter Head", "print_hide": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "group_same_items", "fieldtype": "Check", "label": "Group same items", "print_hide": 1}, {"fieldname": "column_break_73", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "fieldname": "select_print_heading", "fieldtype": "Link", "label": "Print Heading", "no_copy": 1, "oldfieldname": "select_print_heading", "oldfieldtype": "Link", "options": "Print Heading", "print_hide": 1, "report_hide": 1}, {"fieldname": "language", "fieldtype": "Data", "label": "Print Language", "print_hide": 1, "read_only": 1}, {"fieldname": "subscription_section", "fieldtype": "Section Break", "label": "Auto Repeat"}, {"fieldname": "auto_repeat", "fieldtype": "Link", "label": "Auto Repeat", "no_copy": 1, "options": "Auto Repeat", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "depends_on": "eval:doc.auto_repeat", "fieldname": "update_auto_repeat_reference", "fieldtype": "<PERSON><PERSON>", "label": "Update Auto Repeat Reference"}, {"fieldname": "campaign", "fieldtype": "Link", "label": "Campaign", "oldfieldname": "campaign", "oldfieldtype": "Link", "options": "Campaign", "print_hide": 1}, {"fieldname": "source", "fieldtype": "Link", "label": "Source", "oldfieldname": "source", "oldfieldtype": "Select", "options": "Lead Source", "print_hide": 1}, {"allow_on_submit": 1, "depends_on": "eval:doc.status=='Lost'", "fieldname": "order_lost_reason", "fieldtype": "Small Text", "label": "Detailed Reason", "no_copy": 1, "oldfieldname": "order_lost_reason", "oldfieldtype": "Small Text", "print_hide": 1}, {"fieldname": "column_break4", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "print_hide": 1, "width": "50%"}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Select", "options": "Draft\nOpen\nReplied\nPartially Ordered\nOrdered\nLost\nCancelled\nExpired", "print_hide": 1, "read_only": 1, "reqd": 1}, {"fieldname": "enq_det", "fieldtype": "Text", "hidden": 1, "label": "Opportunity Item", "oldfieldname": "enq_det", "oldfieldtype": "Text", "print_hide": 1, "read_only": 1}, {"fieldname": "supplier_quotation", "fieldtype": "Link", "label": "Supplier Quotation", "options": "Supplier Quotation"}, {"fieldname": "opportunity", "fieldtype": "Link", "label": "Opportunity", "options": "Opportunity", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "fieldname": "lost_reasons", "fieldtype": "Table MultiSelect", "label": "Lost Reasons", "options": "Quotation Lost Reason Detail", "read_only": 1}, {"fieldname": "packed_items", "fieldtype": "Table", "label": "Bundle Items", "options": "Packed Item", "print_hide": 1}, {"collapsible": 1, "depends_on": "packed_items", "fieldname": "bundle_items_section", "fieldtype": "Section Break", "label": "Bundle Items", "options": "fa fa-suitcase", "print_hide": 1}, {"allow_on_submit": 1, "fieldname": "competitors", "fieldtype": "Table MultiSelect", "label": "Competitors", "options": "Competitor Detail"}, {"fieldname": "company_address", "fieldtype": "Link", "label": "Company Address Name", "options": "Address"}, {"fieldname": "company_address_display", "fieldtype": "Small Text", "label": "Company Address", "read_only": 1}, {"fieldname": "scan_barcode", "fieldtype": "Data", "label": "Scan Barcode", "options": "Barcode"}, {"fieldname": "address_and_contact_tab", "fieldtype": "Tab Break", "label": "Address & Contact"}, {"fieldname": "terms_tab", "fieldtype": "Tab Break", "label": "Terms"}, {"fieldname": "more_info_tab", "fieldtype": "Tab Break", "label": "More Info"}, {"fieldname": "connections_tab", "fieldtype": "Tab Break", "label": "Connections", "show_dashboard": 1}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fieldname": "column_break_31", "fieldtype": "Column Break"}, {"fieldname": "column_break_36", "fieldtype": "Column Break"}, {"fieldname": "billing_address_section", "fieldtype": "Section Break", "label": "Billing Address", "options": "fa fa-bullhorn"}, {"fieldname": "shipping_address_section", "fieldtype": "Section Break", "label": "Shipping Address"}, {"fieldname": "column_break_81", "fieldtype": "Column Break"}, {"fieldname": "company_address_section", "fieldtype": "Section Break", "label": "Company Address"}, {"fieldname": "column_break_87", "fieldtype": "Column Break"}, {"collapsible": 1, "depends_on": "eval:(doc.lost_reasons || doc.order_lost_reason)", "fieldname": "lost_reasons_section", "fieldtype": "Section Break", "label": "Lost Reasons"}, {"fieldname": "column_break_117", "fieldtype": "Column Break"}, {"collapsible": 1, "fieldname": "additional_info_section", "fieldtype": "Section Break", "label": "Additional Info", "oldfieldtype": "Section Break", "options": "fa fa-file-text", "print_hide": 1}, {"fieldname": "column_break_108", "fieldtype": "Column Break"}, {"fieldname": "incoterm", "fieldtype": "Link", "label": "Incoterm", "options": "Incoterm"}, {"depends_on": "incoterm", "fieldname": "named_place", "fieldtype": "Data", "label": "Named Place"}], "icon": "fa fa-shopping-cart", "idx": 82, "is_submittable": 1, "links": [], "modified": "2023-06-03 16:21:04.980033", "modified_by": "Administrator", "module": "Selling", "name": "Quotation", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "submit": 1, "write": 1}, {"permlevel": 1, "read": 1, "report": 1, "role": "Sales User"}, {"permlevel": 1, "read": 1, "report": 1, "role": "Sales Manager", "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Maintenance Manager", "share": 1, "submit": 1, "write": 1}, {"permlevel": 1, "read": 1, "report": 1, "role": "Maintenance Manager"}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Maintenance User", "share": 1, "submit": 1, "write": 1}, {"permlevel": 1, "read": 1, "report": 1, "role": "Maintenance User"}], "search_fields": "status,transaction_date,party_name,order_type", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "timeline_field": "party_name", "title_field": "title"}