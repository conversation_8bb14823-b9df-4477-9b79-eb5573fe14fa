{"actions": [], "creation": "2013-05-23 16:55:51", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "qty", "description", "rate", "uom"], "fields": [{"fieldname": "item_code", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "label": "<PERSON><PERSON>", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "reqd": 1}, {"fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Qty", "oldfieldname": "qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "reqd": 1}, {"fetch_from": "item_code.description", "fetch_if_empty": 1, "fieldname": "description", "fieldtype": "Text Editor", "in_list_view": 1, "label": "Description", "oldfieldname": "description", "oldfieldtype": "Text", "print_width": "300px"}, {"fieldname": "rate", "fieldtype": "Float", "hidden": 1, "label": "Rate", "oldfieldname": "rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1}, {"fetch_from": "item_code.stock_uom", "fetch_if_empty": 1, "fieldname": "uom", "fieldtype": "Link", "in_list_view": 1, "label": "UOM", "oldfieldname": "uom", "oldfieldtype": "Link", "options": "UOM", "read_only": 1}], "idx": 1, "istable": 1, "links": [], "modified": "2022-06-27 05:30:18.475150", "modified_by": "Administrator", "module": "Selling", "name": "Product Bundle Item", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}