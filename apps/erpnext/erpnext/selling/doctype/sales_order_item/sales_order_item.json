{"actions": [], "autoname": "hash", "creation": "2013-03-07 11:42:58", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "customer_item_code", "ensure_delivery_based_on_produced_serial_no", "is_stock_item", "reserve_stock", "col_break1", "delivery_date", "item_name", "section_break_5", "description", "item_group", "brand", "image_section", "image", "image_view", "quantity_and_rate", "qty", "stock_uom", "col_break2", "uom", "conversion_factor", "stock_qty", "stock_reserved_qty", "section_break_16", "price_list_rate", "base_price_list_rate", "discount_and_margin", "margin_type", "margin_rate_or_amount", "rate_with_margin", "column_break_19", "discount_percentage", "discount_amount", "base_rate_with_margin", "section_break_simple1", "rate", "amount", "item_tax_template", "col_break3", "base_rate", "base_amount", "pricing_rules", "stock_uom_rate", "is_free_item", "grant_commission", "section_break_24", "net_rate", "net_amount", "column_break_27", "base_net_rate", "base_net_amount", "billed_amt", "valuation_rate", "gross_profit", "drop_ship_section", "delivered_by_supplier", "supplier", "item_weight_details", "weight_per_unit", "total_weight", "column_break_21", "weight_uom", "warehouse_and_reference", "warehouse", "target_warehouse", "prevdoc_docname", "quotation_item", "col_break4", "against_blanket_order", "blanket_order", "blanket_order_rate", "manufacturing_section_section", "bom_no", "planning_section", "projected_qty", "actual_qty", "ordered_qty", "planned_qty", "production_plan_qty", "column_break_69", "work_order_qty", "delivered_qty", "produced_qty", "returned_qty", "picked_qty", "shopping_cart_section", "additional_notes", "section_break_63", "page_break", "item_tax_rate", "transaction_date", "inter_transfer_reference_section", "material_request", "purchase_order", "column_break_89", "material_request_item", "purchase_order_item"], "fields": [{"bold": 1, "columns": 3, "fieldname": "item_code", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "print_width": "150px", "reqd": 1, "width": "150px"}, {"fieldname": "customer_item_code", "fieldtype": "Data", "hidden": 1, "label": "Customer's Item Code", "print_hide": 1, "read_only": 1}, {"default": "0", "fieldname": "ensure_delivery_based_on_produced_serial_no", "fieldtype": "Check", "label": "Ensure Delivery Based on Produced Serial No"}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data", "print_hide": 1, "print_width": "150", "reqd": 1, "width": "150"}, {"collapsible": 1, "fieldname": "section_break_5", "fieldtype": "Section Break", "label": "Description"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Small Text", "print_width": "300px", "width": "300px"}, {"allow_on_submit": 1, "columns": 2, "depends_on": "eval: !parent.skip_delivery_note", "fieldname": "delivery_date", "fieldtype": "Date", "in_list_view": 1, "label": "Delivery Date", "no_copy": 1, "print_hide": 1}, {"fetch_from": "item_code.image", "fieldname": "image", "fieldtype": "Attach", "hidden": 1, "label": "Image"}, {"fieldname": "image_view", "fieldtype": "Image", "label": "Image View", "options": "image", "print_hide": 1}, {"fieldname": "quantity_and_rate", "fieldtype": "Section Break", "label": "Quantity and Rate"}, {"columns": 1, "fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity", "non_negative": 1, "oldfieldname": "qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_width": "100px", "reqd": 1, "width": "100px"}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "oldfieldname": "stock_uom", "oldfieldtype": "Data", "options": "UOM", "print_width": "70px", "read_only": 1, "width": "70px"}, {"fieldname": "col_break2", "fieldtype": "Column Break"}, {"fieldname": "uom", "fieldtype": "Link", "label": "UOM", "options": "UOM", "reqd": 1}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "conversion_factor", "fieldtype": "Float", "label": "UOM Conversion Factor", "print_hide": 1, "read_only": 1, "reqd": 1}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "stock_qty", "fieldtype": "Float", "label": "Qty as per Stock UOM", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_16", "fieldtype": "Section Break"}, {"fieldname": "price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price List Rate", "oldfieldname": "ref_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "print_width": "70px", "read_only": 1, "width": "70px"}, {"fieldname": "base_price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price List Rate (Company Currency)", "oldfieldname": "base_ref_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"collapsible": 1, "collapsible_depends_on": "eval: doc.margin_type || doc.discount_amount", "fieldname": "discount_and_margin", "fieldtype": "Section Break", "label": "Discount and Margin"}, {"depends_on": "price_list_rate", "fieldname": "margin_type", "fieldtype": "Select", "label": "Margin Type", "options": "\nPercentage\nAmount", "print_hide": 1}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate", "fieldname": "margin_rate_or_amount", "fieldtype": "Float", "label": "Margin Rate or Amount", "print_hide": 1}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate && doc.margin_rate_or_amount", "fieldname": "rate_with_margin", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate With Margin", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_19", "fieldtype": "Column Break"}, {"depends_on": "price_list_rate", "fieldname": "discount_percentage", "fieldtype": "Percent", "label": "Discount (%) on Price List Rate with <PERSON><PERSON>", "oldfieldname": "adj_rate", "oldfieldtype": "Float", "print_hide": 1, "print_width": "70px", "width": "70px"}, {"depends_on": "price_list_rate", "fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Discount Amount", "options": "currency"}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate && doc.margin_rate_or_amount", "fieldname": "base_rate_with_margin", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate With Margin (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_simple1", "fieldtype": "Section Break", "precision": "2"}, {"columns": 2, "depends_on": "eval: doc.type != \"\"", "fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "oldfieldname": "export_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_width": "100px", "width": "100px"}, {"columns": 2, "fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "oldfieldname": "export_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "col_break3", "fieldtype": "Column Break"}, {"fieldname": "base_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Basic Rate (Company Currency)", "oldfieldname": "basic_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "base_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount (Company Currency)", "oldfieldname": "amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "pricing_rules", "fieldtype": "Small Text", "hidden": 1, "label": "Pricing Rules", "print_hide": 1, "read_only": 1}, {"default": "0", "fieldname": "is_free_item", "fieldtype": "Check", "label": "Is Free Item", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_24", "fieldtype": "Section Break"}, {"fieldname": "net_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Rate", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Amount", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_27", "fieldtype": "Column Break"}, {"fieldname": "base_net_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Rate (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "eval:doc.delivered_by_supplier==1||doc.supplier", "fieldname": "drop_ship_section", "fieldtype": "Section Break", "label": "Drop Ship", "print_hide": 1}, {"default": "0", "fieldname": "delivered_by_supplier", "fieldtype": "Check", "label": "Supplier delivers to Customer", "print_hide": 1}, {"allow_on_submit": 1, "fieldname": "supplier", "fieldtype": "Link", "label": "Supplier", "options": "Supplier", "print_hide": 1}, {"collapsible": 1, "fieldname": "item_weight_details", "fieldtype": "Section Break", "label": "Item Weight Details"}, {"fieldname": "weight_per_unit", "fieldtype": "Float", "label": "Weight Per Unit"}, {"fieldname": "total_weight", "fieldtype": "Float", "label": "Total Weight", "read_only": 1}, {"fieldname": "column_break_21", "fieldtype": "Column Break", "print_hide": 1}, {"fieldname": "weight_uom", "fieldtype": "Link", "label": "Weight UOM", "options": "UOM"}, {"fieldname": "warehouse_and_reference", "fieldtype": "Section Break", "label": "Warehouse and Reference"}, {"depends_on": "eval:doc.delivered_by_supplier!=1", "fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Delivery Warehouse", "oldfieldname": "reserved_warehouse", "oldfieldtype": "Link", "options": "Warehouse", "print_hide": 1, "print_width": "150px", "width": "150px"}, {"depends_on": "eval:doc.delivered_by_supplier!=1", "fieldname": "target_warehouse", "fieldtype": "Link", "hidden": 1, "ignore_user_permissions": 1, "label": "Customer Warehouse (Optional)", "no_copy": 1, "options": "Warehouse", "print_hide": 1}, {"fieldname": "prevdoc_docname", "fieldtype": "Link", "label": "Quotation", "no_copy": 1, "oldfieldname": "prevdoc_docname", "oldfieldtype": "Link", "options": "Quotation", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "brand", "fieldtype": "Link", "hidden": 1, "label": "Brand Name", "oldfieldname": "brand", "oldfieldtype": "Link", "options": "Brand", "print_hide": 1, "read_only": 1}, {"fieldname": "item_group", "fieldtype": "Link", "hidden": 1, "label": "Item Group", "oldfieldname": "item_group", "oldfieldtype": "Link", "options": "Item Group", "print_hide": 1, "read_only": 1}, {"fieldname": "billed_amt", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON>", "no_copy": 1, "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "valuation_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Valuation Rate", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "report_hide": 1}, {"fieldname": "gross_profit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Gross Profit", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "report_hide": 1}, {"depends_on": "eval:doc.against_blanket_order", "fieldname": "blanket_order", "fieldtype": "Link", "label": "Blanket Order", "no_copy": 1, "options": "Blanket Order"}, {"depends_on": "eval:doc.against_blanket_order", "fieldname": "blanket_order_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Blanket Order Rate", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "col_break4", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "fieldname": "projected_qty", "fieldtype": "Float", "label": "Projected Qty", "no_copy": 1, "oldfieldname": "projected_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "print_width": "70px", "read_only": 1, "width": "70px"}, {"allow_on_submit": 1, "fieldname": "actual_qty", "fieldtype": "Float", "label": "Actual Qty", "no_copy": 1, "print_hide": 1, "print_width": "70px", "read_only": 1, "width": "70px"}, {"fieldname": "ordered_qty", "fieldtype": "Float", "label": "Ordered Qty", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "delivered_qty", "fieldtype": "Float", "label": "Delivered <PERSON><PERSON>", "no_copy": 1, "oldfieldname": "delivered_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "print_width": "100px", "read_only": 1, "width": "100px"}, {"fieldname": "work_order_qty", "fieldtype": "Float", "label": "Work Order Qty", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"depends_on": "returned_qty", "fieldname": "returned_qty", "fieldtype": "Float", "label": "Returned <PERSON><PERSON>", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_63", "fieldtype": "Section Break"}, {"fieldname": "item_tax_template", "fieldtype": "Link", "label": "Item Tax Template", "options": "Item Tax Template", "print_hide": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "page_break", "fieldtype": "Check", "label": "Page Break", "oldfieldname": "page_break", "oldfieldtype": "Check", "print_hide": 1, "report_hide": 1}, {"description": "For Production", "fieldname": "planned_qty", "fieldtype": "Float", "hidden": 1, "label": "Planned Quantity", "no_copy": 1, "oldfieldname": "planned_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "print_width": "50px", "read_only": 1, "report_hide": 1, "width": "50px"}, {"description": "For Production", "fieldname": "produced_qty", "fieldtype": "Float", "hidden": 1, "label": "Produced Quantity", "oldfieldname": "produced_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "print_width": "50px", "read_only": 1, "report_hide": 1, "width": "50px"}, {"fieldname": "item_tax_rate", "fieldtype": "Code", "hidden": 1, "label": "Item Tax Rate", "oldfieldname": "item_tax_rate", "oldfieldtype": "Small Text", "print_hide": 1, "read_only": 1, "report_hide": 1}, {"description": "Used for Production Plan", "fieldname": "transaction_date", "fieldtype": "Date", "hidden": 1, "label": "Sales Order Date", "oldfieldname": "transaction_date", "oldfieldtype": "Date", "print_hide": 1, "read_only": 1, "report_hide": 1}, {"fieldname": "planning_section", "fieldtype": "Section Break", "label": "Planning"}, {"fieldname": "column_break_69", "fieldtype": "Column Break"}, {"collapsible": 1, "fieldname": "image_section", "fieldtype": "Section Break", "label": "Image"}, {"collapsible": 1, "fieldname": "shopping_cart_section", "fieldtype": "Section Break", "label": "Shopping Cart"}, {"fieldname": "additional_notes", "fieldtype": "Text", "label": "Additional Notes"}, {"default": "0", "fieldname": "against_blanket_order", "fieldtype": "Check", "label": "Against Blanket Order"}, {"fieldname": "bom_no", "fieldtype": "Link", "label": "BOM No", "no_copy": 1, "options": "BOM", "print_hide": 1}, {"fieldname": "manufacturing_section_section", "fieldtype": "Section Break", "label": "Manufacturing Section"}, {"depends_on": "eval: doc.uom != doc.stock_uom", "fieldname": "stock_uom_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate of Stock UOM", "no_copy": 1, "options": "currency", "read_only": 1}, {"default": "0", "fetch_from": "item_code.grant_commission", "fieldname": "grant_commission", "fieldtype": "Check", "label": "Grant Commission", "read_only": 1}, {"fieldname": "picked_qty", "fieldtype": "Float", "label": "Picked <PERSON><PERSON> (in Stock UOM)", "no_copy": 1, "read_only": 1}, {"fieldname": "inter_transfer_reference_section", "fieldtype": "Section Break", "label": "Inter Transfer Reference"}, {"fieldname": "purchase_order", "fieldtype": "Link", "label": "Purchase Order", "options": "Purchase Order", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_89", "fieldtype": "Column Break"}, {"fieldname": "purchase_order_item", "fieldtype": "Data", "label": "Purchase Order Item", "print_hide": 1, "read_only": 1}, {"fieldname": "quotation_item", "fieldtype": "Data", "hidden": 1, "label": "quotation_item", "no_copy": 1, "read_only": 1}, {"fieldname": "material_request", "fieldtype": "Link", "label": "Material Request", "options": "Material Request"}, {"fieldname": "material_request_item", "fieldtype": "Data", "label": "Material Request Item"}, {"allow_on_submit": 1, "default": "1", "depends_on": "eval:doc.is_stock_item", "fieldname": "reserve_stock", "fieldtype": "Check", "label": "Reserve Stock", "print_hide": 1, "report_hide": 1}, {"default": "0", "depends_on": "eval: doc.stock_reserved_qty", "fieldname": "stock_reserved_qty", "fieldtype": "Float", "label": "Stock Reserved Qty (in Stock UOM)", "no_copy": 1, "non_negative": 1, "print_hide": 1, "read_only": 1, "report_hide": 1}, {"fieldname": "production_plan_qty", "fieldtype": "Float", "label": "Production Plan Qty", "no_copy": 1, "read_only": 1}, {"default": "0", "fetch_from": "item_code.is_stock_item", "fieldname": "is_stock_item", "fieldtype": "Check", "hidden": 1, "label": "Is Stock Item", "print_hide": 1, "report_hide": 1}], "idx": 1, "istable": 1, "links": [], "modified": "2024-01-25 14:24:00.330219", "modified_by": "Administrator", "module": "Selling", "name": "Sales Order Item", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}