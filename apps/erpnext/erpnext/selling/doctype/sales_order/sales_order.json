{"actions": [], "allow_auto_repeat": 1, "allow_import": 1, "autoname": "naming_series:", "creation": "2013-06-18 12:39:59", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["customer_section", "column_break0", "title", "naming_series", "customer", "customer_name", "tax_id", "order_type", "column_break_7", "transaction_date", "delivery_date", "column_break1", "po_no", "po_date", "company", "skip_delivery_note", "amended_from", "accounting_dimensions_section", "cost_center", "dimension_col_break", "project", "currency_and_price_list", "currency", "conversion_rate", "column_break2", "selling_price_list", "price_list_currency", "plc_conversion_rate", "ignore_pricing_rule", "sec_warehouse", "scan_barcode", "column_break_28", "set_warehouse", "reserve_stock", "items_section", "items", "section_break_31", "total_qty", "total_net_weight", "column_break_33", "base_total", "base_net_total", "column_break_33a", "total", "net_total", "taxes_section", "tax_category", "taxes_and_charges", "column_break_38", "shipping_rule", "column_break_49", "incoterm", "named_place", "section_break_40", "taxes", "section_break_43", "base_total_taxes_and_charges", "column_break_46", "total_taxes_and_charges", "totals", "base_grand_total", "base_rounding_adjustment", "base_rounded_total", "base_in_words", "column_break3", "grand_total", "rounding_adjustment", "rounded_total", "in_words", "advance_paid", "disable_rounded_total", "section_break_48", "apply_discount_on", "base_discount_amount", "coupon_code", "column_break_50", "additional_discount_percentage", "discount_amount", "sec_tax_breakup", "other_charges_calculation", "packing_list", "packed_items", "pricing_rule_details", "pricing_rules", "contact_info", "billing_address_column", "customer_address", "address_display", "customer_group", "territory", "column_break_84", "contact_person", "contact_display", "contact_phone", "contact_mobile", "contact_email", "shipping_address_column", "shipping_address_name", "shipping_address", "column_break_93", "dispatch_address_name", "dispatch_address", "col_break46", "company_address", "column_break_92", "company_address_display", "payment_schedule_section", "payment_terms_section", "payment_terms_template", "payment_schedule", "terms_section_break", "tc_name", "terms", "more_info", "section_break_78", "status", "delivery_status", "per_delivered", "column_break_81", "per_billed", "per_picked", "billing_status", "sales_team_section_break", "sales_partner", "column_break7", "amount_eligible_for_commission", "commission_rate", "total_commission", "section_break1", "sales_team", "loyalty_points_redemption", "loyalty_points", "column_break_116", "loyalty_amount", "subscription_section", "from_date", "to_date", "column_break_108", "auto_repeat", "update_auto_repeat_reference", "printing_details", "letter_head", "group_same_items", "column_break4", "select_print_heading", "language", "additional_info_section", "is_internal_customer", "represents_company", "column_break_152", "source", "inter_company_order_reference", "campaign", "party_account_currency", "connections_tab"], "fields": [{"fieldname": "customer_section", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "options": "fa fa-user"}, {"fieldname": "column_break0", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1, "oldfieldtype": "Column Break", "width": "50%"}, {"allow_on_submit": 1, "default": "{customer_name}", "fieldname": "title", "fieldtype": "Data", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "label": "Title", "no_copy": 1, "print_hide": 1}, {"fieldname": "naming_series", "fieldtype": "Select", "hide_days": 1, "hide_seconds": 1, "label": "Series", "no_copy": 1, "oldfieldname": "naming_series", "oldfieldtype": "Select", "options": "SAL-ORD-.YYYY.-", "print_hide": 1, "reqd": 1, "set_only_once": 1}, {"bold": 1, "fieldname": "customer", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "in_global_search": 1, "in_standard_filter": 1, "label": "Customer", "oldfieldname": "customer", "oldfieldtype": "Link", "options": "Customer", "print_hide": 1, "reqd": 1, "search_index": 1}, {"bold": 1, "fetch_from": "customer.customer_name", "fieldname": "customer_name", "fieldtype": "Data", "hide_days": 1, "hide_seconds": 1, "in_global_search": 1, "label": "Customer Name", "read_only": 1}, {"default": "Sales", "fieldname": "order_type", "fieldtype": "Select", "hide_days": 1, "hide_seconds": 1, "label": "Order Type", "oldfieldname": "order_type", "oldfieldtype": "Select", "options": "\nSales\nMaintenance\nShopping Cart", "print_hide": 1, "reqd": 1}, {"fieldname": "column_break1", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1, "oldfieldtype": "Column Break", "width": "50%"}, {"fieldname": "amended_from", "fieldtype": "Link", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "oldfieldname": "amended_from", "oldfieldtype": "Data", "options": "Sales Order", "print_hide": 1, "read_only": 1, "width": "150px"}, {"fieldname": "company", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "in_standard_filter": 1, "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "print_hide": 1, "remember_last_selected_value": 1, "reqd": 1, "width": "150px"}, {"default": "Today", "fieldname": "transaction_date", "fieldtype": "Date", "hide_days": 1, "hide_seconds": 1, "in_standard_filter": 1, "label": "Date", "no_copy": 1, "oldfieldname": "transaction_date", "oldfieldtype": "Date", "reqd": 1, "search_index": 1, "width": "160px"}, {"allow_on_submit": 1, "depends_on": "eval:!doc.skip_delivery_note", "fieldname": "delivery_date", "fieldtype": "Date", "hide_days": 1, "hide_seconds": 1, "in_list_view": 1, "label": "Delivery Date", "no_copy": 1}, {"allow_on_submit": 1, "fieldname": "po_no", "fieldtype": "Data", "hide_days": 1, "hide_seconds": 1, "label": "Customer's Purchase Order", "oldfieldname": "po_no", "oldfieldtype": "Data", "width": "100px"}, {"allow_on_submit": 1, "depends_on": "eval:doc.po_no", "fieldname": "po_date", "fieldtype": "Date", "hide_days": 1, "hide_seconds": 1, "label": "Customer's Purchase Order Date", "oldfieldname": "po_date", "oldfieldtype": "Date", "width": "100px"}, {"fetch_from": "customer.tax_id", "fieldname": "tax_id", "fieldtype": "Data", "hide_days": 1, "hide_seconds": 1, "label": "Tax Id", "read_only": 1, "width": "100px"}, {"collapsible": 1, "depends_on": "customer", "fieldname": "contact_info", "fieldtype": "Tab Break", "hide_days": 1, "hide_seconds": 1, "label": "Address & Contact", "options": "fa fa-bullhorn"}, {"allow_on_submit": 1, "fieldname": "customer_address", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Customer Address", "options": "Address", "print_hide": 1}, {"allow_on_submit": 1, "fieldname": "address_display", "fieldtype": "Small Text", "hide_days": 1, "hide_seconds": 1, "label": "Address", "read_only": 1}, {"fieldname": "contact_person", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Contact Person", "options": "Contact", "print_hide": 1}, {"fieldname": "contact_display", "fieldtype": "Small Text", "hide_days": 1, "hide_seconds": 1, "in_global_search": 1, "label": "Contact", "read_only": 1}, {"fieldname": "contact_mobile", "fieldtype": "Small Text", "hide_days": 1, "hide_seconds": 1, "label": "Mobile No", "options": "Phone", "read_only": 1}, {"fieldname": "contact_email", "fieldtype": "Data", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "label": "Contact Email", "options": "Email", "print_hide": 1, "read_only": 1}, {"fieldname": "company_address_display", "fieldtype": "Small Text", "hide_days": 1, "hide_seconds": 1, "label": "Company Address", "read_only": 1}, {"fieldname": "company_address", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Company Address Name", "options": "Address"}, {"fieldname": "col_break46", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Company Address", "width": "50%"}, {"allow_on_submit": 1, "fieldname": "shipping_address_name", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Shipping Address Name", "options": "Address", "print_hide": 1}, {"allow_on_submit": 1, "fieldname": "shipping_address", "fieldtype": "Small Text", "hide_days": 1, "hide_seconds": 1, "label": "Shipping Address", "print_hide": 1, "read_only": 1}, {"fieldname": "customer_group", "fieldtype": "Link", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "label": "Customer Group", "options": "Customer Group", "print_hide": 1}, {"fieldname": "territory", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Territory", "options": "Territory", "print_hide": 1}, {"collapsible": 1, "fieldname": "currency_and_price_list", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Currency and Price List", "options": "fa fa-tag", "print_hide": 1}, {"fieldname": "currency", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "<PERSON><PERSON><PERSON><PERSON>", "oldfieldname": "currency", "oldfieldtype": "Select", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "reqd": 1, "width": "100px"}, {"description": "Rate at which customer's currency is converted to company's base currency", "fieldname": "conversion_rate", "fieldtype": "Float", "hide_days": 1, "hide_seconds": 1, "label": "Exchange Rate", "oldfieldname": "conversion_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "precision": "9", "print_hide": 1, "reqd": 1, "width": "100px"}, {"fieldname": "column_break2", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1, "width": "50%"}, {"fieldname": "selling_price_list", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Price List", "oldfieldname": "price_list_name", "oldfieldtype": "Select", "options": "Price List", "print_hide": 1, "reqd": 1, "width": "100px"}, {"fieldname": "price_list_currency", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Price List Currency", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1, "reqd": 1}, {"description": "Rate at which Price list currency is converted to company's base currency", "fieldname": "plc_conversion_rate", "fieldtype": "Float", "hide_days": 1, "hide_seconds": 1, "label": "Price List Exchange Rate", "precision": "9", "print_hide": 1, "reqd": 1}, {"default": "0", "fieldname": "ignore_pricing_rule", "fieldtype": "Check", "hide_days": 1, "hide_seconds": 1, "label": "Ignore Pricing Rule", "permlevel": 1, "print_hide": 1}, {"fieldname": "sec_warehouse", "fieldtype": "Section Break", "hide_border": 1, "hide_days": 1, "hide_seconds": 1, "label": "Items"}, {"fieldname": "set_warehouse", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Set Source Warehouse", "options": "Warehouse", "print_hide": 1}, {"fieldname": "items_section", "fieldtype": "Section Break", "hide_border": 1, "hide_days": 1, "hide_seconds": 1, "oldfieldtype": "Section Break", "options": "fa fa-shopping-cart"}, {"fieldname": "scan_barcode", "fieldtype": "Data", "hide_days": 1, "hide_seconds": 1, "label": "Scan Barcode", "options": "Barcode"}, {"allow_bulk_edit": 1, "fieldname": "items", "fieldtype": "Table", "hide_days": 1, "hide_seconds": 1, "label": "Items", "oldfieldname": "sales_order_details", "oldfieldtype": "Table", "options": "Sales Order Item", "reqd": 1}, {"fieldname": "pricing_rule_details", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Pricing Rules"}, {"fieldname": "pricing_rules", "fieldtype": "Table", "hide_days": 1, "hide_seconds": 1, "label": "Pricing Rule Detail", "options": "Pricing Rule Detail", "read_only": 1}, {"fieldname": "section_break_31", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1}, {"fieldname": "column_break_33a", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"fieldname": "total_qty", "fieldtype": "Float", "hide_days": 1, "hide_seconds": 1, "label": "Total Quantity", "read_only": 1}, {"fieldname": "base_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Total (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Net Total (Company Currency)", "oldfieldname": "net_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "width": "150px"}, {"fieldname": "column_break_33", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"fieldname": "total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Total", "options": "currency", "read_only": 1}, {"fieldname": "net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Net Total", "options": "currency", "print_hide": 1, "read_only": 1}, {"depends_on": "total_net_weight", "fieldname": "total_net_weight", "fieldtype": "Float", "hide_days": 1, "hide_seconds": 1, "label": "Total Net Weight", "print_hide": 1, "read_only": 1}, {"fieldname": "taxes_section", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Taxes", "oldfieldtype": "Section Break", "options": "fa fa-money"}, {"fieldname": "tax_category", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Tax Category", "options": "Tax Category", "print_hide": 1}, {"fieldname": "column_break_38", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"fieldname": "shipping_rule", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Shipping Rule", "oldfieldtype": "<PERSON><PERSON>", "options": "Shipping Rule", "print_hide": 1}, {"fieldname": "section_break_40", "fieldtype": "Section Break", "hide_border": 1, "hide_days": 1, "hide_seconds": 1}, {"fieldname": "taxes_and_charges", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Sales Taxes and Charges Template", "oldfieldname": "charge", "oldfieldtype": "Link", "options": "Sales Taxes and Charges Template", "print_hide": 1}, {"fieldname": "taxes", "fieldtype": "Table", "hide_days": 1, "hide_seconds": 1, "label": "Sales Taxes and Charges", "oldfieldname": "other_charges", "oldfieldtype": "Table", "options": "Sales Taxes and Charges"}, {"collapsible": 1, "fieldname": "sec_tax_breakup", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Tax Breakup"}, {"fieldname": "other_charges_calculation", "fieldtype": "Long Text", "hide_days": 1, "hide_seconds": 1, "label": "Taxes and Charges Calculation", "no_copy": 1, "oldfieldtype": "HTML", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_43", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1}, {"fieldname": "base_total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Total Taxes and Charges (Company Currency)", "oldfieldname": "other_charges_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "width": "150px"}, {"fieldname": "column_break_46", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"fieldname": "total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Total Taxes and Charges", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "loyalty_points_redemption", "fieldtype": "Section Break", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "label": "Loyalty Points", "print_hide": 1}, {"fieldname": "loyalty_points", "fieldtype": "Int", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "label": "Loyalty Points", "read_only": 1}, {"fieldname": "loyalty_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "label": "Loyalty Amount", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "section_break_48", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Additional Discount"}, {"fieldname": "coupon_code", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Coupon Code", "options": "Coupon Code"}, {"default": "Grand Total", "fieldname": "apply_discount_on", "fieldtype": "Select", "hide_days": 1, "hide_seconds": 1, "label": "Apply Additional Discount On", "options": "\nGrand Total\nNet Total", "print_hide": 1}, {"fieldname": "base_discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Additional Discount Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_50", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"fieldname": "additional_discount_percentage", "fieldtype": "Float", "hide_days": 1, "hide_seconds": 1, "label": "Additional Discount Percentage", "print_hide": 1}, {"fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Additional Discount Amount", "options": "currency", "print_hide": 1}, {"fieldname": "totals", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Totals", "oldfieldtype": "Section Break", "options": "fa fa-money", "print_hide": 1}, {"fieldname": "base_grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Grand Total (Company Currency)", "oldfieldname": "grand_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "width": "150px"}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "base_rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Rounding Adjustment (Company Currency)", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "base_rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Rounded Total (Company Currency)", "oldfieldname": "rounded_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "width": "150px"}, {"description": "In Words will be visible once you save the Sales Order.", "fieldname": "base_in_words", "fieldtype": "Data", "hide_days": 1, "hide_seconds": 1, "label": "In Words (Company Currency)", "length": 240, "oldfieldname": "in_words", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1, "width": "200px"}, {"fieldname": "column_break3", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1, "oldfieldtype": "Column Break", "print_hide": 1, "width": "50%"}, {"fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "in_list_view": 1, "label": "Grand Total", "oldfieldname": "grand_total_export", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1, "width": "150px"}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Rounding Adjustment", "no_copy": 1, "options": "currency", "print_hide": 1, "read_only": 1}, {"bold": 1, "depends_on": "eval:!doc.disable_rounded_total", "fieldname": "rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Rounded Total", "oldfieldname": "rounded_total_export", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1, "width": "150px"}, {"fieldname": "in_words", "fieldtype": "Data", "hide_days": 1, "hide_seconds": 1, "label": "In Words", "length": 240, "oldfieldname": "in_words_export", "oldfieldtype": "Data", "read_only": 1, "width": "200px"}, {"fieldname": "advance_paid", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Advance Paid", "no_copy": 1, "options": "party_account_currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "packed_items", "depends_on": "packed_items", "fieldname": "packing_list", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Packing List", "oldfieldtype": "Section Break", "options": "fa fa-suitcase", "print_hide": 1}, {"depends_on": "packed_items", "fieldname": "packed_items", "fieldtype": "Table", "hide_days": 1, "hide_seconds": 1, "label": "Packed Items", "options": "Packed Item", "print_hide": 1}, {"fieldname": "payment_schedule_section", "fieldtype": "Tab Break", "hide_days": 1, "hide_seconds": 1, "label": "Terms"}, {"fieldname": "payment_terms_template", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Payment Terms Template", "options": "Payment Terms Template", "print_hide": 1}, {"fieldname": "payment_schedule", "fieldtype": "Table", "hide_days": 1, "hide_seconds": 1, "label": "Payment Schedule", "no_copy": 1, "options": "Payment Schedule", "print_hide": 1}, {"collapsible_depends_on": "terms", "fieldname": "terms_section_break", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Terms & Conditions", "oldfieldtype": "Section Break", "options": "fa fa-legal"}, {"fieldname": "tc_name", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Terms", "oldfieldname": "tc_name", "oldfieldtype": "Link", "options": "Terms and Conditions", "print_hide": 1}, {"fieldname": "terms", "fieldtype": "Text Editor", "hide_days": 1, "hide_seconds": 1, "label": "Terms and Conditions Details", "oldfieldname": "terms", "oldfieldtype": "Text Editor"}, {"collapsible": 1, "collapsible_depends_on": "project", "fieldname": "more_info", "fieldtype": "Tab Break", "hide_days": 1, "hide_seconds": 1, "label": "More Info", "oldfieldtype": "Section Break", "options": "fa fa-file-text", "print_hide": 1}, {"fieldname": "inter_company_order_reference", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Inter Company Order Reference", "options": "Purchase Order", "read_only": 1}, {"fieldname": "project", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Project", "oldfieldname": "project", "oldfieldtype": "Link", "options": "Project"}, {"fieldname": "party_account_currency", "fieldtype": "Link", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "label": "Party Account <PERSON><PERSON><PERSON><PERSON>", "no_copy": 1, "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"fieldname": "source", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Source", "oldfieldname": "source", "oldfieldtype": "Select", "options": "Lead Source", "print_hide": 1}, {"fieldname": "campaign", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Campaign", "oldfieldname": "campaign", "oldfieldtype": "Link", "options": "Campaign", "print_hide": 1}, {"collapsible": 1, "fieldname": "printing_details", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Print Settings"}, {"fieldname": "language", "fieldtype": "Data", "hide_days": 1, "hide_seconds": 1, "label": "Print Language", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "fieldname": "letter_head", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Letter Head", "oldfieldname": "letter_head", "oldfieldtype": "Select", "options": "Letter Head", "print_hide": 1}, {"fieldname": "column_break4", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1, "oldfieldtype": "Column Break", "print_hide": 1, "width": "50%"}, {"allow_on_submit": 1, "fieldname": "select_print_heading", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Print Heading", "no_copy": 1, "oldfieldname": "select_print_heading", "oldfieldtype": "Link", "options": "Print Heading", "print_hide": 1, "report_hide": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "group_same_items", "fieldtype": "Check", "hide_days": 1, "hide_seconds": 1, "label": "Group same items", "print_hide": 1}, {"collapsible": 1, "fieldname": "section_break_78", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Status", "oldfieldtype": "Column Break", "print_hide": 1, "width": "50%"}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "hide_days": 1, "hide_seconds": 1, "in_list_view": 1, "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Select", "options": "\nDraft\nOn Hold\nTo Deliver and <PERSON>\nTo Bill\nTo Deliver\nCompleted\nCancelled\nClosed", "print_hide": 1, "read_only": 1, "reqd": 1, "search_index": 1, "width": "100px"}, {"fieldname": "delivery_status", "fieldtype": "Select", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "in_standard_filter": 1, "label": "Delivery Status", "no_copy": 1, "options": "Not Delivered\nFully Delivered\nPartly Delivered\nClosed\nNot Applicable", "print_hide": 1}, {"depends_on": "eval:!doc.__islocal && !doc.skip_delivery_note_creation", "description": "% of materials delivered against this Sales Order", "fieldname": "per_delivered", "fieldtype": "Percent", "hide_days": 1, "hide_seconds": 1, "in_list_view": 1, "label": "%  Delivered", "no_copy": 1, "oldfieldname": "per_delivered", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1, "width": "100px"}, {"fieldname": "column_break_81", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"depends_on": "eval:!doc.__islocal", "description": "% of materials billed against this Sales Order", "fieldname": "per_billed", "fieldtype": "Percent", "hide_days": 1, "hide_seconds": 1, "in_list_view": 1, "label": "% Amount Billed", "no_copy": 1, "oldfieldname": "per_billed", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1, "width": "100px"}, {"fieldname": "billing_status", "fieldtype": "Select", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "in_standard_filter": 1, "label": "Billing Status", "no_copy": 1, "options": "Not Billed\nFully Billed\nPartly Billed\nClosed", "print_hide": 1}, {"collapsible": 1, "collapsible_depends_on": "commission_rate", "fieldname": "sales_team_section_break", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Commission", "oldfieldtype": "Section Break", "options": "fa fa-group", "print_hide": 1}, {"fieldname": "sales_partner", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Sales Partner", "oldfieldname": "sales_partner", "oldfieldtype": "Link", "options": "Sales Partner", "print_hide": 1, "width": "150px"}, {"fieldname": "column_break7", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1, "print_hide": 1, "width": "50%"}, {"fetch_from": "sales_partner.commission_rate", "fetch_if_empty": 1, "fieldname": "commission_rate", "fieldtype": "Float", "hide_days": 1, "hide_seconds": 1, "label": "Commission Rate", "oldfieldname": "commission_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "width": "100px"}, {"fieldname": "total_commission", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Total Commission", "oldfieldname": "total_commission", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1}, {"collapsible": 1, "collapsible_depends_on": "sales_team", "fieldname": "section_break1", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Sales Team", "print_hide": 1}, {"allow_on_submit": 1, "fieldname": "sales_team", "fieldtype": "Table", "hide_days": 1, "hide_seconds": 1, "label": "Sales Team", "oldfieldname": "sales_team", "oldfieldtype": "Table", "options": "Sales Team", "print_hide": 1}, {"allow_on_submit": 1, "collapsible": 1, "fieldname": "subscription_section", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Auto Repeat", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "fieldname": "from_date", "fieldtype": "Date", "hide_days": 1, "hide_seconds": 1, "label": "From Date", "no_copy": 1}, {"allow_on_submit": 1, "fieldname": "to_date", "fieldtype": "Date", "hide_days": 1, "hide_seconds": 1, "label": "To Date", "no_copy": 1}, {"fieldname": "column_break_108", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"fieldname": "auto_repeat", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Auto Repeat", "options": "Auto Repeat"}, {"allow_on_submit": 1, "depends_on": "eval: doc.auto_repeat", "fieldname": "update_auto_repeat_reference", "fieldtype": "<PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Update Auto Repeat Reference"}, {"fieldname": "contact_phone", "fieldtype": "Data", "hide_days": 1, "hide_seconds": 1, "label": "Phone", "options": "Phone", "read_only": 1}, {"default": "0", "fieldname": "skip_delivery_note", "fieldtype": "Check", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "label": "Skip Delivery Note", "print_hide": 1}, {"default": "0", "fetch_from": "customer.is_internal_customer", "fieldname": "is_internal_customer", "fieldtype": "Check", "label": "Is Internal Customer", "read_only": 1}, {"fetch_from": "customer.represents_company", "fieldname": "represents_company", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Represents Company", "options": "Company", "read_only": 1}, {"default": "0", "depends_on": "grand_total", "fieldname": "disable_rounded_total", "fieldtype": "Check", "label": "Disable Rounded Total"}, {"allow_on_submit": 1, "fieldname": "dispatch_address_name", "fieldtype": "Link", "label": "Dispatch Address Name", "options": "Address", "print_hide": 1}, {"allow_on_submit": 1, "depends_on": "dispatch_address_name", "fieldname": "dispatch_address", "fieldtype": "Small Text", "label": "Dispatch Address", "read_only": 1}, {"fieldname": "amount_eligible_for_commission", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount Eligible for Commission", "read_only": 1}, {"fieldname": "per_picked", "fieldtype": "Percent", "label": "% Picked", "no_copy": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fieldname": "column_break_28", "fieldtype": "Column Break"}, {"fieldname": "column_break_49", "fieldtype": "Column Break"}, {"fieldname": "connections_tab", "fieldtype": "Tab Break", "label": "Connections", "show_dashboard": 1}, {"fieldname": "payment_terms_section", "fieldtype": "Section Break", "label": "Payment Terms"}, {"fieldname": "column_break_116", "fieldtype": "Column Break"}, {"fieldname": "billing_address_column", "fieldtype": "Section Break", "label": "Billing Address"}, {"fieldname": "shipping_address_column", "fieldtype": "Section Break", "label": "Shipping Address"}, {"fieldname": "column_break_93", "fieldtype": "Column Break"}, {"fieldname": "column_break_84", "fieldtype": "Column Break"}, {"fieldname": "column_break_92", "fieldtype": "Column Break"}, {"collapsible": 1, "fieldname": "additional_info_section", "fieldtype": "Section Break", "label": "Additional Info"}, {"fieldname": "column_break_152", "fieldtype": "Column Break"}, {"fieldname": "incoterm", "fieldtype": "Link", "label": "Incoterm", "options": "Incoterm"}, {"depends_on": "incoterm", "fieldname": "named_place", "fieldtype": "Data", "label": "Named Place"}, {"default": "0", "depends_on": "eval: (doc.docstatus == 0 || doc.reserve_stock)", "description": "If checked, Stock will be reserved on <b>Submit</b>", "fieldname": "reserve_stock", "fieldtype": "Check", "label": "Reserve Stock", "no_copy": 1, "print_hide": 1, "report_hide": 1}], "icon": "fa fa-file-text", "idx": 105, "is_submittable": 1, "links": [], "modified": "2023-10-18 12:41:54.813462", "modified_by": "Administrator", "module": "Selling", "name": "Sales Order", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Maintenance User", "share": 1, "submit": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "role": "Accounts User"}, {"read": 1, "report": 1, "role": "Stock User"}, {"permlevel": 1, "read": 1, "role": "Sales Manager", "write": 1}], "search_fields": "status,transaction_date,customer,customer_name, territory,order_type,company", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "timeline_field": "customer", "title_field": "customer_name", "track_changes": 1, "track_seen": 1}