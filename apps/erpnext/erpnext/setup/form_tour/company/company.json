{"creation": "2021-11-24 10:17:18.534917", "docstatus": 0, "doctype": "Form Tour", "first_document": 1, "idx": 0, "include_name_field": 0, "is_standard": 1, "modified": "2021-11-24 15:38:21.026582", "modified_by": "Administrator", "module": "Setup", "name": "Company", "owner": "Administrator", "reference_doctype": "Company", "save_on_complete": 0, "steps": [{"description": "This is the default currency for this company.", "field": "", "fieldname": "default_currency", "fieldtype": "Link", "has_next_condition": 0, "is_table_field": 0, "label": "<PERSON><PERSON><PERSON>", "parent_field": "", "position": "Right", "title": "<PERSON><PERSON><PERSON>"}, {"description": "Here, you can add multiple addresses of the company", "field": "", "fieldname": "company_info", "fieldtype": "Section Break", "has_next_condition": 0, "is_table_field": 0, "label": "Address & Contact", "parent_field": "", "position": "Top", "title": "Address & Contact"}, {"description": "Here, you can set default Accounts, which will ease the creation of accounting entries.", "field": "", "fieldname": "default_settings", "fieldtype": "Section Break", "has_next_condition": 0, "is_table_field": 0, "label": "Accounts Set<PERSON>s", "parent_field": "", "position": "Top", "title": "Accounts Set<PERSON>s"}, {"description": "This setting is recommended if you wish to track the real-time stock balance in your books of account. This will allow the creation of a General Ledger entry for every stock transaction.", "field": "", "fieldname": "enable_perpetual_inventory", "fieldtype": "Check", "has_next_condition": 0, "is_table_field": 0, "label": "Enable Perpetual Inventory", "parent_field": "", "position": "Right", "title": "Enable Perpetual Inventory"}], "title": "Company"}