{"charts": [], "content": "[{\"id\":\"NO5yYHJopc\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Your Shortcuts\\n\\t\\t\\t\\n\\t\\t\\n\\t\\t\\t\\n\\t\\t\\n\\t\\t\\t\\n\\t\\t</b></span>\",\"col\":12}},{\"id\":\"CDxIM-WuZ9\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"System Settings\",\"col\":3}},{\"id\":\"-Uh7DKJNJX\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Accounts Settings\",\"col\":3}},{\"id\":\"K9ST9xcDXh\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Stock Settings\",\"col\":3}},{\"id\":\"27IdVHVQMb\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Selling Settings\",\"col\":3}},{\"id\":\"Rwp5zff88b\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Buying Settings\",\"col\":3}},{\"id\":\"hkfnQ2sevf\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Global Defaults\",\"col\":3}},{\"id\":\"jjxI_PDawD\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Print Settings\",\"col\":3}},{\"id\":\"R3CoYYFXye\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"yynbm1J_VO\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Settings</b></span>\",\"col\":12}},{\"id\":\"KDCv2MvSg3\",\"type\":\"card\",\"data\":{\"card_name\":\"Module Settings\",\"col\":4}},{\"id\":\"Q0_bqT7cxQ\",\"type\":\"card\",\"data\":{\"card_name\":\"Email / Notifications\",\"col\":4}},{\"id\":\"UnqK5haBnh\",\"type\":\"card\",\"data\":{\"card_name\":\"Website\",\"col\":4}},{\"id\":\"kp7u1H5hCd\",\"type\":\"card\",\"data\":{\"card_name\":\"Core\",\"col\":4}},{\"id\":\"Ufc3jycgy9\",\"type\":\"card\",\"data\":{\"card_name\":\"Printing\",\"col\":4}},{\"id\":\"89bSNzv3Yh\",\"type\":\"card\",\"data\":{\"card_name\":\"Workflow\",\"col\":4}}]", "creation": "2022-01-27 13:14:47.349433", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "setting", "idx": 0, "is_hidden": 0, "label": "ERPNext Settings", "links": [{"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Import Data", "link_count": 0, "link_to": "Data Import", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Export Data", "link_count": 0, "link_to": "Data Export", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Bulk Update", "link_count": 0, "link_to": "Bulk Update", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Download Backups", "link_count": 0, "link_to": "backups", "link_type": "Page", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Deleted Documents", "link_count": 0, "link_to": "Deleted Document", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Email / Notifications", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON> Account", "link_count": 0, "link_to": "<PERSON><PERSON> Account", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Email Domain", "link_count": 0, "link_to": "Email Domain", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Notification", "link_count": 0, "link_to": "Notification", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "<PERSON>ail Te<PERSON>late", "link_count": 0, "link_to": "<PERSON>ail Te<PERSON>late", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Auto Email Report", "link_count": 0, "link_to": "Auto Email Report", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Newsletter", "link_count": 0, "link_to": "Newsletter", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Notification Settings", "link_count": 0, "link_to": "Notification Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Website", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Website Settings", "link_count": 0, "link_to": "Website Settings", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Website Theme", "link_count": 0, "link_to": "Website Theme", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Website Script", "link_count": 0, "link_to": "Website Script", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "About Us Settings", "link_count": 0, "link_to": "About Us Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Contact Us Settings", "link_count": 0, "link_to": "Contact Us Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Printing", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Print Format Builder", "link_count": 0, "link_to": "print-format-builder", "link_type": "Page", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Print Settings", "link_count": 0, "link_to": "Print Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Print Format", "link_count": 0, "link_to": "Print Format", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Print Style", "link_count": 0, "link_to": "Print Style", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Workflow", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Workflow", "link_count": 0, "link_to": "Workflow", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Workflow State", "link_count": 0, "link_to": "Workflow State", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Workflow Action", "link_count": 0, "link_to": "Workflow Action", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Core", "link_count": 3, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "System Settings", "link_count": 0, "link_to": "System Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Domain Settings", "link_count": 0, "link_to": "Domain Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Global Defaults", "link_count": 0, "link_to": "Global Defaults", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON><PERSON>", "link_count": 8, "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Accounts Set<PERSON>s", "link_count": 0, "link_to": "Accounts Set<PERSON>s", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Stock Settings", "link_count": 0, "link_to": "Stock Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Selling <PERSON>", "link_count": 0, "link_to": "Selling <PERSON>", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Buying Settings", "link_count": 0, "link_to": "Buying Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Manufacturing Settings", "link_count": 0, "link_to": "Manufacturing Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "CRM Settings", "link_count": 0, "link_to": "CRM Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Projects Settings", "link_count": 0, "link_to": "Projects Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Support Settings", "link_count": 0, "link_to": "Support Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}], "modified": "2023-05-24 14:47:25.356531", "modified_by": "Administrator", "module": "Setup", "name": "ERPNext Settings", "number_cards": [], "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [], "restrict_to_domain": "", "roles": [], "sequence_id": 19.0, "shortcuts": [{"color": "Grey", "doc_view": "List", "label": "Print Settings", "link_to": "Print Settings", "type": "DocType"}, {"color": "Grey", "doc_view": "List", "label": "System Settings", "link_to": "System Settings", "type": "DocType"}, {"icon": "accounting", "label": "Accounts Set<PERSON>s", "link_to": "Accounts Set<PERSON>s", "type": "DocType"}, {"color": "Grey", "doc_view": "List", "label": "Global Defaults", "link_to": "Global Defaults", "type": "DocType"}, {"icon": "stock", "label": "Stock Settings", "link_to": "Stock Settings", "type": "DocType"}, {"icon": "sell", "label": "Selling <PERSON>", "link_to": "Selling <PERSON>", "type": "DocType"}, {"icon": "buying", "label": "Buying Settings", "link_to": "Buying Settings", "type": "DocType"}], "title": "ERPNext Settings"}