{"actions": [], "allow_import": 1, "autoname": "field:order_lost_reason", "creation": "2013-01-10 16:34:24", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["order_lost_reason"], "fields": [{"fieldname": "order_lost_reason", "fieldtype": "Data", "in_list_view": 1, "label": "Quotation Lost Reason", "oldfieldname": "order_lost_reason", "oldfieldtype": "Data", "reqd": 1, "unique": 1}], "icon": "fa fa-flag", "idx": 1, "links": [{"is_child_table": 1, "link_doctype": "Quotation Lost Reason Detail", "link_fieldname": "lost_reason", "parent_doctype": "Quotation", "table_fieldname": "lost_reasons"}], "modified": "2023-11-23 19:31:02.743353", "modified_by": "Administrator", "module": "Setup", "name": "Quotation Lost Reason", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Master Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}