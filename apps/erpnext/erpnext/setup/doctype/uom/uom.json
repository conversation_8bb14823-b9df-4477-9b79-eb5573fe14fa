{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:uom_name", "creation": "2013-01-10 16:34:24", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["enabled", "uom_name", "must_be_whole_number"], "fields": [{"fieldname": "uom_name", "fieldtype": "Data", "in_list_view": 1, "label": "UOM Name", "oldfieldname": "uom_name", "oldfieldtype": "Data", "reqd": 1, "unique": 1}, {"default": "0", "description": "Check this to disallow fractions. (for Nos)", "fieldname": "must_be_whole_number", "fieldtype": "Check", "label": "Must be Whole Number"}, {"default": "1", "fieldname": "enabled", "fieldtype": "Check", "label": "Enabled"}], "icon": "fa fa-compass", "idx": 1, "links": [], "modified": "2021-10-18 14:07:43.722144", "modified_by": "Administrator", "module": "Setup", "name": "UOM", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "<PERSON>em Manager", "share": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager"}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User"}], "quick_entry": 1, "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "ASC"}