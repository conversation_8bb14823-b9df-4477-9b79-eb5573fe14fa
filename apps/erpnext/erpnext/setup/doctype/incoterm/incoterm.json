{"actions": [], "allow_rename": 1, "autoname": "field:code", "creation": "2022-11-17 15:17:34.717467", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["code", "title", "description"], "fields": [{"fieldname": "code", "fieldtype": "Data", "in_list_view": 1, "label": "Code", "length": 3, "reqd": 1, "unique": 1}, {"fieldname": "title", "fieldtype": "Data", "in_list_view": 1, "label": "Title", "reqd": 1}, {"fieldname": "description", "fieldtype": "Long Text", "label": "Description"}], "links": [{"group": "Selling", "link_doctype": "Quotation", "link_fieldname": "incoterm"}, {"group": "Selling", "link_doctype": "Sales Order", "link_fieldname": "incoterm"}, {"group": "Buying", "link_doctype": "Request for Quotation", "link_fieldname": "incoterm"}, {"group": "Buying", "link_doctype": "Supplier Quotation", "link_fieldname": "incoterm"}, {"group": "Buying", "link_doctype": "Purchase Order", "link_fieldname": "incoterm"}, {"group": "Stock", "link_doctype": "Delivery Note", "link_fieldname": "incoterm"}, {"group": "Stock", "link_doctype": "Purchase Receipt", "link_fieldname": "incoterm"}, {"group": "Stock", "link_doctype": "Shipment", "link_fieldname": "incoterm"}, {"group": "Accounts", "link_doctype": "Sales Invoice", "link_fieldname": "incoterm"}, {"group": "Accounts", "link_doctype": "Purchase Invoice", "link_fieldname": "incoterm"}], "modified": "2022-11-17 22:35:52.084553", "modified_by": "Administrator", "module": "Setup", "name": "Incoterm", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "share": 1, "write": 1}, {"read": 1, "role": "Purchase User"}, {"read": 1, "role": "Sales User"}, {"read": 1, "role": "Accounts User"}, {"read": 1, "role": "Stock User"}], "show_title_field_in_link": 1, "sort_field": "name", "sort_order": "ASC", "states": [], "title_field": "title", "translated_doctype": 1}