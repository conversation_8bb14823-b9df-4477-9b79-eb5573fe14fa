[{"doctype": "Item Group", "is_group": 0, "item_group_name": "_Test Item Group", "parent_item_group": "All Item Groups", "item_group_defaults": [{"company": "_Test Company", "buying_cost_center": "_Test Cost Center 2 - _TC", "selling_cost_center": "_Test Cost Center 2 - _TC", "default_warehouse": "_Test Warehouse - _TC"}]}, {"doctype": "Item Group", "is_group": 0, "item_group_name": "_Test Item Group Desktops", "parent_item_group": "All Item Groups"}, {"doctype": "Item Group", "is_group": 1, "item_group_name": "_Test Item Group A", "parent_item_group": "All Item Groups"}, {"doctype": "Item Group", "is_group": 1, "item_group_name": "_Test Item Group B", "parent_item_group": "All Item Groups"}, {"doctype": "Item Group", "is_group": 1, "item_group_name": "_Test Item Group B - 1", "parent_item_group": "_Test Item Group B"}, {"doctype": "Item Group", "is_group": 1, "item_group_name": "_Test Item Group B - 2", "parent_item_group": "_Test Item Group B"}, {"doctype": "Item Group", "is_group": 0, "item_group_name": "_Test Item Group B - 3", "parent_item_group": "_Test Item Group B"}, {"doctype": "Item Group", "is_group": 1, "item_group_name": "_Test Item Group C", "parent_item_group": "All Item Groups"}, {"doctype": "Item Group", "is_group": 1, "item_group_name": "_Test Item Group C - 1", "parent_item_group": "_Test Item Group C"}, {"doctype": "Item Group", "is_group": 1, "item_group_name": "_Test Item Group C - 2", "parent_item_group": "_Test Item Group C"}, {"doctype": "Item Group", "is_group": 1, "item_group_name": "_Test Item Group D", "parent_item_group": "All Item Groups"}, {"doctype": "Item Group", "is_group": 1, "item_group_name": "_Test Item Group Tax Parent", "parent_item_group": "All Item Groups", "taxes": [{"doctype": "Item Tax", "parentfield": "taxes", "item_tax_template": "_Test Account Excise Duty @ 10 - _TC", "tax_category": ""}, {"doctype": "Item Tax", "parentfield": "taxes", "item_tax_template": "_Test Account Excise Duty @ 12 - _TC", "tax_category": "_Test Tax Category 1"}]}, {"doctype": "Item Group", "is_group": 0, "item_group_name": "_Test Item Group Tax Child Override", "parent_item_group": "_Test Item Group Tax Parent", "taxes": [{"doctype": "Item Tax", "parentfield": "taxes", "item_tax_template": "_Test Account Excise Duty @ 15 - _TC", "tax_category": ""}]}]