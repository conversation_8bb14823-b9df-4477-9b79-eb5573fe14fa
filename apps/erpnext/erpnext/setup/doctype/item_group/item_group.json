{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:item_group_name", "creation": "2013-03-28 10:35:29", "description": "An Item Group is a way to classify items based on types.", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["gs", "item_group_name", "parent_item_group", "is_group", "image", "column_break_5", "defaults", "item_group_defaults", "sec_break_taxes", "taxes", "lft", "old_parent", "rgt"], "fields": [{"fieldname": "gs", "fieldtype": "Section Break", "label": "General Settings"}, {"fieldname": "item_group_name", "fieldtype": "Data", "in_list_view": 1, "label": "Item Group Name", "oldfieldname": "item_group_name", "oldfieldtype": "Data", "reqd": 1, "unique": 1}, {"bold": 1, "fieldname": "parent_item_group", "fieldtype": "Link", "ignore_user_permissions": 1, "in_list_view": 1, "label": "Parent Item Group", "oldfieldname": "parent_item_group", "oldfieldtype": "Link", "options": "Item Group"}, {"bold": 1, "default": "0", "description": "Only leaf nodes are allowed in transaction", "fieldname": "is_group", "fieldtype": "Check", "in_list_view": 1, "label": "Is Group", "oldfieldname": "is_group", "oldfieldtype": "Select"}, {"fieldname": "image", "fieldtype": "Attach Image", "hidden": 1, "label": "Image"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "defaults", "fieldtype": "Section Break", "label": "De<PERSON>ults"}, {"fieldname": "item_group_defaults", "fieldtype": "Table", "label": "Item Group Defaults", "options": "<PERSON><PERSON>"}, {"fieldname": "sec_break_taxes", "fieldtype": "Section Break", "label": "Item Tax"}, {"fieldname": "taxes", "fieldtype": "Table", "label": "Taxes", "options": "Item Tax"}, {"fieldname": "lft", "fieldtype": "Int", "hidden": 1, "label": "lft", "no_copy": 1, "oldfieldname": "lft", "oldfieldtype": "Int", "print_hide": 1, "search_index": 1}, {"fieldname": "rgt", "fieldtype": "Int", "hidden": 1, "label": "rgt", "no_copy": 1, "oldfieldname": "rgt", "oldfieldtype": "Int", "print_hide": 1, "search_index": 1}, {"fieldname": "old_parent", "fieldtype": "Link", "hidden": 1, "ignore_user_permissions": 1, "label": "old_parent", "no_copy": 1, "oldfieldname": "old_parent", "oldfieldtype": "Data", "options": "Item Group", "print_hide": 1, "report_hide": 1}], "icon": "fa fa-sitemap", "idx": 1, "image_field": "image", "is_tree": 1, "links": [], "max_attachments": 3, "modified": "2024-01-30 14:08:38.485616", "modified_by": "Administrator", "module": "Setup", "name": "Item Group", "naming_rule": "By fieldname", "nsm_parent_field": "parent_item_group", "owner": "Administrator", "permissions": [{"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager"}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User"}, {"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "<PERSON>em Manager", "share": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User"}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase User"}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User"}, {"email": 1, "export": 1, "print": 1, "report": 1, "role": "Desk User", "select": 1, "share": 1}], "search_fields": "parent_item_group", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}