{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:customer_group_name", "creation": "2013-01-10 16:34:23", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["customer_group_name", "parent_customer_group", "is_group", "cb0", "default_price_list", "payment_terms", "lft", "rgt", "old_parent", "default_receivable_account", "accounts", "credit_limit_section", "credit_limits"], "fields": [{"fieldname": "customer_group_name", "fieldtype": "Data", "in_list_view": 1, "label": "Customer Group Name", "no_copy": 1, "oldfieldname": "customer_group_name", "oldfieldtype": "Data", "reqd": 1, "unique": 1}, {"bold": 1, "fieldname": "parent_customer_group", "fieldtype": "Link", "ignore_user_permissions": 1, "in_list_view": 1, "label": "Parent Customer Group", "oldfieldname": "parent_customer_group", "oldfieldtype": "Link", "options": "Customer Group"}, {"bold": 1, "default": "0", "description": "Only leaf nodes are allowed in transaction", "fieldname": "is_group", "fieldtype": "Check", "in_list_view": 1, "label": "Is Group", "oldfieldname": "is_group", "oldfieldtype": "Select"}, {"fieldname": "cb0", "fieldtype": "Column Break"}, {"fieldname": "default_price_list", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Default Price List", "options": "Price List"}, {"fieldname": "payment_terms", "fieldtype": "Link", "label": "Default Payment Terms Template", "options": "Payment Terms Template"}, {"fieldname": "lft", "fieldtype": "Int", "hidden": 1, "label": "lft", "no_copy": 1, "oldfieldname": "lft", "oldfieldtype": "Int", "print_hide": 1, "report_hide": 1, "search_index": 1}, {"fieldname": "rgt", "fieldtype": "Int", "hidden": 1, "label": "rgt", "no_copy": 1, "oldfieldname": "rgt", "oldfieldtype": "Int", "print_hide": 1, "report_hide": 1, "search_index": 1}, {"fieldname": "old_parent", "fieldtype": "Link", "hidden": 1, "ignore_user_permissions": 1, "label": "old_parent", "no_copy": 1, "oldfieldname": "old_parent", "oldfieldtype": "Data", "options": "Customer Group", "print_hide": 1, "report_hide": 1}, {"fieldname": "default_receivable_account", "fieldtype": "Section Break", "label": "Default Accounts"}, {"depends_on": "eval:!doc.__islocal", "description": "Mention if non-standard receivable account applicable", "fieldname": "accounts", "fieldtype": "Table", "label": "Accounts", "options": "Party Account"}, {"fieldname": "credit_limit_section", "fieldtype": "Section Break", "label": "Credit Limits"}, {"fieldname": "credit_limits", "fieldtype": "Table", "label": "Credit Limit", "options": "Customer Credit Limit"}], "icon": "fa fa-sitemap", "idx": 1, "is_tree": 1, "links": [], "modified": "2023-06-02 13:40:34.435822", "modified_by": "Administrator", "module": "Setup", "name": "Customer Group", "naming_rule": "By fieldname", "nsm_parent_field": "parent_customer_group", "owner": "Administrator", "permissions": [{"email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager"}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User"}, {"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Master Manager", "share": 1, "write": 1}, {"permlevel": 1, "read": 1, "role": "Sales Master Manager", "write": 1}, {"permlevel": 1, "read": 1, "role": "Sales User"}, {"permlevel": 1, "read": 1, "role": "Sales Manager"}, {"email": 1, "export": 1, "print": 1, "report": 1, "role": "Customer", "select": 1, "share": 1}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1}], "search_fields": "parent_customer_group", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}