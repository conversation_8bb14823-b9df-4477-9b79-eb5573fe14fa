{"actions": [], "allow_import": 1, "autoname": "HR-ARU-.#####", "creation": "2013-01-10 16:34:22", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["transaction", "based_on", "customer_or_item", "master_name", "column_break_3", "company", "section_break_17", "value", "section_break_7", "system_role", "to_emp", "column_break_10", "system_user", "to_designation", "section_break_13", "approving_role", "column_break_15", "approving_user"], "fields": [{"fieldname": "transaction", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Transaction", "oldfieldname": "transaction", "oldfieldtype": "Select", "options": "\nSales Order\nPurchase Order\nQuotation\nDelivery Note\nSales Invoice\nPurchase Invoice\nPurchase Receipt", "reqd": 1}, {"fieldname": "based_on", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Based On", "oldfieldname": "based_on", "oldfieldtype": "Select", "options": "\nGrand Total\nAverage Discount\nCustomerwise Discount\nItemwise Discount\nItem Group wise Discount\nNot Applicable", "reqd": 1}, {"fieldname": "customer_or_item", "fieldtype": "Select", "hidden": 1, "label": "Customer or Item", "options": "Customer\nItem\nItem Group", "read_only": 1}, {"fieldname": "master_name", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Customer / Item / Item Group", "oldfieldname": "master_name", "oldfieldtype": "Link", "options": "customer_or_item"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "remember_last_selected_value": 1}, {"fieldname": "section_break_17", "fieldtype": "Section Break"}, {"fieldname": "value", "fieldtype": "Float", "label": "Authorized Value", "oldfieldname": "value", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "section_break_7", "fieldtype": "Section Break"}, {"fieldname": "system_role", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Applicable To (Role)", "oldfieldname": "system_role", "oldfieldtype": "Link", "options": "Role"}, {"fieldname": "to_emp", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Applicable To (Employee)", "oldfieldname": "to_emp", "oldfieldtype": "Link", "options": "Employee"}, {"fieldname": "column_break_10", "fieldtype": "Column Break"}, {"fieldname": "system_user", "fieldtype": "Link", "label": "Applicable To (User)", "oldfieldname": "system_user", "oldfieldtype": "Link", "options": "User"}, {"fieldname": "to_designation", "fieldtype": "Link", "label": "Applicable To (Designation)", "oldfieldname": "to_designation", "oldfieldtype": "Link", "options": "Designation"}, {"fieldname": "section_break_13", "fieldtype": "Section Break"}, {"fieldname": "approving_role", "fieldtype": "Link", "label": "Approving Role (above authorized value)", "oldfieldname": "approving_role", "oldfieldtype": "Link", "options": "Role"}, {"fieldname": "column_break_15", "fieldtype": "Column Break"}, {"fieldname": "approving_user", "fieldtype": "Link", "label": "Approving User  (above authorized value)", "oldfieldname": "approving_user", "oldfieldtype": "Link", "options": "User"}], "icon": "fa fa-shield", "idx": 1, "links": [], "modified": "2023-09-11 10:29:02.863193", "modified_by": "Administrator", "module": "Setup", "name": "Authorization Rule", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "search_fields": "transaction,based_on,system_user,system_role,approving_user,approving_role", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}