{"allow_copy": 0, "allow_import": 0, "allow_rename": 0, "beta": 0, "creation": "2013-02-22 01:27:45", "custom": 0, "docstatus": 0, "doctype": "DocType", "editable_grid": 1, "fields": [{"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "school_univ", "fieldtype": "Small Text", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "School/University", "length": 0, "no_copy": 0, "oldfieldname": "school_univ", "oldfieldtype": "Small Text", "permlevel": 0, "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "qualification", "fieldtype": "Data", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "Qualification", "length": 0, "no_copy": 0, "oldfieldname": "qualification", "oldfieldtype": "Data", "permlevel": 0, "print_hide": 0, "print_hide_if_no_value": 0, "print_width": "100px", "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0, "width": "100px"}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "level", "fieldtype": "Select", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "Level", "length": 0, "no_copy": 0, "oldfieldname": "level", "oldfieldtype": "Select", "options": "Graduate\nPost Graduate\nUnder Graduate", "permlevel": 0, "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "year_of_passing", "fieldtype": "Int", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "Year of Passing", "length": 0, "no_copy": 0, "oldfieldname": "year_of_passing", "oldfieldtype": "Int", "permlevel": 0, "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "class_per", "fieldtype": "Data", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "Class / Percentage", "length": 0, "no_copy": 0, "oldfieldname": "class_per", "oldfieldtype": "Data", "permlevel": 0, "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "maj_opt_subj", "fieldtype": "Text", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "Major/Optional Subjects", "length": 0, "no_copy": 0, "oldfieldname": "maj_opt_subj", "oldfieldtype": "Text", "permlevel": 0, "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}], "hide_heading": 0, "hide_toolbar": 0, "idx": 1, "image_view": 0, "in_create": 0, "is_submittable": 0, "issingle": 0, "istable": 1, "max_attachments": 0, "modified": "2016-07-11 03:27:59.995464", "modified_by": "Administrator", "module": "Setup", "name": "Employee Education", "owner": "Administrator", "permissions": [], "quick_entry": 0, "read_only": 0, "read_only_onload": 0, "track_seen": 0}