{"allow_copy": 0, "allow_import": 1, "allow_rename": 1, "autoname": "field:branch", "beta": 0, "creation": "2013-01-10 16:34:13", "custom": 0, "docstatus": 0, "doctype": "DocType", "document_type": "Setup", "editable_grid": 0, "fields": [{"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "branch", "fieldtype": "Data", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "Branch", "length": 0, "no_copy": 0, "oldfieldname": "branch", "oldfieldtype": "Data", "permlevel": 0, "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 1, "search_index": 0, "set_only_once": 0, "unique": 0}], "hide_heading": 0, "hide_toolbar": 0, "icon": "fa fa-code-fork", "idx": 1, "image_view": 0, "in_create": 0, "is_submittable": 0, "issingle": 0, "istable": 0, "max_attachments": 0, "modified": "2016-07-25 05:24:26.534086", "modified_by": "Administrator", "module": "Setup", "name": "Branch", "owner": "Administrator", "permissions": [{"amend": 0, "apply_user_permissions": 0, "cancel": 0, "create": 1, "delete": 1, "email": 1, "export": 0, "if_owner": 0, "import": 0, "permlevel": 0, "print": 1, "read": 1, "report": 1, "role": "HR User", "set_user_permissions": 0, "share": 1, "submit": 0, "write": 1}, {"amend": 0, "apply_user_permissions": 0, "cancel": 0, "create": 1, "delete": 1, "email": 1, "export": 0, "if_owner": 0, "import": 0, "permlevel": 0, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "set_user_permissions": 0, "share": 1, "submit": 0, "write": 1}], "quick_entry": 1, "read_only": 0, "read_only_onload": 0, "track_seen": 0}