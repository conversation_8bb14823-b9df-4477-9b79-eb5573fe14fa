{"creation": "2018-11-19 12:39:46.153061", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["employee", "employee_name", "user_id"], "fields": [{"fieldname": "employee", "fieldtype": "Link", "in_list_view": 1, "label": "Employee", "options": "Employee"}, {"fetch_from": "employee.first_name", "fieldname": "employee_name", "fieldtype": "Data", "in_list_view": 1, "label": "Employee Name"}, {"fetch_from": "employee.user_id", "fieldname": "user_id", "fieldtype": "Data", "in_list_view": 1, "label": "ERPNext User ID", "read_only": 1}], "istable": 1, "modified": "2022-02-13 19:44:21.302938", "modified_by": "Administrator", "module": "Setup", "name": "Employee Group Table", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}