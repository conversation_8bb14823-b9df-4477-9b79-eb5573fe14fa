{%- from "templates/print_formats/standard_macros.html" import add_header, render_field, print_value, fieldmeta,
	get_width, get_align_class with context -%}

{%- macro render_currency(df, doc) -%}
<div class="row {% if df.bold %}important{% endif %} data-field">
	<div class="col-xs-{{ "9" if df.fieldtype=="Check" else "5" }}
		{%- if doc.align_labels_right %} text-right{%- endif -%}">
		<label>{{ _(df.label) }}</label>
	</div>
	<div class="col-xs-{{ "3" if df.fieldtype=="Check" else "7" }} value">
		{% if doc.get(df.fieldname) != None -%}
			{{ frappe.utils.fmt_money((doc[df.fieldname])|abs, currency=doc.currency) }}
		{% endif %}
	</div>
</div>
{%- endmacro -%}

{%- macro render_taxes(df, doc) -%}
	{%- set data = doc.get(df.fieldname)[df.start:df.end] -%}
	<div class="row">
		<div class="col-xs-6"></div>
		<div class="col-xs-6">
			{%- for charge in data -%}
				{%- if (charge.tax_amount or doc.flags.print_taxes_with_zero_amount) and (not charge.included_in_print_rate or doc.flags.show_inclusive_tax_in_print) -%}
				<div class="row">
					<div class="col-xs-5 {%- if doc.align_labels_right %} text-right{%- endif -%}">
						<label>{{ charge.get_formatted("description") }}</label></div>
					<div class="col-xs-7 text-right">
						{{ frappe.utils.fmt_money((charge.tax_amount)|abs, currency=doc.currency) }}
					</div>
				</div>
				{%- endif -%}
			{%- endfor -%}
		</div>
	</div>
{%- endmacro -%}

{%- macro render_table(df, doc) -%}
	{%- set table_meta = frappe.get_meta(df.options) -%}
	{%- set data = doc.get(df.fieldname)[df.start:df.end] -%}
	{%- if doc.print_templates and
			doc.print_templates.get(df.fieldname) -%}
		{% include doc.print_templates[df.fieldname] %}
	{%- else -%}
		{%- if data -%}
		{%- set visible_columns = get_visible_columns(doc.get(df.fieldname),
			table_meta, df) -%}
		<div {{ fieldmeta(df) }}>
			<table class="table table-bordered table-condensed">
				<thead>
					<tr>
						<th style="width: 40px" class="table-sr">{{ _("Sr") }}</th>
						{% for tdf in visible_columns %}
						{% if (data and not data[0].flags.compact_item_print) or tdf.fieldname in doc.get(df.fieldname)[0].flags.compact_item_fields %}
							<th style="width: {{ get_width(tdf) }};" class="{{ get_align_class(tdf) }}" {{ fieldmeta(df) }}>
								{{ _(tdf.label) }}</th>
						{% endif %}
						{% endfor %}
					</tr>
				</thead>
				<tbody>
					{% for d in data %}
					<tr>
						<td class="table-sr">{{ d.idx }}</td>
						{% for tdf in visible_columns %}
						{% if not print_settings.compact_item_print or tdf.fieldname in doc.flags.compact_item_fields %}
							<td class="{{ get_align_class(tdf) }}" {{ fieldmeta(df) }}>
								{% if tdf.fieldname == 'qty' %}
									<div class="value">{{ (d[tdf.fieldname])|abs }}</div></td>
								{% elif tdf.fieldtype == 'Currency' %}
									<div class="value">{{ frappe.utils.fmt_money((d[tdf.fieldname])|abs, currency=doc.currency) }}</div></td>
								{% else %}
									{% if doc.child_print_templates %}
										{%- set child_templates = doc.child_print_templates.get(df.fieldname) -%}
											<div class="value">{{ print_value(tdf, d, doc, visible_columns, child_templates) }}</div></td>
										{% else %}
											<div class="value">{{ print_value(tdf, d, doc, visible_columns) }}</div></td>
									{% endif %}
								{% endif %}
						{% endif %}
						{% endfor %}
					</tr>
					{% endfor %}
				</tbody>
			</table>
		</div>
		{%- endif -%}
	{%- endif -%}
{%- endmacro -%}

{% for page in layout %}
<div class="page-break">
	<div {% if print_settings.repeat_header_footer %} id="header-html" class="hidden-pdf" {% endif %}>
		{{ add_header(loop.index, layout|len, doc, letter_head, no_letterhead, footer, print_settings) }}
	</div>

	{% if print_settings.repeat_header_footer %}
	<div id="footer-html" class="visible-pdf">
		{% if not no_letterhead and footer %}
		<div class="letter-head-footer">
			{{ footer }}
		</div>
		{% endif %}
		<p class="text-center small page-number visible-pdf">
			{{ _("Page {0} of {1}").format('<span class="page"></span>', '<span class="topage"></span>') }}
		</p>
	</div>
	{% endif %}

	{% for section in page %}
    <div class="row section-break">
		{% if section.columns.fields %}
				{%- if doc.print_line_breaks and loop.index != 1 -%}<hr>{%- endif -%}
				{%- if doc.print_section_headings and section.label and section.has_data -%}
				<h4 class='col-sm-12'>{{ _(section.label) }}</h4>
			{% endif %}
		{%- endif -%}
        {% for column in section.columns %}
			<div class="col-xs-{{ (12 / section.columns|len)|int }} column-break">
				{% for df in column.fields %}
					{% if df.fieldname == 'taxes' %}
						{{ render_taxes(df, doc) }}
					{% elif df.fieldtype == 'Currency' %}
						{{ render_currency(df, doc) }}
					{% elif df.fieldtype =='Table' %}
						{{ render_table(df, doc)}}
					{% elif doc[df.fieldname] and df.fieldname != 'total_qty' %}
						{{ render_field(df, doc) }}
					{% endif %}
           		 {% endfor %}
			</div>
        {% endfor %}
    </div>
    {% endfor %}
</div>
{% endfor %}
