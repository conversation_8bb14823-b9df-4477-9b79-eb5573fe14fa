{%- from "templates/print_formats/standard_macros.html" import add_header -%}
<style>
    .table-bordered td.top-bottom {border-top: none !important;border-bottom: none !important;}
    .table-bordered td.right{border-right: none !important;}
    .table-bordered td.left{border-left: none !important;}


</style>
<div>
    {% set gl = frappe.get_list(doctype="GL Entry",  fields=["account", "party_type", "party", "debit", "credit", "remarks"], filters={"voucher_type": doc.doctype, "voucher_no": doc.name}) %}
    {%- if not doc.get("print_heading") and not doc.get("select_print_heading")
        and doc.set("select_print_heading", _("Payment Entry")) -%}{%- endif -%}
    {{ add_header(0, 1, doc, letter_head, no_letterhead, print_settings) }}
    <div class="row margin-bottom">
            <div class="col-xs-6">
                <table>
                <tr><td><strong>Voucher No: </strong></td><td>{{ doc.name }}</td></tr>
                </table>
            </div>
            <div class="col-xs-6">
                <table>
                    <tr><td><strong>Date: </strong></td><td>{{ frappe.utils.format_date(doc.creation) }}</td></tr>
                </table>
            </div>
    </div>
    <div class="margin-top">
        <table class="table table-bordered table-condensed">
            <tr>
                <th>Account</th>
                <th>Party Type</th>
                <th>Party</th>
                <th>Amount</th>
            </tr>
            <tr>
                    <td class="top-bottom" colspan="5"><strong>Debit</strong></td>
            </tr>
            {% for entries in gl %}
            {% if entries.credit == 0.0 %}
            <tr>
                <td class="right top-bottom">{{ entries.account }}</td>
                <td class="right left top-bottom">{{ entries.party_type }}</td>
                <td class="right left top-bottom">{{ entries.party }}</td>
                <td class="left top-bottom">{{ entries.debit }}</td>
            </tr>
            <tr>
                <td class="top-bottom"colspan="4"><strong> Narration </strong><br>{{ entries.remarks }}</td>
            </tr>
            {% endif %}
            {% endfor %}
            <tr>
                    <td class="right" colspan="3" ><strong>Total (debit) </strong></td>
                    <td class="left" >{{ frappe.format((gl | sum(attribute="debit")), {fieldtype: "Currency"}) }}</td>
            </tr>
            <tr>
                    <td class="top-bottom" colspan="5"><strong>Credit</strong></td>
            </tr>
            {% for entries in gl %}
            {% if entries.debit == 0.0 %}
            <tr>
                <td class="right top-bottom">{{ entries.account }}</td>
                <td class="right left top-bottom">{{ entries.party_type }}</td>
                <td class="right left top-bottom">{{ entries.party }}</td>
                <td class="left top-bottom">{{ entries.credit }}</td>
            </tr>
            <tr>
                <td class="top-bottom" colspan="4"><strong> Narration </strong><br>{{ entries.remarks }}</td>
            </tr>
            {% endif %}
            {% endfor %}
            <tr>
                    <td class="right" colspan="3"><strong>Total (credit) </strong></td>
                    <td class="left" >{{ frappe.format((gl | sum(attribute="credit")), {fieldtype: "Currency"}) }}</td>
            </tr>
        </table>
    <div>
</div>
