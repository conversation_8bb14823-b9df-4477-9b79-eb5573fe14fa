{"creation": "2012-04-11 13:16:56", "custom_format": 1, "doc_type": "Journal Entry", "docstatus": 0, "doctype": "Print Format", "html": "<div style=\"position: relative\">\n\n\t{%- from \"templates/print_formats/standard_macros.html\" import add_header -%}\n<div class=\"page-break\">\n    {%- if not doc.get(\"print_heading\") and not doc.get(\"select_print_heading\") \n        and doc.set(\"select_print_heading\", _(\"Payment Advice\")) -%}{%- endif -%}\n    {{ add_header(0, 1, doc, letter_head, no_letterhead) }}\n\n{%- for label, value in (\n        (_(\"Voucher Date\"), frappe.utils.formatdate(doc.voucher_date)),\n        (_(\"Reference / Cheque No.\"), doc.cheque_no),\n        (_(\"Reference / Cheque Date\"), frappe.utils.formatdate(doc.cheque_date))\n    ) -%}\n    <div class=\"row\">\n        <div class=\"col-xs-4\"><label class=\"text-right\">{{ label }}</label></div>\n        <div class=\"col-xs-8\">{{ value }}</div>\n    </div>\n{%- endfor -%}\n\t<hr>\n\t<p>{{ _(\"This amount is in full / part settlement of the listed bills\") }}:</p>\n{%- for label, value in (\n         (_(\"Amount\"), \"<strong>\" + doc.get_formatted(\"total_amount\") + \"</strong><br>\" + (doc.total_amount_in_words or \"\") + \"<br>\"),\n        (_(\"References\"), doc.remark)\n    ) -%}\n    <div class=\"row\">\n        <div class=\"col-xs-4\"><label class=\"text-right\">{{ label }}</label></div>\n        <div class=\"col-xs-8\">{{ value }}</div>\n    </div>\n    {%- endfor -%}\n    <hr>\n\t<div style=\"position: absolute; top: 14cm; left: 0cm;\">\n\t\tPrepared By</div>\n\t<div style=\"position: absolute; top: 14cm; left: 5.5cm;\">\n\t\tAuthorised Signatory</div>\n\t<div style=\"position: absolute; top: 14cm; left: 11cm;\">\n\t\tReceived Payment as Above</div>\n\t<div style=\"position: absolute; top: 16.4cm; left: 5.9cm;\">\n\t\t<strong>_____________</strong></div>\n\t<div style=\"position: absolute; top: 16.7cm; left: 6cm;\">\n\t\t<strong>A/C Payee</strong></div>\n\t<div style=\"position: absolute; top: 16.7cm; left: 5.9cm;\">\n\t\t<strong>_____________</strong></div>\n\t<div style=\"position: absolute; top: 16.9cm; left: 12cm;\">\n\t\t{{ frappe.utils.formatdate(doc.cheque_date) }}</div>\n\t<div style=\"position: absolute; top: 17.9cm; left: 1cm;\">\n\t\t{{ doc.pay_to_recd_from }}</div>\n\t<div style=\"position: absolute; top: 18.6cm; left: 1cm; width: 7cm;\">\n\t\t{{ doc.total_amount_in_words }}</div>\n\t<div style=\"position: absolute; top: 19.7cm; left: 12cm;\">\n\t\t{{ doc.get_formatted(\"total_amount\") }}</div>\n</div>", "idx": 1, "modified": "2015-05-29 01:57:51.203850", "modified_by": "Administrator", "name": "Cheque Printing Format", "owner": "Administrator", "print_format_type": "<PERSON><PERSON>", "standard": "Yes"}