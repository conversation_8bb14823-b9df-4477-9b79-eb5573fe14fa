{"actions": [], "autoname": "ACC-SUB-.YYYY.-.#####", "creation": "2017-07-18 17:50:43.967266", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["party_type", "party", "cb_1", "company", "status", "subscription_period", "start_date", "end_date", "cancelation_date", "trial_period_start", "trial_period_end", "follow_calendar_months", "generate_new_invoices_past_due_date", "submit_invoice", "column_break_11", "current_invoice_start", "current_invoice_end", "days_until_due", "generate_invoice_at", "number_of_days", "cancel_at_period_end", "sb_4", "plans", "sb_1", "sales_tax_template", "purchase_tax_template", "sb_2", "apply_additional_discount", "cb_2", "additional_discount_percentage", "additional_discount_amount", "accounting_dimensions_section", "cost_center"], "fields": [{"allow_on_submit": 1, "fieldname": "cb_1", "fieldtype": "Column Break"}, {"fieldname": "status", "fieldtype": "Select", "label": "Status", "no_copy": 1, "options": "\nTrialling\nActive\nPast Due Date\nCancelled\nUnpaid\nCompleted", "read_only": 1}, {"fieldname": "subscription_period", "fieldtype": "Section Break", "label": "Subscription Period"}, {"fieldname": "cancelation_date", "fieldtype": "Date", "label": "Cancelation Date", "read_only": 1}, {"allow_on_submit": 1, "fieldname": "trial_period_start", "fieldtype": "Date", "label": "Trial Period Start Date", "set_only_once": 1}, {"depends_on": "eval:doc.trial_period_start", "fieldname": "trial_period_end", "fieldtype": "Date", "label": "Trial Period End Date", "set_only_once": 1}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"fieldname": "current_invoice_start", "fieldtype": "Date", "label": "Current Invoice Start Date", "no_copy": 1, "read_only": 1}, {"fieldname": "current_invoice_end", "fieldtype": "Date", "label": "Current Invoice End Date", "no_copy": 1, "read_only": 1}, {"default": "0", "description": "Number of days that the subscriber has to pay invoices generated by this subscription", "fieldname": "days_until_due", "fieldtype": "Int", "label": "Days Until Due"}, {"default": "0", "fieldname": "cancel_at_period_end", "fieldtype": "Check", "label": "Cancel At End Of Period"}, {"allow_on_submit": 1, "fieldname": "sb_4", "fieldtype": "Section Break", "label": "Plans"}, {"allow_on_submit": 1, "fieldname": "plans", "fieldtype": "Table", "label": "Plans", "options": "Subscription Plan Detail", "reqd": 1}, {"depends_on": "eval:['Customer', 'Supplier'].includes(doc.party_type)", "fieldname": "sb_1", "fieldtype": "Section Break", "label": "Taxes"}, {"fieldname": "sb_2", "fieldtype": "Section Break", "label": "Discounts"}, {"fieldname": "apply_additional_discount", "fieldtype": "Select", "label": "Apply Additional Discount On", "options": "\nGrand Total\nNet Total"}, {"fieldname": "cb_2", "fieldtype": "Column Break"}, {"fieldname": "additional_discount_percentage", "fieldtype": "Percent", "label": "Additional Discount Percentage"}, {"collapsible": 1, "fieldname": "additional_discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Discount Amount"}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "party_type", "fieldtype": "Link", "label": "Party Type", "options": "DocType", "reqd": 1, "set_only_once": 1}, {"fieldname": "party", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Party", "options": "party_type", "reqd": 1, "set_only_once": 1}, {"depends_on": "eval:doc.party_type === 'Customer'", "fieldname": "sales_tax_template", "fieldtype": "Link", "label": "Sales Taxes and Charges Template", "options": "Sales Taxes and Charges Template"}, {"depends_on": "eval:doc.party_type === 'Supplier'", "fieldname": "purchase_tax_template", "fieldtype": "Link", "label": "Purchase Taxes and Charges Template", "options": "Purchase Taxes and Charges Template"}, {"default": "0", "description": "If this is checked subsequent new invoices will be created on calendar  month and quarter start dates irrespective of current invoice start date", "fieldname": "follow_calendar_months", "fieldtype": "Check", "label": "Follow Calendar Months", "set_only_once": 1}, {"default": "0", "description": "New invoices will be generated as per schedule even if current invoices are unpaid or past due date", "fieldname": "generate_new_invoices_past_due_date", "fieldtype": "Check", "label": "Generate New Invoices Past Due Date"}, {"fieldname": "end_date", "fieldtype": "Date", "label": "Subscription End Date", "set_only_once": 1}, {"fieldname": "start_date", "fieldtype": "Date", "label": "Subscription Start Date", "set_only_once": 1}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company"}, {"default": "1", "fieldname": "submit_invoice", "fieldtype": "Check", "label": "Submit Generated Invoices"}, {"default": "End of the current subscription period", "fieldname": "generate_invoice_at", "fieldtype": "Select", "label": "Generate Invoice At", "options": "End of the current subscription period\nBeginning of the current subscription period\nDays before the current subscription period", "reqd": 1}, {"depends_on": "eval:doc.generate_invoice_at === \"Days before the current subscription period\"", "fieldname": "number_of_days", "fieldtype": "Int", "label": "Number of Days", "mandatory_depends_on": "eval:doc.generate_invoice_at === \"Days before the current subscription period\""}], "index_web_pages_for_search": 1, "links": [{"group": "Buying", "link_doctype": "Purchase Invoice", "link_fieldname": "subscription"}, {"group": "Selling", "link_doctype": "Sales Invoice", "link_fieldname": "subscription"}], "modified": "2023-12-28 17:20:42.687789", "modified_by": "Administrator", "module": "Accounts", "name": "Subscription", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}