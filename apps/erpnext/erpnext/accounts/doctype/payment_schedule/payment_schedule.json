{"actions": [], "creation": "2017-08-10 15:38:00.080575", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["payment_term", "section_break_15", "description", "section_break_4", "due_date", "mode_of_payment", "column_break_5", "invoice_portion", "section_break_6", "discount_type", "discount_date", "column_break_9", "discount", "section_break_9", "payment_amount", "outstanding", "paid_amount", "discounted_amount", "column_break_3", "base_payment_amount"], "fields": [{"columns": 2, "fieldname": "payment_term", "fieldtype": "Link", "in_list_view": 1, "label": "Payment Term", "options": "Payment Term", "print_hide": 1}, {"columns": 2, "fetch_from": "payment_term.description", "fetch_if_empty": 1, "fieldname": "description", "fieldtype": "Small Text", "in_list_view": 1, "label": "Description"}, {"columns": 2, "fieldname": "due_date", "fieldtype": "Date", "in_list_view": 1, "label": "Due Date", "reqd": 1}, {"columns": 2, "fieldname": "invoice_portion", "fieldtype": "Percent", "in_list_view": 1, "label": "Invoice Portion", "print_hide": 1}, {"columns": 2, "fieldname": "payment_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Payment Amount", "options": "currency", "reqd": 1}, {"fieldname": "mode_of_payment", "fieldtype": "Link", "label": "Mode of Payment", "options": "Mode of Payment"}, {"depends_on": "paid_amount", "fieldname": "paid_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON>", "options": "currency"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"default": "0", "depends_on": "discounted_amount", "fieldname": "discounted_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Discounted Amount", "read_only": 1}, {"fetch_from": "payment_amount", "fieldname": "outstanding", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Outstanding", "options": "currency", "read_only": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"depends_on": "discount", "fieldname": "discount_date", "fieldtype": "Date", "label": "Discount Date", "mandatory_depends_on": "discount"}, {"default": "Percentage", "fetch_from": "payment_term.discount_type", "fieldname": "discount_type", "fieldtype": "Select", "label": "Discount Type", "options": "Percentage\nAmount"}, {"fetch_from": "payment_term.discount", "fieldname": "discount", "fieldtype": "Float", "label": "Discount"}, {"fieldname": "section_break_9", "fieldtype": "Section Break"}, {"collapsible": 1, "fieldname": "section_break_15", "fieldtype": "Section Break", "label": "Description"}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "section_break_4", "fieldtype": "Section Break"}, {"fieldname": "base_payment_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Payment Amount (Company Currency)", "options": "Company:company:default_currency"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2022-09-16 13:57:06.382859", "modified_by": "Administrator", "module": "Accounts", "name": "Payment Schedule", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}