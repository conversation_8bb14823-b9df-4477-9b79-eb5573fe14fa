{"actions": [], "allow_import": 1, "autoname": "naming_series:", "creation": "2022-01-25 10:29:57.771398", "doctype": "DocType", "engine": "InnoDB", "field_order": ["customer_section", "title", "naming_series", "customer", "customer_name", "tax_id", "company", "company_tax_id", "column_break1", "posting_date", "posting_time", "set_posting_time", "due_date", "column_break_14", "is_pos", "pos_profile", "is_consolidated", "is_return", "return_against", "update_billed_amount_in_sales_order", "update_billed_amount_in_delivery_note", "is_debit_note", "amended_from", "accounting_dimensions_section", "cost_center", "dimension_col_break", "project", "currency_and_price_list", "currency", "conversion_rate", "column_break2", "selling_price_list", "price_list_currency", "plc_conversion_rate", "ignore_pricing_rule", "items_section", "scan_barcode", "update_stock", "column_break_39", "set_warehouse", "set_target_warehouse", "section_break_42", "items", "section_break_30", "total_qty", "total_net_weight", "column_break_32", "base_total", "base_net_total", "column_break_52", "total", "net_total", "taxes_section", "tax_category", "taxes_and_charges", "column_break_38", "shipping_rule", "column_break_55", "incoterm", "named_place", "section_break_40", "taxes", "section_break_43", "base_total_taxes_and_charges", "column_break_47", "total_taxes_and_charges", "totals", "base_grand_total", "base_rounding_adjustment", "base_rounded_total", "base_in_words", "column_break5", "grand_total", "rounding_adjustment", "use_company_roundoff_cost_center", "rounded_total", "in_words", "total_advance", "outstanding_amount", "disable_rounded_total", "section_break_49", "apply_discount_on", "base_discount_amount", "is_cash_or_non_trade_discount", "additional_discount_account", "column_break_51", "additional_discount_percentage", "discount_amount", "sec_tax_breakup", "other_charges_calculation", "pricing_rule_details", "pricing_rules", "packing_list", "packed_items", "product_bundle_help", "time_sheet_list", "timesheets", "section_break_104", "total_billing_hours", "column_break_106", "total_billing_amount", "payments_tab", "payments_section", "cash_bank_account", "payments", "section_break_84", "base_paid_amount", "column_break_86", "paid_amount", "section_break_88", "base_change_amount", "column_break_90", "change_amount", "account_for_change_amount", "advances_section", "allocate_advances_automatically", "only_include_allocated_payments", "get_advances", "advances", "write_off_section", "write_off_amount", "base_write_off_amount", "write_off_outstanding_amount_automatically", "column_break_74", "write_off_account", "write_off_cost_center", "loyalty_points_redemption", "redeem_loyalty_points", "loyalty_points", "loyalty_amount", "column_break_77", "loyalty_program", "loyalty_redemption_account", "loyalty_redemption_cost_center", "contact_and_address_tab", "address_and_contact", "customer_address", "address_display", "col_break4", "contact_person", "contact_display", "contact_mobile", "contact_email", "territory", "shipping_address_section", "shipping_address_name", "shipping_address", "shipping_addr_col_break", "dispatch_address_name", "dispatch_address", "company_address_section", "company_address", "company_addr_col_break", "company_address_display", "terms_tab", "payment_schedule_section", "ignore_default_payment_terms_template", "payment_terms_template", "payment_schedule", "terms_section_break", "tc_name", "terms", "more_info_tab", "customer_po_details", "po_no", "column_break_23", "po_date", "more_info", "debit_to", "party_account_currency", "is_opening", "column_break8", "unrealized_profit_loss_account", "against_income_account", "sales_team_section_break", "sales_partner", "amount_eligible_for_commission", "column_break10", "commission_rate", "total_commission", "section_break2", "sales_team", "edit_printing_settings", "letter_head", "group_same_items", "column_break_84", "select_print_heading", "language", "subscription_section", "subscription", "from_date", "auto_repeat", "column_break_140", "to_date", "update_auto_repeat_reference", "more_information", "status", "inter_company_invoice_reference", "campaign", "represents_company", "source", "customer_group", "col_break23", "is_internal_customer", "is_discounted", "remarks", "repost_required", "connections_tab"], "fields": [{"fieldname": "customer_section", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "options": "fa fa-user"}, {"allow_on_submit": 1, "default": "{customer_name}", "fieldname": "title", "fieldtype": "Data", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "label": "Title", "no_copy": 1, "print_hide": 1}, {"bold": 1, "fieldname": "naming_series", "fieldtype": "Select", "hide_days": 1, "hide_seconds": 1, "label": "Series", "no_copy": 1, "oldfieldname": "naming_series", "oldfieldtype": "Select", "options": "ACC-SINV-.YYYY.-\nACC-SINV-RET-.YYYY.-", "print_hide": 1, "reqd": 1, "set_only_once": 1}, {"bold": 1, "fieldname": "customer", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "in_standard_filter": 1, "label": "Customer", "oldfieldname": "customer", "oldfieldtype": "Link", "options": "Customer", "print_hide": 1, "search_index": 1}, {"bold": 1, "depends_on": "customer", "fetch_from": "customer.customer_name", "fieldname": "customer_name", "fieldtype": "Small Text", "hide_days": 1, "hide_seconds": 1, "in_global_search": 1, "label": "Customer Name", "oldfieldname": "customer_name", "oldfieldtype": "Data", "read_only": 1}, {"fieldname": "tax_id", "fieldtype": "Data", "hide_days": 1, "hide_seconds": 1, "label": "Tax Id", "print_hide": 1, "read_only": 1}, {"fieldname": "project", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "in_global_search": 1, "label": "Project", "oldfieldname": "project_name", "oldfieldtype": "Link", "options": "Project", "print_hide": 1}, {"default": "0", "fieldname": "is_pos", "fieldtype": "Check", "hide_days": 1, "hide_seconds": 1, "label": "Include Payment (POS)", "oldfieldname": "is_pos", "oldfieldtype": "Check", "print_hide": 1}, {"depends_on": "is_pos", "fieldname": "pos_profile", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "POS Profile", "options": "POS Profile", "print_hide": 1}, {"default": "0", "depends_on": "eval: !doc.is_debit_note", "fieldname": "is_return", "fieldtype": "Check", "hide_days": 1, "hide_seconds": 1, "label": "Is Return (Credit Note)", "no_copy": 1, "print_hide": 1}, {"fieldname": "column_break1", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1, "oldfieldtype": "Column Break"}, {"fieldname": "company", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "in_standard_filter": 1, "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "print_hide": 1, "remember_last_selected_value": 1, "reqd": 1}, {"fieldname": "cost_center", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Cost Center", "options": "Cost Center"}, {"bold": 1, "default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "hide_days": 1, "hide_seconds": 1, "label": "Date", "no_copy": 1, "oldfieldname": "posting_date", "oldfieldtype": "Date", "reqd": 1, "search_index": 1}, {"fieldname": "posting_time", "fieldtype": "Time", "hide_days": 1, "hide_seconds": 1, "label": "Posting Time", "no_copy": 1, "oldfieldname": "posting_time", "oldfieldtype": "Time", "print_hide": 1}, {"default": "0", "depends_on": "eval:doc.docstatus==0", "fieldname": "set_posting_time", "fieldtype": "Check", "hide_days": 1, "hide_seconds": 1, "label": "Edit Posting Date and Time", "print_hide": 1}, {"fieldname": "due_date", "fieldtype": "Date", "hide_days": 1, "hide_seconds": 1, "label": "Payment Due Date", "no_copy": 1, "oldfieldname": "due_date", "oldfieldtype": "Date"}, {"fieldname": "amended_from", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "oldfieldname": "amended_from", "oldfieldtype": "Link", "options": "Sales Invoice", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:doc.return_against || doc.is_debit_note", "fieldname": "return_against", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Return Against", "no_copy": 1, "options": "Sales Invoice", "print_hide": 1, "read_only_depends_on": "eval:doc.is_return", "search_index": 1}, {"default": "0", "depends_on": "eval: doc.is_return", "fieldname": "update_billed_amount_in_sales_order", "fieldtype": "Check", "hide_days": 1, "hide_seconds": 1, "label": "Update Billed Amount in Sales Order"}, {"collapsible": 1, "collapsible_depends_on": "po_no", "fieldname": "customer_po_details", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Customer PO Details"}, {"allow_on_submit": 1, "fieldname": "po_no", "fieldtype": "Data", "hide_days": 1, "hide_seconds": 1, "label": "Customer's Purchase Order", "no_copy": 1, "print_hide": 1}, {"fieldname": "column_break_23", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"allow_on_submit": 1, "fieldname": "po_date", "fieldtype": "Date", "hide_days": 1, "hide_seconds": 1, "label": "Customer's Purchase Order Date"}, {"fieldname": "address_and_contact", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Billing Address"}, {"fieldname": "customer_address", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Customer Address", "options": "Address", "print_hide": 1}, {"fieldname": "address_display", "fieldtype": "Small Text", "hide_days": 1, "hide_seconds": 1, "label": "Address", "read_only": 1}, {"fieldname": "contact_person", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "in_global_search": 1, "label": "Contact Person", "options": "Contact", "print_hide": 1}, {"fieldname": "contact_display", "fieldtype": "Small Text", "hide_days": 1, "hide_seconds": 1, "label": "Contact", "read_only": 1}, {"fieldname": "contact_mobile", "fieldtype": "Small Text", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "label": "Mobile No", "options": "Phone", "read_only": 1}, {"fieldname": "contact_email", "fieldtype": "Data", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "label": "Contact Email", "options": "Email", "print_hide": 1, "read_only": 1}, {"fieldname": "territory", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Territory", "options": "Territory", "print_hide": 1}, {"fieldname": "col_break4", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"fieldname": "shipping_address_name", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Shipping Address Name", "options": "Address", "print_hide": 1}, {"fieldname": "shipping_address", "fieldtype": "Small Text", "hide_days": 1, "hide_seconds": 1, "label": "Shipping Address", "print_hide": 1, "read_only": 1}, {"fieldname": "company_address", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Company Address Name", "options": "Address", "print_hide": 1}, {"fieldname": "company_address_display", "fieldtype": "Small Text", "hide_days": 1, "hide_seconds": 1, "label": "Company Address", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "depends_on": "customer", "fieldname": "currency_and_price_list", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Currency and Price List"}, {"fieldname": "currency", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "<PERSON><PERSON><PERSON><PERSON>", "oldfieldname": "currency", "oldfieldtype": "Select", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "reqd": 1}, {"description": "Rate at which Customer Currency is converted to customer's base currency", "fieldname": "conversion_rate", "fieldtype": "Float", "hide_days": 1, "hide_seconds": 1, "label": "Exchange Rate", "oldfieldname": "conversion_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "precision": "9", "print_hide": 1, "reqd": 1}, {"fieldname": "column_break2", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1, "width": "50%"}, {"fieldname": "selling_price_list", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Price List", "oldfieldname": "price_list_name", "oldfieldtype": "Select", "options": "Price List", "print_hide": 1, "reqd": 1}, {"fieldname": "price_list_currency", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Price List Currency", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1, "reqd": 1}, {"description": "Rate at which Price list currency is converted to customer's base currency", "fieldname": "plc_conversion_rate", "fieldtype": "Float", "hide_days": 1, "hide_seconds": 1, "label": "Price List Exchange Rate", "precision": "9", "print_hide": 1, "reqd": 1}, {"default": "0", "fieldname": "ignore_pricing_rule", "fieldtype": "Check", "hide_days": 1, "hide_seconds": 1, "label": "Ignore Pricing Rule", "print_hide": 1}, {"depends_on": "update_stock", "fieldname": "set_warehouse", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Source Warehouse", "options": "Warehouse", "print_hide": 1}, {"fieldname": "items_section", "fieldtype": "Section Break", "hide_border": 1, "hide_days": 1, "hide_seconds": 1, "label": "Items", "oldfieldtype": "Section Break", "options": "fa fa-shopping-cart"}, {"default": "0", "fieldname": "update_stock", "fieldtype": "Check", "hide_days": 1, "hide_seconds": 1, "label": "Update Stock", "oldfieldname": "update_stock", "oldfieldtype": "Check", "print_hide": 1}, {"fieldname": "scan_barcode", "fieldtype": "Data", "hide_days": 1, "hide_seconds": 1, "label": "Scan Barcode", "options": "Barcode"}, {"allow_bulk_edit": 1, "fieldname": "items", "fieldtype": "Table", "hide_days": 1, "hide_seconds": 1, "label": "Items", "oldfieldname": "entries", "oldfieldtype": "Table", "options": "Sales Invoice Item", "reqd": 1}, {"fieldname": "pricing_rule_details", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Pricing Rules"}, {"fieldname": "pricing_rules", "fieldtype": "Table", "hide_days": 1, "hide_seconds": 1, "label": "Pricing Rule Detail", "options": "Pricing Rule Detail", "read_only": 1}, {"depends_on": "packed_items", "fieldname": "packing_list", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Packing List", "options": "fa fa-suitcase", "print_hide": 1}, {"depends_on": "packed_items", "fieldname": "packed_items", "fieldtype": "Table", "hide_days": 1, "hide_seconds": 1, "label": "Packed Items", "options": "Packed Item", "print_hide": 1}, {"fieldname": "product_bundle_help", "fieldtype": "HTML", "hide_days": 1, "hide_seconds": 1, "label": "Product Bundle Help", "print_hide": 1}, {"collapsible": 1, "collapsible_depends_on": "eval:doc.total_billing_amount > 0", "depends_on": "eval:!doc.is_return", "fieldname": "time_sheet_list", "fieldtype": "Section Break", "hide_border": 1, "hide_days": 1, "hide_seconds": 1, "label": "Time Sheet List"}, {"fieldname": "timesheets", "fieldtype": "Table", "hide_days": 1, "hide_seconds": 1, "label": "Time Sheets", "options": "Sales Invoice Timesheet", "print_hide": 1}, {"default": "0", "fieldname": "total_billing_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Total Billing Amount", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_30", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1}, {"fieldname": "total_qty", "fieldtype": "Float", "hide_days": 1, "hide_seconds": 1, "label": "Total Quantity", "read_only": 1}, {"fieldname": "base_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Total (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Net Total (Company Currency)", "oldfieldname": "net_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "reqd": 1}, {"fieldname": "column_break_32", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"fieldname": "total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Total", "options": "currency", "read_only": 1}, {"fieldname": "net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Net Total", "options": "currency", "print_hide": 1, "read_only": 1}, {"depends_on": "total_net_weight", "fieldname": "total_net_weight", "fieldtype": "Float", "hide_days": 1, "hide_seconds": 1, "label": "Total Net Weight", "print_hide": 1, "read_only": 1}, {"fieldname": "taxes_section", "fieldtype": "Section Break", "hide_border": 1, "hide_days": 1, "hide_seconds": 1, "label": "Taxes and Charges", "oldfieldtype": "Section Break", "options": "fa fa-money"}, {"fieldname": "taxes_and_charges", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Sales Taxes and Charges Template", "oldfieldname": "charge", "oldfieldtype": "Link", "options": "Sales Taxes and Charges Template", "print_hide": 1}, {"fieldname": "column_break_38", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"fieldname": "shipping_rule", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Shipping Rule", "oldfieldtype": "<PERSON><PERSON>", "options": "Shipping Rule", "print_hide": 1}, {"fieldname": "tax_category", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Tax Category", "options": "Tax Category", "print_hide": 1}, {"fieldname": "section_break_40", "fieldtype": "Section Break", "hide_border": 1, "hide_days": 1, "hide_seconds": 1}, {"fieldname": "taxes", "fieldtype": "Table", "hide_days": 1, "hide_seconds": 1, "label": "Sales Taxes and Charges", "oldfieldname": "other_charges", "oldfieldtype": "Table", "options": "Sales Taxes and Charges"}, {"collapsible": 1, "fieldname": "sec_tax_breakup", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Tax Breakup"}, {"fieldname": "other_charges_calculation", "fieldtype": "Long Text", "hide_days": 1, "hide_seconds": 1, "label": "Taxes and Charges Calculation", "no_copy": 1, "oldfieldtype": "HTML", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_43", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1}, {"fieldname": "base_total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Total Taxes and Charges (Company Currency)", "oldfieldname": "other_charges_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_47", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"fieldname": "total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Total Taxes and Charges", "options": "currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "loyalty_points_redemption", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Loyalty Points Redemption"}, {"depends_on": "redeem_loyalty_points", "fieldname": "loyalty_points", "fieldtype": "Int", "hide_days": 1, "hide_seconds": 1, "label": "Loyalty Points", "no_copy": 1, "print_hide": 1}, {"depends_on": "redeem_loyalty_points", "fieldname": "loyalty_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Loyalty Amount", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"default": "0", "fieldname": "redeem_loyalty_points", "fieldtype": "Check", "hide_days": 1, "hide_seconds": 1, "label": "Redeem Loyalty Points", "no_copy": 1, "print_hide": 1}, {"fieldname": "column_break_77", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"fetch_from": "customer.loyalty_program", "fieldname": "loyalty_program", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Loyalty Program", "no_copy": 1, "options": "Loyalty Program", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "depends_on": "redeem_loyalty_points", "fieldname": "loyalty_redemption_account", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Redemption Account", "no_copy": 1, "options": "Account"}, {"depends_on": "redeem_loyalty_points", "fieldname": "loyalty_redemption_cost_center", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Redemption Cost Center", "no_copy": 1, "options": "Cost Center"}, {"collapsible": 1, "fieldname": "section_break_49", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Additional Discount"}, {"default": "Grand Total", "fieldname": "apply_discount_on", "fieldtype": "Select", "hide_days": 1, "hide_seconds": 1, "label": "Apply Additional Discount On", "length": 15, "options": "\nGrand Total\nNet Total", "print_hide": 1}, {"fieldname": "base_discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Additional Discount Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_51", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"fieldname": "additional_discount_percentage", "fieldtype": "Float", "hide_days": 1, "hide_seconds": 1, "label": "Additional Discount Percentage", "print_hide": 1}, {"fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Additional Discount Amount", "options": "currency", "print_hide": 1}, {"fieldname": "totals", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Totals", "oldfieldtype": "Section Break", "options": "fa fa-money", "print_hide": 1}, {"fieldname": "base_grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Grand Total (Company Currency)", "oldfieldname": "grand_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "reqd": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "base_rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Rounding Adjustment (Company Currency)", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "base_rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Rounded Total (Company Currency)", "oldfieldname": "rounded_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"description": "In Words will be visible once you save the Sales Invoice.", "fieldname": "base_in_words", "fieldtype": "Small Text", "hide_days": 1, "hide_seconds": 1, "label": "In Words (Company Currency)", "length": 240, "oldfieldname": "in_words", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break5", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1, "oldfieldtype": "Column Break", "print_hide": 1, "width": "50%"}, {"bold": 1, "fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "in_list_view": 1, "label": "Grand Total", "oldfieldname": "grand_total_export", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1, "reqd": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Rounding Adjustment", "no_copy": 1, "options": "currency", "print_hide": 1, "read_only": 1}, {"bold": 1, "depends_on": "eval:!doc.disable_rounded_total", "fieldname": "rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Rounded Total", "oldfieldname": "rounded_total_export", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1}, {"fieldname": "in_words", "fieldtype": "Small Text", "hide_days": 1, "hide_seconds": 1, "label": "In Words", "length": 240, "oldfieldname": "in_words_export", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1}, {"fieldname": "total_advance", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Total Advance", "oldfieldname": "total_advance", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "party_account_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "outstanding_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Outstanding Amount", "no_copy": 1, "oldfieldname": "outstanding_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "party_account_currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "advances", "fieldname": "advances_section", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Advance Payments", "oldfieldtype": "Section Break", "options": "fa fa-money", "print_hide": 1}, {"default": "0", "fieldname": "allocate_advances_automatically", "fieldtype": "Check", "hide_days": 1, "hide_seconds": 1, "label": "Allocate Advances Automatically (FIFO)"}, {"depends_on": "eval:!doc.allocate_advances_automatically", "fieldname": "get_advances", "fieldtype": "<PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Get Advances Received", "options": "set_advances"}, {"fieldname": "advances", "fieldtype": "Table", "hide_days": 1, "hide_seconds": 1, "label": "Advances", "oldfieldname": "advance_adjustment_details", "oldfieldtype": "Table", "options": "Sales Invoice Advance", "print_hide": 1}, {"fieldname": "payment_schedule_section", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Payment Terms"}, {"depends_on": "eval:(!doc.is_pos && !doc.is_return)", "fieldname": "payment_terms_template", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Payment Terms Template", "no_copy": 1, "options": "Payment Terms Template", "print_hide": 1}, {"depends_on": "eval:(!doc.is_pos && !doc.is_return)", "fieldname": "payment_schedule", "fieldtype": "Table", "hide_days": 1, "hide_seconds": 1, "label": "Payment Schedule", "no_copy": 1, "options": "Payment Schedule", "print_hide": 1}, {"collapsible": 1, "collapsible_depends_on": "eval:!doc.is_pos", "depends_on": "eval:doc.is_pos===1", "fieldname": "payments_section", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Payments", "options": "fa fa-money"}, {"allow_on_submit": 1, "depends_on": "is_pos", "fieldname": "cash_bank_account", "fieldtype": "Link", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "label": "Cash/Bank Account", "oldfieldname": "cash_bank_account", "oldfieldtype": "Link", "options": "Account", "print_hide": 1}, {"depends_on": "eval:doc.is_pos===1", "fieldname": "payments", "fieldtype": "Table", "hide_days": 1, "hide_seconds": 1, "label": "Sales Invoice Payment", "options": "Sales Invoice Payment", "print_hide": 1}, {"fieldname": "section_break_84", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1}, {"depends_on": "eval: doc.is_pos || doc.redeem_loyalty_points", "fieldname": "base_paid_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "<PERSON><PERSON> (Company Currency)", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_86", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"depends_on": "eval: doc.is_pos || doc.redeem_loyalty_points", "fieldname": "paid_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "<PERSON><PERSON>", "no_copy": 1, "oldfieldname": "paid_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "depends_on": "is_pos", "fieldname": "section_break_88", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Changes"}, {"depends_on": "is_pos", "fieldname": "base_change_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Base Change Amount (Company Currency)", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_90", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"depends_on": "is_pos", "fieldname": "change_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Change Amount", "no_copy": 1, "options": "currency", "print_hide": 1}, {"allow_on_submit": 1, "depends_on": "is_pos", "fieldname": "account_for_change_amount", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Account for Change Amount", "options": "Account", "print_hide": 1}, {"fieldname": "write_off_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Write Off Amount", "no_copy": 1, "options": "currency", "print_hide": 1, "read_only_depends_on": "eval:doc.write_off_outstanding_amount_automatically"}, {"fieldname": "base_write_off_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Write Off Amount (Company Currency)", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"default": "0", "depends_on": "is_pos", "fieldname": "write_off_outstanding_amount_automatically", "fieldtype": "Check", "hide_days": 1, "hide_seconds": 1, "label": "Write Off Outstanding Amount", "print_hide": 1}, {"fieldname": "column_break_74", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"allow_on_submit": 1, "fieldname": "write_off_account", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Write Off Account", "options": "Account", "print_hide": 1}, {"fieldname": "write_off_cost_center", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Write Off Cost Center", "options": "Cost Center", "print_hide": 1}, {"fieldname": "terms_section_break", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Terms and Conditions", "oldfieldtype": "Section Break"}, {"fieldname": "tc_name", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Terms", "oldfieldname": "tc_name", "oldfieldtype": "Link", "options": "Terms and Conditions", "print_hide": 1}, {"fieldname": "terms", "fieldtype": "Text Editor", "hide_days": 1, "hide_seconds": 1, "label": "Terms and Conditions Details", "oldfieldname": "terms", "oldfieldtype": "Text Editor"}, {"collapsible": 1, "fieldname": "edit_printing_settings", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Print Settings"}, {"allow_on_submit": 1, "fieldname": "letter_head", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Letter Head", "oldfieldname": "letter_head", "oldfieldtype": "Select", "options": "Letter Head", "print_hide": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "group_same_items", "fieldtype": "Check", "hide_days": 1, "hide_seconds": 1, "label": "Group same items", "print_hide": 1}, {"fieldname": "language", "fieldtype": "Data", "hide_days": 1, "hide_seconds": 1, "label": "Print Language", "length": 6, "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_84", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"allow_on_submit": 1, "fieldname": "select_print_heading", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Print Heading", "no_copy": 1, "oldfieldname": "select_print_heading", "oldfieldtype": "Link", "options": "Print Heading", "print_hide": 1, "report_hide": 1}, {"collapsible": 1, "depends_on": "customer", "fieldname": "more_information", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Additional Info"}, {"fieldname": "inter_company_invoice_reference", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Inter Company Invoice Reference", "options": "Purchase Invoice", "read_only": 1, "search_index": 1}, {"fieldname": "customer_group", "fieldtype": "Link", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "label": "Customer Group", "options": "Customer Group", "print_hide": 1}, {"fieldname": "campaign", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Campaign", "oldfieldname": "campaign", "oldfieldtype": "Link", "options": "Campaign", "print_hide": 1}, {"default": "0", "fieldname": "is_discounted", "fieldtype": "Check", "hide_days": 1, "hide_seconds": 1, "label": "Is Discounted", "no_copy": 1, "read_only": 1}, {"fieldname": "col_break23", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1, "width": "50%"}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "hide_days": 1, "hide_seconds": 1, "in_standard_filter": 1, "label": "Status", "length": 30, "no_copy": 1, "options": "\nDraft\nReturn\nCredit Note Issued\nSubmitted\nPaid\nPartly Paid\nUnpaid\nUnpaid and Discounted\nPartly Paid and Discounted\nOverdue and Discounted\nOverdue\nCancelled\nInternal Transfer", "print_hide": 1, "read_only": 1}, {"fieldname": "source", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Source", "oldfieldname": "source", "oldfieldtype": "Select", "options": "Lead Source", "print_hide": 1}, {"collapsible": 1, "fieldname": "more_info", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Accounting Details", "oldfieldtype": "Section Break", "options": "fa fa-file-text", "print_hide": 1}, {"fieldname": "debit_to", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Debit To", "oldfieldname": "debit_to", "oldfieldtype": "Link", "options": "Account", "print_hide": 1, "reqd": 1, "search_index": 1}, {"fieldname": "party_account_currency", "fieldtype": "Link", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "label": "Party Account <PERSON><PERSON><PERSON><PERSON>", "no_copy": 1, "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "default": "No", "fieldname": "is_opening", "fieldtype": "Select", "hide_days": 1, "hide_seconds": 1, "label": "Is Opening Entry", "length": 4, "oldfieldname": "is_opening", "oldfieldtype": "Select", "options": "No\nYes", "print_hide": 1}, {"fieldname": "column_break8", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1, "oldfieldtype": "Column Break", "print_hide": 1}, {"fieldname": "remarks", "fieldtype": "Small Text", "hide_days": 1, "hide_seconds": 1, "label": "Remarks", "no_copy": 1, "oldfieldname": "remarks", "oldfieldtype": "Text", "print_hide": 1}, {"collapsible": 1, "collapsible_depends_on": "sales_partner", "fieldname": "sales_team_section_break", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Commission", "oldfieldtype": "Section Break", "options": "fa fa-group", "print_hide": 1}, {"fieldname": "sales_partner", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Sales Partner", "oldfieldname": "sales_partner", "oldfieldtype": "Link", "options": "Sales Partner", "print_hide": 1}, {"fieldname": "column_break10", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1, "oldfieldtype": "Column Break", "print_hide": 1, "width": "50%"}, {"fetch_from": "sales_partner.commission_rate", "fetch_if_empty": 1, "fieldname": "commission_rate", "fieldtype": "Float", "hide_days": 1, "hide_seconds": 1, "label": "Commission Rate (%)", "oldfieldname": "commission_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1}, {"fieldname": "total_commission", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Total Commission", "oldfieldname": "total_commission", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1}, {"collapsible": 1, "collapsible_depends_on": "sales_team", "fieldname": "section_break2", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Sales Team", "print_hide": 1}, {"allow_on_submit": 1, "fieldname": "sales_team", "fieldtype": "Table", "hide_days": 1, "hide_seconds": 1, "label": "Sales Contributions and Incentives", "oldfieldname": "sales_team", "oldfieldtype": "Table", "options": "Sales Team", "print_hide": 1}, {"collapsible": 1, "fieldname": "subscription_section", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Subscription"}, {"allow_on_submit": 1, "fieldname": "from_date", "fieldtype": "Date", "hide_days": 1, "hide_seconds": 1, "label": "From Date", "no_copy": 1, "print_hide": 1}, {"allow_on_submit": 1, "fieldname": "to_date", "fieldtype": "Date", "hide_days": 1, "hide_seconds": 1, "label": "To Date", "no_copy": 1, "print_hide": 1}, {"fieldname": "column_break_140", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"allow_on_submit": 1, "fieldname": "auto_repeat", "fieldtype": "Link", "hide_days": 1, "hide_seconds": 1, "label": "Auto Repeat", "no_copy": 1, "options": "Auto Repeat", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "depends_on": "eval: doc.auto_repeat", "fieldname": "update_auto_repeat_reference", "fieldtype": "<PERSON><PERSON>", "hide_days": 1, "hide_seconds": 1, "label": "Update Auto Repeat Reference"}, {"fieldname": "against_income_account", "fieldtype": "Small Text", "hidden": 1, "hide_days": 1, "hide_seconds": 1, "label": "Against Income Account", "no_copy": 1, "oldfieldname": "against_income_account", "oldfieldtype": "Small Text", "print_hide": 1, "report_hide": 1}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Accounting Dimensions"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break", "hide_days": 1, "hide_seconds": 1}, {"default": "0", "depends_on": "eval:(doc.is_pos && doc.is_consolidated)", "fieldname": "is_consolidated", "fieldtype": "Check", "label": "Is Consolidated", "read_only": 1}, {"default": "0", "fetch_from": "customer.is_internal_customer", "fieldname": "is_internal_customer", "fieldtype": "Check", "hide_days": 1, "hide_seconds": 1, "label": "Is Internal Customer", "read_only": 1}, {"fetch_from": "company.tax_id", "fieldname": "company_tax_id", "fieldtype": "Data", "label": "Company Tax ID", "read_only": 1}, {"allow_on_submit": 1, "depends_on": "eval:doc.is_internal_customer", "description": "Unrealized Profit / Loss account for intra-company transfers", "fieldname": "unrealized_profit_loss_account", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Unrealized Profit / Loss Account", "options": "Account"}, {"depends_on": "eval:doc.is_internal_customer", "description": "Company which internal customer represents", "fetch_from": "customer.represents_company", "fieldname": "represents_company", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Represents Company", "options": "Company", "read_only": 1}, {"depends_on": "eval: doc.is_internal_customer && doc.update_stock", "fieldname": "set_target_warehouse", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Set Target Warehouse", "options": "Warehouse"}, {"default": "0", "depends_on": "eval: !doc.is_return", "description": "Issue a debit note with 0 qty against an existing Sales Invoice", "fieldname": "is_debit_note", "fieldtype": "Check", "label": "Is Rate Adjustment Entry (Debit Note)"}, {"default": "0", "depends_on": "grand_total", "fieldname": "disable_rounded_total", "fieldtype": "Check", "label": "Disable Rounded Total"}, {"allow_on_submit": 1, "fieldname": "additional_discount_account", "fieldtype": "Link", "label": "Discount Account", "options": "Account"}, {"allow_on_submit": 1, "fieldname": "dispatch_address_name", "fieldtype": "Link", "label": "Dispatch Address Name", "options": "Address", "print_hide": 1}, {"allow_on_submit": 1, "fieldname": "dispatch_address", "fieldtype": "Small Text", "label": "Dispatch Address", "read_only": 1}, {"default": "0", "fieldname": "ignore_default_payment_terms_template", "fieldtype": "Check", "hidden": 1, "label": "Ignore Default Payment Terms Template", "read_only": 1}, {"fieldname": "total_billing_hours", "fieldtype": "Float", "label": "Total Billing Hours", "print_hide": 1, "read_only": 1}, {"fieldname": "amount_eligible_for_commission", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount Eligible for Commission", "read_only": 1}, {"fieldname": "subscription", "fieldtype": "Link", "label": "Subscription", "options": "Subscription"}, {"default": "0", "depends_on": "eval: doc.apply_discount_on == \"Grand Total\"", "fieldname": "is_cash_or_non_trade_discount", "fieldtype": "Check", "label": "Is Cash or Non Trade Discount"}, {"fieldname": "contact_and_address_tab", "fieldtype": "Tab Break", "label": "Contact & Address"}, {"fieldname": "payments_tab", "fieldtype": "Tab Break", "label": "Payments"}, {"fieldname": "terms_tab", "fieldtype": "Tab Break", "label": "Terms"}, {"fieldname": "more_info_tab", "fieldtype": "Tab Break", "label": "More Info"}, {"fieldname": "connections_tab", "fieldtype": "Tab Break", "label": "Connections", "show_dashboard": 1}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"fieldname": "column_break_39", "fieldtype": "Column Break"}, {"fieldname": "section_break_42", "fieldtype": "Section Break", "hide_border": 1, "hide_days": 1, "hide_seconds": 1}, {"fieldname": "column_break_55", "fieldtype": "Column Break"}, {"fieldname": "shipping_address_section", "fieldtype": "Section Break", "label": "Shipping Address"}, {"fieldname": "company_address_section", "fieldtype": "Section Break", "label": "Company Address"}, {"fieldname": "shipping_addr_col_break", "fieldtype": "Column Break"}, {"fieldname": "company_addr_col_break", "fieldtype": "Column Break"}, {"fieldname": "column_break_52", "fieldtype": "Column Break"}, {"depends_on": "eval:(!doc.is_return && doc.total_billing_amount > 0)", "fieldname": "section_break_104", "fieldtype": "Section Break"}, {"fieldname": "column_break_106", "fieldtype": "Column Break"}, {"collapsible": 1, "collapsible_depends_on": "write_off_amount", "depends_on": "is_pos", "fieldname": "write_off_section", "fieldtype": "Section Break", "hide_days": 1, "hide_seconds": 1, "label": "Write Off", "width": "50%"}, {"default": "0", "fieldname": "repost_required", "fieldtype": "Check", "hidden": 1, "label": "Repost Required", "no_copy": 1, "read_only": 1}, {"fieldname": "incoterm", "fieldtype": "Link", "label": "Incoterm", "options": "Incoterm"}, {"depends_on": "incoterm", "fieldname": "named_place", "fieldtype": "Data", "label": "Named Place"}, {"default": "0", "depends_on": "allocate_advances_automatically", "description": "Advance payments allocated against orders will only be fetched", "fieldname": "only_include_allocated_payments", "fieldtype": "Check", "label": "Only Include Allocated Payments"}, {"default": "0", "fieldname": "use_company_roundoff_cost_center", "fieldtype": "Check", "label": "Use Company default Cost Center for Round off"}, {"default": "1", "depends_on": "eval: doc.is_return", "fieldname": "update_billed_amount_in_delivery_note", "fieldtype": "Check", "label": "Update Billed Amount in Delivery Note"}], "icon": "fa fa-file-text", "idx": 181, "is_submittable": 1, "links": [{"group": "Reference", "link_doctype": "POS Invoice", "link_fieldname": "consolidated_invoice"}], "modified": "2023-11-23 16:56:29.679499", "modified_by": "Administrator", "module": "Accounts", "name": "Sales Invoice", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "submit": 1, "write": 1}, {"permlevel": 1, "read": 1, "role": "Accounts Manager", "write": 1}, {"permlevel": 1, "read": 1, "role": "All"}], "quick_entry": 1, "search_fields": "posting_date, due_date, customer, base_grand_total, outstanding_amount", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "timeline_field": "customer", "title_field": "title", "track_changes": 1, "track_seen": 1}