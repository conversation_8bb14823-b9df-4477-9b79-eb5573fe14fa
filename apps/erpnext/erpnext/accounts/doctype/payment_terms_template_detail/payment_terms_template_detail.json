{"actions": [], "creation": "2017-08-10 15:34:09.409562", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["payment_term", "section_break_13", "description", "section_break_4", "invoice_portion", "mode_of_payment", "column_break_3", "due_date_based_on", "credit_days", "credit_months", "section_break_8", "discount_type", "discount", "column_break_11", "discount_validity_based_on", "discount_validity"], "fields": [{"columns": 2, "fieldname": "payment_term", "fieldtype": "Link", "in_list_view": 1, "label": "Payment Term", "options": "Payment Term"}, {"columns": 2, "fetch_from": "payment_term.description", "fieldname": "description", "fieldtype": "Small Text", "in_list_view": 1, "label": "Description"}, {"columns": 2, "fetch_from": "payment_term.invoice_portion", "fetch_if_empty": 1, "fieldname": "invoice_portion", "fieldtype": "Float", "in_list_view": 1, "label": "Invoice Portion (%)", "reqd": 1}, {"columns": 2, "fetch_from": "payment_term.due_date_based_on", "fetch_if_empty": 1, "fieldname": "due_date_based_on", "fieldtype": "Select", "in_list_view": 1, "label": "Due Date Based On", "options": "Day(s) after invoice date\nDay(s) after the end of the invoice month\nMonth(s) after the end of the invoice month", "reqd": 1}, {"columns": 2, "default": "0", "depends_on": "eval:in_list(['Day(s) after invoice date', 'Day(s) after the end of the invoice month'], doc.due_date_based_on)", "fetch_from": "payment_term.credit_days", "fetch_if_empty": 1, "fieldname": "credit_days", "fieldtype": "Int", "in_list_view": 1, "label": "Credit Days", "non_negative": 1}, {"default": "0", "depends_on": "eval:doc.due_date_based_on=='Month(s) after the end of the invoice month'", "fetch_from": "payment_term.credit_months", "fetch_if_empty": 1, "fieldname": "credit_months", "fieldtype": "Int", "label": "Credit Months", "non_negative": 1}, {"fetch_from": "payment_term.mode_of_payment", "fieldname": "mode_of_payment", "fieldtype": "Link", "label": "Mode of Payment", "options": "Mode of Payment"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "section_break_8", "fieldtype": "Section Break", "label": "Discount Settings"}, {"default": "Percentage", "fetch_from": "payment_term.discount_type", "fetch_if_empty": 1, "fieldname": "discount_type", "fieldtype": "Select", "label": "Discount Type", "options": "Percentage\nAmount"}, {"fetch_from": "payment_term.discount", "fetch_if_empty": 1, "fieldname": "discount", "fieldtype": "Float", "label": "Discount"}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"default": "Day(s) after invoice date", "depends_on": "discount", "fetch_from": "payment_term.discount_validity_based_on", "fetch_if_empty": 1, "fieldname": "discount_validity_based_on", "fieldtype": "Select", "label": "Discount Validity Based On", "options": "Day(s) after invoice date\nDay(s) after the end of the invoice month\nMonth(s) after the end of the invoice month"}, {"collapsible": 1, "fieldname": "section_break_13", "fieldtype": "Section Break", "label": "Description"}, {"depends_on": "discount", "fetch_from": "payment_term.discount_validity", "fetch_if_empty": 1, "fieldname": "discount_validity", "fieldtype": "Int", "label": "Discount Validity", "mandatory_depends_on": "discount"}, {"fieldname": "section_break_4", "fieldtype": "Section Break"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2021-02-24 11:56:12.410807", "modified_by": "Administrator", "module": "Accounts", "name": "Payment Terms Template Detail", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}