{"actions": [], "creation": "2021-08-16 17:04:40.185167", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["reference_type", "reference_name", "reference_row", "column_break_3", "invoice_type", "invoice_number", "section_break_6", "allocated_amount", "unreconciled_amount", "column_break_8", "amount", "is_advance", "section_break_5", "difference_amount", "column_break_7", "difference_account", "exchange_rate", "currency", "accounting_dimensions_section", "cost_center", "dimension_col_break"], "fields": [{"fieldname": "invoice_number", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Invoice Number", "options": "invoice_type", "read_only": 1, "reqd": 1}, {"fieldname": "allocated_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Allocated Amount", "options": "currency", "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "section_break_5", "fieldtype": "Section Break"}, {"fieldname": "difference_account", "fieldtype": "Link", "label": "Difference Account", "options": "Account", "read_only": 1}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fieldname": "difference_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Difference Amount", "options": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "reference_name", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Reference Name", "options": "reference_type", "read_only": 1, "reqd": 1}, {"fieldname": "is_advance", "fieldtype": "Data", "hidden": 1, "label": "Is Advance", "read_only": 1}, {"fieldname": "reference_type", "fieldtype": "Link", "label": "Reference Type", "options": "DocType", "read_only": 1, "reqd": 1}, {"fieldname": "invoice_type", "fieldtype": "Link", "label": "Invoice Type", "options": "DocType", "read_only": 1, "reqd": 1}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"fieldname": "column_break_8", "fieldtype": "Column Break"}, {"fieldname": "unreconciled_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "Unreconciled Amount", "options": "currency", "read_only": 1}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "Amount", "options": "currency", "read_only": 1}, {"fieldname": "reference_row", "fieldtype": "Data", "hidden": 1, "label": "Reference Row", "read_only": 1}, {"fieldname": "currency", "fieldtype": "Link", "hidden": 1, "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "exchange_rate", "fieldtype": "Float", "label": "Exchange Rate", "read_only": 1}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"fieldname": "gain_loss_posting_date", "fieldtype": "Date", "label": "Difference Posting Date"}, {"fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}], "is_virtual": 1, "istable": 1, "links": [], "modified": "2023-12-14 13:38:26.104150", "modified_by": "Administrator", "module": "Accounts", "name": "Payment Reconciliation Allocation", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}