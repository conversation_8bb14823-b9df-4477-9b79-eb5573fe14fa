{"actions": [], "creation": "2020-04-30 14:37:08.148707", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["default", "allow_in_returns", "mode_of_payment"], "fields": [{"default": "0", "depends_on": "eval:parent.doctype == 'POS Profile'", "fieldname": "default", "fieldtype": "Check", "in_list_view": 1, "label": "<PERSON><PERSON><PERSON>"}, {"fieldname": "mode_of_payment", "fieldtype": "Link", "in_list_view": 1, "label": "Mode of Payment", "options": "Mode of Payment", "reqd": 1}, {"default": "0", "fieldname": "allow_in_returns", "fieldtype": "Check", "in_list_view": 1, "label": "Allow In Returns"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2020-10-20 12:58:46.114456", "modified_by": "Administrator", "module": "Accounts", "name": "POS Payment Method", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC"}