{"actions": [], "creation": "2013-03-08 15:36:46", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["reference_type", "reference_name", "remarks", "reference_row", "col_break1", "advance_amount", "allocated_amount", "exchange_gain_loss", "ref_exchange_rate"], "fields": [{"fieldname": "reference_type", "fieldtype": "Link", "label": "Reference Type", "no_copy": 1, "oldfieldname": "journal_voucher", "oldfieldtype": "Link", "options": "DocType", "print_width": "180px", "read_only": 1, "width": "180px"}, {"columns": 3, "fieldname": "reference_name", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Reference Name", "no_copy": 1, "options": "reference_type", "read_only": 1}, {"columns": 3, "fieldname": "remarks", "fieldtype": "Text", "in_list_view": 1, "label": "Remarks", "no_copy": 1, "oldfieldname": "remarks", "oldfieldtype": "Small Text", "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "reference_row", "fieldtype": "Data", "hidden": 1, "label": "Reference Row", "no_copy": 1, "oldfieldname": "jv_detail_no", "oldfieldtype": "Date", "print_hide": 1, "print_width": "80px", "read_only": 1, "width": "80px"}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"columns": 2, "fieldname": "advance_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Advance Amount", "no_copy": 1, "oldfieldname": "advance_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "party_account_currency", "print_width": "100px", "read_only": 1, "width": "100px"}, {"columns": 2, "fieldname": "allocated_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Allocated Amount", "no_copy": 1, "oldfieldname": "allocated_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "party_account_currency", "print_width": "100px", "width": "100px"}, {"depends_on": "exchange_gain_loss", "fieldname": "exchange_gain_loss", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Exchange Gain/Loss", "options": "Company:company:default_currency", "read_only": 1}, {"depends_on": "exchange_gain_loss", "fieldname": "ref_exchange_rate", "fieldtype": "Float", "label": "Reference Exchange Rate", "non_negative": 1, "read_only": 1}], "idx": 1, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2023-06-23 21:13:18.013816", "modified_by": "Administrator", "module": "Accounts", "name": "Purchase Invoice Advance", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}