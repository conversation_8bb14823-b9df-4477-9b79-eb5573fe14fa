{"actions": [], "creation": "2016-05-08 23:49:38.842621", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["default", "mode_of_payment", "amount", "reference_no", "column_break_3", "account", "type", "base_amount", "clearance_date"], "fields": [{"fieldname": "mode_of_payment", "fieldtype": "Link", "in_list_view": 1, "label": "Mode of Payment", "options": "Mode of Payment", "reqd": 1}, {"default": "0", "depends_on": "eval:parent.doctype == 'Sales Invoice'", "fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "options": "currency", "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "account", "fieldtype": "Link", "label": "Account", "options": "Account", "print_hide": 1, "read_only": 1}, {"fetch_from": "mode_of_payment.type", "fieldname": "type", "fieldtype": "Read Only", "label": "Type"}, {"fieldname": "base_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Base Amount (Company Currency)", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "clearance_date", "fieldtype": "Date", "label": "Clearance Date", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"default": "0", "fieldname": "default", "fieldtype": "Check", "hidden": 1, "label": "<PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "reference_no", "fieldtype": "Data", "label": "Reference No"}], "istable": 1, "links": [], "modified": "2024-01-23 16:20:06.436979", "modified_by": "Administrator", "module": "Accounts", "name": "Sales Invoice Payment", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}