{"actions": [], "allow_rename": 1, "creation": "2023-09-17 15:40:59.724177", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["posting_date", "subscription", "amended_from"], "fields": [{"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Process Subscription", "print_hide": 1, "read_only": 1}, {"fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "label": "Posting Date", "reqd": 1}, {"fieldname": "subscription", "fieldtype": "Link", "label": "Subscription", "options": "Subscription"}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2023-09-17 17:33:37.974166", "modified_by": "Administrator", "module": "Accounts", "name": "Process Subscription", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "submit": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "submit": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}