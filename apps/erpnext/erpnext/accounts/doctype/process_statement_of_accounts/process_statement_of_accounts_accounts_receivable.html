<style>
	.print-format {
		padding: 4mm;
		font-size: 8.0pt !important;
	}
	.print-format td {
		vertical-align:middle !important;
	}
	</style>

	<div id="header-html" class="hidden-pdf">
		{% if letter_head.content %}
		<div class="letter-head text-center">{{ letter_head.content }}</div>
		<hr style="height:2px;border-width:0;color:black;background-color:black;">
		{% endif %}
	</div>
	<div id="footer-html" class="visible-pdf">
		{% if letter_head.footer %}
		<div class="letter-head-footer">
			<hr style="border-width:0;color:black;background-color:black;padding-bottom:2px;">
			{{ letter_head.footer }}
		</div>
		{% endif %}
	</div>

	<h2 class="text-center" style="margin-top:0">{{ _(report.report_name) }}</h2>
	<h4 class="text-center">
		{{ filters.customer_name }}
	</h4>
	<h6 class="text-center">
		{% if (filters.tax_id) %}
		{{ _("Tax Id: ") }}{{ filters.tax_id }}
		{% endif %}
	</h6>
	<h5 class="text-center">
		{{ _(filters.ageing_based_on) }}
		{{ _("Until") }}
		{{ frappe.format(filters.report_date, 'Date') }}
	</h5>

	<div class="clearfix">
		<div class="pull-left">
		{% if(filters.payment_terms) %}
			<strong>{{ _("Payment Terms") }}:</strong> {{ filters.payment_terms }}
		{% endif %}
		</div>
		<div class="pull-right">
		{% if(filters.credit_limit) %}
			<strong>{{ _("Credit Limit") }}:</strong> {{ frappe.utils.fmt_money(filters.credit_limit) }}
		{% endif %}
		</div>
	</div>

	{% if(filters.show_future_payments) %}
		{% set balance_row = data.slice(-1).pop() %}
		{% for i in report.columns %}
			{% if i.fieldname == 'age' %}
				{% set elem = i %}
			{% endif %}
		{% endfor %}
		{% set start = report.columns.findIndex(elem) %}
		{% set range1 = report.columns[start].label %}
		{% set range2 = report.columns[start+1].label %}
		{% set range3 = report.columns[start+2].label %}
		{% set range4 = report.columns[start+3].label %}
		{% set range5 = report.columns[start+4].label %}
		{% set range6 = report.columns[start+5].label %}

		{% if(balance_row) %}
		<table class="table table-bordered table-condensed">
			<caption class="text-right">(Amount in {{ data[0]["currency"] ~ "" }})</caption>
				<colgroup>
					<col style="width: 30mm;">
					<col style="width: 18mm;">
					<col style="width: 18mm;">
					<col style="width: 18mm;">
					<col style="width: 18mm;">
					<col style="width: 18mm;">
					<col style="width: 18mm;">
					<col style="width: 18mm;">
				</colgroup>

			<thead>
				<tr>
					<th>{{ _(" ") }}</th>
					<th>{{ _(range1) }}</th>
					<th>{{ _(range2) }}</th>
					<th>{{ _(range3) }}</th>
					<th>{{ _(range4) }}</th>
					<th>{{ _(range5) }}</th>
					<th>{{ _(range6) }}</th>
					<th>{{ _("Total") }}</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td>{{ _("Total Outstanding") }}</td>
					<td class="text-right">
						{{ format_number(balance_row["age"], null, 2) }}
					</td>
					<td class="text-right">
						{{ frappe.utils.fmt_money(balance_row["range1"], data[data.length-1]["currency"]) }}
					</td>
					<td class="text-right">
						{{ frappe.utils.fmt_money(balance_row["range2"], data[data.length-1]["currency"]) }}
					</td>
					<td class="text-right">
						{{ frappe.utils.fmt_money(balance_row["range3"], data[data.length-1]["currency"]) }}
					</td>
					<td class="text-right">
						{{ frappe.utils.fmt_money(balance_row["range4"], data[data.length-1]["currency"]) }}
					</td>
					<td class="text-right">
						{{ frappe.utils.fmt_money(balance_row["range5"], data[data.length-1]["currency"]) }}
					</td>
					<td class="text-right">
						{{ frappe.utils.fmt_money(flt(balance_row["outstanding"]), data[data.length-1]["currency"]) }}
					</td>
				</tr>
					<td>{{ _("Future Payments") }}</td>
					<td></td>
					<td></td>
					<td></td>
					<td></td>
					<td></td>
					<td></td>
					<td class="text-right">
						{{ frappe.utils.fmt_money(flt(balance_row[("future_amount")]), data[data.length-1]["currency"]) }}
					</td>
				<tr class="cvs-footer">
					<th class="text-left">{{ _("Cheques Required") }}</th>
					<th></th>
					<th></th>
					<th></th>
					<th></th>
					<th></th>
					<th></th>
					<th class="text-right">
						{{ frappe.utils.fmt_money(flt(balance_row["outstanding"] - balance_row[("future_amount")]), data[data.length-1]["currency"]) }}</th>
				</tr>
			</tbody>

		</table>
		{% endif %}
	{% endif %}
	<table class="table table-bordered">
		<thead>
			<tr>
				{% if(report.report_name == "Accounts Receivable" or report.report_name == "Accounts Payable") %}
					<th style="width: 10%">{{ _("Date") }}</th>
					<th style="width: 4%">{{ _("Age (Days)") }}</th>

					{% if(report.report_name == "Accounts Receivable" and filters.show_sales_person) %}
						<th style="width: 14%">{{ _("Reference") }}</th>
						<th style="width: 10%">{{ _("Sales Person") }}</th>
					{% else %}
						<th style="width: 24%">{{ _("Reference") }}</th>
					{% endif %}
					{% if not(filters.show_future_payments) %}
						<th style="width: 20%">
						{% if (filters.customer or filters.supplier or filters.customer_name) %}
							{{ _("Remarks") }}
						{% else %}
							{{ _("Party") }}
						{% endif %}
						</th>
					{% endif %}
					<th style="width: 10%; text-align: right">{{ _("Invoiced Amount") }}</th>
					{% if not(filters.show_future_payments) %}
						<th style="width: 10%; text-align: right">{{ _("Paid Amount") }}</th>
						<th style="width: 10%; text-align: right">
							{% if report.report_name == "Accounts Receivable" %}
								{{ _('Credit Note') }}
							{% else %}
								{{ _('Debit Note') }}
							{% endif %}
						</th>
					{% endif %}
					<th style="width: 10%; text-align: right">{{ _("Outstanding Amount") }}</th>
					{% if(filters.show_future_payments) %}
						{% if(report.report_name == "Accounts Receivable") %}
							<th style="width: 12%">{{ _("Customer LPO No.") }}</th>
						{% endif %}
						<th style="width: 10%">{{ _("Future Payment Ref") }}</th>
						<th style="width: 10%">{{ _("Future Payment Amount") }}</th>
						<th style="width: 10%">{{ _("Remaining Balance") }}</th>
					{% endif %}
				{% else %}
					<th style="width: 40%">
						{% if (filters.customer or filters.supplier or filters.customer_name) %}
							{{ _("Remarks")}}
						{% else %}
							{{ _("Party") }}
						{% endif %}
					</th>
					<th style="width: 15%">{{ _("Total Invoiced Amount") }}</th>
					<th style="width: 15%">{{ _("Total Paid Amount") }}</th>
					<th style="width: 15%">
						{% if report.report_name == "Accounts Receivable Summary" %}
							{{ _('Credit Note Amount') }}
						{% else %}
							{{ _('Debit Note Amount') }}
						{% endif %}
					</th>
					<th style="width: 15%">{{ _("Total Outstanding Amount") }}</th>
				{% endif %}
			</tr>
		</thead>
		<tbody>
			{% for i in range(data|length) %}
				<tr>
				{% if(report.report_name == "Accounts Receivable" or report.report_name == "Accounts Payable") %}
					{% if(data[i]["party"]) %}
						<td>{{ (data[i]["posting_date"]) }}</td>
						<td style="text-align: right">{{ data[i]["age"] }}</td>
						<td>
							{% if not(filters.show_future_payments) %}
								{{ data[i]["voucher_type"] }}
								<br>
							{% endif %}
							{{ data[i]["voucher_no"] }}
						</td>

						{% if(report.report_name == "Accounts Receivable" and filters.show_sales_person) %}
						<td>{{ data[i]["sales_person"] }}</td>
						{% endif %}

						{% if not (filters.show_future_payments) %}
						<td>
							{% if(not(filters.customer or filters.supplier or filters.customer_name)) %}
								{{ data[i]["party"] }}
								{% if(data[i]["customer_name"] and data[i]["customer_name"] != data[i]["party"]) %}
									<br> {{ data[i]["customer_name"] }}
								{% elif(data[i]["supplier_name"] != data[i]["party"]) %}
									<br> {{ data[i]["supplier_name"] }}
								{% endif %}
							{% endif %}
							<div>
							{% if data[i]["remarks"] %}
								{{ _("Remarks") }}:
								{{ data[i]["remarks"] }}
							{% endif %}
							</div>
						</td>
						{% endif %}

						<td style="text-align: right">
							{{ frappe.utils.fmt_money(data[i]["invoiced"], currency=data[i]["currency"]) }}</td>

						{% if not(filters.show_future_payments) %}
							<td style="text-align: right">
								{{ frappe.utils.fmt_money(data[i]["paid"], currency=data[i]["currency"]) }}</td>
							<td style="text-align: right">
								{{ frappe.utils.fmt_money(data[i]["credit_note"], currency=data[i]["currency"]) }}</td>
						{% endif %}
						<td style="text-align: right">
							{{ frappe.utils.fmt_money(data[i]["outstanding"], currency=data[i]["currency"]) }}</td>

						{% if(filters.show_future_payments) %}
							{% if(report.report_name == "Accounts Receivable") %}
								<td style="text-align: right">
									{{ data[i]["po_no"] }}</td>
							{% endif %}
							<td style="text-align: right">{{ data[i]["future_ref"] }}</td>
							<td style="text-align: right">{{ frappe.utils.fmt_money(data[i]["future_amount"], currency=data[i]["currency"]) }}</td>
							<td style="text-align: right">{{ frappe.utils.fmt_money(data[i]["remaining_balance"], currency=data[i]["currency"]) }}</td>
						{% endif %}
					{% else %}
						<td></td>
						{% if not(filters.show_future_payments) %}
						<td></td>
						{% endif %}
						{% if(report.report_name == "Accounts Receivable" and filters.show_sales_person) %}
						<td></td>
						{% endif %}
						<td></td>
						<td style="text-align: right"><b>{{ _("Total") }}</b></td>
						<td style="text-align: right">
							{{ frappe.utils.fmt_money(data[i]["invoiced"], data[i]["currency"]) }}</td>

						{% if not(filters.show_future_payments) %}
							<td style="text-align: right">
								{{ frappe.utils.fmt_money(data[i]["paid"], currency=data[i]["currency"]) }}</td>
							<td style="text-align: right">{{ frappe.utils.fmt_money(data[i]["credit_note"], currency=data[i]["currency"]) }} </td>
						{% endif %}
						<td style="text-align: right">
							{{ frappe.utils.fmt_money(data[i]["outstanding"], currency=data[i]["currency"]) }}</td>

						{% if(filters.show_future_payments) %}
							{% if(report.report_name == "Accounts Receivable") %}
								<td style="text-align: right">
									{{ data[i]["po_no"] }}</td>
							{% endif %}
							<td style="text-align: right">{{ data[i]["future_ref"] }}</td>
							<td style="text-align: right">{{ frappe.utils.fmt_money(data[i]["future_amount"], currency=data[i]["currency"]) }}</td>
							<td style="text-align: right">{{ frappe.utils.fmt_money(data[i]["remaining_balance"], currency=data[i]["currency"]) }}</td>
						{% endif %}
					{% endif %}
				{% else %}
					{% if(data[i]["party"] or "&nbsp;") %}
						{% if not(data[i]["is_total_row"]) %}
							<td>
								{% if(not(filters.customer | filters.supplier)) %}
									{{ data[i]["party"] }}
									{% if(data[i]["customer_name"] and data[i]["customer_name"] != data[i]["party"]) %}
										<br> {{ data[i]["customer_name"] }}
									{% elif(data[i]["supplier_name"] != data[i]["party"]) %}
										<br> {{ data[i]["supplier_name"] }}
									{% endif %}
								{% endif %}
								<br>{{ _("Remarks") }}:
								{{ data[i]["remarks"] }}
							</td>
						{% else %}
							<td><b>{{ _("Total") }}</b></td>
						{% endif %}
						<td style="text-align: right">{{ frappe.utils.fmt_money(data[i]["invoiced"], currency=data[i]["currency"]) }}</td>
						<td style="text-align: right">{{ frappe.utils.fmt_money(data[i]["paid"], currency=data[i]["currency"]) }}</td>
						<td style="text-align: right">{{ frappe.utils.fmt_money(data[i]["credit_note"], currency=data[i]["currency"]) }}</td>
						<td style="text-align: right">{{ frappe.utils.fmt_money(data[i]["outstanding"], currency=data[i]["currency"]) }}</td>
					{% endif %}
				{% endif %}
				</tr>
			{% endfor %}
			<td></td>
			<td></td>
			<td></td>
			<td></td>
			<td style="text-align: right"><b>{{ frappe.utils.fmt_money(data|sum(attribute="invoiced"), currency=data[0]["currency"]) }}</b></td>
			<td style="text-align: right"><b>{{ frappe.utils.fmt_money(data|sum(attribute="paid"), currency=data[0]["currency"]) }}</b></td>
			<td style="text-align: right"><b>{{ frappe.utils.fmt_money(data|sum(attribute="credit_note"), currency=data[0]["currency"]) }}</b></td>
			<td style="text-align: right"><b>{{ frappe.utils.fmt_money(data|sum(attribute="outstanding"), currency=data[0]["currency"]) }}</b></td>
		</tbody>
	</table>
	<br>
	{% if ageing %}
	<h4 class="text-center">{{ _("Ageing Report based on ") }} {{ ageing.ageing_based_on }}
		{{ _("up to " ) }}  {{ frappe.format(filters.report_date, 'Date')}}
	</h4>
	<table class="table table-bordered">
		<thead>
			<tr>
				<th style="width: 25%">30 Days</th>
				<th style="width: 25%">60 Days</th>
				<th style="width: 25%">90 Days</th>
				<th style="width: 25%">120 Days</th>
			</tr>
		</thead>
		<tbody>
			<tr>
				<td>{{ frappe.utils.fmt_money(ageing.range1, currency=data[0]["currency"]) }}</td>
				<td>{{ frappe.utils.fmt_money(ageing.range2, currency=data[0]["currency"]) }}</td>
				<td>{{ frappe.utils.fmt_money(ageing.range3, currency=data[0]["currency"]) }}</td>
				<td>{{ frappe.utils.fmt_money(ageing.range4, currency=data[0]["currency"]) }}</td>
			</tr>
		</tbody>
	</table>
	{% endif %}
	{% if terms_and_conditions %}
	<div>
		{{ terms_and_conditions }}
	</div>
	{% endif %}
	<p class="text-right text-muted">{{ _("Printed On ") }}{{ frappe.utils.now() }}</p>
