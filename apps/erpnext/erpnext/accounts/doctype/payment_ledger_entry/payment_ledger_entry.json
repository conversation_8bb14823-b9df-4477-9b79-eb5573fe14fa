{"actions": [], "allow_rename": 1, "creation": "2022-05-09 19:35:03.334361", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["posting_date", "company", "account_type", "account", "party_type", "party", "due_date", "voucher_detail_no", "cost_center", "finance_book", "voucher_type", "voucher_no", "against_voucher_type", "against_voucher_no", "amount", "account_currency", "amount_in_account_currency", "delinked", "remarks"], "fields": [{"fieldname": "posting_date", "fieldtype": "Date", "label": "Posting Date", "search_index": 1}, {"fieldname": "account_type", "fieldtype": "Select", "label": "Account Type", "options": "Receivable\nPayable"}, {"fieldname": "account", "fieldtype": "Link", "label": "Account", "options": "Account", "search_index": 1}, {"fieldname": "party_type", "fieldtype": "Link", "label": "Party Type", "options": "DocType", "search_index": 1}, {"fieldname": "party", "fieldtype": "Dynamic Link", "label": "Party", "options": "party_type", "search_index": 1}, {"fieldname": "voucher_type", "fieldtype": "Link", "in_standard_filter": 1, "label": "Voucher Type", "options": "DocType", "search_index": 1}, {"fieldname": "voucher_no", "fieldtype": "Dynamic Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Voucher No", "options": "voucher_type", "search_index": 1}, {"fieldname": "against_voucher_type", "fieldtype": "Link", "in_standard_filter": 1, "label": "Against Voucher Type", "options": "DocType", "search_index": 1}, {"fieldname": "against_voucher_no", "fieldtype": "Dynamic Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Against Voucher No", "options": "against_voucher_type", "search_index": 1}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "options": "Company:company:default_currency"}, {"fieldname": "account_currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "amount_in_account_currency", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount in Account <PERSON><PERSON><PERSON>cy", "options": "account_currency"}, {"default": "0", "fieldname": "delinked", "fieldtype": "Check", "in_list_view": 1, "label": "DeLinked"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "search_index": 1}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"fieldname": "due_date", "fieldtype": "Date", "label": "Due Date"}, {"fieldname": "finance_book", "fieldtype": "Link", "label": "Finance Book", "options": "Finance Book"}, {"fieldname": "remarks", "fieldtype": "Text", "label": "Remarks"}, {"fieldname": "voucher_detail_no", "fieldtype": "Data", "label": "Voucher Detail No", "search_index": 1}], "in_create": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2023-11-03 16:39:58.904113", "modified_by": "Administrator", "module": "Accounts", "name": "Payment Ledger Entry", "owner": "Administrator", "permissions": [{"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Auditor", "share": 1}], "search_fields": "voucher_no, against_voucher_no", "sort_field": "modified", "sort_order": "DESC", "states": []}