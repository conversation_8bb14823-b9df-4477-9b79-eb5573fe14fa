{"actions": [], "autoname": "naming_series:", "creation": "2017-12-25 16:50:53.878430", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["title", "column_break_2", "naming_series", "section_break_2", "folio_no", "column_break_4", "company", "is_company", "address_contacts", "address_html", "column_break_9", "contact_html", "section_break_3", "share_balance", "contact_list"], "fields": [{"fieldname": "title", "fieldtype": "Data", "label": "Title", "reqd": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "naming_series", "fieldtype": "Select", "options": "ACC-SH-.YYYY.-"}, {"fieldname": "section_break_2", "fieldtype": "Section Break"}, {"fieldname": "folio_no", "fieldtype": "Data", "label": "Folio no.", "read_only": 1, "unique": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "label": "Company", "options": "Company", "reqd": 1}, {"default": "0", "fieldname": "is_company", "fieldtype": "Check", "hidden": 1, "label": "Is Company", "read_only": 1}, {"fieldname": "address_contacts", "fieldtype": "Section Break", "label": "Address and Contacts", "options": "fa fa-map-marker"}, {"fieldname": "address_html", "fieldtype": "HTML", "label": "Address HTML", "read_only": 1}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "contact_html", "fieldtype": "HTML", "label": "Contact HTML", "read_only": 1}, {"fieldname": "section_break_3", "fieldtype": "Section Break", "label": "Share Balance"}, {"fieldname": "share_balance", "fieldtype": "Table", "label": "Share Balance", "options": "Share Balance", "read_only": 1}, {"description": "Hidden list maintaining the list of contacts linked to Shareholder", "fieldname": "contact_list", "fieldtype": "Code", "hidden": 1, "label": "Contact List", "read_only": 1}], "links": [], "modified": "2023-04-10 22:02:20.406087", "modified_by": "Administrator", "module": "Accounts", "name": "Shareholder", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}], "search_fields": "folio_no", "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "title", "track_changes": 1}