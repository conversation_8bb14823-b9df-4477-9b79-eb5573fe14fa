[{"doctype": "Payment Term", "due_date_based_on": "Day(s) after invoice date", "payment_term_name": "_Test N30", "description": "_Test Net 30 Days", "invoice_portion": 50, "credit_days": 30}, {"doctype": "Payment Term", "due_date_based_on": "Day(s) after invoice date", "payment_term_name": "_Test COD", "description": "_Test Cash on Delivery", "invoice_portion": 50, "credit_days": 0}, {"doctype": "Payment Term", "due_date_based_on": "Month(s) after the end of the invoice month", "payment_term_name": "_Test EONM", "description": "_Test End of Next Month", "invoice_portion": 100, "credit_months": 1}, {"doctype": "Payment Term", "due_date_based_on": "Day(s) after invoice date", "payment_term_name": "_Test N30 1", "description": "_Test Net 30 Days", "invoice_portion": 100, "credit_days": 30}]