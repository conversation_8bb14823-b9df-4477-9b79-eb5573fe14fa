{"actions": [], "allow_rename": 1, "autoname": "field:plan_name", "creation": "2018-02-24 11:31:23.066506", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["plan_name", "currency", "column_break_3", "item", "section_break_5", "price_determination", "column_break_7", "cost", "price_list", "section_break_11", "billing_interval", "column_break_13", "billing_interval_count", "payment_plan_section", "product_price_id", "column_break_16", "payment_gateway", "accounting_dimensions_section", "cost_center", "dimension_col_break"], "fields": [{"fieldname": "plan_name", "fieldtype": "Data", "in_list_view": 1, "label": "Plan Name", "reqd": 1, "unique": 1}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>", "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "item", "fieldtype": "Link", "in_list_view": 1, "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>", "reqd": 1}, {"fieldname": "section_break_5", "fieldtype": "Section Break"}, {"fieldname": "price_determination", "fieldtype": "Select", "label": "Subscription Price Based On", "options": "\nFixed Rate\nBased On Price List\nMonthly Rate", "reqd": 1}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"depends_on": "eval:['Fixed Rate', 'Monthly Rate'].includes(doc.price_determination)", "fieldname": "cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Cost", "options": "currency"}, {"depends_on": "eval:doc.price_determination==\"Based On Price List\"", "fieldname": "price_list", "fieldtype": "Link", "label": "Price List", "options": "Price List"}, {"fieldname": "section_break_11", "fieldtype": "Section Break"}, {"default": "Day", "fieldname": "billing_interval", "fieldtype": "Select", "in_list_view": 1, "label": "Billing Interval", "options": "Day\nWeek\nMonth\nYear", "reqd": 1}, {"fieldname": "column_break_13", "fieldtype": "Column Break"}, {"default": "1", "description": "Number of intervals for the interval field e.g if Interval is 'Days' and Billing Interval Count is 3, invoices will be generated every 3 days", "fieldname": "billing_interval_count", "fieldtype": "Int", "label": "Billing Interval Count", "reqd": 1}, {"fieldname": "payment_plan_section", "fieldtype": "Section Break", "label": "Payment Plan"}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"fieldname": "payment_gateway", "fieldtype": "Link", "label": "Payment Gateway", "options": "Payment Gateway Account"}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"fieldname": "product_price_id", "fieldtype": "Data", "label": "Product Price ID"}], "links": [], "modified": "2024-01-14 17:59:34.687977", "modified_by": "Administrator", "module": "Accounts", "name": "Subscription Plan", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}