{"actions": [], "allow_rename": 1, "creation": "2022-10-19 21:59:33.553852", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["filters_section", "company", "posting_date", "column_break_4", "voucher_type", "add_manually", "status_section", "repost_status", "repost_error_log", "selected_vouchers_section", "repost_vouchers", "amended_from"], "fields": [{"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "label": "Posting Date", "reqd": 1}, {"fieldname": "voucher_type", "fieldtype": "Link", "label": "Voucher Type", "options": "DocType"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Repost Payment Ledger", "print_hide": 1, "read_only": 1}, {"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "selected_vouchers_section", "fieldtype": "Section Break", "label": "Vouchers"}, {"fieldname": "filters_section", "fieldtype": "Section Break", "label": "Filters"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "repost_vouchers", "fieldtype": "Table", "label": "Selected Vouchers", "options": "Repost Payment Ledger Items"}, {"fieldname": "repost_status", "fieldtype": "Select", "label": "Repost Status", "options": "\nQueued\nFailed\nCompleted", "read_only": 1}, {"fieldname": "status_section", "fieldtype": "Section Break", "label": "Status"}, {"default": "0", "description": "Ignore Voucher Type filter and Select Vouchers Manually", "fieldname": "add_manually", "fieldtype": "Check", "label": "Add Manually"}, {"depends_on": "eval:doc.repost_error_log", "fieldname": "repost_error_log", "fieldtype": "Long Text", "label": "Repost Error Log"}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2023-09-26 14:21:35.719727", "modified_by": "Administrator", "module": "Accounts", "name": "Repost Payment Ledger", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "submit": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}, {"email": 1, "export": 1, "permlevel": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}