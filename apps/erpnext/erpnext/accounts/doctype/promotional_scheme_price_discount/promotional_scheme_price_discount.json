{"actions": [], "creation": "2019-03-24 14:48:59.649168", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["disable", "apply_multiple_pricing_rules", "column_break_2", "rule_description", "section_break_2", "min_qty", "max_qty", "column_break_3", "min_amount", "max_amount", "section_break_6", "rate_or_discount", "column_break_10", "rate", "discount_amount", "discount_percentage", "section_break_11", "warehouse", "threshold_percentage", "validate_applied_rule", "column_break_14", "priority", "apply_discount_on_rate"], "fields": [{"default": "0", "fieldname": "disable", "fieldtype": "Check", "label": "Disable"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "rule_description", "fieldtype": "Small Text", "label": "Rule Description", "no_copy": 1, "reqd": 1}, {"fieldname": "section_break_2", "fieldtype": "Section Break"}, {"columns": 1, "default": "0", "fieldname": "min_qty", "fieldtype": "Float", "in_list_view": 1, "label": "<PERSON>"}, {"columns": 1, "default": "0", "fieldname": "max_qty", "fieldtype": "Float", "in_list_view": 1, "label": "<PERSON>"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "min_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "<PERSON>"}, {"default": "0", "fieldname": "max_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "<PERSON>"}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"default": "Discount Percentage", "fieldname": "rate_or_discount", "fieldtype": "Select", "in_list_view": 1, "label": "Discount Type", "options": "\nRate\nDiscount Percentage\nDiscount Amount"}, {"fieldname": "column_break_10", "fieldtype": "Column Break"}, {"columns": 2, "depends_on": "eval:doc.rate_or_discount==\"Rate\"", "fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate"}, {"depends_on": "eval:doc.rate_or_discount==\"Discount Amount\"", "fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Discount Amount"}, {"depends_on": "eval:doc.rate_or_discount==\"Discount Percentage\"", "fieldname": "discount_percentage", "fieldtype": "Float", "label": "Discount Percentage"}, {"fieldname": "section_break_11", "fieldtype": "Section Break"}, {"fieldname": "warehouse", "fieldtype": "Link", "label": "Warehouse", "options": "Warehouse"}, {"fieldname": "threshold_percentage", "fieldtype": "Percent", "label": "T<PERSON><PERSON><PERSON> for Suggestion"}, {"default": "0", "fieldname": "validate_applied_rule", "fieldtype": "Check", "label": "Validate Applied Rule"}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"fieldname": "priority", "fieldtype": "Select", "label": "Priority", "options": "\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20"}, {"default": "0", "depends_on": "priority", "fieldname": "apply_multiple_pricing_rules", "fieldtype": "Check", "label": "Apply Multiple Pricing Rules"}, {"default": "0", "depends_on": "eval:in_list(['Discount Percentage', 'Discount Amount'], doc.rate_or_discount) && doc.apply_multiple_pricing_rules", "fieldname": "apply_discount_on_rate", "fieldtype": "Check", "label": "Apply Discount on Rate"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2021-11-16 00:25:33.843996", "modified_by": "Administrator", "module": "Accounts", "name": "Promotional Scheme Price Discount", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC"}