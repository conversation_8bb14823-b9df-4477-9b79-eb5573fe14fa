{"actions": [], "autoname": "hash", "creation": "2013-06-04 11:02:19", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["barcode", "has_item_scanned", "item_code", "col_break1", "item_name", "customer_item_code", "description_section", "description", "item_group", "brand", "image_section", "image", "image_view", "quantity_and_rate", "qty", "stock_uom", "col_break2", "uom", "conversion_factor", "stock_qty", "section_break_17", "price_list_rate", "base_price_list_rate", "discount_and_margin", "margin_type", "margin_rate_or_amount", "rate_with_margin", "column_break_19", "discount_percentage", "discount_amount", "base_rate_with_margin", "section_break1", "rate", "amount", "item_tax_template", "col_break3", "base_rate", "base_amount", "pricing_rules", "stock_uom_rate", "is_free_item", "grant_commission", "section_break_21", "net_rate", "net_amount", "column_break_24", "base_net_rate", "base_net_amount", "drop_ship", "delivered_by_supplier", "accounting", "income_account", "is_fixed_asset", "asset", "finance_book", "col_break4", "expense_account", "discount_account", "deferred_revenue", "deferred_revenue_account", "service_stop_date", "enable_deferred_revenue", "column_break_50", "service_start_date", "service_end_date", "section_break_18", "weight_per_unit", "total_weight", "column_break_21", "weight_uom", "warehouse_and_reference", "warehouse", "target_warehouse", "quality_inspection", "pick_serial_and_batch", "serial_and_batch_bundle", "use_serial_batch_fields", "col_break5", "allow_zero_valuation_rate", "incoming_rate", "item_tax_rate", "actual_batch_qty", "actual_qty", "section_break_eoec", "serial_no", "column_break_ytgd", "batch_no", "edit_references", "sales_order", "so_detail", "sales_invoice_item", "column_break_74", "delivery_note", "dn_detail", "delivered_qty", "internal_transfer_section", "purchase_order", "column_break_92", "purchase_order_item", "accounting_dimensions_section", "cost_center", "dimension_col_break", "project", "section_break_54", "page_break"], "fields": [{"fieldname": "barcode", "fieldtype": "Data", "label": "Barcode", "print_hide": 1}, {"bold": 1, "columns": 4, "fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "<PERSON><PERSON>", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "search_index": 1}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"fieldname": "item_name", "fieldtype": "Data", "in_global_search": 1, "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data", "print_hide": 1, "reqd": 1}, {"fieldname": "customer_item_code", "fieldtype": "Data", "hidden": 1, "label": "Customer's Item Code", "print_hide": 1, "read_only": 1}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Text", "print_width": "200px", "width": "200px"}, {"fieldname": "image_view", "fieldtype": "Image", "label": "Image View", "options": "image", "print_hide": 1}, {"fetch_from": "item_code.image", "fieldname": "image", "fieldtype": "Attach", "hidden": 1, "label": "Image"}, {"fieldname": "quantity_and_rate", "fieldtype": "Section Break"}, {"bold": 1, "columns": 2, "fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity", "oldfieldname": "qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "options": "UOM", "read_only": 1}, {"fieldname": "col_break2", "fieldtype": "Column Break"}, {"fieldname": "uom", "fieldtype": "Link", "label": "UOM", "options": "UOM", "reqd": 1}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "conversion_factor", "fieldtype": "Float", "label": "UOM Conversion Factor", "print_hide": 1, "reqd": 1}, {"depends_on": "eval:doc.uom != doc.stock_uom", "fieldname": "stock_qty", "fieldtype": "Float", "label": "Qty as per Stock UOM", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_17", "fieldtype": "Section Break"}, {"fieldname": "price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price List Rate", "oldfieldname": "ref_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_price_list_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price List Rate (Company Currency)", "oldfieldname": "base_ref_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "eval: doc.margin_type || doc.discount_amount", "fieldname": "discount_and_margin", "fieldtype": "Section Break", "label": "Discount and Margin"}, {"depends_on": "price_list_rate", "fieldname": "margin_type", "fieldtype": "Select", "label": "Margin Type", "options": "\nPercentage\nAmount", "print_hide": 1}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate", "fieldname": "margin_rate_or_amount", "fieldtype": "Float", "label": "Margin Rate or Amount", "print_hide": 1}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate && doc.margin_rate_or_amount", "fieldname": "rate_with_margin", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate With Margin", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_19", "fieldtype": "Column Break"}, {"depends_on": "price_list_rate", "fieldname": "discount_percentage", "fieldtype": "Percent", "label": "Discount (%) on Price List Rate with <PERSON><PERSON>", "oldfieldname": "adj_rate", "oldfieldtype": "Float", "print_hide": 1}, {"depends_on": "price_list_rate", "fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Discount Amount", "options": "currency"}, {"depends_on": "eval:doc.margin_type && doc.price_list_rate && doc.margin_rate_or_amount", "fieldname": "base_rate_with_margin", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate With Margin (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break1", "fieldtype": "Section Break"}, {"bold": 1, "columns": 2, "fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "oldfieldname": "export_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "reqd": 1}, {"columns": 2, "fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "oldfieldname": "export_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1, "reqd": 1}, {"fieldname": "col_break3", "fieldtype": "Column Break"}, {"fieldname": "base_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate (Company Currency)", "oldfieldname": "basic_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "reqd": 1}, {"fieldname": "base_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount (Company Currency)", "oldfieldname": "amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1, "reqd": 1}, {"fieldname": "pricing_rules", "fieldtype": "Small Text", "hidden": 1, "label": "Pricing Rules", "print_hide": 1, "read_only": 1}, {"default": "0", "fieldname": "is_free_item", "fieldtype": "Check", "label": "Is Free Item", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_21", "fieldtype": "Section Break"}, {"fieldname": "net_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Rate", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Amount", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_24", "fieldtype": "Column Break"}, {"fieldname": "base_net_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Rate (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "eval:doc.delivered_by_supplier==1", "fieldname": "drop_ship", "fieldtype": "Section Break", "label": "Drop Ship"}, {"default": "0", "fieldname": "delivered_by_supplier", "fieldtype": "Check", "label": "Delivered By Supplier", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "accounting", "fieldtype": "Section Break", "label": "Accounting Details"}, {"allow_on_submit": 1, "fieldname": "income_account", "fieldtype": "Link", "label": "Income Account", "oldfieldname": "income_account", "oldfieldtype": "Link", "options": "Account", "print_hide": 1, "print_width": "120px", "reqd": 1, "width": "120px"}, {"allow_on_submit": 1, "fieldname": "expense_account", "fieldtype": "Link", "label": "Expense Account", "options": "Account", "print_hide": 1, "width": "120px"}, {"fieldname": "col_break4", "fieldtype": "Column Break"}, {"fieldname": "item_tax_template", "fieldtype": "Link", "label": "Item Tax Template", "options": "Item Tax Template", "print_hide": 1}, {"allow_on_submit": 1, "default": ":Company", "fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "oldfieldname": "cost_center", "oldfieldtype": "Link", "options": "Cost Center", "print_hide": 1, "print_width": "120px", "reqd": 1, "width": "120px"}, {"collapsible": 1, "collapsible_depends_on": "enable_deferred_revenue", "fieldname": "deferred_revenue", "fieldtype": "Section Break", "label": "Deferred Revenue"}, {"depends_on": "enable_deferred_revenue", "fieldname": "deferred_revenue_account", "fieldtype": "Link", "label": "Deferred Revenue Account", "options": "Account"}, {"allow_on_submit": 1, "depends_on": "enable_deferred_revenue", "fieldname": "service_stop_date", "fieldtype": "Date", "label": "Service Stop Date", "no_copy": 1}, {"default": "0", "fieldname": "enable_deferred_revenue", "fieldtype": "Check", "label": "Enable Deferred Revenue"}, {"fieldname": "column_break_50", "fieldtype": "Column Break"}, {"depends_on": "enable_deferred_revenue", "fieldname": "service_start_date", "fieldtype": "Date", "label": "Service Start Date", "no_copy": 1}, {"depends_on": "enable_deferred_revenue", "fieldname": "service_end_date", "fieldtype": "Date", "label": "Service End Date", "no_copy": 1}, {"collapsible": 1, "fieldname": "section_break_18", "fieldtype": "Section Break", "label": "Item Weight Details"}, {"fieldname": "weight_per_unit", "fieldtype": "Float", "label": "Weight Per Unit", "print_hide": 1, "read_only": 1}, {"fieldname": "total_weight", "fieldtype": "Float", "label": "Total Weight", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_21", "fieldtype": "Column Break"}, {"fieldname": "weight_uom", "fieldtype": "Link", "label": "Weight UOM", "options": "UOM", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "eval:doc.serial_no || doc.batch_no", "fieldname": "warehouse_and_reference", "fieldtype": "Section Break", "label": "Stock Details"}, {"fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Warehouse", "oldfieldname": "warehouse", "oldfieldtype": "Link", "options": "Warehouse", "print_hide": 1}, {"depends_on": "eval: parent.is_internal_customer && parent.update_stock", "fieldname": "target_warehouse", "fieldtype": "Link", "hidden": 1, "ignore_user_permissions": 1, "label": "Target Warehouse", "no_copy": 1, "options": "Warehouse", "print_hide": 1}, {"depends_on": "eval:!doc.__islocal", "fieldname": "quality_inspection", "fieldtype": "Link", "label": "Quality Inspection", "options": "Quality Inspection"}, {"depends_on": "eval: doc.use_serial_batch_fields === 1 && parent.update_stock === 1", "fieldname": "batch_no", "fieldtype": "Link", "label": "Batch No", "options": "<PERSON><PERSON>", "search_index": 1}, {"fieldname": "col_break5", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "allow_zero_valuation_rate", "fieldtype": "Check", "label": "Allow Zero Valuation Rate", "no_copy": 1, "print_hide": 1}, {"depends_on": "eval: doc.use_serial_batch_fields === 1 && parent.update_stock === 1", "fieldname": "serial_no", "fieldtype": "Text", "label": "Serial No", "oldfieldname": "serial_no", "oldfieldtype": "Small Text"}, {"fieldname": "item_group", "fieldtype": "Link", "hidden": 1, "label": "Item Group", "oldfieldname": "item_group", "oldfieldtype": "Link", "options": "Item Group", "print_hide": 1, "read_only": 1}, {"fieldname": "brand", "fieldtype": "Data", "hidden": 1, "label": "Brand Name", "oldfieldname": "brand", "oldfieldtype": "Data", "print_hide": 1}, {"fieldname": "item_tax_rate", "fieldtype": "Small Text", "hidden": 1, "label": "Item Tax Rate", "oldfieldname": "item_tax_rate", "oldfieldtype": "Small Text", "print_hide": 1, "read_only": 1}, {"allow_on_submit": 1, "fieldname": "actual_batch_qty", "fieldtype": "Float", "label": "Available Batch Qty at Warehouse", "no_copy": 1, "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"allow_on_submit": 1, "fieldname": "actual_qty", "fieldtype": "Float", "label": "Available Qty at Warehouse", "oldfieldname": "actual_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "edit_references", "fieldtype": "Section Break", "label": "References"}, {"fieldname": "sales_order", "fieldtype": "Link", "label": "Sales Order", "no_copy": 1, "oldfieldname": "sales_order", "oldfieldtype": "Link", "options": "Sales Order", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "so_detail", "fieldtype": "Data", "hidden": 1, "label": "Sales Order Item", "no_copy": 1, "oldfieldname": "so_detail", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "column_break_74", "fieldtype": "Column Break"}, {"fieldname": "delivery_note", "fieldtype": "Link", "label": "Delivery Note", "no_copy": 1, "oldfieldname": "delivery_note", "oldfieldtype": "Link", "options": "Delivery Note", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "dn_detail", "fieldtype": "Data", "hidden": 1, "label": "Delivery Note Item", "no_copy": 1, "oldfieldname": "dn_detail", "oldfieldtype": "Data", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "delivered_qty", "fieldtype": "Float", "label": "Delivered <PERSON><PERSON>", "oldfieldname": "delivered_qty", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"default": "0", "fieldname": "is_fixed_asset", "fieldtype": "Check", "hidden": 1, "label": "Is Fixed Asset", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "asset", "fieldtype": "Link", "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>"}, {"fieldname": "section_break_54", "fieldtype": "Section Break"}, {"allow_on_submit": 1, "default": "0", "fieldname": "page_break", "fieldtype": "Check", "label": "Page Break", "no_copy": 1, "print_hide": 1, "report_hide": 1}, {"collapsible": 1, "fieldname": "description_section", "fieldtype": "Section Break", "label": "Description"}, {"collapsible": 1, "fieldname": "image_section", "fieldtype": "Section Break", "label": "Image"}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"depends_on": "asset", "fieldname": "finance_book", "fieldtype": "Link", "label": "Finance Book", "options": "Finance Book"}, {"allow_on_submit": 1, "fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project"}, {"depends_on": "eval:parent.update_stock == 1", "fieldname": "sales_invoice_item", "fieldtype": "Data", "ignore_user_permissions": 1, "label": "Sales Invoice Item", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"depends_on": "eval:parent.is_return && parent.update_stock && !parent.return_against", "fieldname": "incoming_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Incoming Rate (Costing)", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1}, {"depends_on": "eval: doc.uom != doc.stock_uom", "fieldname": "stock_uom_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate of Stock UOM", "no_copy": 1, "options": "currency", "read_only": 1}, {"allow_on_submit": 1, "fieldname": "discount_account", "fieldtype": "Link", "label": "Discount Account", "options": "Account"}, {"default": "0", "fetch_from": "item_code.grant_commission", "fieldname": "grant_commission", "fieldtype": "Check", "label": "Grant Commission", "read_only": 1}, {"collapsible": 1, "depends_on": "eval:parent.is_internal_customer == 1", "fieldname": "internal_transfer_section", "fieldtype": "Section Break", "label": "Internal Transfer"}, {"fieldname": "purchase_order", "fieldtype": "Link", "label": "Purchase Order", "options": "Purchase Order", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_92", "fieldtype": "Column Break"}, {"fieldname": "purchase_order_item", "fieldtype": "Data", "label": "Purchase Order Item", "print_hide": 1, "read_only": 1}, {"default": "0", "depends_on": "barcode", "fieldname": "has_item_scanned", "fieldtype": "Check", "label": "<PERSON> <PERSON><PERSON>", "read_only": 1}, {"depends_on": "eval:parent.update_stock == 1 && (doc.use_serial_batch_fields === 0 || doc.docstatus === 1)", "fieldname": "serial_and_batch_bundle", "fieldtype": "Link", "label": "Serial and Batch Bundle", "no_copy": 1, "options": "Serial and Batch Bundle", "print_hide": 1, "search_index": 1}, {"depends_on": "eval:parent.update_stock === 1", "fieldname": "pick_serial_and_batch", "fieldtype": "<PERSON><PERSON>", "label": "Pick Serial / Batch No"}, {"default": "0", "fieldname": "use_serial_batch_fields", "fieldtype": "Check", "label": "Use Serial No / Batch Fields"}, {"depends_on": "eval:doc.use_serial_batch_fields === 1 && parent.update_stock === 1", "fieldname": "section_break_eoec", "fieldtype": "Section Break"}, {"fieldname": "column_break_ytgd", "fieldtype": "Column Break"}], "idx": 1, "istable": 1, "links": [], "modified": "2024-02-25 15:56:44.828634", "modified_by": "Administrator", "module": "Accounts", "name": "Sales Invoice Item", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}