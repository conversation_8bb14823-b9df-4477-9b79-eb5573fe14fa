{"actions": [], "autoname": "POS-CLO-.YYYY.-.#####", "creation": "2018-05-28 19:06:40.830043", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["period_details_section", "period_start_date", "period_end_date", "column_break_3", "posting_date", "posting_time", "pos_opening_entry", "status", "section_break_5", "company", "column_break_7", "pos_profile", "user", "section_break_12", "pos_transactions", "section_break_9", "payment_reconciliation_details", "section_break_11", "payment_reconciliation", "section_break_13", "grand_total", "net_total", "total_quantity", "column_break_16", "taxes", "failure_description_section", "error_message", "section_break_14", "amended_from"], "fields": [{"fetch_from": "pos_opening_entry.period_start_date", "fieldname": "period_start_date", "fieldtype": "Datetime", "in_list_view": 1, "label": "Period Start Date", "read_only": 1, "reqd": 1}, {"default": "Today", "fieldname": "period_end_date", "fieldtype": "Datetime", "in_list_view": 1, "label": "Period End Date", "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "label": "Posting Date", "reqd": 1}, {"fieldname": "section_break_5", "fieldtype": "Section Break", "label": "User Details"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fetch_from": "pos_opening_entry.pos_profile", "fieldname": "pos_profile", "fieldtype": "Link", "in_list_view": 1, "label": "POS Profile", "options": "POS Profile", "reqd": 1}, {"fetch_from": "pos_opening_entry.user", "fieldname": "user", "fieldtype": "Link", "label": "Cashier", "options": "User", "reqd": 1}, {"fieldname": "section_break_9", "fieldtype": "Section Break", "read_only": 1}, {"depends_on": "eval:doc.docstatus==1", "fieldname": "payment_reconciliation_details", "fieldtype": "HTML"}, {"fieldname": "section_break_11", "fieldtype": "Section Break", "label": "Modes of Payment"}, {"fieldname": "payment_reconciliation", "fieldtype": "Table", "label": "Payment Reconciliation", "options": "POS Closing Entry Detail"}, {"collapsible": 1, "collapsible_depends_on": "eval:doc.docstatus==0", "fieldname": "section_break_13", "fieldtype": "Section Break", "label": "Totals"}, {"default": "0", "fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Grand Total", "read_only": 1}, {"default": "0", "fieldname": "net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total", "read_only": 1}, {"fieldname": "total_quantity", "fieldtype": "Float", "label": "Total Quantity", "read_only": 1}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"fieldname": "taxes", "fieldtype": "Table", "label": "Taxes", "options": "POS Closing Entry Taxes", "read_only": 1}, {"fieldname": "section_break_12", "fieldtype": "Section Break", "label": "Linked Invoices"}, {"fieldname": "section_break_14", "fieldtype": "Section Break"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "POS Closing Entry", "print_hide": 1, "read_only": 1}, {"fieldname": "pos_transactions", "fieldtype": "Table", "label": "POS Transactions", "options": "POS Invoice Reference"}, {"fieldname": "pos_opening_entry", "fieldtype": "Link", "label": "POS Opening Entry", "options": "POS Opening Entry", "reqd": 1}, {"allow_on_submit": 1, "default": "Draft", "fieldname": "status", "fieldtype": "Select", "hidden": 1, "label": "Status", "options": "Draft\nSubmitted\nQueued\nFailed\nCancelled", "print_hide": 1, "read_only": 1}, {"fieldname": "period_details_section", "fieldtype": "Section Break", "label": "Period Details"}, {"collapsible": 1, "collapsible_depends_on": "error_message", "depends_on": "error_message", "fieldname": "failure_description_section", "fieldtype": "Section Break", "label": "Failure Description"}, {"depends_on": "error_message", "fieldname": "error_message", "fieldtype": "Small Text", "label": "Error", "read_only": 1}, {"default": "Now", "fieldname": "posting_time", "fieldtype": "Time", "label": "Posting Time", "no_copy": 1, "reqd": 1}], "is_submittable": 1, "links": [{"link_doctype": "POS Invoice Merge Log", "link_fieldname": "pos_closing_entry"}], "modified": "2023-08-10 16:25:49.322697", "modified_by": "Administrator", "module": "Accounts", "name": "POS Closing Entry", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"cancel": 1, "create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "submit": 1, "write": 1}, {"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Administrator", "share": 1, "submit": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}