{"actions": [], "autoname": "hash", "creation": "2013-05-21 16:16:04", "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "engine": "InnoDB", "field_order": ["category", "add_deduct_tax", "charge_type", "row_id", "included_in_print_rate", "included_in_paid_amount", "col_break1", "account_head", "description", "section_break_10", "rate", "accounting_dimensions_section", "cost_center", "dimension_col_break", "section_break_9", "account_currency", "tax_amount", "tax_amount_after_discount_amount", "total", "column_break_14", "base_tax_amount", "base_total", "base_tax_amount_after_discount_amount", "item_wise_tax_detail"], "fields": [{"default": "Total", "fieldname": "category", "fieldtype": "Select", "label": "Consider Tax or Charge for", "oldfieldname": "category", "oldfieldtype": "Select", "options": "Valuation and Total\nValuation\nTotal", "reqd": 1}, {"default": "Add", "fieldname": "add_deduct_tax", "fieldtype": "Select", "label": "Add or Deduct", "oldfieldname": "add_deduct_tax", "oldfieldtype": "Select", "options": "Add\nDeduct", "reqd": 1}, {"columns": 2, "default": "On Net Total", "fieldname": "charge_type", "fieldtype": "Select", "in_list_view": 1, "label": "Type", "oldfieldname": "charge_type", "oldfieldtype": "Select", "options": "\nActual\nOn Net Total\nOn Previous Row Amount\nOn Previous Row Total\nOn Item Quantity", "reqd": 1}, {"depends_on": "eval:[\"On Previous Row Amount\", \"On Previous Row Total\"].indexOf(doc.charge_type)!==-1", "fieldname": "row_id", "fieldtype": "Data", "label": "Reference Row #", "oldfieldname": "row_id", "oldfieldtype": "Data"}, {"default": "0", "description": "If checked, the tax amount will be considered as already included in the Print Rate / Print Amount", "fieldname": "included_in_print_rate", "fieldtype": "Check", "label": "Is this Tax included in Basic Rate?", "report_hide": 1}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "columns": 2, "fieldname": "account_head", "fieldtype": "Link", "in_list_view": 1, "label": "Account Head", "oldfieldname": "account_head", "oldfieldtype": "Link", "options": "Account", "reqd": 1}, {"allow_on_submit": 1, "default": ":Company", "fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "oldfieldname": "cost_center", "oldfieldtype": "Link", "options": "Cost Center"}, {"fieldname": "description", "fieldtype": "Small Text", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Small Text", "print_width": "300px", "reqd": 1, "width": "300px"}, {"fieldname": "section_break_10", "fieldtype": "Section Break"}, {"columns": 2, "fieldname": "rate", "fieldtype": "Float", "in_list_view": 1, "label": "Tax Rate", "oldfieldname": "rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "section_break_9", "fieldtype": "Section Break"}, {"columns": 2, "fieldname": "tax_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "oldfieldname": "tax_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency"}, {"fieldname": "tax_amount_after_discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Tax Amount After Discount Amount", "options": "currency", "print_hide": 1, "read_only": 1}, {"columns": 2, "fieldname": "total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Total", "oldfieldname": "total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"fieldname": "base_tax_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "Total (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1}, {"fieldname": "base_tax_amount_after_discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Tax Amount After Discount Amount", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "item_wise_tax_detail", "fieldtype": "Code", "hidden": 1, "label": "Item Wise Tax Detail ", "oldfieldname": "item_wise_tax_detail", "oldfieldtype": "Small Text", "print_hide": 1, "read_only": 1}, {"fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"default": "0", "depends_on": "eval:['Purchase Taxes and Charges Template', 'Payment Entry'].includes(parent.doctype)", "description": "If checked, the tax amount will be considered as already included in the Paid Amount in Payment Entry", "fieldname": "included_in_paid_amount", "fieldtype": "Check", "label": "Considered In Paid Amount"}, {"fetch_from": "account_head.account_currency", "fieldname": "account_currency", "fieldtype": "Link", "label": "Account <PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}], "idx": 1, "istable": 1, "links": [], "modified": "2024-01-14 10:04:36.618240", "modified_by": "Administrator", "module": "Accounts", "name": "Purchase Taxes and Charges", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}