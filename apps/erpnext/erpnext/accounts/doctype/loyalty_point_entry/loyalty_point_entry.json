{"creation": "2018-01-23 05:40:18.117583", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["loyalty_program", "loyalty_program_tier", "customer", "invoice_type", "invoice", "redeem_against", "loyalty_points", "purchase_amount", "expiry_date", "posting_date", "company"], "fields": [{"fieldname": "loyalty_program", "fieldtype": "Link", "label": "Loyalty Program", "options": "Loyalty Program"}, {"fieldname": "loyalty_program_tier", "fieldtype": "Data", "label": "Loyalty Program Tier"}, {"fieldname": "customer", "fieldtype": "Link", "in_list_view": 1, "label": "Customer", "options": "Customer"}, {"fieldname": "redeem_against", "fieldtype": "Link", "label": "Redeem Against", "options": "Loyalty Point Entry"}, {"fieldname": "loyalty_points", "fieldtype": "Int", "in_list_view": 1, "label": "Loyalty Points"}, {"fieldname": "purchase_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Purchase Amount"}, {"fieldname": "expiry_date", "fieldtype": "Date", "in_list_view": 1, "label": "Expiry Date"}, {"fieldname": "posting_date", "fieldtype": "Date", "label": "Posting Date"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company"}, {"fieldname": "invoice_type", "fieldtype": "Link", "label": "Invoice Type", "options": "DocType"}, {"fieldname": "invoice", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Invoice", "options": "invoice_type"}], "in_create": 1, "modified": "2020-01-30 17:27:55.964242", "modified_by": "Administrator", "module": "Accounts", "name": "Loyalty Point Entry", "owner": "Administrator", "permissions": [{"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Auditor"}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager"}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User"}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "title_field": "customer", "track_changes": 1}