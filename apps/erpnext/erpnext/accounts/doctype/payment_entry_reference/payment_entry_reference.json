{"actions": [], "creation": "2016-06-01 16:55:32.196722", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["reference_doctype", "reference_name", "due_date", "bill_no", "payment_term", "column_break_4", "total_amount", "outstanding_amount", "allocated_amount", "exchange_rate", "exchange_gain_loss", "account"], "fields": [{"columns": 2, "fieldname": "reference_doctype", "fieldtype": "Link", "in_list_view": 1, "label": "Type", "options": "DocType", "reqd": 1, "search_index": 1}, {"columns": 2, "fieldname": "reference_name", "fieldtype": "Dynamic Link", "in_global_search": 1, "in_list_view": 1, "label": "Name", "options": "reference_doctype", "reqd": 1, "search_index": 1}, {"fieldname": "due_date", "fieldtype": "Date", "label": "Due Date", "read_only": 1}, {"fieldname": "bill_no", "fieldtype": "Data", "label": "Supplier Invoice No", "no_copy": 1, "read_only": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"columns": 2, "fieldname": "total_amount", "fieldtype": "Float", "in_list_view": 1, "label": "Grand Total", "print_hide": 1, "read_only": 1}, {"columns": 2, "fieldname": "outstanding_amount", "fieldtype": "Float", "in_list_view": 1, "label": "Outstanding", "read_only": 1}, {"columns": 2, "fieldname": "allocated_amount", "fieldtype": "Float", "in_list_view": 1, "label": "Allocated"}, {"depends_on": "eval:(doc.reference_doctype=='Purchase Invoice')", "fieldname": "exchange_rate", "fieldtype": "Float", "label": "Exchange Rate", "print_hide": 1, "read_only": 1}, {"fieldname": "payment_term", "fieldtype": "Link", "label": "Payment Term", "options": "Payment Term"}, {"depends_on": "exchange_gain_loss", "fieldname": "exchange_gain_loss", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Exchange Gain/Loss", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "account", "fieldtype": "Link", "label": "Account", "options": "Account"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2023-06-08 07:40:38.487874", "modified_by": "Administrator", "module": "Accounts", "name": "Payment Entry Reference", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}