{"actions": [], "autoname": "ACC-PDA-.#####", "creation": "2019-11-04 18:01:23.454775", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["company", "type", "account", "column_break_3", "posting_date", "start_date", "end_date", "amended_from"], "fields": [{"fieldname": "type", "fieldtype": "Select", "in_list_view": 1, "label": "Type", "options": "\nIncome\nExpense", "reqd": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Process Deferred Accounting", "print_hide": 1, "read_only": 1}, {"fieldname": "start_date", "fieldtype": "Date", "in_list_view": 1, "label": "Service Start Date", "reqd": 1}, {"fieldname": "end_date", "fieldtype": "Date", "in_list_view": 1, "label": "Service End Date", "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "label": "Posting Date", "reqd": 1}, {"depends_on": "eval: doc.type", "fieldname": "account", "fieldtype": "Link", "label": "Account", "options": "Account"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "reqd": 1}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2020-09-03 18:07:02.463754", "modified_by": "Administrator", "module": "Accounts", "name": "Process Deferred Accounting", "owner": "Administrator", "permissions": [{"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "submit": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "submit": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC"}