[{"account": "_Test Account Shipping Charges - _TC", "calculate_based_on": "Net Total", "company": "_Test Company", "cost_center": "_Test Cost Center - _TC", "doctype": "Shipping Rule", "label": "_Test Shipping Rule", "name": "_Test Shipping Rule", "shipping_rule_type": "Selling", "conditions": [{"doctype": "Shipping Rule Condition", "from_value": 0, "parentfield": "conditions", "shipping_amount": 50.0, "to_value": 100}, {"doctype": "Shipping Rule Condition", "from_value": 101, "parentfield": "conditions", "shipping_amount": 100.0, "to_value": 200}, {"doctype": "Shipping Rule Condition", "from_value": 201, "parentfield": "conditions", "shipping_amount": 200.0}], "countries": [{"country": "India"}], "worldwide_shipping": 1}, {"account": "_Test Account Shipping Charges - _TC", "calculate_based_on": "Net Total", "company": "_Test Company", "cost_center": "_Test Cost Center - _TC", "doctype": "Shipping Rule", "label": "_Test Shipping Rule - India", "name": "_Test Shipping Rule - India", "conditions": [{"doctype": "Shipping Rule Condition", "from_value": 0, "parentfield": "conditions", "shipping_amount": 50.0, "to_value": 100}, {"doctype": "Shipping Rule Condition", "from_value": 101, "parentfield": "conditions", "shipping_amount": 100.0, "to_value": 200}, {"doctype": "Shipping Rule Condition", "from_value": 201, "parentfield": "conditions", "shipping_amount": 0.0}], "countries": [{"country": "India"}]}, {"account": "_Test Account Shipping Charges - _TC", "calculate_based_on": "Net Total", "company": "_Test Company", "cost_center": "_Test Cost Center - _TC", "doctype": "Shipping Rule", "label": "_Test Shipping Rule - Rest of the World", "name": "_Test Shipping Rule - Rest of the World", "shipping_rule_type": "Buying", "conditions": [{"doctype": "Shipping Rule Condition", "from_value": 0, "parentfield": "conditions", "shipping_amount": 500.0, "to_value": 1000}, {"doctype": "Shipping Rule Condition", "from_value": 1001, "parentfield": "conditions", "shipping_amount": 1000.0, "to_value": 2000}, {"doctype": "Shipping Rule Condition", "from_value": 2001, "parentfield": "conditions", "shipping_amount": 1500.0}], "worldwide_shipping": 1, "countries": [{"country": "Germany"}]}]