{"actions": [], "allow_rename": 1, "autoname": "format:UNREC-{#####}", "creation": "2023-08-22 10:26:34.421423", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["company", "voucher_type", "voucher_no", "get_allocations", "allocations", "amended_from"], "fields": [{"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Unreconcile Payment", "print_hide": 1, "read_only": 1}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company"}, {"fieldname": "voucher_type", "fieldtype": "Link", "label": "Voucher Type", "options": "DocType"}, {"fieldname": "voucher_no", "fieldtype": "Dynamic Link", "label": "Voucher No", "options": "voucher_type"}, {"fieldname": "get_allocations", "fieldtype": "<PERSON><PERSON>", "label": "Get Allocations"}, {"fieldname": "allocations", "fieldtype": "Table", "label": "Allocations", "options": "Unreconcile Payment Entries"}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2023-08-28 17:42:50.261377", "modified_by": "Administrator", "module": "Accounts", "name": "Unreconcile Payment", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "read": 1, "role": "Accounts Manager", "select": 1, "share": 1, "submit": 1, "write": 1}, {"create": 1, "delete": 1, "read": 1, "role": "Accounts User", "select": 1, "share": 1, "submit": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}