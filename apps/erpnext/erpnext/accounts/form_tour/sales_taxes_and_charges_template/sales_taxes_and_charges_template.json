{"creation": "2021-08-24 12:28:18.044902", "docstatus": 0, "doctype": "Form Tour", "first_document": 0, "idx": 0, "include_name_field": 0, "is_standard": 1, "modified": "2022-01-18 18:32:17.102330", "modified_by": "Administrator", "module": "Accounts", "name": "Sales Taxes and Charges Template", "owner": "Administrator", "reference_doctype": "Sales Taxes and Charges Template", "save_on_complete": 1, "steps": [{"description": "A name by which you will identify this template. You can change this later.", "field": "", "fieldname": "title", "fieldtype": "Data", "has_next_condition": 0, "is_table_field": 0, "label": "Title", "parent_field": "", "position": "Bottom", "title": "Title"}, {"description": "Company for which this tax template will be applicable", "field": "", "fieldname": "company", "fieldtype": "Link", "has_next_condition": 0, "is_table_field": 0, "label": "Company", "parent_field": "", "position": "Bottom", "title": "Company"}, {"description": "Set this template as the default for all sales transactions", "field": "", "fieldname": "is_default", "fieldtype": "Check", "has_next_condition": 0, "is_table_field": 0, "label": "<PERSON><PERSON><PERSON>", "parent_field": "", "position": "Bottom", "title": "Is this Default Tax Template?"}, {"description": "You can add a row for a tax rule here. These rules can be applied on the net total, or can be a flat amount.", "field": "", "fieldname": "taxes", "fieldtype": "Table", "has_next_condition": 0, "is_table_field": 0, "label": "Sales Taxes and Charges", "parent_field": "", "position": "Bottom", "title": "Taxes Table"}], "title": "Sales Taxes and Charges Template"}