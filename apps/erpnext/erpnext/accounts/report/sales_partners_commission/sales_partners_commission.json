{"add_total_row": 0, "columns": [], "creation": "2013-05-06 12:28:23", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "filters": [], "idx": 3, "is_standard": "Yes", "modified": "2021-10-06 06:26:07.881340", "modified_by": "Administrator", "module": "Accounts", "name": "Sales Partners Commission", "owner": "Administrator", "prepared_report": 0, "query": "SELECT\n    sales_partner as \"Sales Partner:Link/Sales Partner:220\",\n\tsum(base_net_total) as \"Invoiced Amount (Excl. Tax):Currency:220\",\n\tsum(amount_eligible_for_commission) as \"Amount Eligible for Commission:Currency:220\",\n\tsum(total_commission) as \"Total Commission:Currency:170\",\n\tsum(total_commission)*100/sum(amount_eligible_for_commission) as \"Average Commission Rate:Percent:220\"\nFROM\n\t`tabSales Invoice`\nWHERE\n\tdocstatus = 1 and ifnull(base_net_total, 0) > 0 and ifnull(total_commission, 0) > 0\nGROUP BY\n\tsales_partner\nORDER BY\n\t\"Total Commission:Currency:120\"", "ref_doctype": "Sales Invoice", "report_name": "Sales Partners Commission", "report_type": "Query Report", "roles": [{"role": "Accounts Manager"}, {"role": "Accounts User"}]}