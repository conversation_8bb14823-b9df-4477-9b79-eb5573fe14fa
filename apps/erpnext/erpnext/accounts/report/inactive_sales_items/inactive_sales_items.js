// Copyright (c) 2016, Frappe Technologies Pvt. Ltd. and contributors
// For license information, please see license.txt


frappe.query_reports["Inactive Sales Items"] = {
	"filters": [
		{
			fieldname: "territory",
			label: __("Territory"),
			fieldtype: "Link",
			options: "Territory",
			reqd: 1,
		},
		{
			fieldname: "item",
			label: __("Item"),
			fieldtype: "Link",
			options: "Item"
		},
		{
			fieldname: "item_group",
			label: __("Item Group"),
			fieldtype: "Link",
			options: "Item Group"
		},
		{
			fieldname: "based_on",
			label: __("Based On"),
			fieldtype: "Select",
			options: "Sales Order\nSales Invoice",
			default: "Sales Order"
		},
		{
			fieldname: "days",
			label: __("Days Since Last order"),
			fieldtype: "Select",
			options: [30, 60, 90],
			default: 30
		},
	]
};
