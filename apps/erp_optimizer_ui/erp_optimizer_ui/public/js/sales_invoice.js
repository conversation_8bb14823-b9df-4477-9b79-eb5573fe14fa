
frappe.ui.form.on('Sales Invoice', {
    refresh: function (frm) {
        // إضافة زر إنشاء فاتورة نقدية في شريط الأدوات
        if (frm.doc.docstatus === 0) {
            frm.add_custom_button(__('💰 إضافة فاتورة نقدية'), function () {
                create_cash_invoice();
            }, __('إنشاء'));
        }

        // إخفاء الحقول غير الضرورية إذا كانت فاتورة نقدية
        if (frm.doc.is_cash) {
            hide_unnecessary_fields_for_cash_invoice(frm);
            show_cash_invoice_indicator(frm);
        }
    },

    onload: function (frm) {
        // تطبيق التخصيصات عند تحميل النموذج
        if (frm.doc.is_cash) {
            hide_unnecessary_fields_for_cash_invoice(frm);
        }
    },

    is_cash: function (frm) {
        // عند تغيير حالة الفاتورة النقدية
        if (frm.doc.is_cash) {
            hide_unnecessary_fields_for_cash_invoice(frm);
            show_cash_invoice_indicator(frm);
            // تعيين تاريخ الاستحقاق لنفس تاريخ الفاتورة
            frm.set_value('due_date', frm.doc.posting_date);
        } else {
            show_all_fields_for_regular_invoice(frm);
            hide_cash_invoice_indicator(frm);
        }
    },

    customer: function (frm) {
        if (frm.doc.customer) {
            frappe.call({
                method: "frappe.client.get_list",
                args: {
                    doctype: "Sales Invoice",
                    filters: { customer: frm.doc.customer },
                    fields: ["name"]
                },
                callback: function (r) {
                    let invoice_count = r.message.length;
                    frappe.call({
                        method: "frappe.client.get_list",
                        args: {
                            doctype: "Payment Entry",
                            filters: { party: frm.doc.customer },
                            fields: ["name"]
                        },
                        callback: function (res) {
                            let payment_count = res.message.length;
                            frappe.msgprint({
                                title: "معلومات العميل",
                                indicator: "blue",
                                message: `🔔 <b>${frm.doc.customer}</b><br>
                                    🧾 عدد الفواتير: <b>${invoice_count}</b><br>
                                    💰 عدد السندات: <b>${payment_count}</b><br>
                                    <br><a href='/app/sales-invoice?customer=${frm.doc.customer}' target='_blank'>عرض الفواتير</a><br>
                                    <a href='/app/payment-entry?party=${frm.doc.customer}' target='_blank'>عرض السندات</a>`
                            });
                        }
                    });
                }
            });
        }
    }
});

// دالة إنشاء فاتورة نقدية جديدة
function create_cash_invoice() {
    frappe.new_doc('Sales Invoice', {
        'is_cash': 1,
        'due_date': frappe.datetime.get_today(),
        'posting_date': frappe.datetime.get_today()
    });
}

// دالة إخفاء الحقول غير الضرورية للفاتورة النقدية
function hide_unnecessary_fields_for_cash_invoice(frm) {
    const fields_to_hide = [
        'due_date', 'payment_terms_template', 'tc_name', 'terms',
        'advance_paid', 'allocate_advances_automatically', 'get_advances',
        'payment_schedule', 'payment_schedule_section', 'advances_section',
        'sales_partner', 'commission_rate', 'total_commission',
        'loyalty_points', 'loyalty_amount', 'redeem_loyalty_points',
        'loyalty_points_redemption', 'apply_discount_on',
        'coupon_code', 'referral_sales_partner', 'sales_team'
    ];

    fields_to_hide.forEach(field => {
        frm.toggle_display(field, false);
    });

    // إخفاء أقسام كاملة
    frm.toggle_display('payment_schedule_section', false);
    frm.toggle_display('advances_section', false);
    frm.toggle_display('sales_team_section', false);
    frm.toggle_display('more_info', false);
}

// دالة إظهار جميع الحقول للفاتورة العادية
function show_all_fields_for_regular_invoice(frm) {
    const fields_to_show = [
        'due_date', 'payment_terms_template', 'tc_name', 'terms',
        'advance_paid', 'allocate_advances_automatically', 'get_advances',
        'payment_schedule', 'payment_schedule_section', 'advances_section',
        'sales_partner', 'commission_rate', 'total_commission',
        'loyalty_points', 'loyalty_amount', 'redeem_loyalty_points',
        'loyalty_points_redemption', 'apply_discount_on',
        'coupon_code', 'referral_sales_partner', 'sales_team'
    ];

    fields_to_show.forEach(field => {
        frm.toggle_display(field, true);
    });

    // إظهار الأقسام
    frm.toggle_display('payment_schedule_section', true);
    frm.toggle_display('advances_section', true);
    frm.toggle_display('sales_team_section', true);
    frm.toggle_display('more_info', true);
}

// دالة إظهار مؤشر الفاتورة النقدية
function show_cash_invoice_indicator(frm) {
    if (!frm.doc.__islocal) {
        frm.dashboard.set_headline(
            `💰 ${__('فاتورة نقدية')} - ${__('مدفوعة فوراً')}`
        );
    }
}

// دالة إخفاء مؤشر الفاتورة النقدية
function hide_cash_invoice_indicator(frm) {
    if (!frm.doc.__islocal) {
        frm.dashboard.clear_headline();
    }
}
