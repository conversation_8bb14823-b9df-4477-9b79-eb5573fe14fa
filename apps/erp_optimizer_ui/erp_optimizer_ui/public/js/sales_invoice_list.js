// تخصيص قائمة فواتير المبيعات - إضافة زر الفاتورة النقدية
// Sales Invoice List Customization - Add Cash Invoice Button

frappe.listview_settings['Sales Invoice'] = {
    add_fields: ["customer", "customer_name", "base_grand_total", "outstanding_amount", "due_date", "company", "currency", "is_return", "is_cash"],

    // تخصيص الإجراء الرئيسي لإضافة زر الفاتورة النقدية
    primary_action: function () {
        // الإجراء الافتراضي لإنشاء فاتورة عادية
        this.make_new_doc();

        // إضافة زر الفاتورة النقدية بعد تحميل الصفحة
        setTimeout(() => {
            add_cash_invoice_button_to_toolbar(this);
        }, 100);
    },

    get_indicator: function (doc) {
        const status_colors = {
            "Draft": "grey",
            "Unpaid": "orange",
            "Paid": "green",
            "Return": "gray",
            "Credit Note Issued": "gray",
            "Unpaid and Discounted": "orange",
            "Partly Paid and Discounted": "yellow",
            "Overdue and Discounted": "red",
            "Overdue": "red",
            "Partly Paid": "yellow",
            "Internal Transfer": "darkgrey"
        };

        // إضافة مؤشر خاص للفواتير النقدية
        if (doc.is_cash && doc.docstatus === 1) {
            return [__("فاتورة نقدية"), "blue", "is_cash,=,1"];
        }

        if (doc.status) {
            return [__(doc.status), status_colors[doc.status] || "grey", "status,=," + doc.status];
        }
    },

    onload: function (listview) {
        // إضافة زر إنشاء فاتورة نقدية بجانب زر Add Sales Invoice
        add_cash_invoice_button_to_toolbar(listview);

        // إضافة زر تحويل الفواتير المحددة إلى نقدية (للفواتير المسودة)
        listview.page.add_action_item(__('🔄 تحويل إلى فاتورة نقدية'), function () {
            convert_to_cash_invoice(listview);
        });

        // إضافة فلتر سريع للفواتير النقدية
        listview.page.add_inner_button(__('الفواتير النقدية'), function () {
            listview.filter_area.add(listview.doctype, 'is_cash', '=', 1);
        }, __('فلاتر سريعة'));

        // إضافة فلتر سريع للفواتير العادية
        listview.page.add_inner_button(__('الفواتير العادية'), function () {
            listview.filter_area.add(listview.doctype, 'is_cash', '=', 0);
        }, __('فلاتر سريعة'));
    },

    // تخصيص عرض البيانات
    prepare_data: function (data) {
        // إضافة رمز للفواتير النقدية
        if (data.is_cash) {
            data.customer_name = `💰 ${data.customer_name || data.customer}`;
        }
    },

    // تخصيص الأعمدة المعروضة
    formatters: {
        customer_name: function (value, field, doc) {
            if (doc.is_cash) {
                return `<span class="cash-invoice-indicator">💰</span> ${value}`;
            }
            return value;
        },
        base_grand_total: function (value, field, doc) {
            let formatted = format_currency(value, doc.currency);
            if (doc.is_cash) {
                return `<span class="text-success"><strong>${formatted}</strong></span>`;
            }
            return formatted;
        }
    }
};

// دالة إضافة زر الفاتورة النقدية بجانب زر Add Sales Invoice
function add_cash_invoice_button_to_toolbar(listview) {
    // انتظار حتى يتم تحميل الصفحة بالكامل
    setTimeout(function () {
        // البحث عن الزر الرئيسي
        let primary_btn = listview.page.btn_primary;
        let page_actions = listview.page.$wrapper.find('.page-actions, .page-head-content');

        // التحقق من عدم وجود الزر مسبقاً
        if (page_actions.find('.btn-cash-invoice').length === 0) {
            // إنشاء زر الفاتورة النقدية
            let cash_invoice_btn = $(`
                <button class="btn btn-success btn-cash-invoice btn-sm"
                        style="margin-left: 8px;"
                        title="إنشاء فاتورة نقدية جديدة">
                    💰 فاتورة نقدية
                </button>
            `);

            // إضافة حدث النقر
            cash_invoice_btn.on('click', function (e) {
                e.preventDefault();
                e.stopPropagation();
                create_new_cash_invoice();
            });

            // محاولة إدراج الزر في المكان الصحيح
            if (primary_btn && primary_btn.length) {
                primary_btn.after(cash_invoice_btn);
            } else {
                // البحث عن زر Add Sales Invoice بطريقة أخرى
                let add_btn = page_actions.find('.btn-primary:contains("Add"), .btn-primary:contains("إضافة")').first();
                if (add_btn.length) {
                    add_btn.after(cash_invoice_btn);
                } else {
                    // إضافة الزر في نهاية شريط الأدوات
                    page_actions.first().append(cash_invoice_btn);
                }
            }
        }
    }, 1000);

    // إضافة مراقب للتحديثات
    setTimeout(function () {
        add_cash_invoice_button_observer(listview);
    }, 2000);
}

// دالة مراقبة إضافة الزر عند التحديث
function add_cash_invoice_button_observer(listview) {
    // مراقب للتغييرات في DOM
    let observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
            if (mutation.type === 'childList') {
                setTimeout(function () {
                    let page_actions = listview.page.$wrapper.find('.page-actions, .page-head-content');
                    if (page_actions.find('.btn-cash-invoice').length === 0) {
                        add_cash_invoice_button_to_toolbar(listview);
                    }
                }, 500);
            }
        });
    });

    // بدء المراقبة
    if (listview.page && listview.page.wrapper) {
        observer.observe(listview.page.wrapper[0], {
            childList: true,
            subtree: true
        });
    }
}

// دالة إنشاء فاتورة نقدية جديدة
function create_new_cash_invoice() {
    frappe.new_doc('Sales Invoice', {
        'is_cash': 1,
        'due_date': frappe.datetime.get_today(),
        'posting_date': frappe.datetime.get_today()
    });
}

// دالة تحويل الفواتير المحددة إلى فواتير نقدية
function convert_to_cash_invoice(listview) {
    let selected_docs = listview.get_checked_items();

    if (selected_docs.length === 0) {
        frappe.msgprint(__('يرجى تحديد فاتورة واحدة على الأقل'));
        return;
    }

    // التحقق من أن جميع الفواتير المحددة هي مسودات
    let draft_docs = selected_docs.filter(doc => doc.docstatus === 0);
    if (draft_docs.length !== selected_docs.length) {
        frappe.msgprint(__('يمكن تحويل المسودات فقط إلى فواتير نقدية'));
        return;
    }

    frappe.confirm(
        __('هل تريد تحويل {0} فاتورة إلى فواتير نقدية؟', [selected_docs.length]),
        function () {
            // تحويل الفواتير المحددة
            selected_docs.forEach(function (doc) {
                frappe.call({
                    method: 'frappe.client.set_value',
                    args: {
                        doctype: 'Sales Invoice',
                        name: doc.name,
                        fieldname: {
                            'is_cash': 1,
                            'due_date': doc.posting_date || frappe.datetime.get_today()
                        }
                    },
                    callback: function (r) {
                        if (!r.exc) {
                            frappe.show_alert({
                                message: __('تم تحويل الفاتورة {0} إلى فاتورة نقدية', [doc.name]),
                                indicator: 'green'
                            });
                        }
                    }
                });
            });

            // تحديث القائمة بعد ثانيتين
            setTimeout(function () {
                listview.refresh();
            }, 2000);
        }
    );
}

// إضافة CSS مخصص للفواتير النقدية وإضافة زر الفاتورة النقدية
frappe.ready(function () {
    if (!$('#cash-invoice-list-css').length) {
        $('head').append(`
            <style id="cash-invoice-list-css">
                .cash-invoice-indicator {
                    color: #28a745;
                    font-weight: bold;
                    margin-right: 5px;
                }

                .list-row[data-is-cash="1"] {
                    background-color: #f8fff8;
                    border-left: 3px solid #28a745;
                }

                .list-row[data-is-cash="1"]:hover {
                    background-color: #f0fff0;
                }

                .btn-cash-invoice {
                    background-color: #28a745 !important;
                    border-color: #28a745 !important;
                    color: white !important;
                    margin-left: 8px;
                }

                .btn-cash-invoice:hover {
                    background-color: #218838 !important;
                    border-color: #1e7e34 !important;
                    color: white !important;
                }
            </style>
        `);
    }

    // إضافة زر الفاتورة النقدية عند تحميل صفحة قائمة فواتير المبيعات
    if (window.location.pathname.includes('/app/sales-invoice')) {
        setTimeout(function () {
            add_cash_invoice_button_to_page();
        }, 1500);

        // مراقبة التغييرات في الصفحة
        let observer = new MutationObserver(function (mutations) {
            mutations.forEach(function (mutation) {
                if (mutation.type === 'childList') {
                    setTimeout(function () {
                        add_cash_invoice_button_to_page();
                    }, 500);
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
});

// دالة إضافة زر الفاتورة النقدية إلى الصفحة
function add_cash_invoice_button_to_page() {
    // البحث عن زر Add Sales Invoice
    let add_btn = $('.btn-primary:contains("Add Sales Invoice"), .btn-primary:contains("Add"), .btn-primary:contains("إضافة")').first();

    // التحقق من عدم وجود الزر مسبقاً
    if (add_btn.length && $('.btn-cash-invoice').length === 0) {
        // إنشاء زر الفاتورة النقدية
        let cash_invoice_btn = $(`
            <button class="btn btn-success btn-cash-invoice btn-sm"
                    title="إنشاء فاتورة نقدية جديدة">
                💰 فاتورة نقدية
            </button>
        `);

        // إضافة حدث النقر
        cash_invoice_btn.on('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            create_new_cash_invoice();
        });

        // إدراج الزر بجانب زر Add Sales Invoice
        add_btn.after(cash_invoice_btn);
    }
}
