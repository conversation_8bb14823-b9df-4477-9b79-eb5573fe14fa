# ميزة الفاتورة النقدية - Cash Invoice Feature

## نظرة عامة / Overview

تم إضافة ميزة الفاتورة النقدية إلى تطبيق `erp_optimizer_ui` لتسهيل إنشاء وإدارة الفواتير النقدية في نظام ERPNext.

This feature adds cash invoice functionality to the `erp_optimizer_ui` app to facilitate creating and managing cash invoices in ERPNext.

## الميزات المضافة / Added Features

### 1. زر إضافة فاتورة نقدية / Cash Invoice Button
- **الموقع**: شريط الأدوات في نموذج فاتورة المبيعات
- **الوظيفة**: إنشاء فاتورة نقدية جديدة مع القيم المحددة مسبقاً
- **الأيقونة**: 💰 إضافة فاتورة نقدية

**Location**: Toolbar in Sales Invoice form
**Function**: Creates new cash invoice with pre-filled values
**Icon**: 💰 إضافة فاتورة نقدية

### 2. زر في قائمة العرض / List View Button
- **الموقع**: شريط الأدوات في قائمة فواتير المبيعات
- **الوظيفة**: إنشاء فاتورة نقدية من قائمة العرض
- **ميزات إضافية**: 
  - تحويل الفواتير المحددة إلى فواتير نقدية
  - فلاتر سريعة للفواتير النقدية والعادية

**Location**: Toolbar in Sales Invoice list view
**Function**: Create cash invoice from list view
**Additional Features**:
  - Convert selected invoices to cash invoices
  - Quick filters for cash and regular invoices

### 3. إخفاء الحقول غير الضرورية / Hide Unnecessary Fields
عند تفعيل خيار "فاتورة نقدية"، يتم إخفاء الحقول التالية:

When "Cash Invoice" option is enabled, the following fields are hidden:

- `due_date` - تاريخ الاستحقاق
- `payment_terms_template` - قالب شروط الدفع
- `tc_name` - الشروط والأحكام
- `terms` - الشروط
- `advance_paid` - المبلغ المدفوع مقدماً
- `allocate_advances_automatically` - تخصيص المقدمات تلقائياً
- `get_advances` - الحصول على المقدمات
- `payment_schedule` - جدول الدفع
- `sales_partner` - شريك المبيعات
- `commission_rate` - معدل العمولة
- `total_commission` - إجمالي العمولة
- `loyalty_points` - نقاط الولاء
- `loyalty_amount` - مبلغ الولاء
- `redeem_loyalty_points` - استرداد نقاط الولاء
- `loyalty_points_redemption` - استرداد نقاط الولاء
- `apply_discount_on` - تطبيق الخصم على
- `coupon_code` - رمز الكوبون
- `referral_sales_partner` - شريك المبيعات المُحيل
- `sales_team` - فريق المبيعات

### 4. مؤشر الفاتورة النقدية / Cash Invoice Indicator
- **العرض**: مؤشر مرئي في أعلى النموذج
- **النص**: "💰 فاتورة نقدية - مدفوعة فوراً"
- **اللون**: أخضر للتمييز

**Display**: Visual indicator at the top of the form
**Text**: "💰 فاتورة نقدية - مدفوعة فوراً"
**Color**: Green for distinction

### 5. تعيين تاريخ الاستحقاق تلقائياً / Auto-set Due Date
عند تفعيل خيار الفاتورة النقدية، يتم تعيين تاريخ الاستحقاق تلقائياً لنفس تاريخ الفاتورة.

When cash invoice option is enabled, due date is automatically set to the same as posting date.

## الملفات المضافة / Added Files

### 1. JavaScript Files
- `apps/erp_optimizer_ui/erp_optimizer_ui/public/js/sales_invoice.js` - تخصيصات النموذج
- `apps/erp_optimizer_ui/erp_optimizer_ui/public/js/sales_invoice_list.js` - تخصيصات قائمة العرض

### 2. Python Files
- `apps/erp_optimizer_ui/erp_optimizer_ui/install_cash_invoice.py` - ملف التثبيت

### 3. Configuration Files
- تحديث `hooks.py` لتضمين ملفات JavaScript الجديدة

## التثبيت / Installation

### 1. تثبيت التخصيصات / Install Customizations
```bash
cd /home/<USER>/frappe-bench
bench --site site1.local execute erp_optimizer_ui.install_cash_invoice.install_cash_invoice_customizations
```

### 2. إعادة بناء الأصول / Rebuild Assets
```bash
bench --site site1.local build
```

### 3. إعادة تشغيل الخادم / Restart Server
```bash
bench restart
```

## الاستخدام / Usage

### 1. إنشاء فاتورة نقدية جديدة / Create New Cash Invoice
1. اذهب إلى قائمة فواتير المبيعات
2. انقر على زر "💰 إضافة فاتورة نقدية"
3. سيتم إنشاء فاتورة جديدة مع تفعيل خيار الفاتورة النقدية

### 2. تحويل فاتورة موجودة / Convert Existing Invoice
1. افتح فاتورة مبيعات (مسودة)
2. فعّل خيار "فاتورة نقدية؟"
3. ستختفي الحقول غير الضرورية تلقائياً

### 3. تحويل متعدد من قائمة العرض / Bulk Convert from List View
1. اذهب إلى قائمة فواتير المبيعات
2. حدد الفواتير المطلوبة (مسودات فقط)
3. انقر على "🔄 تحويل إلى فاتورة نقدية"

## إلغاء التثبيت / Uninstallation

```bash
cd /home/<USER>/frappe-bench
bench --site site1.local execute erp_optimizer_ui.install_cash_invoice.uninstall_cash_invoice_customizations
```

## الدعم / Support

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

For support or to report issues, please contact the development team.

---

**تاريخ الإنشاء / Created**: 2025-07-16
**الإصدار / Version**: 1.0.0
**المطور / Developer**: ERP Optimizer UI Team
