
**For the latest app with 2024 Zatca guidelines , please use this link.  https://github.com/ERPGulf/Saudi-E-Invoicing-Phase-2-2024
**

## Saudi Phase2 Api

Saudi VAT Phase-2 implementation according to new Zatca API realease

#### License

mit# saudi-phase2-api

This app is compliant with new Zatca e-invoicing document published in 2023. Also this app uses only API calls for better performance and response <br> 

You can follow standard Frappe methods for installing apps. Please see it here below<br> 
bench get-app https://github.com/ERPGulf/saudi-phase2-api.git<br> 
bench --site yoursite.yourdomain.com install-app saudi-phase2-api<br> 
bench --site yoursite.yourdomain.com migrate<br> 

Now you can see Zatca API installed on your site. Goto Help->About and make sure you have Zatca app installaed.<br> 

<PERSON><PERSON>na 
