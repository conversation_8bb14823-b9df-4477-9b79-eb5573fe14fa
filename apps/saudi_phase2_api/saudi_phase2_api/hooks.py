app_name = "saudi_phase2_api"
app_title = "Saudi Phase2 Api"
app_publisher = "ERPGulf"
app_description = "Saudi VAT Phase-2 implementation according to new Zatca API realease"
app_email = "<EMAIL>"
app_license = "mit"

from frappe import _
from . import __version__ as app_version
# required_apps = []

# Includes in <head>
# ------------------

# include js, css files in header of desk.html
# app_include_css = "/assets/saudi_phase2_api/css/saudi_phase2_api.css"
# app_include_js = "/assets/saudi_phase2_api/js/saudi_phase2_api.js"

# include js, css files in header of web template
# web_include_css = "/assets/saudi_phase2_api/css/saudi_phase2_api.css"
# web_include_js = "/assets/saudi_phase2_api/js/saudi_phase2_api.js"

# include custom scss in every website theme (without file extension ".scss")
# website_theme_scss = "saudi_phase2_api/public/scss/website"

# include js, css files in header of web form
# webform_include_js = {"doctype": "public/js/doctype.js"}
# webform_include_css = {"doctype": "public/css/doctype.css"}

# include js in page
# page_js = {"page" : "public/js/file.js"}

# include js in doctype views
# doctype_js = {"doctype" : "public/js/doctype.js"}
# doctype_list_js = {"doctype" : "public/js/doctype_list.js"}
# doctype_tree_js = {"doctype" : "public/js/doctype_tree.js"}
# doctype_calendar_js = {"doctype" : "public/js/doctype_calendar.js"}

# Home Pages
# ----------

# application home page (will override Website Settings)
# home_page = "login"

# website user home page (by Role)
# role_home_page = {
#	"Role": "home_page"
# }

# Generators
# ----------

# automatically create page for each record of this doctype
# website_generators = ["Web Page"]

# Jinja
# ----------

# add methods and filters to jinja environment
# jinja = {
#	"methods": "saudi_phase2_api.utils.jinja_methods",
#	"filters": "saudi_phase2_api.utils.jinja_filters"
# }

# Installation
# ------------

# before_install = "saudi_phase2_api.install.before_install"
# after_install = "saudi_phase2_api.install.after_install"

# Uninstallation
# ------------

# before_uninstall = "saudi_phase2_api.uninstall.before_uninstall"
# after_uninstall = "saudi_phase2_api.uninstall.after_uninstall"

# Integration Setup
# ------------------
# To set up dependencies/integrations with other apps
# Name of the app being installed is passed as an argument

# before_app_install = "saudi_phase2_api.utils.before_app_install"
# after_app_install = "saudi_phase2_api.utils.after_app_install"

# Integration Cleanup
# -------------------
# To clean up dependencies/integrations with other apps
# Name of the app being uninstalled is passed as an argument

# before_app_uninstall = "saudi_phase2_api.utils.before_app_uninstall"
# after_app_uninstall = "saudi_phase2_api.utils.after_app_uninstall"

# Desk Notifications
# ------------------
# See frappe.core.notifications.get_notification_config

# notification_config = "saudi_phase2_api.notifications.get_notification_config"

# Permissions
# -----------
# Permissions evaluated in scripted ways

# permission_query_conditions = {
#	"Event": "frappe.desk.doctype.event.event.get_permission_query_conditions",
# }
#
# has_permission = {
#	"Event": "frappe.desk.doctype.event.event.has_permission",
# }

# DocType Class
# ---------------
# Override standard doctype classes

# override_doctype_class = {
#	"ToDo": "custom_app.overrides.CustomToDo"
# }

# Document Events
# ---------------
# Hook on document methods and events

# doc_events = {
#	"*": {
#		"on_update": "method",
#		"on_cancel": "method",
#		"on_trash": "method"
#	}
# }

# Scheduled Tasks
# ---------------

# scheduler_events = {
#	"all": [
#		"saudi_phase2_api.tasks.all"
#	],
#	"daily": [
#		"saudi_phase2_api.tasks.daily"
#	],
#	"hourly": [
#		"saudi_phase2_api.tasks.hourly"
#	],
#	"weekly": [
#		"saudi_phase2_api.tasks.weekly"
#	],
#	"monthly": [
#		"saudi_phase2_api.tasks.monthly"
#	],
# }

# Testing
# -------

# before_tests = "saudi_phase2_api.install.before_tests"

# Overriding Methods
# ------------------------------
#
# override_whitelisted_methods = {
#	"frappe.desk.doctype.event.event.get_events": "saudi_phase2_api.event.get_events"
# }
#
# each overriding function accepts a `data` argument;
# generated from the base implementation of the doctype dashboard,
# along with any modifications made in other Frappe apps
# override_doctype_dashboards = {
#	"Task": "saudi_phase2_api.task.get_dashboard_data"
# }

# exempt linked doctypes from being automatically cancelled
#
# auto_cancel_exempted_doctypes = ["Auto Repeat"]

# Ignore links to specified DocTypes when deleting documents
# -----------------------------------------------------------

# ignore_links_on_delete = ["Communication", "ToDo"]

# Request Events
# ----------------
# before_request = ["saudi_phase2_api.utils.before_request"]
# after_request = ["saudi_phase2_api.utils.after_request"]

# Job Events
# ----------
# before_job = ["saudi_phase2_api.utils.before_job"]
# after_job = ["saudi_phase2_api.utils.after_job"]

# User Data Protection
# --------------------

# user_data_fields = [
#	{
#		"doctype": "{doctype_1}",
#		"filter_by": "{filter_by}",
#		"redact_fields": ["{field_1}", "{field_2}"],
#		"partial": 1,
#	},
#	{
#		"doctype": "{doctype_2}",
#		"filter_by": "{filter_by}",
#		"partial": 1,
#	},
#	{
#		"doctype": "{doctype_3}",
#		"strict": False,
#	},
#	{
#		"doctype": "{doctype_4}"
#	}
# ]

# Authentication and authorization
# --------------------------------

# auth_hooks = [
#	"saudi_phase2_api.auth.validate"
# ]
doctype_js = {
    "Sales Invoice" : "public/js/our_sales_invoice.js"}


fixtures = [ {"dt": "Custom Field","filters": [["module", "=", "Saudi Phase2 Api"]] }]
