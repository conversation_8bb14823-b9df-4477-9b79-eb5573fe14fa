[{"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "{customer_name}", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_invoice_idname", "fieldtype": "Data", "hidden": 1, "hide_border": 0, "hide_days": 1, "hide_seconds": 1, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "title", "is_system_generated": 0, "is_virtual": 0, "label": "invoice idname", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:49:05.814079", "module": "Saudi Phase2 Api", "name": "Sales Invoice-custom_invoice_idname", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 1, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "{customer_name}", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_invoice_id", "fieldtype": "Data", "hidden": 1, "hide_border": 0, "hide_days": 1, "hide_seconds": 1, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_invoice_idname", "is_system_generated": 0, "is_virtual": 0, "label": "invoice id", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:48:44.434129", "module": "Saudi Phase2 Api", "name": "Sales Invoice-custom_invoice_id", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 1, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_payment_code", "fieldtype": "Int", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "naming_series", "is_system_generated": 0, "is_virtual": 0, "label": "payment code", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:45:11.125783", "module": "Saudi Phase2 Api", "name": "Sales Invoice-custom_payment_code", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_accounting_customer_id", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "customer_group", "is_system_generated": 0, "is_virtual": 0, "label": "Accounting customer Id", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 12:14:20.800881", "module": "Saudi Phase2 Api", "name": "Customer-custom_accounting_customer_id", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_uuid", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_payment_code", "is_system_generated": 0, "is_virtual": 0, "label": "uuid", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:46:32.452934", "module": "Saudi Phase2 Api", "name": "Sales Invoice-custom_uuid", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_invoice", "fieldtype": "Int", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_uuid", "is_system_generated": 0, "is_virtual": 0, "label": "invoice num", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:45:48.460843", "module": "Saudi Phase2 Api", "name": "Sales Invoice-custom_invoice", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_invoice_type_code", "fieldtype": "Int", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_invoice", "is_system_generated": 0, "is_virtual": 0, "label": "invoice type code", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:45:23.667233", "module": "Saudi Phase2 Api", "name": "Sales Invoice-custom_invoice_type_code", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_total_no_of_line", "fieldtype": "Int", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_invoice_type_code", "is_system_generated": 0, "is_virtual": 0, "label": "total no of line", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:45:37.080140", "module": "Saudi Phase2 Api", "name": "Sales Invoice-custom_total_no_of_line", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_accounting_supplier_party_id", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "tax_id", "is_system_generated": 0, "is_virtual": 0, "label": "Accounting Supplier Party id", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:44:42.701637", "module": "Saudi Phase2 Api", "name": "Company-custom_accounting_supplier_party_id", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_customer_address_data", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "image", "is_system_generated": 0, "is_virtual": 0, "label": "address data", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:50:55.211574", "module": "Saudi Phase2 Api", "name": "Customer-custom_customer_address_data", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_street", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_customer_address_data", "is_system_generated": 0, "is_virtual": 0, "label": "street", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:52:16.750273", "module": "Saudi Phase2 Api", "name": "Customer-custom_street", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_building_no", "fieldtype": "Int", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_street", "is_system_generated": 0, "is_virtual": 0, "label": "building_no", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:51:58.695084", "module": "Saudi Phase2 Api", "name": "Customer-custom_building_no", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_plot_id_no", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_building_no", "is_system_generated": 0, "is_virtual": 0, "label": "plot_id_no", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:50:22.353289", "module": "Saudi Phase2 Api", "name": "Customer-custom_plot_id_no", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_street", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "date_of_incorporation", "is_system_generated": 0, "is_virtual": 0, "label": "street", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:48:58.308697", "module": "Saudi Phase2 Api", "name": "Company-custom_street", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_sub", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_plot_id_no", "is_system_generated": 0, "is_virtual": 0, "label": "sub", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:49:38.311091", "module": "Saudi Phase2 Api", "name": "Customer-custom_sub", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_country_name", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_street", "is_system_generated": 0, "is_virtual": 0, "label": "country name", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-25 11:58:28.982359", "module": "Saudi Phase2 Api", "name": "Company-custom_country_name", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_city", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_sub", "is_system_generated": 0, "is_virtual": 0, "label": "city", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:50:10.205234", "module": "Saudi Phase2 Api", "name": "Customer-custom_city", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_plot_id_no", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_country_name", "is_system_generated": 0, "is_virtual": 0, "label": "plot_id_no", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:43:59.152387", "module": "Saudi Phase2 Api", "name": "Company-custom_plot_id_no", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_pincode", "fieldtype": "Int", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_city", "is_system_generated": 0, "is_virtual": 0, "label": "pincode", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:49:13.996668", "module": "Saudi Phase2 Api", "name": "Customer-custom_pincode", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_city", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_plot_id_no", "is_system_generated": 0, "is_virtual": 0, "label": "city", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:44:13.550967", "module": "Saudi Phase2 Api", "name": "Company-custom_city", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_document_id", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "due_date", "is_system_generated": 0, "is_virtual": 0, "label": "document id", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:47:21.523694", "module": "Saudi Phase2 Api", "name": "Sales Invoice-custom_document_id", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_state", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_pincode", "is_system_generated": 0, "is_virtual": 0, "label": "state", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:49:24.058439", "module": "Saudi Phase2 Api", "name": "Customer-custom_state", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_sub", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_city", "is_system_generated": 0, "is_virtual": 0, "label": "sub", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:45:01.409343", "module": "Saudi Phase2 Api", "name": "Company-custom_sub", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_doc_uuid", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_document_id", "is_system_generated": 0, "is_virtual": 0, "label": "doc uuid", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:47:31.607880", "module": "Saudi Phase2 Api", "name": "Sales Invoice-custom_doc_uuid", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_pincode", "fieldtype": "Int", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_sub", "is_system_generated": 0, "is_virtual": 0, "label": "pincode", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:44:25.867314", "module": "Saudi Phase2 Api", "name": "Company-custom_pincode", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_country", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_state", "is_system_generated": 0, "is_virtual": 0, "label": "country", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-25 11:58:44.901594", "module": "Saudi Phase2 Api", "name": "Customer-custom_country", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_pih", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_doc_uuid", "is_system_generated": 0, "is_virtual": 0, "label": "pih", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-25 14:25:49.027462", "module": "Saudi Phase2 Api", "name": "Sales Invoice-custom_pih", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_build_no", "fieldtype": "Int", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_pincode", "is_system_generated": 0, "is_virtual": 0, "label": "build no", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:44:50.215589", "module": "Saudi Phase2 Api", "name": "Company-custom_build_no", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_state", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_build_no", "is_system_generated": 0, "is_virtual": 0, "label": "state", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-26 11:44:33.592139", "module": "Saudi Phase2 Api", "name": "Company-custom_state", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_item_character", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 1, "hide_seconds": 1, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "scan_barcode", "is_system_generated": 0, "is_virtual": 0, "label": "item character", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-25 11:57:38.753756", "module": "Saudi Phase2 Api", "name": "Sales Invoice-custom_item_character", "no_copy": 0, "non_negative": 0, "options": "", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}]