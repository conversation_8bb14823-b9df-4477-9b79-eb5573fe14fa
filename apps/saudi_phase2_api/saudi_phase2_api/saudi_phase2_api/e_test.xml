<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
   xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
   xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
   xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2">
   <ext:UBLExtensions>
      <ext:UBLExtension>
         <ext:ExtensionURI>urn:oasis:names:specification:ubl:dsig:enveloped:xades</ext:ExtensionURI>
         <ext:ExtensionContent>
            <sig:UBLDocumentSignatures xmlns:sig="urn:oasis:names:specification:ubl:schema:xsd:CommonSignatureComponents-2"
               xmlns:sac="urn:oasis:names:specification:ubl:schema:xsd:SignatureAggregateComponents-2"
               xmlns:sbc="urn:oasis:names:specification:ubl:schema:xsd:SignatureBasicComponents-2">
               <sac:SignatureInformation>
                  <cbc:ID>urn:oasis:names:specification:ubl:signature:1</cbc:ID>
                  <sbc:ReferencedSignatureID>urn:oasis:names:specification:ubl:signature:Invoice</sbc:ReferencedSignatureID>

               </sac:SignatureInformation>
            </sig:UBLDocumentSignatures>
         </ext:ExtensionContent>
      </ext:UBLExtension>
   </ext:UBLExtensions>

   <cbc:ProfileID>reporting:1.0</cbc:ProfileID>
   <cbc:ID>{{doc.invoice_number}}</cbc:ID>
   <cbc:UUID>{{ doc.uuid }}</cbc:UUID>
   <cbc:IssueDate>{{doc.posting_date}}</cbc:IssueDate>
   <cbc:IssueTime>{{doc.posting_time}}</cbc:IssueTime>
   <cbc:InvoiceTypeCode name="0100000">{{doc.invoice_type_code}}</cbc:InvoiceTypeCode>
   <cbc:DocumentCurrencyCode>{{doc.currency}}</cbc:DocumentCurrencyCode>
   <cbc:TaxCurrencyCode>{{doc.currency}}</cbc:TaxCurrencyCode>
   <cbc:LineCountNumeric>{{doc.lineCount}}</cbc:LineCountNumeric>
   <cac:AdditionalDocumentReference>
      <cbc:ID>{{doc.document_id}}</cbc:ID>
      <cbc:UUID>{{doc.icv_code}}</cbc:UUID>
   </cac:AdditionalDocumentReference>
   <cac:AdditionalDocumentReference>
      <cbc:ID>PIH</cbc:ID>
      <cac:Attachment>
         <cbc:EmbeddedDocumentBinaryObject mimeCode="text/plain">{{ doc.pih }}</cbc:EmbeddedDocumentBinaryObject>
      </cac:Attachment>
   </cac:AdditionalDocumentReference>

   <cac:AdditionalDocumentReference>
      <cbc:ID>QR</cbc:ID>
      <cac:Attachment>
         <cbc:EmbeddedDocumentBinaryObject mimeCode="text/plain">{{doc.qr_code}}</cbc:EmbeddedDocumentBinaryObject>
      </cac:Attachment>
   </cac:AdditionalDocumentReference>
   <cac:Signature>
      <cbc:ID>urn:oasis:names:specification:ubl:signature:Invoice</cbc:ID>
      <cbc:SignatureMethod>urn:oasis:names:specification:ubl:dsig:enveloped:xades</cbc:SignatureMethod>
   </cac:Signature>

   <cac:AccountingSupplierParty>
      <cac:Party>
         <cac:PartyIdentification>
            <cbc:ID schemeID="MLS">{{doc.accsupid}}</cbc:ID>
         </cac:PartyIdentification>
         <cac:PostalAddress>
            <cbc:StreetName>{{ doc.company_address_data.street }}</cbc:StreetName>
            <cbc:BuildingNumber>{{ doc.company_address_data.building_no }}</cbc:BuildingNumber>
            <cbc:PlotIdentification>{{ doc.company_address_data.plot_id_no }}</cbc:PlotIdentification>
            <cbc:CitySubdivisionName>{{ doc.company_address_data.sub}}</cbc:CitySubdivisionName>
            <cbc:CityName>{{ doc.company_address_data.city }}</cbc:CityName>
            <cbc:PostalZone>{{ doc.company_address_data.pincode }}</cbc:PostalZone>
            <cbc:CountrySubentity>{{  doc.company_address_data.state }}</cbc:CountrySubentity>
            <cac:Country>
               <cbc:IdentificationCode>{{doc.company_address_data.country}}</cbc:IdentificationCode>
            </cac:Country>
         </cac:PostalAddress>
         <cac:PartyTaxScheme>
            <cbc:CompanyID>{{doc.company_tax_id}}</cbc:CompanyID>
            <cac:TaxScheme>

               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:PartyTaxScheme>
         <cac:PartyLegalEntity>
            <cbc:RegistrationName>{{ doc.company }}</cbc:RegistrationName>
         </cac:PartyLegalEntity>
      </cac:Party>
   </cac:AccountingSupplierParty>
   <cac:AccountingCustomerParty>
      <cac:Party>
         <cac:PartyIdentification>
            <cbc:ID schemeID="SAG">{{doc.acccustid}}</cbc:ID>
         </cac:PartyIdentification>
         <cac:PostalAddress>
            <cbc:StreetName>{{doc.customer_address_data.street}}</cbc:StreetName>
            <cbc:BuildingNumber>{{doc.customer_address_data.building_no}}</cbc:BuildingNumber>
            <cbc:PlotIdentification>{{ doc.customer_address_data.plot_id_no}}</cbc:PlotIdentification>
            <cbc:CitySubdivisionName>{{ doc.customer_address_data.sub }}</cbc:CitySubdivisionName>
            <cbc:CityName>{{ doc.customer_address_data.city}}</cbc:CityName>
            <cbc:PostalZone>{{ doc.customer_address_data.pincode}}</cbc:PostalZone>
            <cbc:CountrySubentity>{{ doc.customer_address_data.state }}</cbc:CountrySubentity>
            <cac:Country>
               <cbc:IdentificationCode>{{doc.customer_address_data.country}}</cbc:IdentificationCode>
            </cac:Country>
         </cac:PostalAddress>
         <cac:PartyTaxScheme>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:PartyTaxScheme>
         <cac:PartyLegalEntity>
            <cbc:RegistrationName>{{ doc.customer }}</cbc:RegistrationName>
         </cac:PartyLegalEntity>
      </cac:Party>
   </cac:AccountingCustomerParty>
   <cac:Delivery>
      <cbc:ActualDeliveryDate>{{doc.posting_date}}</cbc:ActualDeliveryDate>
   </cac:Delivery>
   <cac:PaymentMeans>
      <cbc:PaymentMeansCode>{{doc.payment_code}}</cbc:PaymentMeansCode>
   </cac:PaymentMeans>
   <cac:TaxTotal>
      <cbc:TaxAmount currencyID="SAR">{{doc.total_taxes_and_charges}}</cbc:TaxAmount>
      <cac:TaxSubtotal>
         <cbc:TaxableAmount currencyID="SAR">{{ doc.total }}</cbc:TaxableAmount>
         <cbc:TaxAmount currencyID="SAR">{{doc.total_taxes_and_charges}}</cbc:TaxAmount>
         <cac:TaxCategory>
            <cbc:ID>{{doc.custom_taxcateg_id}}</cbc:ID>
            <cbc:Percent>{{doc.tax_rate}}</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:TaxSubtotal>
   </cac:TaxTotal>
   <cac:TaxTotal>
      <cbc:TaxAmount currencyID="SAR">{{ doc.total_taxes_and_charges }}</cbc:TaxAmount>
   </cac:TaxTotal>
   <cac:LegalMonetaryTotal>
      <cbc:LineExtensionAmount currencyID="SAR">{{ doc.total }}</cbc:LineExtensionAmount>
      <cbc:TaxExclusiveAmount currencyID="SAR">{{ doc.total }}</cbc:TaxExclusiveAmount>
      <cbc:TaxInclusiveAmount currencyID="SAR">{{ doc.grand_total}}</cbc:TaxInclusiveAmount>
      <cbc:AllowanceTotalAmount currencyID="SAR">0.00</cbc:AllowanceTotalAmount>
      <cbc:PayableAmount currencyID="SAR">{{ doc.grand_total}}</cbc:PayableAmount>
   </cac:LegalMonetaryTotal>
  {% for item in doc.e_invoice_items %}
   <cac:InvoiceLine>
      <cbc:ID>{{loop.index}}</cbc:ID>
      <cbc:InvoicedQuantity unitCode="PCE">{{ item.qty }}</cbc:InvoicedQuantity>
      <cbc:LineExtensionAmount currencyID="SAR">{{ item.amount }}</cbc:LineExtensionAmount>
      <cac:TaxTotal>
         <cbc:TaxAmount currencyID="SAR">{{ item.item_tax_amount }}</cbc:TaxAmount>
         <cbc:RoundingAmount currencyID="SAR">{{ item.base_net_amount }}</cbc:RoundingAmount>
      </cac:TaxTotal>
      <cac:Item>
         <cbc:Name>{{ item.item_code}}</cbc:Name>
         <cac:ClassifiedTaxCategory>
            <cbc:ID>{{item.item_character}}</cbc:ID>
            <cbc:Percent>{{item.item_tax_percentage}}</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:ClassifiedTaxCategory>
      </cac:Item>
      <cac:Price>
         <cbc:PriceAmount currencyID="SAR">{{ item.price_list_rate}}</cbc:PriceAmount>
      </cac:Price>
   </cac:InvoiceLine>
   {% endfor %}
</Invoice>