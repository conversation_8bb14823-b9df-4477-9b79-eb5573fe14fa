# Configuration for probot-stale - https://github.com/probot/stale

# Number of days of inactivity before an Issue or Pull Request becomes stale
daysUntilStale: 14

# Number of days of inactivity before a stale Issue or Pull Request is closed.
# Set to false to disable. If disabled, issues still need to be closed manually, but will remain marked as stale.
daysUntilClose: 3

# Issues or Pull Requests with these labels will never be considered stale. Set to `[]` to disable
exemptLabels:
  - hotfix

# Set to true to ignore issues in a project (defaults to false)
exemptProjects: false

# Set to true to ignore issues in a milestone (defaults to false)
exemptMilestones: true

# Label to use when marking as stale
staleLabel: inactive

# Comment to post when marking as stale. Set to `false` to disable
markComment: >
  This pull request has been automatically marked as stale because it has not had
  recent activity. It will be closed within 3 days if no further activity occurs, but it
  only takes a comment to keep a contribution alive :) Also, even if it is closed,
  you can always reopen the PR when you're ready. Thank you for contributing.

# Limit the number of actions per hour, from 1-30. Default is 30
limitPerRun: 10

# Limit to only `issues` or `pulls`
only: pulls
