name: UI

on:
  pull_request:
  workflow_dispatch:
  schedule:
    # Run everday at midnight UTC / 5:30 IST
    - cron: "0 0 * * *"

concurrency:
  group: ui-develop-${{ github.event_name }}-${{ github.event.number }}
  cancel-in-progress: true

permissions:
  # Do not change this as GITHUB_TOKEN is being used by roulette
  contents: read

jobs:
  checkrun:
    name: Build Check
    runs-on: ubuntu-latest

    outputs:
      build: ${{ steps.check-build.outputs.build }}

    steps:
      - name: <PERSON><PERSON>
        uses: actions/checkout@v4

      - name: Check if build should be run
        id: check-build
        run: |
          python "${GITHUB_WORKSPACE}/.github/helper/roulette.py"
        env:
          TYPE: "ui"
          PR_NUMBER: ${{ github.event.number }}
          REPO_NAME: ${{ github.repository }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  test:
    runs-on: ubuntu-latest
    needs: checkrun
    if: ${{ needs.checkrun.outputs.build == 'strawberry' && github.repository_owner == 'frappe' }}
    timeout-minutes: 60
    env:
      NODE_ENV: "production"

    strategy:
      fail-fast: false
      matrix:
       # Make sure you modify coverage submission file list if changing this
       container: [1, 2, 3]

    name: UI Tests (Cypress)

    services:
      mariadb:
        image: mariadb:10.6
        env:
          MARIADB_ROOT_PASSWORD: travis
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=5s --health-timeout=2s --health-retries=3

    steps:
      - name: Clone
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Check for valid Python & Merge Conflicts
        run: |
          python -m compileall -q -f "${GITHUB_WORKSPACE}"
          if grep -lr --exclude-dir=node_modules "^<<<<<<< " "${GITHUB_WORKSPACE}"
              then echo "Found merge conflicts"
              exit 1
          fi

      - uses: actions/setup-node@v3
        with:
          node-version: 18
          check-latest: true

      - name: Add to Hosts
        run: |
          echo "127.0.0.1 test_site" | sudo tee -a /etc/hosts

      - name: Cache pip
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/*requirements.txt', '**/pyproject.toml', '**/setup.py') }}
          restore-keys: |
            ${{ runner.os }}-pip-
            ${{ runner.os }}-

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn cache dir)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v3
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-ui-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-ui-

      - name: Cache cypress binary
        uses: actions/cache@v3
        with:
          path: ~/.cache/Cypress
          key: ${{ runner.os }}-cypress

      - name: Install Dependencies
        run: |
          bash ${GITHUB_WORKSPACE}/.github/helper/install_dependencies.sh
          bash ${GITHUB_WORKSPACE}/.github/helper/install.sh
        env:
          BEFORE: ${{ env.GITHUB_EVENT_PATH.before }}
          AFTER: ${{ env.GITHUB_EVENT_PATH.after }}
          TYPE: ui
          DB: mariadb

      - name: Verify yarn.lock
        run: |
          cd ~/frappe-bench/apps/frappe
          git diff --exit-code yarn.lock

      - name: Build
        run: cd ~/frappe-bench/ && bench build --apps frappe

      - name: Site Setup
        run: |
          cd ~/frappe-bench/
          bench --site test_site execute frappe.utils.install.complete_setup_wizard
          bench --site test_site execute frappe.tests.ui_test_helpers.create_test_user

      - name: UI Tests
        run: cd ~/frappe-bench/ && bench --site test_site run-ui-tests frappe --headless --parallel --ci-build-id $GITHUB_RUN_ID-$GITHUB_RUN_ATTEMPT
        env:
          CYPRESS_RECORD_KEY: 4a48f41c-11b3-425b-aa88-c58048fa69eb

      - name: Show bench output
        if: ${{ always() }}
        run: cat ~/frappe-bench/bench_start.log || true

  faux-test:
    runs-on: ubuntu-latest
    needs: checkrun
    if: ${{ needs.checkrun.outputs.build != 'strawberry' && github.repository_owner == 'frappe' }}
    name: UI Tests (Cypress)
    strategy:
      matrix:
       container: [1, 2, 3]

    steps:
      - name: Pass skipped tests unconditionally
        run: "echo Skipped"
