name: <PERSON> (MariaDB)

on:
  pull_request:
  workflow_dispatch:

concurrency:
  group: patch-mariadb-develop-${{ github.event_name }}-${{ github.event.number }}
  cancel-in-progress: true

permissions:
  # Do not change this as GITHUB_TOKEN is being used by roulette
  contents: read

jobs:
  checkrun:
    name: Build Check
    runs-on: ubuntu-latest

    outputs:
      build: ${{ steps.check-build.outputs.build }}

    steps:
      - name: <PERSON><PERSON>
        uses: actions/checkout@v4

      - name: Check if build should be run
        id: check-build
        run: |
          python "${GITHUB_WORKSPACE}/.github/helper/roulette.py"
        env:
          TYPE: "server"
          PR_NUMBER: ${{ github.event.number }}
          REPO_NAME: ${{ github.repository }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  test:
    name: Patch
    runs-on: ubuntu-latest
    needs: checkrun
    if: ${{ needs.checkrun.outputs.build == 'strawberry' }}
    timeout-minutes: 60

    services:
      mariadb:
        image: mariadb:10.6
        env:
          MARIADB_ROOT_PASSWORD: travis
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=5s --health-timeout=2s --health-retries=3

    steps:
      - name: Clone
        uses: actions/checkout@v4

      - name: Check for Merge Conflicts
        run: |
          if grep -lr --exclude-dir=node_modules "^<<<<<<< " "${GITHUB_WORKSPACE}"
              then echo "Found merge conflicts"
              exit 1
          fi

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.10"

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 18
          check-latest: true

      - name: Add to Hosts
        run: echo "127.0.0.1 test_site" | sudo tee -a /etc/hosts

      - name: Cache pip
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/*requirements.txt', '**/pyproject.toml', '**/setup.py') }}
          restore-keys: |
            ${{ runner.os }}-pip-
            ${{ runner.os }}-

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn cache dir)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v3
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install Dependencies
        run: |
          bash ${GITHUB_WORKSPACE}/.github/helper/install_dependencies.sh
          pip install frappe-bench
          bash ${GITHUB_WORKSPACE}/.github/helper/install.sh
        env:
          BEFORE: ${{ env.GITHUB_EVENT_PATH.before }}
          AFTER: ${{ env.GITHUB_EVENT_PATH.after }}
          TYPE: server
          DB: mariadb

      - name: Run Patch Tests
        run: |
          cd ~/frappe-bench/
          sed -i 's/^worker:/# worker:/g' Procfile
          wget https://frappeframework.com/files/v13-frappe.sql.gz
          bench --site test_site --force restore ~/frappe-bench/v13-frappe.sql.gz

          source env/bin/activate
          cd apps/frappe/
          git remote set-url upstream https://github.com/frappe/frappe.git

          function update_to_version() {
            version=$1

            branch_name="version-$version-hotfix"
            echo "Updating to v$version"
            git fetch --depth 1 upstream $branch_name:$branch_name
            git checkout -q -f $branch_name

            pgrep honcho | xargs kill
            sleep 3
            rm -rf ~/frappe-bench/env
            bench -v setup env
            bench start &>> ~/frappe-bench/bench_start.log &

            bench --site test_site migrate
          }

          update_to_version 14

          echo "Updating to last commit"
          pgrep honcho | xargs kill
          sleep 3
          rm -rf ~/frappe-bench/env
          git checkout -q -f "$GITHUB_SHA"
          bench -v setup env
          bench start &>> ~/frappe-bench/bench_start.log &
          bench --site test_site migrate
          bench --site test_site execute frappe.tests.utils.check_orpahned_doctypes

      - name: Show bench output
        if: ${{ always() }}
        run: |
          cd ~/frappe-bench
          cat bench_start.log || true
          cd logs
          for f in ./*.log*; do
            echo "Printing log: $f";
            cat $f
          done

  faux-test:
    name: Patch
    runs-on: ubuntu-latest
    needs: checkrun
    if: ${{ needs.checkrun.outputs.build != 'strawberry' }}

    steps:
      - name: Pass skipped tests unconditionally
        run: "echo Skipped"
