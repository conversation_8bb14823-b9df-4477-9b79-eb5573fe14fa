name: Backport
on:
  pull_request_target:
    types:
      - closed
      - labeled

jobs:
  main:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: Checkout Actions
        uses: actions/checkout@v4
        with:
          repository: "frappe/backport"
          path: ./actions
          ref: develop
      - name: Install Actions
        run: npm install --production --prefix ./actions
      - name: Run backport
        uses: ./actions/backport
        with:
          token: ${{secrets.RELEASE_TOKEN}}
          labelsToAdd: "backport"
          title: "{{originalTitle}}"
