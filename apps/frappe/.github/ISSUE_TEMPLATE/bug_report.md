---
name: Bug report
about: Report a bug encountered while using the Frappe Framework
labels: bug
---

<!--
Welcome to the Frappe Framework issue tracker! Before creating an issue, please heed the following:

1. This tracker should only be used to report bugs and request features / enhancements to Frappe
    - For questions and general support, use https://stackoverflow.com/questions/tagged/frappe
    - For documentation issues, refer to https://frappeframework.com/docs/user/en or the developer cheetsheet https://github.com/frappe/frappe/wiki/Developer-Cheatsheet
2. Use the search function before creating a new issue. Duplicates will be closed and directed to
   the original discussion.
3. When making a bug report, make sure you provide all required information. The easier it is for
   maintainers to reproduce, the faster it'll be fixed.
4. If you think you know what the reason for the bug is, share it with us. Maybe put in a PR 😉
-->

## Description of the issue

## Context information (for bug reports)

**Output of `bench version`**
```
(paste here)
```

## Steps to reproduce the issue

1.
2.
3.

### Observed result

### Expected result

### Stacktrace / full error message

```
(paste here)
```

## Additional information

OS version / distribution, `Frappe` install method, etc.
