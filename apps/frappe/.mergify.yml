pull_request_rules:
  - name: Auto-close PRs on stable branch
    conditions:
      - and:
        - and:
          - author!=surajshetty3416
          - author!=deepeshgarg007
          - author!=ankush
          - author!=frappe-pr-bot
          - author!=mergify[bot]
        - or:
          - base=version-15
          - base=version-14
          - base=version-13
          - base=version-12
    actions:
      close:
      comment:
          message: |
            @{{author}}, thanks for the contribution, but we do not accept pull requests on a stable branch. Please raise PR on an appropriate hotfix branch.
            https://github.com/frappe/erpnext/wiki/Pull-Request-Checklist#which-branch

  - name: Automatic merge on CI success and review
    conditions:
      - label!=dont-merge
      - label!=squash
      - "#approved-reviews-by>=1"
    actions:
      merge:
        method: merge
  - name: Automatic squash on CI success and review
    conditions:
      - label!=dont-merge
      - label=squash
      - "#approved-reviews-by>=1"
    actions:
      merge:
        method: squash
        commit_message_template: |
            {{ title }} (#{{ number }})

            {{ body }}

  - name: backport to develop
    conditions:
      - label="backport develop"
    actions:
      backport:
        branches:
          - develop
        assignees:
          - "{{ author }}"

  - name: backport to version-13-hotfix
    conditions:
      - label="backport version-13-hotfix"
    actions:
      backport:
        branches:
          - version-13-hotfix
        assignees:
          - "{{ author }}"

  - name: backport to version-14-hotfix
    conditions:
      - label="backport version-14-hotfix"
    actions:
      backport:
        branches:
          - version-14-hotfix
        assignees:
          - "{{ author }}"

