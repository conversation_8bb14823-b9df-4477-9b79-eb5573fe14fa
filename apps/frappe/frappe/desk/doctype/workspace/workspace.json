{"actions": [], "allow_rename": 1, "autoname": "field:label", "beta": 1, "creation": "2020-01-23 13:45:59.470592", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["label", "title", "sequence_id", "for_user", "parent_page", "module", "column_break_3", "icon", "indicator_color", "restrict_to_domain", "hide_custom", "public", "is_hidden", "content", "number_cards_tab", "number_cards", "tab_break_2", "charts", "tab_break_15", "shortcuts", "tab_break_18", "links", "quick_lists_tab", "quick_lists", "custom_blocks_tab", "custom_blocks", "roles_tab", "roles"], "fields": [{"fieldname": "label", "fieldtype": "Data", "label": "Name", "reqd": 1, "unique": 1}, {"collapsible": 1, "collapsible_depends_on": "charts", "fieldname": "tab_break_2", "fieldtype": "Tab Break", "label": "Dashboards"}, {"fieldname": "charts", "fieldtype": "Table", "label": "Charts", "options": "Workspace Chart"}, {"fieldname": "shortcuts", "fieldtype": "Table", "label": "Shortcuts", "options": "Workspace Shortcut"}, {"fieldname": "restrict_to_domain", "fieldtype": "Link", "label": "Restrict to Domain", "options": "Domain", "search_index": 1}, {"fieldname": "module", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "<PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"collapsible": 1, "collapsible_depends_on": "shortcuts", "fieldname": "tab_break_15", "fieldtype": "Tab Break", "label": "Shortcuts"}, {"collapsible": 1, "collapsible_depends_on": "links", "fieldname": "tab_break_18", "fieldtype": "Tab Break", "label": "Link Cards"}, {"fieldname": "for_user", "fieldtype": "Data", "label": "For User", "read_only": 1}, {"default": "0", "description": "Checking this will hide custom doctypes and reports cards in Links section", "fieldname": "hide_custom", "fieldtype": "Check", "label": "Hide Custom DocTypes and Reports"}, {"fieldname": "icon", "fieldtype": "Icon", "label": "Icon", "read_only": 1}, {"fieldname": "links", "fieldtype": "Table", "label": "Links", "options": "Workspace Link"}, {"default": "0", "fieldname": "public", "fieldtype": "Check", "in_list_view": 1, "in_standard_filter": 1, "label": "Public", "read_only": 1, "search_index": 1}, {"fieldname": "title", "fieldtype": "Data", "label": "Title", "read_only": 1, "reqd": 1}, {"fieldname": "parent_page", "fieldtype": "Data", "label": "<PERSON><PERSON>", "read_only": 1}, {"default": "[]", "fieldname": "content", "fieldtype": "Long Text", "hidden": 1, "label": "Content"}, {"fieldname": "sequence_id", "fieldtype": "Float", "label": "Sequence Id", "read_only": 1}, {"fieldname": "roles", "fieldtype": "Table", "label": "Roles", "options": "Has Role"}, {"fieldname": "roles_tab", "fieldtype": "Tab Break", "label": "Roles"}, {"fieldname": "quick_lists_tab", "fieldtype": "Tab Break", "label": "Quick Lists"}, {"fieldname": "quick_lists", "fieldtype": "Table", "label": "Quick Lists", "options": "Workspace Quick List"}, {"default": "0", "fieldname": "is_hidden", "fieldtype": "Check", "label": "Is Hidden"}, {"fieldname": "number_cards_tab", "fieldtype": "Tab Break", "label": "Number Cards"}, {"fieldname": "number_cards", "fieldtype": "Table", "label": "Number Cards", "options": "Workspace Number Card"}, {"fieldname": "custom_blocks_tab", "fieldtype": "Tab Break", "label": "Custom Blocks"}, {"fieldname": "custom_blocks", "fieldtype": "Table", "label": "Custom Blocks", "options": "Workspace Custom Block"}, {"depends_on": "doc.icon", "fieldname": "indicator_color", "fieldtype": "Select", "label": "Indicator Color", "options": "green\ncyan\nblue\norange\nyellow\ngray\ngrey\nred\npink\ndarkgrey\npurple\nlight-blue"}], "in_create": 1, "links": [], "modified": "2024-05-30 17:30:36.791171", "modified_by": "Administrator", "module": "Desk", "name": "Workspace", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Workspace Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Desk User", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}