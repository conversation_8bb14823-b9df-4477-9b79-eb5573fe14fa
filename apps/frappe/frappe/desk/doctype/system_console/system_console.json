{"actions": [{"action": "/app/console-log", "action_type": "Route", "label": "Logs"}, {"action": "frappe.desk.doctype.system_console.system_console.execute_code", "action_type": "Server Action", "hidden": 1, "label": "Execute"}], "creation": "2020-08-18 17:44:35.647815", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["execute_section", "type", "console", "commit", "output", "sql_output", "database_processes_section", "show_processlist", "processlist"], "fields": [{"description": "To print output use <code>print(text)</code>", "fieldname": "console", "fieldtype": "Code", "label": "<PERSON><PERSON><PERSON>", "options": "Python"}, {"fieldname": "output", "fieldtype": "Code", "label": "Output", "read_only": 1}, {"default": "0", "fieldname": "commit", "fieldtype": "Check", "label": "Commit"}, {"fieldname": "execute_section", "fieldtype": "Section Break", "label": "Execute"}, {"fieldname": "database_processes_section", "fieldtype": "Section Break", "label": "Database Processes"}, {"default": "0", "fieldname": "show_processlist", "fieldtype": "Check", "label": "Show Processlist"}, {"fieldname": "processlist", "fieldtype": "HTML", "label": "processlist"}, {"default": "Python", "fieldname": "type", "fieldtype": "Select", "label": "Type", "options": "Python\nSQL"}, {"depends_on": "eval:doc.type == 'SQL'", "fieldname": "sql_output", "fieldtype": "HTML", "label": "SQL Output"}], "hide_toolbar": 1, "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2023-11-03 13:02:00.706806", "modified_by": "Administrator", "module": "Desk", "name": "System Console", "owner": "Administrator", "permissions": [{"email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}