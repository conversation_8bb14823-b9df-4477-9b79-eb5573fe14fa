{"actions": [], "autoname": "hash", "creation": "2012-07-03 13:30:35", "doctype": "DocType", "document_type": "Setup", "email_append_to": 1, "engine": "InnoDB", "field_order": ["description_and_status", "status", "priority", "column_break_2", "color", "date", "allocated_to", "description_section", "description", "section_break_6", "reference_type", "reference_name", "column_break_10", "role", "assigned_by", "assigned_by_full_name", "sender", "assignment_rule"], "fields": [{"fieldname": "description_and_status", "fieldtype": "Section Break"}, {"default": "Open", "fieldname": "status", "fieldtype": "Select", "in_global_search": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Open\nClosed\nCancelled"}, {"default": "Medium", "fieldname": "priority", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Priority", "oldfieldname": "priority", "oldfieldtype": "Data", "options": "High\nMedium\nLow"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "color", "fieldtype": "Color", "label": "Color"}, {"allow_in_quick_entry": 1, "default": "Today", "fieldname": "date", "fieldtype": "Date", "in_list_view": 1, "in_standard_filter": 1, "label": "Due Date", "oldfieldname": "date", "oldfieldtype": "Date"}, {"fieldname": "description_section", "fieldtype": "Section Break"}, {"fieldname": "description", "fieldtype": "Text Editor", "in_global_search": 1, "label": "Description", "oldfieldname": "description", "oldfieldtype": "Text", "print_width": "300px", "reqd": 1, "width": "300px"}, {"fieldname": "section_break_6", "fieldtype": "Section Break", "label": "Reference"}, {"fieldname": "reference_type", "fieldtype": "Link", "in_list_view": 1, "label": "Reference Type", "oldfieldname": "reference_type", "oldfieldtype": "Data", "options": "DocType"}, {"fieldname": "reference_name", "fieldtype": "Dynamic Link", "label": "Reference Name", "oldfieldname": "reference_name", "oldfieldtype": "Data", "options": "reference_type"}, {"fieldname": "column_break_10", "fieldtype": "Column Break"}, {"fieldname": "role", "fieldtype": "Link", "label": "Role", "oldfieldname": "role", "oldfieldtype": "Link", "options": "Role"}, {"fieldname": "assigned_by", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Assigned By", "options": "User"}, {"fetch_from": "assigned_by.full_name", "fieldname": "assigned_by_full_name", "fieldtype": "Read Only", "label": "Assigned By Full Name"}, {"fieldname": "sender", "fieldtype": "Data", "hidden": 1, "label": "Sender", "options": "Email"}, {"fieldname": "assignment_rule", "fieldtype": "Link", "label": "Assignment Rule", "options": "Assignment Rule", "read_only": 1}, {"fieldname": "allocated_to", "fieldtype": "Link", "ignore_user_permissions": 1, "in_global_search": 1, "in_standard_filter": 1, "label": "Allocated To", "options": "User"}], "icon": "fa fa-check", "idx": 2, "links": [], "modified": "2023-10-05 07:44:38.476400", "modified_by": "Administrator", "module": "Desk", "name": "ToDo", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "All", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "search_fields": "description, reference_type, reference_name", "sender_field": "sender", "sort_field": "modified", "sort_order": "DESC", "subject_field": "description", "title_field": "description", "track_changes": 1, "track_seen": 1}