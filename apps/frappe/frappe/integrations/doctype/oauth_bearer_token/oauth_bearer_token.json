{"actions": [], "autoname": "field:access_token", "creation": "2016-08-24 14:10:17.471264", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["client", "user", "scopes", "access_token", "refresh_token", "expiration_time", "expires_in", "status"], "fields": [{"fieldname": "client", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Client", "options": "OAuth Client", "read_only": 1}, {"fieldname": "user", "fieldtype": "Link", "label": "User", "options": "User", "read_only": 1}, {"fieldname": "scopes", "fieldtype": "Text", "label": "<PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "access_token", "fieldtype": "Data", "label": "Access Token", "read_only": 1, "unique": 1}, {"fieldname": "refresh_token", "fieldtype": "Data", "label": "Refresh <PERSON>", "read_only": 1}, {"fieldname": "expiration_time", "fieldtype": "Datetime", "label": "Expiration time", "read_only": 1}, {"fieldname": "expires_in", "fieldtype": "Int", "label": "Expires In", "read_only": 1}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Active\nRevoked", "read_only": 1}], "links": [], "modified": "2023-04-07 07:08:00.249740", "modified_by": "Administrator", "module": "Integrations", "name": "<PERSON><PERSON><PERSON>", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}