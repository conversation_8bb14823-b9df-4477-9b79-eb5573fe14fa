{"actions": [], "creation": "2016-09-21 10:12:57.399174", "doctype": "DocType", "document_type": "System", "editable_grid": 1, "engine": "InnoDB", "field_order": ["enabled", "send_notifications_to", "send_email_for_successful_backup", "backup_frequency", "limit_no_of_backups", "no_of_backups", "file_backup", "app_access_key", "app_secret_key", "allow_dropbox_access", "dropbox_refresh_token", "dropbox_access_token"], "fields": [{"default": "0", "fieldname": "enabled", "fieldtype": "Check", "label": "Enabled"}, {"fieldname": "send_notifications_to", "fieldtype": "Data", "in_list_view": 1, "label": "Send Notifications To", "reqd": 1}, {"default": "1", "description": "Note: By default emails for failed backups are sent.", "fieldname": "send_email_for_successful_backup", "fieldtype": "Check", "label": "Send Email for Successful Backup"}, {"fieldname": "backup_frequency", "fieldtype": "Select", "in_list_view": 1, "label": "Backup Frequency", "options": "\nDaily\nWeekly", "reqd": 1}, {"default": "0", "fieldname": "limit_no_of_backups", "fieldtype": "Check", "label": "Limit Number of DB Backups"}, {"default": "5", "depends_on": "eval:doc.limit_no_of_backups", "fieldname": "no_of_backups", "fieldtype": "Int", "label": "Number of DB Backups"}, {"default": "1", "fieldname": "file_backup", "fieldtype": "Check", "label": "File Backup"}, {"fieldname": "app_access_key", "fieldtype": "Data", "label": "App Access Key"}, {"fieldname": "app_secret_key", "fieldtype": "Password", "label": "App Secret Key"}, {"fieldname": "allow_dropbox_access", "fieldtype": "<PERSON><PERSON>", "label": "Allow Dropbox Access"}, {"fieldname": "dropbox_refresh_token", "fieldtype": "Password", "hidden": 1, "label": "Dropbox Refresh Token", "no_copy": 1, "read_only": 1}, {"fieldname": "dropbox_access_token", "fieldtype": "Password", "hidden": 1, "label": "Dropbox Access Token"}], "in_create": 1, "issingle": 1, "links": [], "modified": "2023-03-20 14:20:19.180611", "modified_by": "Administrator", "module": "Integrations", "name": "Dropbox Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}], "read_only": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}