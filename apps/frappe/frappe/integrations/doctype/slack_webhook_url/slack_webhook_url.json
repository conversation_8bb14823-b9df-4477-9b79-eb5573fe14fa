{"actions": [], "autoname": "field:webhook_name", "creation": "2018-05-22 13:20:51.450815", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["webhook_name", "webhook_url", "show_document_link"], "fields": [{"fieldname": "webhook_name", "fieldtype": "Data", "in_list_view": 1, "label": "Name", "reqd": 1, "unique": 1}, {"fieldname": "webhook_url", "fieldtype": "Data", "in_list_view": 1, "label": "Webhook URL", "reqd": 1}, {"allow_in_quick_entry": 1, "default": "1", "fieldname": "show_document_link", "fieldtype": "Check", "label": "Show link to document"}], "links": [], "modified": "2021-05-12 18:24:37.810235", "modified_by": "Administrator", "module": "Integrations", "name": "Slack Webhook URL", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}