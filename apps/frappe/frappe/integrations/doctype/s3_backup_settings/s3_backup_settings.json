{"actions": [], "creation": "2017-09-04 20:57:20.129205", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["enabled", "api_access_section", "access_key_id", "column_break_4", "secret_access_key", "notification_section", "notify_email", "column_break_8", "send_email_for_successful_backup", "s3_bucket_details_section", "bucket", "endpoint_url", "column_break_13", "backup_details_section", "frequency", "backup_files"], "fields": [{"default": "0", "fieldname": "enabled", "fieldtype": "Check", "label": "Enable Automatic Backup"}, {"fieldname": "notify_email", "fieldtype": "Data", "in_list_view": 1, "label": "Send Notifications To", "mandatory_depends_on": "enabled", "reqd": 1}, {"default": "1", "description": "By default, emails are only sent for failed backups.", "fieldname": "send_email_for_successful_backup", "fieldtype": "Check", "label": "Send Email for Successful Backup"}, {"fieldname": "frequency", "fieldtype": "Select", "in_list_view": 1, "label": "Backup Frequency", "mandatory_depends_on": "enabled", "options": "Daily\nWeekly\nMonthly\nNone", "reqd": 1}, {"fieldname": "access_key_id", "fieldtype": "Data", "in_list_view": 1, "label": "Access Key ID", "mandatory_depends_on": "enabled", "reqd": 1}, {"fieldname": "secret_access_key", "fieldtype": "Password", "in_list_view": 1, "label": "Access Key Secret", "mandatory_depends_on": "enabled", "reqd": 1}, {"default": "https://s3.amazonaws.com", "description": "Only change this if you want to use other S3 compatible object storage backends.", "fieldname": "endpoint_url", "fieldtype": "Data", "label": "Endpoint URL"}, {"fieldname": "bucket", "fieldtype": "Data", "label": "Bucket Name", "mandatory_depends_on": "enabled", "reqd": 1}, {"depends_on": "enabled", "fieldname": "api_access_section", "fieldtype": "Section Break", "label": "API Access"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"depends_on": "enabled", "fieldname": "notification_section", "fieldtype": "Section Break", "label": "Notification"}, {"fieldname": "column_break_8", "fieldtype": "Column Break"}, {"depends_on": "enabled", "fieldname": "s3_bucket_details_section", "fieldtype": "Section Break", "label": "S3 Bucket Details"}, {"fieldname": "column_break_13", "fieldtype": "Column Break"}, {"depends_on": "enabled", "fieldname": "backup_details_section", "fieldtype": "Section Break", "label": "Backup Details"}, {"default": "1", "description": "Backup public and private files along with the database.", "fieldname": "backup_files", "fieldtype": "Check", "label": "Backup Files"}], "hide_toolbar": 1, "issingle": 1, "links": [], "modified": "2023-01-11 15:38:20.333833", "modified_by": "Administrator", "module": "Integrations", "name": "S3 Backup Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}