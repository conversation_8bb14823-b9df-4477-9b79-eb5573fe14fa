{"actions": [], "creation": "2020-08-01 23:38:41.783206", "doctype": "DocType", "engine": "InnoDB", "field_order": ["item_label", "item_type", "route", "action", "hidden", "is_standard", "column_break_dtwu", "condition"], "fields": [{"columns": 2, "fieldname": "item_label", "fieldtype": "Data", "in_list_view": 1, "label": "Item Label", "mandatory_depends_on": "eval:doc.item_type == 'Route' || doc.item_type == 'Action'", "show_days": 1, "show_seconds": 1}, {"columns": 2, "fieldname": "item_type", "fieldtype": "Select", "in_list_view": 1, "label": "Item Type", "options": "Route\nAction\nSeparator", "read_only_depends_on": "eval:doc.is_standard", "show_days": 1, "show_seconds": 1}, {"default": "0", "fieldname": "hidden", "fieldtype": "Check", "in_list_view": 1, "label": "Hidden", "show_days": 1, "show_seconds": 1}, {"default": "0", "fieldname": "is_standard", "fieldtype": "Check", "label": "Is Standard", "read_only": 1, "show_days": 1, "show_seconds": 1}, {"columns": 4, "depends_on": "eval:doc.item_type == 'Route'", "fieldname": "route", "fieldtype": "Data", "in_list_view": 1, "label": "Route", "mandatory_depends_on": "eval:doc.item_type == 'Route'", "read_only_depends_on": "eval:doc.is_standard", "show_days": 1, "show_seconds": 1}, {"depends_on": "eval:doc.item_type == 'Action'", "fieldname": "action", "fieldtype": "Data", "label": "Action", "mandatory_depends_on": "eval:doc.item_type == 'Action'", "read_only_depends_on": "eval:doc.is_standard", "show_days": 1, "show_seconds": 1}, {"fieldname": "condition", "fieldtype": "Code", "label": "Condition", "options": "Javascript"}, {"fieldname": "column_break_dtwu", "fieldtype": "Column Break"}], "istable": 1, "links": [], "modified": "2024-11-15 14:12:19.803995", "modified_by": "Administrator", "module": "Core", "name": "<PERSON><PERSON><PERSON>", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}