{"actions": [], "creation": "2019-09-23 16:28:13.953520", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["label", "action_type", "action", "group", "hidden", "custom"], "fields": [{"columns": 2, "fieldname": "label", "fieldtype": "Data", "in_list_view": 1, "label": "Label", "reqd": 1}, {"fieldname": "group", "fieldtype": "Data", "in_list_view": 1, "label": "Group"}, {"columns": 2, "fieldname": "action_type", "fieldtype": "Select", "in_list_view": 1, "label": "Action Type", "options": "Server Action\nRoute", "reqd": 1}, {"columns": 4, "fieldname": "action", "fieldtype": "Small Text", "in_list_view": 1, "label": "Action / Route", "reqd": 1}, {"default": "0", "fieldname": "hidden", "fieldtype": "Check", "label": "Hidden"}, {"default": "0", "fieldname": "custom", "fieldtype": "Check", "hidden": 1, "label": "Custom"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2020-09-24 14:19:05.549835", "modified_by": "Administrator", "module": "Core", "name": "DocType Action", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}