{"actions": [], "creation": "2020-09-08 10:17:54.366279", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["field", "condition", "value"], "fields": [{"fieldname": "field", "fieldtype": "Select", "in_list_view": 1, "label": "Field", "reqd": 1}, {"fieldname": "condition", "fieldtype": "Select", "in_list_view": 1, "label": "Condition", "options": "=\n!=\n>\n<\n>=\n<=", "reqd": 1}, {"fieldname": "value", "fieldtype": "Data", "in_list_view": 1, "label": "Value", "reqd": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2020-09-08 10:19:56.192949", "modified_by": "Administrator", "module": "Core", "name": "Document Naming Rule Condition", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}