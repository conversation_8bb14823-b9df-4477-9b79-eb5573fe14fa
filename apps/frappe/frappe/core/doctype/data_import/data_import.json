{"actions": [], "autoname": "format:{reference_doctype} Import on {creation}", "beta": 1, "creation": "2019-08-04 14:16:08.318714", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["reference_doctype", "import_type", "download_template", "import_file", "payload_count", "html_5", "google_sheets_url", "refresh_google_sheet", "column_break_5", "status", "submit_after_import", "mute_emails", "template_options", "import_warnings_section", "template_warnings", "import_warnings", "section_import_preview", "import_preview", "import_log_section", "show_failed_logs", "import_log_preview"], "fields": [{"fieldname": "reference_doctype", "fieldtype": "Link", "in_list_view": 1, "label": "Document Type", "options": "DocType", "reqd": 1, "set_only_once": 1}, {"fieldname": "import_type", "fieldtype": "Select", "in_list_view": 1, "label": "Import Type", "options": "\nInsert New Records\nUpdate Existing Records", "reqd": 1, "set_only_once": 1}, {"depends_on": "eval:!doc.__islocal", "fieldname": "import_file", "fieldtype": "Attach", "in_list_view": 1, "label": "Import File", "read_only_depends_on": "eval: ['Success', 'Partial Success'].includes(doc.status)"}, {"fieldname": "import_preview", "fieldtype": "HTML", "label": "Import Preview"}, {"fieldname": "section_import_preview", "fieldtype": "Section Break", "label": "Preview"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "template_options", "fieldtype": "Code", "hidden": 1, "label": "Template Options", "options": "JSON", "read_only": 1}, {"fieldname": "import_log_section", "fieldtype": "Section Break", "label": "Import Log"}, {"fieldname": "import_log_preview", "fieldtype": "HTML", "label": "Import Log Preview"}, {"default": "Pending", "fieldname": "status", "fieldtype": "Select", "hidden": 1, "label": "Status", "no_copy": 1, "options": "Pending\nSuccess\nPartial Success\nError\nTimed Out", "read_only": 1}, {"fieldname": "template_warnings", "fieldtype": "Code", "hidden": 1, "label": "Template Warnings", "options": "JSON"}, {"default": "0", "fieldname": "submit_after_import", "fieldtype": "Check", "label": "Submit After Import", "set_only_once": 1}, {"fieldname": "import_warnings_section", "fieldtype": "Section Break", "label": "Import File Errors and Warnings"}, {"fieldname": "import_warnings", "fieldtype": "HTML", "label": "Import Warnings"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "download_template", "fieldtype": "<PERSON><PERSON>", "label": "Download Template"}, {"default": "1", "fieldname": "mute_emails", "fieldtype": "Check", "label": "Don't Send Emails", "set_only_once": 1}, {"default": "0", "fieldname": "show_failed_logs", "fieldtype": "Check", "label": "Show Only Failed Logs"}, {"depends_on": "eval:!doc.__islocal && !doc.import_file", "fieldname": "html_5", "fieldtype": "HTML", "options": "<h5 class=\"text-muted uppercase\">Or</h5>"}, {"depends_on": "eval:!doc.__islocal && !doc.import_file\n", "description": "Must be a publicly accessible Google Sheets URL", "fieldname": "google_sheets_url", "fieldtype": "Data", "label": "Import from Google Sheets", "read_only_depends_on": "eval: ['Success', 'Partial Success'].includes(doc.status)"}, {"depends_on": "eval:doc.google_sheets_url && !doc.__unsaved &&  ['Success', 'Partial Success'].includes(doc.status)", "fieldname": "refresh_google_sheet", "fieldtype": "<PERSON><PERSON>", "label": "Refresh Google Sheet"}, {"fieldname": "payload_count", "fieldtype": "Int", "hidden": 1, "label": "Payload Count", "read_only": 1}], "hide_toolbar": 1, "links": [], "modified": "2024-01-30 17:08:05.566686", "modified_by": "Administrator", "module": "Core", "name": "Data Import", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}