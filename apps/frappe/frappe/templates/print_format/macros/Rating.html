{% extends "templates/print_format/macros/Data.html" %}

{% macro star(is_active=false) %}
<svg id="icon-star" class="rating-star {{ is_active and 'active' or '' }}" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    {%- set color = '#f6c35e' if is_active else '#dce0e3' -%}
    <path
        fill="{{ color }}" stroke="{{ color }}"
        d="M11.5516 2.90849C11.735 2.53687 12.265 2.53687 12.4484 2.90849L14.8226 7.71919C14.8954 7.86677 15.0362 7.96905 15.1991 7.99271L20.508 8.76415C20.9181 8.82374 21.0818 9.32772 20.7851 9.61699L16.9435 13.3616C16.8257 13.4765 16.7719 13.642 16.7997 13.8042L17.7066 19.0916C17.7766 19.5001 17.3479 19.8116 16.9811 19.6187L12.2327 17.1223C12.087 17.0457 11.913 17.0457 11.7673 17.1223L7.01888 19.6187C6.65207 19.8116 6.22335 19.5001 6.29341 19.0916L7.20028 13.8042C7.2281 13.642 7.17433 13.4765 7.05648 13.3616L3.21491 9.61699C2.91815 9.32772 3.08191 8.82374 3.49202 8.76415L8.80094 7.99271C8.9638 7.96905 9.10458 7.86677 9.17741 7.71919L11.5516 2.90849Z"
    />
</svg>
{% endmacro %}

{%- block value -%}
<div class="value">
    {%- for i in range(value) -%}
    {{ star(true) }}
    {%- endfor -%}
    {%- for i in range(5 - value) -%}
    {{ star() }}
    {%- endfor -%}
</div>
{%- endblock -%}
