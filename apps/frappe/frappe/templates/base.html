<!DOCTYPE html>
<!-- Built on Frappe. https://frappeframework.com/ -->
<html lang="{{boot.lang}}">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<meta name="generator" content="frappe">

	{% block meta_block %}
		{% include "templates/includes/meta_block.html" %}
	{% endblock %}

	<title>{% block title %}{{ title | striptags }}{% endblock %}</title>

	{% block favicon %}
	<link
		rel="shortcut icon"
		href="{{ (favicon or "/assets/frappe/images/frappe-favicon.svg") | abs_url }}"
		type="image/x-icon"
	>
	{% endblock %}

	{%- block head -%}
		{% include "templates/includes/head.html" %}
	{%- endblock -%}

	{%- block head_include %}
		{{ head_include or "" }}
	{% endblock -%}

	{%- block style %}
		{% if colocated_css -%}
			<style>{{ colocated_css }}</style>
		{%- endif %}
	{%- endblock -%}

	{% if boot.lang == "eo" %}
	<script type="text/javascript">
		var _jipt = [];
		_jipt.push(['project', 'frappe']);
	</script>
	<script type="text/javascript" src="https://cdn.crowdin.com/jipt/jipt.js"></script>
	{% endif %}

	<script>
		window.frappe = {};
		frappe.ready_events = [];
		frappe.ready = function(fn) {
			frappe.ready_events.push(fn);
		}
		window.dev_server = {{ dev_server }};
		window.socketio_port = {{ (frappe.socketio_port or 9000) }};
		window.show_language_picker = {{ show_language_picker or 'false' }};
	</script>
</head>
<body frappe-session-status="{{ 'logged-in' if frappe.session.user != 'Guest' else 'logged-out'}}" data-path="{{ path | e }}" {%- if template and template.endswith('.md') %} frappe-content-type="markdown" {%- endif %} class="{{ body_class or ''}}">
	{% include "public/icons/timeless/icons.svg" %}
	{% include "public/icons/espresso/icons.svg" %}
	{%- block banner -%}
		{% include "templates/includes/banner_extension.html" ignore missing %}

		{% if banner_html -%}
		{{ banner_html or "" }}
		{%- endif %}
	{%- endblock -%}

	{%- block navbar -%}
		{{ web_block(
			navbar_template or 'Standard Navbar',
			values=_context_dict,
			add_container=0,
			add_top_padding=0,
			add_bottom_padding=0,
		) }}
	{%- endblock -%}

	{% block content %}
		{{ content }}
	{% endblock %}

	{%- block footer -%}
		{{ web_block(
			footer_template or 'Standard Footer',
			values=_context_dict,
			add_container=0,
			add_top_padding=0,
			add_bottom_padding=0
		) }}
	{%- endblock -%}

	{% block base_scripts %}
	<!-- js should be loaded in body! -->
	<script>
		frappe.boot = {{ boot | json }}
		// for backward compatibility of some libs
		frappe.sys_defaults = frappe.boot.sysdefaults;
	</script>
	{{ include_script('frappe-web.bundle.js') }}
	{% endblock %}

	{%- for link in web_include_js %}
	{{ include_script(link) }}
	{%- endfor -%}

	{%- block script %}
		{% if colocated_js -%}
			<script>{{ colocated_js }}</script>
		{%- endif %}
	{%- endblock %}
	<!-- csrf_token -->
	{%- block body_include %}{{ body_include or "" }}{% endblock -%}
</body>
</html>
