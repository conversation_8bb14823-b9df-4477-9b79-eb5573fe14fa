{% extends "templates/base.html" %}
{%- from "templates/includes/navbar/navbar_items.html" import render_item -%}

{%- block head_include %}
<link rel="stylesheet" href="/assets/frappe/css/hljs-night-owl.css">
{% endblock -%}

{%- block navbar -%}
<nav class="navbar navbar-light navbar-expand-lg doc-navbar fixed-top">
	<div class="container-fluid doc-container">
		<div class="row no-gutters w-100">
			<div class="col-12 col-lg-2">
				<a class="navbar-brand" href="{{ url_prefix }}{{ home_page or "/" }}">
					{%- if brand_html -%}
					{{ brand_html }}
					{%- elif banner_image -%}
					<img src='{{ banner_image }}'>
					{%- else -%}
					<span>{{ (frappe.get_hooks("brand_html") or [_("Home")])[0] }}</span>
					{%- endif -%}
				</a>
			</div>
			<div class="col-12 col-lg-8">
				<div class="doc-search-container">
					<div class="website-search doc-search" id="search-container">
					</div>
					<button class="navbar-toggler" type="button"
						data-toggle="collapse"
						data-target="#navbarSupportedContent"
						aria-controls="navbarSupportedContent"
						aria-expanded="false"
						aria-label="Toggle navigation">
						<span>
							<svg class="icon icon-lg">
								<use href="#icon-menu"></use>
							</svg>
						</span>
					</button>
				</div>
			</div>
			<div class="col-12 col-lg-2">
				<div class="collapse navbar-collapse" id="navbarSupportedContent">
					<ul class="navbar-nav">
						{%- set items = docs_navbar_items or [] -%}
						{%- for item in items -%}
						{{ render_item(item, parent=True) }}
						{%- endfor -%}
					</ul>
					{% include "templates/includes/web_sidebar.html" %}
				</div>
			</div>
		</div>
	</div>
</nav>
{%- endblock -%}

{% block content %}


{% macro container_attributes() -%}
id="page-{{ name or route | e }}" data-path="{{ pathname | e }}"
{%- if page_or_generator=="Generator" %}source-type="Generator" data-doctype="{{ doctype }}"{%- endif %}
{%- if source_content_type %}source-content-type="{{ source_content_type }}"{%- endif %}
{%- endmacro %}

<div class="container-fluid doc-layout doc-container">
	<div class="row no-gutters" {{ container_attributes() }}>
		<div class="sidebar-column col-sm-2">
			<aside class="doc-sidebar">
				{% block page_sidebar %}
				{% include "templates/includes/web_sidebar.html" %}
				{% endblock %}
			</aside>
		</div>
		<div class="main-column doc-main col-12 col-lg-10 col-xl-8">
			<div class="page-content-wrapper">
				{% block page_container %}
				<main>
					<div class="page_content page-content doc-content">
						{%- if add_breadcrumbs -%}
						{% include "templates/includes/breadcrumbs.html" %}
						{%- endif -%}
						{%- block page_content -%}{%- endblock -%}
					</div>
				</main>
				{% endblock %}
			</div>
		</div>
		{%- if page_toc_html -%}
		<div class="page-toc col-sm-2 d-none d-xl-block">
			{% block page_toc %}
			{% if page_toc_html %}
			<div>
				<h5>On this page</h5>
				{{ page_toc_html }}
			</div>
			{% endif %}
			{% endblock %}
		</div>
		{%- endif -%}
	</div>
</div>

{% endblock %}

{%- block script -%}
<script>
	frappe.ready(() => {
		frappe.setup_search('#search-container', '{{ docs_search_scope or "" }}');

		$('.web-footer .container')
			.removeClass('container')
			.addClass('container-fluid doc-container');
	});
</script>
{%- endblock -%}
