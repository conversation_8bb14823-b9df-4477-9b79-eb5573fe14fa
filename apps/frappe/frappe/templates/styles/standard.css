@media screen {
	.print-format-gutter {
		background-color: #d1d8dd;
		padding: 30px 0px;
	}
	.print-format {
		background-color: white;
		border-radius: 8px;
		max-width: 8.3in;
		min-height: 11.69in;
		padding: 0.75in;
		margin: auto;
		color: var(--gray-900);
	}

	.print-format.landscape {
		max-width: 11.69in;
		padding: 0.2in;
	}

	.page-break {
		/* padding: 15px 0px; */
		border-bottom: 1px dashed #888;
	}

	/* .page-break:first-child {
		padding-top: 0px;
	} */

	.page-break:last-child {
		border-bottom: 0px;
	}

	/* mozilla hack for images in table */
	body:last-child .print-format td img {
		width: 100% !important;
	}

	@media(max-width: 767px) {
		.print-format {
			padding: 0.2in;
		}
	}
}

@media print {
	.print-format p {
		margin-left: 1px;
		margin-right: 1px;
	}
}

.disabled-check {
	color: #eee;
}

.data-field {
	margin-top: 5px;
	margin-bottom: 5px;
}

.data-field .value {
	word-wrap: break-word;
}

.important .value {
	font-size: 120%;
	font-weight: bold;
}

.important label {
	line-height: 1.8;
	margin: 0px;
}

.table {
	font-size: inherit;
	margin: 20px 0px;
}

.checkbox-options {
	columns: var(--checkbox-options-columns);
}

.square-image {
	width: 100%;
	height: 0;
	padding: 50% 0;
	background-size: contain;
	/*background-size: cover;*/
	background-repeat: no-repeat !important;
	background-position: center center;
	border-radius: 4px;
}

.print-item-image {
	object-fit: contain;
}

.pdf-variables,
.pdf-variable,
.visible-pdf {
	display: none !important;
}

.print-format {
	font-size: {{ print_settings.font_size|flt or 9 }}pt;
	font-family: {{ font }};
	-webkit-print-color-adjust:exact;
}

.page-break {
	page-break-after: always;
}

.print-heading {
	border-bottom: 1px solid #aaa;
	margin-bottom: 10px;
}

.print-heading h2 {
	margin: 0px;
}
.print-heading h4 {
	margin-top: 5px;
}

table.no-border, table.no-border td {
	border: 0px;
}

.print-format label {
	/* wkhtmltopdf breaks label into multiple lines when it is inline-block */
	display: block;
	font-weight: 700;
}

.print-format img {
	max-width: 100%;
}

.print-format table td > .primary:first-child {
	font-weight: bold;
}

.print-format td, .print-format th {
	vertical-align: top !important;
	padding: 6px !important;
}

.print-format p {
	margin: 3px 0px 3px;
}

.print-format table td pre {
	white-space: normal;
	word-break: normal;
}

table td div {
	{% if not print_settings.allow_page_break_inside_tables %}
	/* needed to avoid partial cutting of text between page break in wkhtmltopdf */
	page-break-inside: avoid !important;
	{% endif %}
}

/* hack for webkit specific browser */
@media (-webkit-min-device-pixel-ratio:0) {
	thead, tfoot {
		display: table-header-group;
	}
}

[document-status] {
	margin-bottom: 5mm;
}

.signature-img {
	background: #fff;
	border-radius: 3px;
	margin-top: 5px;
	max-height: 150px;
}

.print-format-preview [data-fieldtype="Table"] {
	overflow: auto;
}
