{%- from "templates/print_formats/standard_macros.html" import add_header, render_field with context -%}

{% for page in layout %}
<div class="page-break">
	<div {% if print_settings.repeat_header_footer %} id="header-html" class="hidden-pdf" {% endif %}>
		{{ add_header(loop.index, layout|len, doc, letter_head, no_letterhead, footer, print_settings, print_heading_template) }}
	</div>

	{% for section in page %}
	<div class="row section-break" data-label="{{ section.label or '' | e }}">
		{%- if doc.print_line_breaks and loop.index != 1 -%}<hr>{%- endif -%}
		{%- if doc.print_section_headings and section.label and section.has_data -%}
		<h4 class='col-sm-12'>{{ _(section.label) }}</h4>
		{%- endif -%}
		{%- set no_of_cols = section.columns|len -%}
		{% for column in section.columns %}
			<div class="col-xs-{{ (12 / no_of_cols)|int }} column-break">
			{% for df in column.fields %}
				{{ render_field(df, doc, no_of_cols) }}
			{% endfor %}
			</div>
		{% endfor %}
	</div>
	{% endfor %}

	<div {% if print_settings.repeat_header_footer %} id="footer-html" class="visible-pdf" {% endif %}>
		{% if not no_letterhead and footer %}
		<div class="letter-head-footer">
			{{ footer }}
		</div>
		{% endif %}
		{% if print_settings.repeat_header_footer %}
			<p class="text-center small page-number visible-pdf">
				{{ _("Page {0} of {1}").format('<span class="page"></span>', '<span class="topage"></span>') }}
			</p>
		{% endif %}
	</div>
</div>
{% endfor %}
