{% if google_analytics_id %}
<script>
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

  ga('create', '{{ google_analytics_id }}', 'auto');
  {% if google_analytics_anonymize_ip %}
  ga('set', 'anonymizeIp', true);
  {% endif %}

  $(document).on("mousedown", function(event) {
    if(!frappe && !frappe.get_route) return;
    frappe.last_target = event;
    var label = event.target.getAttribute("data-doctype")
        || (event.target.innerText && event.target.innerText.trim())
        || (event.target.textContent && event.target.textContent.trim())
        || event.target.title;
    if(!label) return;
    var category = frappe.get_route().splice(0, 2).join("/");
    ga('send', 'event', category, label);
  });
</script>
{% endif %}
