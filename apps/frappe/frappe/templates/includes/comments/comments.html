<div class="comment-view mb-6">
	{% if not comment_list %}
		<div class="no-comment">
			<p class="text-muted small">{{ _("No comments yet. ") }}
				<span class="hidden login-required">
					<a href="/login?redirect-to={{ pathname }}">{{ _("Login to start a new discussion") }}</a>
				</span>
				<span class="hidden start-discussion">{{ _("Start a new discussion") }}</span>
			</p>
		</div>
	{% endif %}

	{% if not is_communication %}
		<div class="add-comment-section mb-5">
			<div class="comment-form-wrapper">
				<div id="comment-form">
					<form class="new-comment">
						<fieldset class="new-comment-fields">
							<div class="user-details row" style="margin-bottom: 15px; display:none;">
								<div class="comment-by col-sm-6 pb-4">
									<div class="form-label mb-1">{{ _("Your Name") }}</div>
									<input class="form-control comment_by" name="comment_by" type="text">
								</div>
								<div class="col-sm-6">
									<div class="form-label mb-1">{{ _("Email") }}</div>
									<input class="form-control comment_email" name="comment_email" type="email">
								</div>
							</div>
							<div class="comment-text-area">
								<div class="form-label mb-1">{{ _("Add a comment") }}</div>
								<textarea class="form-control" name="comment" rows=5 ></textarea>
								<div class="text-muted small mt-1 mb-4">{{ _("Ctrl+Enter to add comment") }}</div>
							</div>
							<button class="btn btn-sm btn-primary small" id="submit-comment">{{ _("Comment") }}</button>
						</fieldset>
					</form>
				</div>
			</div>
		</div>
	{% endif %}

	<hr class="add-comment-hr my-5">

	<div itemscope itemtype="http://schema.org/UserComments" id="comment-list">
		<div class="add-comment mb-5">
			<div class="timeline-dot"></div>
			<button class="btn btn-sm btn-primary small add-comment-button">{{ _("Add a comment") }}</button>
		</div>
		<div class="comment-list">
			{% for comment in comment_list %}
				{% include "templates/includes/comments/comment.html" %}
			{% endfor %}
		</div>
	</div>
</div>

<script>
	frappe.ready(function() {
		let guest_allowed = parseInt("{{ guest_allowed or 0}}");
		let comment_count = "{{ comment_count }}";
		let full_name = ""
		let user_id = "";

		let update_timeline_line_length = function(direction, size) {
			if ($('.blog-container').length) {
				if (direction == 'top') {
					$('.blog-container')[0].style.setProperty('--comment-timeline-top', size);
				} else {
					let comment_timeline_bottom = $('.comment-list .comment-row:last-child').height() - 10;
					$('.blog-container')[0].style.setProperty('--comment-timeline-bottom', comment_timeline_bottom +'px');
				}
			}
		}

		let show_comment_box = function() {
			$('.comment-form-wrapper').show();
			update_timeline_line_length('top', '-20px');
			$('.add-comment-hr').hide();
			$('.add-comment').hide();
		}

		let hide_comment_box = function() {
			$('.comment-form-wrapper').hide();
			update_timeline_line_length('top', '8px');
			update_timeline_line_length('bottom');
			$('.add-comment-hr').show();
			$('.add-comment').show();
		}

		let $comment_count = $(`
			<div class="feedback-item comments">
				<span class="comment-icon">${frappe.utils.icon('small-message', 'md')}</span>
				<span class="comment-count">${comment_count}</span>
			</div>
		`);

		$('form').keydown(function(event) {
			if (event.ctrlKey && event.keyCode === 13) {
				$(this).find('#submit-comment').trigger('click');
			}
		})

		if (!frappe.is_user_logged_in()) {
			$(".user-details").toggle('hide');
			if (guest_allowed) {
				$('.start-discussion').removeClass('hidden');
			} else {
				$(".login-required, .comment-form-wrapper").toggleClass("hidden");

				$('.add-comment-button').text('{{ _("Login to comment") }}');
				$('.add-comment-button').click(() => {
					window.location.href = '/login?redirect-to={{ pathname }}';
				});
			}
		} else {
			$('input.comment_by').prop("disabled", true);
			$('input.comment_email').prop("disabled", true);

			full_name = frappe.get_cookie("full_name");
			user_id = frappe.get_cookie("user_id");
			if(user_id != "Guest") {
				$("[name='comment_email']").val(user_id);
				$("[name='comment_by']").val(full_name);
			}

			$('.start-discussion').removeClass('hidden');
		}

		$('.blog-feedback').append($comment_count);
		$("#comment-form textarea").val("");

		update_timeline_line_length('bottom');

		let n_comments = $(".comment-row").length;
		n_comments ? $(".no_comment").toggle(false) : show_comment_box();

		if(n_comments > 50) {
			$(".add-comment").toggle(false)
				.parent().append("<div class='text-muted'>Comments are closed.</div>")
		}

		$('.add-comment-button').click(() => {
			show_comment_box();
		});

		$("#submit-comment").click(function() {
			var args = {
				comment_by: $("[name='comment_by']").val(),
				comment_email: $("[name='comment_email']").val(),
				comment: $("[name='comment']").val(),
				reference_doctype: "{{ reference_doctype or doctype }}",
				reference_name: "{{ reference_name or name }}",
				comment_type: "Comment",
				route: "{{ pathname }}",
			}

			if(!args.comment_by || !args.comment_email || !args.comment) {
				frappe.msgprint('{{ _("All fields are necessary to submit the comment.") }}');
				return false;
			}

			if (args.comment_email!=='Administrator' && !validate_email(args.comment_email)) {
				frappe.msgprint('{{ _("Please enter a valid email address.") }}');
				return false;
			}

			if(!args.comment || !args.comment.trim()) {
				frappe.msgprint('{{ _("Please add a valid comment.") }}');
				return false;
			}

			frappe.call({
				btn: this,
				type: "POST",
				method: "frappe.templates.includes.comments.comments.add_comment",
				args: args,
				callback: function(r) {
					if(r.exc) {
						if(r._server_messages)
							frappe.msgprint(r._server_messages);
					} else {
						if (r.message) {
							$(r.message).prependTo(".comment-list");
							comment_count = cint(comment_count) + 1;
							$('.comment-count').text(comment_count);
						}
						$(".no-comment").toggle(false);
						$("#comment-form textarea").val("");
						hide_comment_box();
					}
				}
			})

			return false;
		});
	});
</script>

<style>

.add-comment-button {
	margin-left: 35px;
}

.timeline-dot {
	width: 16px;
	height: 16px;
	border-radius: 50%;
	position: absolute;
	top: 8px;
	left: 23.5px;
	background-color: var(--fg-color);
}

.timeline-dot::before {
	content: ' ';
	background: var(--gray-700);
	position: absolute;
	top: 5px;
	left: 5px;
	border-radius: 50%;
	height: 4px;
	width: 4px;
}

.comment-form-wrapper {
	display: none;
}

.login-required {
	padding: var(--padding-sm);
	border-radius: var(--border-radius-sm);
	box-shadow: var(--card-shadow);
}

.new-comment {
	display: flex;
	padding: var(--padding-lg);
	border: 1px solid var(--border-color);
	border-radius: var(--border-radius-md);
	background-color: var(--fg-color);
}

.new-comment-fields {
	flex: 1;
}

.new-comment .form-label {
	font-weight: var(--text-bold);
}

.new-comment .comment-text-area textarea {
	resize: none;
}

@media (min-width: 576px) {
	.comment-by {
		padding-right: 0px !important;
		padding-bottom: 0px !important;
	}
}

#comment-list {
	position: relative;
	padding-left: var(--padding-xl);
}

#comment-list::before {
	content: " ";
	position: absolute;
	top: var(--comment-timeline-top);
	bottom: var(--comment-timeline-bottom);
	border-left: 1px solid var(--dark-border-color);
}

.comment-row {
	position: relative;
}
.comment-avatar {
	position: absolute;
	top: 10px;
	left: -13.75px;
}

.comment-content {
	border: 1px solid var(--border-color);
	background-color: var(--fg-color);
	border-radius: var(--border-radius-md);
	padding: var(--padding-md);
	margin-left: 35px;
	flex: 1;
}

.comment-content .content p{
	margin-bottom: 0px;
}

</style>
