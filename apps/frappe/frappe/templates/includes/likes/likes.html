<div id="likes" class="feedback-item likes mr-3">
	<span class="like-icon"></span>
	<span class="like-count"></span>
</div>

<script type="text/javascript">
	frappe.ready(() => {
		let like = parseInt("{{ like or 0 }}");
		let like_count = parseInt("{{ like_count or 0 }}");

		let toggle_like_icon = function(active) {
			active ? $('.like-icon').addClass('liked') : $('.like-icon').removeClass('liked');
		}

		$('.like-icon').append(frappe.utils.icon('es-solid-heart', 'md'))
		toggle_like_icon(like);

		$('.like-count').text(like_count);

		$('.like-icon').click(() => {
			update_like();
		})

		let update_like = function() {
			like = !like;
			like ? like_count++ : like_count--;
			toggle_like_icon(like);
			$('.like-count').text(like_count);

			return frappe.call({
				method: "frappe.templates.includes.likes.likes.like",
				args: {
					reference_doctype: "{{ reference_doctype or doctype }}",
					reference_name: "{{ reference_name or name }}",
					like,
					route: "{{ pathname }}",
				}
			});
		}
	});
</script>