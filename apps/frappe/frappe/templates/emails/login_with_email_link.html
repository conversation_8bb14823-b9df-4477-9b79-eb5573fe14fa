{% macro table(content, table_class) %}
<table class="{{ table_class or '' }}" cellpadding="0" cellspacing="0" width="100%" align="center">
	<tbody>
		<tr>
			<td align="center">
				{{ content }}
			</td>
		</tr>
	</tbody>
</table>
{% endmacro %}

{% macro body() %}
<table width="100%" cellspacing="0" cellpadding="0">
	<tbody>
		<tr>
			<td align="center">
				<div class="email-header-title">
					{{ _('Click on the button to log in to {0}').format(app_name) }}
				</div>
				<div>{{ _('The link will expire in {0} minutes').format(minutes) }}</div>
			</td>
		</tr>
		<tr>
			<td align="center">
				<a href="{{ link or '#'}}" class="btn btn-primary" style="background-color: #171717; text-decoration: none; margin-top: 30px;">
					{{ _('Log In To {0}').format(app_name) }}
				</a>
			</td>
		</tr>
	</tbody>
</table>
{% endmacro %}

<div class="body-table with-container">
	<div class="body-content">
		{{ table(table(body(), 'email-body'), 'email-container') }}
	</div>
</div>
