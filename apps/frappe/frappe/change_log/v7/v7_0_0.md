#### UI
- Editable Grids
- Image Field in DocType, form and list
- Dashboard, Heatmap, Graphs on Form View
- Document Flow in forms
- List views: remembers user settings

#### Celery to RQ

#### Quick Entry

#### Razorpay Integration

#### Passwords

#### Portals
- Statics (`www` folder): directly served from templates
- New Routing
- Web Forms

#### Limits
- Expiry, space etc
- Usage Info Page

#### Minor
- **Rename:** Bulk Email is now Email Queue
- `frappe.require` is async
- `flot.js` replaced by `c3.js`
- Most popular links on the top
- Standard Replies configurable
- Timeline permisions (not based on user permissions)
- "Track Seen" feature in doctypes
- Moved: "Edit Profile" page is now in frappe (moved from ERPNext)
- Cleanup UI for chat
- New default user icons (based on initials)
- Multiple assign (add a document to multiple users)