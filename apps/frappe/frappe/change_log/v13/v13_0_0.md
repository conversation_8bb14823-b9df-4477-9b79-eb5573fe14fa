# Version 13.0.0 Release Notes

## Highlights

- Re-branded UI 💎 ✨🎊 ([#12277](https://github.com/frappe/frappe/pull/12277))
- New Page Builder in Web Page ([#10035](https://github.com/frappe/frappe/pull/10035))
- Customizable desk ([#9617](https://github.com/frappe/frappe/pull/9617))
- Custom Dashboard for DocTypes ([#9872](https://github.com/frappe/frappe/pull/9872))
- Widgets to make dashboards ([#9693](https://github.com/frappe/frappe/pull/9693))
- Events Streaming ([#8567](https://github.com/frappe/frappe/pull/8567))
- Contextual translation and Translation Tool ([#9636](https://github.com/frappe/frappe/pull/9636))

### Other Features & Enhancements

- Added permission to grant only `Select` access ([#12063](https://github.com/frappe/frappe/pull/12063))
- Add columns and filters for reports via configuration ([#11287](https://github.com/frappe/frappe/pull/11287))
- Configurable Navbar logo and dropdowns ([#11213](https://github.com/frappe/frappe/pull/11213))
- Rule based naming of documents ([#11439](https://github.com/frappe/frappe/pull/11439))
- New routing style, not using hashes, also /desk -> /app ([#11917](https://github.com/frappe/frappe/pull/11917))
- Web Page tracking ([#9959](https://github.com/frappe/frappe/pull/9959))
- Introduced "Yesterday" and "Tomorrow" options for Timespan filter ([12179](https://github.com/frappe/frappe/pull/12179))
- Child table pagination ([#8786](https://github.com/frappe/frappe/pull/8786))
- Introduced Duration Control ([#10248](https://github.com/frappe/frappe/pull/10248))
- Form Tour feature ([#10287](https://github.com/frappe/frappe/pull/10287))
<details>
<summary>More</summary>

- Introduced Map View ([#11202](https://github.com/frappe/frappe/pull/11202))
- Custom JS & CSS support in Web Form ([#9121](https://github.com/frappe/frappe/pull/9121)) ([#9610](https://github.com/frappe/frappe/pull/9610))
- Ability to attach photo from webcam ([#12160](https://github.com/frappe/frappe/pull/12160))
- Added a System Console to help in debugging ([#11306](https://github.com/frappe/frappe/pull/11306))
- Introduced System Settings to automatically delete old Prepared Reports ([#9751](https://github.com/frappe/frappe/pull/9751))
- "Mandatory Depends On" and "Read Only Depends On" option for document fields ([#8820](https://github.com/frappe/frappe/pull/8820))
- Added 2FA for LDAP users ([#10001](https://github.com/frappe/frappe/pull/10001))
- Introduced Help Article Feedback system ([#10260](https://github.com/frappe/frappe/pull/10260))
- Introduced Razorpay client ([#11418](https://github.com/frappe/frappe/pull/11418))
- Rate Limiting ([#10310](https://github.com/frappe/frappe/pull/10310))
- Introduced Log Settings ([#11699](https://github.com/frappe/frappe/pull/11699))
- Enhancements in notifications ([#11398](https://github.com/frappe/frappe/pull/11398)) ([#11409](https://github.com/frappe/frappe/pull/11409))
- Added a field-level permission check for report data ([12163](https://github.com/frappe/frappe/pull/12163))
- Ability to cancel all linked document with a single click ([#8905](https://github.com/frappe/frappe/pull/8905))
- Made checkboxes navigable via tab key ([#11030](https://github.com/frappe/frappe/pull/11030))
- Renamed "Custom Script" to "Client Script" ([#12324](https://github.com/frappe/frappe/pull/12324))

</details>

### Performance

- Faster application load ([#12364](https://github.com/frappe/frappe/pull/12364)) ([#10229](https://github.com/frappe/frappe/pull/10229)) ([#10147](https://github.com/frappe/frappe/pull/10147)) ([#9930](https://github.com/frappe/frappe/pull/9930))
- Theme files will now be compressed to make the website load faster ([#11048](https://github.com/frappe/frappe/pull/11048))
- Confirmation emails will be sent instantly ([#10790](https://github.com/frappe/frappe/pull/10790))
- Faster scheduled job processing ([#9928](https://github.com/frappe/frappe/pull/9928))
- Faster data imports ([#12565](https://github.com/frappe/frappe/pull/12565))
- Faster CLI commands ([#12447](https://github.com/frappe/frappe/pull/12447))
