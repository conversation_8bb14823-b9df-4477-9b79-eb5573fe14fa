{"actions": [], "allow_rename": 1, "autoname": "hash", "creation": "2022-02-21 20:28:05.662187", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["role"], "fields": [{"fieldname": "role", "fieldtype": "Link", "label": "Role", "options": "Role"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2022-02-21 20:28:05.662187", "modified_by": "Administrator", "module": "Workflow", "name": "Workflow Action Permitted Role", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}