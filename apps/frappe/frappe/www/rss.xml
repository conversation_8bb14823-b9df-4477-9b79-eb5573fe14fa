<?xml version="1.0" encoding="UTF-8" ?>
<rss version="2.0">
<channel>
        <title>{{ title }}</title>
        <description>{{ description }}</description>
        <link>{{ link }}</link>
        <lastBuildDate>{{ modified }}</lastBuildDate>
        <pubDate>{{ modified }}</pubDate>
        <ttl>1800</ttl>
		{% for i in items %}<item>
			        <title>{{ i.title }}</title>
			        <description>{{ i.blog_intro }}</description>
			        <link>{{ i.link }}</link>
			        <guid>{{ i.name }}</guid>
			        <pubDate>{{ i.published_on }}</pubDate>
		</item>{% endfor %}
</channel>
</rss>
