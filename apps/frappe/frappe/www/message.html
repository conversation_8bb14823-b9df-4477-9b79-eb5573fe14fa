{% extends "templates/web.html" %}

{% block title %}{{ title or _("Message") }}{% endblock %}

{% block page_content %}
<style>
{% include "templates/styles/card_style.css" %}
{% if fullpage %}
header, footer {
	display: none;
}
html, body {
	background-color: #f5f7fa;
}
{% endif %}
{% if card_width %}
.page-card {
	max-width: {{ card_width }}px;
}
{% endif %}
</style>
<div class='page-card'>
	<h5 class='page-card-head'>
		<span class='indicator {{ indicator_color or "blue" }}'>
			{{ title or _("Message") }}</span>
	</h5>
	<div class="page-card-body">
	{% block message_body %}
		{% if message %}
		<p>{{ message }}</p>
		{% endif %}
		{% if primary_action %}
		<div><a href='{{ primary_action or "/" }}' class='btn btn-primary btn-sm btn-block'>
			{{ primary_label or _("Home") }}</a></div>
		{% endif %}
	{% endblock %}
	</div>
</div>
{% if error_code %}
<p class='text-muted text-center small' style='margin-top: -20px;'>{{ _("Status: {0}").format(error_code) }}</p>
{% endif %}
<script>
	frappe.ready(function() {
		if (window.location.hash || window.location.href.includes('/app')) {
			localStorage.setItem('session_last_route', window.location.pathname);
		}

		$('.btn-primary').focus();
	});
</script>
{% endblock %}
