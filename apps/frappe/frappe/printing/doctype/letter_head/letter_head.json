{"actions": [], "allow_rename": 1, "autoname": "field:letter_head_name", "creation": "2012-11-22 17:45:46", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["letter_head_name", "source", "footer_source", "column_break_3", "disabled", "is_default", "letter_head_image_section", "image", "image_height", "image_width", "align", "header_section", "content", "footer_section", "footer", "footer_image_section", "footer_image", "footer_image_height", "footer_image_width", "footer_align", "scripts_section", "header_script", "footer_script", "instructions"], "fields": [{"fieldname": "letter_head_name", "fieldtype": "Data", "in_list_view": 1, "label": "Letter Head Name", "oldfieldname": "letter_head_name", "oldfieldtype": "Data", "reqd": 1, "unique": 1}, {"depends_on": "letter_head_name", "fieldname": "source", "fieldtype": "Select", "label": "Letter Head Based On", "options": "Image\nHTML"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"default": "0", "depends_on": "letter_head_name", "fieldname": "disabled", "fieldtype": "Check", "in_list_view": 1, "label": "Disabled", "oldfieldname": "disabled", "oldfieldtype": "Check"}, {"default": "0", "depends_on": "letter_head_name", "fieldname": "is_default", "fieldtype": "Check", "in_list_view": 1, "label": "Default Letter Head", "oldfieldname": "is_default", "oldfieldtype": "Check", "search_index": 1}, {"depends_on": "eval:doc.letter_head_name && doc.source === 'Image'", "fieldname": "letter_head_image_section", "fieldtype": "Section Break", "label": "Letter Head Image"}, {"depends_on": "eval:doc.letter_head_name && doc.source === 'Image'", "fieldname": "image", "fieldtype": "Attach Image", "label": "Image"}, {"depends_on": "eval:doc.source==='HTML' && doc.letter_head_name", "fieldname": "header_section", "fieldtype": "Section Break", "label": "Header"}, {"depends_on": "eval:!doc.__islocal && doc.source==='HTML'", "description": "Letter Head in HTML", "fieldname": "content", "fieldtype": "HTML Editor", "label": "Header <PERSON>", "oldfieldname": "content", "oldfieldtype": "Text Editor"}, {"depends_on": "eval:doc.footer_source==='HTML' && doc.letter_head_name", "fieldname": "footer_section", "fieldtype": "Section Break", "label": "Footer"}, {"depends_on": "eval:!doc.__islocal", "description": "Footer will display correctly only in PDF", "fieldname": "footer", "fieldtype": "HTML Editor", "label": "Footer HTML"}, {"default": "Left", "fieldname": "align", "fieldtype": "Select", "label": "Align", "options": "Left\nRight\nCenter"}, {"fieldname": "image_height", "fieldtype": "Float", "label": "Image Height"}, {"fieldname": "image_width", "fieldtype": "Float", "label": "Image Width"}, {"depends_on": "eval:doc.footer_source==='Image' && doc.letter_head_name", "fieldname": "footer_image_section", "fieldtype": "Section Break", "label": "Footer Image"}, {"fieldname": "footer_image", "fieldtype": "Attach Image", "label": "Image"}, {"fieldname": "footer_image_height", "fieldtype": "Float", "label": "Image Height"}, {"fieldname": "footer_image_width", "fieldtype": "Float", "label": "Image Width"}, {"fieldname": "footer_align", "fieldtype": "Select", "label": "Align", "options": "Left\nRight\nCenter"}, {"default": "HTML", "depends_on": "letter_head_name", "fieldname": "footer_source", "fieldtype": "Select", "label": "Footer Based On", "options": "Image\nHTML"}, {"depends_on": "eval:!doc.__islocal && doc.source==='HTML'", "fieldname": "header_script", "fieldtype": "Code", "label": "Head<PERSON>", "options": "Javascript"}, {"depends_on": "eval:!doc.__islocal && doc.footer_source==='HTML'", "fieldname": "footer_script", "fieldtype": "Code", "label": "<PERSON><PERSON>", "options": "Javascript"}, {"collapsible": 1, "collapsible_depends_on": "eval: doc.header_script || doc.footer_script", "fieldname": "scripts_section", "fieldtype": "Section Break", "label": "<PERSON><PERSON><PERSON>"}, {"fieldname": "instructions", "fieldtype": "HTML", "label": "Instructions", "read_only": 1}], "icon": "fa fa-font", "idx": 1, "links": [], "make_attachments_public": 1, "max_attachments": 3, "modified": "2024-04-12 10:30:25.793932", "modified_by": "Administrator", "module": "Printing", "name": "Letter Head", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"read": 1, "role": "Desk User"}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}