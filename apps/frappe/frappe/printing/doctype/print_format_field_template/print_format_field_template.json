{"actions": [], "allow_rename": 1, "autoname": "Prompt", "creation": "2021-10-05 14:23:56.508499", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["document_type", "field", "template_file", "column_break_3", "module", "standard", "section_break_5", "template"], "fields": [{"depends_on": "eval:!doc.multiple", "fieldname": "document_type", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Document Type", "mandatory_depends_on": "eval:!doc.multiple", "options": "DocType", "reqd": 1}, {"fieldname": "field", "fieldtype": "Data", "in_list_view": 1, "label": "De<PERSON><PERSON> Template For Field"}, {"depends_on": "eval:!doc.standard", "fieldname": "template", "fieldtype": "Code", "label": "Template", "mandatory_depends_on": "eval:!doc.standard", "options": "HTML"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "section_break_5", "fieldtype": "Section Break", "hide_border": 1}, {"depends_on": "standard", "fieldname": "module", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON>"}, {"default": "0", "fieldname": "standard", "fieldtype": "Check", "in_list_view": 1, "label": "Standard"}, {"depends_on": "eval:doc.standard", "fieldname": "template_file", "fieldtype": "Data", "label": "Template File", "mandatory_depends_on": "eval:doc.standard"}], "index_web_pages_for_search": 1, "links": [], "modified": "2021-10-19 17:47:59.577949", "modified_by": "Administrator", "module": "Printing", "name": "Print Format Field Template", "naming_rule": "Set by user", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}