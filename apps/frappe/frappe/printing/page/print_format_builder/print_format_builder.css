.print-format-builder-section {
	border: 1px solid var(--dark-border-color);
	border-radius: var(--border-radius);
	margin: 0px;
	margin-bottom: var(--margin-md);
}

.print-format-builder-add-section, .print-format-builder-header {
	border: 1px dashed var(--dark-border-color);
	border-radius: var(--border-radius);
	padding: var(--padding-sm);
	width: 100%;
	display: inline-block;
	background: var(--bg-color);
	cursor: pointer;
}

.print-format-builder-header-edit {
	margin-bottom: var(--margin-sm);
}

.print-format-builder-header {
	margin-bottom: var(--margin-md);
}

.print-format-builder-add-section {
	color: var(--text-light);
	align-items: center;
	padding: var(--padding-lg);
	display: flex;
	justify-content: center;
}

.print-format-builder-add-section .icon {
	margin-right: var(--margin-sm);
}

.print-format-builder-column {
	border-radius: var(--border-radius);
}

.print-format-builder-section .section-column {
	padding: var(--padding-xs) 0 var(--padding-md) var(--padding-md);
}

.print-format-builder-section .section-column:last-child {
	padding-right: var(--padding-md);
}

.print-format-builder-field {
	padding: 8px;
	width: 100%;
	display: inline-block;
	border-radius: var(--border-radius);
	background: var(--bg-light-gray);
	margin-bottom: var(--margin-sm);
	font-size: var(--text-md);
	color: var(--text-color);
}

.print-format-builder-field:last-child {
	margin-bottom: 0;
}

.print-format-builder-field .field-label {
	vertical-align: middle;
}

.print-format-builder-column .print-format-builder-field {
	cursor: move;
}

.print-format-builder-section-head .section-label {
	font-size: var(--text-lg);
	font-weight: var(--weight-medium);
	letter-spacing: 0.015em;
	color: var(--text-color);
	vertical-align: middle;
	margin-left: var(--margin-sm);
}

.print-format-builder-section-head {
	cursor: move;
	padding: var(--padding-md) calc(var(--padding-md) + 8px)
		var(--padding-sm) calc(var(--padding-md) + 8px);
}

.column-selector-row {
	margin-bottom: var(--margin-xs);
	padding: var(--padding-xs) 0;
	cursor: grab;
}

.column-selector-row:hover {
	background-color: var(--highlight-color);
}

.column-selector-row .drag-handle {
	margin-right: var(--margin-sm);
}

.print-format-builder-field .drag-handle {
	margin-right: var(--margin-sm);
}

.print-format-builder-sidebar .sidebar-field {
	width: 100%;
	padding: 4px 8px;
	color: var(--text-on-light-gray);
	/* color: var(--text-light); */
	text-align: left;
	cursor: grab;
}

.print-format-builder-sidebar .sidebar-custom-field {
	background-color: var(--gray-300);
}

.print-format-builder-sidebar {
	top: calc(var(--navbar-height) + 70px);
	position: sticky;
}

.print-format-builder-sidebar-fields {
	padding: var(--padding-xs);
	overflow-y: auto;
	height: 75vh;
}

.print-format-builder-field-placeholder {
	margin-bottom: var(--margin-sm);
}

.print-format-builder-field-placeholder:last-child {
	margin-bottom: 0;
}

.print-format-builder-field-placeholder .drag-handle {
	margin-right: var(--margin-sm);
}

.filter-searchbox {
	padding: 0 var(--padding-xs);
	margin-bottom: var(--margin-sm);
}

.filter-searchbox input {
	background-color: var(--control-bg-on-gray);
}

.print-format-builder-main {
	display: inline-block;
	vertical-align: top;
	border-top: 0px;
	padding: var(--padding-lg);
}

.print-format-help-message {
	font-size: var(--text-md);
	margin-bottom: var(--margin-md);
}
