{"creation": "2021-11-24 17:31:44.978996", "docstatus": 0, "doctype": "Form Tour", "first_document": 0, "idx": 0, "include_name_field": 1, "is_standard": 1, "modified": "2021-11-24 17:58:00.807972", "modified_by": "Administrator", "module": "Printing", "name": "Print Format", "owner": "Administrator", "reference_doctype": "Print Format", "save_on_complete": 1, "steps": [{"description": "Select a Doctype for which you want to create a Print Format", "field": "", "fieldname": "doc_type", "fieldtype": "Link", "has_next_condition": 0, "is_table_field": 0, "label": "DocType", "parent_field": "", "position": "Right", "title": "Doctype"}, {"description": "You can modify the style of the Print Format from this section", "field": "", "fieldname": "section_break_9", "fieldtype": "Section Break", "has_next_condition": 0, "is_table_field": 0, "label": "Style Settings", "parent_field": "", "position": "Top", "title": "Style Settings"}, {"description": "You can add custom css for your Print Format from this section", "field": "", "fieldname": "css", "fieldtype": "Code", "has_next_condition": 0, "is_table_field": 0, "label": "Custom CSS", "parent_field": "", "position": "Top", "title": "Custom CSS"}, {"description": "Check this if you want to add custom Jinja Code or JavaScript to your Print Format", "field": "", "fieldname": "custom_format", "fieldtype": "Check", "has_next_condition": 1, "is_table_field": 0, "label": "Custom Format", "next_step_condition": "eval: doc.custom_format", "parent_field": "", "position": "Left", "title": "Custom Format"}, {"description": "Select the type of Print Format", "field": "", "fieldname": "print_format_type", "fieldtype": "Select", "has_next_condition": 1, "is_table_field": 0, "label": "Print Format Type", "next_step_condition": "eval: doc.custom_format", "parent_field": "", "position": "Right", "title": "Print Format Type"}, {"description": "Enter the code based on the Print Format Type you selected above", "field": "", "fieldname": "html", "fieldtype": "Code", "has_next_condition": 1, "is_table_field": 0, "label": "HTML", "next_step_condition": "eval:doc.html", "parent_field": "", "position": "Right", "title": "Code"}], "title": "Print Format"}