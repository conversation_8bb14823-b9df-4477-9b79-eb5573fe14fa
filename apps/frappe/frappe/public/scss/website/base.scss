html {
	height: 100%;
}

body {
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	@include get_textstyle("lg", "regular");
	color: $body-color;
	display: flex;
	flex-direction: column;
}

kbd {
	background-color: var(--control-bg);
	font-family: var(--font-stack);
	color: var(--text-light);
	line-height: 1.2em;
	letter-spacing: 0.02em;
	height: 20px;
	padding: 4px 8px;
}
img {
	max-width: 100%;
	height: auto;
}

h1,
h2,
h3,
h4 {
	font-weight: 600;
}

h1 {
	font-size: 2rem;
	line-height: 1.25;
	letter-spacing: -0.025em;
	margin-top: 3rem;
	margin-bottom: 0.75rem;

	@include media-breakpoint-up(sm) {
		font-size: 2.5rem;
		margin-top: 3.5rem;
		margin-bottom: 1.25rem;
	}
	@include media-breakpoint-up(xl) {
		font-size: 3.5rem;
		line-height: 1;
		margin-top: 4rem;
	}
}

h2 {
	font-size: 1.4rem;
	margin-top: 2rem;
	margin-bottom: 0.5rem;

	@include media-breakpoint-up(sm) {
		font-size: 2rem;
		margin-top: 4rem;
		margin-bottom: 0.75rem;
	}
	@include media-breakpoint-up(xl) {
		font-size: 2.5rem;
		margin-top: 4rem;
	}
}

h3 {
	font-size: 1.2rem;
	margin-top: 2rem;
	margin-bottom: 0.5rem;

	@include media-breakpoint-up(sm) {
		font-size: 1.4rem;
		margin-top: 2.5rem;
	}
	@include media-breakpoint-up(xl) {
		font-size: 1.9rem;
		margin-top: 3.5rem;
	}
}

h4 {
	font-size: 1.1rem;
	margin-top: 2rem;
	margin-bottom: 0.5rem;

	@include media-breakpoint-up(sm) {
		font-size: 1.3rem;
		margin-top: 2.5rem;
	}
	@include media-breakpoint-up(xl) {
		font-size: 1.5rem;
		margin-top: 3rem;
	}

	a {
		color: $body-color;
		text-decoration: underline !important;
	}
}

p {
	line-height: 1.7;
}

.btn.btn-lg {
	font-size: 1.1rem;
}
