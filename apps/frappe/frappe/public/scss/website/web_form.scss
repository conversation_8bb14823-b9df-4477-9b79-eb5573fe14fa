@import "../common/form";

[data-doctype="Web Form"] {
	.page-content-wrapper {
		.container {
			.page-header {
				width: 100%;

				img {
					margin: -1rem 0rem -10.5rem;
					object-fit: cover;
					width: 100%;
					height: 250px;
					z-index: -1;
				}
			}

			.page_content {
				max-width: 800px;
				margin: auto;

				.tooltip-content {
					display: none;
				}

				h1 {
					font-size: 2.25rem;
					margin-top: 0;
					margin-bottom: 0;
					padding-bottom: 2px;

					@include media-breakpoint-down(sm) {
						font-size: 1.25rem;
					}
				}

				.web-form-header {
					border: 1px solid var(--dark-border-color);
					border-bottom: none;
					border-top-left-radius: var(--border-radius-md);
					border-top-right-radius: var(--border-radius-md);
					background-color: var(--fg-color);
					padding: 2rem 2rem 0;

					@include media-breakpoint-down(sm) {
						padding: 1.5rem 1.5rem 0;
					}

					.breadcrumb-container {
						padding: 0px;
						margin: 0 0 2rem;

						ol.breadcrumb {
							padding: 0px;
						}
					}

					.web-form-head {
						border-bottom: 1px solid var(--dark-border-color);
						padding-bottom: 1.25rem;

						.title {
							display: flex;
							justify-content: space-between;
							flex-wrap: wrap;
							gap: 1rem;

							.web-form-title p {
								margin-bottom: 0;
							}

							.indicator-pill {
								margin-top: 7px;
							}

							.web-form-actions {
								display: flex;
								align-items: center;
								justify-content: flex-end;
								flex: 1;

								.btn {
									@include get_textstyle("base", "regular");
								}
							}
						}

						.web-form-introduction {
							color: var(--text-muted);
							margin-top: 1.25rem;

							p {
								color: var(--text-muted);

								@include media-breakpoint-down(sm) {
									font-size: var(--text-xs);
								}
							}
						}
					}
				}

				.web-form {
					background-color: var(--fg-color);
					padding: 1.25rem 2rem 2rem;
					border: 1px solid var(--dark-border-color);
					border-top: none;
					border-bottom-left-radius: var(--border-radius);
					border-bottom-right-radius: var(--border-radius);

					@include media-breakpoint-down(sm) {
						padding: 1rem 1.5rem 1.5rem;
					}

					.web-form-wrapper {
						.form-control {
							color: var(--text-color);
							background-color: var(--control-bg);

							@include media-breakpoint-down(sm) {
								@include get_textstyle("sm", "regular");
							}
						}

						.form-section {
							.section-head {
								font-weight: bold;
								font-size: var(--text-xl);
								padding: var(--padding-md) 0;
							}
						}

						.form-column {
							padding: 0 var(--padding-sm);

							.form-group {
								@include media-breakpoint-down(sm) {
									margin-bottom: 0.5rem;
								}
							}

							.frappe-control {
								position: relative;

								@include media-breakpoint-down(sm) {
									@include get_textstyle("sm", "regular");
								}

								&[data-fieldtype="Rating"] {
									.like-disabled-input {
										background-color: unset;
										padding-left: 0px;

										.rating {
											cursor: default;
										}
									}
								}

								.selected-color {
									top: 6px;
								}
							}

							&:first-child {
								padding-left: 0;
							}

							&:last-child {
								padding-right: 0;
							}

							@include media-breakpoint-down(xs) {
								padding: 0;
							}
						}

						.web-form-skeleton {
							.box-group {
								display: flex;
								flex-wrap: wrap;

								.box-container {
									width: 100%;
									padding: 0 var(--padding-sm);
									margin-bottom: 15px;

									&:first-child {
										padding-left: 0;
									}

									&:last-child {
										padding-right: 0;
									}

									@include media-breakpoint-down(xs) {
										padding: 0;
									}

									.box {
										background-color: var(--control-bg);
										border-radius: var(--border-radius);
									}

									.box-label {
										height: 20px;
										width: 100px;
										margin-bottom: 0.5rem;
									}

									.box-area {
										height: 34px;
										width: 100%;
									}
								}
							}
						}
					}

					.web-form-footer {
						margin-top: 1rem;

						@include media-breakpoint-down(sm) {
							margin-top: 0.5rem;
						}

						.web-form-actions {
							display: flex;
							justify-content: space-between;
							flex-wrap: wrap;

							.btn {
								@include get_textstyle("base", "regular");

								@include media-breakpoint-down(sm) {
									@include get_textstyle("sm", "regular");
								}
							}

							.btn-link {
								padding-left: 0px;
								color: var(--text-color);

								&:hover {
									color: var(--text-on-light-blue);
								}
							}

							.left-area {
								display: flex;
								flex: 1;

								@include media-breakpoint-down(sm) {
									order: 1;
								}
							}

							.center-area {
								display: flex;
								align-items: center;
								@include get_textstyle("base", "regular");

								.slides-progress {
									display: flex;

									.slide-step {
										@include flex(flex, center, center, null);

										height: 18px;
										width: 18px;
										border-radius: var(--border-radius-full);
										border: 1px solid var(--gray-300);
										margin: 0 var(--margin-xs);
										background-color: var(--card-bg);

										.slide-step-indicator {
											height: 6px;
											width: 6px;
											background-color: var(--gray-300);
											border-radius: var(--border-radius-full);
										}

										.slide-step-complete {
											display: none;

											.icon-xs {
												height: 10px;
												width: 10px;
											}
										}

										&.active {
											border: 1px solid var(--primary);

											.slide-step-indicator {
												display: block;
												background-color: var(--primary);
											}
										}

										&.step-success:not(.active) {
											background-color: var(--primary);
											border: 1px solid var(--primary);

											.slide-step-indicator {
												display: none;
											}

											.slide-step-complete {
												display: flex;

												.icon use {
													stroke-width: 2;
													stroke: var(--white);
												}
											}
										}

										@include media-breakpoint-down(xs) {
											width: 16px;
											height: 16px;
										}
									}
								}

								@include media-breakpoint-down(sm) {
									order: 0;
									width: 100%;
									justify-content: center;
									margin-bottom: 1.5rem;

									&:empty {
										margin: 0;
									}
								}
							}

							.right-area {
								display: flex;
								justify-content: flex-end;
								flex: 1;

								@include media-breakpoint-down(sm) {
									order: 2;
								}
							}
						}
					}
				}

				.attachments {
					margin-top: 2rem;
					padding: 2rem;
					border-radius: var(--border-radius);
					border: 1px solid var(--dark-border-color);

					.attachment {
						display: flex;
						justify-content: space-between;
						gap: 6px;
						color: var(--text-muted);
						@include get_textstyle("base", "regular");

						&:hover {
							text-decoration: none;
							.file-name span {
								text-decoration: underline;
							}
						}
					}
				}

				.success-page {
					background-color: var(--fg-color);
					padding: 5rem 2rem;
					margin-top: 3rem;
					border: 1px solid var(--dark-border-color);
					border-radius: var(--border-radius);
					text-align: center;

					.success-header {
						display: flex;
						justify-content: center;
						align-items: center;
						gap: 0.5rem;
						margin-top: 1rem;

						.success-icon {
							width: 3rem;
							height: 3rem;
							margin: 0;

							@include media-breakpoint-down(sm) {
								width: 2rem;
								height: 2rem;
							}
						}

						.success-title {
							margin-top: 0;
							margin-bottom: 0;
						}
					}

					.success-body .success-message {
						margin: 1rem 0rem 1.5rem;
					}

					.success-footer a {
						margin: 0rem 0.3rem 1rem;
					}
				}

				.web-list-container {
					min-height: 470px;
					border: 1px solid var(--dark-border-color);
					border-radius: var(--border-radius-md);
					padding: 2rem;

					.web-list-header {
						display: flex;
						justify-content: space-between;
						flex-wrap: wrap;
						gap: 1rem;
						border-bottom: 1px solid var(--dark-border-color);
						padding-bottom: 1.25rem;

						.web-list-actions {
							display: flex;
							align-items: center;
							justify-content: flex-end;
							flex: 1;
						}
					}

					.web-list-filters {
						display: flex;
						flex-wrap: wrap;
						margin: 1.25rem 0;
						gap: 10px;

						.form-group.frappe-control {
							min-width: 145px;
							padding: 0px;
							margin: 0px;
							align-self: center;

							.checkbox {
								.input-xs {
									height: var(--checkbox-size);
								}

								.help-box {
									display: none;
								}
							}

							.input-xs {
								height: 28px;
								line-height: 1.2;
							}
						}
					}

					.web-list-table {
						overflow: auto;

						.table {
							border-bottom: 1px solid var(--border-color);
							border-top: 1px solid var(--border-color);

							thead tr {
								th {
									border: 0;
									font-size: 13px;
									font-weight: normal;
									color: var(--text-muted);

									input[type="checkbox"] {
										margin-bottom: -2px;
									}
								}
							}

							tbody tr {
								color: var(--text-color);
								cursor: pointer;

								td {
									font-size: 13px;
									border-top: 1px solid var(--border-color);
									max-width: 160px;

									.ql-editor,
									p {
										width: max-content;
										max-width: 150px;
										margin-bottom: 0;

										&.read-mode {
											display: inline-flex;
											gap: 5px;
										}
									}
								}
							}

							input[type="checkbox"] {
								margin-top: 2px;
							}

							.list-col-checkbox {
								width: 1rem;
							}

							.list-col-serial {
								width: 1.5rem;
							}
						}

						.no-result {
							min-height: 330px;
							border-top: 1px solid var(--border-color);
						}
					}

					.web-list-footer {
						text-align: right;
					}
				}

				.breadcrumb-container.container {
					@include media-breakpoint-up(sm) {
						padding-left: 0;
					}
				}

				@include media-breakpoint-down(lg) {
					padding-left: 1.5rem;
					padding-right: 1.5rem;
				}
			}

			@include media-breakpoint-down(lg) {
				padding-left: 0;
				padding-right: 0;
			}
		}
	}
}
