@import "../espresso/colors";
@import "../espresso/typography";
@import "../espresso/shadows";
@import "../espresso/spacing";
@import "../espresso/borders";

$body-color: $gray-700 !default;
$text-muted: $gray-600 !default;
$border-color: $gray-300 !default;
$headings-color: $gray-900 !default;
$breadcrumb-bg: transparent !default;
$navbar-bg: white !default;
$navbar-height: 48px;

$container-max-widths: (
	sm: 540px,
	md: 840px,
	lg: 1090px,
	xl: 1290px,
);

$font-sizes: (
	"xs": 0.75rem,
	"sm": 0.875rem,
	"base": 1rem,
	"lg": 1.125rem,
	"xl": 1.25rem,
	"2xl": 1.5rem,
	"3xl": 1.875rem,
	"4xl": 2.25rem,
	"5xl": 3rem,
	"6xl": 4rem,
);

@each $size, $value in $font-sizes {
	.font-size-#{$size} {
		font-size: $value;
	}
}

$border-radius: var(--border-radius);
$border-radius-sm: var(--border-radius-sm);
$border-radius-lg: var(--border-radius-lg);

$font-size-xs: 0.75rem !default;
$font-size-sm: 0.875rem !default;
$font-size-base: 1rem !default;
$font-size-lg: 1.125rem !default;
$font-size-xl: 1.25rem !default;
$font-size-2xl: 1.5rem !default;
$font-size-3xl: 1.875rem !default;
$font-size-4xl: 2.5rem !default;
$font-size-5xl: 3rem !default;
$font-size-6xl: 4rem !default;

$btn-padding-y-lg: 1rem !default;
$btn-padding-x-lg: 2.5rem !default;
$btn-font-size-lg: 1.125rem !default;
$btn-line-height-lg: 1 !default;
$btn-border-radius-lg: 0.5rem !default;
$btn-border-radius: 0.375rem !default;
$btn-font-size: $font-size-sm !default;
$btn-padding-x: 1rem !default;
$btn-padding-y: 0.5rem !default;
$btn-font-weight: 500 !default;

$navbar-nav-link-padding-x: 1rem !default;
$navbar-padding-y: 1rem !default;
$card-border-radius: 0.75rem !default;
$card-spacer-y: 0.5rem !default;

$dropdown-font-size: $font-size-sm !default;
$dropdown-border-radius: 0.375rem !default;
$dropdown-item-padding-y: 0.5rem !default;
$dropdown-item-padding-x: 0.5rem !default;

$input-bg: $gray-100;
$input-focus-bg: $gray-200;
$input-focus-box-shadow: none;
$input-border-color: $gray-100;
$input-focus-border-color: $gray-200;
$input-border-radius: 0.375rem;
$custom-control-indicator-bg: white;

$grid-breakpoints: (
	xs: 0,
	sm: 576px,
	md: 768px,
	lg: 992px,
	xl: 1200px,
	2xl: 1440px,
) !default;

$spacers: (
	0: 0,
	1: 0.25rem,
	2: 0.5rem,
	3: 0.75rem,
	4: 1rem,
	5: 1.25rem,
	6: 1.5rem,
	8: 2rem,
	10: 2.5rem,
	12: 3rem,
	14: 3.5rem,
	16: 4rem,
	18: 4.5rem,
	20: 5rem,
	22: 5.5rem,
	24: 6rem,
	28: 7rem,
	32: 8rem,
	36: 9rem,
	40: 10rem,
	44: 11rem,
	48: 12rem,
	52: 13rem,
	56: 14rem,
	64: 16rem,
);

@import "~bootstrap/scss/functions";
@import "~bootstrap/scss/variables";
@import "~bootstrap/scss/mixins";
@import "css_variables";

$code-color: $purple;
