input[type="radio"] {
	position: relative;
	width: var(--checkbox-size) !important;
	height: var(--checkbox-size);
	margin-right: 8px !important;
	font-size: calc(var(--checkbox-size) - 1px);
	background-repeat: no-repeat;
	background-position: center;
	cursor: pointer;

	// Reset browser behavior
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;

	-webkit-print-color-adjust: exact;
	color-adjust: exact;

	&:focus {
		outline: none;
	}

	&:before {
		width: var(--checkbox-size);
		height: var(--checkbox-size);
		position: absolute;
		content: " ";
		border: 1px solid var(--gray-400);
		border-radius: 16px;
	}

	&:checked::before {
		background-color: var(--primary);
		border-radius: 16px;
		box-shadow: inset 0 0 0 2px white;
	}
}
