@import "~quill/dist/quill.snow";
@import "~quill/dist/quill.bubble";

.ql-toolbar.ql-snow,
.ql-container.ql-snow {
	border-color: var(--border-color);
	font-family: inherit;
}

/*rtl:begin:ignore*/
.ql-editor {
	font-family: var(--font-stack);
	color: var(--text-color);
	line-height: 1.6;
	h1,
	h2,
	h3,
	h4,
	h5 {
		margin-top: 0.5em;
		margin-bottom: 0.25em;
	}
	a[href]:not(.btn) {
		text-decoration: underline;
	}
	.ql-direction-rtl {
		direction: rtl;
		+ .table {
			direction: ltr;
		}
	}
}
/*rtl:end:ignore*/

// Quill doesn't use variables for :(
.ql-snow.ql-toolbar button:hover,
.ql-snow .ql-toolbar button:hover,
.ql-snow.ql-toolbar button:focus,
.ql-snow .ql-toolbar button:focus,
.ql-snow.ql-toolbar button.ql-active,
.ql-snow .ql-toolbar button.ql-active,
.ql-snow.ql-toolbar .ql-picker-label:hover,
.ql-snow .ql-toolbar .ql-picker-label:hover,
.ql-snow.ql-toolbar .ql-picker-label.ql-active,
.ql-snow .ql-toolbar .ql-picker-label.ql-active,
.ql-snow.ql-toolbar .ql-picker-item:hover,
.ql-snow .ql-toolbar .ql-picker-item:hover,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected {
	color: var(--invert-neutral);
}
.ql-snow.ql-toolbar button:hover .ql-fill,
.ql-snow .ql-toolbar button:hover .ql-fill,
.ql-snow.ql-toolbar button:focus .ql-fill,
.ql-snow .ql-toolbar button:focus .ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {
	fill: var(--invert-neutral);
}
.ql-snow.ql-toolbar button:hover .ql-stroke,
.ql-snow .ql-toolbar button:hover .ql-stroke,
.ql-snow.ql-toolbar button:focus .ql-stroke,
.ql-snow .ql-toolbar button:focus .ql-stroke,
.ql-snow.ql-toolbar button.ql-active .ql-stroke,
.ql-snow .ql-toolbar button.ql-active .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow.ql-toolbar button:hover .ql-stroke-miter,
.ql-snow .ql-toolbar button:hover .ql-stroke-miter,
.ql-snow.ql-toolbar button:focus .ql-stroke-miter,
.ql-snow .ql-toolbar button:focus .ql-stroke-miter,
.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,
.ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {
	stroke: var(--invert-neutral);
}

.ql-toolbar.ql-snow {
	border-top-left-radius: var(--border-radius);
	border-top-right-radius: var(--border-radius);
	background-color: var(--gray-50);
	border-bottom: 1px solid var(--border-color);
	padding: 0;
	padding-top: 4px;

	.ql-picker-label::before {
		color: var(--text-color);
	}
}
[data-theme="dark"] {
	.ql-toolbar.ql-snow {
		background-color: var(--gray-900);
	}
}

.ql-container.ql-snow {
	background-color: var(--gray-100);
	border-bottom-left-radius: var(--border-radius);
	border-bottom-right-radius: var(--border-radius);
}

.ql-snow {
	.ql-editor {
		height: 300px;
		border-bottom-left-radius: var(--border-radius);
		border-bottom-right-radius: var(--border-radius);
		resize: vertical;
	}
	.ql-stroke {
		stroke: var(--icon-stroke);
		stroke-width: 1.2px;
	}
	.ql-picker-options {
		border-color: var(--border-color) !important;
		background-color: var(--bg-color);
		border-radius: var(--border-radius);
	}

	.ql-tooltip {
		background-color: var(--fg-color);
		border-radius: var(--border-radius-sm);
		border: none;
		color: var(--text-color);
		box-shadow: var(--shadow-base);
	}
}

.print-format {
	.ql-snow .ql-editor {
		height: auto;
		min-height: 0;
		max-height: none;
		overflow: hidden;
		resize: none;
	}
}

.ql-snow .ql-picker {
	@include get_textstyle("sm", "regular");
	color: var(--text-light);
}

// font-size dropdown
.ql-snow .ql-picker.ql-size {
	width: 58px;

	&.ql-expanded {
		.ql-picker-options {
			overflow-y: auto;
			height: 200px;
		}
	}

	.ql-picker-label,
	.ql-picker-item {
		&:before {
			content: attr(data-value) !important;
		}
	}
}

.ql-snow .ql-picker-label {
	outline: none;
}

.ql-formats {
	margin-bottom: 8px;
}

.ql-code-block-container {
	background-color: var(--code-block-bg);
	color: var(--code-block-text);
	padding: var(--padding-xs) var(--padding-sm) !important;
	margin-bottom: var(--margin-xs) !important;
	margin-top: var(--margin-xs) !important;
	border-radius: var(--border-radius-sm);
}

.ql-bubble .ql-editor {
	min-height: 100px;
	max-height: 300px;
	border-radius: var(--border-radius-sm);
	.ql-code-block-container {
		@extend .ql-code-block-container;
	}
}

.ql-mention-list-container {
	width: 270px;
	border: 1px solid var(--border-color);
	border-radius: var(--border-radius-sm);
	background-color: var(--fg-color);
	box-shadow: var(--shadow-md);
	z-index: 1;
}

.ql-mention-list-item {
	font-size: var(--text-xs);
	padding: 10px 12px;
	height: initial;
	line-height: initial;
	cursor: pointer;

	&.selected {
		background-color: var(--control-bg);
	}
}

.mention {
	display: inline-block;
	height: auto;
	width: auto;
	border-radius: var(--border-radius-lg);
	border: 1px solid var(--border-color);
	padding: 2px 5px;
	@include get_textstyle("sm", "regular");
	background-color: var(--user-mention-bg-color);
	a[href] {
		text-decoration: none;
	}
}

// table

.ql-table {
	width: 66px;

	.ql-picker-label::before {
		content: "Table";
	}

	.ql-picker-options {
		[data-value="insert-table"]::before {
			content: "Insert Table";
		}
		[data-value="insert-row-above"]::before {
			content: "Insert Row Above";
		}
		[data-value="insert-row-below"]::before {
			content: "Insert Row Below";
		}
		[data-value="insert-column-right"]::before {
			content: "Insert Column Right";
		}
		[data-value="insert-column-left"]::before {
			content: "Insert Column Left";
		}
		[data-value="delete-row"]::before {
			content: "Delete Row";
		}
		[data-value="delete-column"]::before {
			content: "Delete Column";
		}
		[data-value="delete-table"]::before {
			content: "Delete Table";
		}
	}
}

.ql-editor td {
	border: 1px solid var(--dark-border-color);
}

.ql-editor blockquote {
	border-left: 4px solid var(--gray-300);
	padding-left: var(--padding-sm);
	@include get_textstyle("sm", "regular");
	margin-top: 0px !important;
	margin-bottom: 0px !important;
}

.ql-editor.read-mode {
	height: unset;
	padding: 0;
	.mention {
		--user-mention-bg-color: var(--control-bg);
	}
}

.ql-mention-list {
	border-radius: var(--border-radius-sm);
	list-style: none;
	margin: 0;
	padding: 0;
	max-height: 300px;
	overflow: scroll;
}

.mention > span {
	margin: 0 3px;
}

.mention[data-is-group="true"] {
	.icon {
		margin-top: -2px;
		margin-left: 4px;
	}
}

.comment-input-wrapper .ql-editor.ql-blank::before {
	left: 8px;
	color: var(--text-muted);
	font-style: normal;
}
