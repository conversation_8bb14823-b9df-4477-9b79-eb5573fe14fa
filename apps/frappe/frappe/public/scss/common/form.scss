.form-control {
	border: none;
	height: var(--input-height);
	@include get_textstyle("base", "regular");
	border-radius: var(--border-radius-sm);
	padding: var(--input-padding);
	position: relative;
}

.form-control.bold {
	font-weight: var(--weight-medium);
}

.like-disabled-input {
	.for-description {
		@include get_textstyle("sm", "regular");
	}
	min-height: var(--input-height);
	border-radius: var(--border-radius-sm);
	padding: var(--disabled-input-padding);
	cursor: default;
	color: var(--disabled-text-color);
	background-color: var(--disabled-control-bg);
}

.head-title {
	font-size: var(--text-xl);
	font-weight: 700;
	color: var(--heading-color);
}
