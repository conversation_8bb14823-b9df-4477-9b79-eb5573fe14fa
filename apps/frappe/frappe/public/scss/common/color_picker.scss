.color-picker {
	font-size: var(--text-xs);
	color: var(--text-muted);
	--color-picker-width: 210px;
	width: var(--color-picker-width);
	.swatches {
		margin-top: 10px;
		margin-bottom: 10px;
		display: flex;
		flex-wrap: wrap;
	}

	.swatch {
		height: 20px;
		width: 20px;
		border-radius: 50%;
		margin-right: 10px;
		margin-bottom: 10px;
		cursor: pointer;
	}

	.color-selector,
	.hue-selector {
		width: 12px;
		height: 12px;
		background: transparent;
		position: absolute;
		border-radius: 50%;
		/* box-shadow: 0 0 0 1px gray, 0 0 0 3px white, 0 0 0 4px gray; */
		border: 1px solid rgba(0, 0, 0, 0.2);
		&::before,
		&::after {
			position: absolute;
			background-color: transparent;
			border: 1px solid rgba(0, 0, 0, 0.2);
			content: " ";
			border-radius: 50%;
		}

		&::before {
			width: 100%;
			height: 100%;
			background-color: currentColor;
			border: 2px solid white;
		}

		&::after {
			width: calc(100% - 4px);
			height: calc(100% - 4px);
			border: 1px solid rgba(0, 0, 0, 0.2);
			top: 2px;
			left: 2px;
		}
	}

	.hue-selector {
		width: 14px;
		height: 14px;
	}

	.color-map {
		margin-top: 10px;
		color: transparent;
		position: relative;
		width: auto;
		height: 140px;
		/* background: linear-gradient(0deg, black, transparent), linear-gradient(90deg, white, transparent), red; */
		border-radius: 6px;
		margin-bottom: 10px;
	}

	.hue-map {
		color: transparent;
		width: auto;
		height: 14px;
		position: relative;
		background: linear-gradient(
			90deg,
			hsl(0, 100%, 50%),
			hsl(60, 100%, 50%),
			hsl(120, 100%, 50%),
			hsl(180, 100%, 50%),
			hsl(240, 100%, 50%),
			hsl(300, 100%, 50%),
			hsl(360, 100%, 50%)
		);
		border: 1px solid rgba(0, 0, 0, 0.1);
		border-radius: 10px;
	}
}
.color-picker-popover {
	.picker-arrow {
		left: 15px !important;
	}
}

.frappe-control[data-fieldtype="Color"] {
	input {
		padding-left: 32px;
	}
	.control-input {
		position: relative;
	}
	.selected-color {
		cursor: pointer;
		width: 16px;
		height: 16px;
		border-radius: 5px;
		background-color: red;
		position: absolute;
		top: 6px;
		left: 8px;
		content: " ";
		&.no-value {
			background: url("/assets/frappe/images/color-circle.png");
			background-size: contain;
		}
	}
	.like-disabled-input {
		.color-value {
			padding-left: 26px;
		}
		.selected-color {
			cursor: default;
		}
	}
}

.data-row.row {
	.selected-color {
		top: calc(50% - 11px);
		z-index: 2;
	}
}
