.flex {
	display: flex;
}

.flex-column {
	@extend .flex;
	flex-direction: column;
}

.justify-center {
	justify-content: center;
}

.align-center {
	align-items: center;
}

.align-flex-start {
	align-items: flex-start;
}

.align-flex-end {
	align-items: flex-end;
}

.justify-between {
	justify-content: space-between;
}

.justify-flex-end {
	justify-content: flex-end;
}

.justify-flex-start {
	justify-content: flex-start;
}

.flex-wrap {
	flex-wrap: wrap;
}

.flush-top {
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
}

.level {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.level-left,
.level-right {
	display: flex;
	flex-basis: auto;
	flex-grow: 0;
	flex-shrink: 0;
	align-items: center;

	&.is-flexible {
		flex-grow: initial;
		flex-shrink: initial;
	}
}

.level-left {
	justify-content: flex-start;
}

.level-right {
	justify-content: flex-end;
}

.level-item {
	font-weight: var(--weight-medium);
	color: var(--text-muted);
	align-items: center;
	display: flex;
	flex-basis: auto;
	flex-grow: 0;
	flex-shrink: 0;
	justify-content: center;
}

.fill-width {
	flex: 1;
}
