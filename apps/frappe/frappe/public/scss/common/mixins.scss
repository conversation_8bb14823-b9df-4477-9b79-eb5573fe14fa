@mixin flex($dis: flex, $x: center, $y: center, $dir: row) {
	display: $dis;
	justify-content: $x;
	align-items: $y;
	flex-direction: $dir;
}

@mixin card(
	$radius: var(--border-radius-md),
	$shadow: var(--card-shadow),
	$padding: var(--padding-sm),
	$background-color: var(--card-bg)
) {
	border-radius: $radius;
	box-shadow: $shadow;
	padding: $padding;
	background-color: $background-color;
}

@mixin transition($property: all, $duration: 0.2s, $timing-fn: linear) {
	transition: $property $duration $timing-fn;
	-webkit-transition: $property $duration $timing-fn;
}

@mixin img-background() {
	content: " ";
	display: block;
	position: absolute;
	left: 0;
	height: calc(100%);
	width: 100%;
	background-color: var(--bg-light-gray);
}

@mixin broken-img(
	$content: null,
	$height: 100%,
	$top: 0,
	$left: 0,
	$background-color: var(--bg-color),
	$border-radius: var(--border-radius)
) {
	// Deprecated: Does not work as expected anymore. Also, this never worked in Safari.
}
