.modal-body .theme-grid {
	display: grid;
	grid-template-columns: repeat(3, minmax(0, 1fr));
	grid-gap: 18px;

	.background {
		background-color: var(--bg-color);
		box-shadow: var(--shadow-sm);
		border-radius: var(--border-radius-lg);
		overflow: hidden;
		cursor: pointer;
		height: 120px;
		position: relative;

		&:hover {
			box-shadow: var(--shadow-base);
		}

		.preview-check {
			@include flex(flex, center, center, null);
			position: absolute;
			bottom: 10px;
			right: 10px;
			height: 20px;
			width: 20px;
			color: var(--text-on-light-gray);
			background-color: var(--bg-light-gray);

			margin-right: var(--margin-sm);
			border-radius: var(--border-radius-full);
			z-index: 1;
		}
	}

	.theme-title {
		color: var(--text-color);
	}

	.selected {
		.background {
			box-shadow: var(--focus-default);
		}

		.preview-check {
			background-color: var(--primary-color);
			.icon use {
				stroke: var(--white);
			}
		}
	}

	.navbar {
		background-color: var(--navbar-bg);
		height: 10px;
	}

	.toolbar {
		height: 10px;
		display: flex;
		justify-content: space-between;
	}

	.foreground {
		background-color: var(--card-bg);
		box-shadow: var(--shadow-sm);
		border-radius: var(--border-radius-sm);
		margin-top: var(--margin-sm);
		height: 50px;

		&:first-child {
			height: 30px;
		}
	}

	.primary {
		background-color: var(--primary-color);
		border-radius: var(--border-radius-sm);
		height: 10px;
		width: 20px;
		z-index: 1;
	}

	.text {
		background-color: var(--text-light);
		border-radius: var(--border-radius-sm);
		height: 10px;
		width: 40px;
	}
}

// TODO: Replace with better alternative
[data-theme="dark"] {
	.background {
		border: 1px solid var(--gray-500);
	}
	.selected {
		.preview-check {
			background-color: var(--white) !important;
			.icon use {
				stroke: var(--primary-color) !important;
			}
		}
	}
}
[data-is-auto-theme="true"] {
	.background::after {
		content: "";
		top: 0;
		right: 0;
		height: 100%;
		width: 50%;
		background: var(--gray-900);
		position: absolute;
	}
}
