@import "../espresso/colors";
@import "../espresso/spacing";
@import "../espresso/typography";
@import "../espresso/shadows";
@import "../espresso/borders";

html,
body {
	font-size: 16px;
}

$border-color: var(--border-color);
$code-color: var(--purple-600);

$border-radius: var(--border-radius);
$border-radius-sm: var(--border-radius-sm);
$border-radius-lg: var(--border-radius-lg);

$nav-divider-margin-y: 4px;

$container-max-widths: (
	sm: 540px,
	md: 840px,
	lg: 1090px,
	xl: 1290px,
);

$theme-colors: (
	"primary": $primary,
	"danger": $danger,
);

$navbar-height: 48px;

$body-color: var(--text-muted);
$component-active-color: var(--fg-color);
$component-active-bg: $gray-600;
$modal-content-bg: var(--modal-bg);
$modal-backdrop-bg: var(--gray-800);
$modal-content-box-shadow-sm-up: 0px 5px 10px rgba(0, 0, 0, 0.100661);
$modal-md: 575px;
$modal-sm: 450px;
$link-color: var(--text-color);

// input
$input-bg: var(--control-bg);
$input-placeholder-color: var(--gray-500);
$input-disabled-bg: var(--input-disabled-bg);
$input-color: var(--text-color);
$input-box-shadow: none;
$input-focus-border-color: var(--gray-500);
$input-border-radius: var(--border-radius);
$input-btn-focus-width: 2px;

// dropdown
$dropdown-color: var(--text-color);
$dropdown-bg: var(--fg-color);
$dropdown-border-color: var(--border-color);
$dropdown-link-color: var(--text-color);
$dropdown-link-hover-color: var(--text-color);
$dropdown-link-hover-bg: var(--fg-hover-color);
$dropdown-link-disabled-color: var(--gray-600);
$dropdown-header-color: var(--gray-600);
$dropdown-divider-bg: var(--border-color);
$dropdown-min-width: 200px;

// button
$btn-padding-y-lg: 1rem !default;
$btn-padding-x-lg: 2.5rem !default;
$btn-font-size-lg: 1.125rem !default;
$btn-line-height-lg: 1 !default;
$btn-border-radius-lg: 0.5rem !default;
$btn-border-radius: 0.375rem !default;
$btn-padding-x: 1rem !default;
$btn-padding-y: 0.5rem !default;
$btn-font-weight: 400 !default;
$btn-active-box-shadow: var(--shadow-inset);

// mark
$mark-bg: #fdf9af;
$mark-padding: 0;

// transitions
$btn-transition: none;
$input-transition: none;

// popover
$enable-shadows: true;
$popover-border-radius: 12px;
$popover-bg: var(--popover-bg);
$popover-box-shadow: var(--shadow-2xl);
$popover-body-padding-x: var(--padding-md);
$popover-body-padding-y: var(--padding-md);
$popover-border-color: transparent;
$popover-arrow-outer-color: var(--dark-border-color);
$popover-header-bg: white;

// custom toggle switch
$custom-switch-width: 1.625rem;
$custom-switch-indicator-border-radius: 1rem;
$custom-control-indicator-border-color: var(--fg-color);
$custom-control-label-color: var(--text-color);
$custom-switch-indicator-size: 8px;
$custom-control-indicator-border-width: 2px;

// progress bar
$progress-bar-bg: var(--progress-bar-bg);

$navbar-nav-link-padding-x: 1rem !default;
$navbar-padding-y: 1rem !default;
$card-border-radius: 0.75rem !default;
$card-spacer-y: 0.5rem !default;

$link-hover-color: var(--text-color);

$dropdown-border-radius: 0.375rem !default;
$dropdown-item-padding-y: var(--padding-sm);
$dropdown-item-padding-x: var(--padding-sm);

$spacer: 14px;
$grid-breakpoints: (
	xs: 0,
	sm: 576px,
	md: 768px,
	lg: 992px,
	xl: 1200px,
	2xl: 1440px,
) !default;

@import "typography";
@import "~bootstrap/scss/functions";
@import "~bootstrap/scss/variables";
@import "~bootstrap/scss/mixins";
@import "css_variables";
@import "dark";
