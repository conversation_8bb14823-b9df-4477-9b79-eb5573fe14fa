$check-icon-dark: url("data:image/svg+xml, <svg viewBox='0 0 8 7' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M1 4.00001L2.66667 5.80001L7 1.20001' stroke='black' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/></svg>");
[data-theme="dark"] {
	--neutral: var(--neutral-black);
	--invert-neutral: var(--neutral-white);
	// TODO: Update Pallete for dark mode.
	--gray-700: #383838;
	--gray-800: #232323;

	// Type Colors
	--text-neutral: var(--gray-50);
	--text-dark: var(--gray-900);
	--text-muted: var(--gray-400);
	--text-light: var(--gray-300);
	--text-color: var(--gray-50);
	--heading-color: var(--gray-50);

	// SVG Colors
	--icon-fill: transparent;
	--icon-stroke: var(--gray-300);

	// Error colors
	--error-bg: var(--red-800);
	--error-border: var(--red-400);

	// Layout Colors
	--bg-color: var(--gray-900);
	--fg-color: var(--gray-900);
	--subtle-accent: var(--gray-700);
	--subtle-fg: var(--gray-800);
	--navbar-bg: var(--gray-900);
	--fg-hover-color: var(--gray-800);
	--card-bg: var(--gray-900);
	--disabled-text-color: var(--gray-400);
	--disabled-control-bg: var(--gray-800);
	--control-bg: var(--gray-800);
	--control-bg-on-gray: var(--gray-800);
	--awesomebar-focus-bg: var(--control-bg);
	--awesomplete-hover-bg: var(--gray-800);
	--modal-bg: var(--gray-900);
	--toast-bg: var(--modal-bg);
	--popover-bg: var(--bg-color);

	// Button Colors
	--btn-default-bg: var(--gray-800);
	--btn-default-hover-bg: var(--gray-700);
	--btn-primary: var(--gray-300);

	// Border Colors
	--border-primary: var(--gray-200);

	// Background Text Color Pairs
	--bg-blue: var(--blue-600);
	--bg-light-blue: var(--blue-600);
	--bg-dark-blue: var(--blue-900);
	--bg-green: var(--green-900);
	--bg-yellow: var(--yellow-700);
	--bg-orange: var(--orange-700);
	--bg-red: var(--red-600);
	--bg-gray: var(--gray-600);
	--bg-grey: var(--gray-600);
	--bg-darkgrey: var(--gray-600);
	--bg-dark-gray: var(--gray-500);
	--bg-light-gray: var(--gray-800);
	--bg-purple: var(--purple-700);
	--bg-pink: var(--pink-700);
	--bg-cyan: var(--cyan-800);

	--text-on-blue: var(--blue-50);
	--text-on-light-blue: var(--blue-50);
	--text-on-dark-blue: var(--blue-300);
	--text-on-green: var(--green-100);
	--text-on-yellow: var(--yellow-50);
	--text-on-orange: var(--orange-100);
	--text-on-red: var(--red-50);
	--text-on-gray: var(--gray-50);
	--text-on-grey: var(--gray-50);
	--text-on-darkgrey: var(--gray-200);
	--text-on-dark-gray: var(--gray-200);
	--text-on-light-gray: var(--gray-100);
	--text-on-purple: var(--purple-100);
	--text-on-pink: var(--pink-100);
	--text-on-cyan: var(--cyan-100);

	// alert colors
	--alert-text-danger: var(--red-300);
	--alert-text-warning: var(--yellow-300);
	--alert-text-info: var(--blue-300);
	--alert-text-success: var(--green-300);
	--alert-bg-danger: var(--red-900);
	--alert-bg-warning: var(--yellow-900);
	--alert-bg-info: var(--blue-900);
	--alert-bg-success: var(--green-900);

	--sidebar-select-color: var(--gray-800);

	--scrollbar-thumb-color: var(--gray-600);
	--scrollbar-track-color: var(--gray-700);

	--timeline-badge-color: var(--gray-500);
	--timeline-badge-bg: var(--gray-900);

	--shadow-inset: var(--fg-color);
	--border-color: var(--gray-800);
	--dark-border-color: var(--gray-600);
	--table-border-color: var(--border-color);
	--highlight-color: var(--gray-700);
	--yellow-highlight-color: var(--yellow-700);

	--btn-group-border-color: var(--gray-700);

	--placeholder-color: var(--gray-700);

	--highlight-shadow: 1px 1px 10px var(--blue-900), 0px 0px 4px var(--blue-500);

	--shadow-base: 0px 4px 8px rgba(114, 176, 233, 0.06), 0px 0px 4px rgba(112, 172, 228, 0.12);

	// "diff" colors
	--diff-added: var(--green-800);
	--diff-removed: var(--red-800);

	// sidebar toggle
	.page-title .sidebar-toggle-btn {
		--icon-stroke: var(--gray-300);
	}

	// input
	--input-disabled-bg: none;

	// checkbox
	--checkbox-color: var(--neutral-white);
	--checkbox-gradient: linear-gradient(
		180deg,
		var(--neutral-white) -124.51%,
		var(--neutral-white) 100%
	);
	--checkbox-disabled-gradient: linear-gradient(
		180deg,
		var(--gray-600) -124.51%,
		var(--gray-600) 100%
	);
	--checkbox-focus-shadow: var(--focus-default);

	input[type="checkbox"] {
		&:checked {
			background-image: $check-icon-dark, var(--checkbox-gradient);
		}
		&.disabled-selected,
		&:disabled:checked {
			background-image: $check-icon-dark, var(--checkbox-disabled-gradient);
		}
		&.disabled-deselected {
			border: none;
			background-color: var(--gray-700);
		}
	}

	// switch
	--switch-bg: var(--gray-500);

	// date-picker
	--date-active-text: var(--gray-100);
	--date-active-bg: var(--gray-700);
	--date-range-bg: var(--subtle-fg);

	// grid
	.grid-body .editable-row {
		--control-bg: var(--gray-900);
		--input-disabled-bg: var(--gray-800);
	}
	color-scheme: dark;

	.frappe-card {
		.btn-default {
			background-color: var(--control-bg);

			&:hover {
				background-color: var(--fg-hover-color);
			}
		}
	}

	.modal,
	.form-in-grid {
		--control-bg: var(--gray-800);
		--border-color: var(--gray-800);
	}

	.print-format {
		--text-color: var(--gray-900);
		--border-color: var(--gray-100);
	}

	.ql-editor {
		color: var(--text-on-gray);
		&.read-mode {
			span:not(.mention),
			p,
			u,
			strong {
				background-color: inherit !important;
				color: inherit !important;
			}
		}
	}
	.comment-input-wrapper .ql-editor.ql-blank::before {
		color: var(--text-color);
	}
	// --appreciation-color: var(--dark-green-100);
	// --appreciation-bg: var(--dark-green-600);
	// --criticism-color: var(--red-100);
	// --criticism-bg: var(--red-600);

	// Frappe Charts Colors
	.chart-container {
		--charts-label-color: var(--gray-300);
		--charts-axis-line-color: var(--subtle-fg);

		--charts-stroke-width: 5px;
		--charts-dataset-circle-stroke: #ffffff;
		--charts-dataset-circle-stroke-width: var(--charts-stroke-width);

		--charts-tooltip-title: var(--charts-label-color);
		--charts-tooltip-label: var(--charts-label-color);
		--charts-tooltip-value: white;
		--charts-tooltip-bg: var(--gray-900);

		--charts-legend-label: var(--charts-label-color);
	}

	// find better fix
	.heatmap-chart {
		g > rect[fill="#ebedf0"] {
			fill: var(--gray-700);
		}
	}

	.rating {
		// rating
		--star-fill: var(--gray-500);
		.star-hover {
			--star-fill: var(--gray-400);
		}
	}

	// skeleton
	--skeleton-bg: var(--gray-800);

	--right-arrow-svg: url("data: image/svg+xml;utf8, <svg width='6' height='8' viewBox='0 0 6 8' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M1.25 7.5L4.75 4L1.25 0.5' stroke='white' stroke-linecap='round' stroke-linejoin='round'/></svg>");

	--left-arrow-svg: url("data: image/svg+xml;utf8, <svg width='6' height='8' viewBox='0 0 6 8' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M7.5 9.5L4 6l3.5-3.5' stroke='white' stroke-linecap='round' stroke-linejoin='round'></path></svg>");

	::selection {
		color: var(--text-color);
		background: var(--gray-500);
	}

	$indicator-colors: green, cyan, blue, orange, yellow, gray, grey, red, pink, darkgrey, purple,
		light-blue;
	@each $color in $indicator-colors {
		.indicator {
			--indicator-dot-#{"" + $color}: var(--bg-#{$color});
		}
	}
}
