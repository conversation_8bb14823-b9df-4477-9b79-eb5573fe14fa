@import "variables";
@import "../common/mixins.scss";
@import "../common/global.scss";
@import "../common/icons.scss";
@import "../common/alert.scss";

@import "~bootstrap/scss/bootstrap";
@import "global";
@import "../common/buttons";
@import "../common/flex";
@import "mobile";
@import "module.scss";
@import "form";
@import "print_preview";
@import "scrollbar";
@import "navbar";
@import "../common/modal";
@import "slides";
@import "toast";
@import "breadcrumb";
@import "../common/indicator";
@import "tags";
@import "page";
@import "timeline";
@import "avatar";
@import "notification";
@import "global_search";
@import "desktop";
@import "../common/awesomeplete";
@import "sidebar";
@import "filters";
@import "list";
@import "report";
@import "file_view";
@import "frappe_datatable";
@import "frappe_gantt";
@import "image_view";
@import "kanban";
@import "calendar";
@import "dashboard_view";
@import "tree";
@import "../common/controls";
@import "data_import";
@import "driver";
@import "role_editor";
@import "user_profile";
@import "theme_switcher";
@import "link_preview";
@import "../common/quill";
@import "plyr";
@import "version";
