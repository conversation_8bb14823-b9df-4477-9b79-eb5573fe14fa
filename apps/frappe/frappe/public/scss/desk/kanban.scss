:root {
	--kanban-column-bg: var(--gray-100);
	--kanban-card-bg: var(--card-bg);
	--kanban-card-hover-bg: var(--card-bg);
	--kanban-new-card-bg: var(--gray-200);
	--kanban-new-card-hover-bg: white;
}

[data-theme="dark"] {
	--kanban-column-bg: var(--gray-700);
	--kanban-card-bg: var(--gray-800);
	--kanban-new-card-bg: var(--gray-700);
	--kanban-new-card-hover-bg: var(--gray-800);
}

.kanban {
	display: flex;
	gap: 0.5em;
	overflow-y: hidden;

	-ms-overflow-style: none; /* IE and Edge */
	scrollbar-width: none; /* Firefox */

	&::-webkit-scrollbar {
		display: none;
	}

	.kanban-column {
		@include transition();

		flex: 1 0 260px;
		border-radius: var(--border-radius);
		padding: var(--padding-md);
		min-height: calc(100vh - 150px);

		.add-card {
			@include flex(flex, center, center, null);
			@include transition();

			color: var(--text-light);
			background-color: var(--bg-color);
			height: 27px;
			font-size: var(--text-md);
			margin-bottom: var(--margin-sm);
			border-radius: var(--border-radius-md);

			&:hover {
				color: var(--text-color);
				cursor: pointer;
			}

			.octicon-plus {
				top: -1px;
				font-size: 1em;
				margin-right: var(--margin-xs);
				position: relative;
			}
		}

		.kanban-column:not(.add-new-column) {
			&:hover {
				.add-card {
					background-color: var(--kanban-new-card-hover-bg);
					box-shadow: var(--shadow-xs);
				}

				background-color: var(--kanban-new-card-bg);
			}
		}

		.kanban-column-header {
			@include flex(flex, space-between, null, null);
			margin-top: 0;
			margin-bottom: 15px;
			position: relative;
			font-weight: 500;
			font-size: 12px;
			// align-items: center;
			.indicator-pill {
				padding: 2px 7px;
				width: 20px;
				height: 20px;

				&:before {
					margin-right: 0px;
				}
			}

			.column-options {
				.button-group {
					display: flex;
					padding: 8px;

					div.indicator-pill {
						margin: 0 5px;
					}

					.btn.indicator {
						flex: 1;
					}
				}

				.indicator::before {
					margin: 0;
				}
			}
		}

		.kanban-column-title {
			@include flex(flex, null, center, null);

			@include get_textstyle("lg", "regular");
			color: var(--text-color);
			padding-right: var(--padding-xs);
			max-width: 100%;

			// margin-left: 10px;
			.kanban-title {
				@include get_textstyle("lg", "semibold");
				margin-left: var(--margin-sm);
				cursor: grab;
			}
		}

		.sortable-ghost > .kanban-card:not(.add-card) {
			background: var(--fg-hover-color) !important;
			color: transparent;

			* {
				background: transparent !important;
				color: transparent !important;
			}
		}

		.kanban-cards {
			height: 100%;
			max-height: calc(100vh - 250px);
			margin: -5px;
			padding: 5px;
			overflow-y: scroll;
			-ms-overflow-style: none; /* IE and Edge */
			scrollbar-width: none; /* Firefox */

			&::-webkit-scrollbar {
				display: none;
			}

			.kanban-card-wrapper {
				position: relative;
				display: block;

				&:last-child .kanban-card {
					margin-bottom: var(--margin-xl);
				}
				.kanban-card {
					@include flex(flex, space-between, null, column);
					margin-top: var(--margin-sm);
					min-height: 100px;
					@include card($padding: 0, $background-color: var(--kanban-card-bg));
					box-shadow: none;
					border: 1px solid var(--border-color);

					.kanban-image {
						height: 125px;

						img {
							border-radius: var(--border-radius) var(--border-radius) 0 0;
							object-position: top;
							object-fit: cover;
							margin: 0 auto;
							height: 100%;
							width: 100%;
							min-width: 100%;
							color: transparent;
							position: relative;
						}
					}

					.kanban-card-body {
						cursor: grab;
						padding: var(--padding-md);

						.kanban-title-area {
							margin-bottom: 12px;
							max-width: 90%;
							font-size: var(--text-md);
							font-weight: 500;

							.kanban-card-doc {
								.text-muted div {
									display: inline;
								}
							}

							.kanban-card-creation {
								font-size: var(--text-md);
								color: var(--text-muted);
								margin-top: var(--margin-xs);
							}
						}

						.kanban-card-meta {
							.list-comment-count {
								width: 30px;
							}

							.like-action:not(.liked) {
								.icon use {
									stroke: var(--text-muted);
								}
							}

							.kanban-tags {
								@include get_textstyle("sm", "regular");
								margin-bottom: 8px;

								.tag-pill {
									border-radius: 100px;
									height: 22px;
									width: auto;
									padding: 2px 8px;
									margin-bottom: var(--margin-xs);
									margin-right: var(--margin-xs);
								}
							}

							.kanban-assignments {
								display: flex;
								float: right;

								.avatar {
									cursor: default;
									width: 22px;
									height: 22px;
								}

								.avatar-action {
									width: 22px;
									height: 22px;

									.icon {
										width: 12px;
										height: 12px;
									}
								}
							}
						}
					}
				}
			}
		}
	}

	.kanban-card:hover,
	.new-card-area,
	.edit-card-area {
		box-shadow: var(--shadow-base);
	}

	.kanban-card-wrapper:hover {
		text-decoration: none;

		.kanban-card-edit {
			opacity: 1;
		}
	}

	.kanban-card-edit {
		position: absolute;
		right: 10px;
		opacity: 0;
		transition: 0.2s ease;
	}

	.new-card-area,
	.edit-card-area {
		margin-bottom: 10px;

		textarea {
			color: var(--text-color);
			font-size: 12px;
			resize: none;
			border: none;
			background: none;
			overflow: hidden;
			word-wrap: break-word;
			width: 100%;

			&:focus {
				outline: none;
			}
		}
	}

	.compose-column-form {
		.new-column-title {
			background: transparent;
			border: none;
			outline: none;
		}
	}

	.kanban-column.add-new-column {
		color: var(--text-muted);
		background-color: transparent;
		order: 1;

		&:hover {
			background-color: none;
		}

		.kanban-column-title.compose-column {
			@include flex(flex, center, center, null);
			min-height: 65px;
			border-radius: var(--border-radius);
			border: 1px dashed var(--gray-400);
			@include get_textstyle("base", "regular");

			&:hover {
				background-color: var(--kanban-column-bg);
			}
		}
	}

	.kanban-empty-state {
		width: 100%;
		line-height: 400px;
	}
}

body[data-route*="Kanban"] {
	.modal .add-assignment:hover {
		// border-color: @text-color;
		i {
			color: var(--text-color) !important;
		}
	}
}

.edit-card-title {
	.h4 {
		margin-top: 5px;
		margin-bottom: 5px;
	}
	span:hover {
		// background-color: @light-yellow;
		cursor: pointer;
	}
	input {
		border: none;
		outline: none;
		width: 100%;
	}
}
