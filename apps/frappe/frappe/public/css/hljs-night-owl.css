/*

Night Owl for highlight.js (c) <PERSON> <<EMAIL>>

An adaptation of <PERSON>'s Night Owl VS Code Theme
https://github.com/sdras/night-owl-vscode-theme

Copyright (c) 2018 <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

*/

.hljs {
	display: block;
	overflow-x: auto;
	padding: 1rem 1.25rem;
	background: #011627;
	color: #d6deeb;
	border-radius: 0.5rem;
  }

  /* General Purpose */
  .hljs-keyword {
	color: #c792ea;
	font-style: italic;
  }
  .hljs-built_in {
	color: #addb67;
	font-style: italic;
  }
  .hljs-type {
	color: #82aaff;
  }
  .hljs-literal {
	color: #ff5874;
  }
  .hljs-number {
	color: #F78C6C;
  }
  .hljs-regexp {
	color: #5ca7e4;
  }
  .hljs-string {
	color: #ecc48d;
  }
  .hljs-subst {
	color: #d3423e;
  }
  .hljs-symbol {
	color: #82aaff;
  }
  .hljs-class {
	color: #ffcb8b;
  }
  .hljs-function {
	color: #82AAFF;
  }
  .hljs-title {
	color: #DCDCAA;
	font-style: italic;
  }
  .hljs-params {
	color: #7fdbca;
  }

  /* Meta */
  .hljs-comment {
	color: #637777;
	font-style: italic;
  }
  .hljs-doctag {
	color: #7fdbca;
  }
  .hljs-meta {
	color: #82aaff;
  }
  .hljs-meta-keyword {
	color: #82aaff;
  }
  .hljs-meta-string {
	color: #ecc48d;
  }

  /* Tags, attributes, config */
  .hljs-section {
	color: #82b1ff;
  }
  .hljs-tag,
  .hljs-name,
  .hljs-builtin-name {
	color: #7fdbca;
  }
  .hljs-attr {
	color: #7fdbca;
  }
  .hljs-attribute {
	color: #80cbc4;
  }
  .hljs-variable {
	color: #addb67;
  }

  /* Markup */
  .hljs-bullet {
	color: #d9f5dd;
  }
  .hljs-code {
	color: #80CBC4;
  }
  .hljs-emphasis {
	color: #c792ea;
	font-style: italic;
  }
  .hljs-strong {
	color: #addb67;
	font-weight: bold;
  }
  .hljs-formula {
	color: #c792ea;
  }
  .hljs-link {
	color: #ff869a;
  }
  .hljs-quote {
	color: #697098;
	font-style: italic;
  }

  /* CSS */
  .hljs-selector-tag {
	color: #ff6363;
  }

  .hljs-selector-id {
	color: #fad430;
  }

  .hljs-selector-class {
	color: #addb67;
	font-style: italic;
  }

  .hljs-selector-attr,
  .hljs-selector-pseudo {
	color: #c792ea;
	font-style: italic;
  }

  /* Templates */
  .hljs-template-tag {
	color: #c792ea;
  }
  .hljs-template-variable {
	color: #addb67;
  }

  /* diff */
  .hljs-addition {
	color: #addb67ff;
	font-style: italic;
  }

  .hljs-deletion {
	color: #EF535090;
	font-style: italic;
  }
