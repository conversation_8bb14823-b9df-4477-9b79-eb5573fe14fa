<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <defs>
    <symbol id="es-line-add-people" fill="none" viewBox="0 0 16 16">
      <g class="es-line-add-people" clip-path="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 1.853a6.147 6.147 0 0 0-4.587 10.239 5.147 5.147 0 0 1 1.815-1.883 5.187 5.187 0 0 1 5.398 0 5.145 5.145 0 0 1 1.87 1.984A6.126 6.126 0 0 0 14.148 8a.5.5 0 1 1 1 0A7.147 7.147 0 1 1 8 .853a.5.5 0 1 1 0 1Zm3.759 11.011a.498.498 0 0 1-.12-.152 4.145 4.145 0 0 0-1.534-1.65 4.187 4.187 0 0 0-4.356 0 4.15 4.15 0 0 0-1.534 1.65.502.502 0 0 1-.056.087A6.121 6.121 0 0 0 8 14.147a6.12 6.12 0 0 0 3.759-1.283ZM7.927 4.57c-.98 0-1.769.788-1.769 1.753 0 .965.789 1.753 1.769 1.753a1.76 1.76 0 0 0 1.768-1.753A1.76 1.76 0 0 0 7.927 4.57ZM5.158 6.323A2.761 2.761 0 0 1 7.927 3.57a2.761 2.761 0 0 1 2.768 2.753 2.761 2.761 0 0 1-2.768 2.753 2.761 2.761 0 0 1-2.769-2.753Zm7.644-5.31a.5.5 0 0 1 .5.5v1.19h1.201a.5.5 0 0 1 0 1h-1.201v1.19a.5.5 0 1 1-1 0v-1.19H11.1a.5.5 0 1 1 0-1h1.2v-1.19a.5.5 0 0 1 .5-.5Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <path fill="#fff" d="M0 0h16v16H0z"/>
        </clipPath>
      </defs>
    </symbol>
    <symbol id="es-line-resizer" fill="none" viewBox="0 0 16 16">
      <g class="es-line-resizer">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8.147 13.406a.501.501 0 0 0 .706.708l5.25-5.26a.501.501 0 0 0-.707-.708l-5.25 5.26Zm6.703-1.834a.501.501 0 0 0-.71-.704l-3.235 3.276a.501.501 0 0 0 .71.704l3.235-3.276Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-filter" fill="none" viewBox="0 0 16 16">
      <g class="es-line-filter">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M1.5 4.667a.5.5 0 0 1 .5-.5h12a.5.5 0 0 1 0 1H2a.5.5 0 0 1-.5-.5Zm2 3.33a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 0 1H4a.5.5 0 0 1-.5-.5Zm2.4 3.332a.5.5 0 0 1 .5-.5h3.2a.5.5 0 1 1 0 1H6.4a.5.5 0 0 1-.5-.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-search" fill="none" viewBox="0 0 16 16">
      <g class="es-line-search">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M12.502 7.501a5 5 0 1 1-10.002 0 5 5 0 0 1 10.002 0Zm-1.184 4.63a6 6 0 1 1 .716-.698l2.32 2.32a.5.5 0 1 1-.707.708l-2.329-2.33Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-close" fill="none" viewBox="0 0 16 16">
      <g class="es-line-Close">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M12.857 3.854a.5.5 0 1 0-.707-.708L8.002 7.294 3.854 3.146a.5.5 0 1 0-.707.708L7.295 8l-4.149 4.15a.5.5 0 1 0 .708.707l4.148-4.149 4.148 4.149a.5.5 0 1 0 .708-.707L8.709 8l4.148-4.147Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-text-cursor" fill="none" viewBox="0 0 16 16">
      <g class="es-line-text-cursor">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M5.6 1.5a.5.5 0 1 0 0 1 1.9 1.9 0 0 1 1.9 1.9v7.221A1.9 1.9 0 0 1 5.6 13.5a.5.5 0 0 0 0 1A2.9 2.9 0 0 0 8 13.228a2.9 2.9 0 0 0 2.4 1.272.5.5 0 0 0 0-1 1.9 1.9 0 0 1-1.9-1.9V4.379A1.9 1.9 0 0 1 10.4 2.5a.5.5 0 0 0 0-1A2.9 2.9 0 0 0 8 2.772 2.9 2.9 0 0 0 5.6 1.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-success" fill="none" viewBox="0 0 16 16">
      <g class="es-line-success">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M14 8A6 6 0 1 1 2 8a6 6 0 0 1 12 0Zm1 0A7 7 0 1 1 1 8a7 7 0 0 1 14 0Zm-3.71-2.015a.5.5 0 1 0-.77-.636L7.07 9.532 5.728 7.8a.5.5 0 0 0-.79.612l1.724 2.228a.5.5 0 0 0 .781.013l3.848-4.667Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-alert-triangle" fill="none" viewBox="0 0 16 16">
      <g class="es-line-alert-triangle">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M13.969 13.291 7.999 3.1 2.03 13.291h11.94ZM8.646 2.225a.75.75 0 0 0-1.294 0L.946 13.162a.75.75 0 0 0 .647 1.13h12.812c.58 0 .94-.63.647-1.13L8.646 2.225ZM8 12.417a.875.875 0 1 0 0-1.75.875.875 0 0 0 0 1.75Zm.5-6.155a.5.5 0 0 0-1 0v3.25a.5.5 0 1 0 1 0v-3.25Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-alert-circle" fill="none" viewBox="0 0 16 16">
      <g class="es-line-alert-circle">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12Zm0 1A7 7 0 1 0 8 1a7 7 0 0 0 0 14Zm0-9.25A.875.875 0 1 0 8 4a.875.875 0 0 0 0 1.75Zm.5 1.686a.5.5 0 1 0-1 0v3.707a.5.5 0 0 0 1 0V7.436Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-close-circle" fill="none" viewBox="0 0 16 16">
      <g class="es-line-close-circle" filter="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12Zm0 1A7 7 0 1 0 8 1a7 7 0 0 0 0 14Zm2.854-9.854a.5.5 0 0 1 0 .708L8.707 8l2.147 2.146a.5.5 0 0 1-.708.708L8 8.707l-2.146 2.147a.5.5 0 0 1-.708-.708L7.293 8 5.146 5.854a.5.5 0 1 1 .708-.708L8 7.293l2.146-2.147a.5.5 0 0 1 .708 0Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <filter id="a" width="24" height="24" x="-4" y="-4" class="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_5_8100"/>
          <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_5_8100" result="shape"/>
        </filter>
      </defs>
    </symbol>
    <symbol id="es-line-delete" fill="none" viewBox="0 0 16 16">
      <g class="es-line-delete" filter="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.676 2.312H5.77v-.23a1.5 1.5 0 0 1 1.5-1.5h1.458a1.5 1.5 0 0 1 1.5 1.5v.23h2.595v.008h1.23a.5.5 0 1 1 0 1h-1.23v9.598a2.5 2.5 0 0 1-2.5 2.5H5.676a2.5 2.5 0 0 1-2.5-2.5V3.32H1.947a.5.5 0 1 1 0-1h1.229v-.008h.5Zm.5 1.008v9.598a1.5 1.5 0 0 0 1.5 1.5h4.647a1.5 1.5 0 0 0 1.5-1.5V3.32H4.176Zm5.052-1.008H6.77v-.23a.5.5 0 0 1 .5-.5h1.458a.5.5 0 0 1 .5.5v.23ZM6.486 5.77a.5.5 0 0 1 .5.5v5.188a.5.5 0 1 1-1 0V6.27a.5.5 0 0 1 .5-.5Zm3.528.5a.5.5 0 1 0-1 0v5.188a.5.5 0 0 0 1 0V6.27Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <filter id="a" width="24" height="24" x="-4" y="-4" class="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_5_8105"/>
          <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_5_8105" result="shape"/>
        </filter>
      </defs>
    </symbol>
    <symbol id="es-line-delete-alt" fill="none" viewBox="0 0 16 16">
      <g class="es-line-delete-alt" filter="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.676 2.312H5.77v-.23a1.5 1.5 0 0 1 1.5-1.5h1.458a1.5 1.5 0 0 1 1.5 1.5v.23h2.595v.008h1.23a.5.5 0 1 1 0 1h-1.23v9.598a2.5 2.5 0 0 1-2.5 2.5H5.676a2.5 2.5 0 0 1-2.5-2.5V3.32H1.947a.5.5 0 1 1 0-1h1.229v-.008h.5Zm.5 1.008v9.598a1.5 1.5 0 0 0 1.5 1.5h4.647a1.5 1.5 0 0 0 1.5-1.5V3.32H4.176Zm5.052-1.008H6.77v-.23a.5.5 0 0 1 .5-.5h1.458a.5.5 0 0 1 .5.5v.23Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <filter id="a" width="24" height="24" x="-4" y="-4" class="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_5_8112"/>
          <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_5_8112" result="shape"/>
        </filter>
      </defs>
    </symbol>
    <symbol id="es-line-email" fill="none" viewBox="0 0 16 16">
      <g class="es-line-email">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.5 2A2.5 2.5 0 0 0 1 4.5v7A2.5 2.5 0 0 0 3.5 14h9a2.5 2.5 0 0 0 2.5-2.5v-7A2.5 2.5 0 0 0 12.5 2h-9ZM14 5.169V4.5A1.5 1.5 0 0 0 12.5 3h-9A1.5 1.5 0 0 0 2 4.5v.669l5.805 2.456a.5.5 0 0 0 .39 0L14 5.169ZM2 6.254V11.5A1.5 1.5 0 0 0 3.5 13h9a1.5 1.5 0 0 0 1.5-1.5V6.254L8.584 8.546a1.5 1.5 0 0 1-1.168 0L2 6.254Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-sparkle" fill="none" viewBox="0 0 16 16">
      <g class="es-line-sparkle" clip-path="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7.505.788a.7.7 0 0 1 .99 0l6.717 6.717a.7.7 0 0 1 0 .99l-3.358 3.359-3.359 3.358a.7.7 0 0 1-.99 0l-3.358-3.358L.788 8.495a.7.7 0 0 1 0-.99l3.359-3.359L7.505.788Zm.495.92L4.854 4.853 1.707 8l3.147 3.146L8 14.293l3.147-3.147L14.293 8 8 1.707Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <path fill="#fff" d="M0 0h16v16H0z"/>
        </clipPath>
      </defs>
    </symbol>
    <symbol id="es-line-call" fill="none" viewBox="0 0 16 16">
      <g class="es-line-call">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.953 2.54a.086.086 0 0 0-.134-.014L2.785 3.603a.962.962 0 0 0-.197 1.08c.258.543.632 1.275 1.108 2.066l1.738-1.723a.086.086 0 0 0 .012-.106L3.953 2.54Zm.293 5.072 1.892-1.876c.36-.357.425-.917.155-1.347L4.8 2.009a1.086 1.086 0 0 0-1.703-.175L2.064 2.91c-.558.58-.736 1.448-.379 2.202.6 1.261 1.84 3.591 3.582 5.362 1.795 1.826 4.305 3.227 5.583 3.877a1.974 1.974 0 0 0 2.102-.213l1.125-.866a1.085 1.085 0 0 0-.033-1.745l-2.363-1.68a1.086 1.086 0 0 0-1.326.053l-2.093 1.755c-.796-.552-1.598-1.187-2.282-1.883a15.145 15.145 0 0 1-1.734-2.16Zm4.887 4.619c.838.527 1.615.947 2.17 1.229a.975.975 0 0 0 1.039-.114l1.125-.866a.086.086 0 0 0-.003-.137l-2.362-1.68a.086.086 0 0 0-.104.005L9.133 12.23Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-filetype" fill="none" viewBox="0 0 16 16">
      <g class="es-line-filetype">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M11.997 14h-8a1.5 1.5 0 0 1-1.5-1.5v-9a1.5 1.5 0 0 1 1.5-1.5H9.5v2A2.5 2.5 0 0 0 12 6.5h1.497v6a1.5 1.5 0 0 1-1.5 1.5Zm2.453-8.5a2.5 2.5 0 0 0-.62-1.22L11.524 1.8A2.5 2.5 0 0 0 9.693 1H3.997a2.5 2.5 0 0 0-2.5 2.5v9a2.5 2.5 0 0 0 2.5 2.5h8a2.5 2.5 0 0 0 2.5-2.5v-7h-.047Zm-1.032 0H12A1.5 1.5 0 0 1 10.5 4V2.236a1.5 1.5 0 0 1 .292.243l2.304 2.482a1.5 1.5 0 0 1 .322.539Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-zap" fill="none" viewBox="0 0 16 16">
      <g class="es-line-zap">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7.512 2.152c.533-.574 1.479-.05 1.274.707l-.723 2.67 6.257.883a.5.5 0 0 1 .296.836l-6.128 6.6c-.533.574-1.479.05-1.274-.707l.724-2.67-6.258-.883a.5.5 0 0 1-.296-.836l6.128-6.6Zm.044 1.423L2.768 8.73l5.87.828a.5.5 0 0 1 .413.626l-.607 2.24 4.788-5.156-5.87-.828a.5.5 0 0 1-.413-.626l.607-2.24Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-check" fill="none" viewBox="0 0 16 16">
      <g class="es-line-check">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M13.788 3.331a.5.5 0 0 1 .052.705l-7.411 8.581a.5.5 0 0 1-.75.008l-3.51-3.9a.5.5 0 0 1 .743-.67l3.13 3.48 7.04-8.152a.5.5 0 0 1 .706-.052Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-dash" fill="none" viewBox="0 0 16 16">
      <g class="es-line-dash">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.5 8a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 0 1H4a.5.5 0 0 1-.5-.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-select" fill="none" viewBox="0 0 16 16">
      <g class="es-line-select">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M5.854 10.646a.5.5 0 0 0-.708.708l2.5 2.5a.5.5 0 0 0 .708 0l2.5-2.5a.5.5 0 0 0-.708-.708L8 12.793l-2.146-2.147Zm0-5.292a.5.5 0 1 1-.708-.708l2.5-2.5a.5.5 0 0 1 .708 0l2.5 2.5a.5.5 0 0 1-.708.708L8 3.207 5.854 5.354Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-arrow-up-right" fill="none" viewBox="0 0 16 16">
      <g class="es-line-arrow-up-right">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M12 3.5H4.929a.5.5 0 0 0 0 1h5.864l-7.147 7.146a.5.5 0 0 0 .708.708L11.5 5.207v5.864a.5.5 0 1 0 1 0V4.015A.499.499 0 0 0 12 3.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-slash" fill="none" viewBox="0 0 16 16">
      <g class="es-line-slash">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M6.923 14.938 10.673 1H9.15L5.4 14.938h1.523Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-dot" fill="none" viewBox="0 0 16 16">
      <g class="es-line-dot">
        <path fill="var(--icon-stroke)" d="M10 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-line-dot-horizontal" fill="none" viewBox="0 0 16 16">
      <g class="es-line-dot-horizontal">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M4.5 8a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0ZM8 9.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm5 0a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-overflow" fill="none" viewBox="0 0 16 16">
      <g class="es-line-overflow">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M4.75 12.25a1.25 1.25 0 1 1-2.5 0 1.25 1.25 0 0 1 2.5 0ZM8 13.5A1.25 1.25 0 1 0 8 11a1.25 1.25 0 0 0 0 2.5Zm4.5 0a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-inbox" fill="none" viewBox="0 0 16 16">
      <g class="es-line-inbox">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.763 4.238A2 2 0 0 1 5.612 3h4.776a2 2 0 0 1 1.85 1.239l1.514 3.68h-1.844c-.625 0-1.208.312-1.555.832a.868.868 0 0 1-.722.387H6.368a.868.868 0 0 1-.722-.387A1.868 1.868 0 0 0 4.09 7.92H2.247l1.516-3.68ZM2 8.92v2.375a1.5 1.5 0 0 0 1.5 1.5h8.999a1.5 1.5 0 0 0 1.5-1.5V8.919h-2.091a.868.868 0 0 0-.723.387c-.346.52-.93.832-1.554.832H6.368a1.868 1.868 0 0 1-1.554-.832.868.868 0 0 0-.723-.387H2ZM5.612 2a3 3 0 0 0-2.774 1.858L1.113 8.046A1.5 1.5 0 0 0 1 8.617v2.677a2.5 2.5 0 0 0 2.5 2.5h8.999a2.5 2.5 0 0 0 2.5-2.5V8.617a1.5 1.5 0 0 0-.113-.571l-1.724-4.188A3 3 0 0 0 10.388 2H5.612Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-edit-alt" fill="none" viewBox="0 0 16 16">
      <g class="es-line-edit-alt">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.35 9.846a.5.5 0 0 1 .13-.224l7-7a1 1 0 0 1 1.415 0l1.414 1.414a1 1 0 0 1 0 1.414l-7 7a.501.501 0 0 1-.225.13l-2.899.777a.5.5 0 0 1-.612-.612l.778-2.9Zm.932.388-.519 1.933 1.933-.518 6.906-6.906-1.415-1.414-6.905 6.905Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-location" fill="none" viewBox="0 0 16 16">
      <g class="es-line-location">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="m4.16 11.012.013.013.006.006.598.542L6.1 12.768l.205.185c.724.653 1.44 1.3 1.7 1.538l.78-.693 1.163-1.034a150.846 150.846 0 0 0 1.596-1.442v-.026l.32-.296c1.085-1.008 1.646-2.417 1.646-3.948v-.025C13.59 3.953 11.155 1.5 8.051 1.5A5.512 5.512 0 0 0 2.5 7.052c0 1.531.56 2.94 1.646 3.948l.014.012Zm3.747 3.57Zm-.51.707a415.59 415.59 0 0 0-1.966-1.778c-.959-.866-1.919-1.732-1.965-1.779C2.156 10.516 1.5 8.831 1.5 7.052A6.512 6.512 0 0 1 8.052.5c3.65 0 6.553 2.902 6.459 6.552 0 1.779-.655 3.464-1.966 4.68 0 .07-2.146 1.973-3.268 2.967-.378.335-.64.567-.663.59-.28.281-.843.281-1.217 0Zm.655-5.365A2.878 2.878 0 0 1 5.18 7.052 2.878 2.878 0 0 1 8.052 4.18a2.878 2.878 0 0 1 2.872 2.872 2.878 2.878 0 0 1-2.872 2.872ZM6.18 7.052c0 1.03.843 1.872 1.872 1.872 1.03 0 1.872-.842 1.872-1.872 0-1.03-.842-1.872-1.872-1.872-1.03 0-1.872.843-1.872 1.872Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-location" fill="none" viewBox="0 0 16 16">
      <g class="es-line-location">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M14 8A6 6 0 1 1 2 8a6 6 0 0 1 12 0Zm1 0A7 7 0 1 1 1 8a7 7 0 0 1 14 0Zm-3.106-3.27a.5.5 0 0 0-.674-.674L6.606 6.442a.5.5 0 0 0-.214.215L4.006 11.27a.5.5 0 0 0 .673.674l4.614-2.386a.5.5 0 0 0 .214-.215l2.387-4.613ZM7.207 7.257l3.077-1.591-1.592 3.077-3.076 1.59 1.591-3.076Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-settings" fill="none" viewBox="0 0 16 16">
      <g class="es-line-settings">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M4.205 3.31A2.917 2.917 0 0 0 7.367 2h1.266a2.917 2.917 0 0 0 3.162 1.31l.448.447.447.448A2.917 2.917 0 0 0 14 7.367v1.266a2.917 2.917 0 0 0-1.31 3.162l-.443.443-.476.452A2.873 2.873 0 0 0 8.631 14H7.366a2.917 2.917 0 0 0-3.162-1.31l-.448-.447-.447-.448A2.917 2.917 0 0 0 2 8.633V7.367A2.894 2.894 0 0 0 3.296 4.22l.461-.462.448-.447ZM7.154 1a.634.634 0 0 0-.565.346 1.917 1.917 0 0 1-2.297.95.634.634 0 0 0-.643.156l-.599.598-.615.615a.632.632 0 0 0-.152.648 1.892 1.892 0 0 1-.938 2.276.634.634 0 0 0-.345.564v1.693c0 .239.133.457.346.565a1.917 1.917 0 0 1 .95 2.296.634.634 0 0 0 .156.644l.598.599.599.598a.634.634 0 0 0 .643.155 1.917 1.917 0 0 1 2.297.951.634.634 0 0 0 .565.346h1.693a.634.634 0 0 0 .564-.345l.013-.026a1.872 1.872 0 0 1 2.251-.927.632.632 0 0 0 .633-.142l.633-.601.009-.01.598-.598a.634.634 0 0 0 .155-.643 1.917 1.917 0 0 1 .951-2.297.634.634 0 0 0 .346-.565V7.154a.634.634 0 0 0-.346-.565 1.917 1.917 0 0 1-.95-2.297.634.634 0 0 0-.156-.643l-.598-.599-.599-.598a.634.634 0 0 0-.644-.155 1.917 1.917 0 0 1-2.296-.951A.634.634 0 0 0 8.846 1H7.154ZM6 8a2 2 0 1 1 4 0 2 2 0 0 1-4 0Zm2-3a3 3 0 1 0 0 6 3 3 0 0 0 0-6Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-people" fill="none" viewBox="0 0 16 16">
      <g class="es-line-people">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M11.901 3.881a1.063 1.063 0 1 1-2.126 0 1.063 1.063 0 0 1 2.126 0Zm1 0a2.063 2.063 0 1 1-4.126 0 2.063 2.063 0 0 1 4.126 0Zm-3.45 3.111a3.589 3.589 0 0 0-1.774.468l.494.87a2.594 2.594 0 0 1 1.28-.338h2.257c1.216 0 2.269.845 2.533 2.032l.074.336a1.048 1.048 0 0 1-1.022 1.275h-2.197v1h2.197a2.048 2.048 0 0 0 1.999-2.492l-.075-.336a3.595 3.595 0 0 0-3.51-2.815H9.452ZM4.293 9.54c-1.216 0-2.27.844-2.533 2.032l-.075.336a1.048 1.048 0 0 0 1.023 1.274h5.427c.67 0 1.168-.62 1.022-1.274l-.074-.336A2.595 2.595 0 0 0 6.55 9.54H4.293Zm-3.51 1.815a3.595 3.595 0 0 1 3.51-2.815H6.55a3.595 3.595 0 0 1 3.509 2.815l.075.335a2.048 2.048 0 0 1-2 2.492H2.709a2.048 2.048 0 0 1-2-2.492l.075-.335Zm5.96-6.184a1.321 1.321 0 1 1-2.643 0 1.321 1.321 0 0 1 2.643 0Zm1 0a2.321 2.321 0 1 1-4.643 0 2.321 2.321 0 0 1 4.643 0Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-emoji" fill="none" viewBox="0 0 16 16">
      <g class="es-line-emoji" clip-path="url(#a)">
        <g class="Union">
          <path fill="url(#b)" d="M0 16.12h16v-16H0v16Z" class="Vector"/>
        </g>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <path fill="#fff" d="M0 0h16v16H0z"/>
        </clipPath>
        <pattern id="b" width="1" height="1" class="b" patternContentUnits="objectBoundingBox">
          <use href="#c" transform="scale(.00625)"/>
        </pattern>
        <image id="c" width="160" height="160" class="c" href="data:image/png;base64,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"/>
      </defs>
    </symbol>
    <symbol id="es-line-calender" fill="none" viewBox="0 0 16 16">
      <g class="es-line-calender">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M4.5 1a.5.5 0 0 1 .5.5v1h6v-1a.5.5 0 0 1 1 0v1.024c.49.03.838.097 1.135.248a2.5 2.5 0 0 1 1.092 1.093C14.5 4.4 14.5 5.1 14.5 6.5v4c0 1.4 0 2.1-.273 2.635a2.5 2.5 0 0 1-1.092 1.092c-.535.273-1.235.273-2.635.273h-5c-1.4 0-2.1 0-2.635-.273a2.5 2.5 0 0 1-1.093-1.092C1.5 12.6 1.5 11.9 1.5 10.5v-4c0-1.4 0-2.1.272-2.635a2.5 2.5 0 0 1 1.093-1.093c.297-.15.645-.218 1.136-.248L4 2.5v-1a.5.5 0 0 1 .5-.5Zm1 2.5h5c.717 0 1.194 0 1.56.03.356.03.518.081.621.133a1.5 1.5 0 0 1 .655.656c.053.103.104.265.133.62.023.274.029.61.03 1.061H2.501c.001-.451.007-.787.03-1.06.029-.356.08-.518.132-.621a1.5 1.5 0 0 1 .656-.656c.103-.052.265-.103.62-.132.367-.03.844-.031 1.561-.031ZM2.5 7v3.5c0 .717 0 1.194.03 1.56.03.356.081.518.133.621a1.5 1.5 0 0 0 .656.655c.103.053.265.104.62.133.367.03.844.031 1.561.031h5c.717 0 1.194 0 1.56-.03.356-.03.518-.081.621-.134a1.5 1.5 0 0 0 .655-.655c.053-.103.104-.265.133-.62.03-.367.031-.844.031-1.561V7h-11Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-home" fill="none" viewBox="0 0 16 16">
      <g class="es-line-home" filter="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7.322 2.806a1 1 0 0 1 1.356 0l4.84 4.467A1.5 1.5 0 0 1 14 8.376V12.5a1.5 1.5 0 0 1-1.5 1.5H11v-2.75a3 3 0 0 0-6 0V14H3.5A1.5 1.5 0 0 1 2 12.5V8.376a1.5 1.5 0 0 1 .483-1.103l4.839-4.467ZM6 14h4v-2.75a2 2 0 0 0-4 0V14ZM9.357 2.072a2 2 0 0 0-2.714 0L1.804 6.539A2.5 2.5 0 0 0 1 8.376V12.5A2.5 2.5 0 0 0 3.5 15h9a2.5 2.5 0 0 0 2.5-2.5V8.376a2.5 2.5 0 0 0-.804-1.837l-4.84-4.467Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <filter id="a" width="24" height="24" x="-4" y="-4" class="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_5_8236"/>
          <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_5_8236" result="shape"/>
        </filter>
      </defs>
    </symbol>
    <symbol id="es-line-chat" fill="none" viewBox="0 0 16 16">
      <g class="es-line-chat">
        <path fill="var(--icon-stroke)" d="m2.957 10.952.48.137a.5.5 0 0 0-.06-.409l-.42.272ZM2 14.3l-.48-.137a.5.5 0 0 0 .656.605L2 14.3Zm3.303-1.239.225-.446a.5.5 0 0 0-.4-.022l.175.469Zm8.197-5.36A5.5 5.5 0 0 1 8 13.2v1a6.5 6.5 0 0 0 6.5-6.5h-1ZM8 2.2a5.5 5.5 0 0 1 5.5 5.5h1A6.5 6.5 0 0 0 8 1.2v1ZM2.5 7.7A5.5 5.5 0 0 1 8 2.2v-1a6.5 6.5 0 0 0-6.5 6.5h1Zm.877 2.98A5.472 5.472 0 0 1 2.5 7.7h-1a6.46 6.46 0 0 0 1.037 3.522l.84-.543Zm-.896 3.757.956-3.349-.961-.274-.957 3.348.962.275Zm2.647-1.845-3.304 1.239.352.936 3.303-1.238-.351-.937ZM8 13.2c-.89 0-1.73-.21-2.472-.585l-.45.893A6.475 6.475 0 0 0 8 14.2v-1Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-line-teams" fill="none" viewBox="0 0 16 16">
      <g class="es-line-teams" filter="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8.347 2.123a.7.7 0 0 0-.694 0L4.252 4.066 1.056 5.892a.7.7 0 0 0 0 1.216l3.196 1.826 3.4 1.943a.7.7 0 0 0 .695 0l3.401-1.943 3.196-1.826a.7.7 0 0 0 0-1.216l-6.597-3.77ZM4.748 4.934 8 3.076 13.992 6.5l-2.74 1.566L8 9.924 4.748 8.066 2.008 6.5l2.74-1.566Zm-3.5 4.132a.5.5 0 0 0-.496.868l3.5 2 3.4 1.943a.7.7 0 0 0 .695 0l3.401-1.943 3.5-2a.5.5 0 0 0-.496-.868l-3.5 2L8 12.924l-3.252-1.858-3.5-2Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <filter id="a" width="24" height="24" x="-4" y="-4" class="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_84_50838"/>
          <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_84_50838" result="shape"/>
        </filter>
      </defs>
    </symbol>
    <symbol id="es-line-emoji" fill="none" viewBox="0 0 16 16">
      <g class="es-line-emoji">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M14 8A6 6 0 1 1 2 8a6 6 0 0 1 12 0Zm1 0A7 7 0 1 1 1 8a7 7 0 0 1 14 0Zm-9.154-.538a1.08 1.08 0 0 0 1.077-1.077 1.08 1.08 0 0 0-1.077-1.077A1.08 1.08 0 0 0 4.77 6.385a1.08 1.08 0 0 0 1.077 1.077Zm4.592 2.615H5.48c.43.758 1.382 1.23 2.52 1.23 1.123 0 1.992-.562 2.438-1.23Zm1.281-.503c-.288 1.434-1.787 2.734-3.719 2.734-1.693 0-3.46-.91-3.733-2.732-.04-.273.188-.5.464-.5h6.538c.276 0 .505.227.45.498Zm-1.565-2.112a1.08 1.08 0 0 0 1.077-1.077 1.08 1.08 0 0 0-1.077-1.077 1.08 1.08 0 0 0-1.077 1.077 1.08 1.08 0 0 0 1.077 1.077Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-add-emoji" fill="none" viewBox="0 0 16 16">
      <g class="es-line-add-emoji">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M13.5 0a.5.5 0 0 1 .5.5V2h1.5a.5.5 0 0 1 0 1H14v1.5a.5.5 0 0 1-1 0V3h-1.5a.5.5 0 0 1 0-1H13V.5a.5.5 0 0 1 .5-.5ZM2 8a6 6 0 0 1 6-6 .5.5 0 0 0 0-1 7 7 0 1 0 7 7 .5.5 0 0 0-1 0A6 6 0 1 1 2 8Zm3.833-.543c.596 0 1.083-.487 1.083-1.082 0-.596-.487-1.083-1.083-1.083-.595 0-1.083.487-1.083 1.083 0 .595.488 1.082 1.083 1.082Zm4.63 2.627H5.455c.431.77 1.394 1.25 2.545 1.25 1.137 0 2.016-.573 2.463-1.25Zm1.279-.504c-.289 1.444-1.797 2.753-3.742 2.753-1.705 0-3.482-.917-3.756-2.75-.04-.274.188-.5.464-.5h6.583c.277 0 .505.226.45.497Zm-1.576-2.123c.596 0 1.083-.487 1.083-1.082 0-.596-.487-1.083-1.083-1.083-.595 0-1.083.487-1.083 1.083 0 .595.488 1.082 1.083 1.082Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-template" fill="none" viewBox="0 0 16 16">
      <g class="es-line-template">
        <g class="Union">
          <mask id="a" fill="#fff" class="a">
            <path fill-rule="evenodd" d="M9.131 3.825a7.338 7.338 0 0 1 2.83 2.858c.017.03.06.03.078 0a7.009 7.009 0 0 1 .567-.877A7.338 7.338 0 0 1 14.93 3.79a.044.044 0 0 0 0-.077 7.331 7.331 0 0 1-2.891-2.89.044.044 0 0 0-.078 0 6.98 6.98 0 0 1-.513.804A7.337 7.337 0 0 1 9.07 3.714a.044.044 0 0 0 0 .077l.061.034ZM12 4.958a8.34 8.34 0 0 0-1.205-1.205A8.34 8.34 0 0 0 12 2.548c.36.441.763.845 1.205 1.205A8.34 8.34 0 0 0 12 4.958ZM3.6 2.353a1.5 1.5 0 0 0-1.5 1.5v8.8a1.5 1.5 0 0 0 1.5 1.5H11a1.5 1.5 0 0 0 1.5-1.5v-2.4a.5.5 0 0 1 1 0v2.4a2.5 2.5 0 0 1-2.5 2.5H3.6a2.5 2.5 0 0 1-2.5-2.5v-8.8a2.5 2.5 0 0 1 2.5-2.5H7a.5.5 0 0 1 0 1H3.6Zm.41 6.985a9.288 9.288 0 0 0-.795-.476.07.07 0 0 1 0-.123 9.068 9.068 0 0 0 .876-.532A9.29 9.29 0 0 0 7.04 4.915a.07.07 0 0 1 .123 0 9.382 9.382 0 0 0 .53.876 9.29 9.29 0 0 0 3.293 2.948.07.07 0 0 1 0 .123l-.092.05a9.294 9.294 0 0 0-3.731 3.773.07.07 0 0 1-.123 0 9.485 9.485 0 0 0-.532-.876A9.29 9.29 0 0 0 4.01 9.338Zm.972-.538c.805.599 1.52 1.313 2.118 2.118A10.291 10.291 0 0 1 9.218 8.8 10.29 10.29 0 0 1 7.1 6.682 10.29 10.29 0 0 1 4.982 8.8Z" clip-rule="evenodd"/>
          </mask>
          <path fill="var(--icon-stroke)" d="m9.874 4.304.592-.806-.592.806Zm-.743-.479-.49.872.49-.872Zm.816.534.602-.8-.602.8Zm1.447 1.447-.799.601.799-.601Zm.054.073.806-.592-.806.592Zm.48.742-.872.49.871-.49Zm.033.062-.876.482.876-.482Zm.078 0 .876.482-.876-.482Zm.034-.062.871.49-.871-.49Zm.479-.742-.806-.592.806.592Zm.054-.073.799.601-.799-.601Zm1.447-1.447.602.798-.602-.798Zm.073-.055.592.807-.592-.807Zm.743-.479.49.872-.49-.872Zm.06-.034.483.876-.482-.876Zm0-.077.483-.876-.482.876Zm-.06-.034.49-.871-.49.871Zm-.743-.479.592-.806-.592.806Zm-.073-.054.602-.799-.602.799ZM12.606 1.7l.799-.602-.799.602Zm-.054-.073-.806.591.806-.591Zm-.48-.743.872-.49-.871.49ZM12.04.823l.876-.482-.876.482Zm-.078 0 .877.482-.877-.482Zm-.034.061.872.49-.872-.49Zm-.479.743.806.591-.806-.591Zm-.054.073-.799-.602.799.602ZM9.947 3.147l-.602-.799.602.799Zm-.073.054.592.806-.592-.806Zm-.743.48-.49-.872.49.871Zm-.06.033-.483-.876.482.876Zm0 .077-.483.876.482-.876Zm1.724-.038-.631-.776-.954.776.954.775.63-.775ZM12 4.958l-.776.631.776.953.776-.953L12 4.958Zm0-2.41.776-.632L12 .963l-.776.953.776.632Zm1.205 1.205.631.775.954-.775-.954-.776-.63.776ZM3.307 8.91l.481-.877-.48.877Zm.703.427.556-.831-.556.83Zm-.795-.476-.47.882.47-.882Zm0-.123-.47-.883.47.883Zm.092-.05-.48-.877.48.877Zm.703-.426.556.83-.556-.83Zm.081-.056.565.825-.565-.825ZM6.507 5.79l.825.565-.825-.565Zm.056-.081.83.556-.83-.556Zm.426-.703-.877-.48.877.48Zm.05-.092-.883-.47.883.47Zm.123 0-.883.471.883-.47Zm.05.092-.878.481.877-.48Zm.426.703-.831.556.83-.556Zm.055.081.825-.565-.825.565Zm2.416 2.416-.565.825.565-.825Zm.082.056.556-.832-.556.832Zm.702.426-.48.877.48-.877Zm.092.05-.47.882.47-.882Zm0 .123-.47-.883.47.883Zm-.092.05.48.876-.48-.877Zm-.702.426.556.83-.556-.83Zm-.082.055-.565-.825.565.825Zm-2.416 2.416-.825-.565.825.565Zm-.055.082.83.556-.83-.556Zm-.427.702-.877-.481.877.48Zm-.05.092-.882-.47.883.47Zm-.122 0 .882-.47-.882.47Zm-.05-.092.877-.481-.877.48Zm-.426-.702-.831.556.83-.556Zm-.056-.082.825-.565-.825.565ZM4.091 9.393l.565-.825-.565.825Zm3.01 1.525-.803.597.802 1.08.803-1.08-.803-.597ZM4.98 8.8l-.596-.802-1.08.802 1.08.803.597-.803Zm4.237 0 .597.803 1.08-.803-1.08-.802-.597.802ZM7.1 6.682l.803-.597-.803-1.08-.802 1.08.802.597Zm3.366-3.184a8.342 8.342 0 0 0-.844-.544L8.64 4.697c.223.125.437.263.641.414l1.184-1.613Zm.083.062a8.012 8.012 0 0 0-.083-.062L9.282 5.111l.063.046L10.55 3.56Zm1.644 1.644a8.338 8.338 0 0 0-1.644-1.644L9.345 5.157a6.34 6.34 0 0 1 1.25 1.25l1.598-1.203Zm.061.083a7.282 7.282 0 0 0-.061-.083l-1.598 1.203.047.063 1.612-1.183Zm.545.844a8.313 8.313 0 0 0-.545-.844L10.642 6.47c.15.205.289.42.414.642l1.743-.981Zm.039.07a8.365 8.365 0 0 0-.04-.07l-1.742.98.03.054 1.752-.964Zm-1.675 0a.956.956 0 0 1 1.675 0l-1.753.964c.397.72 1.433.72 1.83 0L11.163 6.2Zm.038-.07a8.055 8.055 0 0 0-.039.07l1.753.964.03-.053L11.2 6.13Zm.545-.844c-.198.27-.38.551-.545.844l1.743.98a6.3 6.3 0 0 1 .414-.64l-1.612-1.184Zm.061-.083-.061.083 1.612 1.183.047-.063-1.598-1.203Zm1.644-1.644a8.338 8.338 0 0 0-1.644 1.644l1.598 1.203a6.34 6.34 0 0 1 1.25-1.25L13.45 3.56Zm.083-.062-.083.062 1.204 1.597a6.42 6.42 0 0 1 .063-.046l-1.184-1.613Zm.844-.544a8.335 8.335 0 0 0-.844.544l1.184 1.613a6.34 6.34 0 0 1 .641-.414l-.98-1.743Zm.07-.039a7.531 7.531 0 0 0-.07.039l.981 1.743.053-.03-.964-1.752Zm0 1.675a.956.956 0 0 1 0-1.675l.964 1.752c.72-.396.72-1.432 0-1.829l-.964 1.752Zm-.07-.038.07.038.964-1.752a5.332 5.332 0 0 1-.053-.03l-.98 1.744Zm-.844-.545c.27.198.551.38.844.545l.981-1.743a6.342 6.342 0 0 1-.641-.414l-1.184 1.612Zm-.083-.061.083.061 1.184-1.612a6.585 6.585 0 0 1-.063-.047L13.45 3.946ZM11.807 2.3a8.338 8.338 0 0 0 1.644 1.645l1.204-1.598a6.34 6.34 0 0 1-1.25-1.25L11.807 2.3Zm-.061-.083.061.083 1.598-1.203a7.186 7.186 0 0 1-.047-.063l-1.612 1.183Zm-.545-.843c.165.292.347.574.545.843l1.612-1.183a6.335 6.335 0 0 1-.414-.641l-1.743.98Zm-.039-.07a8.7 8.7 0 0 0 .04.07l1.742-.981a5.503 5.503 0 0 1-.03-.053l-1.752.964Zm1.675 0a.956.956 0 0 1-1.675 0L12.915.34a1.044 1.044 0 0 0-1.83 0l1.752.964Zm-.038.07.039-.07L11.085.34l-.03.053 1.744.98Zm-.545.843c.198-.269.38-.55.545-.843l-1.743-.98a6.336 6.336 0 0 1-.414.641l1.612 1.183Zm-.061.083.061-.083-1.612-1.183a7.186 7.186 0 0 1-.047.063L12.193 2.3Zm-1.644 1.645A8.338 8.338 0 0 0 12.193 2.3l-1.598-1.203a6.34 6.34 0 0 1-1.25 1.25l1.204 1.598Zm-.083.061.083-.061-1.204-1.598-.063.047 1.184 1.612Zm-.844.545a8.33 8.33 0 0 0 .844-.545L9.282 2.395a6.34 6.34 0 0 1-.641.414l.98 1.743Zm-.07.038.07-.038-.981-1.743-.053.03.964 1.751Zm0-1.675a.956.956 0 0 1 0 1.675l-.964-1.752a1.044 1.044 0 0 0 0 1.83l.964-1.753Zm.07.039a8.593 8.593 0 0 0-.07-.039l-.964 1.752.053.03.98-1.743Zm.542 1.574c.389.317.744.672 1.06 1.061l1.552-1.262a9.34 9.34 0 0 0-1.35-1.35l-1.262 1.551Zm1.06-2.612a7.336 7.336 0 0 1-1.06 1.061l1.262 1.551a9.34 9.34 0 0 0 1.35-1.35l-1.552-1.262Zm2.612 1.061a7.336 7.336 0 0 1-1.06-1.06l-1.552 1.262a9.34 9.34 0 0 0 1.35 1.35l1.262-1.552Zm-1.06 2.612a7.336 7.336 0 0 1 1.06-1.06l-1.262-1.552a9.34 9.34 0 0 0-1.35 1.35l1.552 1.262ZM3.1 3.853a.5.5 0 0 1 .5-.5v-2a2.5 2.5 0 0 0-2.5 2.5h2Zm0 8.8v-8.8h-2v8.8h2Zm.5.5a.5.5 0 0 1-.5-.5h-2a2.5 2.5 0 0 0 2.5 2.5v-2Zm7.4 0H3.6v2H11v-2Zm.5-.5a.5.5 0 0 1-.5.5v2a2.5 2.5 0 0 0 2.5-2.5h-2Zm0-2.4v2.4h2v-2.4h-2Zm1.5-1.5a1.5 1.5 0 0 0-1.5 1.5h2a.5.5 0 0 1-.5.5v-2Zm1.5 1.5a1.5 1.5 0 0 0-1.5-1.5v2a.5.5 0 0 1-.5-.5h2Zm0 2.4v-2.4h-2v2.4h2Zm-3.5 3.5a3.5 3.5 0 0 0 3.5-3.5h-2a1.5 1.5 0 0 1-1.5 1.5v2Zm-7.4 0H11v-2H3.6v2Zm-3.5-3.5a3.5 3.5 0 0 0 3.5 3.5v-2a1.5 1.5 0 0 1-1.5-1.5h-2Zm0-8.8v8.8h2v-8.8h-2Zm3.5-3.5a3.5 3.5 0 0 0-3.5 3.5h2a1.5 1.5 0 0 1 1.5-1.5v-2Zm3.4 0H3.6v2H7v-2Zm1.5 1.5A1.5 1.5 0 0 0 7 .353v2a.5.5 0 0 1-.5-.5h2ZM7 3.353a1.5 1.5 0 0 0 1.5-1.5h-2a.5.5 0 0 1 .5-.5v2Zm-3.4 0H7v-2H3.6v2Zm-.773 6.435c.215.118.424.245.626.38l1.113-1.661a10.397 10.397 0 0 0-.778-.473l-.961 1.754Zm-.082-.044.082.044.961-1.754a10.407 10.407 0 0 0-.102-.055l-.941 1.765Zm0-1.888a1.07 1.07 0 0 0 0 1.888l.941-1.765a.93.93 0 0 1 0 1.642l-.941-1.765Zm.082-.044-.082.044.941 1.765.102-.055-.961-1.754Zm.626-.38a8.29 8.29 0 0 1-.626.38l.961 1.754c.267-.147.526-.304.778-.472L3.453 7.43Zm.073-.05a8.234 8.234 0 0 1-.073.05l1.113 1.662.09-.062-1.13-1.65Zm2.156-2.156a8.29 8.29 0 0 1-2.156 2.156l1.13 1.65a10.29 10.29 0 0 0 2.676-2.676l-1.65-1.13Zm.05-.073a8.3 8.3 0 0 1-.05.073l1.65 1.13.062-.09-1.662-1.113Zm.38-.626a8.29 8.29 0 0 1-.38.626l1.662 1.113c.168-.252.325-.511.472-.778l-1.754-.961Zm.044-.082-.044.082 1.754.961.055-.102-1.765-.941Zm1.888 0a1.07 1.07 0 0 0-1.888 0l1.765.941a.93.93 0 0 1-1.642 0l1.765-.941Zm.044.082a8.323 8.323 0 0 1-.044-.082l-1.765.941.055.102 1.754-.961Zm.38.626a8.29 8.29 0 0 1-.38-.626l-1.754.961c.147.267.304.526.473.778l1.662-1.113Zm.05.073a8.371 8.371 0 0 1-.05-.073l-1.66 1.113.06.09 1.651-1.13Zm2.156 2.156a8.29 8.29 0 0 1-2.156-2.156l-1.65 1.13a10.29 10.29 0 0 0 2.676 2.676l1.13-1.65Zm.073.05a9.496 9.496 0 0 1-.073-.05l-1.13 1.65.09.062 1.113-1.663Zm.626.38a8.3 8.3 0 0 1-.626-.38L9.634 9.093c.252.168.511.325.778.472l.961-1.754Zm.083.044a8.33 8.33 0 0 1-.083-.044l-.96 1.754.101.055.942-1.765Zm0 1.888a1.07 1.07 0 0 0 0-1.888l-.942 1.765a.93.93 0 0 1 0-1.642l.942 1.765Zm-.083.044.083-.044-.942-1.765a9.777 9.777 0 0 0-.102.055l.961 1.754Zm-.626.38c.202-.135.411-.262.626-.38l-.96-1.754c-.268.147-.527.304-.779.473l1.113 1.662Zm-.073.05.073-.05-1.113-1.661-.09.06 1.13 1.65Zm-2.156 2.156a8.29 8.29 0 0 1 2.156-2.156l-1.13-1.65a10.289 10.289 0 0 0-2.676 2.676l1.65 1.13Zm-.05.073a7.69 7.69 0 0 1 .05-.073l-1.65-1.13-.061.09 1.662 1.113Zm-.38.626c.118-.215.245-.424.38-.626l-1.661-1.113c-.169.252-.326.511-.473.778l1.754.961Zm-.044.082.044-.082-1.754-.961-.055.102 1.765.941Zm-1.888 0a1.07 1.07 0 0 0 1.888 0l-1.765-.94a.93.93 0 0 1 1.642 0l-1.765.94Zm-.044-.082.044.082 1.765-.94a10.677 10.677 0 0 0-.055-.103l-1.754.961Zm-.38-.626c.135.202.262.411.38.626l1.754-.961a10.289 10.289 0 0 0-.472-.778l-1.662 1.113Zm-.05-.073.05.073 1.662-1.113a9.93 9.93 0 0 0-.062-.09l-1.65 1.13Zm-2.156-2.156a8.29 8.29 0 0 1 2.156 2.156l1.65-1.13a10.289 10.289 0 0 0-2.676-2.676l-1.13 1.65Zm-.073-.05.073.05 1.13-1.65-.09-.061-1.113 1.662Zm4.45.154a11.29 11.29 0 0 0-2.325-2.324L4.385 9.603a9.288 9.288 0 0 1 1.913 1.912l1.605-1.193Zm.719-2.324a11.29 11.29 0 0 0-2.324 2.324l1.605 1.193a9.289 9.289 0 0 1 1.912-1.912L8.622 7.998Zm-2.324-.72a11.29 11.29 0 0 0 2.324 2.325l1.193-1.605a9.29 9.29 0 0 1-1.912-1.913L6.298 7.278Zm-.72 2.325a11.29 11.29 0 0 0 2.325-2.325L6.298 6.085a9.29 9.29 0 0 1-1.913 1.913l1.193 1.605Z" mask="url(#a)"/>
        </g>
      </g>
    </symbol>
    <symbol id="es-line-attachment" fill="none" viewBox="0 0 16 16">
      <g class="es-line-attachment">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M12.568 2.508a2.572 2.572 0 0 0-3.646 0L3.624 7.714c-1.499 1.473-1.499 3.955 0 5.428 1.504 1.477 4.045 1.477 5.548 0l2.989-2.937a.5.5 0 1 1 .7.713l-2.988 2.937c-1.893 1.86-5.057 1.86-6.95 0-1.897-1.865-1.897-4.99 0-6.854l5.298-5.207a3.572 3.572 0 0 1 5.048 0c1.422 1.398 1.422 3.588 0 4.986l-.002.002-4.888 4.737-.001.001c-.86.844-2.267.874-3.087-.01-.855-.85-.883-2.241.018-3.05l2.03-1.993a.5.5 0 1 1 .7.713L6.001 9.183l-.009.009-.01.009c-.443.391-.472 1.122.02 1.605l.009.009.009.01c.403.44 1.162.468 1.658-.02l.002-.002 4.888-4.737h.002c1.022-1.006 1.021-2.553-.002-3.558Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="icon/at-sign" fill="none" viewBox="0 0 16 16">
      <g class="icon/at-sign">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M5.01 1.671a7 7 0 0 1 4.58-.488l-.113.487.114-.487A7 7 0 0 1 15 8v.65a2.45 2.45 0 0 1-4.555 1.256A3.1 3.1 0 1 1 10.1 5.72V5.4a.5.5 0 0 1 1 0v3.25a1.45 1.45 0 0 0 2.9 0V8a6 6 0 0 0-4.636-5.843l.113-.486-.113.486a6 6 0 0 0-3.928.418l-.195-.413.195.413a6 6 0 1 0 6.212 10.189.5.5 0 0 1 .608.794 7 7 0 0 1-8.7-.15 7 7 0 0 1-1.833-8.507l.446.22-.446-.22a7 7 0 0 1 3.286-3.23ZM10.1 8a2.1 2.1 0 1 0-4.2 0 2.1 2.1 0 0 0 4.2 0Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-table-view" fill="none" viewBox="0 0 16 16">
      <g class="es-line-table-view">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M4.5 2h-1A1.5 1.5 0 0 0 2 3.5v9A1.5 1.5 0 0 0 3.5 14h1V2ZM5 1H3.5A2.5 2.5 0 0 0 1 3.5v9A2.5 2.5 0 0 0 3.5 15h9a2.5 2.5 0 0 0 2.5-2.5v-9A2.5 2.5 0 0 0 12.5 1H5Zm.5 1v3H14V3.5A1.5 1.5 0 0 0 12.5 2h-7Zm0 4v3.5H14V6H5.5Zm0 4.5V14h7a1.5 1.5 0 0 0 1.5-1.5v-2H5.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-image" fill="none" viewBox="0 0 16 16">
      <g class="es-line-image" clip-path="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="m13.643 2.93-1.192-.21c.035.356.132.746.324 1.114.263.504.717.992 1.496 1.293l.183-1.038a1 1 0 0 0-.81-1.158ZM7.036 1.767l4.402.776a4.12 4.12 0 0 0 .45 1.755c.402.768 1.1 1.453 2.207 1.83l-.642 3.64c-1.775-.84-3.22-.973-4.357-.751a4.912 4.912 0 0 0-.2.043c-.61-.917-1.684-1.33-2.6-1.478a5.313 5.313 0 0 0-1.291-.053l.873-4.951a1 1 0 0 1 1.158-.811Zm-.9 6.802c.698.112 1.359.385 1.773.845a4.48 4.48 0 0 0-1.257.917.5.5 0 1 0 .723.69 3.57 3.57 0 0 1 1.912-1.023c.935-.182 2.238-.084 3.97.787l.009.004.007.003-.316 1.789a1 1 0 0 1-1.158.81l-6.607-1.164a1 1 0 0 1-.812-1.159l.442-2.505c.074-.015.16-.028.256-.037.299-.028.67-.02 1.058.043ZM7.21.78a2 2 0 0 0-2.317 1.622l-.265 1.5-.21.047-1.22.27-.489.109a2.5 2.5 0 0 0-1.9 2.982l1.3 5.858a2.5 2.5 0 0 0 2.981 1.9l2.93-.65 1.22-.27.485-.108 1.9.335a2 2 0 0 0 2.317-1.623l1.497-8.49a2 2 0 0 0-1.622-2.317L7.21.78Zm-.058 12.806-2.134-.376a2 2 0 0 1-1.622-2.317L4.44 4.97l-1.026.227-.488.108a1.5 1.5 0 0 0-1.14 1.79l1.299 5.857a1.5 1.5 0 0 0 1.789 1.14l2.278-.505Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <path fill="#fff" d="M0 0h16v16H0z"/>
        </clipPath>
      </defs>
    </symbol>
    <symbol id="es-line-image-alt1" fill="none" viewBox="0 0 16 16">
      <g class="es-line-image-alt1">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M12.5 2.5h-9A1.5 1.5 0 0 0 2 4v8a1.5 1.5 0 0 0 1.102 1.447.502.502 0 0 1 .053-.06l6.429-6.112a.9.9 0 0 1 1.1-.11L14 9.252V4a1.5 1.5 0 0 0-1.5-1.5Zm0 11H4.488l5.729-5.447 3.767 2.37.016.01V12a1.5 1.5 0 0 1-1.5 1.5Zm-9-12h9A2.5 2.5 0 0 1 15 4v8a2.5 2.5 0 0 1-2.5 2.5h-9A2.5 2.5 0 0 1 1 12V4a2.5 2.5 0 0 1 2.5-2.5ZM6 5.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM5.25 7.5a1.75 1.75 0 1 0 0-3.5 1.75 1.75 0 0 0 0 3.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-code" fill="none" viewBox="0 0 16 16">
      <g class="es-line-code" clip-path="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M6.518 2.186a.5.5 0 1 0-.929-.371l-2 5a.5.5 0 0 0 .929.371l2-5ZM11.5 3a.5.5 0 0 0 0 1h1a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2h-8a2 2 0 0 1-2-2V9a.5.5 0 0 0-1 0v3a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3h-1Zm-4.253-.577a.5.5 0 0 1 .707 0l2 2a.5.5 0 0 1 0 .707l-2 2a.5.5 0 1 1-.707-.707l1.646-1.646L7.247 3.13a.5.5 0 0 1 0-.707Zm-4.393.707a.5.5 0 1 0-.708-.707l-2 2a.5.5 0 0 0 0 .707l2 2a.5.5 0 1 0 .708-.707L1.207 4.777 2.854 3.13Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <path fill="#fff" d="M0 0h16v16H0z"/>
        </clipPath>
      </defs>
    </symbol>
    <symbol id="es-line-chat-alt" fill="none" viewBox="0 0 16 16">
      <g class="es-line-chat-alt">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 2.5c-3.709 0-6.5 2.344-6.5 5 0 1.645 1.047 3.152 2.751 4.082.25.136.507.422.482.826a5.164 5.164 0 0 1-.261 1.258 4.794 4.794 0 0 1-.098.253 4.4 4.4 0 0 0 .892-.361c.223-.118.428-.268.644-.437l.125-.099c.173-.137.363-.288.553-.412a.97.97 0 0 1 .62-.147c.26.025.524.037.792.037 3.709 0 6.5-2.344 6.5-5s-2.791-5-6.5-5Zm-7.5 5c0-3.42 3.477-6 7.5-6s7.5 2.58 7.5 6-3.477 6-7.5 6c-.298 0-.593-.014-.882-.041-.142.094-.28.203-.442.332l-.15.117a5.324 5.324 0 0 1-.792.534c-1 .528-1.74.588-2.104.573a.584.584 0 0 1-.508-.34.599.599 0 0 1 .035-.565c.091-.147.25-.43.371-.776.102-.288.173-.646.2-.9C1.814 11.375.5 9.586.5 7.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-tomorrow" fill="none" viewBox="0 0 16 16">
      <g class="es-line-tomorrow">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 1a.5.5 0 0 1 .5.5v1.3a.5.5 0 0 1-1 0V1.5A.5.5 0 0 1 8 1Zm5.294 1.646a.5.5 0 0 1 0 .708l-1 1a.5.5 0 0 1-.708-.708l1-1a.5.5 0 0 1 .708 0ZM13.2 7.5a.5.5 0 0 0 0 1h1.3a.5.5 0 0 0 0-1h-1.3Zm-1.613 4.086a.5.5 0 0 1 .707 0l1 1a.5.5 0 1 1-.707.707l-1-1a.5.5 0 0 1 0-.707ZM8.5 13.2a.5.5 0 0 0-1 0v1.3a.5.5 0 0 0 1 0v-1.3Zm-4.146-1.614a.5.5 0 0 1 0 .707l-1 1a.5.5 0 1 1-.707-.707l1-1a.5.5 0 0 1 .707 0ZM1.5 7.5a.5.5 0 0 0 0 1h1.3a.5.5 0 0 0 0-1H1.5Zm1.207-4.794a.5.5 0 0 1 .707 0l1 1a.5.5 0 0 1-.707.708l-1-1a.5.5 0 0 1 0-.708ZM5.5 8a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0ZM8 4.5a3.5 3.5 0 1 0 0 7 3.5 3.5 0 0 0 0-7Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-tag" fill="none" viewBox="0 0 16 16">
      <g class="es-line-tag">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="m9.928 14.171-6.17-6.17A3 3 0 1 1 8 3.756l6.17 6.172a.273.273 0 0 1 0 .385l-3.856 3.857a.273.273 0 0 1-.386 0Zm1.093.707a1.273 1.273 0 0 1-1.8 0L3.05 8.708A4 4 0 1 1 8.707 3.05l6.171 6.171a1.273 1.273 0 0 1 0 1.8l-3.857 3.857ZM6.586 4.818a1.25 1.25 0 1 0-1.768 1.768 1.25 1.25 0 0 0 1.768-1.768Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-today-alt" fill="none" viewBox="0 0 16 16">
      <g class="es-line-today-alt">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8.002 2a5.135 5.135 0 0 0-5.135 5.135.5.5 0 0 1-1 0 6.135 6.135 0 1 1 12.27 0 .5.5 0 1 1-1 0A5.135 5.135 0 0 0 8.003 2ZM1.5 8.803a.5.5 0 0 0 0 1h13.005a.5.5 0 1 0 0-1H1.5Zm1.734 2.6a.5.5 0 0 0 0 1h9.537a.5.5 0 1 0 0-1H3.234Zm1.734 2.602a.5.5 0 0 0 0 1h6.069a.5.5 0 1 0 0-1h-6.07Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-status" fill="none" viewBox="0 0 16 16">
      <g class="es-line-status">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 6.375A1.812 1.812 0 1 0 8 2.75a1.812 1.812 0 0 0 0 3.625Zm0 1A2.813 2.813 0 1 0 8 1.75a2.813 2.813 0 0 0 0 5.625Zm3.938 5.75a1.813 1.813 0 1 0 0-3.625 1.813 1.813 0 0 0 0 3.625Zm0 1a2.813 2.813 0 1 0 0-5.625 2.813 2.813 0 0 0 0 5.625Zm-6.063-2.813a1.813 1.813 0 1 1-3.625 0 1.813 1.813 0 0 1 3.625 0Zm1 0a2.813 2.813 0 1 1-5.625 0 2.813 2.813 0 0 1 5.625 0Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-quotes-alt" fill="none" viewBox="0 0 16 16">
      <g class="es-line-quotes-alt">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M6.75 5.252a1 1 0 1 0 0-.008v.008Zm.467-1.93A2.002 2.002 0 0 1 9.75 5.25a2 2 0 1 1-4 0v-.01A5.236 5.236 0 0 1 8.193.827a.5.5 0 1 1 .533.846 4.236 4.236 0 0 0-1.508 1.649Zm3.369-.384a.5.5 0 0 0 .148.691c.413.267.789.589 1.116.955a5.478 5.478 0 0 1 1.4 3.666c0 .297-.075.694-.192 1.124-.115.421-.261.841-.386 1.17a2.678 2.678 0 0 0-.104 1.614l.451 1.758-1.427-.576a2.675 2.675 0 0 0-1.855-.036c-.639.216-1.46.446-1.987.446a5.5 5.5 0 0 1-5.5-5.5.5.5 0 0 0-1 0 6.5 6.5 0 0 0 6.5 6.5c.712 0 1.68-.287 2.306-.498.391-.132.804-.13 1.162.015l2.16.873a.6.6 0 0 0 .806-.706l-.647-2.525a1.678 1.678 0 0 1 .07-1.01c.131-.347.29-.799.416-1.262.124-.456.227-.956.227-1.387a6.478 6.478 0 0 0-1.655-4.333 6.53 6.53 0 0 0-1.318-1.128.5.5 0 0 0-.691.149ZM3.25 6.25a1 1 0 1 0-1-1.006v.008a1 1 0 0 0 1 .998Zm-2-1.01v.01a2 2 0 1 0 1.467-1.928 4.247 4.247 0 0 1 1.508-1.65.5.5 0 1 0-.533-.845A5.236 5.236 0 0 0 1.25 5.24Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-expand" fill="none" viewBox="0 0 16 16">
      <g class="es-line-expand">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M14 1.5H9.333a.5.5 0 1 0 0 1h3.46L8.645 6.646a.5.5 0 1 0 .707.707L13.5 3.206v3.46a.5.5 0 0 0 1 0V2a.5.5 0 0 0-.5-.5Zm-12 13h4.666a.5.5 0 1 0 0-1H3.207l4.147-4.146a.5.5 0 0 0-.707-.707L2.5 12.794v-3.46a.5.5 0 0 0-1 0V14a.5.5 0 0 0 .5.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-lock" fill="none" viewBox="0 0 16 16">
      <g class="es-line-lock">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M10 4v1H6V4a2 2 0 1 1 4 0ZM5 5V4a3 3 0 0 1 6 0v1h1a2.5 2.5 0 0 1 2.5 2.5v5A2.5 2.5 0 0 1 12 15H4a2.5 2.5 0 0 1-2.5-2.5v-5A2.5 2.5 0 0 1 4 5h1Zm6 1H4a1.5 1.5 0 0 0-1.5 1.5v5A1.5 1.5 0 0 0 4 14h8a1.5 1.5 0 0 0 1.5-1.5v-5A1.5 1.5 0 0 0 12 6h-1Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-copy" fill="none" viewBox="0 0 16 16">
      <g class="es-line-copy">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M6.55 2a1 1 0 0 0-1 1v6.9a1 1 0 0 0 1 1h5.25a1 1 0 0 0 1-1.001V6.126H9.675a1 1 0 0 1-1-1V2.5a.5.5 0 0 0-.5-.5H6.55Zm7.25 4.126v-.653a2 2 0 0 0-.671-1.494l-2.783-2.474A2 2 0 0 0 9.017 1H6.55a2 2 0 0 0-2 1.953.505.505 0 0 0-.05-.003H4a2 2 0 0 0-2 2v7.5a2 2 0 0 0 2 2h5.453a2.057 2.057 0 0 0 2.047-2.048V11.9h.3a2 2 0 0 0 2-2.001V6.126ZM10.5 11.9H6.55a2 2 0 0 1-2-2V3.948a.506.506 0 0 1-.05.002H4a1 1 0 0 0-1 1v7.5a1 1 0 0 0 1 1h5.453c.571 0 1.047-.476 1.047-1.047V11.9Zm1.964-7.174a1 1 0 0 1 .273.4H9.675V2.5c0-.094-.009-.185-.025-.274a1 1 0 0 1 .031.027l2.783 2.473Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-copy-light" fill="none" viewBox="0 0 16 16">
      <g class="es-line-copy-light">
        <path fill="var(--text-light)" fill-rule="evenodd" d="M6.55 2a1 1 0 0 0-1 1v6.9a1 1 0 0 0 1 1h5.25a1 1 0 0 0 1-1.001V6.126H9.675a1 1 0 0 1-1-1V2.5a.5.5 0 0 0-.5-.5H6.55Zm7.25 4.126v-.653a2 2 0 0 0-.671-1.494l-2.783-2.474A2 2 0 0 0 9.017 1H6.55a2 2 0 0 0-2 1.953.505.505 0 0 0-.05-.003H4a2 2 0 0 0-2 2v7.5a2 2 0 0 0 2 2h5.453a2.057 2.057 0 0 0 2.047-2.048V11.9h.3a2 2 0 0 0 2-2.001V6.126ZM10.5 11.9H6.55a2 2 0 0 1-2-2V3.948a.506.506 0 0 1-.05.002H4a1 1 0 0 0-1 1v7.5a1 1 0 0 0 1 1h5.453c.571 0 1.047-.476 1.047-1.047V11.9Zm1.964-7.174a1 1 0 0 1 .273.4H9.675V2.5c0-.094-.009-.185-.025-.274a1 1 0 0 1 .031.027l2.783 2.473Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-edit" fill="none" viewBox="0 0 16 16">
      <g class="es-line-edit">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.5 4.5a2 2 0 0 1 2-2h4a.5.5 0 0 0 0-1h-4a3 3 0 0 0-3 3v7a3 3 0 0 0 3 3h7a3 3 0 0 0 3-3v-4a.5.5 0 0 0-1 0v4a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2v-7Zm11.626-1.916a.5.5 0 0 0-.708-.707L6.686 8.61a.5.5 0 0 0 .707.707l6.733-6.733Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-pin" fill="none" viewBox="0 0 16 16">
      <g class="es-line-pin">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M6.177 1a2.5 2.5 0 0 0-2.488 2.254L3.191 8.29a1.5 1.5 0 0 1-.264.713l-.946 1.35a1 1 0 0 0 .82 1.574h4.732V14.5a.5.5 0 1 0 1 0v-2.573h4.669a1 1 0 0 0 .819-1.573l-.953-1.361a1.5 1.5 0 0 1-.262-.696l-.56-5.071A2.5 2.5 0 0 0 9.761 1H6.177ZM4.684 3.352A1.5 1.5 0 0 1 6.177 2H9.76a1.5 1.5 0 0 1 1.491 1.335l.56 5.072a2.5 2.5 0 0 0 .437 1.159l.953 1.361H2.8l.945-1.351a2.5 2.5 0 0 0 .44-1.188l.498-5.036Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-unpin" fill="none" viewBox="0 0 16 16">
      <g class="es-line-unpin" clip-path="url(#a)">
        <g class="Group 482428">
          <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M6.178 1A2.5 2.5 0 0 0 3.69 3.254L3.193 8.29a1.5 1.5 0 0 1-.264.713l-.946 1.35a1 1 0 0 0 .819 1.574h4.733V14.5a.5.5 0 1 0 1 0v-2.573h4.668a1 1 0 0 0 .82-1.573l-.954-1.361a1.5 1.5 0 0 1-.262-.696l-.56-5.071A2.5 2.5 0 0 0 9.764 1H6.178ZM4.686 3.352A1.5 1.5 0 0 1 6.178 2h3.585a1.5 1.5 0 0 1 1.49 1.335l.56 5.072a2.5 2.5 0 0 0 .437 1.159l.953 1.361H2.802l.946-1.351a2.5 2.5 0 0 0 .44-1.188l.498-5.036Z" class="Union" clip-rule="evenodd"/>
          <rect width="2" height="17" x="1.793" y="1" fill="var(--icon-stroke)" stroke="#fff" class="Rectangle 18237" rx="1" transform="rotate(-45 1.793 1)"/>
        </g>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <path fill="#fff" d="M0 0h16v16H0z"/>
        </clipPath>
      </defs>
    </symbol>
    <symbol id="es-line-reply" fill="none" viewBox="0 0 16 16">
      <g class="es-line-reply">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="m1.75 7.76 5.094-4.458v1.899c0 .557.45.989.982 1.008 1.869.068 3.385.543 4.439 1.6.843.845 1.444 2.118 1.649 4.003a7.84 7.84 0 0 0-1.583-1.385c-1.094-.708-2.486-1.153-4.47-1.218a.989.989 0 0 0-1.017.992v2.015L1.75 7.76Zm6.094-5.01A.75.75 0 0 0 6.6 2.187l-5.508 4.82a1 1 0 0 0 0 1.505l5.508 4.82a.75.75 0 0 0 1.244-.565V10.21c1.832.062 3.035.47 3.944 1.057.922.597 1.582 1.404 2.305 2.355a.5.5 0 0 0 .898-.303c0-2.891-.713-4.908-2.018-6.216-1.302-1.305-3.109-1.82-5.11-1.892a.025.025 0 0 1-.017-.007.013.013 0 0 1-.002-.003v-2.45Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-divider" fill="none" viewBox="0 0 16 16">
      <g class="es-line-divider">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.5 8a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-chart" fill="none" viewBox="0 0 16 16">
      <g class="es-line-chart">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M13.98 8.503a6 6 0 0 1-11.96 0h2.224a1 1 0 0 0 .869-.504l1.126-1.972 2.408 4.816a.75.75 0 0 0 1.322.037l1.358-2.377h2.652Zm0-1h-2.653a1 1 0 0 0-.868.503L9.332 9.978 6.925 5.162a.75.75 0 0 0-1.322-.036L4.244 7.503H2.02a6 6 0 0 1 11.96 0ZM8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-tiles" fill="none" viewBox="0 0 16 16">
      <g class="es-line-tiles">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.5 3h3a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1h-3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Zm-2 1a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v1a2 2 0 0 1-2 2h-3a2 2 0 0 1-2-2V4Zm2 6h3a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1h-3a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1Zm-2 1a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v1a2 2 0 0 1-2 2h-3a2 2 0 0 1-2-2v-1Zm13-8h-3a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1Zm-3-1a2 2 0 0 0-2 2v1a2 2 0 0 0 2 2h3a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2h-3Zm0 8h3a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1h-3a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1Zm-2 1a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v1a2 2 0 0 1-2 2h-3a2 2 0 0 1-2-2v-1Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-mark-unread" fill="none" viewBox="0 0 16 16">
      <g class="es-line-mark unread">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 1.5c.283 0 .562.018.835.053-.223.29-.409.61-.547.954a5.5 5.5 0 1 0 5.205 5.205c.344-.138.665-.324.954-.547A6.5 6.5 0 1 1 8 1.5ZM13.5 4a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm1 0a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-move" fill="none" viewBox="0 0 16 16">
      <g class="es-line-move">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M10.354 3.146a.5.5 0 0 0-.708.708L12.793 7H6a4.5 4.5 0 0 0-4.5 4.5v.5a.5.5 0 0 0 1 0v-.5A3.5 3.5 0 0 1 6 8h6.793l-3.147 3.146a.5.5 0 0 0 .708.708l4-4a.5.5 0 0 0 0-.708l-4-4Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-star" fill="none" viewBox="0 0 16 16">
      <g class="es-line-star">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M9.548 5.1 7.983 2.5 6.42 5.1a2 2 0 0 1-1.262.917L2.2 6.702l1.99 2.291a2 2 0 0 1 .482 1.484L4.409 13.5l2.794-1.184a2 2 0 0 1 1.56 0l2.794 1.184-.262-3.023a2 2 0 0 1 .482-1.484l1.99-2.291-2.957-.685A2 2 0 0 1 9.548 5.1ZM8.84 1.984a1 1 0 0 0-1.713 0l-1.565 2.6a1 1 0 0 1-.631.459l-2.956.684a1 1 0 0 0-.53 1.63l1.99 2.291a1 1 0 0 1 .24.742l-.262 3.023A1 1 0 0 0 4.8 14.421l2.794-1.184a1 1 0 0 1 .78 0l2.794 1.184a1 1 0 0 0 1.387-1.008l-.263-3.023a1 1 0 0 1 .242-.742l1.989-2.29a1 1 0 0 0-.53-1.63l-2.956-.685a1 1 0 0 1-.631-.459l-1.565-2.6Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-link" fill="none" viewBox="0 0 16 16">
      <g class="es-line-link" filter="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M9.505 11.011h2.008a3.014 3.014 0 0 0 0-6.027H9.505v-1h2.008a4.014 4.014 0 0 1 0 8.027H9.505v-1ZM6.494 4.986H4.486a3.014 3.014 0 0 0 0 6.027h2.008v1H4.486a4.014 4.014 0 0 1 0-8.027h2.008v1Zm3.514 3.513h.5v-1H5.492v1h4.516Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <filter id="a" width="24" height="24" x="-4" y="-4" class="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_456_47565"/>
          <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_456_47565" result="shape"/>
        </filter>
      </defs>
    </symbol>
    <symbol id="es-line-bold" fill="none" viewBox="0 0 16 16">
      <g class="es-line-bold">
        <path fill="var(--icon-stroke)" d="M4.431 14H8.44c.853 0 1.585-.136 2.195-.408.61-.271 1.078-.66 1.405-1.164.333-.51.5-1.114.5-1.813V10.6c0-.521-.114-.987-.341-1.397a2.772 2.772 0 0 0-.948-1.015 3.182 3.182 0 0 0-1.423-.474v-.141c.4-.067.757-.225 1.073-.474.316-.25.569-.555.757-.915.189-.366.283-.76.283-1.18v-.018c0-.61-.144-1.136-.433-1.58-.288-.449-.704-.795-1.247-1.04C9.723 2.123 9.08 2 8.33 2h-3.9v1.522h3.46c.693 0 1.23.158 1.613.474.383.31.574.748.574 1.314v.016c0 .583-.21 1.026-.632 1.331-.421.3-1.045.449-1.871.449H4.43v1.422h3.52c.588 0 1.078.075 1.472.225.4.144.699.36.898.648.2.289.3.649.3 1.081v.017c0 .643-.217 1.134-.65 1.472-.432.338-1.07.507-1.912.507H4.431V14ZM3.5 14h1.863V2H3.5v12Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-line-italic" fill="none" viewBox="0 0 16 16">
      <g class="es-line-italic">
        <path fill="var(--icon-stroke)" d="m3.25 14 .19-1.135h3.008l1.633-9.73H5.072L5.262 2h7.426L12.5 3.135H9.491l-1.634 9.73h3.009L10.676 14H3.25Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-line-strike-through" fill="none" viewBox="0 0 16 16">
      <g class="es-line-strike through">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M11.921 4.522a4.18 4.18 0 0 0-3.93-2.521c-.968.015-1.92.298-2.643.817-.73.524-1.245 1.305-1.245 2.28 0 .937.36 1.615.938 2.102H2a.5.5 0 1 0 0 1h5.282a.51.51 0 0 0 .022 0H14a.5.5 0 0 0 0-1H7.363c-.686-.198-1.25-.419-1.644-.737-.376-.305-.616-.71-.616-1.364 0-.587.3-1.09.827-1.468.534-.383 1.28-.618 2.078-.63a3.18 3.18 0 0 1 3 1.928.5.5 0 0 0 .913-.407Zm-.34 4.448a.5.5 0 0 0-.753.658c.247.282.409.657.409 1.211 0 .603-.323 1.16-.895 1.594-.574.436-1.367.72-2.197.76-.736.037-1.439-.103-2.025-.445-.58-.339-1.077-.892-1.387-1.74a.5.5 0 1 0-.94.343c.386 1.055 1.03 1.798 1.823 2.26.786.46 1.689.625 2.578.581 1.013-.05 2.004-.394 2.753-.963.75-.57 1.29-1.396 1.29-2.39 0-.775-.237-1.39-.656-1.87Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-underline" fill="none" viewBox="0 0 16 16">
      <g class="es-line-underline">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M5 2a.5.5 0 0 0-1 0v5.5a4 4 0 1 0 8 0V2a.5.5 0 0 0-1 0v5.5a3 3 0 0 1-6 0V2ZM2 13.5a.5.5 0 0 0 0 1h12a.5.5 0 0 0 0-1H2Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-numbered-list" fill="none" viewBox="0 0 16 16">
      <g class="es-line-numbered List">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.136 5.801V2.834h-.018l-.956.646v-.837l.972-.662h.969v3.82h-.967ZM5.417 3.42a.5.5 0 0 1 .5-.5H14a.5.5 0 1 1 0 1H5.917a.5.5 0 0 1-.5-.5Zm0 4.477a.5.5 0 0 1 .5-.5H14a.5.5 0 1 1 0 1H5.917a.5.5 0 0 1-.5-.5Zm.5 3.976a.5.5 0 1 0 0 1H14a.5.5 0 1 0 0-1H5.917ZM1.423 8.761a1.145 1.145 0 0 0-.407.913v.018h.883v-.018c0-.156.053-.284.158-.384a.564.564 0 0 1 .405-.153.55.55 0 0 1 .376.126.389.389 0 0 1 .145.308.64.64 0 0 1-.097.337 2.034 2.034 0 0 1-.334.386L1.07 11.657v.644h2.893v-.742H2.325v-.018l.908-.836.192-.187c.049-.051.108-.119.176-.203.07-.086.125-.165.166-.236.04-.074.075-.158.105-.253a.967.967 0 0 0 .045-.287.995.995 0 0 0-.392-.815c-.26-.209-.603-.313-1.031-.313-.442 0-.799.117-1.07.35Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-bullet-list" fill="none" viewBox="0 0 16 16">
      <g class="es-line-bullet list">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2 2.5a.5.5 0 0 0 0 1h1a.5.5 0 0 0 0-1H2Zm3.333 0a.5.5 0 0 0 0 1H14a.5.5 0 0 0 0-1H5.333Zm0 4.8a.5.5 0 0 0 0 1H14a.5.5 0 0 0 0-1H5.333Zm-.5 5.3a.5.5 0 0 1 .5-.5H14a.5.5 0 0 1 0 1H5.333a.5.5 0 0 1-.5-.5ZM1.5 7.8a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1H2a.5.5 0 0 1-.5-.5Zm.5 4.3a.5.5 0 0 0 0 1h1a.5.5 0 0 0 0-1H2Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-align" fill="none" viewBox="0 0 16 16">
      <g class="es-line-align">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.5 3.2a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5Zm0 4.8a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5Zm.5 4.3a.5.5 0 0 0 0 1h6a.5.5 0 0 0 0-1H3Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-divider" fill="none" viewBox="0 0 16 16">
      <g class="es-line-divider">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M1.5 7a.5.5 0 0 0-1 0v2a.5.5 0 0 0 1 0V7ZM15 6.5a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0V7a.5.5 0 0 1 .5-.5ZM2.5 8a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-quote" fill="none" viewBox="0 0 16 16">
      <g class="es-line-quote">
        <path fill="#000" fill-rule="evenodd" d="M2.01 8.08c.589.725 1.845.907 2.583.22.467-.433.554-.98.373-1.525-.114-.348-.392-.652-.85-.816-.358-.128-.755-.126-1.222-.124h-.175c.127-1.328 1.029-2.12 2.235-2.705l-.391-.63a5.877 5.877 0 0 0-2.175 1.64C1.175 5.596 1.314 7.222 2.01 8.08Zm4.373 0c.588.725 1.845.907 2.583.22.466-.433.554-.98.373-1.525-.114-.348-.392-.652-.85-.816-.358-.128-.755-.126-1.222-.124H7.09c.128-1.328 1.03-2.12 2.235-2.705l-.39-.63A5.877 5.877 0 0 0 6.76 4.14c-1.213 1.455-1.074 3.081-.378 3.94Zm4.896-5.126a.5.5 0 0 0 0 1h3.202a.5.5 0 0 0 0-1h-3.202Zm0 4.649a.5.5 0 0 0 0 1h3.202a.5.5 0 0 0 0-1h-3.202ZM1.52 12.75a.5.5 0 0 1 .5-.5h12.46a.5.5 0 1 1 0 1H2.02a.5.5 0 0 1-.5-.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-decrease-indent" fill="none" viewBox="0 0 16 16">
      <g class="es-line-decrease indent">
        <g class="Group 300">
          <g class="Group">
            <path stroke="#171B1F" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M7.213 7.934H14M2.776 2.5h11.09M2.776 13.368h11.09" class="Vector"/>
          </g>
          <path fill="#171B1F" d="M5.327 6.016v3.837a.4.4 0 0 1-.656.307L2.37 8.241a.4.4 0 0 1 0-.614l2.3-1.919a.4.4 0 0 1 .656.308Z" class="Vector 85"/>
        </g>
      </g>
    </symbol>
    <symbol id="es-line-increase-indent" fill="none" viewBox="0 0 16 16">
      <g class="es-line-increase indent">
        <g class="Group 300">
          <g class="Group">
            <path stroke="#171B1F" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M7.213 7.934H14M2.776 2.5h11.09M2.776 13.368h11.09" class="Vector"/>
          </g>
          <path fill="#171B1F" d="M2 9.853V6.016a.4.4 0 0 1 .656-.308l2.302 1.919a.4.4 0 0 1 0 .615L2.656 10.16A.4.4 0 0 1 2 9.853Z" class="Vector 85"/>
        </g>
      </g>
    </symbol>
    <symbol id="es-line-sort" fill="none" viewBox="0 0 16 16">
      <g class="es-line-sort">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M5.5 3.5a.5.5 0 0 0-1 0v7.793L2.878 9.672a.5.5 0 1 0-.707.707l2.475 2.475a.5.5 0 0 0 .35.146h.007c.127 0 .253-.05.35-.146l2.475-2.475a.5.5 0 0 0-.707-.707L5.5 11.292V3.5Zm5 9V4.707L8.878 6.328a.5.5 0 0 1-.707-.707l2.475-2.475a.499.499 0 0 1 .707 0l2.475 2.475a.5.5 0 1 1-.707.707L11.5 4.708V12.5a.5.5 0 0 1-1 0Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-web" fill="none" viewBox="0 0 16 16">
      <g class="es-line-web">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.12 8c.573.705 1.402 1.204 2.377 1.54.173.06.35.114.53.163A15.195 15.195 0 0 1 4.9 7.99c.012-.549.05-1.12.127-1.693-.18.05-.357.104-.53.163-.975.336-1.804.835-2.377 1.54Zm3.952-1.928C5.966 6.712 5.914 7.367 5.9 8c.014.633.066 1.288.172 1.928.64.105 1.295.158 1.928.172a13.79 13.79 0 0 0 1.928-.172c.105-.64.158-1.295.172-1.928a13.79 13.79 0 0 0-.172-1.928A13.786 13.786 0 0 0 8 5.9c-.633.014-1.288.066-1.928.172Zm3.63-1.045A15.195 15.195 0 0 0 7.99 4.9c-.549.012-1.12.05-1.693.127.05-.18.104-.357.163-.53.336-.975.835-1.804 1.54-2.377.705.573 1.204 1.402 1.54 2.377.06.173.114.35.163.53Zm1.27 1.27A15.185 15.185 0 0 1 11.1 8.01c-.011.549-.05 1.12-.127 1.693.18-.05.357-.104.53-.163.975-.336 1.804-.835 2.377-1.54-.573-.705-1.402-1.204-2.377-1.54a8.49 8.49 0 0 0-.53-.163Zm2.855 3.14c-.593.46-1.28.8-1.998 1.048a9.97 9.97 0 0 1-1.048.296c-.08.356-.178.707-.296 1.048-.247.719-.587 1.405-1.047 1.998a6.01 6.01 0 0 0 4.389-4.39ZM8 13.88c.705-.573 1.204-1.402 1.54-2.377.06-.173.114-.35.163-.53a15.185 15.185 0 0 1-1.713.127c-.549-.011-1.12-.05-1.693-.127.05.18.104.357.163.53.336.975.835 1.804 1.54 2.377Zm-2.78-3.1a9.967 9.967 0 0 1-1.05-.295c-.718-.247-1.404-.587-1.997-1.047a6.01 6.01 0 0 0 4.39 4.389c-.46-.593-.8-1.28-1.048-1.998a9.96 9.96 0 0 1-.296-1.048ZM2.172 6.563c.593-.46 1.28-.8 1.998-1.047a9.962 9.962 0 0 1 1.048-.296c.08-.356.178-.707.296-1.048.247-.719.587-1.405 1.047-1.998a6.01 6.01 0 0 0-4.389 4.39Zm7.265-4.389c.46.593.8 1.28 1.047 1.998.118.341.215.692.296 1.048.356.08.707.178 1.048.296.719.247 1.405.587 1.998 1.047a6.01 6.01 0 0 0-4.39-4.389ZM1 8a7 7 0 1 1 14 0A7 7 0 0 1 1 8Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-ticket" fill="none" viewBox="0 0 16 16">
      <g class="es-line-ticket">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M1.998 4.123a.5.5 0 0 1 .5-.5h11.004a.5.5 0 0 1 .5.5v1.412a2.669 2.669 0 0 0 0 4.998v1.344a.5.5 0 0 1-.5.5H2.498a.5.5 0 0 1-.5-.5v-1.344a2.669 2.669 0 0 0 0-4.998V4.123Zm.5-1.5a1.5 1.5 0 0 0-1.5 1.5V5.91a.5.5 0 0 0 .4.49 1.668 1.668 0 0 1 0 3.268.5.5 0 0 0-.4.49v1.719a1.5 1.5 0 0 0 1.5 1.5h11.004a1.5 1.5 0 0 0 1.5-1.5v-1.719a.5.5 0 0 0-.4-.49 1.669 1.669 0 0 1 0-3.268.5.5 0 0 0 .4-.49V4.123a1.5 1.5 0 0 0-1.5-1.5H2.498ZM5.41 6.606a.5.5 0 0 1 .5-.5h4.18a.5.5 0 1 1 0 1H5.91a.5.5 0 0 1-.5-.5Zm0 2.787a.5.5 0 0 1 .5-.5h4.18a.5.5 0 1 1 0 1H5.91a.5.5 0 0 1-.5-.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-ticket-no-" fill="none" viewBox="0 0 16 16">
      <g class="es-line-ticket no.">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M6.498 2.055a.5.5 0 0 0-.994-.11l-.32 2.889H2a.5.5 0 0 0 0 1h3.072l-.481 4.335H2a.5.5 0 0 0 0 1h2.48l-.31 2.779a.5.5 0 0 0 .995.11l.32-2.89h4.33l-.31 2.78a.5.5 0 0 0 .995.11l.32-2.89h3.183a.5.5 0 1 0 0-1h-3.071l.235-2.111a.5.5 0 0 0-.994-.11l-.247 2.222h-4.33l.483-4.335h7.923a.5.5 0 1 0 0-1h-2.43l.261-2.787a.5.5 0 0 0-.996-.093l-.269 2.88H6.19l.308-2.779Z" class="Vector" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-reply" fill="none" viewBox="0 0 16 16">
      <g class="es-line-reply">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M5.646 3.146a.5.5 0 1 1 .708.708L3.207 7H10a4.5 4.5 0 0 1 4.5 4.5v.5a.5.5 0 0 1-1 0v-.5A3.5 3.5 0 0 0 10 8H3.207l3.147 3.146a.5.5 0 0 1-.708.708l-4-4a.5.5 0 0 1 0-.708l4-4Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-activity" fill="none" viewBox="0 0 16 16">
      <g class="es-line-activity" clip-path="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M4 .75a.5.5 0 0 1 .5.5v1h1a.5.5 0 0 1 0 1h-1v1a.5.5 0 0 1-1 0v-1h-1a.5.5 0 0 1 0-1h1v-1a.5.5 0 0 1 .5-.5Zm4.75 1.587c.257-.697 1.243-.697 1.5 0l.86 2.325a2.5 2.5 0 0 0 1.478 1.477l2.325.86c.697.258.697 1.244 0 1.501l-2.325.86a2.5 2.5 0 0 0-1.477 1.478l-.86 2.325c-.258.697-1.244.697-1.501 0l-.86-2.325A2.5 2.5 0 0 0 6.411 9.36L4.087 8.5c-.697-.258-.697-1.244 0-1.501l2.325-.86A2.5 2.5 0 0 0 7.89 4.661l.86-2.325Zm.75.854-.673 1.818A3.5 3.5 0 0 1 6.76 7.077l-1.818.673 1.818.673a3.5 3.5 0 0 1 2.068 2.068l.673 1.818.673-1.818a3.5 3.5 0 0 1 2.068-2.068l1.818-.673-1.818-.673a3.5 3.5 0 0 1-2.068-2.068L9.5 3.191Zm-6 7.559a.5.5 0 0 0-1 0v1.5H1a.5.5 0 0 0 0 1h1.5v1.5a.5.5 0 0 0 1 0v-1.5H5a.5.5 0 0 0 0-1H3.5v-1.5Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <path fill="#fff" d="M0 0h16v16H0z"/>
        </clipPath>
      </defs>
    </symbol>
    <symbol id="es-line-notifications" fill="none" viewBox="0 0 16 16">
      <g class="es-line-notifications">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7.004 2.4a1 1 0 0 1 1.99 0l.033.33.316.099a4.501 4.501 0 0 1 3.156 4.296V11.5h.5a.5.5 0 1 1 0 1h-10a.5.5 0 0 1 0-1h.5V7.125a4.5 4.5 0 0 1 3.156-4.296l.316-.099.033-.33ZM5.549 13.5h-2.55a1.5 1.5 0 0 1-.5-2.915v-3.46a5.5 5.5 0 0 1 3.57-5.152 2 2 0 0 1 3.86 0 5.501 5.501 0 0 1 3.57 5.152v3.46a1.5 1.5 0 0 1-.5 2.915h-2.55a2.5 2.5 0 0 1-4.9 0Zm1.035 0a1.5 1.5 0 0 0 2.83 0h-2.83Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <!-- TODO replace with espresso icon when new icon is available -->
    <symbol id="es-line-notifications-unseen" fill="none" viewBox="0 0 16 16">
      <g class="es-line-notifications-unseen">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M8 1.5C7.4814 1.5 7.05449 1.89515 7.00486 2.40045L6.97248 2.73008L6.65633 2.82883C6.42002 2.90265 6.19204 2.99545 5.97416 3.10549C4.5053 3.84736 3.49982 5.3692 3.49982 7.125V11.5H2.99982C2.72384 11.5 2.5 11.7238 2.5 11.9999C2.5 12.2761 2.72391 12.5 3 12.5H12.9998C13.276 12.5 13.4998 12.2761 13.4998 12C13.4998 11.7239 13.276 11.5 12.9998 11.5H12.4998V7.125C12.4998 7.06944 12.4988 7.01412 12.4968 6.95905C12.8476 6.90058 13.1778 6.78124 13.4762 6.61225C13.4918 6.78107 13.4998 6.9521 13.4998 7.125V10.5854C14.0824 10.7913 14.4998 11.3469 14.4998 12C14.4998 12.8284 13.8282 13.5 12.9998 13.5H10.45C10.2184 14.6411 9.20948 15.5 8 15.5C6.79052 15.5 5.78164 14.6411 5.55001 13.5H3C2.17152 13.5 1.5 12.8283 1.5 11.9999C1.5 11.3469 1.9173 10.7913 2.49982 10.5854V7.125C2.49982 4.97742 3.73079 3.11823 5.52334 2.21288C5.70033 2.12349 5.88284 2.04336 6.07022 1.97315C6.30157 1.12403 7.07767 0.5 8 0.5C8.88147 0.5 9.62937 1.06996 9.89585 1.86165C9.62482 2.12838 9.40425 2.44624 9.2498 2.79961L9.0275 2.73016L8.99515 2.40053C8.94555 1.89519 8.51862 1.5 8 1.5ZM8 14.5C7.34689 14.5 6.79127 14.0826 6.58535 13.5H9.41465C9.20873 14.0826 8.65311 14.5 8 14.5Z"/>
        <path fill="var(--green-700)" d="M14 4C14 5.10457 13.1046 6 12 6C10.8954 6 10 5.10457 10 4C10 2.89543 10.8954 2 12 2C13.1046 2 14 2.89543 14 4Z" fill="#278F5E"/>
      </g>
    </symbol>
    <symbol id="es-line-reports" fill="none" viewBox="0 0 16 16">
      <g class="es-line-reports">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M6 3.5A1.5 1.5 0 0 1 7.5 2h1A1.5 1.5 0 0 1 10 3.5v10a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-10ZM7.5 3a.5.5 0 0 0-.5.5V13h2V3.5a.5.5 0 0 0-.5-.5h-1ZM11 6.5A1.5 1.5 0 0 1 12.5 5h1A1.5 1.5 0 0 1 15 6.5v7a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-7Zm1.5-.5a.5.5 0 0 0-.5.5V13h2V6.5a.5.5 0 0 0-.5-.5h-1Zm-10 2A1.5 1.5 0 0 0 1 9.5v4a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-4A1.5 1.5 0 0 0 3.5 8h-1ZM2 9.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5V13H2V9.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-book" fill="none" viewBox="0 0 16 16">
      <g class="es-line-book">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M1.736 3.863a.5.5 0 0 1 .5-.5h2.397l.057.002a6.6 6.6 0 0 1 1.016.129c.575.12 1.244.341 1.794.74v8.976a6.25 6.25 0 0 0-1.591-.55 7.597 7.597 0 0 0-1.258-.15h-.032v.5-.5H1.735V3.862Zm6.589 10.654c.579-.486 1.331-.745 1.97-.877a6.604 6.604 0 0 1 1.016-.129l.057-.002h3.396a.5.5 0 0 0 .5-.5V3.863a1.5 1.5 0 0 0-1.5-1.5h-2.382v.5-.5h-.032a6.076 6.076 0 0 0-.35.019 7.6 7.6 0 0 0-.908.133c-.619.128-1.4.373-2.092.848-.69-.475-1.473-.72-2.091-.848a7.6 7.6 0 0 0-1.258-.151h-.032v.5-.5H2.235a1.5 1.5 0 0 0-1.5 1.5v9.145a.5.5 0 0 0 .5.5h3.397l.057.002a6.602 6.602 0 0 1 1.016.129c.638.132 1.39.39 1.97.877a.498.498 0 0 0 .649 0ZM8.5 13.21V4.234c.55-.399 1.22-.62 1.795-.74a6.602 6.602 0 0 1 1.073-.13h2.396a.5.5 0 0 1 .5.5v8.645h-2.882v.5-.5h-.032a6.145 6.145 0 0 0-.35.019 7.596 7.596 0 0 0-.908.133 6.25 6.25 0 0 0-1.592.55Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-dashboard" fill="none" viewBox="0 0 16 16">
      <g class="es-line-dashboard">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3 1a1.5 1.5 0 0 0-1.5 1.5v2A1.5 1.5 0 0 0 3 6h3a1.5 1.5 0 0 0 1.5-1.5v-2A1.5 1.5 0 0 0 6 1H3Zm-.5 1.5A.5.5 0 0 1 3 2h3a.5.5 0 0 1 .5.5v2A.5.5 0 0 1 6 5H3a.5.5 0 0 1-.5-.5v-2ZM10 10a1.5 1.5 0 0 0-1.5 1.5v2A1.5 1.5 0 0 0 10 15h3a1.5 1.5 0 0 0 1.5-1.5v-2A1.5 1.5 0 0 0 13 10h-3Zm-.5 1.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-2Zm-1-9A1.5 1.5 0 0 1 10 1h3a1.5 1.5 0 0 1 1.5 1.5v5A1.5 1.5 0 0 1 13 9h-3a1.5 1.5 0 0 1-1.5-1.5v-5ZM10 2a.5.5 0 0 0-.5.5v5a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-5A.5.5 0 0 0 13 2h-3ZM3 7a1.5 1.5 0 0 0-1.5 1.5v5A1.5 1.5 0 0 0 3 15h3a1.5 1.5 0 0 0 1.5-1.5v-5A1.5 1.5 0 0 0 6 7H3Zm-.5 1.5A.5.5 0 0 1 3 8h3a.5.5 0 0 1 .5.5v5a.5.5 0 0 1-.5.5H3a.5.5 0 0 1-.5-.5v-5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-log-out" fill="none" viewBox="0 0 16 16">
      <g class="es-line-log-out">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M1.75 3a1.5 1.5 0 0 1 1.5-1.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 0-.5.5v10a.5.5 0 0 0 .5.5h3a.5.5 0 0 1 0 1h-3a1.5 1.5 0 0 1-1.5-1.5V3Zm3.5 5a.5.5 0 0 1 .5-.5h6.793l-1.975-1.975a.5.5 0 0 1 .707-.707l2.829 2.828a.5.5 0 0 1 .146.351v.006c0 .127-.05.254-.146.35l-2.829 2.829a.5.5 0 0 1-.707-.707L12.543 8.5H5.75a.5.5 0 0 1-.5-.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-sidebar-collapse" fill="none" viewBox="0 0 16 16">
      <g class="es-line-sidebar-collapse">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7.646 12.056a.5.5 0 1 0 .707.707l4.41-4.41a.5.5 0 0 0 0-.707l-4.41-4.41a.5.5 0 1 0-.707.708L11.703 8l-4.057 4.056Zm-4.41 0a.5.5 0 0 0 .708.707l4.41-4.41a.5.5 0 0 0 0-.707l-4.41-4.41a.5.5 0 0 0-.707.708L7.293 8l-4.056 4.056Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-compact" fill="none" viewBox="0 0 16 16">
      <g class="es-line-compact">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M9.5 5.793v-3.46a.5.5 0 0 0-1 0V7a.5.5 0 0 0 .5.5h4.666a.5.5 0 1 0 0-1h-3.459l4.147-4.147a.5.5 0 0 0-.708-.707L9.5 5.793ZM2.332 8.5a.5.5 0 1 0 0 1h3.46l-4.147 4.146a.5.5 0 1 0 .707.708L6.5 10.207v3.46a.5.5 0 1 0 1 0V9a.5.5 0 0 0-.5-.5H2.334Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-sidebar-expand" fill="none" viewBox="0 0 16 16">
      <g class="es-line-sidebar-expand">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8.354 12.056a.5.5 0 1 1-.707.707l-4.41-4.41a.5.5 0 0 1 0-.707l4.41-4.41a.5.5 0 1 1 .707.708L4.298 8l4.056 4.056Zm4.41 0a.5.5 0 0 1-.708.707l-4.41-4.41a.5.5 0 0 1 0-.707l4.41-4.41a.5.5 0 1 1 .707.708L8.707 8l4.056 4.056Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-reply-all" fill="none" viewBox="0 0 16 16">
      <g class="es-line-reply-all">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M5.854 3.146a.5.5 0 0 0-.708 0l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.207 7.5l3.647-3.646a.5.5 0 0 0 0-.708ZM10 8a3.5 3.5 0 0 1 3.5 3.5v.5a.5.5 0 0 0 1 0v-.5A4.5 4.5 0 0 0 10 7H6.673l3.146-3.146a.5.5 0 0 0-.707-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .707-.708L6.673 8H10Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-reload" width="17" height="16" fill="none" viewBox="0 0 17 16">
      <g class="es-line-reload" clip-path="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M9.743 2.189a6 6 0 0 0-6.558 2.596.5.5 0 0 0 .844.535 5 5 0 0 1 9.12 1.683l-1.187-.686a.5.5 0 0 0-.5.866l2.165 1.25a.5.5 0 0 0 .683-.183l1.25-2.165a.5.5 0 0 0-.866-.5l-.603 1.044a6 6 0 0 0-4.348-4.44ZM3.356 9.024l1.189.687a.5.5 0 0 0 .5-.866L2.88 7.595a.5.5 0 0 0-.683.183L.947 9.943a.5.5 0 1 0 .866.5l.603-1.044a6 6 0 0 0 10.9 1.816.5.5 0 0 0-.844-.536 5 5 0 0 1-9.116-1.655Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <path fill="#fff" d="M.25 0h16v16h-16z"/>
        </clipPath>
      </defs>
    </symbol>
    <symbol id="es-line-customer" fill="none" viewBox="0 0 16 16">
      <g class="es-line-customer">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M1 8a7 7 0 1 1 14 0A7 7 0 0 1 1 8Zm7-6a6 6 0 0 0-4.55 9.912C4.182 10.85 5.63 9.52 7.996 9.5H8c1.893 0 3.562.956 4.55 2.41A6 6 0 0 0 8 2Zm3.823 10.625a4.497 4.497 0 0 0-3.82-2.125c-2.055.018-3.266 1.237-3.826 2.124A5.975 5.975 0 0 0 8 14a5.976 5.976 0 0 0 3.823-1.375ZM8 5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3ZM5.5 6.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-add-circle" fill="none" viewBox="0 0 16 16">
      <g class="es-line-add-circle">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12Zm0 1A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM8 3.964a.5.5 0 0 1 .5.5V7.5h3.036a.5.5 0 1 1 0 1H8.5v3.036a.5.5 0 1 1-1 0V8.5H4.464a.5.5 0 1 1 0-1H7.5V4.464a.5.5 0 0 1 .5-.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-time" fill="none" viewBox="0 0 16 16">
      <g class="es-line-time">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M14 8A6 6 0 1 1 2 8a6 6 0 0 1 12 0Zm1 0A7 7 0 1 1 1 8a7 7 0 0 1 14 0Zm-4.65 1.643a.5.5 0 1 1-.7.714l-2-1.96-.15-.147V4a.5.5 0 1 1 1 0v3.83l1.85 1.813Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-overdue" fill="none" viewBox="0 0 16 16">
      <g class="es-line-overdue">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M5.5 1a.5.5 0 1 0 0 1h5a.5.5 0 0 0 0-1h-5ZM8 14A5 5 0 1 0 8 4a5 5 0 0 0 0 10Zm0 1A6 6 0 1 0 8 3a6 6 0 0 0 0 12Zm0-5.5a.5.5 0 0 0 .5-.5V5.98a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .5.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-details" fill="none" viewBox="0 0 16 16">
      <g class="es-line-details">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M12.5 2.5h-9a1 1 0 0 0-1 1v9a1 1 0 0 0 1 1h9a1 1 0 0 0 1-1v-9a1 1 0 0 0-1-1Zm-9-1a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2-2v-9a2 2 0 0 0-2-2h-9Zm2 6.5a.625.625 0 1 1-1.25 0A.625.625 0 0 1 5.5 8Zm-.625-2.375a.625.625 0 1 0 0-1.25.625.625 0 0 0 0 1.25ZM5.5 11a.625.625 0 1 1-1.25 0 .625.625 0 0 1 1.25 0Zm1.25-6.5a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1h-5ZM6.25 8a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm.5 2.5a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1h-5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-dot-vertical" fill="none" viewBox="0 0 16 16">
      <g class="es-line-dot-vertical">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7.75 4.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3ZM6.25 8a1.5 1.5 0 1 0 3 0 1.5 1.5 0 0 0-3 0Zm0 5a1.5 1.5 0 1 0 3 0 1.5 1.5 0 0 0-3 0Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-double-check" fill="none" viewBox="0 0 16 16">
      <g class="es-line-double-check">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M15.144 4.567a.5.5 0 1 0-.753-.658l-6.203 7.102-.4-.44a.5.5 0 1 0-.74.672l.778.855a.5.5 0 0 0 .746-.007l6.572-7.524Zm-3.85 0a.5.5 0 0 0-.753-.658l-6.203 7.102-2.736-3.006a.5.5 0 1 0-.74.673l3.114 3.42a.5.5 0 0 0 .746-.007l6.572-7.524Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-support" fill="none" viewBox="0 0 16 16">
      <g class="es-line-support" filter="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7.5 13.98v-2.01A4.002 4.002 0 0 1 4.031 8.5h-2.01A6.001 6.001 0 0 0 7.5 13.98Zm6.48-5.48a6.002 6.002 0 0 1-5.48 5.48v-2.01a4.002 4.002 0 0 0 3.47-3.47h2.01Zm0-1h-2.01A4.002 4.002 0 0 0 8.5 4.031v-2.01A6.001 6.001 0 0 1 13.98 7.5ZM7.5 2.02v2.011A4.002 4.002 0 0 0 4.031 7.5h-2.01A6.001 6.001 0 0 1 7.5 2.02ZM15 8A7 7 0 1 1 1 8a7 7 0 0 1 14 0Zm-4 0a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <filter id="a" width="24" height="24" x="-4" y="-4" class="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_785_70503"/>
          <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_785_70503" result="shape"/>
        </filter>
      </defs>
    </symbol>
    <symbol id="es-line-agent" fill="none" viewBox="0 0 16 16">
      <g class="es-line-agent" filter="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M9.625 4.49a1.625 1.625 0 1 1-3.25 0 1.625 1.625 0 0 1 3.25 0Zm1 0a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Zm-7.294 6.996a3.347 3.347 0 0 1 3.267-2.621h2.805a3.347 3.347 0 0 1 3.267 2.62l.093.418a1.424 1.424 0 0 1-1.39 1.732H4.628a1.423 1.423 0 0 1-1.39-1.732l.093-.417Zm3.267-3.621a4.347 4.347 0 0 0-4.243 3.404l-.093.417a2.424 2.424 0 0 0 2.366 2.95h6.745a2.424 2.424 0 0 0 2.366-2.95l-.093-.417a4.347 4.347 0 0 0-4.243-3.404H6.598Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <filter id="a" width="24" height="24" x="-4" y="-4" class="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_793_70501"/>
          <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_793_70501" result="shape"/>
        </filter>
      </defs>
    </symbol>
    <symbol id="es-line-agent-alt" fill="none" viewBox="0 0 16 16">
      <g class="es-line-agent-alt">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M9.625 4.49a1.625 1.625 0 1 1-3.25 0 1.625 1.625 0 0 1 3.25 0Zm1 0a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Zm-7.294 6.996a3.347 3.347 0 0 1 2.956-2.607l1.26 2.697.453.97.453-.97L9.714 8.88a3.347 3.347 0 0 1 2.956 2.607l.093.417a1.424 1.424 0 0 1-1.39 1.732H4.628a1.423 1.423 0 0 1-1.39-1.732l.093-.417Zm3.267-3.621a4.347 4.347 0 0 0-4.243 3.404l-.093.417a2.424 2.424 0 0 0 2.366 2.95h6.745a2.424 2.424 0 0 0 2.366-2.95l-.093-.417a4.347 4.347 0 0 0-4.243-3.404h-.319l-.134.288-.95 2.03-.949-2.03-.135-.288h-.318Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-manage" fill="none" viewBox="0 0 16 16">
      <g class="es-line-manage">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M4.814 1.5a.5.5 0 0 1 .5.5v2c0 .017 0 .033-.002.05a2.5 2.5 0 0 1 0 4.9.507.507 0 0 1 .002.05v5a.5.5 0 0 1-1 0V9a.42.42 0 0 1 .003-.05 2.5 2.5 0 0 1 0-4.9.507.507 0 0 1-.003-.05V2a.5.5 0 0 1 .5-.5Zm1.5 5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm6.371 3a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm-1.002 2.45a2.5 2.5 0 0 0 0-4.9.507.507 0 0 0 .002-.05V2a.5.5 0 1 0-1 0v5a.42.42 0 0 0 .003.05 2.5 2.5 0 0 0 0 4.9.506.506 0 0 0-.003.05v2a.5.5 0 0 0 1 0v-2c0-.017 0-.033-.002-.05Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-drag" fill="none" viewBox="0 0 16 16">
      <g class="es-line-drag">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M4 3a1.5 1.5 0 1 0 3 0 1.5 1.5 0 0 0-3 0Zm1.5 6.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3Zm0 5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3ZM9 3a1.5 1.5 0 1 0 3 0 1.5 1.5 0 0 0-3 0Zm1.5 6.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3Zm0 5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-tick" fill="none" viewBox="0 0 16 16">
      <g class="es-line-tick">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M13.897 3.464a.5.5 0 0 1-.19.681 16.167 16.167 0 0 0-6.964 7.792l-.232.548a.5.5 0 0 1-.833.139L3.63 10.336l-.001-.001-1.46-1.61a.5.5 0 1 1 .741-.67l1.46 1.61.003.002 1.525 1.703a17.167 17.167 0 0 1 7.318-8.096.5.5 0 0 1 .68.19Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-video" fill="none" viewBox="0 0 16 16">
      <g class="es-line-video">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M13 13.5H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v8.5a1 1 0 0 1-1 1Zm2-1a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v8.5ZM7.124 5.199a.75.75 0 0 0-1.18.614v4.484a.75.75 0 0 0 1.18.615l3.203-2.242a.75.75 0 0 0 0-1.229L7.124 5.199Zm-.18 4.618V6.294l2.517 1.761-2.517 1.762Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-question" fill="none" viewBox="0 0 16 16">
      <g class="es-line-question">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M14 8A6 6 0 1 1 2 8a6 6 0 0 1 12 0Zm1 0A7 7 0 1 1 1 8a7 7 0 0 1 14 0ZM7.38 9.725v.068h1.01v-.068c0-.274.03-.492.089-.655a1.06 1.06 0 0 1 .273-.424c.127-.118.291-.236.493-.357.316-.195.565-.431.747-.708.182-.28.274-.62.274-1.02 0-.385-.091-.721-.274-1.011a1.827 1.827 0 0 0-.757-.679c-.325-.163-.708-.244-1.147-.244-.394 0-.755.076-1.084.23a1.86 1.86 0 0 0-.791.668c-.199.293-.304.651-.317 1.075H6.97c.016-.251.08-.454.19-.61.11-.16.248-.277.41-.352a1.246 1.246 0 0 1 1.07.014c.169.085.3.204.395.357a.986.986 0 0 1 .147.542c0 .267-.072.496-.215.688a1.84 1.84 0 0 1-.537.474 4.13 4.13 0 0 0-.567.42c-.156.14-.276.33-.361.571-.081.238-.122.578-.122 1.02Zm0 2.143c.14.134.31.2.513.2a.705.705 0 1 0 0-1.41.706.706 0 0 0-.513.204.674.674 0 0 0-.205.503c0 .199.068.366.205.503Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-demand-video" fill="none" viewBox="0 0 16 16">
      <g class="es-line-demand video">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3 12h10a1 1 0 0 0 1-1V3a1 1 0 0 0-1-1H3a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1Zm10 1a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2H3a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h10ZM5.944 4.813a.75.75 0 0 1 1.18-.614l3.203 2.242a.75.75 0 0 1 0 1.229L7.124 9.912a.75.75 0 0 1-1.18-.614V4.813Zm1 .48v3.524l2.517-1.762-2.517-1.761ZM5 13.5a.5.5 0 0 0 0 1h6a.5.5 0 0 0 0-1H5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-certificates" fill="none" viewBox="0 0 16 16">
      <g class="es-line-certificates">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3 2a1.5 1.5 0 0 0-1.5 1.5v7.333a1.5 1.5 0 0 0 1.5 1.5h5a.5.5 0 0 1 0 1H3a2.5 2.5 0 0 1-2.5-2.5V3.5A2.5 2.5 0 0 1 3 1h8.6a2.5 2.5 0 0 1 2.5 2.5v.461a.5.5 0 0 1-1 0V3.5A1.5 1.5 0 0 0 11.6 2H3Zm8.3 8.582v2.309l1.143-.572.226-.113.225.115 1.14.582v-2.32a2.82 2.82 0 0 1-1.367.35 2.82 2.82 0 0 1-1.367-.35Zm3.734-.924v4.878l-.728-.371-1.641-.838-1.641.82-.724.362V9.658a2.833 2.833 0 1 1 4.733 0Zm-4.2-1.558a1.833 1.833 0 1 1 3.666 0 1.833 1.833 0 0 1-3.666 0ZM3.3 5.767a.5.5 0 0 1 .5-.5h4.666a.5.5 0 0 1 0 1H3.8a.5.5 0 0 1-.5-.5Zm.5 2.3a.5.5 0 0 0 0 1h2.8a.5.5 0 1 0 0-1H3.8Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-like" fill="none" viewBox="0 0 16 16">
      <g class="es-line-like">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M9.193 3.835a2.872 2.872 0 0 0-.357.442l-.84 1.275-.833-1.278a2.8 2.8 0 0 0-3.896-.801 2.867 2.867 0 0 0-.797 3.952l.001.001c.104.16.225.308.359.443l.001.001 5.17 5.212 5.17-5.21V7.87a2.87 2.87 0 0 0 0-4.036 2.796 2.796 0 0 0-3.978 0Zm4.69 4.74-5.179 5.217-.704.71-.704-.71L2.12 8.574a3.867 3.867 0 0 1 .59-5.933A3.8 3.8 0 0 1 8 3.727a3.873 3.873 0 0 1 .675-.778 3.796 3.796 0 0 1 5.206.183 3.87 3.87 0 0 1 0 5.442Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-progress" fill="none" viewBox="0 0 16 16">
      <g class="es-line-progress">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 13A5 5 0 1 0 8 3a5 5 0 0 0 0 10Zm0 2A7 7 0 1 0 8 1a7 7 0 0 0 0 14Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-progress25%" fill="none" viewBox="0 0 16 16">
      <g class="es-line-progress25%">
        <path fill="#E2E2E2" fill-rule="evenodd" d="M8 13A5 5 0 1 0 8 3a5 5 0 0 0 0 10Zm0 2A7 7 0 1 0 8 1a7 7 0 0 0 0 14Z" class="Union" clip-rule="evenodd"/>
        <path stroke="var(--icon-stroke)" stroke-width="2" d="M8 2a6 6 0 0 1 6 6" class="Vector"/>
      </g>
    </symbol>
    <symbol id="es-line-plans" fill="none" viewBox="0 0 16 16">
      <g class="es-line-plans">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M4.5 1.125a.5.5 0 0 1 .5.5v.75h6v-.75a.5.5 0 0 1 1 0v.762a2.5 2.5 0 0 1 2.25 2.488v7.462a2.5 2.5 0 0 1-2.47 2.5l-3.196.038H4.25a2.5 2.5 0 0 1-2.5-2.5v-7.5A2.5 2.5 0 0 1 4 2.387v-.762a.5.5 0 0 1 .5-.5Zm-1.75 3.75a1.5 1.5 0 0 1 1.5-1.5h7.5a1.5 1.5 0 0 1 1.5 1.5v7.462a1.5 1.5 0 0 1-1.482 1.5l-3.193.038H4.25a1.5 1.5 0 0 1-1.5-1.5v-7.5Zm2 3.25a.5.5 0 0 0 0 1h6.5a.5.5 0 0 0 0-1h-6.5Zm-.5-2.3a.5.5 0 0 1 .5-.5h6.5a.5.5 0 0 1 0 1h-6.5a.5.5 0 0 1-.5-.5Zm.5 5.1a.5.5 0 0 0 0 1h2.8a.5.5 0 1 0 0-1h-2.8Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-payments" fill="none" viewBox="0 0 16 16">
      <g class="es-line-payments">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2 8a6 6 0 1 0 12 0A6 6 0 0 0 2 8Zm6-7a7 7 0 1 0 0 14A7 7 0 0 0 8 1Zm-.481 2.262v.85A2.22 2.22 0 0 0 8 8.5a1.22 1.22 0 1 1-1.22 1.22h-1a2.22 2.22 0 0 0 1.739 2.168v.85h1v-.858A2.22 2.22 0 0 0 8 7.5a1.22 1.22 0 1 1 1.22-1.22h1a2.22 2.22 0 0 0-1.7-2.16v-.858h-1Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-folder" fill="none" viewBox="0 0 16 16">
      <g class="es-line-folder">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M10.023 2.5 8.432 3.686a1 1 0 0 1-.598.199H2V12.5a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1v-10h-3.977Zm-.531-.851A.75.75 0 0 1 9.94 1.5h4.41a.65.65 0 0 1 .65.65V12.5a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V3.535a.65.65 0 0 1 .65-.65h6.184l1.658-1.236Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-article" fill="none" viewBox="0 0 16 16">
      <g class="es-line-article">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.926 12.688a1.5 1.5 0 0 0 1.5 1.5h7.148a1.5 1.5 0 0 0 1.5-1.5V3.312a1.5 1.5 0 0 0-1.5-1.5H4.426a1.5 1.5 0 0 0-1.5 1.5v9.377Zm11.148-1.594V3.31a2.5 2.5 0 0 0-2.5-2.5H4.426a2.5 2.5 0 0 0-2.5 2.5v9.377a2.5 2.5 0 0 0 2.5 2.5h7.148a2.5 2.5 0 0 0 2.5-2.5v-1.594ZM10.75 4.42a.5.5 0 0 1 0 1h-5.5a.5.5 0 1 1 0-1h5.5Zm0 2.5a.5.5 0 0 1 0 1h-5.5a.5.5 0 1 1 0-1h5.5Zm.5 3a.5.5 0 0 0-.5-.5h-5.5a.5.5 0 1 0 0 1h5.5a.5.5 0 0 0 .5-.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-create-ticket" fill="none" viewBox="0 0 16 16">
      <g class="es-line-create ticket">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.498 3.623a.5.5 0 0 0-.5.5v1.412a2.668 2.668 0 0 1 0 4.998v1.344a.5.5 0 0 0 .5.5h11.004a.5.5 0 0 0 .5-.5v-1.344a2.669 2.669 0 0 1 0-4.998V4.123a.5.5 0 0 0-.5-.5H2.498Zm-1.5.5a1.5 1.5 0 0 1 1.5-1.5h11.004a1.5 1.5 0 0 1 1.5 1.5V5.91a.5.5 0 0 1-.4.49 1.669 1.669 0 0 0 0 3.268.5.5 0 0 1 .4.49v1.719a1.5 1.5 0 0 1-1.5 1.5H2.498a1.5 1.5 0 0 1-1.5-1.5v-1.719a.5.5 0 0 1 .4-.49 1.669 1.669 0 0 0 0-3.268.5.5 0 0 1-.4-.49V4.123ZM5.25 8a.5.5 0 0 1 .5-.5H7.5V5.75a.5.5 0 0 1 1 0V7.5h1.75a.5.5 0 0 1 0 1H8.5v1.75a.5.5 0 0 1-1 0V8.5H5.75a.5.5 0 0 1-.5-.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-cloud" fill="none" viewBox="0 0 16 16">
      <g class="es-line-cloud">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.355 7.1a5.188 5.188 0 0 1 10.34-.162c.786.002 1.603.351 2.223.92C15.545 8.434 16 9.265 16 10.25c0 .562-.13 1.38-.62 2.07-.51.72-1.37 1.242-2.693 1.242H3.313C1.812 13.563 0 12.466 0 10.25 0 8.513 1.22 7.475 2.355 7.1Zm.989.31a1.347 1.347 0 0 1 .004.074.5.5 0 0 1-.4.49C2.027 8.16 1 8.933 1 10.25c0 1.534 1.224 2.313 2.313 2.313h9.374c1.022 0 1.568-.385 1.878-.821.33-.465.435-1.054.435-1.492 0-.661-.303-1.237-.759-1.655-.461-.424-1.047-.658-1.553-.658-.128 0-.253.014-.375.038a.5.5 0 0 1-.595-.556V7.41a4.188 4.188 0 0 0-8.374 0Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-select-file" fill="none" viewBox="0 0 16 16">
      <g class="es-line-select file">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.5 1.5A2.5 2.5 0 0 0 1 4v5.534a2.5 2.5 0 0 0 2.5 2.5h2.7v1.6H4.1a.5.5 0 0 0 0 1h7.8a.5.5 0 1 0 0-1H9.8v-1.6h2.7a2.5 2.5 0 0 0 2.5-2.5V4a2.5 2.5 0 0 0-2.5-2.5h-9Zm5.806 9.534H3.5a1.5 1.5 0 0 1-1.5-1.5V4a1.5 1.5 0 0 1 1.5-1.5h9A1.5 1.5 0 0 1 14 4v5.534a1.5 1.5 0 0 1-1.5 1.5H9.307Zm-2.106 1h1.6v1.6H7.2v-1.6Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-quiz" fill="none" viewBox="0 0 16 16">
      <g class="es-line-quiz">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M12.5 2h-9a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h9a1 1 0 0 0 1-1V3a1 1 0 0 0-1-1Zm-9-1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h9a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2h-9Zm6 3a.5.5 0 0 0 0 1H12a.5.5 0 1 0 0-1H9.5Zm0 3a.5.5 0 0 0 0 1H12a.5.5 0 1 0 0-1H9.5ZM9 11.5a.5.5 0 0 1 .5-.5H12a.5.5 0 1 1 0 1H9.5a.5.5 0 0 1-.5-.5Zm-1.122-1.173a.5.5 0 1 0-.756-.654l-1.665 1.928-.585-.65a.5.5 0 0 0-.744.67l.965 1.07a.5.5 0 0 0 .75-.007l2.035-2.357ZM5 7V5h2v2H5Zm0-3a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1H5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-youtube" fill="none" viewBox="0 0 16 16">
      <g class="es-line-youtube">
        <g class="Group 482433">
          <path stroke="#000" d="m14.378 3.834.02.023.023.02c.136.118.26.373.35.678a3.67 3.67 0 0 1 .103.471l.004.027v.004l.001.007.004.038a22.467 22.467 0 0 1 .056.675c.03.439.061 1.02.061 1.598V8.5a24.638 24.638 0 0 1-.117 2.335l-.003.03-.004.021a4.798 4.798 0 0 1-.126.486c-.1.314-.227.585-.354.712-.347.347-.687.4-1.019.45-.066.01-.131.02-.197.033-1.04.09-2.327.136-3.363.16a98.983 98.983 0 0 1-1.68.023H8v.5-.5h-.17a238.696 238.696 0 0 1-1.94-.016c-1.127-.015-2.366-.046-2.942-.104a4.158 4.158 0 0 0-.329-.044c-.352-.038-.685-.073-.997-.42l-.02-.023-.023-.02c-.136-.118-.26-.373-.35-.678a3.67 3.67 0 0 1-.103-.471l-.004-.027v-.01l-.005-.04a20.772 20.772 0 0 1-.056-.704A24.95 24.95 0 0 1 1 8.563V7.437a24.65 24.65 0 0 1 .117-2.335l.003-.03.004-.02.024-.113a4.83 4.83 0 0 1 .102-.374c.1-.313.227-.585.354-.711.347-.348.687-.4 1.018-.45.064-.01.127-.02.19-.032a84.542 84.542 0 0 1 3.367-.106 147.554 147.554 0 0 1 1.684-.016H8v-.5.5h.137a120.859 120.859 0 0 1 1.684.016c1.045.015 2.348.046 3.4.108l.014.001h.015c.282 0 .716.001 1.128.46Z" class="Vector"/>
          <path stroke="#000" d="m6.75 6.326 3.26 1.705-3.26 1.654v-3.36Z" class="Vector"/>
        </g>
      </g>
    </symbol>
    <symbol id="es-line-embed" fill="none" viewBox="0 0 16 16">
      <g class="es-line-embed">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M9.62 4.036a.5.5 0 0 1 .278.65l-2.857 7.143a.5.5 0 0 1-.929-.372L8.97 4.314a.5.5 0 0 1 .65-.278Zm1.67 1.22a.5.5 0 0 1 .707 0l2.857 2.856a.5.5 0 0 1 0 .707l-2.857 2.858a.5.5 0 0 1-.707-.708l2.503-2.503-2.503-2.504a.5.5 0 0 1 0-.707Zm-6.58.706a.5.5 0 0 0-.706-.707L1.146 8.112a.5.5 0 0 0 0 .707l2.858 2.858a.5.5 0 0 0 .707-.708L2.207 8.466l2.504-2.504Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-download" fill="none" viewBox="0 0 16 16">
      <g class="es-line-download">
        <path stroke="var(--icon-stroke)" stroke-linecap="round" d="M8 11V5" class="Vector"/>
        <path stroke="var(--icon-stroke)" stroke-linecap="round" stroke-linejoin="round" d="M10.5 8.5 8 11 5.5 8.5" class="Vector"/>
        <circle cx="8" cy="8" r="6.5" stroke="#000" class="Ellipse 2906"/>
      </g>
    </symbol>
    <symbol id="es-line-like" fill="none" viewBox="0 0 16 16">
      <g class="es-line-like">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8.805 5.408a1 1 0 0 0 1 1h2.914a1 1 0 0 1 .926 1.378l-2.333 5.718a1 1 0 0 1-.926.622h-5a1 1 0 0 1-1-1v-5.52l3.118-5.733h.3a1 1 0 0 1 1 1v2.535Zm3.914 0H9.805V2.873a2 2 0 0 0-2-2h-.3a1 1 0 0 0-.879.523L3.508 7.129a1 1 0 0 0-.122.477v5.52a2 2 0 0 0 2 2h5a2 2 0 0 0 1.852-1.244l2.333-5.718a2 2 0 0 0-1.852-2.756ZM1.28 14.492a.5.5 0 1 0 1 0V8.007a.5.5 0 1 0-1 0v6.485Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-dislike" fill="none" viewBox="0 0 16 16">
      <g class="es-line-dislike">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7.195 10.591a1 1 0 0 0-1-1H3.281a1 1 0 0 1-.926-1.377l2.333-5.718a1 1 0 0 1 .926-.622h5a1 1 0 0 1 1 1v5.52l-3.118 5.732h-.3a1 1 0 0 1-1-1v-2.535Zm-3.914 0h2.914v2.535a2 2 0 0 0 2 2h.3a1 1 0 0 0 .879-.522l3.118-5.733a1 1 0 0 0 .122-.478v-5.52a2 2 0 0 0-2-2h-5a2 2 0 0 0-1.852 1.245L1.429 7.836a2 2 0 0 0 1.852 2.755Zm11.44-9.083a.5.5 0 1 0-1 0v6.484a.5.5 0 0 0 1 0V1.508Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-ticket-alt" fill="none" viewBox="0 0 16 16">
      <g class="es-line-ticket-alt">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M1.998 4.123a.5.5 0 0 1 .5-.5H5v.965a.5.5 0 0 0 1 0v-.965h7.502a.5.5 0 0 1 .5.5v1.412a2.669 2.669 0 0 0 0 4.998v1.344a.5.5 0 0 1-.5.5H6v-.965a.5.5 0 0 0-1 0v.965H2.498a.5.5 0 0 1-.5-.5v-1.344a2.669 2.669 0 0 0 0-4.998V4.123Zm.5-1.5a1.5 1.5 0 0 0-1.5 1.5V5.91a.5.5 0 0 0 .4.49 1.668 1.668 0 0 1 0 3.268.5.5 0 0 0-.4.49v1.719a1.5 1.5 0 0 0 1.5 1.5h11.004a1.5 1.5 0 0 0 1.5-1.5v-1.719a.5.5 0 0 0-.4-.49 1.669 1.669 0 0 1 0-3.268.5.5 0 0 0 .4-.49V4.123a1.5 1.5 0 0 0-1.5-1.5H2.498ZM6 6.537a.5.5 0 0 0-1 0v2.926a.5.5 0 0 0 1 0V6.538Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-web-link" fill="none" viewBox="0 0 16 16">
      <g class="es-line-web-link">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M5.868 10.132a.5.5 0 0 1 0-.707L12.793 2.5H9a.5.5 0 0 1 0-1h4.99a.499.499 0 0 1 .51.5v5a.5.5 0 0 1-1 0V3.207l-6.925 6.925a.5.5 0 0 1-.707 0ZM2.5 4A1.5 1.5 0 0 1 4 2.5h1.8a.5.5 0 0 0 0-1H4A2.5 2.5 0 0 0 1.5 4v8A2.5 2.5 0 0 0 4 14.5h8a2.5 2.5 0 0 0 2.5-2.5v-1.8a.5.5 0 0 0-1 0V12a1.5 1.5 0 0 1-1.5 1.5H4A1.5 1.5 0 0 1 2.5 12V4Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-share" fill="none" viewBox="0 0 16 16">

      <g class="es-line-share">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M7.64441 1.15078C7.66167 1.13309 7.68024 1.11668 7.69995 1.10169C7.72628 1.08165 7.75414 1.06458 7.78309 1.05046C7.84925 1.01815 7.9236 1.00001 8.00219 1M7.64441 1.15078L5.04779 3.74739C4.85253 3.94266 4.85253 4.25924 5.04779 4.4545C5.24305 4.64976 5.55964 4.64976 5.7549 4.4545L7.50224 2.70716V9.86019C7.50224 10.1363 7.7261 10.3602 8.00224 10.3602C8.27838 10.3602 8.50224 10.1363 8.50224 9.86019V2.70705L10.2497 4.4545C10.4449 4.64976 10.7615 4.64976 10.9568 4.4545C11.1521 4.25924 11.1521 3.94266 10.9568 3.74739L8.35585 1.14645C8.30212 1.09272 8.23921 1.05378 8.17216 1.02961C8.11986 1.01076 8.06503 1.00091 8.0101 1.00006C8.00748 1.00002 8.00486 1 8.00224 1M3.5 7.57356C2.67157 7.57356 2 8.24513 2 9.07356V12.5048C2 13.3333 2.67157 14.0048 3.5 14.0048H12.5047C13.3332 14.0048 14.0047 13.3333 14.0047 12.5048V9.07356C14.0047 8.24513 13.3332 7.57356 12.5047 7.57356H10.7891V6.57356H12.5047C13.8855 6.57356 15.0047 7.69285 15.0047 9.07356V12.5048C15.0047 13.8855 13.8855 15.0048 12.5047 15.0048H3.5C2.11929 15.0048 1 13.8855 1 12.5048V9.07356C1 7.69285 2.11929 6.57356 3.5 6.57356H5.21564V7.57356H3.5Z" fill="var(--icon-stroke)"/>
      </g>
    </symbol>
    <symbol id="es-line-archive" fill="none" viewBox="0 0 16 16">
      <g class="es-line-archive">
        <g class="Union">
          <mask id="a" fill="#fff" class="a">
            <path fill-rule="evenodd" d="M3.5 2.5h9a1 1 0 0 1 1 1v5H10a2 2 0 1 1-4 0H2.5v-5a1 1 0 0 1 1-1Zm-1 7v3a1 1 0 0 0 1 1h9a1 1 0 0 0 1-1v-3h-2.67a3.001 3.001 0 0 1-5.66 0H2.5Zm-1-1v-5a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-9a2 2 0 0 1-2-2v-4Z" clip-rule="evenodd"/>
          </mask>
          <path fill="var(--icon-stroke)" d="M13.5 8.5v1h1v-1h-1Zm-3.5 0v-1H9v1h1Zm-4 0h1v-1H6v1Zm-3.5 0h-1v1h1v-1Zm0 1v-1h-1v1h1Zm11 0h1v-1h-1v1Zm-2.67 0v-1h-.708l-.236.667.943.333Zm-5.66 0 .944-.333-.236-.667H5.17v1Zm7.33-8h-9v2h9v-2Zm2 2a2 2 0 0 0-2-2v2h2Zm0 5v-5h-2v5h2Zm-1-1H11v2h2.5v-2Zm-2.5 0h-1v2h1v-2Zm-2 1a1 1 0 0 1-1 1v2a3 3 0 0 0 3-3H9Zm-1 1a1 1 0 0 1-1-1H5a3 3 0 0 0 3 3v-2Zm-2-2H5v2h1v-2Zm-1 0H2.5v2H5v-2Zm-3.5-4v5h2v-5h-2Zm2-2a2 2 0 0 0-2 2h2v-2Zm-2 8v3h2v-3h-2Zm0 3a2 2 0 0 0 2 2v-2h-2Zm2 2h9v-2h-9v2Zm9 0a2 2 0 0 0 2-2h-2v2Zm2-2v-3h-2v3h2Zm-1-4h-2.67v2h2.67v-2Zm-3.614.667A2.001 2.001 0 0 1 8 10.5v2a4.001 4.001 0 0 0 3.772-2.667l-1.886-.666ZM8 10.5a2 2 0 0 1-1.886-1.333l-1.886.666A4.001 4.001 0 0 0 8 12.5v-2Zm-2.83-2H2.5v2h2.67v-2ZM.5 3.5v5h2v-5h-2Zm3-3a3 3 0 0 0-3 3h2a1 1 0 0 1 1-1v-2Zm9 0h-9v2h9v-2Zm3 3a3 3 0 0 0-3-3v2a1 1 0 0 1 1 1h2Zm0 5v-5h-2v5h2Zm0 1v-1h-2v1h2Zm0 3v-3h-2v3h2Zm-3 3a3 3 0 0 0 3-3h-2a1 1 0 0 1-1 1v2Zm-9 0h9v-2h-9v2Zm-3-3a3 3 0 0 0 3 3v-2a1 1 0 0 1-1-1h-2Zm0-3v3h2v-3h-2Zm0-1v1h2v-1h-2Z" mask="url(#a)"/>
        </g>
      </g>
    </symbol>
    <symbol id="es-line-title" fill="none" viewBox="0 0 16 16">
      <g class="es-line-title">
        <path fill="#171B1F" fill-rule="evenodd" d="M7.066 2.8a.5.5 0 0 1 .5-.5H14.5a.5.5 0 0 1 0 1H7.566a.5.5 0 0 1-.5-.5Zm0 3.466a.5.5 0 0 1 .5-.5H14.5a.5.5 0 0 1 0 1H7.566a.5.5 0 0 1-.5-.5Zm-5.209.623a.45.45 0 0 1-.425-.598L2.6 2.93a.75.75 0 0 1 .709-.504h.261v.032a.731.731 0 0 1 .474.458l1.174 3.375a.45.45 0 0 1-.425.598h-.017a.45.45 0 0 1-.43-.32l-.234-.763H2.536l-.233.764a.45.45 0 0 1-.43.319h-.016Zm.893-1.782h1.148l-.545-1.79h-.056l-.547 1.79ZM1 9.733a.5.5 0 0 1 .5-.5h13a.5.5 0 0 1 0 1h-13a.5.5 0 0 1-.5-.5Zm.5 2.967a.5.5 0 0 0 0 1h6.066a.5.5 0 0 0 0-1H1.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-unlock" fill="none" viewBox="0 0 16 16">
      <g class="es-line-unlock">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 1a3 3 0 0 0-3 3v1H4a2.5 2.5 0 0 0-2.5 2.5v5A2.5 2.5 0 0 0 4 15h8a2.5 2.5 0 0 0 2.5-2.5v-5A2.5 2.5 0 0 0 12 5H6V4a2 2 0 0 1 2-2h.5A1.5 1.5 0 0 1 10 3.5h1A2.5 2.5 0 0 0 8.5 1H8ZM2.5 7.5A1.5 1.5 0 0 1 4 6h8a1.5 1.5 0 0 1 1.5 1.5v5A1.5 1.5 0 0 1 12 14H4a1.5 1.5 0 0 1-1.5-1.5v-5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-unarchive" fill="none" viewBox="0 0 16 16">
      <g class="es-line-unarchive">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.5 2.5h9a1 1 0 0 1 1 1v5H10a2 2 0 1 1-4 0H2.5v-5a1 1 0 0 1 1-1Zm-1 7v3a1 1 0 0 0 1 1h9a1 1 0 0 0 1-1v-3h-2.67a3.001 3.001 0 0 1-5.66 0H2.5Zm-1-1v-5a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-9a2 2 0 0 1-2-2v-4Zm7-3.293 1.146 1.147a.5.5 0 0 0 .708-.708l-2-2a.5.5 0 0 0-.708 0l-2 2a.5.5 0 1 0 .708.708L7.5 5.207V9a.5.5 0 0 0 1 0V5.207Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-darkmode" fill="none" viewBox="0 0 16 16">
      <g class="es-line-darkmode">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2 8a6 6 0 0 1 6-6v12a6 6 0 0 1-6-6Zm6-7a7 7 0 1 0 0 14A7 7 0 0 0 8 1Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-up" fill="none" viewBox="0 0 16 16">
      <g class="es-line-up">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M13.354 10.854a.5.5 0 0 1-.708 0L8 6.207l-4.646 4.647a.5.5 0 0 1-.708-.708l5-5a.5.5 0 0 1 .708 0l5 5a.5.5 0 0 1 0 .708Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-right-chevron" fill="none" viewBox="0 0 16 16">
      <g class="es-line-right-chevron">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M5.646 13.354a.5.5 0 0 1 0-.708L10.293 8 5.646 3.354a.5.5 0 1 1 .708-.708l5 5a.5.5 0 0 1 0 .708l-5 5a.5.5 0 0 1-.708 0Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-left-chevron" fill="none" viewBox="0 0 16 16">
      <g class="es-line-left-chevron">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M10.354 2.646a.5.5 0 0 1 0 .708L5.707 8l4.647 4.646a.5.5 0 0 1-.708.708l-5-5a.5.5 0 0 1 0-.708l5-5a.5.5 0 0 1 .708 0Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-down" fill="none" viewBox="0 0 16 16">
      <g class="es-line-down">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.646 5.686a.5.5 0 0 1 .708 0L8 10.333l4.646-4.647a.5.5 0 0 1 .708.708l-5 5a.5.5 0 0 1-.708 0l-5-5a.5.5 0 0 1 0-.708Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-add" fill="none" viewBox="0 0 16 16">
      <g class="es-line-add">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8.5 2.5a.5.5 0 0 0-1 0v5h-5a.5.5 0 0 0 0 1h5v5a.5.5 0 0 0 1 0v-5h5a.5.5 0 0 0 0-1h-5v-5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-arrow-right" fill="none" viewBox="0 0 16 16">
      <g class="es-line-arrow-right">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M9.01 2.646a.5.5 0 0 0-.707.707L12.45 7.5H2.343a.5.5 0 1 0 0 1H12.45l-4.146 4.146a.5.5 0 1 0 .707.707L14 8.363a.498.498 0 0 0 .015-.712l-.005-.005-5-5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-align-center" fill="none" viewBox="0 0 16 16">
      <g class="es-line-align-center">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.5 3.2a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5Zm2 4.8a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1H5a.5.5 0 0 1-.5-.5ZM3 12.3a.5.5 0 0 0 0 1h10a.5.5 0 0 0 0-1H3Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-align-right" fill="none" viewBox="0 0 16 16">
      <g class="es-line-align-right">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.5 3.2a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5Zm0 4.8a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5ZM7 12.3a.5.5 0 0 0 0 1h6a.5.5 0 0 0 0-1H7Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-align-justify" fill="none" viewBox="0 0 16 16">
      <g class="es-line-align-justify">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.5 3.2a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5Zm0 4.8a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5Zm.5 4.3a.5.5 0 0 0 0 1h10a.5.5 0 0 0 0-1H3Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-discussions" fill="none" viewBox="0 0 16 16">
      <g class="es-line-discussions" clip-path="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M6.6.5C3.32.5.5 2.732.5 5.667c0 1.016.347 1.957.933 2.747v3.32a.5.5 0 0 0 .8.4l2.189-1.642a7.04 7.04 0 0 0 2.178.341c3.28 0 6.1-2.232 6.1-5.166C12.7 2.732 9.88.5 6.6.5ZM1.5 5.667C1.5 3.447 3.694 1.5 6.6 1.5c2.906 0 5.1 1.947 5.1 4.167 0 2.219-2.194 4.166-5.1 4.166a6.015 6.015 0 0 1-2.091-.37.5.5 0 0 0-.474.069l-1.602 1.201V8.245a.5.5 0 0 0-.109-.312c-.526-.66-.824-1.438-.824-2.266Zm13.166.653a.5.5 0 0 0-.822.569 3.57 3.57 0 0 1 .656 2.044c0 .829-.298 1.606-.824 2.267a.5.5 0 0 0-.109.311v2.555l-1.822-1.215a.5.5 0 0 0-.434-.058 6.078 6.078 0 0 1-1.91.307c-1.453 0-2.752-.497-3.673-1.278a.5.5 0 0 0-.646.763c1.11.942 2.642 1.515 4.318 1.515a7.08 7.08 0 0 0 1.989-.284l2.4 1.6a.5.5 0 0 0 .778-.416v-3.32c.586-.789.933-1.73.933-2.747a4.57 4.57 0 0 0-.834-2.613Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <path fill="#fff" d="M0 0h16v16H0z"/>
        </clipPath>
      </defs>
    </symbol>
    <symbol id="es-line-camera" fill="none" viewBox="0 0 16 16">
      <g class="es-line-camera">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7 2.5a1.5 1.5 0 0 0-1.2.6l-.6.8a1.5 1.5 0 0 1-1.2.6H3A1.5 1.5 0 0 0 1.5 6v6A1.5 1.5 0 0 0 3 13.5h10a1.5 1.5 0 0 0 1.5-1.5V6A1.5 1.5 0 0 0 13 4.5h-1a1.5 1.5 0 0 1-1.2-.6l-.6-.8A1.5 1.5 0 0 0 9 2.5H7Zm-2 0a2.5 2.5 0 0 1 2-1h2a2.5 2.5 0 0 1 2 1l.6.8a.5.5 0 0 0 .4.2h1A2.5 2.5 0 0 1 15.5 6v6a2.5 2.5 0 0 1-2.5 2.5H3A2.5 2.5 0 0 1 .5 12V6A2.5 2.5 0 0 1 3 3.5h1a.5.5 0 0 0 .4-.2l.6-.8Zm3 8a2 2 0 1 0 0-4 2 2 0 0 0 0 4Zm0 1a3 3 0 1 0 0-6 3 3 0 0 0 0 6Zm5-5a.5.5 0 1 0 0-1 .5.5 0 0 0 0 1Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-cursor" width="196" height="227" fill="none" viewBox="0 0 196 227">
      <g class="es-line-cursor" filter="url(#a)">
        <g class="Shape" filter="url(#b)">
          <path fill="#fff" fill-rule="evenodd" d="M94.477 12.914c-.243-.33-.539-1.002-1.064-1.82-.298-.461-1.037-1.332-1.257-1.774-.191-.39-.17-.565-.125-.889.08-.576.632-1.024 1.22-.964.444.045.82.36 1.16.657.204.179.456.526.608.722.14.18.173.254.322.467.197.282.259.421.184.111-.061-.455-.16-1.231-.304-1.918-.11-.521-.136-.603-.24-1.002-.111-.426-.168-.724-.271-1.175-.072-.32-.202-.971-.237-1.338-.049-.501-.074-1.32.226-1.695.236-.295.776-.384 1.11-.202.44.238.688.92.802 1.192.205.49.331 1.055.442 1.798.14.946.399 2.258.407 2.534.021-.339-.058-1.051-.003-1.376.05-.294.28-.636.57-.729a1.56 1.56 0 0 1 .784-.05c.268.059.55.264.656.457.31.573.316 1.742.329 1.68.074-.345.06-1.128.243-1.453.239-.427.844-.534 1.328-.462.029.004.057.01.086.015.373.078.501.317.579.447.187.315.293 1.208.325 1.52.013.13.063-.36.251-.675.347-.586 1.577-.7 1.625.586.021.6.017.572.017.976 0 .474-.011.76-.035 1.102-.026.367-.1 1.196-.207 1.597-.073.276-.317.897-.558 1.27 0 0-.919 1.146-1.02 1.662-.101.515-.067.519-.087.885-.02.365.104.845.104.845s-.687.095-1.057.032c-.335-.058-.749-.771-.856-.99-.147-.3-.461-.242-.584-.02-.193.35-.607.98-.9 1.02-.572.077-1.758.029-2.687.019 0 0 .158-.927-.195-1.246-.26-.237-.71-.719-.979-.972l-.712-.844Z" clip-rule="evenodd"/>
          <path stroke="var(--icon-stroke)" stroke-linecap="round" stroke-linejoin="round" d="M94.477 12.914c-.243-.33-.539-1.002-1.064-1.82-.298-.461-1.037-1.332-1.257-1.774-.191-.39-.17-.565-.125-.889.08-.576.632-1.024 1.22-.964.444.045.82.36 1.16.657.204.179.456.526.608.722.14.18.173.254.322.467.197.282.259.421.184.111-.061-.455-.16-1.231-.304-1.918-.11-.521-.136-.603-.24-1.002-.111-.426-.168-.724-.271-1.175-.072-.32-.202-.971-.237-1.338-.049-.501-.074-1.32.226-1.695.236-.295.776-.384 1.11-.202.44.238.688.92.802 1.192.205.49.331 1.055.442 1.798.14.946.399 2.258.407 2.534.021-.339-.058-1.051-.003-1.376.05-.294.28-.636.57-.729a1.56 1.56 0 0 1 .784-.05c.268.059.55.264.656.457.31.573.316 1.742.329 1.68.074-.345.06-1.128.243-1.453.239-.427.844-.534 1.328-.462.029.004.057.01.086.015.373.078.501.317.579.447.187.315.293 1.208.325 1.52.013.13.063-.36.251-.675.347-.586 1.577-.7 1.625.586.021.6.017.572.017.976 0 .474-.011.76-.035 1.102-.026.367-.1 1.196-.207 1.597-.073.276-.317.897-.558 1.27 0 0-.919 1.146-1.02 1.662-.101.515-.067.519-.087.885-.02.365.104.845.104.845s-.687.095-1.057.032c-.335-.058-.749-.771-.856-.99-.147-.3-.461-.242-.584-.02-.193.35-.607.98-.9 1.02-.572.077-1.758.029-2.687.019 0 0 .158-.927-.195-1.246-.26-.237-.71-.719-.979-.972l-.712-.844Z" clip-rule="evenodd"/>
        </g>
      </g>
      <defs>
        <filter id="a" width="198" height="226" x="-1" y="1" class="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
          <feOffset dy="4.088"/>
          <feGaussianBlur stdDeviation="1.563"/>
          <feColorMatrix values="0 0 0 0 0.152941 0 0 0 0 0.152941 0 0 0 0 0.152941 0 0 0 0.0372022 0"/>
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_1848_83112"/>
          <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
          <feOffset dy="10.338"/>
          <feGaussianBlur stdDeviation="3.953"/>
          <feColorMatrix values="0 0 0 0 0.152941 0 0 0 0 0.152941 0 0 0 0 0.152941 0 0 0 0.0532451 0"/>
          <feBlend in2="effect1_dropShadow_1848_83112" result="effect2_dropShadow_1848_83112"/>
          <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
          <feOffset dy="21.088"/>
          <feGaussianBlur stdDeviation="8.063"/>
          <feColorMatrix values="0 0 0 0 0.152941 0 0 0 0 0.152941 0 0 0 0 0.152941 0 0 0 0.0667549 0"/>
          <feBlend in2="effect2_dropShadow_1848_83112" result="effect3_dropShadow_1848_83112"/>
          <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
          <feOffset dy="43.437"/>
          <feGaussianBlur stdDeviation="16.608"/>
          <feColorMatrix values="0 0 0 0 0.152941 0 0 0 0 0.152941 0 0 0 0 0.152941 0 0 0 0.0827978 0"/>
          <feBlend in2="effect3_dropShadow_1848_83112" result="effect4_dropShadow_1848_83112"/>
          <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
          <feOffset dy="119"/>
          <feGaussianBlur stdDeviation="45.5"/>
          <feColorMatrix values="0 0 0 0 0.152941 0 0 0 0 0.152941 0 0 0 0 0.152941 0 0 0 0.12 0"/>
          <feBlend in2="effect4_dropShadow_1848_83112" result="effect5_dropShadow_1848_83112"/>
          <feBlend in="SourceGraphic" in2="effect5_dropShadow_1848_83112" result="shape"/>
        </filter>
        <filter id="b" width="17.25" height="19" x="89.5" y=".5" class="b" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
          <feOffset dy="1"/>
          <feGaussianBlur stdDeviation="1"/>
          <feComposite in2="hardAlpha" operator="out"/>
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.24 0"/>
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_1848_83112"/>
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_1848_83112" result="shape"/>
        </filter>
      </defs>
    </symbol>
    <symbol id="es-line-weekend" fill="none" viewBox="0 0 16 16">
      <g class="es-line-weekend">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8.502 1.5a.5.5 0 1 0-1 0v2.6a.5.5 0 0 0 1 0V1.5ZM6.156 5.017a2.662 2.662 0 0 0-4.27 1.91l-.85 5.919a1.768 1.768 0 0 0-.036.357v.002a1.8 1.8 0 0 0 3.274 1.03L6.3 11.537h3.402l2.026 2.7a1.8 1.8 0 0 0 3.276-1.033c0-.12-.012-.24-.037-.357l-.848-5.918a2.662 2.662 0 0 0-5.149-.726H7.034a2.662 2.662 0 0 0-.878-1.185Zm-1.725.457A1.662 1.662 0 0 1 6.168 6.8a.5.5 0 0 0 .49.4h2.688a.5.5 0 0 0 .49-.4 1.662 1.662 0 0 1 3.29.252l.854 5.951a.765.765 0 0 1 .024.2.8.8 0 0 1-1.46.455l-.011-.017-2.181-2.906a.5.5 0 0 0-.4-.2H6.05a.5.5 0 0 0-.4.2l-2.182 2.906a.8.8 0 0 1-1.445-.638l.853-5.951A1.662 1.662 0 0 1 4.43 5.474Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-nextweek" fill="none" viewBox="0 0 16 16">
      <g class="es-line-nextweek">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M5 1.5a.5.5 0 0 0-1 0v1.024c-.49.03-.838.097-1.135.248a2.5 2.5 0 0 0-1.093 1.093C1.5 4.4 1.5 5.1 1.5 6.5v4c0 1.4 0 2.1.272 2.635a2.5 2.5 0 0 0 1.093 1.092C3.4 14.5 4.1 14.5 5.5 14.5h5c1.4 0 2.1 0 2.635-.273a2.5 2.5 0 0 0 1.092-1.092c.273-.535.273-1.235.273-2.635v-4c0-1.4 0-2.1-.273-2.635a2.5 2.5 0 0 0-1.092-1.093c-.297-.15-.645-.218-1.136-.248L12 2.5v-1a.5.5 0 0 0-1 0v1H5v-1Zm5.5 2h-5c-.717 0-1.194 0-1.56.03-.356.03-.518.081-.621.133a1.5 1.5 0 0 0-.656.656c-.052.103-.103.265-.132.62-.023.274-.029.61-.03 1.061h10.998a14.426 14.426 0 0 0-.03-1.06c-.029-.356-.08-.518-.133-.621a1.5 1.5 0 0 0-.655-.656c-.103-.052-.265-.103-.62-.132-.367-.03-.844-.031-1.561-.031Zm-8 7V7h11v3.5c0 .717 0 1.194-.03 1.56-.03.356-.081.518-.134.621a1.5 1.5 0 0 1-.655.655c-.103.053-.265.104-.62.133-.367.03-.844.031-1.561.031h-5c-.717 0-1.194 0-1.56-.03-.356-.03-.518-.081-.621-.134a1.5 1.5 0 0 1-.656-.655c-.052-.103-.103-.265-.132-.62-.03-.367-.031-.844-.031-1.561Zm5.646-2.604a.5.5 0 0 1 .708 0l2 2a.5.5 0 0 1 0 .708l-2 2a.5.5 0 0 1-.708-.708l1.147-1.146H5.5a.5.5 0 0 1 0-1h3.793L8.146 8.604a.5.5 0 0 1 0-.708Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-today" fill="none" viewBox="0 0 16 16">
      <g class="es-line-today">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12Zm0 1A7 7 0 1 0 8 1a7 7 0 0 0 0 14Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-arrow-left" fill="none" viewBox="0 0 16 16">
      <g class="es-line-arrow-left">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M6.99 13.354a.5.5 0 0 0 .707-.707L3.55 8.5h10.107a.5.5 0 0 0 0-1H3.55l4.146-4.146a.5.5 0 1 0-.707-.707L2 7.637a.499.499 0 0 0-.01.717l5 5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-duplicate" fill="none" viewBox="0 0 16 16">
      <g class="es-line-duplicate">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M6.721 1.6a2 2 0 0 0-2 2v.827h1V3.6a1 1 0 0 1 1-1H13a1 1 0 0 1 1 1v5.226a1 1 0 0 1-1 1h-.82v1H13a2 2 0 0 0 2-2V3.6a2 2 0 0 0-2-2H6.72ZM3 5.285a2 2 0 0 0-2 2v5.226a2 2 0 0 0 2 2h6.278a2 2 0 0 0 2-2V7.285a2 2 0 0 0-2-2H3Zm-1 2a1 1 0 0 1 1-1h6.278a1 1 0 0 1 1 1v5.226a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7.285Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-colour" fill="none" viewBox="0 0 16 16">
      <g class="es-line-colour">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M4.234 1a1.5 1.5 0 0 0-1.5 1.5v1.468a1.5 1.5 0 0 0 1.5 1.5h8.404a1.5 1.5 0 0 0 1.5-1.5V2.5a1.5 1.5 0 0 0-1.5-1.5H4.234Zm-.5 1.5a.5.5 0 0 1 .5-.5h8.404a.5.5 0 0 1 .5.5v1.468a.5.5 0 0 1-.5.5H4.234a.5.5 0 0 1-.5-.5V2.5ZM1.5 2.734a.5.5 0 0 1 .5.5v3.835h6.436a.5.5 0 0 1 .5.5v6.936a.5.5 0 0 1-1 0V8.069H1.5a.5.5 0 0 1-.5-.5V3.234a.5.5 0 0 1 .5-.5Zm11.682 4.551-.411.284-.412-.284a.5.5 0 0 1 .823 0Zm-.411.284-.412-.284-.002.003-.004.006-.015.022-.057.084a23 23 0 0 0-.81 1.295c-.221.381-.448.803-.621 1.197-.164.373-.313.793-.313 1.145a2.234 2.234 0 0 0 4.467 0c0-.352-.149-.772-.313-1.145-.173-.394-.4-.816-.62-1.197a23.633 23.633 0 0 0-.868-1.379l-.015-.022-.004-.006-.001-.002-.412.283Zm0 .912a21.75 21.75 0 0 0-.435.715c-.212.366-.419.752-.57 1.098-.162.367-.23.618-.23.743a1.234 1.234 0 1 0 2.469 0c0-.125-.068-.377-.23-.743a11.59 11.59 0 0 0-.57-1.098c-.15-.26-.302-.506-.434-.715Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-header-column" fill="none" viewBox="0 0 16 16">
      <g class="es-line-header column">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M4.5 9.5v-3H14v3H4.5Zm0 4v-3H14v2a1 1 0 0 1-1 1H4.5Zm0 1H3a2 2 0 0 1-2-2v-9a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4.5Zm0-12H13a1 1 0 0 1 1 1v2H4.5v-3Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-header-row" fill="none" viewBox="0 0 16 16">
      <g class="es-line-header row">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.5 1.5H13a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-9a2 2 0 0 1 2-2h.5Zm10.5 4H8.5V9H14V5.5Zm-6.5 0H2V9h5.5V5.5Zm-4.5 8h4.5V10H2v2.5a1 1 0 0 0 1 1Zm10 0H8.5V10H14v2.5a1 1 0 0 1-1 1Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-all-apps" fill="none" viewBox="0 0 16 16">
      <g class="es-line-all apps">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M13.85 4.697 12.006 6.54a.5.5 0 0 1-.707 0L9.456 4.697a.5.5 0 0 1 0-.707l1.843-1.844a.5.5 0 0 1 .707 0L13.85 3.99a.5.5 0 0 1 0 .707Zm.707-1.414a1.5 1.5 0 0 1 0 2.121l-1.844 1.843a1.5 1.5 0 0 1-2.121 0L8.749 5.404a1.5 1.5 0 0 1 0-2.121l1.843-1.844a1.5 1.5 0 0 1 2.121 0l1.844 1.844Zm-8.95 1.06a1.803 1.803 0 1 1-3.607 0 1.803 1.803 0 0 1 3.607 0Zm1 0a2.803 2.803 0 1 1-5.607 0 2.803 2.803 0 0 1 5.607 0Zm6.35 6.047H10.35a.5.5 0 0 0-.5.5v2.607a.5.5 0 0 0 .5.5h2.607a.5.5 0 0 0 .5-.5V10.89a.5.5 0 0 0-.5-.5Zm-2.607-1a1.5 1.5 0 0 0-1.5 1.5v2.607a1.5 1.5 0 0 0 1.5 1.5h2.607a1.5 1.5 0 0 0 1.5-1.5V10.89a1.5 1.5 0 0 0-1.5-1.5H10.35Zm-7.85 1h2.607a.5.5 0 0 1 .5.5v2.607a.5.5 0 0 1-.5.5H2.5a.5.5 0 0 1-.5-.5V10.89a.5.5 0 0 1 .5-.5Zm-1.5.5a1.5 1.5 0 0 1 1.5-1.5h2.607a1.5 1.5 0 0 1 1.5 1.5v2.607a1.5 1.5 0 0 1-1.5 1.5H2.5a1.5 1.5 0 0 1-1.5-1.5V10.89Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-folder-upload" fill="none" viewBox="0 0 16 16">
      <g class="es-line-folder-upload">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M10.023 2.5 8.432 3.686a1 1 0 0 1-.598.199H2V12.5a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1v-10h-3.977Zm-.531-.851A.75.75 0 0 1 9.94 1.5h4.41a.65.65 0 0 1 .65.65V12.5a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V3.535a.65.65 0 0 1 .65-.65h6.184l1.658-1.236ZM8 5.669a.5.5 0 0 1 .353.146l2.5 2.5a.5.5 0 1 1-.708.707L8.5 7.375v4.293a.5.5 0 1 1-1 0V7.375L5.853 9.022a.5.5 0 0 1-.707-.707l2.501-2.5a.5.5 0 0 1 .354-.147Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-file-upload" fill="none" viewBox="0 0 16 16">
      <g class="es-line-file-upload">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M11.997 14h-8a1.5 1.5 0 0 1-1.5-1.5v-9a1.5 1.5 0 0 1 1.5-1.5H9.5v2A2.5 2.5 0 0 0 12 6.5h1.497v6a1.5 1.5 0 0 1-1.5 1.5Zm2.453-8.5a2.5 2.5 0 0 0-.62-1.22L11.524 1.8A2.5 2.5 0 0 0 9.693 1H3.997a2.5 2.5 0 0 0-2.5 2.5v9a2.5 2.5 0 0 0 2.5 2.5h8a2.5 2.5 0 0 0 2.5-2.5v-7h-.047Zm-1.032 0H12A1.5 1.5 0 0 1 10.5 4V2.236a1.5 1.5 0 0 1 .292.243l2.304 2.482a1.5 1.5 0 0 1 .322.539ZM8 6a.5.5 0 0 1 .354.147l2.499 2.5a.5.5 0 0 1-.708.706L8.5 7.707V12a.5.5 0 0 1-1 0V7.707L5.853 9.354a.5.5 0 1 1-.706-.708l2.5-2.5A.5.5 0 0 1 8.001 6Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-new-folder" fill="none" viewBox="0 0 16 16">
      <g class="es-line-new-folder">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M10.023 2.5 8.432 3.686a1 1 0 0 1-.598.199H2V12.5a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1v-10h-3.977Zm-.531-.851A.75.75 0 0 1 9.94 1.5h4.41a.65.65 0 0 1 .65.65V12.5a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V3.535a.65.65 0 0 1 .65-.65h6.184l1.658-1.236ZM8 5.823a.5.5 0 0 1 .5.5v2.002h2a.5.5 0 1 1 0 1h-2v2a.5.5 0 0 1-1 0v-2H5.499a.5.5 0 1 1 0-1H7.5V6.323a.5.5 0 0 1 .5-.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-notes" fill="none" viewBox="0 0 16 16">
      <g class="es-line-notes">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M11.997 14h-8a1.5 1.5 0 0 1-1.5-1.5v-9a1.5 1.5 0 0 1 1.5-1.5H9.5v2A2.5 2.5 0 0 0 12 6.5h1.497v6a1.5 1.5 0 0 1-1.5 1.5Zm2.453-8.5a2.5 2.5 0 0 0-.62-1.22L11.524 1.8A2.5 2.5 0 0 0 9.693 1H3.997a2.5 2.5 0 0 0-2.5 2.5v9a2.5 2.5 0 0 0 2.5 2.5h8a2.5 2.5 0 0 0 2.5-2.5v-7h-.047Zm-1.032 0H12A1.5 1.5 0 0 1 10.5 4V2.236a1.5 1.5 0 0 1 .292.243l2.304 2.482a1.5 1.5 0 0 1 .322.539ZM4.5 9a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1H5a.5.5 0 0 1-.5-.5Zm.5 2a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1H5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-storage" width="17" height="17" fill="none" viewBox="0 0 17 17">
      <g class="es-line-storage">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3 3a1 1 0 0 1 1-1h5.894a1 1 0 0 1 .795.394l2.254 2.957a1 1 0 0 1 .205.606v7.42a1 1 0 0 1-1 1h-.074v-4.5a.5.5 0 0 0-.5-.5h-7a.5.5 0 0 0-.5.5v4.5H4a1 1 0 0 1-1-1V3Zm8.074 11.377h-6v-4h6v4Zm-6.51 1H4a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h5.894a2 2 0 0 1 1.59.788l2.254 2.956a2 2 0 0 1 .41 1.213v7.42a2 2 0 0 1-2 2H4.564ZM5.074 3a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2a.5.5 0 0 1 .5-.5Zm2.5.5a.5.5 0 0 0-1 0v2a.5.5 0 0 0 1 0v-2Zm1.5-.5a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2a.5.5 0 0 1 .5-.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-preview" fill="none" viewBox="0 0 16 16">
      <g class="es-line-preview">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 12a7.5 7.5 0 0 1-6.635-4A7.5 7.5 0 0 1 8 4a7.499 7.499 0 0 1 6.635 4A7.499 7.499 0 0 1 8 12Zm0-9a8.502 8.502 0 0 1 7.748 5A8.502 8.502 0 0 1 8 13 8.502 8.502 0 0 1 .25 8 8.502 8.502 0 0 1 8 3Zm1.5 5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm1 0a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <!-- TODO replace with espresso icon when new icon is available -->
    <symbol id="es-line-hide" fill="none" viewBox="0 0 16 16">
      <g class="es-line-hide">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M3.07279 1.00088L2.86463 0.70945C2.70413 0.484744 2.39185 0.432698 2.16714 0.593203C1.94244 0.753707 1.89039 1.06598 2.0509 1.29069L3.98896 4.00397C2.34466 4.8856 1.02406 6.29245 0.251465 8.0002C1.58548 10.9487 4.55301 13.0001 7.99967 13.0001C8.76345 13.0001 9.50369 12.8993 10.2079 12.7104L12.0509 15.2907C12.2114 15.5154 12.5237 15.5674 12.7484 15.4069C12.9358 15.2731 13.0031 15.0337 12.9272 14.8276L3.13537 1.11902C3.10871 1.08169 3.08791 1.04194 3.07279 1.00088ZM12.0992 11.9479C13.7023 11.0637 14.9893 9.67688 15.748 7.99995C14.414 5.0515 11.4464 3.00007 7.99978 3.00007C7.27344 3.00007 6.56837 3.09118 5.89539 3.26259L6.52562 4.1449C7.00232 4.04989 7.49526 4.00007 7.99978 4.00007C10.8763 4.00007 13.3765 5.6195 14.6349 7.99997C13.9318 9.3301 12.841 10.4226 11.5134 11.1278L12.0992 11.9479ZM10.1685 9.24493C10.3794 8.87838 10.5 8.45331 10.5 8.00007C10.5 6.61936 9.3807 5.50007 7.99999 5.50007C7.83782 5.50007 7.67926 5.51551 7.52569 5.54501L8.21925 6.51598C8.94374 6.6221 9.49999 7.24613 9.49999 8.00007C9.49999 8.09365 9.49142 8.18523 9.47502 8.27407L10.1685 9.24493ZM1.36453 8.00018C2.08344 6.64021 3.20762 5.52861 4.57573 4.82545L5.8877 6.66222C5.64217 7.04907 5.49999 7.50797 5.49999 8.00007C5.49999 9.38079 6.61927 10.5001 7.99999 10.5001C8.19988 10.5001 8.39429 10.4766 8.58062 10.4323L9.58115 11.833C9.07124 11.9425 8.54215 12.0001 7.99967 12.0001C5.12317 12.0001 2.62299 10.3807 1.36453 8.00018ZM6.49999 8.00007C6.49999 7.8602 6.51913 7.7248 6.55494 7.59635L7.91297 9.49759C7.12504 9.45252 6.49999 8.79929 6.49999 8.00007Z"/>
      </g>
    </symbol>
    <symbol id="es-line-upload" fill="none" viewBox="0 0 16 16">
      <g class="es-line-upload">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.5 1a.5.5 0 0 0 0 1h11a.5.5 0 0 0 0-1h-11Zm5.146 3.146a.5.5 0 0 1 .708 0l5 5a.5.5 0 0 1-.708.708L8.5 5.707V14.5a.5.5 0 0 1-1 0V5.707L3.354 9.854a.5.5 0 0 1-.708-.708l5-5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-folder-alt" fill="none" viewBox="0 0 16 16">
      <g class="es-line-folder-alt">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M1.842 3.258a1 1 0 0 1 1-1h2.754a.5.5 0 0 1 .422.233l1.37 2.168a.5.5 0 0 0 .423.233h5.347a1 1 0 0 1 1 .984v6.366a1.5 1.5 0 0 1-1.5 1.5H3.342a1.5 1.5 0 0 1-1.5-1.5V3.258ZM15.158 5.87v6.372a2.5 2.5 0 0 1-2.5 2.5H3.342a2.5 2.5 0 0 1-2.5-2.5V3.258a2 2 0 0 1 2-2h2.754a1.5 1.5 0 0 1 1.107.489h6.455a2 2 0 0 1 2 2v2.122Zm-1-1.71v-.412a1 1 0 0 0-1-1H7.363l.724 1.145h5.07c.365 0 .707.098 1 .268Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-download" fill="none" viewBox="0 0 16 16">
      <g class="es-line-download">
        <path fill="#212121" fill-rule="evenodd" d="M3.354 6.146 7.5 10.293V1.5a.5.5 0 0 1 1 0v8.793l4.146-4.147a.5.5 0 0 1 .708.708l-5 5a.5.5 0 0 1-.708 0l-5-5a.5.5 0 1 1 .708-.708ZM2.5 15a.5.5 0 0 1 0-1h11a.5.5 0 0 1 0 1h-11Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-share" fill="none" viewBox="0 0 16 16">
      <g class="es-line-share">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7.644 1.15A.503.503 0 0 1 8.002 1m-.358.15L5.048 3.748a.5.5 0 0 0 .707.708l1.747-1.748V9.86a.5.5 0 0 0 1 0V2.707l1.748 1.748a.5.5 0 1 0 .707-.708l-2.601-2.6A.498.498 0 0 0 8.002 1M3.5 7.574a1.5 1.5 0 0 0-1.5 1.5v3.43a1.5 1.5 0 0 0 1.5 1.5h9.005a1.5 1.5 0 0 0 1.5-1.5v-3.43a1.5 1.5 0 0 0-1.5-1.5h-1.716v-1h1.716a2.5 2.5 0 0 1 2.5 2.5v3.43a2.5 2.5 0 0 1-2.5 2.5H3.5a2.5 2.5 0 0 1-2.5-2.5v-3.43a2.5 2.5 0 0 1 2.5-2.5h1.716v1H3.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-globe" fill="none" viewBox="0 0 16 16">
      <g class="es-line-globe">
        <path fill="#000" fill-rule="evenodd" d="M2 8.002c0-.767.144-1.5.406-2.175l2.026 1.447.02.015c.076.054.172.123.282.168a1 1 0 0 0 .3.071c.12.01.235-.009.328-.023l.024-.004.064-.01.025-.004c.096-.014.216-.032.33-.08a1 1 0 0 0 .27-.166c.093-.08.163-.18.22-.258l.014-.022.556-.778.056-.078.001-.001h.001l.091-.032 1.908-.635.02-.007c.073-.024.164-.054.25-.102a1 1 0 0 0 .204-.153c.071-.068.126-.148.17-.21l.012-.017.028-.041.01-.014c.037-.053.084-.12.12-.195a1 1 0 0 0 .072-.205c.019-.081.024-.162.028-.227l.001-.017.128-1.92A6.002 6.002 0 1 1 2 8.002ZM5.013 6.46l-2.16-1.543A6 6 0 0 1 8.98 2.08l-.14 2.103a2.962 2.962 0 0 1-.006.082v.002h-.001a2.983 2.983 0 0 1-.047.068l-.028.041c-.03.043-.045.065-.057.08l-.002.001a3.018 3.018 0 0 1-.092.032l-1.909.635-.019.007a1.386 1.386 0 0 0-.246.1 1 1 0 0 0-.202.15c-.07.067-.125.144-.169.205l-.011.017-.556.779a3.083 3.083 0 0 1-.073.1l-.001.001h-.002a3.1 3.1 0 0 1-.122.021l-.063.01a3.08 3.08 0 0 1-.12.017h-.002a3.084 3.084 0 0 1-.099-.07ZM8.003 1a7.002 7.002 0 1 0 0 14.005A7.002 7.002 0 0 0 8.002 1Zm-.188 7.05a.5.5 0 0 0-.693.14l-.93 1.393a.5.5 0 0 0-.073.375l.464 2.322a.5.5 0 0 0 .49.402h1.858a.5.5 0 0 0 .4-.2l1.394-1.857a.5.5 0 0 0-.123-.716L7.815 8.05ZM7.14 9.966l.537-.805 1.93 1.287-.926 1.236H7.483l-.343-1.72Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-folder-shared" fill="none" viewBox="0 0 16 16">
      <g class="es-line-folder-shared">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8.432 3.686 10.023 2.5H14v10a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.885h5.834a1 1 0 0 0 .598-.199ZM9.94 1.5a.75.75 0 0 0-.448.149L7.834 2.885H1.65a.65.65 0 0 0-.65.65V12.5a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V2.15a.65.65 0 0 0-.65-.65H9.94ZM8.37 7c.396 0 .746.196.958.497a1.996 1.996 0 0 0-.013 1.371A1.173 1.173 0 1 1 8.368 7Zm-.29 4.187c.117-.528.418-.975.827-1.281H7.74a1.72 1.72 0 0 0-1.679 1.346l-.041.187a.86.86 0 0 0 .84 1.046h1.51a1.095 1.095 0 0 1-.344-1.06l.052-.238Zm3.053-1.84a1.173 1.173 0 1 0 0-2.347 1.173 1.173 0 0 0 0 2.347Zm-2.306 1.905a1.72 1.72 0 0 1 1.68-1.346h1.253c.806 0 1.504.56 1.679 1.346l.041.187a.86.86 0 0 1-.84 1.046H9.625a.86.86 0 0 1-.84-1.046l.042-.187Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-plan" fill="none" viewBox="0 0 16 16">
      <g class="es-line-plan" clip-path="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M15.5 1a.5.5 0 0 0-.686-.464l-4.48 1.792L5.86.538A.498.498 0 0 0 5.684.5m-.018 0a.498.498 0 0 0-.198.04L.814 2.403a.5.5 0 0 0-.314.465V15a.5.5 0 0 0 .686.464l4.48-1.792 4.473 1.789a.498.498 0 0 0 .394-.002l4.653-1.861a.5.5 0 0 0 .314-.465V1m-4.666 13.261 3.666-1.466V1.739l-3.666 1.466v11.056Zm-1-11.056L6.167 1.738v11.057l3.667 1.467V3.205Zm-4.667 9.59V1.739L1.5 3.205v11.056l3.667-1.466Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <path fill="#fff" d="M0 0h16v16H0z"/>
        </clipPath>
      </defs>
    </symbol>
    <symbol id="es-line-security" fill="none" viewBox="0 0 16 16">
      <g class="es-line-security" clip-path="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7.853.022a.5.5 0 0 1 .294 0l6.5 2A.5.5 0 0 1 15 2.5V9c0 2.512-1.813 4.268-3.476 5.356a14.893 14.893 0 0 1-3.294 1.595l-.06.02-.016.005-.005.001h-.001L8 15.5l-.147.478H7.85l-.005-.002-.017-.006-.059-.019a14.38 14.38 0 0 1-.98-.373 14.892 14.892 0 0 1-2.314-1.222C2.813 13.268 1 11.512 1 9V2.5a.5.5 0 0 1 .353-.478l6.5-2ZM8 15.5l.147.478a.5.5 0 0 1-.294 0L8 15.5Zm0-.527c.032-.01.07-.024.111-.039a13.889 13.889 0 0 0 2.865-1.415C12.563 12.482 14 10.988 14 9V2.87L8 1.022 2 2.87V9c0 1.988 1.437 3.482 3.024 4.519A13.89 13.89 0 0 0 8 14.973ZM8 5a1 1 0 0 0-1 1v1h2V6a1 1 0 0 0-1-1Zm2 2V6a2 2 0 0 0-4 0v1h-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h5a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5H10Zm-.5 1H6v2h4V8h-.5Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <path fill="#fff" d="M0 0h16v16H0z"/>
        </clipPath>
      </defs>
    </symbol>
    <symbol id="es-line-privacy-alt" fill="none" viewBox="0 0 16 16">
      <g class="es-line-privacy-alt" clip-path="url(#a)">
        <path fill="#212121" fill-rule="evenodd" d="M8.147.022a.5.5 0 0 0-.294 0l-6.5 2A.5.5 0 0 0 1 2.5V9c0 2.512 1.813 4.268 3.476 5.356a14.892 14.892 0 0 0 3.294 1.595l.06.02.016.005.005.001h.001L8 15.5l-.147.478a.5.5 0 0 0 .294 0L8 15.5l.147.478h.002l.005-.002.017-.006.059-.019a14.383 14.383 0 0 0 .98-.373c.628-.266 1.469-.67 2.314-1.222C13.188 13.268 15 11.512 15 9V2.5a.5.5 0 0 0-.353-.478l-6.5-2Zm-.036 14.912-.111.04-.111-.04a13.89 13.89 0 0 1-2.865-1.415C3.437 12.482 2 10.988 2 9V2.87l6-1.847 6 1.846V9c0 1.988-1.438 3.482-3.024 4.519a13.889 13.889 0 0 1-2.865 1.415Zm.036-12.78a.5.5 0 0 0-.294 0L3.201 3.586a.5.5 0 0 0-.353.478v4.652c0 1.873 1.35 3.167 2.552 3.952a10.79 10.79 0 0 0 2.39 1.158l.045.014.012.004.004.002h.001L8 13.368l-.147.478a.5.5 0 0 0 .294 0L8 13.368l.147.478h.002l.004-.002.012-.004.044-.014a10.434 10.434 0 0 0 .712-.271 10.79 10.79 0 0 0 1.679-.887c1.202-.785 2.552-2.08 2.552-3.952V4.064a.5.5 0 0 0-.353-.478L8.147 2.154ZM8.032 12.83 8 12.84l-.032-.01a9.798 9.798 0 0 1-2.02-.998c-1.125-.736-2.1-1.768-2.1-3.116V4.433L8 3.155l4.152 1.278v4.283c0 1.348-.975 2.38-2.1 3.115a9.796 9.796 0 0 1-2.02.999Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <path fill="#fff" d="M0 0h16v16H0z"/>
        </clipPath>
      </defs>
    </symbol>
    <symbol id="es-line-laptop" fill="none" viewBox="0 0 16 16">
      <g class="es-line-laptop">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.146 3.146A.5.5 0 0 1 2.5 3h11a.5.5 0 0 1 .5.5v6a.5.5 0 0 0 1 0v-6A1.5 1.5 0 0 0 13.5 2h-11A1.5 1.5 0 0 0 1 3.5v6a.5.5 0 0 0 1 0v-6a.5.5 0 0 1 .146-.354ZM.5 11a.5.5 0 0 0-.5.5A2.5 2.5 0 0 0 2.5 14h11a2.5 2.5 0 0 0 2.5-2.5.5.5 0 0 0-.5-.5H.5Zm.94 1.56a1.5 1.5 0 0 1-.354-.56h13.828a1.5 1.5 0 0 1-1.414 1h-11a1.5 1.5 0 0 1-1.06-.44Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-mobile" fill="none" viewBox="0 0 16 16">
      <g class="es-line-mobile">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3 1.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 .5.5v13a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5v-13ZM3.5 0A1.5 1.5 0 0 0 2 1.5v13A1.5 1.5 0 0 0 3.5 16h9a1.5 1.5 0 0 0 1.5-1.5v-13A1.5 1.5 0 0 0 12.5 0h-9ZM9 12a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-minimise" fill="none" viewBox="0 0 16 16">
      <g class="es-line-minimise">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.5 11a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 0 1H4a.5.5 0 0 1-.5-.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-pages" fill="none" viewBox="0 0 16 16">
      <g class="es-line-pages">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M5.249 1.474a.5.5 0 0 0-.5.5v9.899a.5.5 0 0 0 .5.5h7.749a.5.5 0 0 0 .5-.5v-6.1h-2.8a1.5 1.5 0 0 1-1.5-1.5v-2.8h-3.95Zm4.95.707v2.092a.5.5 0 0 0 .5.5h2.092l-2.593-2.592Zm-6.45-.207a1.5 1.5 0 0 1 1.5-1.5h4.035a1.5 1.5 0 0 1 1.06.439l3.714 3.714a1.5 1.5 0 0 1 .44 1.06v6.186a1.5 1.5 0 0 1-1.5 1.5h-.5v.5a1.5 1.5 0 0 1-1.5 1.5H3.673a1.85 1.85 0 0 1-1.85-1.85v-9.55a1.5 1.5 0 0 1 1.5-1.5h.426v-.5Zm1.5 11.399h6.249v.5a.5.5 0 0 1-.5.5H3.673a.85.85 0 0 1-.85-.85v-9.55a.5.5 0 0 1 .5-.5h.426v8.4a1.5 1.5 0 0 0 1.5 1.5Zm.496-5.45a.5.5 0 0 1 .5-.5h3.962a.5.5 0 1 1 0 1H6.245a.5.5 0 0 1-.5-.5Zm.5 1.65a.5.5 0 1 0 0 1h5.943a.5.5 0 1 0 0-1H6.245Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-pages" fill="none" viewBox="0 0 16 16">
      <g class="es-line-pages">
        <path fill="#fff" d="M0 0h16v16H0z"/>
        <path stroke="#000" stroke-linecap="round" d="M3.5 4h9m-9 3h4m1.302 7.5H3.5a2 2 0 0 1-2-2v-9a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v5.267" class="Vector"/>
        <path stroke="var(--icon-stroke)" stroke-linejoin="round" d="m11.08 7.168 3.843 5.488a.5.5 0 0 1-.123.697l-1.228.86a.5.5 0 0 1-.697-.123L9.032 8.602l-.008-2.191 2.056.757Z" class="Vector"/>
      </g>
    </symbol>
    <symbol id="es-line-pages-alt" fill="none" viewBox="0 0 16 16">
      <g class="es-line-pages-alt">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.5 2A1.5 1.5 0 0 0 2 3.5v9A1.5 1.5 0 0 0 3.5 14h9a1.5 1.5 0 0 0 1.5-1.5V7.198a.5.5 0 0 1 1 0V12.5a2.5 2.5 0 0 1-2.5 2.5h-9A2.5 2.5 0 0 1 1 12.5v-9A2.5 2.5 0 0 1 3.5 1h5.267a.5.5 0 1 1 0 1H3.5ZM3 12.3a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5Zm2.575-4.931a.5.5 0 0 0-.138.26l-.495 2.617a.5.5 0 0 0 .584.584l2.616-.495a.5.5 0 0 0 .261-.138l5.869-5.869a1 1 0 0 0 0-1.414L12.858 1.5a1 1 0 0 0-1.414 0l-5.87 5.869ZM6.06 9.71l.33-1.744 5.76-5.76 1.414 1.414-5.76 5.76-1.744.33Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-wifi-off" fill="none" viewBox="0 0 16 16">
      <g class="es-line-wifi-off">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M14.854 2.354a.5.5 0 0 0-.708-.708L12.313 3.48A9.999 9.999 0 0 0 .929 5.43a.5.5 0 1 0 .707.707 9 9 0 0 1 9.922-1.902L9.175 6.618a5.999 5.999 0 0 0-5.417 1.64.5.5 0 1 0 .707.707A5 5 0 0 1 8.283 7.51l-6.137 6.136a.5.5 0 0 0 .708.708L9.48 7.726a5 5 0 0 1 2.052 1.239.5.5 0 1 0 .707-.708 5.998 5.998 0 0 0-1.978-1.312L12.5 4.708a9 9 0 0 1 1.863 1.429.5.5 0 1 0 .707-.708 9.997 9.997 0 0 0-1.84-1.45l1.625-1.625Zm-5.52 8.654a.5.5 0 1 0-.667.746A1 1 0 1 1 7 12.5a.5.5 0 1 0-1 0 2 2 0 1 0 3.333-1.492Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-all-apps" fill="none" viewBox="0 0 16 16">
      <g class="es-line-all apps">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M13.85 4.697 12.006 6.54a.5.5 0 0 1-.707 0L9.456 4.697a.5.5 0 0 1 0-.707l1.843-1.844a.5.5 0 0 1 .707 0L13.85 3.99a.5.5 0 0 1 0 .707Zm.707-1.414a1.5 1.5 0 0 1 0 2.121l-1.844 1.843a1.5 1.5 0 0 1-2.12 0L8.748 5.404a1.5 1.5 0 0 1 0-2.121l1.843-1.844a1.5 1.5 0 0 1 2.121 0l1.844 1.844Zm-8.95 1.06a1.803 1.803 0 1 1-3.607 0 1.803 1.803 0 0 1 3.607 0Zm1 0a2.803 2.803 0 1 1-5.607 0 2.803 2.803 0 0 1 5.607 0Zm6.35 6.047H10.35a.5.5 0 0 0-.5.5v2.607a.5.5 0 0 0 .5.5h2.607a.5.5 0 0 0 .5-.5V10.89a.5.5 0 0 0-.5-.5Zm-2.607-1a1.5 1.5 0 0 0-1.5 1.5v2.607a1.5 1.5 0 0 0 1.5 1.5h2.607a1.5 1.5 0 0 0 1.5-1.5V10.89a1.5 1.5 0 0 0-1.5-1.5H10.35Zm-4.368 4.606c-.818-.002-1.774-.002-2.537 0C2.898 14 2.23 14 1.62 14l2.183-3.58 2.18 3.574v.002h-.002Zm.855-.523a1.002 1.002 0 0 1-.856 1.523c-.818-.002-1.772-.002-2.533 0C2.9 15 2.231 15 1.618 15a.999.999 0 0 1-.852-1.52L2.95 9.9a1 1 0 0 1 1.708 0l2.18 3.573Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-square" fill="none" viewBox="0 0 16 16">
      <g class="es-line-square">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M1.776 1.776h12.448v12.448H1.776V1.776Zm1 1v10.448h10.448V2.776H2.776Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-pentagon" fill="none" viewBox="0 0 16 16">
      <g class="es-line-pentagon">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="m8 1.364.294.213 6.39 4.643.293.213-.112.346-2.44 7.512-.113.345H3.687l-.112-.345-2.44-7.512-.113-.346.294-.213 6.39-4.643L8 1.364ZM8 2.6 2.198 6.815l2.216 6.821h7.172l2.216-6.82L8 2.6Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="espressoline---polyline" fill="none" viewBox="0 0 16 16">
      <g class="es-line-Polyline">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M12.997 3.498a.5.5 0 1 1 .995.105.5.5 0 0 1-.995-.105Zm.654-1.44a1.5 1.5 0 0 0-1.57 1.994l-8.793 7.12a1.5 1.5 0 1 0 .63.777l8.792-7.12a1.5 1.5 0 1 0 .941-2.77ZM2.556 11.954a.5.5 0 1 0-.105.994.5.5 0 0 0 .105-.994Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-restrictions" fill="none" viewBox="0 0 16 16">
      <g class="es-line-restrictions" clip-path="url(#a)">
        <path fill="#212121" fill-rule="evenodd" d="M1.982 8a6.017 6.017 0 0 1 9.904-4.594l-8.48 8.48A5.993 5.993 0 0 1 1.982 8Zm2.13 4.594a6.017 6.017 0 0 0 8.48-8.48l-8.48 8.48ZM8 .983a7.017 7.017 0 1 0 0 14.034A7.017 7.017 0 0 0 8 .983Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <path fill="#fff" d="M0 0h16v16H0z"/>
        </clipPath>
      </defs>
    </symbol>
    <symbol id="es-line-group" fill="none" viewBox="0 0 16 16">
      <g class="es-line-group">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.5 4h9a1 1 0 1 1 0 2h-9a1 1 0 1 1 0-2Zm9 3a2 2 0 1 0 0-4h-9a2 2 0 1 0 0 4h9Zm-9 3h3a1 1 0 1 1 0 2h-3a1 1 0 1 1 0-2Zm3 3a2 2 0 1 0 0-4h-3a2 2 0 1 0 0 4h3Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-line-heart" fill="none" viewBox="0 0 16 16">
      <g class="es-line-heart">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.009 2.9a4.063 4.063 0 0 1 5.745 0c.09.088.17.18.246.273.075-.093.156-.184.244-.272a4.063 4.063 0 0 1 5.746 0c1.587 1.586 1.658 3.968.012 5.733a.57.57 0 0 1-.017.016L8.35 14.148a.5.5 0 0 1-.698 0L2.013 8.651a.501.501 0 0 1-.02-.02C.447 6.94.413 4.495 2.01 2.9Zm5.039.707a3.063 3.063 0 0 0-4.332 0c-1.184 1.184-1.2 3.011.006 4.338L8 13.092l5.278-5.149c1.28-1.379 1.2-3.14.005-4.335a3.063 3.063 0 0 0-4.332 0 3.039 3.039 0 0 0-.512.713.5.5 0 0 1-.879 0 3.022 3.022 0 0 0-.512-.713Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
        <symbol id="es-small-add" viewBox="0 0 16 16">
      <g class="es-small-add">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8.5 3.188a.5.5 0 0 0-1 0V7.5H3.187a.5.5 0 0 0 0 1H7.5v4.313a.5.5 0 0 0 1 0V8.5h4.313a.5.5 0 0 0 0-1H8.5V3.187Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-small-right" viewBox="0 0 16 16">
      <g class="es-small-right">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M5.747 12.729a.5.5 0 0 1 0-.708L9.768 8 5.747 3.979a.5.5 0 1 1 .707-.708l4.375 4.375a.5.5 0 0 1 0 .708l-4.375 4.375a.5.5 0 0 1-.707 0Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-small-down" viewBox="0 0 16 16">
      <g class="es-small-down">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.271 5.846a.5.5 0 0 1 .708 0L8 9.868l4.021-4.022a.5.5 0 0 1 .708.707l-4.375 4.375a.5.5 0 0 1-.708 0L3.271 6.553a.5.5 0 0 1 0-.707Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-small-left" viewBox="0 0 16 16">
      <g class="es-small-left">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M10.254 3.271a.5.5 0 0 1 0 .708L6.232 8l4.022 4.021a.5.5 0 0 1-.708.708L5.171 8.354a.5.5 0 0 1 0-.708l4.375-4.375a.5.5 0 0 1 .708 0Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-small-up" viewBox="0 0 16 16">
      <g class="es-small-up">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M12.729 10.154a.5.5 0 0 1-.708 0L8 6.132l-4.021 4.022a.5.5 0 1 1-.708-.707l4.375-4.375a.5.5 0 0 1 .708 0l4.375 4.374a.5.5 0 0 1 0 .708Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-small-close" viewBox="0 0 16 16">
      <g class="es-small-close">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.846 4.553a.5.5 0 1 1 .707-.707L8 7.293l3.446-3.447a.5.5 0 0 1 .707.707L8.707 8l3.446 3.446a.5.5 0 0 1-.707.707L8 8.707l-3.447 3.446a.5.5 0 1 1-.707-.707L7.293 8 3.846 4.553Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-close-circle" viewBox="0 0 16 16">
      <g class="es-solid-close-circle">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM5.146 5.146a.5.5 0 0 0 0 .708L7.293 8l-2.147 2.146a.5.5 0 0 0 .708.708L8 8.707l2.146 2.147a.5.5 0 0 0 .708-.708L8.707 8l2.147-2.146a.5.5 0 0 0-.708-.708L8 7.293 5.854 5.146a.5.5 0 0 0-.708 0Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-down" viewBox="0 0 16 16">
      <g class="es-solid-down">
        <path fill="var(--icon-stroke)" d="M4.293 5.28h7.413a.5.5 0 0 1 .41.787L8.408 11.36a.5.5 0 0 1-.82 0L3.884 6.067a.5.5 0 0 1 .41-.787Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-solid-left" viewBox="0 0 16 16">
      <g class="es-solid-left">
        <path fill="var(--icon-stroke)" d="M10.72 4.294v7.413a.5.5 0 0 1-.787.41L4.638 8.41a.5.5 0 0 1 0-.82l5.295-3.706a.5.5 0 0 1 .787.41Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-solid-right" viewBox="0 0 16 16">
      <g class="es-solid-right">
        <path fill="var(--icon-stroke)" d="M5.4 11.707V4.293a.5.5 0 0 1 .786-.41l5.295 3.707a.5.5 0 0 1 0 .819l-5.295 3.706a.5.5 0 0 1-.787-.41Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-solid-up" viewBox="0 0 16 16">
      <g class="es-solid-up">
        <path fill="var(--icon-stroke)" d="M4.293 10.718h7.413a.5.5 0 0 0 .41-.787L8.408 4.636a.5.5 0 0 0-.82 0L3.884 9.931a.5.5 0 0 0 .41.787Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-solid-alert-triangle" viewBox="0 0 16 16">
      <g class="es-solid-alert-triangle">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8.646 2.225a.75.75 0 0 0-1.294 0L.946 13.162a.75.75 0 0 0 .647 1.13h12.812c.58 0 .94-.63.647-1.13L8.646 2.225ZM8 5.762a.5.5 0 0 1 .5.5v3.25a.5.5 0 1 1-1 0v-3.25a.5.5 0 0 1 .5-.5Zm0 6.655a.875.875 0 1 0 0-1.75.875.875 0 0 0 0 1.75Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-alert-circle" viewBox="0 0 16 16">
      <g class="es-solid-alert-circle">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14Zm0-9.25A.875.875 0 1 0 8 4a.875.875 0 0 0 0 1.75Zm0 1.186a.5.5 0 0 1 .5.5v3.707a.5.5 0 0 1-1 0V7.436a.5.5 0 0 1 .5-.5Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-success" viewBox="0 0 16 16">
      <g class="es-solid-success">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14Zm3.173-9.015a.5.5 0 0 0-.772-.636l-3.45 4.183L5.612 7.8a.5.5 0 1 0-.792.612l1.725 2.228a.5.5 0 0 0 .781.013l3.848-4.667Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-dot" viewBox="0 0 16 16">
      <g class="es-solid-dot">
        <path fill="var(--icon-stroke)" d="M11.5 8a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-solid-fire" viewBox="0 0 16 16">
      <g class="es-solid-fire">
        <path fill="#E86C13" d="M9.717 6.577s.622-3.656-1.915-5.244a4.475 4.475 0 0 1-1.688 3.24c-1.083.952-3.12 3.094-3.1 5.377a5.237 5.237 0 0 0 2.877 4.717 3.32 3.32 0 0 1 1.161-2.296 2.711 2.711 0 0 0 1.039-1.821 4.88 4.88 0 0 1 2.583 4.067v.011a4.903 4.903 0 0 0 2.857-4.255c.18-2.145-.995-5.061-2.038-6.015a5.66 5.66 0 0 1-1.776 2.22Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-solid-pin" viewBox="0 0 16 16">
      <g class="es-solid-pin">
        <path fill="var(--icon-stroke)" d="M8.384 1.816a2.553 2.553 0 0 1 3.409.182l2.46 2.46a2.553 2.553 0 0 1 .181 3.408l-3.031 3.758a1.54 1.54 0 0 0-.316.696l-.28 1.585a1.021 1.021 0 0 1-1.728.545l-3.082-3.136-1.99 1.99a.75.75 0 1 1-1.06-1.061l1.99-1.99L1.8 7.17a1.021 1.021 0 0 1 .545-1.728l1.585-.28a1.54 1.54 0 0 0 .696-.316l3.758-3.031Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-solid-user" viewBox="0 0 16 16">
      <g class="es-solid-user">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M4.5 4.722C4.5 6.532 6.067 8 8 8s3.5-1.468 3.5-3.278v-.444C11.5 2.468 9.933 1 8 1S4.5 2.468 4.5 4.278v.444ZM.971 13.239c.073-.968.323-2.1 1.029-2.739C3.512 9.132 6.516 9 8 9s4.488.132 6 1.5c.707.64.956 1.77 1.029 2.74.075 1.004-.785 1.76-1.793 1.76H2.764C1.756 15 .896 14.244.97 13.24Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-google" viewBox="0 0 16 16">
      <g class="es-solid-google">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M10.464 5.059a3.82 3.82 0 0 0-2.466-.9 3.845 3.845 0 0 0-3.643 2.634 3.823 3.823 0 0 0 0 2.411 3.845 3.845 0 0 0 3.643 2.635 4.13 4.13 0 0 0 2.199-.543l2.16 1.666a6.459 6.459 0 0 1-4.359 1.536 6.505 6.505 0 0 1-5.82-3.615 6.48 6.48 0 0 1 0-5.768 6.49 6.49 0 0 1 10.218-1.896l-1.932 1.84Zm3.768 1.758H8.029v2.659h3.515c-.21.751-.689 1.4-1.346 1.82l2.161 1.666c1.381-1.24 2.193-3.255 1.873-6.145Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-github" viewBox="0 0 16 16">
      <g class="es-solid-github">
        <path fill="#383838" fill-rule="evenodd" d="M7.914 1.5C4.111 1.5 1 4.611 1 8.414c0 3.025 1.988 5.618 4.754 6.569.345.086.432-.173.432-.346v-1.21c-1.902.432-2.334-.864-2.334-.864-.346-.778-.778-1.037-.778-1.037-.605-.432.087-.432.087-.432.691.086 1.037.691 1.037.691.605 1.124 1.642.778 1.988.605.086-.432.259-.778.432-.95-1.556-.174-3.112-.779-3.112-3.458 0-.778.26-1.383.692-1.815-.087-.173-.346-.864.086-1.815 0 0 .605-.173 1.902.692a5.573 5.573 0 0 1 1.728-.26c.605 0 1.21.087 1.729.26 1.296-.865 1.901-.692 1.901-.692.346.95.173 1.642.087 1.815.432.519.691 1.124.691 1.815 0 2.68-1.642 3.198-3.198 3.37.26.347.519.779.519 1.384v1.901c0 .173.086.432.518.346 2.766-.95 4.754-3.544 4.754-6.569-.086-3.803-3.198-6.914-7-6.914Z" class="Vector" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-facebook" viewBox="0 0 16 16">
      <g class="es-solid-facebook">
        <path fill="var(--icon-stroke)" d="M14.833 8.04a6.791 6.791 0 1 0-7.853 6.71v-4.746H5.256V8.041H6.98V6.544c0-1.701 1.015-2.642 2.566-2.642.509.007 1.017.052 1.52.132v1.672h-.857a.981.981 0 0 0-1.106 1.061v1.274h1.882l-.301 1.963H9.099v4.746a6.792 6.792 0 0 0 5.734-6.71Z" class="Vector"/>
      </g>
    </symbol>
    <symbol id="es-solid-apple" viewBox="0 0 16 16">
      <g class="es-solid-apple">
        <g class="Frame">
          <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M10.65 1c.131.884-.231 1.75-.709 2.362C9.43 4.018 8.55 4.526 7.698 4.5c-.155-.846.244-1.717.727-2.304C8.958 1.55 9.87 1.053 10.649 1Zm1.71 12.132c.39-.592.535-.892.838-1.562-2.197-.83-2.55-3.936-.376-5.128-.663-.827-1.595-1.306-2.475-1.306-.635 0-1.07.165-1.465.314-.329.124-.63.238-.997.238-.396 0-.747-.125-1.115-.256-.404-.144-.828-.295-1.355-.295-.989 0-2.04.6-2.705 1.626-.938 1.443-.778 4.157.74 6.47.545.828 1.27 1.758 2.22 1.767.393.004.655-.113.938-.24.325-.144.678-.301 1.29-.304.614-.004.961.155 1.281.302.277.127.533.244.923.24.95-.009 1.715-1.04 2.259-1.866Z" class="Union" clip-rule="evenodd"/>
        </g>
      </g>
    </symbol>
    <symbol id="es-solid-tag" viewBox="0 0 16 16">
      <g class="es-solid-tag">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M9.222 14.878a1.273 1.273 0 0 0 1.8 0l3.856-3.857a1.273 1.273 0 0 0 0-1.8l-6.17-6.17A4 4 0 0 0 3.05 8.707l6.17 6.171ZM6.409 4.641A1.25 1.25 0 1 0 4.641 6.41 1.25 1.25 0 0 0 6.41 4.641Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-knob" width="20" height="20" viewBox="0 0 20 20">
      <g class="es-solid-knob">
        <g class="Union" filter="url(#a)">
          <path fill="#fff" d="M18 9A8 8 0 1 1 2 9a8 8 0 0 1 16 0Z"/>
        </g>
      </g>
      <defs>
        <filter id="a" width="20" height="20" x="0" y="0" class="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
          <feOffset dy="1"/>
          <feGaussianBlur stdDeviation="1"/>
          <feComposite in2="hardAlpha" operator="out"/>
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_5_8060"/>
          <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
          <feOffset/>
          <feGaussianBlur stdDeviation=".5"/>
          <feComposite in2="hardAlpha" operator="out"/>
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
          <feBlend in2="effect1_dropShadow_5_8060" result="effect2_dropShadow_5_8060"/>
          <feBlend in="SourceGraphic" in2="effect2_dropShadow_5_8060" result="shape"/>
        </filter>
      </defs>
    </symbol>
    <symbol id="es-solid-home" viewBox="0 0 16 16">
      <g class="es-solid-home" filter="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7.408 1.935 2.315 5.678A2 2 0 0 0 1.5 7.29v5.21a2 2 0 0 0 2 2h2.675v-3.65a1.825 1.825 0 0 1 3.65 0v3.65H12.5a2 2 0 0 0 2-2V7.29a2 2 0 0 0-.816-1.612L8.593 1.935a1 1 0 0 0-1.184 0Z" class="Subtract" clip-rule="evenodd"/>
      </g>
      <defs>
        <filter id="a" width="24" height="24" x="-4" y="-4" class="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_84_50816"/>
          <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_84_50816" result="shape"/>
        </filter>
      </defs>
    </symbol>
    <symbol id="es-solid-star" viewBox="0 0 16 16">
      <g class="es-solid-star">
        <path fill="var(--icon-stroke)" d="M7.357 2.068a.75.75 0 0 1 1.286 0l1.78 2.958 3.363.779a.75.75 0 0 1 .397 1.222L11.92 9.634l.299 3.44a.75.75 0 0 1-1.04.755L8 12.482l-3.179 1.347a.75.75 0 0 1-1.04-.756l.3-3.439-2.264-2.607a.75.75 0 0 1 .397-1.222l3.363-.78 1.78-2.957Z" class="Star 3"/>
      </g>
    </symbol>
    <symbol id="es-solid-notification" viewBox="0 0 16 16">
      <g class="es-solid-notification">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.5 11h.272c.112 0 .203-.09.203-.202L3 7c-.024-1.558.875-3.708 3.044-4.384A.628.628 0 0 0 6.5 2.03V2a1.5 1.5 0 1 1 3 0v.03c0 .273.195.505.456.586C12.122 3.292 13 5.442 13 7l-.024 3.788a.21.21 0 0 0 .21.212h.315a1 1 0 1 1 0 2H2.5a1 1 0 1 1 0-2Zm7.25 3a1.75 1.75 0 1 1-3.5 0h3.5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-heading" viewBox="0 0 16 16">
      <g class="es-solid-heading">
        <path fill="var(--icon-stroke)" d="M3.242 14h2.016V9.11h5.484V14h2.008V2.727h-2.008v4.687H5.258V2.727H3.242V14Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-solid-text" viewBox="0 0 16 16">
      <g class="es-solid-text">
        <path fill="var(--icon-stroke)" d="M1.5 3v2.053h3.421v8.21h2.053v-8.21h3.42V3H1.5Zm13 3.421H8.342v2.053h2.053v4.79h2.052v-4.79H14.5V6.42Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-solid-location" viewBox="0 0 16 16">
      <g class="es-solid-location">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M1 8c0-3.85 3.15-7 7-7s7 3.15 7 7-3.15 7-7 7-7-3.15-7-7Zm9.963-2.076-1.308 2.83c-.28.378-.561.661-.935.85l-2.804 1.32c-.561.284-1.122-.282-.841-.848l1.308-2.83c.187-.378.468-.756.841-.85l2.805-1.32c.56-.284 1.121.282.934.848Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-calendar" viewBox="0 0 16 16">
      <g class="es-solid-calendar">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M1.327 2.638C1 3.28 1 4.12 1 5.8v4.4c0 1.68 0 2.52.327 3.162a3 3 0 0 0 1.311 1.311C3.28 15 4.12 15 5.8 15h4.4c1.68 0 2.52 0 3.162-.327a3 3 0 0 0 1.311-1.311C15 12.72 15 11.88 15 10.2V5.8c0-1.68 0-2.52-.327-3.162a3 3 0 0 0-1.311-1.311C12.72 1 11.88 1 10.2 1H5.8c-1.68 0-2.52 0-3.162.327a3 3 0 0 0-1.311 1.311ZM4.5 3.75a.75.75 0 0 0 0 1.5h7a.75.75 0 0 0 0-1.5h-7Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-title" viewBox="0 0 16 16">
      <g class="es-solid-title">
        <path fill="#171B1F" fill-rule="evenodd" d="M2.063 7.618a.674.674 0 0 1-.64-.889l1.294-3.848a1 1 0 0 1 .948-.681h.486v.029a1 1 0 0 1 .71.652l1.296 3.848a.674.674 0 1 1-1.287.397l-.195-.694h-1.77l-.193.693a.674.674 0 0 1-.65.493Zm1.12-2.181h1.214l-.572-2.043h-.072l-.57 2.043Zm3.86-2.195a.75.75 0 0 1 .75-.75h6.46a.75.75 0 0 1 0 1.5h-6.46a.75.75 0 0 1-.75-.75Zm0 3.334a.75.75 0 0 1 .75-.75h6.46a.75.75 0 0 1 0 1.5h-6.46a.75.75 0 0 1-.75-.75ZM1.75 9.161a.75.75 0 0 0 0 1.5h12.504a.75.75 0 0 0 0-1.5H1.75ZM1 13.245a.75.75 0 0 1 .75-.75h5.835a.75.75 0 0 1 0 1.5H1.75a.75.75 0 0 1-.75-.75Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-lighting" viewBox="0 0 16 16">
      <g class="es-solid-lighting">
        <path fill="#171B1F" d="M7.258 2.608c.343-.4.992-.065.864.447L7.363 6.09h6.55a.5.5 0 0 1 .38.825l-5.551 6.476c-.344.4-.993.065-.865-.447l.76-3.036h-6.55a.5.5 0 0 1-.38-.825l5.55-6.476Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-solid-stage" viewBox="0 0 16 16">
      <g class="es-solid-stage">
        <path fill="#171B1F" fill-rule="evenodd" d="M8 1a2.333 2.333 0 1 0 0 4.667A2.333 2.333 0 0 0 8 1ZM3.333 5.667a2.333 2.333 0 1 0 0 4.666 2.333 2.333 0 0 0 0-4.666Zm2.333 7a2.333 2.333 0 1 1 4.667 0 2.333 2.333 0 0 1-4.667 0Zm7-7a2.333 2.333 0 1 0 0 4.666 2.333 2.333 0 0 0 0-4.666Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-organization" viewBox="0 0 16 16">
      <g class="es-solid-organization">
        <path fill="#171B1F" fill-rule="evenodd" d="M8.45 7.1h3.36c.784 0 1.176 0 1.476.153a1.4 1.4 0 0 1 .611.611c.153.3.153.692.153 1.476v4.76h.25a.45.45 0 0 1 0 .9H1.2a.45.45 0 0 1 0-.9h.25V3.74c0-.784 0-1.176.153-1.476a1.4 1.4 0 0 1 .611-.611c.3-.153.692-.153 1.476-.153h2.52c.784 0 1.176 0 1.476.153a1.4 1.4 0 0 1 .611.611c.153.3.153.692.153 1.476V7.1ZM3.932 3.666a.75.75 0 0 0 0 1.5h2.187a.75.75 0 0 0 0-1.5H3.932Zm0 2.917a.75.75 0 1 0 0 1.5h2.187a.75.75 0 0 0 0-1.5H3.932Zm0 2.917a.75.75 0 0 0 0 1.5h2.187a.75.75 0 0 0 0-1.5H3.932Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-location" viewBox="0 0 16 16">
      <g class="es-solid-location">
        <path fill="#171B1F" d="M8.115 1A6.078 6.078 0 0 0 2 7.115c0 1.66.612 3.233 1.835 4.369.087.087 3.582 3.232 3.669 3.32.35.261.874.261 1.136 0 .087-.088 3.669-3.233 3.669-3.32 1.223-1.136 1.835-2.709 1.835-4.369C14.23 3.708 11.522 1 8.114 1Zm0 7.863a1.752 1.752 0 0 1-1.747-1.748c0-.96.786-1.747 1.747-1.747.961 0 1.748.786 1.748 1.747 0 .961-.787 1.748-1.748 1.748Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-solid-location-alt" viewBox="0 0 16 16">
      <g class="es-solid-location-alt">
        <path fill="#171B1F" fill-rule="evenodd" d="M9.875 2.633 6.125 1.3v12.133l3.75 1.334V2.633Zm1.5 12.08 2.774-1.183A1.4 1.4 0 0 0 15 12.242v-9.09a1.4 1.4 0 0 0-1.95-1.287l-1.675.715v12.133ZM1.851 2.537l2.774-1.184v12.134l-1.676.715A1.4 1.4 0 0 1 1 12.914v-9.09a1.4 1.4 0 0 1 .85-1.287Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-type" viewBox="0 0 16 16">
      <g class="es-solid-type">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM5.207 7h5.586a.5.5 0 0 0 .353-.854L8.353 3.353a.5.5 0 0 0-.707 0L4.854 6.146A.5.5 0 0 0 5.207 7Zm5.586 2H5.207a.5.5 0 0 0-.353.853l2.439 2.44a1 1 0 0 0 1.414 0l2.44-2.44A.5.5 0 0 0 10.792 9Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-text-alt" viewBox="0 0 16 16">
      <g class="es-solid-text-alt">
        <path fill="var(--icon-stroke)" d="M2.28 12.27H.5L3.822 3h2.022l3.316 9.27H7.344L4.83 4.78H4.8l-2.52 7.49Zm.155-3.69h4.784v1.35H2.435V8.58Zm9.5 3.802c-.44 0-.832-.073-1.176-.218a1.85 1.85 0 0 1-.809-.665c-.194-.295-.292-.662-.292-1.102 0-.377.07-.688.212-.933.14-.245.331-.44.572-.585.24-.149.516-.261.827-.336a6.84 6.84 0 0 1 .97-.168c.407-.045.73-.085.971-.118.241-.037.413-.091.517-.162.104-.07.155-.178.155-.323v-.05a.856.856 0 0 0-.143-.491.901.901 0 0 0-.398-.33 1.534 1.534 0 0 0-.634-.118c-.25 0-.47.04-.66.118a1.11 1.11 0 0 0-.448.33.9.9 0 0 0-.199.479l-1.55-.038c.038-.406.175-.771.412-1.095.236-.323.561-.578.976-.765.42-.19.915-.286 1.487-.286.415 0 .792.054 1.133.162.34.103.632.253.877.448s.433.427.566.696c.133.27.199.57.199.903v4.535h-1.593v-.933h-.03a1.846 1.846 0 0 1-1.039.908c-.25.091-.55.137-.903.137Zm.41-1.188c.332 0 .612-.06.84-.18a1.32 1.32 0 0 0 .53-.492c.12-.204.18-.432.18-.685v-.715a.844.844 0 0 1-.224.093c-.095.03-.21.058-.342.087a16.31 16.31 0 0 1-.865.143 2.55 2.55 0 0 0-.616.156c-.183.07-.33.168-.442.292a.698.698 0 0 0-.168.486.7.7 0 0 0 .299.597c.203.145.473.218.809.218Z" class="Aa"/>
      </g>
    </symbol>
    <symbol id="es-solid-everyone" viewBox="0 0 16 16">
      <g class="es-solid-everyone">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM6.957 4.004a.65.65 0 0 1 .574.718l-.127 1.14H9.1l.118-1.272a.65.65 0 0 1 1.295.12l-.108 1.152h.95a.65.65 0 0 1 0 1.3H7.26l-.187 1.68h1.672l.101-.912a.65.65 0 0 1 1.292.144l-.085.768h1.302a.65.65 0 0 1 0 1.3H9.909l-.143 1.284a.65.65 0 1 1-1.292-.143l.127-1.14H6.929l-.143 1.283a.65.65 0 1 1-1.292-.143l.127-1.14H4.65a.65.65 0 1 1 0-1.3h1.115l.187-1.68H4.65a.65.65 0 1 1 0-1.3h1.446l.143-1.285a.65.65 0 0 1 .718-.574Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-black" width="22" height="16" viewBox="0 0 22 16">
      <g class="es-solid-black" clip-path="url(#a)">
        <rect width="22" height="16" fill="#fff" rx="4"/>
        <g class="Frame 838235">
          <rect width="21" height="15" x=".5" y=".5" rx="4.5"/>
          <rect width="21" height="15" x=".5" y=".5" stroke="#E2E2E2" rx="4.5"/>
        </g>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <rect width="22" height="16" fill="#fff" rx="4"/>
        </clipPath>
      </defs>
    </symbol>
    <symbol id="es-solid-white" width="22" height="16" viewBox="0 0 22 16">
      <g class="es-solid-white" clip-path="url(#a)">
        <rect width="22" height="16" fill="#fff" rx="4"/>
        <g class="Frame 838235">
          <rect width="21" height="15" x=".5" y=".5" fill="#fff" rx="4.5"/>
          <rect width="21" height="15" x=".5" y=".5" stroke="#EDEDED" rx="4.5"/>
        </g>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <rect width="22" height="16" fill="#fff" rx="4"/>
        </clipPath>
      </defs>
    </symbol>
    <symbol id="icon/line/quote-alt" viewBox="0 0 16 16">
      <g class="icon/line/quote-alt">
        <path fill="#171B1F" fill-rule="evenodd" d="M5.312 13.5a1.5 1.5 0 0 0 1.5-1.5V9.29a1.5 1.5 0 0 0-1.5-1.5H2.919c.15-2.542 1.15-4.163 3.043-4.945a.7.7 0 0 0-.535-1.292C2.821 2.63 1.5 4.963 1.5 8.489V12A1.5 1.5 0 0 0 3 13.5h2.312Zm7.5 0a1.5 1.5 0 0 0 1.5-1.5V9.29a1.5 1.5 0 0 0-1.5-1.5h-2.394c.15-2.542 1.15-4.163 3.043-4.945a.7.7 0 0 0-.535-1.292C10.321 2.63 9 4.963 9 8.489V12a1.5 1.5 0 0 0 1.5 1.501h2.312Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-external-link" viewBox="0 0 16 16">
      <g class="es-solid-external-link">
        <path fill="var(--icon-stroke)" d="M15 8A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" class="Union"/>
        <path fill="#fff" d="M10.95 5.5a.5.5 0 0 0-.5-.5H6.207a.5.5 0 0 0 0 1H9.32l-4.175 4.175a.5.5 0 1 0 .708.707L9.95 6.786v2.957a.5.5 0 0 0 1 0v-4.15a.5.5 0 0 0 0-.03V5.5Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-solid-arrow-up" viewBox="0 0 16 16">
      <g class="es-solid-arrow-up">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM5.066 6.452l2.58-2.58a.5.5 0 0 1 .708 0l2.58 2.58a.5.5 0 1 1-.707.707L8.5 5.432v6.343a.5.5 0 1 1-1 0V5.432L5.773 7.159a.5.5 0 0 1-.707-.707Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-ticket" viewBox="0 0 16 16">
      <g class="es-solid-ticket">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M.998 4.123a1.5 1.5 0 0 1 1.5-1.5h11.004a1.5 1.5 0 0 1 1.5 1.5V5.91a.5.5 0 0 1-.4.49 1.669 1.669 0 0 0 0 3.268.5.5 0 0 1 .4.49v1.719a1.5 1.5 0 0 1-1.5 1.5H2.498a1.5 1.5 0 0 1-1.5-1.5v-1.719a.5.5 0 0 1 .4-.49 1.669 1.669 0 0 0 0-3.268.5.5 0 0 1-.4-.49V4.123ZM5.41 6.606a.5.5 0 0 1 .5-.5h4.18a.5.5 0 0 1 0 1H5.91a.5.5 0 0 1-.5-.5Zm0 2.787a.5.5 0 0 1 .5-.5h4.18a.5.5 0 0 1 0 1H5.91a.5.5 0 0 1-.5-.5Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-search" viewBox="0 0 16 16">
      <g class="es-solid-search">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M12.002 7.251a4.5 4.5 0 1 1-9.002 0 4.5 4.5 0 0 1 9.002 0Zm-.881 4.787a6 6 0 1 1 1.074-1.047l2.336 2.336a.75.75 0 0 1-1.06 1.06l-2.35-2.35Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-people" viewBox="0 0 16 16">
      <g class="es-solid-people">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M10.837 5.695a2.063 2.063 0 1 0 0-4.127 2.063 2.063 0 0 0 0 4.127ZM8.165 6.98a3.589 3.589 0 0 1 1.285-.238h2.257a3.595 3.595 0 0 1 3.51 2.815l.074.336a2.047 2.047 0 0 1-1.999 2.492h-2.197a.5.5 0 0 1-.5-.486c-.011-.407-.168-1.45-.672-2.243-.233-.365-.62-.707-1.04-.99A5.467 5.467 0 0 0 7.805 8.1a.5.5 0 0 1-.075-.905l.032-.018c.117-.067.231-.132.402-.197ZM4.293 8.29a3.595 3.595 0 0 0-3.51 2.815l-.074.335a2.048 2.048 0 0 0 1.999 2.492h5.426a2.048 2.048 0 0 0 2-2.492l-.075-.335a3.595 3.595 0 0 0-3.51-2.815H4.293ZM5.42 7.242a2.321 2.321 0 1 0 0-4.642 2.321 2.321 0 0 0 0 4.642Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-customer" viewBox="0 0 16 16">
      <g class="es-solid-customer">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M1 7.75a7 7 0 1 1 12.068 4.829l.005.009-.24.225A6.975 6.975 0 0 1 8 14.75a6.98 6.98 0 0 1-4.801-1.906l-.273-.257.005-.01A6.976 6.976 0 0 1 1 7.75Zm11.55 3.91A5.494 5.494 0 0 0 8 9.25h-.004c-2.366.02-3.815 1.351-4.546 2.412a6 6 0 1 1 9.1-.001ZM8 3.75a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-book" viewBox="0 0 16 16">
      <g class="es-solid-book">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7.501 3.061a6.248 6.248 0 0 0-1.592-.55 7.6 7.6 0 0 0-1.258-.15H2.236a1.5 1.5 0 0 0-1.5 1.5v9.145a.5.5 0 0 0 .5.5h3.397c.012 0 .031 0 .057.002a6.599 6.599 0 0 1 1.016.129c.576.12 1.244.34 1.795.74V3.061Zm1 11.315c.55-.399 1.218-.62 1.794-.74a6.6 6.6 0 0 1 1.073-.13h3.396a.5.5 0 0 0 .5-.5V3.86a1.5 1.5 0 0 0-1.5-1.5H11.35l-.077.003a7.6 7.6 0 0 0-1.181.149A6.249 6.249 0 0 0 8.5 3.06v11.315Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-report" viewBox="0 0 16 16">
      <g class="es-solid-report">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7.5 2A1.5 1.5 0 0 0 6 3.5v10a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-10A1.5 1.5 0 0 0 8.5 2h-1Zm5 3A1.5 1.5 0 0 0 11 6.5v7a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-7A1.5 1.5 0 0 0 13.5 5h-1ZM1 9.5A1.5 1.5 0 0 1 2.5 8h1A1.5 1.5 0 0 1 5 9.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-4Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-dashboard" viewBox="0 0 16 16">
      <g class="es-solid-dashboard">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.5 3A1.5 1.5 0 0 1 4 1.5h2A1.5 1.5 0 0 1 7.5 3v2A1.5 1.5 0 0 1 6 6.5H4A1.5 1.5 0 0 1 2.5 5V3Zm6 8A1.5 1.5 0 0 1 10 9.5h2a1.5 1.5 0 0 1 1.5 1.5v2a1.5 1.5 0 0 1-1.5 1.5h-2A1.5 1.5 0 0 1 8.5 13v-2ZM10 1.5A1.5 1.5 0 0 0 8.5 3v4A1.5 1.5 0 0 0 10 8.5h2A1.5 1.5 0 0 0 13.5 7V3A1.5 1.5 0 0 0 12 1.5h-2ZM2.5 9A1.5 1.5 0 0 1 4 7.5h2A1.5 1.5 0 0 1 7.5 9v4A1.5 1.5 0 0 1 6 14.5H4A1.5 1.5 0 0 1 2.5 13V9Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-settings" viewBox="0 0 16 16">
      <g class="es-solid-settings">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M6.589 1.346A.634.634 0 0 1 7.154 1h1.692c.239 0 .457.133.565.346a1.917 1.917 0 0 0 2.296.95.634.634 0 0 1 .644.156l1.197 1.197a.634.634 0 0 1 .155.643c-.29.896.114 1.869.951 2.297a.634.634 0 0 1 .346.565v1.692a.634.634 0 0 1-.346.565 1.917 1.917 0 0 0-.95 2.297.634.634 0 0 1-.156.643l-.598.599-.01.009-.632.601a.632.632 0 0 1-.633.142 1.872 1.872 0 0 0-2.25.927l-.014.026a.634.634 0 0 1-.564.345H7.154a.634.634 0 0 1-.565-.346 1.917 1.917 0 0 0-2.297-.95.634.634 0 0 1-.643-.156l-1.197-1.197a.634.634 0 0 1-.155-.644 1.917 1.917 0 0 0-.951-2.296A.634.634 0 0 1 1 8.846V7.153c0-.24.135-.457.345-.564a1.892 1.892 0 0 0 .938-2.276.632.632 0 0 1 .152-.648L3.65 2.452a.634.634 0 0 1 .643-.155 1.917 1.917 0 0 0 2.297-.951ZM8 10.25a2.25 2.25 0 1 0 0-4.5 2.25 2.25 0 0 0 0 4.5Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-details" viewBox="0 0 16 16">
      <g class="es-solid-details">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.5 1.5a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2-2v-9a2 2 0 0 0-2-2h-9Zm2 3.5a.625.625 0 1 1-1.25 0A.625.625 0 0 1 5.5 5Zm0 3a.625.625 0 1 1-1.25 0A.625.625 0 0 1 5.5 8Zm-.625 3.625a.625.625 0 1 0 0-1.25.625.625 0 0 0 0 1.25ZM6.25 5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Zm.5 2.5a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1h-5Zm-.5 3.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-play" viewBox="0 0 16 16">
      <g class="es-solid-play">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M1 8a7 7 0 1 1 14 0A7 7 0 0 1 1 8Zm6.13 2.684 3.387-2.237a.521.521 0 0 0 .26-.447.521.521 0 0 0-.26-.447L7.131 5.316a.67.67 0 0 0-.645-.06.623.623 0 0 0-.375.507v4.474a.623.623 0 0 0 .375.507.67.67 0 0 0 .645-.06Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-agent" viewBox="0 0 16 16">
      <g class="es-solid-agent" filter="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 7.115a2.625 2.625 0 1 0 0-5.25 2.625 2.625 0 0 0 0 5.25Zm-1.402.75a4.347 4.347 0 0 0-4.243 3.404l-.093.417a2.424 2.424 0 0 0 2.366 2.95h6.745a2.424 2.424 0 0 0 2.366-2.95l-.093-.417a4.347 4.347 0 0 0-4.243-3.404h-.319L8 10.184l-1.084-2.32h-.318Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <filter id="a" width="24" height="24" x="-4" y="-4" class="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_917_70473"/>
          <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_917_70473" result="shape"/>
        </filter>
      </defs>
    </symbol>
    <symbol id="es-solid-youtube" viewBox="0 0 16 16">
      <g class="es-solid-youtube">
        <path fill="var(--icon-stroke)" d="M14.883 5.1s-.116-.992-.583-1.4c-.525-.583-1.108-.583-1.4-.583C10.917 3 8 3 8 3s-2.917 0-4.9.117c-.292.058-.875.058-1.4.583-.408.408-.583 1.4-.583 1.4S1 6.208 1 7.375v1.05c0 1.108.117 2.275.117 2.275s.116.992.583 1.4c.525.583 1.225.525 1.517.583C4.325 12.8 8 12.8 8 12.8s2.917 0 4.9-.175c.292-.058.875-.058 1.4-.583.408-.409.583-1.4.583-1.4S15 9.533 15 8.367v-1.05a24.26 24.26 0 0 0-.117-2.217ZM6.542 9.708V5.8l3.791 1.983-3.791 1.925Z" class="Vector"/>
      </g>
    </symbol>
    <symbol id="es-solid-quiz" viewBox="0 0 16 16">
      <g class="es-solid-quiz">
        <g class="Group 482431">
          <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.5 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h9a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2h-9Zm5 3.5A.5.5 0 0 1 9 4h3a.5.5 0 0 1 0 1H9a.5.5 0 0 1-.5-.5Zm0 3A.5.5 0 0 1 9 7h3a.5.5 0 0 1 0 1H9a.5.5 0 0 1-.5-.5ZM9 11a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1H9Zm-2.173-.935a.5.5 0 0 1 .051.705l-1.696 1.964a.5.5 0 0 1-.75.008l-.804-.893a.5.5 0 1 1 .744-.67l.424.472 1.326-1.535a.5.5 0 0 1 .705-.051Z" class="Subtract" clip-rule="evenodd"/>
          <path fill="var(--icon-stroke)" d="M4.5 4a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1h-2Z" class="Union"/>
          <path fill="#fff" fill-rule="evenodd" d="M4.5 5v2h2V5h-2Zm-1 0a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1V5Z" class="Union (Stroke)" clip-rule="evenodd"/>
        </g>
      </g>
    </symbol>
    <symbol id="es-solid-image" viewBox="0 0 16 16">
      <g class="es-solid-image">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M3.5 1.5h9A2.5 2.5 0 0 1 15 4v6.214l-4.29-3.011a.9.9 0 0 0-1.14.086l-7.233 6.925A2.5 2.5 0 0 1 1 12V4a2.5 2.5 0 0 1 2.5-2.5Zm-.016 13H12.5A2.5 2.5 0 0 0 15 12v-.567a.507.507 0 0 1-.037-.024l-4.76-3.34L3.484 14.5Z" class="Subtract" clip-rule="evenodd"/>
        <path fill="var(--icon-stroke)" d="M5.25 7.5a1.75 1.75 0 1 0 0-3.5 1.75 1.75 0 0 0 0 3.5Z" class="Vector"/>
        <path fill="#fff" fill-rule="evenodd" d="M5.25 6.5a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5ZM7 5.75a1.75 1.75 0 1 1-3.5 0 1.75 1.75 0 0 1 3.5 0Z" class="Vector (Stroke)" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="icon/soldi/support" viewBox="0 0 16 16">
      <g class="icon/soldi/support" filter="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7.5 14.982V10.96A3.002 3.002 0 0 1 5.041 8.5H1.018A7.001 7.001 0 0 0 7.5 14.982ZM1 8.008v-.015.015Zm.018-.508H5.04A3.003 3.003 0 0 1 7.5 5.041V1.018A7.001 7.001 0 0 0 1.018 7.5ZM7.992 1h.016-.016Zm6.99 7.5A7.001 7.001 0 0 1 8.5 14.982V10.96a3.002 3.002 0 0 0 2.459-2.46h4.023Zm0-1H10.96A3.002 3.002 0 0 0 8.5 5.041V1.018A7.001 7.001 0 0 1 14.982 7.5Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <filter id="a" width="24" height="24" x="-4" y="-4" class="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1006_70498"/>
          <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_1006_70498" result="shape"/>
        </filter>
      </defs>
    </symbol>
    <symbol id="es-solid-ticket-alt" viewBox="0 0 16 16">
      <g class="es-solid-ticket-alt">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M5 2.623V3.95a.5.5 0 0 0 1 0V2.623h7.502a1.5 1.5 0 0 1 1.5 1.5V5.91a.5.5 0 0 1-.4.49 1.669 1.669 0 0 0 0 3.268.5.5 0 0 1 .4.49v1.719a1.5 1.5 0 0 1-1.5 1.5H6V12.05a.5.5 0 1 0-1 0v1.327H2.498a1.5 1.5 0 0 1-1.5-1.5v-1.719a.5.5 0 0 1 .4-.49 1.669 1.669 0 0 0 0-3.268.5.5 0 0 1-.4-.49V4.123a1.5 1.5 0 0 1 1.5-1.5H5ZM6 6.65a.5.5 0 1 0-1 0v2.7a.5.5 0 0 0 1 0v-2.7Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-agent" viewBox="0 0 16 16">
      <g class="es-solid-agent" filter="url(#a)">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 7.115a2.625 2.625 0 1 0 0-5.25 2.625 2.625 0 0 0 0 5.25Zm-5.645 4.154a4.347 4.347 0 0 1 4.243-3.404h2.805a4.347 4.347 0 0 1 4.243 3.404l.093.417a2.424 2.424 0 0 1-2.366 2.95H4.628a2.424 2.424 0 0 1-2.366-2.95l.093-.417Z" class="Union" clip-rule="evenodd"/>
      </g>
      <defs>
        <filter id="a" width="24" height="24" x="-4" y="-4" class="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1045_70490"/>
          <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_1045_70490" result="shape"/>
        </filter>
      </defs>
    </symbol>
    <symbol id="es-solid-pdf" viewBox="0 0 16 16">
      <g class="es-solid-pdf">
        <path fill="#E03636" fill-rule="evenodd" d="M4 1a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm1.809 9.677C4.634 12.757 4.023 13 3.664 13a.65.65 0 0 1-.467-.2.635.635 0 0 1-.186-.58c.133-.755 1.54-1.53 2.421-1.948.31-.557.63-1.194.954-1.894.288-.621.55-1.258.759-1.848-.45-1.06-.798-2.492-.35-3.177A.748.748 0 0 1 7.448 3a.69.69 0 0 1 .587.294c.346.487.237 1.568-.325 3.213.207.459.452.89.727 1.286.293.42.717.82 1.23 1.158a9.584 9.584 0 0 1 1.41-.115c.863 0 1.444.151 1.73.45.134.14.2.314.192.5-.006.137-.087.582-.874.582-.712 0-1.712-.338-2.572-.866a13.19 13.19 0 0 0-1.59.37c-.734.217-1.477.495-2.154.805Zm6.617-1.027c-.064-.068-.352-.288-1.349-.288-.198 0-.407.01-.626.027.665.31 1.283.453 1.674.453.25 0 .338-.058.348-.073.002-.036-.003-.073-.047-.12ZM7.448 3.526a.229.229 0 0 0-.212.115c-.187.286-.17 1.076.168 2.09.366-1.245.356-1.915.202-2.132-.031-.044-.07-.073-.158-.073Zm0 3.698c-.174.451-.372.918-.584 1.375-.214.462-.427.898-.636 1.302l.02-.01-.016.03c.514-.209 1.05-.397 1.581-.554.364-.108.729-.2 1.09-.277l-.023-.015.053-.008a4.74 4.74 0 0 1-.928-.974 8.43 8.43 0 0 1-.538-.885l-.011.032-.008-.016ZM4.933 11.12c-.908.507-1.36.945-1.403 1.192-.007.04-.003.073.04.116.04.04.07.046.095.046.077 0 .455-.075 1.268-1.354Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-inprogress" viewBox="0 0 16 16">
      <g class="es-solid-inprogress">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14Zm0-1.75a5.25 5.25 0 1 1 0-10.5 5.25 5.25 0 0 1 0 10.5Zm2.828-2.422A4 4 0 0 1 8 12V4a4 4 0 0 1 2.828 6.828Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-folder-alt" viewBox="0 0 16 16">
      <g class="es-solid-folder-alt">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M.842 3.258a2 2 0 0 1 2-2h2.754a1.5 1.5 0 0 1 1.268.699l1.223 1.935h5.07a2 2 0 0 1 2 2v6.35a2.5 2.5 0 0 1-2.5 2.5H3.343a2.5 2.5 0 0 1-2.5-2.5V3.258Zm12.316-.366a2.99 2.99 0 0 1 1.998.762 2 2 0 0 0-1.998-1.907H7.914l.724 1.145h4.52Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-notes" width="16" height="17" viewBox="0 0 16 17">
      <g class="es-solid-notes">
        <path fill="#E79913" fill-rule="evenodd" d="M4 1.5a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V7h-3a2.5 2.5 0 0 1-2.5-2.5v-3H4Zm6.5.093V4.5A1.5 1.5 0 0 0 12 6h2.907a3 3 0 0 0-.786-1.379L11.88 2.38a3 3 0 0 0-1.379-.786ZM4 8a.5.5 0 0 0 0 1h6a.5.5 0 0 0 0-1H4Zm0 3a.5.5 0 0 0 0 1h4a.5.5 0 0 0 0-1H4Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-video" viewBox="0 0 16 16">
      <g class="es-solid-video">
        <path fill="#E86C13" fill-rule="evenodd" d="M4 1a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm3.093 9.876L10.72 8.48A.559.559 0 0 0 11 8a.559.559 0 0 0-.279-.48L7.093 5.125a.718.718 0 0 0-.691-.064.667.667 0 0 0-.402.543v4.794a.667.667 0 0 0 .402.543.718.718 0 0 0 .69-.064Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-zip" viewBox="0 0 16 16">
      <g class="es-solid-zip">
        <g class="Frame 482434" clip-path="url(#a)">
          <path fill="#E2E2E2" d="M1 4a3 3 0 0 1 3-3h8a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3H4a3 3 0 0 1-3-3V4Z" class="Vector"/>
          <g class="Subtract" filter="url(#b)">
            <path fill="#7C7C7C" fill-rule="evenodd" d="M6 1h2v1H6V1Zm2 2V2h2v1H8Zm0 1H6V3h2v1Zm0 1V4h2v1H8Zm0 1H6V5h2v1Zm0 1V6h2v1H8Zm0 1H6V7h2v1Zm0 1V8h2v1H8Zm0 0v1h2v1.25a2 2 0 1 1-4 0V9h2Zm0 3.25a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Z" clip-rule="evenodd"/>
          </g>
        </g>
      </g>
      <defs>
        <clipPath id="a" class="a">
          <path fill="#fff" d="M1 1h14v14H1z"/>
        </clipPath>
        <filter id="b" width="4" height="12.75" x="6" y="1" class="b" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
          <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
          <feOffset dy=".5"/>
          <feGaussianBlur stdDeviation=".5"/>
          <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
          <feBlend in2="shape" result="effect1_innerShadow_2025_84891"/>
        </filter>
      </defs>
    </symbol>
    <symbol id="es-solid-doc" viewBox="0 0 16 16">
      <g class="es-solid-doc">
        <path fill="#0289F7" fill-rule="evenodd" d="M4 1a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm.5 4h7v1h-7V5Zm0 2.5h7v1h-7v-1Zm0 2.5h5v1h-5v-1Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-image" viewBox="0 0 16 16">
      <g class="es-solid-image">
        <path fill="#34BAE3" fill-rule="evenodd" d="M4 1a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm1.25 6a1.75 1.75 0 1 0 0-3.5 1.75 1.75 0 0 0 0 3.5Zm-1.574 7a.25.25 0 0 1-.163-.44l6.208-5.32a.5.5 0 0 1 .59-.045l3.454 2.158a.5.5 0 0 1 .235.424V12a2 2 0 0 1-2 2H3.676Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-excel" viewBox="0 0 16 16">
      <g class="es-solid-excel">
        <path fill="#30A66D" fill-rule="evenodd" d="M4 1a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm0 1h1.5v2H14v1.5H5.5V14H4V5.5H2V4h2V2Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-sketch" viewBox="0 0 16 16">
      <g class="es-solid-sketch">
        <path fill="#E79213" fill-rule="evenodd" d="M4 1a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm8.544 6.154-2.272-2.48H5.728l-2.272 2.48L8 12.11l4.544-4.957Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-aftereffects" viewBox="0 0 16 16">
      <g class="es-solid-aftereffects">
        <path fill="#6846E3" fill-rule="evenodd" d="M4 1a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm2.523 8.246.467 1.474h1.4L6.245 4.54h-1.52l-2.147 6.18h1.298l.476-1.474h2.171ZM4.63 8.248l.776-2.424h.077l.766 2.424H4.63Zm5.098-.385c.043-.613.45-1.015 1.028-1.015.582 0 .96.39.985 1.015H9.727Zm3.2 1.512h-1.161c-.133.312-.471.488-.964.488-.65 0-1.062-.437-1.08-1.14V8.66h3.243v-.38c0-1.5-.822-2.386-2.214-2.386-1.405 0-2.27.95-2.27 2.488 0 1.533.848 2.441 2.287 2.441 1.156 0 1.97-.556 2.159-1.447Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-link" viewBox="0 0 16 16">
      <g class="es-solid-link">
        <path fill="#E34AA6" fill-rule="evenodd" d="M4 1a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm6.624 6.647-.85.85.707.708.85-.851a2.606 2.606 0 1 0-3.684-3.685l-.851.85.707.708.85-.851a1.606 1.606 0 1 1 2.271 2.27Zm-5.248.707.85-.851-.706-.707-.851.85a2.606 2.606 0 0 0 3.685 3.686l.85-.851-.706-.707-.851.85a1.606 1.606 0 0 1-2.27-2.27ZM9.56 7.149l-.354.354-1.701 1.702-.354.353-.707-.707.354-.354 1.701-1.701.354-.354.707.707Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-dropbox" viewBox="0 0 16 16">
      <g class="es-solid-dropbox">
        <path fill="#fff" d="M0 0h16v16H0z"/>
        <path fill="#0744DF" fill-rule="evenodd" d="M4 1a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm-.363 4.756 2.181-1.454L8 5.756l2.182-1.454 2.182 1.454-2.182 1.455L8 5.756 5.818 7.211 3.637 5.756Zm0 2.91 2.181 1.454L8 8.665l2.182 1.455 2.182-1.455-2.182-1.454L8 8.665 5.818 7.211 3.637 8.665Zm2.181 2.181L8 12.302l2.182-1.455L8 9.392l-2.182 1.455Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-word" viewBox="0 0 16 16">
      <g class="es-solid-word">
        <path fill="#9C2671" fill-rule="evenodd" d="M4 1a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm2.82 10.37 1.142-4.038h.068l1.154 4.038h1.197L12 5.269h-1.32l-.942 4.3H9.67l-1.133-4.3H7.459l-1.112 4.3h-.068l-.951-4.3H4l1.615 6.101H6.82Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-dropbox" viewBox="0 0 16 16">
      <g class="es-solid-dropbox">
        <path fill="#383838" fill-rule="evenodd" d="M4 1a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm4.968 3.119a.5.5 0 0 1 .364.606l-1.694 6.792a.5.5 0 0 1-.97-.242l1.694-6.792a.5.5 0 0 1 .606-.364ZM5.016 5.63a.5.5 0 0 1 .707.708L4.06 8l1.662 1.662a.5.5 0 1 1-.707.707L3 8.354a.5.5 0 0 1 0-.707L5.016 5.63Zm5.969 0a.5.5 0 0 0-.708.708L11.94 8l-1.662 1.662a.5.5 0 0 0 .708.707L13 8.354a.5.5 0 0 0 0-.707L10.985 5.63Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-audio" viewBox="0 0 16 16">
      <g class="es-solid-audio">
        <path fill="#9C45E3" fill-rule="evenodd" d="M4 1a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm5.994 2.868c.273-.04.55.045.757.23a.952.952 0 0 1 .313.718v5.522c0 .699-.701 1.264-1.565 1.264-.863 0-1.564-.565-1.564-1.264 0-.698.7-1.264 1.564-1.264.33-.002.655.087.939.259V5.645l-3.755.54v4.693c0 .698-.701 1.264-1.565 1.264-.863 0-1.564-.566-1.564-1.264 0-.699.7-1.265 1.564-1.265.33-.002.656.088.94.26V5.267a.945.945 0 0 1 .806-.948l3.13-.452Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-illustrator" viewBox="0 0 16 16">
      <g class="es-solid-illustrator">
        <path fill="#EBA71D" fill-rule="evenodd" d="M4 1a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm3.671 9.024.512 1.614h1.533l-2.35-6.77H5.701l-2.35 6.77h1.421l.521-1.614h2.378ZM5.598 8.931l.849-2.655h.084l.84 2.655H5.598Zm4.761-2.467v5.174h1.365V6.464h-1.365Zm-.122-1.412c0 .403.314.69.807.69.487 0 .806-.287.806-.69 0-.408-.319-.69-.806-.69-.493 0-.807.282-.807.69Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-photoshop" viewBox="0 0 16 16">
      <g class="es-solid-photoshop">
        <path fill="#267A94" fill-rule="evenodd" d="M4 1a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm1.984 4H3.522v5.902h1.235V9.07h1.166c1.243 0 2.106-.814 2.106-2.03C8.03 5.815 7.2 5 5.984 5ZM4.757 8.068V6.014h.9c.712 0 1.116.36 1.116 1.031 0 .663-.413 1.023-1.12 1.023h-.896Zm5.73-1.776c-1.125 0-1.878.569-1.878 1.424 0 .695.425 1.112 1.3 1.304l.819.184c.396.086.568.221.568.442 0 .29-.319.483-.781.483-.475 0-.765-.176-.855-.483H8.5c.08.867.8 1.354 1.99 1.354 1.182 0 1.988-.589 1.988-1.489 0-.675-.393-1.043-1.268-1.235l-.846-.184c-.418-.094-.61-.229-.61-.45 0-.286.315-.478.74-.478.442 0 .716.18.777.47h1.1c-.065-.863-.74-1.342-1.885-1.342Z" class="Subtract" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-music" viewBox="0 0 16 16">
      <g class="es-solid-music">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M12.638 1.96a1.444 1.444 0 0 0-1.172-.358l-4.845.7A1.464 1.464 0 0 0 5.37 3.77v7.129a2.774 2.774 0 0 0-1.454-.401c-1.337 0-2.422.876-2.422 1.957 0 1.082 1.085 1.957 2.422 1.957 1.338 0 2.423-.875 2.423-1.957V5.191l5.814-.837v5.71a2.774 2.774 0 0 0-1.454-.401c-1.337 0-2.422.875-2.422 1.957 0 1.081 1.085 1.957 2.422 1.957 1.337 0 2.423-.876 2.423-1.957V3.07a1.474 1.474 0 0 0-.485-1.11Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-play" viewBox="0 0 16 16">
      <g class="es-solid-play">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M13.014 7.095 6.03 2.568a1.482 1.482 0 0 0-1.371-.107c-.444.192-.724.584-.724 1.012v9.054c0 .428.28.82.724 1.012.443.191.974.15 1.37-.107l6.985-4.527c.33-.213.523-.549.523-.905s-.194-.692-.524-.905Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-shared-folder-alt" viewBox="0 0 16 16">
      <g class="es-solid-shared folder-alt">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M2.842 1.258a2 2 0 0 0-2 2v8.984a2.5 2.5 0 0 0 2.5 2.5h9.316a2.5 2.5 0 0 0 2.5-2.5V5.877a2 2 0 0 0-2-1.985H8.087L6.864 1.957a1.5 1.5 0 0 0-1.268-.699H2.842Zm6.485 6.239a1.172 1.172 0 0 0-2.132.676 1.173 1.173 0 0 0 2.12.695 1.997 1.997 0 0 1 .012-1.371Zm-.421 2.409c-.409.306-.71.753-.827 1.28l-.052.239c-.091.41.059.807.344 1.06h-1.51a.86.86 0 0 1-.84-1.046l.041-.187a1.72 1.72 0 0 1 1.68-1.346h1.164Zm3.4-1.733a1.173 1.173 0 1 1-2.347 0 1.173 1.173 0 0 1 2.347 0Zm-1.8 1.733a1.72 1.72 0 0 0-1.68 1.346l-.041.187a.86.86 0 0 0 .84 1.046h3.015a.86.86 0 0 0 .84-1.046l-.042-.187a1.72 1.72 0 0 0-1.68-1.346h-1.253Zm2.652-7.014a2.99 2.99 0 0 1 1.998.762 2 2 0 0 0-1.998-1.907H7.914l.724 1.145h4.52Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-folder" viewBox="0 0 16 16">
      <g class="es-solid-folder">
        <path fill="var(--icon-stroke)" d="M7.834 2.885H1.5a.5.5 0 0 0-.5.5V12.5a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V2a.5.5 0 0 0-.5-.5H9.94a.25.25 0 0 0-.15.05L8.134 2.785a.5.5 0 0 1-.299.1Z" class="Union"/>
      </g>
    </symbol>
    <symbol id="es-solid-shared-folder" viewBox="0 0 16 16">
      <g class="es-solid-shared folder">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M1.5 2.885h6.334a.5.5 0 0 0 .299-.1L9.791 1.55a.25.25 0 0 1 .15-.05H14.5a.5.5 0 0 1 .5.5v10.5a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V3.385a.5.5 0 0 1 .5-.5ZM7.869 7c.396 0 .746.196.958.497a1.996 1.996 0 0 0-.013 1.371A1.173 1.173 0 1 1 7.868 7Zm-.29 4.187c.117-.528.418-.975.827-1.281H7.24a1.72 1.72 0 0 0-1.679 1.346l-.041.187a.86.86 0 0 0 .84 1.046h1.51a1.095 1.095 0 0 1-.344-1.06l.052-.238Zm3.054-1.84a1.173 1.173 0 1 0 0-2.347 1.173 1.173 0 0 0 0 2.347Zm-2.307 1.905a1.72 1.72 0 0 1 1.68-1.346h1.253c.806 0 1.504.56 1.679 1.346l.041.187a.86.86 0 0 1-.84 1.046H9.125a.86.86 0 0 1-.84-1.046l.042-.187Z" class="Vector" clip-rule="evenodd"/>
      </g>
    </symbol>
    <symbol id="es-solid-color-wheel" viewBox="0 0 16 16">
      <g class="es-solid-color-wheel" clip-path="url(#a)">
        <path fill="url(#b)" d="M15.5 8a7.5 7.5 0 1 1-15 0 7.5 7.5 0 0 1 15 0ZM4.4 8a3.6 3.6 0 1 0 7.2 0 3.6 3.6 0 0 0-7.2 0Z" class="Union"/>
      </g>
      <defs>
        <radialGradient id="b" cx="0" cy="0" r="1" class="b" gradientTransform="matrix(0 7.5 -7.5 0 8 8)" gradientUnits="userSpaceOnUse">
          <stop offset=".142" stop-color="#00F"/>
          <stop offset=".278" stop-color="#00FFF7"/>
          <stop offset=".395" stop-color="#0DFF1B"/>
          <stop offset=".49" stop-color="#17FF00"/>
          <stop offset=".624" stop-color="#FFF91D"/>
          <stop offset=".758" stop-color="red"/>
          <stop offset=".923" stop-color="#FE00FF"/>
          <stop offset=".992" stop-color="#B400FF" stop-opacity=".911"/>
        </radialGradient>
        <clipPath id="a" class="a">
          <path fill="#fff" d="M0 0h16v16H0z"/>
        </clipPath>
      </defs>
    </symbol>
    <symbol id="es-solid-heart" viewBox="0 0 16 16">
      <g class="es-solid-heart">
        <path fill="var(--icon-stroke)" fill-rule="evenodd" d="M7.201 2.436A4.024 4.024 0 0 1 8 3.173a3.41 3.41 0 0 1 .244-.272 4.037 4.037 0 0 1 1.873-1.066 4.084 4.084 0 0 1 1.776-.05c.768.15 1.502.521 2.097 1.116 1.587 1.586 1.658 3.968.012 5.733l-.008.007-.009.01-5.636 5.497a.5.5 0 0 1-.698 0L2.013 8.651a.501.501 0 0 1-.02-.02C.447 6.94.413 4.495 2.01 2.9a4.052 4.052 0 0 1 3.648-1.116 4.04 4.04 0 0 1 1.544.651Z" class="Union" clip-rule="evenodd"/>
      </g>
    </symbol>
  </defs>
</svg>
