# Copyright (c) 2015, Frappe Technologies Pvt. Ltd. and Contributors
# License: MIT. See LICENSE
import json

import frappe
from frappe.tests.utils import FrappeTestCase


class TestSeen(FrappeTestCase):
	def tearDown(self):
		frappe.set_user("Administrator")

	def test_if_user_is_added(self):
		ev = frappe.get_doc(
			{
				"doctype": "Event",
				"subject": "test event for seen",
				"starts_on": "2016-01-01 10:10:00",
				"event_type": "Public",
			}
		).insert()

		frappe.set_user("<EMAIL>")

		from frappe.desk.form.load import getdoc

		# load the form
		getdoc("Event", ev.name)

		# reload the event
		ev = frappe.get_doc("Event", ev.name)

		self.assertTrue("<EMAIL>" in json.loads(ev._seen))

		# test another user
		frappe.set_user("<EMAIL>")

		# load the form
		getdoc("Event", ev.name)

		# reload the event
		ev = frappe.get_doc("Event", ev.name)

		self.assertTrue("<EMAIL>" in json.loads(ev._seen))
		self.assertTrue("<EMAIL>" in json.loads(ev._seen))

		ev.save()
		ev = frappe.get_doc("Event", ev.name)

		self.assertFalse("<EMAIL>" in json.loads(ev._seen))
		self.assertTrue("<EMAIL>" in json.loads(ev._seen))
