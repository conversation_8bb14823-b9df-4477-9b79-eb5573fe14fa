# Copyright (c) 2015, Frappe Technologies Pvt. Ltd. and Contributors
# License: MIT. See LICENSE
import frappe
import frappe.desk.form.assign_to
from frappe.automation.doctype.assignment_rule.test_assignment_rule import (
	TEST_DOCTYPE,
	_make_test_record,
	create_test_doctype,
)
from frappe.desk.form.load import get_assignments
from frappe.desk.listview import get_group_by_count
from frappe.tests.utils import FrappeTestCase


class TestAssign(FrappeTestCase):
	@classmethod
	def setUpClass(cls):
		super().setUpClass()
		create_test_doctype(TEST_DOCTYPE)

	def test_assign(self):
		todo = frappe.get_doc({"doctype": "ToDo", "description": "test"}).insert()
		if not frappe.db.exists("User", "<EMAIL>"):
			frappe.get_doc({"doctype": "User", "email": "<EMAIL>", "first_name": "Test"}).insert()

		self._test_basic_assign_on_document(todo)

	def _test_basic_assign_on_document(self, doc):
		added = assign(doc, "<EMAIL>")

		self.assertTrue("<EMAIL>" in [d.owner for d in added])

		frappe.desk.form.assign_to.remove(doc.doctype, doc.name, "<EMAIL>")

		# assignment is cleared
		assignments = frappe.desk.form.assign_to.get(dict(doctype=doc.doctype, name=doc.name))
		self.assertEqual(len(assignments), 0)

	def test_assign_single(self):
		c = frappe.get_doc("Contact Us Settings")
		self._test_basic_assign_on_document(c)

	def test_assignment_count(self):
		frappe.db.delete("ToDo")

		if not frappe.db.exists("User", "<EMAIL>"):
			frappe.get_doc(
				{
					"doctype": "User",
					"email": "<EMAIL>",
					"first_name": "Test",
					"roles": [{"role": "System Manager"}],
				}
			).insert()

		if not frappe.db.exists("User", "<EMAIL>"):
			frappe.get_doc(
				{
					"doctype": "User",
					"email": "<EMAIL>",
					"first_name": "Test",
					"roles": [{"role": "System Manager"}],
				}
			).insert()

		note = _make_test_record()
		assign(note, "<EMAIL>")

		note = _make_test_record(public=1)
		assign(note, "<EMAIL>")

		note = _make_test_record(public=1)
		assign(note, "<EMAIL>")

		note = _make_test_record()
		assign(note, "<EMAIL>")

		data = {d.name: d.count for d in get_group_by_count(TEST_DOCTYPE, "[]", "assigned_to")}

		self.assertTrue("<EMAIL>" in data)
		self.assertEqual(data["<EMAIL>"], 1)
		self.assertEqual(data["<EMAIL>"], 3)

		data = {d.name: d.count for d in get_group_by_count(TEST_DOCTYPE, '[{"public": 1}]', "assigned_to")}

		self.assertFalse("<EMAIL>" in data)
		self.assertEqual(data["<EMAIL>"], 2)

		frappe.db.rollback()

	def test_assignment_removal(self):
		todo = frappe.get_doc({"doctype": "ToDo", "description": "test"}).insert()
		if not frappe.db.exists("User", "<EMAIL>"):
			frappe.get_doc({"doctype": "User", "email": "<EMAIL>", "first_name": "Test"}).insert()

		new_todo = assign(todo, "<EMAIL>")

		# remove assignment
		frappe.db.set_value("ToDo", new_todo[0].name, "allocated_to", "")

		self.assertFalse(get_assignments("ToDo", todo.name))


def assign(doc, user):
	return frappe.desk.form.assign_to.add(
		{
			"assign_to": [user],
			"doctype": doc.doctype,
			"name": doc.name,
			"description": "test",
		}
	)
