{"autoname": "field:rule_name", "creation": "2018-06-20 10:08:36.275253", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["enabled", "section_break_2", "rule_name", "reference_doctype", "for_doc_event", "field_to_check", "points", "for_assigned_users", "user_field", "multiplier_field", "max_points", "column_break_12", "apply_only_once", "condition"], "fields": [{"default": "1", "fieldname": "enabled", "fieldtype": "Check", "in_list_view": 1, "label": "Enabled", "reqd": 1}, {"fieldname": "rule_name", "fieldtype": "Data", "in_list_view": 1, "label": "Rule Name", "reqd": 1, "unique": 1}, {"fieldname": "reference_doctype", "fieldtype": "Link", "in_list_view": 1, "label": "Reference Document Type", "options": "DocType", "reqd": 1}, {"depends_on": "eval:['Custom', 'Value Change'].includes(doc.for_doc_event)", "description": "If the condition is satisfied user will be rewarded with the points. eg. doc.status == 'Closed'\n", "fieldname": "condition", "fieldtype": "Code", "in_list_view": 1, "label": "Condition"}, {"fieldname": "points", "fieldtype": "Int", "label": "Points", "reqd": 1}, {"depends_on": "eval:!doc.for_assigned_users || doc.for_doc_event==='New'", "description": "The user from this field will be rewarded points", "fieldname": "user_field", "fieldtype": "Select", "label": "User Field"}, {"fieldname": "multiplier_field", "fieldtype": "Select", "label": "Multiplier Field"}, {"depends_on": "eval:doc.multiplier_field", "description": "Maximum points allowed after multiplying points with the multiplier value\n(Note: For no limit leave this field empty or set 0)", "fieldname": "max_points", "fieldtype": "Int", "label": "Maximum Points"}, {"fieldname": "section_break_2", "fieldtype": "Section Break"}, {"default": "Custom", "fieldname": "for_doc_event", "fieldtype": "Select", "label": "For Document Event", "options": "New\nSubmit\nCancel\nValue Change\nCustom"}, {"default": "0", "depends_on": "eval:doc.for_doc_event !=='New'", "description": "Users assigned to the reference document will get points.", "fieldname": "for_assigned_users", "fieldtype": "Check", "label": "Allot Points To Assigned Users"}, {"depends_on": "eval:doc.for_doc_event=='Value Change'", "fieldname": "field_to_check", "fieldtype": "Select", "label": "Field To Check"}, {"fieldname": "column_break_12", "fieldtype": "Column Break"}, {"default": "0", "description": "Apply this rule only once per document", "fieldname": "apply_only_once", "fieldtype": "Check", "label": "Apply Only Once"}], "modified": "2019-10-28 14:16:09.710167", "modified_by": "Administrator", "module": "Social", "name": "Energy Point Rule", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}