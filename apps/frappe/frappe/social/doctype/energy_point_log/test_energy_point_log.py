# Copyright (c) 2019, Frappe Technologies and Contributors
# License: MIT. See LICENSE
import frappe
from frappe.desk.form.assign_to import add as assign_to
from frappe.desk.page.user_profile.user_profile import get_energy_points_heatmap_data
from frappe.tests.utils import FrappeTestCase
from frappe.utils.testutils import add_custom_field, clear_custom_fields

from .energy_point_log import create_review_points_log, review
from .energy_point_log import get_energy_points as _get_energy_points


class TestEnergyPointLog(FrappeTestCase):
	@classmethod
	def setUpClass(cls):
		super().setUpClass()
		settings = frappe.get_single("Energy Point Settings")
		settings.enabled = 1
		settings.save()

	@classmethod
	def tearDownClass(cls):
		settings = frappe.get_single("Energy Point Settings")
		settings.enabled = 0
		settings.save()

	def setUp(self):
		frappe.cache.delete_value("energy_point_rule_map")

	def tearDown(self):
		frappe.set_user("Administrator")
		frappe.db.delete("Energy Point Log")
		frappe.db.delete("Energy Point Rule")
		frappe.cache.delete_value("energy_point_rule_map")

	def test_user_energy_point(self):
		frappe.set_user("<EMAIL>")
		todo_point_rule = create_energy_point_rule_for_todo()
		energy_point_of_user = get_points("<EMAIL>")

		created_todo = create_a_todo()

		created_todo.status = "Closed"
		created_todo.save()

		points_after_closing_todo = get_points("<EMAIL>")

		self.assertEqual(points_after_closing_todo, energy_point_of_user + todo_point_rule.points)

		created_todo.save()
		points_after_double_save = get_points("<EMAIL>")

		# point should not be awarded more than once for same doc
		self.assertEqual(points_after_double_save, energy_point_of_user + todo_point_rule.points)

	def test_points_based_on_multiplier_field(self):
		frappe.set_user("<EMAIL>")
		add_custom_field("ToDo", "multiplier", "Float")
		multiplier_value = 0.51

		todo_point_rule = create_energy_point_rule_for_todo("multiplier")
		energy_point_of_user = get_points("<EMAIL>")

		created_todo = create_a_todo()
		created_todo.status = "Closed"
		created_todo.multiplier = multiplier_value
		created_todo.save()

		points_after_closing_todo = get_points("<EMAIL>")

		self.assertEqual(
			points_after_closing_todo,
			energy_point_of_user + round(todo_point_rule.points * multiplier_value),
		)

		clear_custom_fields("ToDo")

	def test_points_based_on_max_points(self):
		frappe.set_user("<EMAIL>")
		# here multiplier is high
		# let see if points get capped to max_point limit
		multiplier_value = 15
		max_points = 50

		add_custom_field("ToDo", "multiplier", "Float")
		todo_point_rule = create_energy_point_rule_for_todo("multiplier", max_points=max_points)
		energy_point_of_user = get_points("<EMAIL>")

		created_todo = create_a_todo()
		created_todo.status = "Closed"
		created_todo.multiplier = multiplier_value
		created_todo.save()

		points_after_closing_todo = get_points("<EMAIL>")

		# test max_points cap
		self.assertNotEqual(
			points_after_closing_todo,
			energy_point_of_user + round(todo_point_rule.points * multiplier_value),
		)

		self.assertEqual(points_after_closing_todo, energy_point_of_user + max_points)

		clear_custom_fields("ToDo")

	def test_disabled_energy_points(self):
		settings = frappe.get_single("Energy Point Settings")
		settings.enabled = 0
		settings.save()

		frappe.set_user("<EMAIL>")
		create_energy_point_rule_for_todo()
		energy_point_of_user = get_points("<EMAIL>")

		created_todo = create_a_todo()

		created_todo.status = "Closed"
		created_todo.save()

		points_after_closing_todo = get_points("<EMAIL>")

		# no change in points
		self.assertEqual(points_after_closing_todo, energy_point_of_user)

		settings.enabled = 1
		settings.save()

	def test_review(self):
		created_todo = create_a_todo()
		review_points = 20
		create_review_points_log("<EMAIL>", review_points)

		# reviewer
		frappe.set_user("<EMAIL>")

		review_points_before_review = get_points("<EMAIL>", "review_points")
		self.assertEqual(review_points_before_review, review_points)

		# for appreciation
		appreciation_points = 5
		energy_points_before_review = get_points("<EMAIL>")
		review(created_todo, appreciation_points, "<EMAIL>", "good job")
		energy_points_after_review = get_points("<EMAIL>")
		review_points_after_review = get_points("<EMAIL>", "review_points")
		self.assertEqual(energy_points_after_review, energy_points_before_review + appreciation_points)
		self.assertEqual(review_points_after_review, review_points_before_review - appreciation_points)

		# for criticism
		criticism_points = 2
		todo = create_a_todo(description="Bad patch")
		energy_points_before_review = energy_points_after_review
		review_points_before_review = review_points_after_review
		review(todo, criticism_points, "<EMAIL>", "You could have done better.", "Criticism")
		energy_points_after_review = get_points("<EMAIL>")
		review_points_after_review = get_points("<EMAIL>", "review_points")
		self.assertEqual(energy_points_after_review, energy_points_before_review - criticism_points)
		self.assertEqual(review_points_after_review, review_points_before_review - criticism_points)

	def test_user_energy_point_as_admin(self):
		frappe.set_user("Administrator")
		create_energy_point_rule_for_todo()
		created_todo = create_a_todo()

		created_todo.status = "Closed"
		created_todo.save()

		points_after_closing_todo = get_points("Administrator")

		# no points for admin
		self.assertEqual(points_after_closing_todo, 0)

	def test_revert_points_on_cancelled_doc(self):
		frappe.set_user("<EMAIL>")
		create_energy_point_rule_for_todo()
		created_todo = create_a_todo()
		created_todo.status = "Closed"
		created_todo.save()

		energy_point_logs = frappe.get_all("Energy Point Log")

		self.assertEqual(len(energy_point_logs), 1)

		# for submit and cancel permission
		frappe.set_user("Administrator")
		# submit
		created_todo.docstatus = 1
		created_todo.save()

		# cancel
		created_todo.docstatus = 2
		created_todo.save()

		energy_point_logs = frappe.get_all("Energy Point Log", fields=["reference_name", "type", "reverted"])

		self.assertListEqual(
			energy_point_logs,
			[
				{"reference_name": created_todo.name, "type": "Revert", "reverted": 0},
				{"reference_name": created_todo.name, "type": "Auto", "reverted": 1},
			],
		)

	def test_energy_point_for_new_document_creation(self):
		frappe.set_user("<EMAIL>")
		todo_point_rule = create_energy_point_rule_for_todo(for_doc_event="New")

		points_before_todo_creation = get_points("<EMAIL>")
		create_a_todo()
		points_after_todo_creation = get_points("<EMAIL>")

		self.assertEqual(points_after_todo_creation, points_before_todo_creation + todo_point_rule.points)

	def test_point_allocation_for_assigned_users(self):
		todo = create_a_todo()

		assign_users_to_todo(todo.name, ["<EMAIL>", "<EMAIL>"])

		test_user_before_points = get_points("<EMAIL>")
		test2_user_before_points = get_points("<EMAIL>")

		rule = create_energy_point_rule_for_todo(for_assigned_users=1)

		todo.status = "Closed"
		todo.save()

		test_user_after_points = get_points("<EMAIL>")
		test2_user_after_points = get_points("<EMAIL>")

		self.assertEqual(test_user_after_points, test_user_before_points + rule.points)

		self.assertEqual(test2_user_after_points, test2_user_before_points + rule.points)

	def test_eps_heatmap_query(self):
		# Just asserts that query works, not correctness.
		self.assertIsInstance(get_energy_points_heatmap_data(user="<EMAIL>", date=None), dict)

	def test_points_on_field_value_change(self):
		rule = create_energy_point_rule_for_todo(for_doc_event="Value Change", field_to_check="description")

		frappe.set_user("<EMAIL>")
		points_before_todo_creation = get_points("<EMAIL>")
		todo = create_a_todo()
		todo.status = "Closed"
		todo.save()
		points_after_closing_todo = get_points("<EMAIL>")
		self.assertEqual(points_after_closing_todo, points_before_todo_creation)

		todo.description = "This is new todo"
		todo.save()
		points_after_changing_todo_description = get_points("<EMAIL>")
		self.assertEqual(points_after_changing_todo_description, points_before_todo_creation + rule.points)

	def test_apply_only_once(self):
		frappe.set_user("<EMAIL>")
		todo_point_rule = create_energy_point_rule_for_todo(apply_once=True, user_field="modified_by")
		first_user_points = get_points("<EMAIL>")

		created_todo = create_a_todo()

		created_todo.status = "Closed"
		created_todo.save()

		first_user_points_after_closing_todo = get_points("<EMAIL>")

		self.assertEqual(first_user_points_after_closing_todo, first_user_points + todo_point_rule.points)

		frappe.set_user("<EMAIL>")
		second_user_points = get_points("<EMAIL>")
		created_todo.save(ignore_permissions=True)
		second_user_points_after_closing_todo = get_points("<EMAIL>")

		# point should not be awarded more than once for same doc (irrespective of user)
		self.assertEqual(second_user_points_after_closing_todo, second_user_points)

	def test_allow_creation_of_new_log_if_the_previous_log_was_reverted(self):
		frappe.set_user("<EMAIL>")
		todo_point_rule = create_energy_point_rule_for_todo()
		energy_point_of_user = get_points("<EMAIL>")

		created_todo = create_a_todo()

		created_todo.status = "Closed"
		created_todo.save()
		points_after_closing_todo = get_points("<EMAIL>")

		log_name = frappe.db.exists("Energy Point Log", {"reference_name": created_todo.name})
		frappe.get_doc("Energy Point Log", log_name).revert("Just for test")
		points_after_reverting_todo = get_points("<EMAIL>")
		created_todo.save()
		points_after_saving_todo_again = get_points("<EMAIL>")

		rule_points = todo_point_rule.points
		self.assertEqual(points_after_closing_todo, energy_point_of_user + rule_points)
		self.assertEqual(points_after_reverting_todo, points_after_closing_todo - rule_points)
		self.assertEqual(points_after_saving_todo_again, points_after_reverting_todo + rule_points)

	def test_energy_points_disabled_user(self):
		frappe.set_user("<EMAIL>")
		user = frappe.get_doc("User", "<EMAIL>")
		user.enabled = 0
		user.save()
		todo_point_rule = create_energy_point_rule_for_todo()
		energy_point_of_user = get_points("<EMAIL>")

		created_todo = create_a_todo()

		created_todo.status = "Closed"
		created_todo.save()
		points_after_closing_todo = get_points("<EMAIL>")

		# do not update energy points for disabled user
		self.assertEqual(points_after_closing_todo, energy_point_of_user)

		with self.set_user("Administrator"):
			user.enabled = 1
			user.save()

		created_todo.save()
		points_after_re_saving_todo = get_points("<EMAIL>")
		self.assertEqual(points_after_re_saving_todo, energy_point_of_user + todo_point_rule.points)


def create_energy_point_rule_for_todo(
	multiplier_field=None,
	for_doc_event="Custom",
	max_points=None,
	for_assigned_users=0,
	field_to_check=None,
	apply_once=False,
	user_field="owner",
):
	name = "ToDo Closed"
	point_rule_exists = frappe.db.exists("Energy Point Rule", name)

	if point_rule_exists:
		return frappe.get_doc("Energy Point Rule", name)

	return frappe.get_doc(
		{
			"doctype": "Energy Point Rule",
			"rule_name": name,
			"points": 5,
			"reference_doctype": "ToDo",
			"condition": 'doc.status == "Closed"',
			"for_doc_event": for_doc_event,
			"user_field": user_field,
			"for_assigned_users": for_assigned_users,
			"multiplier_field": multiplier_field,
			"max_points": max_points,
			"field_to_check": field_to_check,
			"apply_only_once": apply_once,
		}
	).insert(ignore_permissions=1)


def create_a_todo(description=None):
	if not description:
		description = "Fix a bug"
	return frappe.get_doc(
		{
			"doctype": "ToDo",
			"description": description,
		}
	).insert(ignore_permissions=True)


def get_points(user, point_type="energy_points"):
	return _get_energy_points(user).get(point_type) or 0


def assign_users_to_todo(todo_name, users):
	for user in users:
		assign_to({"assign_to": [user], "doctype": "ToDo", "name": todo_name})
