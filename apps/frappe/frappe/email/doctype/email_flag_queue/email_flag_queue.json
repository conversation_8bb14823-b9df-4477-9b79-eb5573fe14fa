{"actions": [], "allow_copy": 1, "creation": "2016-04-20 15:29:39.785172", "doctype": "DocType", "engine": "InnoDB", "field_order": ["is_completed", "communication", "action", "email_account", "uid"], "fields": [{"default": "0", "fieldname": "is_completed", "fieldtype": "Check", "label": "Is Completed", "read_only": 1}, {"fieldname": "communication", "fieldtype": "Data", "label": "Communication"}, {"fieldname": "action", "fieldtype": "Select", "label": "Action", "options": "Read\nUnread"}, {"fieldname": "email_account", "fieldtype": "Data", "hidden": 1, "label": "<PERSON><PERSON> Account"}, {"fieldname": "uid", "fieldtype": "Data", "hidden": 1, "label": "UID"}], "in_create": 1, "links": [], "modified": "2021-11-30 09:51:34.489932", "modified_by": "Administrator", "module": "Email", "name": "Email Flag Queue", "owner": "Administrator", "permissions": [{"delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1}], "sort_field": "modified", "sort_order": "DESC"}