{"actions": [], "allow_rename": 1, "creation": "2013-01-10 16:34:31", "description": "Create and send emails to a specific group of subscribers periodically.", "doctype": "DocType", "document_type": "Other", "engine": "InnoDB", "field_order": ["status_section", "email_sent_at", "column_break_3", "total_recipients", "column_break_12", "total_views", "email_sent", "from_section", "sender_name", "column_break_5", "sender_email", "column_break_7", "send_from", "recipients", "email_group", "subject_section", "subject", "newsletter_content", "content_type", "message", "message_md", "message_html", "campaign", "attachments", "send_unsubscribe_link", "send_webview_link", "schedule_settings_section", "scheduled_to_send", "schedule_sending", "schedule_send", "publish_as_a_web_page_section", "published", "route"], "fields": [{"fieldname": "email_group", "fieldtype": "Table", "in_standard_filter": 1, "label": "Audience", "options": "Newsletter Email Group", "reqd": 1}, {"fieldname": "send_from", "fieldtype": "Data", "ignore_xss_filter": 1, "label": "Sender", "read_only": 1}, {"default": "0", "fieldname": "email_sent", "fieldtype": "Check", "hidden": 1, "label": "<PERSON><PERSON>", "no_copy": 1, "read_only": 1}, {"fieldname": "newsletter_content", "fieldtype": "Section Break", "label": "Content"}, {"fieldname": "subject", "fieldtype": "Small Text", "in_global_search": 1, "in_list_view": 1, "label": "Subject", "reqd": 1}, {"depends_on": "eval: doc.content_type === 'Rich Text'", "fieldname": "message", "fieldtype": "Text Editor", "in_list_view": 1, "label": "Message", "mandatory_depends_on": "eval: doc.content_type === 'Rich Text'"}, {"default": "1", "fieldname": "send_unsubscribe_link", "fieldtype": "Check", "label": "Send Unsubscribe Link"}, {"default": "0", "fieldname": "published", "fieldtype": "Check", "label": "Published"}, {"depends_on": "published", "fieldname": "route", "fieldtype": "Data", "label": "Route", "read_only": 1}, {"fieldname": "scheduled_to_send", "fieldtype": "Int", "hidden": 1, "label": "Scheduled To Send"}, {"fieldname": "recipients", "fieldtype": "Section Break", "label": "To"}, {"depends_on": "eval: doc.schedule_sending", "fieldname": "schedule_send", "fieldtype": "Datetime", "label": "Send Email At", "read_only": 1, "read_only_depends_on": "eval: doc.email_sent"}, {"fieldname": "content_type", "fieldtype": "Select", "label": "Content Type", "options": "Rich Text\nMarkdown\nHTML"}, {"depends_on": "eval:doc.content_type === 'Markdown'", "fieldname": "message_md", "fieldtype": "Markdown Editor", "label": "Message (Markdown)", "mandatory_depends_on": "eval:doc.content_type === 'Markdown'"}, {"depends_on": "eval:doc.content_type === 'HTML'", "fieldname": "message_html", "fieldtype": "HTML Editor", "label": "Message (HTML)", "mandatory_depends_on": "eval:doc.content_type === 'HTML'"}, {"default": "0", "fieldname": "schedule_sending", "fieldtype": "Check", "label": "Schedule sending at a later time", "read_only_depends_on": "eval: doc.email_sent"}, {"default": "0", "fieldname": "send_webview_link", "fieldtype": "Check", "label": "Send Web View Link"}, {"fieldname": "from_section", "fieldtype": "Section Break", "label": "From"}, {"fieldname": "sender_name", "fieldtype": "Data", "label": "Sender Name"}, {"fieldname": "sender_email", "fieldtype": "Data", "label": "Sender <PERSON><PERSON>", "options": "Email", "reqd": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fieldname": "subject_section", "fieldtype": "Section Break", "label": "Subject"}, {"fieldname": "publish_as_a_web_page_section", "fieldtype": "Section Break", "label": "Publish as a web page"}, {"depends_on": "schedule_sending", "fieldname": "schedule_settings_section", "fieldtype": "Section Break", "label": "Scheduled Sending"}, {"fieldname": "attachments", "fieldtype": "Table", "label": "Attachments", "options": "Newsletter Attachment"}, {"fieldname": "email_sent_at", "fieldtype": "Datetime", "label": "<PERSON><PERSON>", "read_only": 1}, {"fieldname": "total_recipients", "fieldtype": "Int", "label": "Total Recipients", "read_only": 1}, {"depends_on": "email_sent", "fieldname": "status_section", "fieldtype": "Section Break", "label": "Status"}, {"fieldname": "column_break_12", "fieldtype": "Column Break"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "total_views", "fieldtype": "Int", "label": "Total Views", "no_copy": 1, "read_only": 1}, {"fieldname": "campaign", "fieldtype": "Link", "label": "Campaign", "options": "Marketing Campaign"}], "has_web_view": 1, "icon": "fa fa-envelope", "idx": 1, "index_web_pages_for_search": 1, "is_published_field": "published", "links": [], "make_attachments_public": 1, "modified": "2024-11-12 12:41:02.569631", "modified_by": "Administrator", "module": "Email", "name": "Newsletter", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Newsletter Manager", "share": 1, "write": 1}], "route": "newsletters", "sort_field": "modified", "sort_order": "ASC", "states": [], "title_field": "subject", "track_changes": 1}