{% extends "templates/web.html" %}

{% block title %} {{ doc.subject }} {% endblock %}

{% block page_content %}
    <style>
        .blog-container {
            max-width: 720px;
            margin: auto;
        }
        .blog-header {
            font-weight: 700;
            font-size: 1.5em;
        }
        .blog-info {
            text-align:center;
            margin-top: 30px;
        }
        .blog-text {
            padding-top: 50px;
            padding-bottom: 50px;
            font-size: 15px;
            line-height: 1.5;
        }
        .blog-text p {
            margin-bottom: 30px;
        }
    </style>

    <div class="blog-container">
        <article class="blog-content" itemscope>
            <div class="blog-info">
                <h1 itemprop="headline" class="blog-header">{{ doc.subject }}</h1>
                <p class="post-by text-muted">
                    {{ frappe.format_date(doc.modified) }}
                </p>
            </div>
            <div itemprop="articleBody" class="longform blog-text">
                {{ doc.get_message(medium="web_page") }}
            </div>
        </article>

        {% if doc.attachments %}
            <div>
                <div class="row text-muted">
                    <div class="col-sm-12 h6 text-uppercase">
                        {{ _("Attachments") }}
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        {% for attachment in doc.attachments %}
                            <p class="small">
                                <a href="{{ attachment.attachment }}" target="_blank">
                                    {{ attachment.attachment }}
                                </a>
                            </p>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}

    </div>
{% endblock %}