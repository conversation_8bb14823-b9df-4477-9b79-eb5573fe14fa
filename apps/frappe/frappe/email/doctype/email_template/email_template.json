{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "Prompt", "creation": "2014-06-19 05:20:26.331041", "doctype": "DocType", "document_type": "Document", "engine": "InnoDB", "field_order": ["subject", "use_html", "response_html", "response", "section_break_4", "email_reply_help"], "fields": [{"fieldname": "subject", "fieldtype": "Data", "in_list_view": 1, "label": "Subject", "reqd": 1}, {"depends_on": "eval:!doc.use_html", "fieldname": "response", "fieldtype": "Text Editor", "in_list_view": 1, "label": "Response", "mandatory_depends_on": "eval:!doc.use_html"}, {"fieldname": "section_break_4", "fieldtype": "Section Break"}, {"fieldname": "email_reply_help", "fieldtype": "HTML", "label": "Email Reply Help", "options": "<h4>Email Reply Example</h4>\n\n<pre>Order Overdue\n\nTransaction {{ name }} has exceeded Due Date. Please take necessary action.\n\nDetails\n\n- Customer: {{ customer }}\n- Amount: {{ grand_total }}\n</pre>\n\n<h4>How to get fieldnames</h4>\n\n<p>The fieldnames you can use in your email template are the fields in the document from which you are sending the email. You can find out the fields of any documents via Setup &gt; Customize Form View and selecting the document type (e.g. Sales Invoice)</p>\n\n<h4>Templating</h4>\n\n<p>Templates are compiled using the Jinja Templating Language. To learn more about <PERSON><PERSON>, <a class=\"strong\" href=\"http://jinja.pocoo.org/docs/dev/templates/\">read this documentation.</a></p>\n"}, {"default": "0", "fieldname": "use_html", "fieldtype": "Check", "label": "Use HTML"}, {"depends_on": "eval:doc.use_html", "fieldname": "response_html", "fieldtype": "Code", "label": "Response ", "options": "<PERSON><PERSON>"}], "icon": "fa fa-comment", "links": [], "modified": "2023-12-12 20:01:07.080625", "modified_by": "Administrator", "module": "Email", "name": "<PERSON>ail Te<PERSON>late", "naming_rule": "Set by user", "owner": "Administrator", "permissions": [{"read": 1, "role": "Desk User"}, {"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}