{"actions": [], "creation": "2019-01-09 16:39:23.746535", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["ref_doctype", "ref_docname", "user"], "fields": [{"fieldname": "ref_doctype", "fieldtype": "Link", "in_list_view": 1, "label": "Doctype", "options": "DocType", "reqd": 1, "search_index": 1}, {"fieldname": "ref_docname", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Document Name", "options": "ref_doctype", "reqd": 1, "search_index": 1}, {"fieldname": "user", "fieldtype": "Link", "in_list_view": 1, "label": "User", "options": "User", "reqd": 1, "search_index": 1}], "in_create": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2023-08-28 22:34:53.394652", "modified_by": "Administrator", "module": "Email", "name": "Document Follow", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Desk User", "share": 1, "write": 1}], "read_only": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}