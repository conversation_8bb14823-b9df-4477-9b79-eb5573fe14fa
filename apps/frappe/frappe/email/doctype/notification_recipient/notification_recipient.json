{"actions": [], "creation": "2014-07-11 17:19:37.037109", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["receiver_by_document_field", "receiver_by_role", "cc", "bcc", "condition"], "fields": [{"depends_on": "eval:parent.channel=='Email'", "description": "Optional: Always send to these ids. Each Email Address on a new row", "fieldname": "cc", "fieldtype": "Code", "label": "CC"}, {"depends_on": "eval:parent.channel=='Email'", "fieldname": "bcc", "fieldtype": "Code", "label": "BCC"}, {"description": "Expression, Optional", "fieldname": "condition", "fieldtype": "Data", "in_list_view": 1, "label": "Condition"}, {"fieldname": "receiver_by_document_field", "fieldtype": "Select", "in_list_view": 1, "label": "Receiver By Document Field"}, {"fieldname": "receiver_by_role", "fieldtype": "Link", "in_list_view": 1, "label": "Receiver By Role", "options": "Role"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2020-09-01 17:40:27.289105", "modified_by": "Administrator", "module": "Email", "name": "Notification Recipient", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC"}