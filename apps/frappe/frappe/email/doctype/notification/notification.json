{"actions": [], "allow_rename": 1, "autoname": "Prompt", "creation": "2014-07-11 17:18:09.923399", "doctype": "DocType", "document_type": "System", "engine": "InnoDB", "field_order": ["enabled", "is_standard", "module", "column_break_2", "channel", "slack_webhook_url", "filters", "subject", "event", "document_type", "col_break_1", "method", "date_changed", "days_in_advance", "value_changed", "sender", "send_system_notification", "sender_email", "section_break_9", "condition", "column_break_6", "html_7", "property_section", "set_property_after_alert", "property_value", "column_break_5", "send_to_all_assignees", "recipients", "message_sb", "message_type", "message", "message_examples", "view_properties", "column_break_25", "attach_print", "print_format"], "fields": [{"default": "1", "fieldname": "enabled", "fieldtype": "Check", "label": "Enabled"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"default": "Email", "depends_on": "eval: !doc.disable_channel", "fieldname": "channel", "fieldtype": "Select", "label": "Channel", "options": "Email\nSlack\nSystem Notification\nSMS", "reqd": 1, "set_only_once": 1}, {"depends_on": "eval:doc.channel=='Slack'", "description": "To use Slack Channel, add a <a href=\"#List/Slack%20Webhook%20URL/List\">Slack Webhook URL</a>.", "fieldname": "slack_webhook_url", "fieldtype": "Link", "label": "Slack Channel", "mandatory_depends_on": "eval:doc.channel=='Slack'", "options": "Slack Webhook URL"}, {"fieldname": "filters", "fieldtype": "Section Break", "label": "Filters"}, {"depends_on": "eval: in_list(['Email', 'Slack', 'System Notification'], doc.channel)", "description": "To add dynamic subject, use jinja tags like\n\n<div><pre><code>{{ doc.name }} Delivered</code></pre></div>", "fieldname": "subject", "fieldtype": "Data", "ignore_xss_filter": 1, "in_list_view": 1, "label": "Subject", "mandatory_depends_on": "eval: in_list(['Email', 'Slack', 'System Notification'], doc.channel)"}, {"fieldname": "document_type", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Document Type", "options": "DocType", "reqd": 1, "search_index": 1}, {"default": "0", "fieldname": "is_standard", "fieldtype": "Check", "label": "Is Standard", "no_copy": 1}, {"depends_on": "is_standard", "fieldname": "module", "fieldtype": "Link", "in_standard_filter": 1, "label": "<PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON>"}, {"fieldname": "col_break_1", "fieldtype": "Column Break"}, {"fieldname": "event", "fieldtype": "Select", "in_list_view": 1, "label": "Send <PERSON><PERSON>", "options": "\nNew\nSave\nSubmit\nCancel\nDays After\nDays Before\nValue Change\nMethod\nCustom", "reqd": 1, "search_index": 1}, {"depends_on": "eval:doc.event=='Method'", "description": "Trigger on valid methods like \"before_insert\", \"after_update\", etc (will depend on the DocType selected)", "fieldname": "method", "fieldtype": "Data", "label": "Trigger Method"}, {"depends_on": "eval:doc.document_type && (doc.event==\"Days After\" || doc.event==\"Days Before\")", "description": "Send alert if date matches this field's value", "fieldname": "date_changed", "fieldtype": "Select", "label": "Reference Date"}, {"default": "0", "depends_on": "eval:doc.document_type && (doc.event==\"Days After\" || doc.event==\"Days Before\")", "description": "Send days before or after the reference date", "fieldname": "days_in_advance", "fieldtype": "Int", "label": "Days Before or After"}, {"depends_on": "eval:doc.document_type && doc.event==\"Value Change\"", "description": "Send alert if this field's value changes", "fieldname": "value_changed", "fieldtype": "Select", "label": "Value Changed"}, {"depends_on": "eval: doc.channel == 'Email'", "fieldname": "sender", "fieldtype": "Link", "label": "Sender", "options": "<PERSON><PERSON> Account"}, {"fieldname": "sender_email", "fieldtype": "Data", "label": "Sender <PERSON><PERSON>", "options": "Email", "read_only": 1}, {"fieldname": "section_break_9", "fieldtype": "Section Break"}, {"description": "Optional: The alert will be sent if this expression is true", "fieldname": "condition", "fieldtype": "Code", "ignore_xss_filter": 1, "in_list_view": 1, "label": "Condition"}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"fieldname": "html_7", "fieldtype": "HTML", "options": "<p><strong>Condition Examples:</strong></p>\n<pre>doc.status==\"Open\"<br>doc.due_date==nowdate()<br>doc.total &gt; 40000\n</pre>\n"}, {"collapsible": 1, "fieldname": "property_section", "fieldtype": "Section Break", "label": "Set Property After Alert"}, {"fieldname": "set_property_after_alert", "fieldtype": "Select", "label": "Set Property After Alert"}, {"fieldname": "property_value", "fieldtype": "Data", "label": "Value To Be Set"}, {"depends_on": "eval:doc.channel !=\"Slack\"", "fieldname": "column_break_5", "fieldtype": "Section Break", "label": "Recipients"}, {"fieldname": "recipients", "fieldtype": "Table", "label": "Recipients", "mandatory_depends_on": "eval:doc.channel!=='Slack' && !doc.send_to_all_assignees", "options": "Notification Recipient"}, {"fieldname": "message_sb", "fieldtype": "Section Break", "label": "Message"}, {"default": "Add your message here", "fieldname": "message", "fieldtype": "Code", "ignore_xss_filter": 1, "label": "Message", "options": "<PERSON><PERSON>"}, {"fieldname": "message_examples", "fieldtype": "HTML", "label": "Message Examples", "options": "<h5>Message Example</h5>\n\n<pre>&lt;h3&gt;Order Overdue&lt;/h3&gt;\n\n&lt;p&gt;Transaction {{ doc.name }} has exceeded Due Date. Please take necessary action.&lt;/p&gt;\n\n&lt;!-- show last comment --&gt;\n{% if comments %}\nLast comment: {{ comments[-1].comment }} by {{ comments[-1].by }}\n{% endif %}\n\n&lt;h4&gt;Details&lt;/h4&gt;\n\n&lt;ul&gt;\n&lt;li&gt;Customer: {{ doc.customer }}\n&lt;li&gt;Amount: {{ doc.grand_total }}\n&lt;/ul&gt;\n</pre>"}, {"fieldname": "view_properties", "fieldtype": "<PERSON><PERSON>", "label": "View Properties (via Customize Form)"}, {"collapsible": 1, "collapsible_depends_on": "attach_print", "fieldname": "column_break_25", "fieldtype": "Section Break", "label": "Print Settings"}, {"default": "0", "fieldname": "attach_print", "fieldtype": "Check", "label": "Attach Print"}, {"depends_on": "attach_print", "fieldname": "print_format", "fieldtype": "Link", "label": "Print Format", "options": "Print Format"}, {"default": "0", "depends_on": "eval: doc.channel !== 'System Notification'", "description": "If enabled, the notification will show up in the notifications dropdown on the top right corner of the navigation bar.", "fieldname": "send_system_notification", "fieldtype": "Check", "label": "Send System Notification"}, {"default": "0", "depends_on": "eval:doc.channel == 'Email'", "fieldname": "send_to_all_assignees", "fieldtype": "Check", "label": "Send To All Assignees"}, {"default": "<PERSON><PERSON>", "depends_on": "is_standard", "fieldname": "message_type", "fieldtype": "Select", "label": "Message Type", "options": "Markdown\nHTML\nPlain Text"}], "icon": "fa fa-envelope", "index_web_pages_for_search": 1, "links": [], "modified": "2024-07-04 05:53:40.595130", "modified_by": "Administrator", "module": "Email", "name": "Notification", "naming_rule": "Set by user", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "export": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "subject", "track_changes": 1}