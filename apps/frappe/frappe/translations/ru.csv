A4,A4,
API Endpoint,Конечная точка API,
API Key,API Ключ,
Access Token,Маркер доступа,
Account,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>т,
Accounts Manager,Диспетчер учетных записей,
Accounts User,Пользователь Учетных записей,
Action,Действие,
Actions,Действия,
Active,Активен,
Add,Добавить,
Add Row,Добавить строку,
Address,Адрес,
Address Line 2,Адрес (2-я строка),
Address Title,Название адреса,
Address Type,Ти<PERSON> адреса,
Administrator,администратор,
All Day,Весь день,
Allow Delete,Разрешить удаление,
Amended From,Измененный С,
Amount,<PERSON><PERSON>мма,
Applicable For,Применимо для,
Approval Status,Статус утверждения,
Assign,Наз<PERSON>чить,
Assign To,Назначить в,
Attachment,Вложение,
Attachments,Приложения,
Author,Автор,
Auto Repeat,Автоматическое повторение,
Base URL,Базовый URL,
Based On,На основании,
Beginner,начинающий,
Billing,Выставление счетов,
Cancel,Отмена,
Category,Категория,
Category Name,Название категории,
City,Город,
City/Town,Город / поселок,
Client,Клиент,
Client ID,ID клиента,
Client Secret,Секрет клиента,
Closed,Закрыт,
Code,Код,
Collapse All,Свернуть все,
Color,Цвет,
Company Name,Название компании,
Condition,Условия,
Contact,Контакты,
Contact Details,Контактная информация,
Content,Содержимое,
Content Type,Тип контента,
Create,Создать,
Created By,Созданный,
Current,Текущий,
Custom HTML,Особый HTML,
Custom?,Пользовательские?,
Date Format,Формат даты,
Datetime,Datetime,
Day,День,
Default Letter Head,По умолчанию бланке,
Defaults,Значения по Умолчанию,
Delivery Status,Статус доставки,
Department,Отдел,
Details,Подробности,
Document Name,название документа,
Document Status,Статус документа,
Document Type,Тип документа,
Domain,Сфера деятельности,
Domains,Сферы деятельности,
Draft,Черновик,
Edit,Редактировать,
Email Account,Электронная почта,
Email Address,Адрес электронной почты,
Email ID,Email ID,
Email Sent,Письмо отправлено,
Email Template,Шаблон электронной почты,
Enable,Автоматическое обновление,
Enabled,Включено,
End Date,Дата окончания,
Error Code: {0},Код ошибки: {0},
Error Log,Журнал ошибок,
Event,Событие,
Expand All,Расширить все,
Fail,Потерпеть неудачу,
Failed,Не выполнено,
Fax,Факс,
Feedback,Обратная связь,
Female,Женский,
Field Name,Имя поля,
Fieldname,Имя поля,
Fields,Поля,
First Name,Имя,
Frequency,Частота,
Friday,Пятница,
From,От,
Full,Полный,
Full Name,Полное имя,
Further nodes can be only created under 'Group' type nodes,Дальнейшие узлы могут быть созданы только под узлами типа «Группа»,
Gender,Пол,
GitHub Sync ID,Идентификатор синхронизации GitHub,
Guest,Гость,
Half Day,Полдня,
Half Yearly,Половина года,
High,Высокий,
Hourly,Почасовой,
Hub Sync ID,Идентификатор синхронизации концентратора,
IP Address,IP адрес,
Image,Изображение,
Image View,Просмотр изображений,
Import Data,Импорт данных,
Import Log,Лог импорта,
Inactive,Неактивный,
Insert,Вставить,
Interests,Интересы,
Introduction,Введение,
Is Active,Активен,
Is Completed,Выполнен,
Is Default,По умолчанию,
Kanban Board,Канбан-доска,
Label,Имя метки,
Language Name,Название языка,
Last Name,Фамилия,
Leaderboard,Доска почёта,
Letter Head,Печатный бланк,
Level,Уровень,
Limit,Лимит,
Log,Запись в журнале,
Logs,Журналы,
Low,Низкий,
Maintenance Manager,Менеджер обслуживания,
Maintenance User,Сотрудник обслуживания,
Male,Мужской,
Mandatory,Обязательный,
Mapping,Картографирование,
Mapping Type,Тип отображения,
Medium,Средний,
Meeting,Встреча,
Message Examples,Пример сообщения,
Method,Метод,
Middle Name,Второе имя,
Middle Name (Optional),Отчество (необязательно),
Monday,Понедельник,
Monthly,Ежемесячно,
More,Далее,
More Information,Больше информации,
Move,Переместить,
My Account,Мой аккаунт,
New Address,Новый адрес,
New Contact,Новый контакт,
Next,Далее,
No Data,Нет данных,
No address added yet.,Адресов пока нет.,
No contacts added yet.,Контакты еще не добавлены.,
No items found.,Ничего не найдено.,
None,Никто,
Not Permitted,Не разрешено,
Not active,Не действует,
Number,Число,
Online,В сети,
Operation,Операция,
Options,Опции,
Other,Другое,
Owner,Владелец,
Page Missing or Moved,Страница отсутствует или перемещена,
Parameter,Параметр,
Password,Пароль,
Period,Период обновления,
Pincode,PIN код,
Please enable pop-ups,"Пожалуйста, включите всплывающие окна",
Please select Company,"Пожалуйста, выберите компанию",
Please select {0},"Пожалуйста, выберите {0}",
Please set Email Address,"Пожалуйста, установите адрес электронной почты",
Portal Settings,Настройки портала,
Preview,Просмотр,
Primary,Основной,
Print Format,Печатный бланк,
Print Settings,Настройки печати,
Print taxes with zero amount,Печать налогов с нулевой суммой,
Private,Личные,
Property,Свойство,
Public,Публично,
Published,Опубликовано,
Purchase Manager,Менеджер закупок,
Purchase Master Manager,Мастер закупок,
Purchase User,Специалист закупок,
Query Options,Параметры запроса,
Range,Диапазон,
Rating,Рейтинг,
Received,Получено,
Recipients,Получатели,
Redirect URL,Перенаправление URL,
Reference,Справка,
Reference Date,Ссылка Дата,
Reference Document,Справочный документ,
Reference Document Type,Ссылка Тип документа,
Reference Owner,Владелец ссылки,
Reference Type,Тип ссылки,
Refresh Token,Токен обновления,
Region,Область,
Rejected,Отклоненные,
Reopen,Возобновить,
Replied,С ответом,
Report,Отчет,
Report Builder,Конструктор отчетов,
Report Type,Тип отчета,
Reports,Отчеты,
Response,Ответ,
Role,Роль,
Route,Маршрут,
Sales Manager,Менеджер продаж,
Sales Master Manager,Мастер продаж,
Sales User,Продавец,
Salutation,Обращение,
Sample,Образец,
Saturday,Суббота,
Saved,Сохраненные,
Scheduled,Запланированно,
Search,Поиск,
Secret Key,Секретный ключ,
Select,Выбрать,
Select DocType,Выберите тип документа,
Send Now,Отправить сейчас,
Sent,Отправлено,
Series {0} already used in {1},Идентификатор {0} уже используется в {1},
Service,Услуги,
Set as Default,Установить по умолчанию,
Settings,Настройки,
Shipping,Доставка,
Short Name,Короткое имя,
Slideshow,Слайд-шоу,
Source,Источник,
Source Name,Имя источника,
Standard,Стандартный,
Start Date,Дата начала,
Start Import,Начать импорт,
State,Состояние,
Stopped,Приостановлено,
Subject,Тема,
Submit,Подписать,
Summary,Резюме,
Sunday,Воскресенье,
System Manager,Менеджер системы,
Target,Цель,
Task,Задача,
Tax Category,Налоговая категория,
Test,Тест,
Thank you,Спасибо,
The page you are looking for is missing. This could be because it is moved or there is a typo in the link.,"Страница, которую вы ищете, не найдена. Возможно, она перемещена, или в ссылке допущена опечатка",
Timespan,Промежуток времени,
To,К,
To Date,До,
Traceback,Диагностика,
URL,URL,
Unsubscribed,Отписался,
User,Пользователь,
User ID,ID пользователя,
Users,Пользователи,
Validity,Период действия,
Warning,Предупреждение,
Website,Сайт,
Website Manager,Менеджер сайта,
Website Settings,Настройки сайта,
Wednesday,Среда,
Week,Неделя,
Weekdays,Будни,
Weekly,Еженедельно,
Welcome email sent,Письмо с приглашением в систему отправлено,
Workflow,Поток,
You need to be logged in to access this page,"Вы должны войти в систему, чтобы получить доступ к этой странице",
old_parent,old_parent,
{0} is mandatory,{0} является обязательным,
 to your browser,в ваш браузер,
"""Company History""","""История компании""",
"""Parent"" signifies the parent table in which this row must be added","«Родитель» означает родительскую таблицу, в которую должна быть добавлена эта строка",
"""Team Members"" or ""Management""","""Члены команды"" или ""Управление""",
&lt;head&gt; HTML,&lt;head&gt; HTML,
'In Global Search' not allowed for type {0} in row {1},«В глобальном поиске» не допускается тип {0} в строке {1},
'In List View' not allowed for type {0} in row {1},"""Режим Просмотра Списком"" не допускается для типа {0} в строке {1}",
'Recipients' not specified,«Получатели» не указаны,
(Ctrl + G),(Ctrl + G),
** Failed: {0} to {1}: {2},** Не удалось: {0} до {1}: {2},
**Currency** Master,**Валютный** Мастер,
0 - Draft; 1 - Submitted; 2 - Cancelled,0 - Черновики; 1 - Принятые; 2 - Отмененные,
0 is highest,0 является самым высоким,
1 Currency = [?] Fraction\nFor e.g. 1 USD = 100 Cent,"1 Валюта = [?] Фракция \n, например, 1 USD = 100 центов",
1 comment,1 комментарий,
1 hour ago,1 час назад,
1 minute ago,1 минуту назад,
1 month ago,1 месяц назад,
1 year ago,1 год назад,
; not allowed in condition,; не допускается в состоянии,
"<h4>Default Template</h4>\n<p>Uses <a href=""http://jinja.pocoo.org/docs/templates/"">Jinja Templating</a> and all the fields of Address (including Custom Fields if any) will be available</p>\n<pre><code>{{ address_line1 }}&lt;br&gt;\n{% if address_line2 %}{{ address_line2 }}&lt;br&gt;{% endif -%}\n{{ city }}&lt;br&gt;\n{% if state %}{{ state }}&lt;br&gt;{% endif -%}\n{% if pincode %} PIN:  {{ pincode }}&lt;br&gt;{% endif -%}\n{{ country }}&lt;br&gt;\n{% if phone %}Phone: {{ phone }}&lt;br&gt;{% endif -%}\n{% if fax %}Fax: {{ fax }}&lt;br&gt;{% endif -%}\n{% if email_id %}Email: {{ email_id }}&lt;br&gt;{% endif -%}\n</code></pre>","<h4>Шаблон по умолчанию</h4>\n<p>Используя <a href=""http://jinja.pocoo.org/docs/templates/"">шаблонизатор Jinja</a> и все поля адреса (включая настраиваемые поля, если таковые имеются) будет доступно</p>\n<pre><code>{{ address_line1 }}&lt;br&gt;\n{% if address_line2 %}{{ address_line2 }}&lt;br&gt;{% endif -%}\n{{ city }}&lt;br&gt;\n{% if state %}{{ state }}&lt;br&gt;{% endif -%}\n{% if pincode %} PIN:  {{ pincode }}&lt;br&gt;{% endif -%}\n{{ country }}&lt;br&gt;\n{% if phone %}Телефон: {{ phone }}&lt;br&gt;{% endif -%}\n{% if fax %}Факс: {{ fax }}&lt;br&gt;{% endif -%}\n{% if email_id %}Email: {{ email_id }}&lt;br&gt;{% endif -%}\n</code></pre>",
A Lead with this Email Address should exist,Лид с этим адресом электронной почты должно существовать,
A list of resources which the Client App will have access to after the user allows it.<br> e.g. project,"Список ресурсов, к которым клиентское приложение будет иметь доступ после того, как пользователь разрешит это.<br> например. проект",
A log of request errors,Журнал ошибок запроса,
A new account has been created for you at {0},Новая учетная запись была создана для вас в {0},
A symbol for this currency. For e.g. $,"Символ этой валюты. Например, ₽",
A word by itself is easy to guess.,"Слово, по которому нетрудно догадаться.",
API Endpoint Args,API Endpoint Args,
API Key cannot be  regenerated,API-ключ нельзя восстановить,
API Password,API Пароль,
API Secret,API Секретный,
API Username,API Имя пользователя,
ASC,ASC,
About Us Settings,Настройки «О нас»,
About Us Team Member,О члене комманды,
Accept Payment,Подтвердить платеж,
Access Key ID,Идентификатор ключа доступа,
Access Token URL,URL токена доступа,
Action Failed,Ошибка действия,
Action Timeout (Seconds),Тайм-аут действия (секунды),
"Actions for workflow (e.g. Approve, Cancel).","Действия для потока (например: утвердить, отменить).",
Active Domains,Активные домены,
Active Sessions,Активные сессии,
Activity Log,Журнал активности,
Activity log of all users.,Журнал активность всех пользователей.,
Add / Manage Email Domains.,Добавление / Управление доменами электронной почты,
Add / Update,Добавить / обновить,
Add A New Rule,Добавить новое правило,
Add Another Comment,Добавить еще один комментарий,
Add Attachment,Добавить приложение,
Add Column,Добавить столбец,
Add Contact,Добавить контакт,
Add Contacts,Добавить контакты,
Add Filter,Добавить фильтр,
Add Group,Добавить группу,
Add New Permission Rule,Добавить новое правило доступа,
Add Review,Добавить отзыв,
Add Signature,Добавить подпись,
Add Subscribers,Добавить подписчиков,
Add Total Row,Добавить итоговую строку,
Add Unsubscribe Link,Добавить ссылку Отписаться,
Add User Permissions,Добавить разрешения пользователя,
Add a New Role,Добавить новую роль,
Add a column,Добавить столбец,
Add a comment,Добавить комментарий,
Add a new section,Добавить новый раздел,
Add a tag ...,Добавить тег ...,
Add all roles,Добавить все роли,
Add custom forms.,Добавить пользовательские формы.,
Add custom javascript to forms.,Добавить пользовательские JavaScript в формах.,
Add fields to forms.,Добавление полей в формах.,
Add meta tags to your web pages,Добавьте метатеги на свои веб-страницы,
Add script for Child Table,Добавить скрипт для дочерней таблицы,
Add to table,Добавить в таблицу,
Add your own translations,Добавить свои собственные переводы,
"Added HTML in the &lt;head&gt; section of the web page, primarily used for website verification and SEO","Добавлена HTML в &lt;HEAD&gt; часть веб-страницы, в основном используется для проверки веб-сайтов и SEO",
Added {0},Добавлено {0},
Adding System Manager to this User as there must be atleast one System Manager,"Добавление этого пользователя в менеджеры системы, так как должен быть хотя бы один системный менеджер",
Additional Permissions,Дополнительные разрешения,
Address Template,Шаблон адреса,
Address Title is mandatory.,Название адреса является обязательным.,
Address and other legal information you may want to put in the footer.,"Адрес и другое юридическое информация, которую вы можете положить в нижней.",
Addresses And Contacts,Адреса и контакты,
Adds a client custom script to a DocType,Добавляет клиентский пользовательский скрипт в DocType,
Adds a custom field to a DocType,Добавляет настраиваемое поле в DocType,
Admin,Администратор,
Administrator Logged In,Администратор вошел в систему,
Administrator accessed {0} on {1} via IP Address {2}.,Администратор обращались {0} {1} с помощью IP-адреса {2}.,
Advanced,Продвинутый,
Advanced Control,Расширенный контроль,
Advanced Search,Расширенный поиск,
Align Labels to the Right,Выровнять метки по правому краю,
Align Value,Значение выравнивания,
All Images attached to Website Slideshow should be public,"Все изображения, прикрепленные к Слайд-шоу веб-сайта, должны быть общедоступными",
All customizations will be removed. Please confirm.,"Все настройки будут удалены. Пожалуйста, подтвердите.",
"All possible Workflow States and roles of the workflow. Docstatus Options: 0 is""Saved"", 1 is ""Submitted"" and 2 is ""Cancelled""","Все возможные статусы и роли бизнес-процесса. Варианты статуса документа: 0 — ""Сохранён"", 1 — ""Проведён/Утвержден"" и 2 — ""Отменен""",
All-uppercase is almost as easy to guess as all-lowercase.,"Все заглавные почти так же легко угадать, как и все строчные.",
Allocated To,Выделяемых на,
Allow,Разрешить,
Allow Bulk Edit,Разрешить массовое редактирование,
Allow Comments,Разрешить комментарии,
Allow Consecutive Login Attempts ,Разрешить последовательные попытки авторизации,
Allow Dropbox Access,Разрешить доступ Dropbox,
Allow Edit,Разрешить редактирование,
Allow Guest to View,"Разрешить для гостей, чтобы посмотреть",
Allow Import (via Data Import Tool),Разрешить импорт (с помощью инструмента импорта данных),
Allow Incomplete Forms,Разрешить неполные формы,
Allow Login After Fail,Разрешить авторизацию после неудачной попытки,
Allow Login using Mobile Number,Разрешить вход в систему с помощью мобильного номера,
Allow Login using User Name,Разрешить использование имени пользователя,
Allow Modules,Разрешить модули,
Allow Multiple,Разрешить многократный,
Allow Print,Разрешить печать,
Allow Print for Cancelled,Разрешить печать для отмененых,
Allow Print for Draft,Разрешить печать для черновика,
Allow Read On All Link Options,Разрешить чтение всех ссылок,
Allow Rename,Разрешить переименовать,
Allow Roles,Разрешить ролям,
Allow Self Approval,Разрешить самостоятельное утверждение,
Allow approval for creator of the document,Разрешает утверждать создателю документа,
Allow events in timeline,Разрешить события на временной шкале,
Allow in Quick Entry,Разрешить в быстрой записи,
Allow on Submit,Разрешить проведение,
Allow only one session per user,Разрешить только один сеанс для каждого пользователя,
Allow page break inside tables,Разрешить разрыв страницы внутри таблиц,
Allow saving if mandatory fields are not filled,"Разрешить сохранение, если обязательные поля не заполнены",
Allow user to login only after this hour (0-24),Разрешить пользователю входить только после этого часа (0—24),
Allow user to login only before this hour (0-24),Разрешать пользователю входить только до этого часа (0—24),
Allowed,Разрешено,
Allowed In Mentions,Разрешено в упоминаниях,
"Allowing DocType, DocType. Be careful!","Разрешение DocType, DocType. Будьте осторожны!",
Already Registered,Уже регистрировались,
Also adding the dependent currency field {0},Также добавление зависимого валютного поля {0},
"Always add ""Draft"" Heading for printing draft documents","Всегда добавлять в заголовок Черновик"" при печати черновых документов""",
Always use Account's Email Address as Sender,Всегда использовать почту учетной записи в качестве отправителя,
Always use Account's Name as Sender's Name,Всегда использовать имя учетной записи в качестве имени отправителя,
Amend,Изменен,
Amending,Исправление,
Amount Based On Field,Сумма на основании поля,
Amount Field,Поле суммы,
Amount must be greater than 0.,Сумма должна быть больше 0.,
An error occured during the payment process. Please contact us.,В процессе оплаты произошла ошибка. Пожалуйста свяжитесь с нами.,
An icon file with .ico extension. Should be 16 x 16 px. Generated using a favicon generator. [favicon-generator.org],Значок файла с расширением .ico расширением. Должно быть 16 х 16 пикселей. Генерируется с использованием генератора FavIcon. [favicon-generator.org],
Ancestors Of,Предки,
Another transaction is blocking this one. Please try again in a few seconds.,"Другой сделка блокирования этот. Пожалуйста, попробуйте еще раз через несколько секунд.",
"Another {0} with name {1} exists, select another name","Еще {0} с именем {1} существует, выберите другое имя",
Any string-based printer languages can be used. Writing raw commands requires knowledge of the printer's native language provided by the printer manufacturer. Please refer to the developer manual provided by the printer manufacturer on how to write their native commands. These commands are rendered on the server side using the Jinja Templating Language.,"Можно использовать любые языки принтера на основе строк. Для написания необработанных команд требуется знание родного языка принтера, предоставляемого производителем принтера. Пожалуйста, обратитесь к руководству разработчика, предоставленному производителем принтера, о том, как писать собственные команды. Эти команды отображаются на стороне сервера с использованием языка шаблонов Jinja.",
"Apart from System Manager, roles with Set User Permissions right can set permissions for other users for that Document Type.","Помимо менеджера системы, роли с правами Установки прав пользователя, могут установить разрешения для других пользователей для этого типа документов.",
Api Access,Доступ к API,
App,Приложение,
App Access Key,Ключ доступа к приложению,
App Client ID,ID клиента приложения,
App Client Secret,Секрет клиента приложения,
App Name,Имя приложения,
App Secret Key,Секретный ключ приложения,
App not found,Приложение не найдено,
App {0} already installed,Приложение {0} уже установлено,
App {0} is not installed,Приложение {0} не установленно,
Append To,Добавить к,
Append To can be one of {0},Добавить к может быть одним из {0},
Append To is mandatory for incoming mails,Добавить к является обязательным для входящих сообщений,
"Append as communication against this DocType (must have fields, ""Status"", ""Subject"")","Добавить как коммуникацию для этого DocType (должен иметь поля, ""Статус"", ""Тема"")",
Applicable Document Types,Применимые типы документов,
Apply,Применить,
Apply Strict User Permissions,Применение строгих пользовательских разрешений,
Apply To All Document Types,Применить ко всем типам документов,
Apply this rule if the User is the Owner,"Применить это правило, если пользователь является владелец",
Apply to all Documents Types,Применить ко всем типам документов,
Appreciate,понимать,
Appreciation,Признательность,
Archive,Архив,
Archived,Архивные,
Archived Columns,Архивные столбцы,
Are you sure you want to delete the attachment?,"Вы уверены, что хотите удалить вложение?",
Are you sure you want to relink this communication to {0}?,"Вы уверены, что хотите перелинковать эту коммуникацию на {0}?",
Are you sure?,Вы уверены?,
Arial,Arial,
"As a best practice, do not assign the same set of permission rule to different Roles. Instead, set multiple Roles to the same User.","В качестве лучшего решения, не присваивайте одинаковый набор правил доступа для различных ролей. Вместо этого, установите несколько ролей одному и тому же пользователю.",
Assign Condition,Назначить условие,
Assign To Users,Назначить пользователям,
"Assign one by one, in sequence","Назначить по одному, в последовательности",
Assign to me,Назначить самому себе,
Assign to the one who has the least assignments,"Назначьте тому, у кого меньше всего заданий",
Assigned,Назначенный,
Assigned By,Назначаются,
Assigned By Full Name,Присваиваемый Полное имя,
Assigned By Me,Назначенные мной,
Assigned To,Назначено для,
Assigned To/Owner,Назначено для/Владелец,
Assignment,Назначение,
Assignment Complete,Задание выполнено,
Assignment Completed,Задание выполнено,
Assignment Rule,Правило назначения,
Assignment Rule User,Правило назначения пользователя,
Assignment Rules,Правила назначения,
Assignment closed by {0},Назначение закрыт {0},
Assignment for {0} {1},Назначение для {0} {1},
Atleast one field of Parent Document Type is mandatory,По крайней мере одно поле родительского документа является обязательным,
Attach,Прикрепить,
Attach Document Print,Прикрепить печатный документ,
Attach Image,Прикрепить изображение,
Attach Print,Прикрепите печать,
Attach Your Picture,Прикрепите свою фотографию,
Attach file for Import,Прикрепить файл для импорта,
Attach files / urls and add in table.,Прикрепить файлы / URL-адреса и добавить в таблицу.,
Attached To DocType,Прикреплено к DocType,
Attached To Field,Прикрепленный к полю,
Attached To Name,Привязаны к имени,
Attachment Limit (MB),Лимит вложения (MB),
Attachment Removed,Вложение удалено,
Attempting Connection to QZ Tray...,Попытка подключения к QZ Tray...,
Attempting to launch QZ Tray...,Попытка запустить QZ Tray...,
Auth URL Data,Данные URL-адреса аутентификации,
Authenticating...,Проверка подлинности...,
Authentication,Аутентификация,
Authentication Apps you can use are: ,"Приложения аутентификации, которые вы можете использовать:",
Authentication Credentials,Аутентификационные учетные данные,
Authorization Code,Код авторизации,
Authorize URL,Авторизованный URL,
Authorized,Авторизованный,
Auto,Авто,
Auto Email Report,Автоотчет по электронной почте,
Auto Name,Авто имя,
Auto Reply Message,Автоматический ответ,
Auto assignment failed: {0},Автоматическое назначение не выполнено: {0},
Automatically Assign Documents to Users,Автоматически назначать документы пользователям,
Automation,Автоматизация,
Avatar,Аватар,
Average,Средний,
Average of {0},Среднее из {0},
Avoid dates and years that are associated with you.,"Избегайте дат и лет, которые связаны с вами.",
Avoid recent years.,Избегайте в последние годы.,
Avoid sequences like abc or 6543 as they are easy to guess,"Избегайте последовательностей как аЬс или 6543, поскольку они легко угадать",
Avoid years that are associated with you.,"Избегайте лет, которые связаны с вами.",
Awaiting Password,В ожидании пароля,
Away,Далеко,
BCC,Скрытая копия,
Back to Desk,Вернуться к списку,
Back to Login,Вернуться на страницу входа,
Background Color,Цвет фона,
Background Email Queue,Фоновая очередь электронной почты,
Background Jobs,Фоновые задания,
Background Workers,Фоновые процессы,
Backup,Резервное копирование,
Backup Frequency,Частота резервного копирования,
Backup Limit,Ограничение резервного копирования,
Backup job is already queued. You will receive an email with the download link,Задача резервного копирования уже поставлена в очередь. Вы получите электронное письмо с ссылкой для скачивания,
Backups,Резервные копии,
Banner,Баннер,
Banner HTML,HTML Баннер,
Banner Image,Изображение баннера,
Banner is above the Top Menu Bar.,Баннер над верхним  меню.,
Bar,Бар,
Base Distinguished Name (DN),Основание различающееся имя (DN),
Based on Permissions For User,На основании разрешения для пользователя,
Beta,Бета,
Better add a few more letters or another word,Лучше добавить еще несколько букв или другое слово,
Between,Между,
Bio,Биография,
Birth Date,Дата рождения,
Block Module,Блок модуль,
Block Modules,Блок модули,
Blocked,Блокирован,
Blog,Блог,
Blog Category,Категория блога,
Blog Intro,Блог Intro,
Blog Introduction,Блог введение,
Blog Post,Пост блога,
Blog Settings,Настройки блога,
Blog Title,Название блога,
Blogger,Блоггер,
Bot,Бот,
Both DocType and Name required,И DocType и имя требуется,
Both login and password required,Необходимо ввести логин и пароль,
Bounced,Возвращенные,
Braintree Settings,Настройки Braintree,
Braintree payment gateway settings,Настройки шлюза платежных данных Braintree,
Brand HTML,Брендирование HTML,
Brand Image,Изображение бренда,
Breadcrumbs,Хлебные крошки,
Browser not supported,Браузер не поддерживается,
Brute Force Security,Защита от перебора,
Build Report,Построить отчет,
Bulk Delete,Массовое удаление,
Bulk Edit {0},Массовая правка {0},
Bulk Rename,Массовое переименование,
Bulk Update,Массовое обновление,
Button,Кнопка,
Button Help,Кнопка помощь,
Button Label,Кнопка метка,
Bypass Two Factor Auth for users who login from restricted IP Address,"Обход двух факторов для пользователей, которые подключаются с ограниченным IP-адресом",
Bypass restricted IP Address check If Two Factor Auth Enabled,Обход ограниченного IP-адреса. Если включен параметр Two Factor Auth,
CC,Копия,
"CC, BCC & Email Template","Копия, Скрытая копия и Шаблон электронной почты",
CSS,CSS,
CSV,CSV,
Cache Cleared,Кэш очищен,
Calculate,Рассчитать,
Calendar Name,Имя календаря,
Calendar View,Просмотр календаря,
Call,Звонок,
Can Read,Может читать,
Can Share,Может поделиться,
Can Write,Может изменять,
Can't identify open {0}. Try something else.,Не удается идентифицировать открытое {0}. Попробуйте что-нибудь другое.,
Can't save the form as data import is in progress.,Невозможно сохранить форму в процессе импорта данных.,
Cancel {0} documents?,Отменить {0} документы?,
Cancelled Document restored as Draft,Отмененный документ восстановлен как черновик,
Cancelling,Отмена,
Cancelling {0},Отмена {0},
Cannot Remove,Не удается удалить,
Cannot cancel before submitting. See Transition {0},Нельзя отменить перед проведением. См. транзакцию {0},
Cannot change docstatus from 0 to 2,Нельзя изменить статус документа с 0 на 2,
Cannot change docstatus from 1 to 0,Нельзя изменить статус документа с 1 на 0,
Cannot change header content,Невозможно изменить содержимое заголовка,
Cannot change state of Cancelled Document. Transition row {0},Невозможно изменить состояние Отмененные документа. Переход строка {0},
Cannot change user details in demo. Please signup for a new account at https://erpnext.com,Невозможно изменить данные пользователя в демо. Создайте новую учетную запись на https://erpnext.com,
Cannot create a {0} against a child document: {1},Невозможно создать {0} против дочернего документа: {1},
Cannot delete Home and Attachments folders,Не удается удалить Дома и папки Вложения,
Cannot delete file as it belongs to {0} {1} for which you do not have permissions,"Не удается удалить файл, поскольку он принадлежит {0} {1}, для которого у вас нет прав доступа",
Cannot delete or cancel because {0} {1} is linked with {2} {3} {4},"Невозможно удалить или отменить, поскольку {0} {1} связан с {2} {3} {4}",
Cannot delete standard field. You can hide it if you want,"Не удается удалить стандартное поле. Вы можете скрыть его, если вы хотите",
Cannot delete {0},Не удается удалить {0},
Cannot delete {0} as it has child nodes,"Не удается удалить {0}, поскольку он имеет дочерние узлы",
"Cannot edit Standard Notification. To edit, please disable this and duplicate it","Не удается изменить стандартное уведомление. Чтобы отредактировать, отключите это и продублируйте его.",
Cannot edit a standard report. Please duplicate and create a new report,"Изменять стандартный отчёт нельзя. Пожалуйста, создайте новый отчёт из копии",
Cannot edit cancelled document,Нельзя редактировать отмененый документ,
Cannot edit standard fields,Нельзя редактировать стандартные поля,
Cannot have multiple printers mapped to a single print format.,"Невозможно иметь несколько принтеров, сопоставленных с одним форматом печати.",
Cannot link cancelled document: {0},Нельзя привязать отмененый документ: {0},
Cannot map because following condition fails: ,"Невозможно отобразить, потому что не выполняется следующее условие:",
Cannot move row,Невозможно переместить строку,
Cannot open instance when its {0} is open,"Не могу открыть экземпляр, когда его {0} открыто",
Cannot open {0} when its instance is open,"Не могу открыть {0}, когда его экземпляр открыт",
Cannot remove ID field,Не удается удалить поле ID,
Cannot set Notification on Document Type {0},Невозможно установить уведомление о типе документа {0},
Cannot update {0},Не удается обновить {0},
Cannot use sub-query in order by,Невозможно использовать подзапрос в порядке,
Cannot {0} {1},Невозможно {0} {1},
Capitalization doesn't help very much.,Капитализация не очень поможет.,
Card Details,Детали карты,
Categorize blog posts.,Классифицировать посты блога.,
Category Description,Описание категории,
Cent,Цент,
"Certain documents, like an Invoice, should not be changed once final. The final state for such documents is called Submitted. You can restrict which roles can Submit.","Некоторые документы, как, например, счета-фактуры, не подлежат изменению, если являются финальными. Конечное состояние для таких документов называется Проведенный. Вы можете ограничить, какие роли имеют право проводить документы.",
Chain Integrity,Целостность цепей,
Chaining Hash,Цепочный хэш,
Change Label (via Custom Translation),Изменение метки (с помощью пользовательского перевода),
Change Password,Изменить пароль,
"Change field properties (hide, readonly, permission etc.)","Изменение свойств поля (скрыть, только для чтения, доступ и т.д.)",
Channel,Канал,
Chart Name,Название диаграммы,
Chart Options,Параметры диаграммы,
Chart Source,Источник диаграммы,
Chart Type,Тип диаграммы,
Charts,Графики,
Chat,Чат,
Chat Background,История чата,
Chat Message,Чат-сообщение,
Chat Operators,Операторы чата,
Chat Profile,Профиль пользователя чата,
Chat Profile for User {0} exists.,Профиль чата для пользователя {0} существует.,
Chat Room,Общение,
Chat Room Name,Имя в чате,
Chat Room User,Пользователь комнаты чата,
Chat Token,Чат-токен,
Chat Type,Тип чата,
Chat messages and other notifications.,Сообщения чата и другие уведомления.,
Check,Проверьте,
Check Request URL,Проверить URL-адрес заявки,
"Check columns to select, drag to set order.","Проверьте колонки, чтобы выбрать, перетащите, чтобы задать порядок.",
Check this if you are testing your payment using the Sandbox API,"Включите, если вы проверяете платёж с помощью API-песочницы",
Check this to pull emails from your mailbox,"Отметьте это, чтобы получать письма из вашего почтового ящика",
Check which Documents are readable by a User,"Проверьте, какие документы могут быть прочитаны Пользователем",
Checking one moment,Проверка одного момента,
Checksum Version,Версия контрольной суммы,
Child Table Mapping,Отображение детского стола,
Child Tables are shown as a Grid in other DocTypes,Дочерние таблицы отображаются в виде таблицы в других типах документов,
Choose authentication method to be used by all users,"Выберите метод аутентификации, который будет использоваться всеми пользователями.",
Clear Error Logs,Очистить журналы ошибок,
Clear User Permissions,Очистить разрешения пользователя,
Clear all roles,Отчистить все роли,
"Clearing end date, as it cannot be in the past for published pages.","Очистка даты окончания, поскольку она не может быть в прошлом для опубликованных страниц.",
Click here to post bugs and suggestions,"Нажмите здесь, чтобы сообщать об ошибках и предложениях",
Click here to verify,"Нажмите здесь, чтобы проверить,",
Click on the link below to complete your registration and set a new password,"Нажмите на ссылку ниже, чтобы завершить регистрацию и задать новый пароль",
Click on the link below to download your data,"Нажмите на ссылку ниже, чтобы загрузить ваши данные",
Click on the link below to verify your request,"Нажмите на ссылку ниже, чтобы подтвердить свой запрос",
Click table to edit,Нажмите для редактирования таблиц,
Click to Set Filters,"Нажмите, чтобы установить фильтры",
Clicked,Нажато,
Client Credentials,Учетные данные клиента,
Client Information,Информация о клиенте,
Client Script,Скрипт клиента,
Client URLs,URL-адреса клиентов,
Client side script extensions in Javascript,Расширения клиентский сценарий в Javascript,
Collapsible,Сворачиваемый,
Collapsible Depends On,Сворачиваемый - зависит от,
Column,Колонка,
Column <b>{0}</b> already exist.,Столбец <b>{0}</b> уже существует.,
Column Break,Разрыв столбца,
Column Labels:,Колонка меток:,
Column Name,Имя столбца,
Column Name cannot be empty,Имя столбца не может быть пустым,
Columns,Колонки,
Columns based on,Колонки на основе…,
Combination of Grant Type (<code>{0}</code>) and Response Type (<code>{1}</code>) not allowed,Сочетание типа гранта ( <code>{0}</code> ) и типа ответа ( <code>{1}</code> ) не допускается,
Comment By,Комментарий по,
Comment Email,Комментарий по электронной почте,
Comment Type,Тип комментариев,
Comment can only be edited by the owner,Комментарий может редактировать только владелец,
Commented on {0}: {1},Комментарии {0}: {1},
Comments and Communications will be associated with this linked document,Комментарии и коммуникации будут связаны с этой связанного документа,
Comments cannot have links or email addresses,Комментарии не могут иметь ссылки или адреса электронной почты,
Common names and surnames are easy to guess.,Общие имена и фамилии легко догадаться.,
Communicated via {0} on {1}: {2},Сообщается через {0} {1}: {2},
Communication Type,Вид коммуникации,
Company History,История компании,
Company Introduction,Компания - введение,
Compiled Successfully,Успешно скомпилировано,
Complete By,Завершить до,
Complete Registration,Полная регистрация,
Complete Setup,Завершение установки,
Compose Email,Написать письмо,
Condition Detail,Детализация условий,
Conditions,Условия,
Configure Chart,Настроить диаграмму,
Configure Charts,Настроить графики,
Confirm,Подтвердить,
Confirm Deletion of Data,Подтвердите удаление данных,
Confirm Request,Подтвердите запрос,
Confirm Your Email,Подтвердите ваш адрес электронной почты,
Confirmed,Подтвердил,
Connected to QZ Tray!,Подключен к QZ Tray!,
Connection Name,Название соединения,
Connection Success,Успешно подключено,
Connection lost. Some features might not work.,Соединение потеряно. Некоторые функции могут не работать.,
Connector Name,Имя соединителя,
Connector Type,Тип соединителя,
Contact Us Settings,Контакты Настройки,
"Contact options, like ""Sales Query, Support Query"" etc each on a new line or separated by commas.","Параметры контакта, как ""Запрос на продажу, Запрос в службу поддержки"" и т.д. каждого с новой строки или через запятую.",
Contacts,Контакты,
Content (HTML),Контент (HTML),
Content (Markdown),Контент (Markdown),
Content Hash,Контент Hash,
Content web page.,Контент веб-страницы.,
Conversation Tones,Разговорные тона,
Copyright,Авторское право,
Core,Центр управления,
Core DocTypes cannot be customized.,Основные DocTypes не могут быть настроены.,
Could not connect to outgoing email server,Не удалось подключиться к серверу исходящей почты,
Could not find {0},Не удалось найти {0},
Could not find {0} in {1},Не удалось найти {0} в {1},
Could not identify {0},Не удалось определить {0},
Count,подсчитывать,
Country Name,Название страны,
County,Округ,
Create Chart,Создать диаграмму,
Create New,Создать,
Create Post,Создать пост,
Create User Email,Создать электронной почты пользователя,
Create a New Format,Создать новый бланк,
Create a new record,Создать новую запись,
Create a new {0},Создать {0},
Create and Send Newsletters,Создание и отправка рассылки,
Create and manage newsletter,Создать и управлять рассылкой,
Created,Созданный,
Created Custom Field {0} in {1},Дата создания настраиваемого поля {0} в {1},
Created On,Дата создания,
Criticism,Критика,
Criticize,Критиковать,
Ctrl+Enter to add comment,"Ctrl+Enter, чтобы добавить комментарий",
Currency Name,Название валюты,
Currency Precision,Точность валюты,
Current Mapping,Текущее сопоставление,
Current Mapping Action,Текущие действия по составлению карт,
Current Mapping Delete Start,Текущее сопоставление Удалить начало,
Current Mapping Start,Текущий запуск картографирования,
Current Mapping Type,Текущий тип отображения,
Currently Viewing,Сейчас просматривают,
Currently updating {0},В настоящее время обновление {0},
Custom,Пользовательские,
Custom Base URL,Пользовательский базовый URL,
Custom CSS,Пользовательский CSS,
Custom DocPerm,Пользовательские DocPerm,
Custom Field,Пользовательские поля,
Custom Fields can only be added to a standard DocType.,Пользовательские поля могут быть добавлены только к стандартному типу документа.,
Custom Fields cannot be added to core DocTypes.,Пользовательские поля не могут быть добавлены в основные типы документов.,
Custom Format,Пользовательский формат,
Custom HTML Help,Пользовательский HTML Помощь,
Custom JS,Пользовательский JS,
Custom Menu Items,Пользовательские пункты меню,
Custom Report,Пользовательский отчет,
Custom Reports,Пользовательские отчеты,
Custom Role,Пользовательские роли,
Custom Script,Пользовательский сценарий,
Custom Sidebar Menu,Пользовательское боковое меню,
Custom Translations,Пользовательские переводы,
Customization,Пользовательские настройки,
Customizations Reset,Сброс настроек,
Customizations for <b>{0}</b> exported to:<br>{1},Настройки для <b>{0}</b> экспортированы в: <br> {1},
Customize Form,Настроить форму,
Customize Form Field,Настроить поля формы,
"Customize Label, Print Hide, Default etc.","Пользовательские метки, скрыть печать, по умолчанию и т.д.",
Customize...,Пользовательские настройки...,
"Customized Formats for Printing, Email","Индивидуальные форматы для печати, электронной почты",
Customized HTML Templates for printing transactions.,Индивидуальные шаблоны HTML для печатных операций.,
Cut,Вырезать,
DESC,DESC,
Daily Event Digest is sent for Calendar Events where reminders are set.,"Ежедневный дайджест событий направляется в календарь событий, где установлены напоминания.",
Danger,Опасность,
Dark Color,Темный цвет,
Dashboard Chart,Диаграмма приборной панели,
Dashboard Chart Link,Ссылка на панель инструментов,
Dashboard Chart Source,Источник диаграммы приборной панели,
Dashboard Name,Название панели,
Dashboards,Сводки,
Data,Данные,
Data Export,Экспорт данных,
Data Import,Импорт данных,
Data Import Template,Шаблон импорт данных,
Data Migration,Перенос данных,
Data Migration Connector,Разделитель данных,
Data Migration Mapping,Миграция данных,
Data Migration Mapping Detail,Подробное описание миграции данных,
Data Migration Plan,План миграции данных,
Data Migration Plan Mapping,Составление карт переноса данных,
Data Migration Run,Запуск миграции данных,
Data missing in table,Данные отсутствует в таблице,
Database Engine,Движок базы данных,
Database Name,Имя базы данных,
Date and Number Format,Настройки времени и валюты,
Date {0} must be in format: {1},Дата {0} должна быть в формате: {1},
Dates are often easy to guess.,Даты часто легко угадать.,
Day of Week,День недели,
Days After,Дней после,
Days Before,Дней до,
Days Before or After,Дней до или после,
"Dear System Manager,","Уважаемый менеджер системы,",
"Dear User,","Дорогой пользователь,",
Dear {0},Уважаемый {0},
Default Address Template cannot be deleted,Адрес по умолчанию шаблона не может быть удален,
Default Inbox,По умолчанию входящие,
Default Incoming,По умолчанию для входящих,
Default Outgoing,По умолчанию для исходящих,
Default Print Format,Печатная форма по умолчанию,
Default Print Language,Язык печати по умолчанию,
Default Redirect URI,По умолчанию перенаправление URI,
Default Role at Time of Signup,Роль по умолчанию во время регистрации,
Default Sending,По умолчанию для отправки,
Default Sending and Inbox,По умолчанию отправка и получение,
Default Sort Field,Поле сортировки по умолчанию,
Default Sort Order,Порядок сортировки по умолчанию,
Default Value,Значение по умолчанию,
"Default: ""Contact Us""","По умолчанию: ""Contact Us""",
DefaultValue,DefaultValue,
Define workflows for forms.,Определите рабочие процессы для форм.,
Defines actions on states and the next step and allowed roles.,"Определяет действия на статусах, следующий шаг и роли, обладающие правами перевода статусов.",
Defines workflow states and rules for a document.,Определяет статусы рабочего процесса и правила их перехода для документа.,
Delayed,Задерживается,
Delete Data,Удалить данные,
Delete comment?,Удалить комментарий?,
Delete this record to allow sending to this email address,"Удалить эту запись, чтобы разрешить отправку на этот адрес электронной почты",
Delete {0} items permanently?,Удалить {0} объектов навсегда?,
Deleted,Удаленный,
Deleted DocType,Удаленный DocType,
Deleted Document,Удаленный документ,
Deleted Documents,Удаленные документы,
Deleted Name,Удаляется имя,
Deleting {0},Удаление {0},
Depends On,Зависит от,
Descendants Of,Потомки,
Desk,Рабочий стол,
Desk Access,Доступ к рабочему столу,
Desktop Icon,Иконка рабочего стола,
Desktop Icon already exists,Значок рабочего стола уже существует,
Developer,Разработчик,
Did not add,Не добавить,
Did not cancel,Не отменить,
Did not find {0} for {0} ({1}),Не нашли {0} {0} ({1}),
Did not remove,Не удален,
"Different ""States"" this document can exist in. Like ""Open"", ""Pending Approval"" etc.","Документ может содержать различные ""Статусы"". Например, ""Создан"", ""Ожидает утверждения"" и т.д.",
Direct,Непосредственный,
Direct room with {0} already exists.,Прямая комната с {0} уже существует.,
Disable Auto Refresh,Отключить автоматическое обновление,
Disable Count,Отключить счет,
Disable Customer Signup link in Login page,Отключение клиента Регистрация ссылку в странице входа,
Disable Prepared Report,Отключить подготовленный отчет,
Disable Report,Отключить отчет,
Disable SMTP server authentication,Отключить аутентификацию SMTP-сервера,
Disable Sidebar Stats,Отключить статистику боковой панели,
Disable Signup,Отключение Регистрация,
Disable Standard Email Footer,Отключить стандартный нижний колонтитул электронной почты,
Discard,Отменить,
Display,Показать,
Display Depends On,Показание зависит от,
Do not allow user to change after set the first time,Не позволяйте пользователю изменять после установить в первый раз,
Do not edit headers which are preset in the template,"Не редактируйте заголовки, которые заданы в шаблоне",
Do not send Emails,Не отправлять письма,
Doc Event,Событие документа,
Doc Events,События документа,
Doc Status,Статус документа,
DocField,DocField,
DocPerm,DocPerm,
DocShare,DocShare,
DocType <b>{0}</b> provided for the field <b>{1}</b> must have atleast one Link field,"DocType <b>{0},</b> предоставленный для поля <b>{1},</b> должен иметь как минимум одно поле Link",
DocType can not be merged,DocType не могут быть объединены,
DocType can only be renamed by Administrator,DocType могут быть переименованы только администратор,
DocType is a Table / Form in the application.,DocType является стола / форма в приложении.,
DocType must be Submittable for the selected Doc Event,DocType должен быть подчинен для выбранного события Doc,
DocType on which this Workflow is applicable.,"DocType, на котором этот поток применяется.",
"DocType's name should start with a letter and it can only consist of letters, numbers, spaces and underscores","Имя DOCTYPE должно начинаться с буквы и может состоять только из букв, цифр, пробелов и знаков подчеркивания",
Doctype required,Требуется Doctype,
Document,Документ,
Document Follow,Следить за документом,
Document Follow Notification,Документ следовать уведомлению,
Document Queued,Документ в очереди,
Document Restored,Восстановленный документ,
Document Share Report,Документ Поделиться Пожаловаться,
Document States,Состояния документа,
Document Type is not importable,Тип документа не импортируется,
Document Type is not submittable,Тип документа не подлежит отправке,
Document Type to Track,Тип документа для отслеживания,
Document Types,Типы документов,
Document can't saved.,Документ не может быть сохранен.,
Document {0} has been set to state {1} by {2},Документ {0} установлен в состояние {1} на {2},
Documents,Документы,
Documents assigned to you and by you.,"Документы, назначенные вам и вами.",
Domain Settings,Настройка сфер деятельности,
Domains HTML,Домены HTML,
"Don't HTML Encode HTML tags like &lt;script&gt; or just characters like &lt; or &gt;, as they could be intentionally used in this field","Не HTML Кодировать HTML-теги, такие как &lt;скрипт&gt; или просто символы, такие как &lt;или&gt;, так как они могут быть преднамеренно использованы в этой области",
Don't Override Status,Не переопределять статус,
Don't create new records,Не создавать новые записи,
Don't have an account? Sign up,У вас нет аккаунта? Зарегистрироваться,
"Don't know, ask 'help'","Не знаю, попросить 'помощь'",
Download Data,Скачать данные,
Download Files Backup,Скачать файлы резервной копии,
Download Link,Ссылка для скачивания,
Download Report,Скачать отчет,
Download Your Data,Скачать ваши данные,
Download link for your backup will be emailed on the following email address: {0},Ссылка для скачивания вашей резервной копии будет отправлена на следующий электронный адрес: {0},
Download with Data,Скачать с данными,
Drag and Drop tool to build and customize Print Formats.,Инструмент для сборки и настройки бланков для печати.,
Drag elements from the sidebar to add. Drag them back to trash.,"Перетащите элементы с боковой панели, чтобы добавить. Перетащите их обратно чтобы удалить.",
Dropbox Access Key,Dropbox ключ доступа,
Dropbox Access Secret,Dropbox секретный ключ,
Dropbox Access Token,Токен доступа к Dropbox,
Dropbox Settings,Настройки Dropbox,
Dropbox Setup,Настройка Dropbox,
Dropbox access is approved!,Доступ к Dropbox получен!,
Dropbox backup settings,Настройки резервного копирования Dropbox,
Duplicate Filter Name,Дублируемое имя фильтра,
Dynamic Link,Динамическая ссылка,
Dynamic Report Filters,Фильтры динамических отчетов,
ESC,ESC,
Edit Auto Email Report Settings,Изменить настройки отчета по электронной почте,
Edit Custom HTML,Редактировать пользовательский HTML,
Edit DocType,Редактирование DocType,
Edit Filter,Редактировать фильтр,
Edit Format,Редактировать формат,
Edit HTML,Редактировать HTML,
Edit Heading,Редактировать заголовок,
Edit Properties,Редактировать свойства,
Edit to add content,"Редактировать, чтобы добавить содержание",
Edit {0},Редактировать {0},
Editable Grid,Редактируемые сетки,
Editing Row,Редактирование строк,
Eg. smsgateway.com/api/send_sms.cgi,Например smsgateway.com/api/send_sms.cgi,
Email Account Name,Имя учетной записи электронной почты,
Email Account added multiple times,Аккаунт электронной почты добавлен несколько раз,
Email Addresses,Адрес электронной почты,
Email Domain,Домен электронной почты,
"Email Domain not configured for this account, Create one?","Домен электронной почты не настроен для этой учетной записи, создать?",
Email Flag Queue,Очередь флагов электронной почты,
Email Footer Address,Адрес в нижнем колонтитуле,
Email Group,Группа электронной почты,
Email Group List,Список групп электронной почты,
Email Group Member,Электронная почта участника группы,
Email Login ID,Идентификатор входа в Email,
Email Queue,Очередь электронной почты,
Email Queue Recipient,E-mail в очереди получателя,
Email Queue records.,Записи очереди электронной почты,
Email Reply Help,Ответить на Email Помощь,
Email Rule,Правило электронной почты,
Email Server,Сервер электронной почты,
Email Settings,Настройки электронной почты,
Email Signature,Подпись электронной почты,
Email Status,Статус электронной почты,
Email Sync Option,Опция синхронизации электронной почты,
Email Templates for common queries.,Шаблоны электронной почты для общих запросов.,
Email To,Электронная почта для,
Email Unsubscribe,E-mail Отписаться,
Email has been marked as spam,Электронная почта отмечена как спам,
Email has been moved to trash,Электронная почта была перемещена в корзину,
Email not sent to {0} (unsubscribed / disabled),Письмо не отправлено {0} (отписан / отключено),
Email not verified with {0},Электронная почта не подтверждена {0},
Emails are muted,Письма отключены,
Emails will be sent with next possible workflow actions,Будут отправляться письма с возможными следующими действиями бизнес-процесса,
Embed image slideshows in website pages.,Код для вставки слайд-шоу изображений в веб-страницах.,
Enable / Disable Domains,Включить / отключить домены,
Enable Auto Reply,Включить автоматический ответ,
Enable Automatic Backup,Включить автоматическое резервное копирование,
Enable Chat,Включить чат,
Enable Comments,Включить комментарии,
Enable Incoming,Включить входящие,
Enable Outgoing,Включить исходящие,
Enable Password Policy,Включить политику паролей,
Enable Print Server,Включить сервер печати,
Enable Raw Printing,Включить необработанную печать,
Enable Report,Включить отчет,
Enable Scheduled Jobs,Включить запланированных заданий,
Enable Social Login,Включить социальный вход,
Enable Two Factor Auth,Включить двухфакторный аут,
Enabled email inbox for user {0},Включен почтовый ящик для пользователя {0},
"Encryption key is invalid, Please check site_config.json","Ключ шифрования недействителен, проверьте site_config.json",
End Date Field,Поле конечной даты,
End Date cannot be before Start Date!,Дата окончания не может быть до даты начала!,
Endpoint URL,URL конечной точки,
Energy Point Log,Журнал баллов активности,
Energy Point Rule,Правило баллов активности,
Energy Point Settings,Настройки баллов активности,
Energy Points,Баллы активности,
Enter Email Recipient(s),Введите E-mail получателя (ей),
Enter Form Type,Введите тип формы,
"Enter default value fields (keys) and values. If you add multiple values for a field, the first one will be picked. These defaults are also used to set ""match"" permission rules. To see list of fields, go to ""Customize Form"".","Введите значение по умолчанию поля (Keys) и значения. При добавлении нескольких значений для поля, первая будет определена. Эти значения по умолчанию также используется для установки &quot;Матч&quot; разрешающие правила. Чтобы увидеть список полей, перейдите в раздел &quot;Настройка формы&quot;.",
Enter folder name,Введите имя папки,
"Enter keys to enable login via Facebook, Google, GitHub.","Введите ключи для того, чтобы войти с помощью Facebook, Google, GitHub.",
Enter python module or select connector type,Введите модуль python или выберите тип разъема,
"Enter static url parameters here (Eg. sender=ERPNext, username=ERPNext, password=1234 etc.)","Введите статические параметры адрес здесь (Например отправитель = ERPNext, имя пользователя = ERPNext, пароль = 1234 и т.д.)",
Enter url parameter for message,Введите URL-параметр для сообщения,
Enter url parameter for receiver nos,Введите URL-параметр для получателя сообщения,
Enter your password,Введите ваш пароль,
Entity Name,Имя сущности,
Equals,Равно,
Error Message,Сообщение об ошибке,
Error Report,Сообщение об ошибке,
Error Snapshot,Снимок ошибки,
Error in Custom Script,Ошибка в пользовательском скрипте,
Error in Notification,Ошибка в уведомлении,
Error in Notification: {},Ошибка в уведомлении: {},
Error while connecting to email account {0},Ошибка при подключении к учетной записи электронной почты {0},
Error while evaluating Notification {0}. Please fix your template.,Ошибка при оценке уведомления {0}. Исправьте шаблон.,
Error: Document has been modified after you have opened it,"Ошибка: документ был изменен после того, как вы открыли его",
Error: Value missing for {0}: {1},Ошибка: отсутствует значение для {0}: {1},
Errors in Background Events,Ошибки в фоновых событиях,
Event Category,Категория события,
Event Participants,Участники мероприятия,
Event Type,Тип события,
Event and other calendars.,Событие и другие календари.,
Events in Today's Calendar,События в сегодняшнем календаре,
Everyone,Все,
Example,Пример,
Example Email Address,Пример электронного адреса,
Example: {{ subject }},Пример: {{ subject }},
Excel,превосходить,
Exception,Исключение,
Exception Type,Тип исключения,
Execution Time: {0} sec,Время выполнения: {0} сек,
Expert,Эксперт,
Expiration time,Время истечения,
Expire Notification On,Expire Уведомление о,
Expires In,Истекает,
Expiry time of QR Code Image Page,Время истечения срока действия страницы изображения QR-кода,
Export All {0} rows?,Экспортировать все строки {0}?,
Export Custom Permissions,Разрешения Экспорт пользовательских,
Export Customizations,Экспорт адаптаций,
Export Data,Экспорт данных,
Export Data in CSV / Excel format.,Экспорт данных в формате CSV / Excel.,
Export Report: {0},Экспорт отчета: {0},
Expose Recipients,Expose Получатели,
"Expression, Optional","Выражение, необязательно",
Facebook,Facebook,
Failed to complete setup,Не удалось завершить настройку,
Failed to connect to server,Не удалось подключиться к серверу,
Failed while amending subscription,Ошибка при внесении изменений в подписку,
FavIcon,FavIcon,
Feedback Request,Запрос обратной связи,
Fetch From,Извлечь из,
Fetch If Empty,"Получить, если пусто",
Fetch Images,Получение изображений,
Fetch attached images from document,Получение прикрепленных изображений из документа,
"Field ""route"" is mandatory for Web Views",Поле «маршрут» обязательно для веб-представлений,
"Field ""value"" is mandatory. Please specify value to be updated","Поле ""Значение"" является обязательным. Пожалуйста, укажите значение, чтобы обновить",
Field Description,Описание поля,
Field Maps,Карты полей,
Field Type,Тип поля,
"Field that represents the Workflow State of the transaction (if field is not present, a new hidden Custom Field will be created)","Поле, показывающее статус бизнес-процесса (если поле отсутствует, будет создано новое скрытое настраиваемое поле)",
Field to Track,Поле для отслеживания,
Field type cannot be changed for {0},Тип поля не может быть изменен для {0},
Field {0} not found.,Поле {0} не найдено.,
Fieldname is limited to 64 characters ({0}),Имя поля ограничено 64 символами ({0}),
Fieldname not set for Custom Field,Имя поля не определено для настраиваемого поля,
Fieldname which will be the DocType for this link field.,"Имя поля, которое будет Типом документа для этого поля связи.",
Fieldname {0} cannot have special characters like {1},"Имя поля {0} не может иметь специальные символы, такие как {1}",
Fieldname {0} conflicting with meta object,"Имя поля {0}, конфликтующее с метаобъектом",
Fields Multicheck,Поле Multicheck,
"Fields separated by comma (,) will be included in the ""Search By"" list of Search dialog box","Поля, разделенные запятыми (,) будут включены в список ""Поиск по"" диалогового окна Поиск",
Fieldtype,Тип поля,
Fieldtype cannot be changed from {0} to {1} in row {2},Тип поля не может быть изменен с {0} на {1} в строке {2},
File '{0}' not found,Файл &#39;{0}&#39; не найден,
File Backup,Резервная копия файла,
File Name,Имя файла,
File Size,Размер файла,
File Type,Тип файла,
File URL,URL файла,
File Upload,Файл загружен,
File Upload Disconnected. Please try again.,"Ошибка при загрузке. Пожалуйста, попробуйте еще раз.",
File Upload in Progress. Please try again in a few moments.,"Файл загружается, повторите позже.",
File backup is ready,Файл резервной копии готов,
File not attached,Файл не прикреплен,
File size exceeded the maximum allowed size of {0} MB,Размер файла превысил максимально допустимый размер {0} МБ,
File too big,Файл слишком большой,
File {0} does not exist,Файл {0} не существует,
Files,Файлы,
Filter,Фильтр,
Filter Data,Фильтрация данных,
Filter List,Список фильтров,
Filter Meta,Фильтр Meta,
Filter Name,Имя фильтра,
Filter Values,Значение фильтра,
Filter must be a tuple or list (in a list),Фильтр должен быть кортежем или списком (в списке),
"Filter must have 4 values (doctype, fieldname, operator, value): {0}","Фильтр должен иметь 4 значения (doctype, имя поля, оператор, значение): {0}",
Filter...,Фильтр...,
"Filtered by ""{0}""","Отфильтровано по ""{0}""",
Filters Display,Показать фильтры,
Filters JSON,Фильтры JSON,
Filters saved,Фильтры сохранены,
Find {0} in {1},Найти {0} в {1},
First Level,Первый уровень,
First Success Message,Первое сообщение о успехе,
First Transaction,Первая сделка,
First data column must be blank.,Первая колонка данных должна быть пустой.,
First set the name and save the record.,Сначала задайте имя и сохраните запись.,
Flag,Флаг,
Float,Дробное,
Float Precision,Плавающая точность,
Fold,Сворачиваемое,
Fold can not be at the end of the form,Сворачиваемое поле не может быть в конце формы,
Fold must come before a Section Break,Сворачиваемое должно идти до разрыва раздел,
Folder,Папка,
Folder name should not include '/' (slash),Имя папки не должно включать «/» (косая черта),
Folder {0} is not empty,Папка {0} не пуста,
Follow,Следить,
Followed by,С последующим,
Following fields are missing:,Эти поля отсутствуют:,
Following fields have missing values:,Эти поля имеют пропущенные значения:,
Font,Шрифт,
Font Size,Размер шрифта,
Fonts,Шрифты,
Footer,Нижний колонтитул,
Footer HTML,Нижний колонтитул HTML,
Footer Items,Элементы нижнего колонтитула,
Footer will display correctly only in PDF,Нижний колонтитул будет отображаться правильно только в PDF,
For Document Type,Для типа документа,
"For Links, enter the DocType as range.\nFor Select, enter list of Options, each on a new line.","Для ссылки, введите DOCTYPE как диапазона.\nДля выбора, введите список вариантов, каждый с новой строки.",
For User,Для пользователей,
For Value,Для значений,
"For currency {0}, the minimum transaction amount should be {1}",Для валюты {0} минимальная сумма транзакции должна быть {1},
For example if you cancel and amend INV004 it will become a new document INV004-1. This helps you to keep track of each amendment.,"Например, если вы отмените и измените INV004, он станет новым документом INV004-1. Это поможет вам отследить каждую правку.",
"For example: If you want to include the document ID, use {0}","Например: Если вы хотите добавить идентификатор документа, используйте {0}",
"For updating, you can update only selective columns.","Для обновления, вы можете обновить только выборочные столбцы.",
For {0} at level {1} in {2} in row {3},Для {0} на уровне {1} в {2} в строке {3},
Force,Принудительно,
Force Show,Принудительно показывать,
Forgot Password,Забыли пароль,
Forgot Password?,Забыли пароль?,
Form Customization,Настройка формы,
Form Settings,Настройки формы,
Format,Формат,
Format Data,Формат данных,
Forward To Email Address,Переслать на адрес электронной почты,
Fraction,Дробные единицы,
Fraction Units,Количество единиц,
Frames,Рамки,
Frappe,Frappe,
Frappe Framework,Frappe Framework,
Friendly Title,Упрощённое наименование,
From Date Field,Из поля даты,
From Document Type,Из типа документа,
From Full Name,От полного имени,
Full Page,Полная страница,
Fw: {0},Fw: {0},
GCalendar Sync ID,Идентификатор синхронизации GCalendar,
GMail,GMail,
Gantt,Ганта,
Gateway,Шлюз,
Gateway Controller,Контроллер шлюза,
Gateway Settings,Настройки шлюза,
Generate Keys,Создать ключи,
Generate New Report,Создать новый отчет,
Generated File,Сгенерированный файл,
Geo,Гео,
Geolocation,Геолокация,
Get Alerts for Today,Получить оповещения на сегодня,
Get Contacts,Получить контакты,
Get Fields,Получить поля,
Get your globally recognized avatar from Gravatar.com,Получить всемирно признанный аватара из Gravatar.com,
GitHub,GitHub,
Give Review Points,Дайте баллы обзора,
Global Unsubscribe,Глобальная отписка,
Go to the document,Перейти к документу,
Go to this URL after completing the form (only for Guest users),Перейдите по этому URL-адресу после заполнения формы (только для гостевых пользователей),
Go to {0},Перейти к {0},
Go to {0} List,Перейти к списку {0},
Go to {0} Page,Перейти на страницу {0},
Google,Google,
Google Analytics ID,Google Analytics ID,
Google Calendar ID,Идентификатор Google Calendar,
Google Font,Google Font,
Google Services,Службы Google,
Grant Type,Тип гранта,
Group Name,Название группы,
Group name cannot be empty.,Имя группы не может быть пустым.,
Groups of DocTypes,Группы DocTypes,
HTML,HTML,
HTML Editor,Редактор HTML,
"HTML Header, Robots and Redirects","Заголовок HTML, роботы и перенаправления",
HTML for header section. Optional,HTML для секции header. Необязателен,
Half,Половина,
Has  Attachment,Содержит вложения,
Has Attachments,Содержит приложения,
Has Domain,Имеет домен,
Has Role,Имеет роль,
Has Web View,Имеет Web View,
Have an account? Login,Уже есть аккаунт? Авторизоваться,
Header,Заголовок,
Header HTML,Заголовок HTML,
Header HTML set from attachment {0},HTML-код заголовка из вложения {0},
Header Image,Заглавное изображение,
Headers,Заголовки,
Heading,Заголовок,
Hello {0},"Здравствуйте, {0}",
Hello!,Здравствуйте!,
Help Articles,Статьи помощи,
Help Category,Категория помощи,
Help on Search,Помощь в поиске,
"Help: To link to another record in the system, use ""#Form/Note/[Note Name]"" as the Link URL. (don't use ""http://"")","Помощь: Чтобы связать с другой записью в системе, используйте «# Form/Note/[Название статьи]», в виде ссылки URL. (Не используйте «http://»)",
Helvetica,Helvetica,
Hi {0},Привет {0},
Hide Copy,Скрыть копирование,
Hide Footer Signup,Скрыть колонтитул регистрации,
Hide Sidebar and Menu,Скрыть боковую панель и меню,
Hide Standard Menu,Скрыть стандартное меню,
Hide Weekends,Скрыть выходные,
Hide details,Скрыть подробности,
Hide footer in auto email reports,Скрыть нижний колонтитул в автоответах электронной почты,
Higher priority rule will be applied first,Правило с более высоким приоритетом будет применено первым,
Highlight,Выделить,
"Hint: Include symbols, numbers and capital letters in the password","Подсказка: укажите в пароле символы, цифры и прописные буквы",
Home Page,Домашняя страница,
Home Settings,Домашние настройки,
Home/Test Folder 1,Главная/Тестовая Папка 1,
Home/Test Folder 1/Test Folder 3,Главная/Тестовая Папка 1/Тестовая Папка 3,
Home/Test Folder 2,Главная/Тестовая Папка 2,
"How should this currency be formatted? If not set, will use system defaults","Как следует отображать числа в этой валюте? Если не указано, то будут использоваться системные значения",
I found these: ,Я нашел это: ,
ID,ID,
ID (name) of the entity whose property is to be set,"ID (имя) лица, имущество которого должно быть установлено",
Icon will appear on the button,Иконка появится на кнопке,
Identity Details,Сведения о личности,
"If Apply Strict User Permission is checked and User Permission is defined for a DocType for a User, then all the documents where value of the link is blank, will not be shown to that User","Если флажок Apply Strict User Permission установлен, а для пользователя DocType для пользователя задано разрешение пользователя, тогда все документы, где значение ссылки пустым, не будут показаны этому пользователю",
If Checked workflow status will not override status in list view,"Если установлен флажок, статус процесса не будет отменять статус в журнале",
If Owner,Если владелец,
"If a Role does not have access at Level 0, then higher levels are meaningless.","Если роль не имеет доступа на уровне 0, то более высокие уровни не имеют смысла.",
"If checked, all other workflows become inactive.","Если этот флажок установлен, то все остальные рабочие процессы становятся неактивными.",
"If checked, this field will be not overwritten based on Fetch From if a value already exists.","Если флажок установлен, это поле не будет перезаписано на основе Извлечено из, если значение уже существует.",
"If checked, users will not see the Confirm Access dialog.","Если этот флажок установлен, пользователи не будут видеть диалоговое окно подтверждения доступа.",
"If disabled, this role will be removed from all users.","Если эта функция отключена, эта роль будет удалена у всех пользователей.",
"If enabled,  user can login from any IP Address using Two Factor Auth, this can also be set for all users in System Settings","Если включено, пользователь может войти в систему с любого IP-адреса, используя двухфакторную аутентификацию, это также можно установить для всех пользователей в настройках системы.",
"If enabled, all users can login from any IP Address using Two Factor Auth. This can also be set only for specific user(s) in User Page","Если включено, все пользователи могут войти в систему с любого IP-адреса, используя двухфакторную аутентификацию. Это также можно установить только для определенных пользователей на странице пользователя",
"If enabled, changes to the document are tracked and shown in timeline","Если включено, изменения в документе отслеживаются и отображаются на временной шкале.",
"If enabled, document views are tracked, this can happen multiple times","Если включено, просмотры документов отслеживаются, это может происходить несколько раз",
"If enabled, the document is marked as seen, the first time a user opens it","Если этот параметр включен, документ помечается как видимый, когда пользователь впервые открывает его",
"If enabled, the password strength will be enforced based on the Minimum Password Score value. A value of 2 being medium strong and 4 being very strong.","Если включено, сложность пароля будет определяться в зависимости от значения минимальной оценки пароля. Значение 2 является средней сложностью, а 4 - очень сложным.",
"If enabled, users who login from Restricted IP Address, won't be prompted for Two Factor Auth","Если включено, пользователи, которые подключаются с установленного IP-адреса, не будут проходить двухфакторную аутентификацию",
"If enabled, users will be notified every time they login. If not enabled, users will only be notified once.","Если включено, пользователи будут получать уведомления каждый раз при входе в систему. Если этот параметр выключен, пользователи будут получать уведомления только один раз.",
If non standard port (e.g. 587),"Если, не стандартный порт (например, 587)",
"If non standard port (e.g. 587). If on Google Cloud, try port 2525.","Если нестандартный порт (например, 587). Если в Google Cloud, попробуйте порт 2525.",
"If not set, the currency precision will depend on number format","Если не установлено, точность валюты будет зависеть от формата числа",
If the condition is satisfied user will be rewarded with the points. eg. doc.status == 'Closed'\n,"Если условие выполнено, пользователь будет вознагражден баллами. например. doc.status == &#39;Закрыто&#39;",
"If the user has any role checked, then the user becomes a ""System User"". ""System User"" has access to the desktop","Если пользователь имеет какую-либо роль, то он становится «Пользователем системы». Пользователи системы имеет доступ к рабочему столу",
"If these instructions where not helpful, please add in your suggestions on GitHub Issues.","Если эти инструкции бесполезны, пожалуйста, добавьте предложения в GitHub.",
"If this is checked, rows with valid data will be imported and invalid rows will be dumped into a new file for you to import later.","Если этот флажок установлен, строки с достоверными данными будут импортированы, а недопустимые строки будут сбрасываться в новый файл для последующего импорта.",
If user is the owner,Если пользователь является владельцем,
"If you are updating, please select ""Overwrite"" else existing rows will not be deleted.","Если вы обновляете, пожалуйста, выберите ""Заменить"" еще существующие строки не будут удалены.",
If you are updating/overwriting already created records.,Если вы обновляете или перезаписываете уже созданные записи.,
"If you are uploading new records, ""Naming Series"" becomes mandatory, if present.","Если вы загружаете новые записи, «Идентификация по имени» становится обязательной, если она присутствует.",
"If you are uploading new records, leave the ""name"" (ID) column blank.","Если вы загружаете новые рекорды, оставьте колонку ""Наименование"" (ID) пустым.",
If you don't want to create any new records while updating the older records.,Если вы не хотите создавать новые записи при обновлении старых записей.,
"If you set this, this Item will come in a drop-down under the selected parent.","Если вы установите это, этот предмет придет в раскрывающемся списке под выбранной родителя.",
"If you think this is unauthorized, please change the Administrator password.","Если вы думаете, что это несанкционированное, пожалуйста, измените пароль администратора.",
"If your data is in HTML, please copy paste the exact HTML code with the tags.","Если данные в HTML, скопируйте вставьте точный HTML код с тегами.",
Ignore User Permissions,Игнорировать разрешения пользователя,
Ignore XSS Filter,Игнорировать XSS-фильтр,
Ignore attachments over this size,Игнорировать вложения больше указаного размера,
Ignore encoding errors,Игнорировать ошибки кодирования,
Ignored: {0} to {1},Игнорируется: {0} до {1},
Illegal Access Token. Please try again,"Неправильный токен доступа. Пожалуйста, попробуйте еще раз",
Illegal Document Status for {0},Недопустимый статус документа для {0},
Image Field,Поле изображения,
Image Link,Ссылка на изображение,
Image field must be a valid fieldname,Поле изображения должно быть допустимым имя_поля,
Image field must be of type Attach Image,Поле изображения должно быть типа Прикрепить изображение,
Images,Изображения,
Implicit,Неявный,
Import,Импорт,
Import Email From,Импортировать электронную почту из,
Import Status,Статус импорта,
Import Subscribers,Импорт подписчиков,
Import Zip,Импорт Zip,
In Filter,В фильтрe,
In Global Search,В глобальном поиске,
In Grid View,В табличном виде,
In Hours,В час,
In List View,Отображать в списке,
In Preview,В предварительном просмотре,
In Reply To,В ответ на,
In Standard Filter,В стандартный фильтр,
In Valid Request,В действующем запросе,
In points. Default is 9.,В баллах. По умолчанию 9.,
In seconds,В секундах,
Include Search in Top Bar,Включить поиск в верхней панели,
"Include symbols, numbers and capital letters in the password","Включить символы, цифры и заглавные буквы в пароле",
Incoming email account not correct,Аккаунт входящей электронной почты неверен,
Incomplete login details,Неполные данные для входа,
Incorrect User or Password,Неверный пользователь или пароль,
Incorrect Verification code,Неверный код подтверждения,
Incorrect value in row {0}: {1} must be {2} {3},Неверное значение в строке {0}: {1} должно быть {2} {3},
Incorrect value: {0} must be {1} {2},Неверное значение: {0} должно быть {1} {2},
Index,Индекс,
Indicator,Индикатор,
Info,Информация,
Info:,Информация:,
Initial Sync Count,Первоначальная синхронизация,
InnoDB,InnoDB,
Insert Above,Вставить сверху,
Insert After,Вставить после,
Insert After cannot be set as {0},Вставка после не может быть установлен как {0},
"Insert After field '{0}' mentioned in Custom Field '{1}', with label '{2}', does not exist","После того, как вставить поле &#39;{0}&#39;, упомянутой в настраиваемое поле &#39;{1}&#39;, с меткой &#39;{2}&#39;, не существует",
Insert Below,Вставить ниже,
Insert Column Before {0},Вставить столбец до {0},
Insert Style,Вставьте стиль,
Insert new records,Вставить новые записи,
Instructions Emailed,Инструкции отправлены по электронной почте,
Insufficient Permission for {0},Недостаточно прав для {0},
Int,Интервал,
Integration Request,Интеграция заявки,
Integration Request Service,Интеграция заявки на обслуживание,
Integration Type,Тип интеграции,
Integrations,Интеграция,
Integrations can use this field to set email delivery status,Интеграция может использовать это поле для установки статуса доставки электронной почты,
Internal Server Error,Внутренняя ошибка сервера,
Internal record of document shares,Внутренняя запись акций документов,
Introduce your company to the website visitor.,Представьте вашу компанию на посетителя сайта.,
Introductory information for the Contact Us Page,Вводная информация для страницы контактов,
Invalid,Неверно,
"Invalid ""depends_on"" expression","Недопустимое выражение ""depends_on""",
Invalid Access Key ID or Secret Access Key.,Недействительный ключ ключа доступа или секретный ключ доступа.,
Invalid CSV Format,Неверный формат CSV,
Invalid Home Page,Неверная главная страница,
Invalid Link,Неверная ссылка,
Invalid Login Token,Неверный логин токен,
Invalid Login. Try again.,Неверный логин. Попробуй еще раз.,
Invalid Mail Server. Please rectify and try again.,"Неверный почтовый сервер. Пожалуйста, исправьте и попробуйте еще раз.",
Invalid Outgoing Mail Server or Port,Неверный сервер исходящей почты или порт,
Invalid Output Format,Неверный формат выходного,
Invalid Password,Неверный пароль,
Invalid Password:,Неверный пароль:,
Invalid Request,Неверный запрос,
Invalid Search Field {0},Неверное поле поиска {0},
Invalid Subscription,Недействительная подписка,
Invalid Token,Недопустимый токен,
Invalid User Name or Support Password. Please rectify and try again.,"Неверное имя пользователя или поддержки Пароль. Пожалуйста, исправить и попробовать еще раз.",
Invalid column,Недопустимый столбец,
Invalid field name {0},Недопустимое имя поля {0},
Invalid fieldname '{0}' in autoname,Недопустимое имя_поля &#39;{0}&#39; в autoname,
Invalid file path: {0},Неверный путь к файлу: {0},
Invalid login or password,Неверный логин или пароль,
Invalid module path,Недопустимый путь к модулю,
Invalid naming series (. missing),Неверный идентификатор по имени (отсутствует),
Invalid payment gateway credentials,Неверные учетные данные платежного шлюза,
Invalid recipient address,Неверный адрес получателя,
Invalid {0} condition,Недопустимое условие {0},
Inverse,Обратный,
Is,Является,
Is Attachments Folder,Это папка для вложений,
Is Child Table,Это дочерняя таблицей,
Is Custom Field,Это нестандартное поле,
Is First Startup,Первый запуск,
Is Folder,Это папка,
Is Global,Является глобальным,
Is Globally Pinned,Глобально закреплено,
Is Home Folder,Является корневой папкой,
Is Mandatory Field,Является обязательным полем,
Is Pinned,Прикреплено,
Is Primary Contact,Это основной контакт,
Is Private,Является приватным,
Is Published Field,Это опубликованое поле,
Is Published Field must be a valid fieldname,Опубликованое роле должно быть допустимым именем поля,
Is Single,Единственный,
Is Spam,Это спам,
Is Standard,Это стандартный отчёт,
Is Submittable,Подлежит исполнению,
Is Table,Является таблицей,
Is Your Company Address,Является адресом вашей компании,
It is risky to delete this file: {0}. Please contact your System Manager.,"Рискованно удалять этот файл: {0}. Пожалуйста, обратитесь к менеджеру системы.",
Item cannot be added to its own descendants,Продукт не может быть добавлен к своим подпродуктам,
JavaScript Format: frappe.query_reports['REPORTNAME'] = {},Формат JavaScript: frappe.query_reports ['REPORTNAME'] = {},
Javascript to append to the head section of the page.,Javascript для добавления к головной части страницы.,
John Doe,Джон Доу,
Kanban,Канбан,
Kanban Board Column,Колонка канбан-доски,
Kanban Board Name,Наименование канбан-доски,
Karma,Карма,
Keep track of all update feeds,Следите за всеми фидами обновлений,
Keeps track of all communications,Отслеживать все коммуникации,
Key,Ключ,
Knowledge Base,База знаний,
Knowledge Base Contributor,Пользователь базы знаний,
Knowledge Base Editor,Редактор базы знаний,
LDAP Email Field,LDAP Email Поле,
LDAP First Name Field,LDAP поле Имя,
LDAP Not Installed,LDAP не установлен,
LDAP Search String,LDAP Строка поиска,
"LDAP Search String needs to end with a placeholder, eg sAMAccountName={0}","Строка поиска LDAP должна заканчиваться заполнителем, например, sAMAccountName = {0}",
LDAP Security,LDAP Security,
LDAP Server Url,URL cервера LDAP,
LDAP Username Field,LDAP Имя пользователя Поле,
LDAP is not enabled.,LDAP не включен.,
Label Help,Метка Помощь,
Label and Type,Метка и Тип,
Label is mandatory,Метка является обязательной,
Landing Page,Страница входа,
Language,Язык,
Language Code,Языковой код,
"Language, Date and Time settings","Настройки языка, даты и времени",
Last Active,Последнее посещение,
Last IP,Последний IP,
Last Known Versions,Последние известные версии,
Last Login,Последний вход,
Last Message,Последнее сообщение,
Last Modified By,Последнее изменение,
Last Modified Date,Дата последней модификации,
Last Modified On,Дата последнего обновления,
Last Month,Прошлый месяц,
Last Point Allocation Date,Дата последнего начисления баллов,
Last Quarter,Последняя четверть,
Last Synced On,Последняя синхронизация включена,
Last Updated By,Последнее обновление,
Last Updated On,Обновлено,
Last User,Последний пользователь,
Last Week,Прошлая неделя,
Last Year,Прошлый год,
Last synced {0},Последняя синхронизация {0},
Leave a Comment,Оставить комментарий,
Leave blank to repeat always,"Оставьте пустым, чтобы повторять всегда",
Leave this conversation,Покинуть этот разговор,
Left this conversation,Покинуть этот разговор,
Length,Длина,
Length of {0} should be between 1 and 1000,Длина {0} должно быть от 1 до 1000,
Let's avoid repeated words and characters,Давайте избегать повторяющихся слов и символов,
Let's prepare the system for first use.,Давайте подготовить систему для первого использования.,
Letter,Письмо,
Letter Head Based On,Заголовок письма на основе,
Letter Head Image,Изображение в заголовке письма,
Letter Head Name,Название заголовка письма,
Letter Head in HTML,Заголовок письма в HTML,
Level Name,Название уровня,
Liked,Понравилось,
Liked By,Нравится,
Liked by {0},Нравится {0},
Likes,Понравившееся,
Limit Number of DB Backups,Ограничение количества резервных копий БД,
Line,Линия,
Link DocType,Ссылка DocType,
Link Expired,Срок действия ссылки,
Link Name,Имя ссылки,
Link Title,Название ссылки,
"Link that is the website home page. Standard Links (index, login, products, blog, about, contact)","Ссылка, которая является стартовой страницей сайта. Стандартные ссылки (index, login, products, blog, about, contact)",
Link to the page you want to open. Leave blank if you want to make it a group parent.,"Ссылка на страницу, которую вы хотите открыть. Оставьте пустым, если хотите сделать его родительским элементом группы.",
Linked,Связанный,
Linked With,Связанные с,
Linked with {0},Связано с {0},
Links,Связи,
List,Список,
List Filter,Фильтр списка,
List View Setting,Настройка просмотра списка,
List a document type,Перечислите тип документа,
"List as [{""label"": _(""Jobs""), ""route"":""jobs""}]","Список как [{""Метка"": _(""Работы""), ""маршруты"":""работы""}]",
List of backups available for download,"Список резервных копий, доступных для загрузки",
List of patches executed,Список выполненных патчей,
List of themes for Website.,Список тем для сайта.,
Load Balancing,Балансировка нагрузки,
Loading,Идёт загрузка,
Local DocType,Локальный DocType,
Local Fieldname,Локальное имя поля,
Local Primary Key,Местный первичный ключ,
Locals,Локальные переменные,
Log Details,Сведения о журнале,
Log of Scheduler Errors,Журнал ошибок планировщика,
Log of error during requests.,Журнал ошибок во время запросов.,
Log of error on automated events (scheduler).,Журнал ошибок автоматизированных событий (планировщик).,
Logged Out,Вышел из системы,
Logged in as Guest or Administrator,Войти как гость или администратор,
Login,Войти,
Login After,Войти после,
Login Before,Войти до,
Login Id is required,Требуется ID для входа,
Login Required,Авторизация обязательна,
Login Verification Code from {},Код подтверждения входа в систему {},
Login and view in Browser,Вход и просмотр в браузере,
Login not allowed at this time,Авторизация не допускается в это время,
"Login session expired, refresh page to retry","Срок действия сеанса истек, обновить страницу, чтобы повторить попытку",
Login to comment,Авторизоваться чтобы оставить комментарий,
Login token required,Для входа требуется токен,
Login with LDAP,Вход с LDAP,
Logout,Выход,
Long Text,Длинный текст,
Looks like something is wrong with this site's Paypal configuration.,"Похоже, что что-то не так с конфигурацией Paypal этого сайта.",
Looks like something is wrong with this site's payment gateway configuration. No payment has been made.,"Похоже, что-то не так с конфигурацией платежного шлюза этого сайта. Платеж не был выполнен.",
"Looks like something went wrong during the transaction. Since we haven't confirmed the payment, Paypal will automatically refund you this amount. If it doesn't, please send us an email and mention the Correlation ID: {0}.","Похоже, что-то пошло не так во время транзакции. Поскольку мы не подтвердили платеж, Paypal автоматически вернет вам эту сумму. Если это не так, отправьте нам электронное письмо и укажите идентификатор корреляции: {0}.",
Madam,Мадам,
Main Section,Основной раздел,
"Make ""name"" searchable in Global Search","Индексировать ""name"" для глобального поиска",
Make use of longer keyboard patterns,Используйте более длинных моделей клавиатуры,
Manage Third Party Apps,Управление приложениями сторонних разработчиков,
Mandatory Information missing:,Обязательная информация отсутствует:,
Mandatory field: set role for,Обязательное поле: установить роль для,
Mandatory field: {0},Обязательное поле: {0},
"Mandatory fields required in table {0}, Row {1}","Обязательные поля, требуемые в таблице {0}, строка {1}",
Mandatory fields required in {0},"Обязательные поля, необходимые в {0}",
Mandatory:,Обязательно:,
Mapping Name,Название карты,
Mappings,Отображения,
Mark as Read,"Пометить, как прочитанное",
Mark as Spam,Отметить как спам,
Mark as Unread,Отметить как непрочитанные,
Markdown,Markdown,
Markdown Editor,Редактор Markdown,
Marked As Spam,Помеченные как спам,
Max 500 records at a time,Макс. 500 записей за один раз,
Max Attachment Size (in MB),Макс. размер вложения (в МБ),
Max Attachments,Макс. вложений,
Max Length,Макс. длина,
Max Value,Макс. значение,
Max width for type Currency is 100px in row {0},Макс. ширина для типа валюты 100px в строке {0},
Maximum Attachment Limit for this record reached.,Достигнут предел вложений для этой записи.,
Maximum {0} rows allowed,Макс. {0} строк разрешено,
"Meaning of Submit, Cancel, Amend","Значение Подписать, Отменить, Изменить",
Mention transaction completion page URL,URL-ссылка на страницу-упоминание о завершении транзакции,
Mentions,Упоминания,
Menu,Меню,
Merchant ID,Идентификатор продавца,
Merge with existing,Слияние с существующими,
Merging is only possible between Group-to-Group or Leaf Node-to-Leaf Node,Слияние возможно только между Группа-в-группе или Leaf узел-листовой узел,
Message Count,Количество сообщений,
Message ID,Идентификатор сообщения,
Message Parameter,Параметры сообщения,
Message Preview,Предварительный просмотр сообщений,
Message clipped,Сообщение обрезано,
Message not setup,Сообщение не установлено,
Message to be displayed on successful completion (only for Guest users),"Сообщение, которое будет отображаться при успешном завершении (только для гостевых пользователей)",
Message-id,ID сообщения,
Meta Tags,Мета-теги,
Migration ID Field,Поле идентификатора миграции,
Milestone,Этап,
Milestone Tracker,Трекер этапа,
Minimum Password Score,Минимальный балл пароля,
Miss,Мисс,
Missing Fields,Отсутствующие поля,
Missing parameter Kanban Board Name,Отсутствует параметр Наименование канбан-доски,
Missing parameters for login,Недостающие параметры для входа,
Models (building blocks) of the Application,Модели (строительные блоки) приложения,
Modified By,Модифицирован,
Module,Модуль,
Module Def,Модуль Def,
Module Name,Название модуля,
Module Not Found,Модуль не найден,
Module Path,Путь модуля,
Module to Export,Модуль для экспорта,
Modules HTML,Модули HTML,
Monospace,Моноширинный,
More articles on {0},Другие статьи на {0},
More content for the bottom of the page.,Более контент для нижней части страницы.,
Most Used,Наиболее используемое,
Move To,Переместить в,
Move To Trash,Переместить в корзину,
Move to Row Number,Переместить на строку,
Mr,Г-н,
Mrs,Г-жа,
Ms,Госпожа,
Multiple root nodes not allowed.,Несколько корневые узлы не допускается.,
Multiplier Field,Поле множителя,
"Must be of type ""Attach Image""","Должно быть типа """"Прикрепить изображение""""",
Must have report permission to access this report.,Должен иметь разрешение отчета для доступа к этой отчета.,
Must specify a Query to run,"Необходимо указать запрос, чтобы запустить",
Mute Sounds,Отключить звуки,
MyISAM,MyISAM,
Name Case,"Название, наименование, обозначение дела",
Name cannot contain special characters like {0},"Имя не может содержать специальные символы, такие как {0}",
Name not set via prompt,Имя не установлено через строки,
Name of the Document Type (DocType) you want this field to be linked to. e.g. Customer,"Название типа документа (DOCTYPE) вы хотите это поле, чтобы быть связаны с. например клиентов",
Name of the new Print Format,Название новой печатной формы,
Name of {0} cannot be {1},Название {0} не может быть {1},
Names and surnames by themselves are easy to guess.,Имена и фамилии сами по себе легко угадать.,
Naming,Название,
"Naming Options:\n<ol><li><b>field:[fieldname]</b> - By Field</li><li><b>naming_series:</b> - By Naming Series (field called naming_series must be present</li><li><b>Prompt</b> - Prompt user for a name</li><li><b>[series]</b> - Series by prefix (separated by a dot); for example PRE.#####</li>\n<li><b>format:EXAMPLE-{MM}morewords{fieldname1}-{fieldname2}-{#####}</b> - Replace all braced words (fieldnames, date words (DD, MM, YY), series) with their value. Outside braces, any characters can be used.</li></ol>","Варианты именования: <ol><li> <b>поле: [имя поля]</b> - по полю </li><li> <b>naming_series:</b> - с помощью Naming Series (должно быть указано поле под названием naming_series </li><li> <b>Prompt</b> - запрашивать имя пользователя </li><li> <b>[серия]</b> - серия с префиксом (разделенная точкой); например, PRE. ##### </li><li> <b>format: EXAMPLE- {MM} morewords {fieldname1} - {fieldname2} - {#####}</b> - Заменить все слова с <b>привязкой (имена полей</b> , слова даты (DD, MM, YY), серия) с их значением. Внешние фигурные скобки могут использоваться любыми символами. </li></ol>",
Naming Series mandatory,Идентификация по Имени обязательна,
Nested set error. Please contact the Administrator.,"Вложенные набор ошибок. Пожалуйста, обратитесь к администратору.",
New Activity,Новая активность,
New Chat,Новый чат,
New Comment on {0}: {1},Новый комментарий к {0}: {1},
New Connection,Новое соединение,
New Custom Print Format,Новый пользовательский печатный бланк,
New Email,Новое письмо,
New Email Account,Новый аккаунт электронной почты,
New Event,Новое событие,
New Folder,Новая папка,
New Kanban Board,Новая панель канбан,
New Message from Website Contact Page,Новое сообщение с формы обратной связи на сайте,
New Name,Новое имя,
New Newsletter,Новая новость,
New Password,Новый пароль,
New Password Required.,Требуется новый пароль.,
New Print Format Name,Название нового печатного бланка,
New Report name,Новое название отчёта,
New Value,Новое значение,
New data will be inserted.,Новые данные будут вставлены.,
New updates are available,Доступны новые обновления,
New value to be set,Новое значение будет установлено,
New {0},Новый {0},
New {} releases for the following apps are available,Доступны новые {} выпуски для следующих приложений.,
Newsletter Email Group,Группа рассылки,
Newsletter Manager,Менеджер рассылки,
Newsletter has already been sent,Информационный бюллетень уже был отправлен,
"Newsletters to contacts, leads.","Рассылки контактам, обращениям",
Next Action Email Template,Шаблон письма по следующим действиям,
Next Actions HTML,Следующие действия HTML,
Next Schedule Date,Следующая дата расписания,
Next Scheduled Date,Следующая дата по расписанию,
Next State,Следующее состояние,
Next Sync Token,Следующий токен синхронизации,
Next actions,Дальнейшие действия,
No Active Sessions,Нет активных сеансов,
No Copy,Без копии,
No Email Account,Нет аккаунта электронной почты,
No Email Accounts Assigned,Аккаунты электронной почты не установлены,
No Emails,Нет сообщений электронной почты,
No Label,Без отметки,
No Permissions Specified,Нет разрешенных разрешений,
No Permissions set for this criteria.,Разрешения не установлен для этого критериев.,
No Preview,Нет предварительного просмотра,
No Preview Available,Предварительный просмотр недоступен,
No Printer is Available.,Принтер не доступен.,
No Results,Нет результатов,
No Tags,Нет тегов,
No alerts for today,Нет предупреждений на сегодня,
No comments yet,Комментариев нет,
No comments yet. Start a new discussion.,Пока без коментариев. Начать новое обсуждение.,
No data found in the file. Please reattach the new file with data.,В файле нет данных. Перезагрузите новый файл данными.,
No document found for given filters,Документ не найден для данных фильтров,
"No fields found that can be used as a Kanban Column. Use the Customize Form to add a Custom Field of type ""Select"".","Не найдено полей, которые можно использовать в качестве столбца Канбана. Используйте форму настройки, чтобы добавить настраиваемое поле типа «Выбрать».",
No file attached,Нет прикрепленных файлов,
No further records,Никаких дополнительных записей,
No matching records. Search something new,Нет соответствующих записей. Поиск что-то новое,
"No need for symbols, digits, or uppercase letters.","Нет необходимости для символов, цифр или букв в верхнем регистре.",
No of Columns,Кол-во колонок,
No of Rows (Max 500),Кол-во строк (максимум 500),
No of emails remaining to be synced,Нет сообщений для синхронизации,
No permission for {0},Нет доступа для {0},
No permission to '{0}' {1},Нет доступа для '{0}' {1},
No permission to read {0},"Нет разрешения, чтобы прочитать {0}",
No permission to {0} {1} {2},Нет доступа для {0} {1} {2},
No records deleted,Записи не удалены,
No records present in {0},Нет записей в {0},
No records tagged.,Записи отсутствуют помечены.,
No template found at path: {0},Нет шаблона по адресу: {0},
No {0} found,{0} не найдено,
No {0} mail,Нет {0} почта,
No {0} permission,Нет {0} разрешение,
None: End of Workflow,Нет: конец рабочего процесса,
Not Allowed: Disabled User,Не разрешено: пользователь отключен,
Not Ancestors Of,Не предки,
Not Descendants Of,Не потомки,
Not Equals,Не равно,
Not In,Не в,
Not Linked to any record,Не привязано к какой-либо записи,
Not Published,Не опубликовано,
Not Saved,Не сохранено,
Not Seen,Непрочитанно,
Not Sent,Не отправлено,
Not Set,Не указано,
Not a valid Comma Separated Value (CSV File),"Не является допустимым значения, разделенные запятыми (CSV-файл)",
Not a valid User Image.,Недействительный изображение пользователя.,
Not a valid Workflow Action,Недоступное действие рабочего-процесса,
Not a valid user,Не является действительным пользователем,
Not a zip file,Не является zip файлом,
Not allowed for {0}: {1},Не разрешено для {0}: {1},
"You are not allowed to access {0} because it is linked to {1} '{2}' in row {3}, field {4}","Вы не имеете права доступа к {0}, потому что он связан с {1} '{2}' в строке {3}, поле {4}",
You are not allowed to access this {0} record because it is linked to {1} '{2}' in field {3},"Вы не имеете права доступа к этой записи {0}, потому что он связан с {1} '{2}' в поле {3}",
Not allowed to Import,Не разрешается импортировать,
Not allowed to change {0} after submission,Не разрешено менять {0} после подтверждения,
Not allowed to print cancelled documents,Не разрешено печатать аннулированные документы,
Not allowed to print draft documents,Не разрешено печатать черновые документы,
Not enough permission to see links,Недостаточно прав для просмотра ссылок,
Not in Developer Mode,Не в режиме разработчика,
Not in Developer Mode! Set in site_config.json or make 'Custom' DocType.,"Не в режиме разработчика! Расположенный в site_config.json или сделать DOCTYPE ""Custom"".",
Note Seen By,Примечание увиденных,
Note:,Заметка:,
Note: By default emails for failed backups are sent.,Примечание. По умолчанию отправляются сообщения электронной почты о неудачных резервных копиях.,
Note: Changing the Page Name will break previous URL to this page.,Примечание. Изменение имени страницы разбивает предыдущий URL на эту страницу.,
"Note: For best results,  images must be of the same size and width must be greater than height.","Примечание. Для получения наилучших результатов изображения должны быть одинакового размера, а ширина должна быть больше высоты.",
Note: Multiple sessions will be allowed in case of mobile device,Примечание: Несколько сессий будет разрешено в случае мобильного устройства,
Nothing to show,"К сожалению, здесь еще ничего не размещено",
Nothing to update,Нечего обновлять,
Notification,Уведомление,
Notification Recipient,Получатель уведомлений,
Notification Tones,Сигналы уведомления,
Notifications,Уведомления,
Notifications and bulk mails will be sent from this outgoing server.,Уведомления и сыпучих почты будет отправлено от этого сервера исходящей почты.,
Notify Users On Every Login,Уведомлять пользователей о каждом входе в систему,
Notify if unreplied,Уведомить при отсутствии ответа,
Notify if unreplied for (in mins),Уведомить при отсутствии ответа в течении (в мин),
Notify users with a popup when they log in,"Сообщите пользователям всплывающего окна, когда они войти",
Number Format,Формат числа,
Number of Backups,Количество резервных копий,
Number of DB Backups,Количество резервных копий БД,
Number of DB backups cannot be less than 1,Количество резервных копий БД не может быть меньше 1,
Number of columns for a field in a Grid (Total Columns in a grid should be less than 11),Количество колонок для поля в сетке (Всего столбцов в сетке должно быть не менее 11),
Number of columns for a field in a List View or a Grid (Total Columns should be less than 11),Количество столбцов для поля в режиме Список или Таблица (общее количество столбцов должно быть меньше 11),
OAuth Authorization Code,Код авторизации OAuth,
OAuth Bearer Token,OAuth Bearer Токен,
OAuth Client,Клиент OAuth,
OAuth Provider Settings,Настройки провайдера OAuth,
OTP App,OTP-приложение,
OTP Issuer Name,Название эмитента OTP,
OTP Secret has been reset. Re-registration will be required on next login.,OTP Secret был сброшен. Повторная регистрация потребуется при следующем входе в систему.,
OTP secret can only be reset by the Administrator.,Секрет OTP может быть сброшен администратором.,
Office,Офис,
Office 365,Офис 365,
Old Password,Старый пароль,
Old Password Required.,Старый пароль обязателен.,
Older backups will be automatically deleted,Более старые резервные копии будут автоматически удалены,
"On {0}, {1} wrote:","На {0}, {1} писал:",
"Once submitted, submittable documents cannot be changed. They can only be Cancelled and Amended.",После отправки подписанные документы не могут быть изменены. Они могут быть только отменены и исправлены.,
"Once you have set this, the users will only be able access documents (eg. Blog Post) where the link exists (eg. Blogger).","После такой установки, пользователи получат доступ только к документам (например, сообщениям в блоге), связанным с этими разрешениями пользователя (например, блоггера).",
One Last Step,Последний шаг,
One Time Password (OTP) Registration Code from {},Одноразовый пароль (OTP) Регистрационный код от {},
Only 200 inserts allowed in one request,Только 200 вставок допускается в одной заявке,
Only Administrator can delete Email Queue,Только администратор может удалить очередь электронной почты,
Only Administrator can edit,Только администратор может редактировать,
Only Administrator can save a standard report. Please rename and save.,"Только администратор может сохранить стандартный отчет. Пожалуйста, переименуйте и сохраните.",
Only Administrator is allowed to use Recorder,Только администратор может использовать регистратор,
Only Allow Edit For,Разрешено редактировать только,
Only Send Records Updated in Last X Hours,"Отправлять записи, обновленные за последние X часов",
Only mandatory fields are necessary for new records. You can delete non-mandatory columns if you wish.,"Только обязательные поля необходимы для новых записей. Вы можете удалить необязательные столбцы, если хотите.",
Only standard DocTypes are allowed to be customized from Customize Form.,Только стандартные типы документов могут быть настроены из формы настройки.,
Only users involved in the document are listed,"Только пользователи, вовлеченные в документ, перечислены",
Only {0} emailed reports are allowed per user,Только {0} отчетов по электронной почте разрешено пользователю,
Oops! Something went wrong,Упс! Что-то пошло не так.,
"Oops, you are not allowed to know that","К сожалению, у вас нет права доступа",
Open Link,Открыть ссылку,
Open Source Applications for the Web,Open Source приложений для Web,
Open Translation,Открыть перевод,
Open a dialog with mandatory fields to create a new record quickly,"Откройте диалог с обязательными полями, чтобы быстро создать новую запись",
Open a module or tool,Открыть модуль или инструмент,
Open your authentication app on your mobile phone.,Откройте приложение для проверки подлинности на своем мобильном телефоне.,
Open {0},Открыть {0},
Opened,Открыть,
Operator must be one of {0},Оператор должен быть одним из {0},
Option 1,Опция 1,
Option 2,Опция 2,
Option 3,Опция 3,
Optional: Always send to these ids. Each Email Address on a new row,Дополнительно: Всегда рассылают по этим идентификаторам. Каждый адрес электронной почты в новой строке,
Optional: The alert will be sent if this expression is true,"Дополнительно: предупреждение будет отправлено, если это выражение истинно",
Options 'Dynamic Link' type of field must point to another Link Field with options as 'DocType',"""Dynamic Link 'Тип варианты поле должен указывать на другой Link Field с вариантами, как« DocType """,
Options Help,Опции Помощь,
Options for select. Each option on a new line.,Варианты выбора. Каждый вариант с новой строки.,
Options not set for link field {0},Опции не установлен поля связи {0},
Or login with,Или войти в систему с,
Order,Порядок,
Org History,Org История,
Org History Heading,Org История Заголовок,
Orientation,Ориентация,
Original Value,Первоначальная стоимость,
Outgoing email account not correct,Исходящая учетная запись электронной почты не верна,
Output,Вывод,
PDF Page Size,Размер PDF страницы,
PDF Settings,Настройки PDF,
PDF generation failed,Не удалось сгенерировать PDF-файл,
PDF generation failed because of broken image links,Не удалось сгенерировать PDF из-за битых ссылок изображения,
"PDF printing via ""Raw Print"" is not yet supported. Please remove the printer mapping in Printer Settings and try again.",Печать PDF через «Raw Print» пока не поддерживается. Удалите отображение принтера в настройках принтера и повторите попытку.,
Page HTML,Страница HTML,
Page Length,Длина страницы,
Page Name,Имя страницы,
Page Settings,Настройки страницы,
Page has expired!,Срок действия страницы истек!,
Page not found,Страница не найдена,
Page to show on the website\n,Страница для показа на сайте,
Pages in Desk (place holders),Страницы-заглушки,
Parent,Родитель,
Parent Error Snapshot,Родитель снимка ошибки,
Parent Label,Родительская метка,
Parent Table,Родительская таблица,
Parent is required to get child table data,Родитель обязан получать данные дочерней таблицы,
Parent is the name of the document to which the data will get added to.,"Родитель - это имя документа, к которому будут добавлены данные.",
Partial Success,Выполнено не полностью,
Partially Successful,Частично успешный,
Participants,Участники,
Passive,Пассивный,
Password Reset,Сброс пароля,
Password Updated,Пароль обновлён,
Password for Base DN,Пароль для Base DN,
Password is required or select Awaiting Password,Требуется пароль или выберите Ожидание пароля,
Password not found,Пароль не найден,
Password reset instructions have been sent to your email,Инструкции по восстановлению пароля были отправлены на ваш email,
Paste,Вставить,
Patch,Исправление,
Patch Log,Журнал исправлений,
Path to CA Certs File,Путь к CA Certs File,
Path to Server Certificate,Путь к сертификату сервера,
Path to private Key File,Путь к файлу закрытого ключа,
PayPal Settings,Настройки PayPal,
PayPal payment gateway settings,Настройки оплаты шлюз PayPal,
Payment Cancelled,Оплата отменена,
Payment Success,Оплата прошла успешно,
Pending Approval,В ожидании утверждения,
Pending Verification,ожидает подтвержения,
Percent,Процент,
Percent Complete,Процент завершен,
Perm Level,Уровень разрешения,
Permanent,Постоянный,
Permanently Cancel {0}?,Постоянно Отменить {0}?,
Permanently Submit {0}?,Постоянно провести {0}?,
Permanently delete {0}?,Навсегда удалить {0}?,
Permission Error,Ошибка доступа,
Permission Level,Уровень доступа,
Permission Levels,Уровни доступа,
Permission Rules,Правило доступа,
Permissions,Разрешения,
Permissions are automatically applied to Standard Reports and searches.,Разрешения автоматически применяются к стандартнSым отчетам и поисковым запросам.,
"Permissions are set on Roles and Document Types (called DocTypes) by setting rights like Read, Write, Create, Delete, Submit, Cancel, Amend, Report, Import, Export, Print, Email and Set User Permissions.","Разрешения устанавливаются в Ролях и Типах Документов (называемые DocTypes) путем наделения правами Чтение, Запись, Создание, Удаление, Проведение, Отмена, Изменение, Создание отчета, Импорт, Экпорт, Печать, Отправка email и Назначение прав пользователя.",
Permissions at higher levels are Field Level permissions. All Fields have a Permission Level set against them and the rules defined at that permissions apply to the field. This is useful in case you want to hide or make certain field read-only for certain Roles.,"Разрешения на более высоких уровнях - это разрешения на уровне поля. Все поля имеют установленный для них уровень разрешения, и правила, определенные в этих разрешениях, относятся к полю. Это полезно, если вы хотите скрыть или сделать определенное поле только для чтения для определенных ролей.",
"Permissions at level 0 are Document Level permissions, i.e. they are primary for access to the document.","Разрешения на уровне 0 это разрешения на уровне документа, то есть они являются первичными для доступа к документу.",
Permissions get applied on Users based on what Roles they are assigned.,"Разрешения применяются к Пользователям на основе того, какие роли им назначены.",
Personal,Личное,
Personal Data Deletion Request,Запрос на удаление персональных данных,
Personal Data Download Request,Запрос на скачивание личных данных,
Phone No.,Номер телефона,
Pick Columns,Выберите столбцы,
Plant,Завод,
Please Duplicate this Website Theme to customize.,"Пожалуйста, дублировать эту тему для настройки.",
Please Enter Your Password to Continue,"Пожалуйста, введите ваш пароль чтобы продолжить",
Please Install the ldap3 library via pip to use ldap functionality.,"Пожалуйста, установите библиотеку ldap3 через pip, чтобы использовать функциональность ldap.",
Please Update SMS Settings,Обновите настройки SMS,
Please add a subject to your email,"Пожалуйста, укажите тему вашего письма",
Please ask your administrator to verify your sign-up,"Пожалуйста, обратитесь к администратору, для подтверждения регистрации",
Please attach a file first.,"Пожалуйста, прикрепите первый файл.",
Please attach an image file to set HTML,"Пожалуйста, прикрепите файл изображения, чтобы установить HTML",
Please check your email for verification,"Пожалуйста, проверьте свою электронную почту для подтверждения",
Please check your registered email address for instructions on how to proceed. Do not close this window as you will have to return to it.,"Пожалуйста, проверьте электронную почту регистрации для получения инструкций. Не закрывайте это окно, так как вам придется вернуться к нему.",
Please close this window,"Пожалуйста, закройте это окно",
Please confirm your action to {0} this document.,Подтвердите свое действие до {0} этого документа.,
Please do not change the rows above {0},"Пожалуйста, не изменяйте строки выше {0}",
Please do not change the template headings.,"Пожалуйста, не изменяйте заголовки шаблона.",
Please duplicate this to make changes,"Пожалуйста, дублировать это внести изменения",
Please enable developer mode to create new connection,"Включите режим разработчика, чтобы создать новое соединение.",
Please ensure that your profile has an email address,"Пожалуйста, убедитесь, что ваш профиль имеет адрес электронной почты",
Please enter Access Token URL,Введите URL-адрес токена доступа,
Please enter Authorize URL,Введите URL авторизации,
Please enter Base URL,Введите базовый URL-адрес,
Please enter Client ID before social login is enabled,Введите идентификатор клиента перед включением социального входа,
Please enter Client Secret before social login is enabled,Введите секретный ключ клиента перед включением социального входа,
Please enter Redirect URL,Введите URL-адрес переадресации,
Please enter the password,"Пожалуйста, введите пароль",
Please enter valid mobile nos,Введите действительные мобильных NOS,
Please enter values for App Access Key and App Secret Key,"Пожалуйста, введите ключ доступа и секретный ключ приложения",
Please make sure that there are no empty columns in the file.,"Пожалуйста, убедитесь, что нет никаких пустые столбцы в файле.",
Please make sure the Reference Communication Docs are not circularly linked.,"Пожалуйста, убедитесь, что справочные коммуникационные документы не имеют циклических ссылок.",
Please refresh to get the latest document.,"Обновите, чтобы получить последнюю документ.",
Please save before attaching.,"Пожалуйста, сохраните перед установкой.",
Please save the Newsletter before sending,"Пожалуйста, сохраните бюллетень перед отправкой",
Please save the document before assignment,"Пожалуйста, сохраните документ, прежде чем задания",
Please save the document before removing assignment,"Пожалуйста, сохраните документ, прежде чем снимать назначение",
Please save the report first,"Пожалуйста, сохраните отчет первой",
Please select DocType first,"Пожалуйста, выберите DOCTYPE первый",
Please select Entity Type first,Сначала выберите тип сущности,
Please select Minimum Password Score,Выберите минимальный балл пароля,
Please select a Amount Field.,"Пожалуйста, выберите поле Сумма.",
Please select a file or url,"Пожалуйста, выберите файл или URL",
Please select a new name to rename,Выберите новое имя для переименования,
Please select a valid csv file with data,"Пожалуйста, выберите правильный файл CSV с данными",
Please select another payment method. PayPal does not support transactions in currency '{0}',"Пожалуйста, выберите другой способ оплаты. PayPal не поддерживает транзакции в валюте «{0}»",
Please select another payment method. Razorpay does not support transactions in currency '{0}',"Пожалуйста, выберите другой способ оплаты. Razorpay не поддерживает транзакции в валюте «{0}»",
Please select atleast 1 column from {0} to sort/group,"Пожалуйста, выберите по крайней мере 1 столбец {0} для сортировки / групп",
Please select document type first.,Сначала выберите тип документа.,
Please select the Document Type.,Выберите тип документа.,
Please set Base URL in Social Login Key for Frappe,Укажите базовый URL-адрес в ключе социального входа для Frappe,
Please set Dropbox access keys in your site config,"Пожалуйста, установите ключи доступа Dropbox на своем сайте конфигурации",
Please set a printer mapping for this print format in the Printer Settings,"Пожалуйста, установите сопоставление принтера для этого формата печати в настройках принтера",
Please set filters,"Пожалуйста, установите фильтры",
Please set filters value in Report Filter table.,"Пожалуйста, установите значение фильтров в Report Filter таблицы.",
"Please setup SMS before setting it as an authentication method, via SMS Settings","Пожалуйста, настройте SMS, прежде чем устанавливать его как метод аутентификации, с помощью настроек SMS",
Please setup a message first,Сначала настройте сообщение,
Please specify which date field must be checked,"Просьба уточнить, какие поля даты должны быть проверены",
Please specify which value field must be checked,"Просьба уточнить, какие значения поля должны быть проверены",
Please try again,"Пожалуйста, попробуйте еще раз",
Please verify your Email Address,"Пожалуйста, подтвердите свой адрес электронной почты",
Point Allocation Periodicity,Периодичность распределения баллов,
Points,Баллы,
Points Given,Баллы засчитаны,
Port,Порт,
Portal Menu,Меню портала,
Portal Menu Item,Пункт меню портала,
Post,Опубликовать,
Post Comment,Оставить комментарий,
Postal,Почтовый,
Postal Code,Почтовый индекс,
Postprocess Method,Метод постпроцесса,
Posts,Посты,
Posts by {0},Сообщений {0},
Posts filed under {0},"Сообщения, поданные в соответствии с {0}",
Precision,Точность,
Precision should be between 1 and 6,Точность должна быть между 1 и 6,
Predictable substitutions like '@' instead of 'a' don't help very much.,Предсказуемость замены как &#39;@&#39; вместо &#39;а&#39; не очень поможет.,
Preferred Billing Address,Популярные Адрес для выставления счета,
Preferred Shipping Address,Популярные Адрес доставки,
Prepared Report,Подготовленный отчет,
Preparing Report,Подготовка отчета,
Preprocess Method,Метод предварительной обработки,
Press Enter to save,"Нажмите «Ввод», чтобы сохранить",
Preview HTML,Предварительный HTML,
Preview Message,Предварительный просмотр сообщения,
Previous,Предыдущая,
Previous Hash,Предыдущий хэш,
Primary Color,Основной цвет,
Print Documents,Печать документов,
Print Format Builder,Конструктор бланков для печати,
Print Format Help,Помощь по печатным формам,
Print Format Type,Тип печатной формы,
Print Format {0} is disabled,Печатная форма {0} отключена,
Print Hide,Не печатать,
Print Hide If No Value,Не печатать если нет значения,
Print Sent to the printer!,Печать отправлена на принтер!,
Print Server,Сервер печати,
Print Style,Стиль печати,
Print Style Name,Название стиля печати,
Print Style Preview,Предварительный просмотр стиля печати,
Print Width,Ширина печати,
"Print Width of the field, if the field is a column in a table","Ширина поля при печати, если поле является столбцом в таблице",
Print with letterhead,Печать с помощью фирменных бланков,
Printer,Принтер,
Printer Mapping,Отображение принтера,
Printer Name,Имя принтера,
Printer Settings,Настройки принтера,
Printing failed,Ошибка печати,
Private Key,Закрытый ключ,
Private and public Notes.,Личные и общедоступные примечания.,
ProTip: Add <code>Reference: {{ reference_doctype }} {{ reference_name }}</code> to send document reference,ProTip: Добавить <code>Reference: {{ reference_doctype }} {{ reference_name }}</code> отправить справочный документ,
Processing,Обработка,
Processing...,Обработка...,
Prof,Проф.,
Progress,Готовность,
Property Setter,Сеттер недвижимости,
Property Setter overrides a standard DocType or Field property,Недвижимость сеттер отменяет стандартный DOCTYPE или свойств поля,
Property Type,Тип недвижимости,
Provider,Поставщик,
Provider Name,Имя поставщика,
Public Key,Открытый ключ,
Publishable Key,Ключ для публикации,
Published On,Опубликовано на,
Python Module,Модуль Python,
Pyver,Pyver,
QR Code,QR код,
QR Code for Login Verification,QR-код для подтверждения входа,
QZ Tray Connection Active!,Подключение к лотку QZ активно!,
QZ Tray Failed: ,QZ Tray Failed:,
Quarter Day,Четверть дня,
Query,Запрос,
Query Report,Отчёт-выборка,
Query must be a SELECT,Запрос должен быть ВЫБОР,
Queue should be one of {0},Очередь должна быть одной из {0},
Queued for backup. It may take a few minutes to an hour.,В очереди для резервного копирования. Это может занять от несколько минут до часа.,
Queued for backup. You will receive an email with the download link,Очередь для резервного копирования. Вы получите электронное письмо с ссылкой для загрузки,
Quick Help for Setting Permissions,Быстрая помощь при настройки прав доступа,
Rating: ,Рейтинг: ,
Raw Commands,Необработанные команды,
Raw Email,Необработанная почта,
Raw Printing,Необработанная печать,
Razorpay Payment gateway settings,Настройки шлюза оплаты Razorpay,
Razorpay Settings,Настройки Razorpay,
Re: ,На:,
Re: {0},На: {0},
Read,Читать,
Read Only,Только чтения,
Read by Recipient,Прочитано получателем,
Read by Recipient On,Прочитано получателем вкл.,
Rebuild,Перестраивать,
Receiver Parameter,Параметры получателя,
Recent years are easy to guess.,За последние годы легко догадаться.,
Recipient,Сторона-реципиент,
Recipient Unsubscribed,Получатель отменил подписку,
Record does not exist,Запись не существует,
Records for following doctypes will be filtered,Записи для следующих доктринов будут отфильтрованы,
Redirect To,Перенаправить,
Redirect URI Bound To Auth Code,"Перенаправление URI, связанного с Auth кодекса",
Redirect URIs,Перенаправление идентификаторы URI,
Redis cache server not running. Please contact Administrator / Tech support,"Кэш-сервер Redis не работает. Пожалуйста, обратитесь к администратору / техническую поддержку",
Ref DocType,DocType ссылки,
Ref Report DocType,Ref Report DocType,
Reference DocName,Ссылка DocName,
Reference DocType and Reference Name are required,Ссылка DocType и Имя ссылки требуется,
Reference Report,Справочный отчет,
Reference: {0} {1},Ссылка: {0} {1},
Refreshing...,Обновление...,
Register OAuth Client App,Регистрация OAuth Client App,
Registered but disabled,Зарегистрированный но отключен,
Relapsed,Повторный,
Relapses,Повторные,
Relink,Связь,
Relink Communication,Повторно связать коммуникации,
Relinked,Связать повторно,
Reload,Обновить страницу,
Remember Last Selected Value,Помнить последнее выбранное значение,
Remote,Дистанционный пульт,
Remote Fieldname,Удаленное имя поля,
Remote ID,Удаленный идентификатор,
Remote Objectname,Удаленное имя объекта,
Remote Primary Key,Удаленный основной ключ,
Remove,Удалить,
Remove Field,Удалить поле,
Remove Filter,Удалить фильтр,
Remove Section,Удалить раздел,
Remove Tag,Убрать метку,
Remove all customizations?,Удалить все настройки?,
Removed {0},Удалены {0},
Rename many items by uploading a .csv file.,"Переименовать много пунктов, загрузив. Файл CSV.",
Rename {0},Переименовать {0},
Repeat Header and Footer in PDF,Повторите Колонтитулы в PDF,
Repeat On,Повторите на,
Repeat Till,Повторите до,
Repeat on Day,Повторить в день,
Repeat this Event,Повторить этот событие,
"Repeats like ""aaa"" are easy to guess","Повторы, как &quot;ааа&quot; легко догадаться,",
"Repeats like ""abcabcabc"" are only slightly harder to guess than ""abc""","Повторы, как &quot;abcabcabc&quot; лишь немного труднее угадать, чем &quot;Азбуки&quot;",
Reply,Ответить,
Reply All,Ответить всем,
Report End Time,Время окончания отчета,
Report Filters,Фильтры отчетов,
Report Hide,Скрыть отчет,
Report Manager,Диспетчер отчетов,
Report Name,Название отчета,
Report Start Time,Время начала отчета,
Report cannot be set for Single types,Сообщить не могут быть установлены для отдельных видов,
Report of all document shares,Сообщить всех акций документов,
Report updated successfully,Отчет успешно обновлен,
Report was not saved (there were errors),Сообщить не был сохранен (были ошибки),
Report {0},Сообщить {0},
Report {0} is disabled,Сообщить о {0} отключена,
Report:,Доклад:,
Represents a User in the system.,Представляет пользователя в системе.,
Represents the states allowed in one document and role assigned to change the state.,"Показывает статусы документа, и роли с правами для их изменения.",
Request Timed Out,Истекло время запроса,
Request URL,URL запроса,
Require Trusted Certificate,Требуется доверенный сертификат,
Res: {0},Res: {0},
Reset OTP Secret,Сбросить секретный ключ OTP,
Reset Password,Сбросить пароль,
Reset Password Key,Сброс пароля ключ,
Reset Permissions for {0}?,Сброс разрешений для {0}?,
Reset to defaults,Восстановить значения по умолчанию,
Reset your password,Сбросить пароль,
Response Type,Ответ Тип,
Restore,Восстановить,
Restore Original Permissions,Восстановление исходных разрешений,
Restore or permanently delete a document.,Восстановить или навсегда удалить документ.,
Restore to default settings?,Восстановление настроек по умолчанию?,
Restored,Восстановлена,
Restrict IP,Ограничения IP,
Restrict To Domain,Ограничить доменом,
Restrict user for specific document,Ограничить пользователя конкретным документом,
Restrict user from this IP address only. Multiple IP addresses can be added by separating with commas. Also accepts partial IP addresses like (111.111.111),"Разрешить пользователю только этот IP-адрес. Несколько IP-адресов могут быть добавлены через запятую. Принимает также частичные IP-адреса, например 111.111.111",
Resume Sending,Резюме Отправка,
Retake,пересдавать,
Retry,Повторить,
Return to the Verification screen and enter the code displayed by your authentication app,"Вернитесь на экран проверки и введите код, отображаемый приложением для аутентификации.",
Reverse Icon Color,Обратный цвет значка,
Revert,Возврат,
Revert Of,Возвращенно из,
Reverted,Отменено,
Review Level,Уровень обзора,
Review Levels,Уровни обзора,
Review Points,Баллы обзора,
Reviews,Отзывы,
Revoke,Аннулировать,
Revoked,Аннулировано,
Rich Text,Форматированный текст,
Role Name,Название роли,
Role Permission for Page and Report,Разрешение роли для страницы и отчета,
Role Permissions,Разрешения роли,
Role Profile,Профиль ролей,
Role and Level,Роль и уровень,
Roles,Роли,
Roles Assigned,Роли назначены,
Roles can be set for users from their User page.,Роли могут быть установлены для пользователей со страницы пользователя.,
Root {0} cannot be deleted,Корневая {0} не может быть удален,
Round Robin,По-круговой,
Route History,История маршрута,
Route Redirects,Маршрутные перенаправления,
Route to Success Link,Ссылка маршрута к успеху,
Row,Строка,
Row #{0}:,Строка #{0}:,
Row Index,Индекс строк,
Row No,Строка №,
Row Status,Статус строки,
Row Values Changed,Значения строк Измененные,
Row {0}: Not allowed to disable Mandatory for standard fields,Строка {0}: не разрешено отключать обязательные для стандартных полей,
Row {0}: Not allowed to enable Allow on Submit for standard fields,Строка {0}: Не разрешается включать Разрешить проведение для стандартных полей,
Rows Added,Строки добавлены,
Rows Removed,Строки удалены,
Rule,Правило,
Rule Name,Название правила,
Rules defining transition of state in the workflow.,"Правила, определяющие переход этапов в потоке.",
"Rules for how states are transitions, like next state and which role is allowed to change state etc.","Правила для перехода статусов, например, следующий статус и роль, которой доступна смена статуса и т.д.",
Run,Запуск,
Run scheduled jobs only if checked,"Запуск запланированных заданий, только если проверяются",
S3 Backup Settings,Настройки резервного копирования S3,
S3 Backup complete!,Резервное копирование S3 завершено!,
SMS,SMS,
SMS Gateway URL,URL SMS-шлюза,
SMS Parameter,Параметр SMS,
SMS Settings,Настройки SMS,
SMS sent to following numbers: {0},SMS отправлено следующим номерам: {0},
SMTP Server,SMTP-сервер,
SMTP Settings for outgoing emails,Настройки SMTP для исходящих писем,
"SQL Conditions. Example: status=""Open""",SQL условия. Пример: статус = &quot;Открыть&quot;,
SSL/TLS Mode,Режим SSL/TLS,
Salesforce,Salesforce,
Same Field is entered more than once,Одно и то же поле вводится не один раз,
Save API Secret: ,Сохранить API секрет: ,
Save As,Сохранить как,
Save Filter,Сохранить фильтр,
Save Report,Сохранить отчет,
Save filters,Сохранить фильтры,
Saving,Сохранение,
Saving...,Сохранение...,
Scan the QR Code and enter the resulting code displayed.,Сканируйте QR-код и введите полученный код.,
Scopes,Области применения,
Script,Скрипт,
Script Report,Программируемый отчёт,
Script or Query reports,Отчёты-выборка или программируемый отчёт,
Script to attach to all web pages.,Сценарий для подключения к всех веб-страниц.,
Search Fields,Поиск полей,
Search Help,Поиск по справке,
Search field {0} is not valid,Поле поиска {0} не является действительным,
Search for '{0}',Поиск '{0}',
Search for anything,Искать что-нибудь,
Search in a document type,Поиск в тип документа,
Search or Create a New Chat,Поиск или создание нового чата,
Search or type a command,Поиск либо введите команду,
Search...,Поиск...,
Searching,Поиск,
Searching ...,Поиск ...,
Section Break,Разделитель секций,
Section Heading,Заголовок раздела,
Security,Безопасность,
Security Settings,Настройки безопасности,
See all past reports.,Посмотреть все прошлые отчеты.,
See on Website,Посмотреть на веб-сайте,
See the document at {0},См. Документ в {0},
Seems API Key or API Secret is wrong !!!,"Кажется, ключ API или API Секрет неверны !!!",
Seems Publishable Key or Secret Key is wrong !!!,"Кажется, что ключ для публикации или секретный ключ неправильный !!!",
"Seems issue with server's razorpay config. Don't worry, in case of failure amount will get refunded to your account.","Кажется, вопрос с razorpay конфигурации сервера. Не волнуйтесь, в случае отказа суммы будет получить возврат на ваш счет.",
Seems token you are using is invalid!,"Кажется, токен, который вы используете, недействителен!",
Seen,Посещение,
Seen By,Виденный,
Seen By Table,Увиденные таблице,
Select Attachments,Выберите вложения,
Select Child Table,Выберите дочерний стол,
Select Column,Выберите колонку,
Select Columns,Выбрать столбцы,
Select Document Type,Выбор типа документа,
Select Document Type or Role to start.,"Выберите тип документа или роль, чтобы начать.",
Select Document Types to set which User Permissions are used to limit access.,"Выберите типы документов, чтобы указать, какие пользовательские разрешения используются для ограничения доступа.",
Select File Format,Выберите формат файла,
Select File Type,Выберите тип файла,
Select Language...,Выберите язык...,
Select Languages,Выберите языки,
Select Module,Выбор модуля,
Select Print Format,Выберите бланк для печати,
Select Print Format to Edit,Выберите печатный бланк для редактирования,
Select Role,Выберите роль,
Select Table Columns for {0},Выберите столбцы таблицы для {0},
Select Your Region,Выберите ваш регион,
Select a Brand Image first.,Выберите бренд изображение в первую очередь.,
Select a DocType to make a new format,"Выберите DOCTYPE, чтобы сделать новый бланк",
Select a chat to start messaging.,"Выберите чат, чтобы начать обмен сообщениями.",
Select a group node first.,Выберите узел группы в первую очередь.,
Select an existing format to edit or start a new format.,Выберите существующий формат для редактирования или начать новый формат.,
Select an image of approx width 150px with a transparent background for best results.,Выберите изображение шириной около 150px с прозрачным фоном для достижения наилучших результатов.,
Select atleast 1 record for printing,Выберите по крайней мере 1 запись для печати,
Select or drag across time slots to create a new event.,"Выберите или перетащите через временные интервалы, чтобы создать новое событие.",
Select records for assignment,Выберите записи для присвоения,
Select the label after which you want to insert new field.,"Выберите метку, после чего Вы хотите вставить новое поле.",
"Select your Country, Time Zone and Currency","Выберите страну, часовой пояс и валюта",
Select {0},Выберите {0},
Self approval is not allowed,Самоподтверждение не допускается,
Send After,Отправить после,
Send Alert On,Отправить оповещение о,
Send Email Alert,Отправлять оповещение по электронной почте,
Send Email Print Attachments as PDF (Recommended),Отправлять Email-вложения в формате PDF (рекомендуется),
Send Email for Successful Backup,Отправить письмо для успешного резервного копирования,
Send Me A Copy of Outgoing Emails,Отправить мне копию исходящих писем,
Send Notification to,Отправить уведомление на,
Send Notifications To,Отправлять уведомления,
Send Print as PDF,Отправить Печать в формате PDF,
Send Read Receipt,Отправить прочтение,
Send Unsubscribe Link,Отправить ссылку отписки,
Send Welcome Email,Отправить приветственное письмо,
Send alert if date matches this field's value,"Отправить уведомление, если дата соответствует значению этого поля",
Send alert if this field's value changes,"Отправить уведомление, если изменяется значение этого поля",
Send an email reminder in the morning,Отправить утром напоминание по электронной почте,
Send days before or after the reference date,Отправить за несколько дней до или после отчетной даты,
Send enquiries to this email address,Отправить запросы на этот адрес электронной почты,
Send me a copy,Отправить мне копию,
Send only if there is any data,"Отправить только если есть какие-либо данные,",
Send unsubscribe message in email,Отправить сообщение об отказе от подписки на электронную почту,
Sender,Отправитель,
Sender Email,Электронная почта отправителя,
Sent Read Receipt,Отправлять уведомление о прочтении,
Sent or Received,Отправлено или получено,
Sent/Received Email,Отправлено/Получено письмо,
Server IP,IP-адрес сервера,
Session Expired,Сеанс истек,
Session Expiry,Время сессии,
Session Expiry Mobile,Срок мобильной сессии,
Session Expiry in Hours e.g. 06:00,"Время сессии в часах, например 06:00",
Session Expiry must be in format {0},Время сессии должно быть в формате {0},
Session Start Failed,Сбой запуска сеанса,
Set Banner from Image,Установить баннер из изображения,
Set Chart,Построить график,
Set Filters,Установить фильтры,
Set New Password,Установить новый пароль,
Set Number of Backups,Установить количество резервных копий,
Set Only Once,Установлен только один раз,
Set Password,Установите пароль,
Set Permissions,Настройка разрешений,
Set Permissions on Document Types and Roles,Установить разрешения на типов документов и роли,
Set Property After Alert,Задать свойство после оповещения,
Set Quantity,Установите Количество,
Set Role For,Установить роль для,
Set User Permissions,Задание разрешений пользователя,
Set Value,Установить значение,
Set custom roles for page and report,Набор пользовательских ролей для страницы и отчета,
"Set default format, page size, print style etc.","Установить форму, размер страницы, стиль печати и т.д., используюмых по умолчанию",
Set non-standard precision for a Float or Currency field,Установите нестандартные точность для поплавка или валютной области,
Set numbering series for transactions.,Установить идентификаторы по номеру для транзакций,
Set up rules for user assignments.,Установите правила для пользовательских назначений.,
Setting this Address Template as default as there is no other default,"Установка этого Адрес шаблон по умолчанию, поскольку нет никакого другого умолчанию",
Setting up your system,Настройка системы,
Settings for About Us Page.,Настройки страницы О нас.,
Settings for Contact Us Page,Настройки страницы контактов,
Settings for Contact Us Page.,Настройки страницы контактов.,
Settings for OAuth Provider,Настройки для OAuth провайдера,
Settings for the About Us Page,Установки для страницы О нас,
Setup Auto Email,Настройка автоматической электронной почты,
Setup Complete,Завершение установки,
Setup Notifications based on various criteria.,Настройка уведомлений на основе различных критериев.,
Setup Reports to be emailed at regular intervals,Настройка регулярной отправки отчетов по электронной почте,
"Setup of top navigation bar, footer and logo.","Настройка верхней панели, навигации, нижнего колонтитула и логотипа.",
Share,Поделиться,
Share URL,Поделиться URL,
Share With,Поделиться с,
Share this document with,Поделиться этим документом с,
Share {0} with,Поделиться {0} с,
Shared,Общий,
Shared With,Совместно с,
Shared with everyone,Общий для всех,
Shared with {0},Общий с {0},
Shop,Магазин,
Short keyboard patterns are easy to guess,Короткие модели клавиатуры легко угадать,
Show Attachments,Показать прикрепленные файлы,
Show Calendar,Показать календарь,
Show Dashboard,Показать панель инструментов,
Show Full Error and Allow Reporting of Issues to the Developer,Показать полную ошибку и разрешить отправку отчетов разработчикам,
Show Line Breaks after Sections,Показать разрывы строк после разделов,
Show Permissions,Показать права доступа,
Show Preview Popup,Предварительный просмотр,
Show Relapses,Показать рецидивы,
Show Report,Показать отчет,
Show Section Headings,Показать заголовки разделов,
Show Sidebar,Показать боковую панель,
Show Title,Показать Название,
Show Totals,Показать итоги,
Show Weekends,Показать выходные,
Show all Versions,Показать все версии,
Show as Grid,Показать как сетку,
Show as cc,Показать в кубических сантиметрах,
Show failed jobs,Показывать не удалось рабочих мест,
Show in Module Section,Показать в секции модулей,
Show in filter,Показать в фильтре,
Show more details,Показать больше информации,
Show only errors,Показать только ошибки,
"Show title in browser window as ""Prefix - title""",Показать название в окне браузера как &quot;префикс - название&quot;,
Showing only Numeric fields from Report,Отображение только числовых полей из отчета,
Sidebar Items,Элементы боковой панели,
Sidebar Settings,Настройки боковой панели,
Sidebar and Comments,Боковая панель и комментарии,
Sign Up,Регистрация,
Sign Up is disabled,Регистрация отключена,
Signature,Подпись,
"Simple Python Expression, Example: Status in (""Closed"", ""Cancelled"")","Простое выражение Python, пример: Status in (""Closed"", ""Cancelled"")",
"Simple Python Expression, Example: status == 'Open' and type == 'Bug'","Простое выражение Python, пример: status == 'Open' and type == 'Bug'",
Simultaneous Sessions,Одновременные сеансы,
Single DocTypes cannot be customized.,Отдельные типы документов не могут быть настроены.,
Single Post (article).,Один пост(статья).,
Single Types have only one record no tables associated. Values are stored in tabSingles,"Холост Типы нет только одна запись не таблицы, связанные. Значения сохраняются в tabSingles",
Skip Authorization,Пропустить авторизацию,
Skip rows with errors,Пропустить строки с ошибками,
Slack Channel,Slack канал,
Slack Webhook Error,Slack Webhook ошибка,
Slack Webhook URL,Неверный URL веб-хостинга,
Slack Webhooks for internal integration,Slack Webhooks для внутренней интеграции,
Slideshow Items,Элементы слайд-шоу,
Slideshow Name,Название слайд-шоу,
Slideshow like display for the website,"Слайд-шоу, как дисплей для сайта",
Small Text,Маленьикий текст,
Smallest Currency Fraction Value,Минимальное дробное значение,
Smallest circulating fraction unit (coin). For e.g. 1 cent for USD and it should be entered as 0.01,"Минимальная разменная денежная единица (монета). Например, для доллара — 1 цент, и его нужно ввести как 0,01",
Snapshot View,Просмотр снимка,
Social,Сообщество,
Social Login Key,Ключ социального входа,
Social Login Provider,Социальный провайдер,
Social Logins,Социальные логины,
Socketio is not connected. Cannot upload,Socketio не подключен. Не удается загрузить,
Soft-Bounced,Софт-Возвращенные,
Some of the features might not work in your browser. Please update your browser to the latest version.,Некоторые из функций могут не работать в вашем браузере. Обновите браузер до последней версии.,
Something went wrong,Что-то пошло не так,
Something went wrong while generating dropbox access token. Please check error log for more details.,"Что-то пошло не так, создав токен доступа к Dropbox. Пожалуйста, проверьте журнал ошибок для получения более подробной информации.",
Sorry! I could not find what you were looking for.,"Извините! Я не мог найти то, что вы ищете.",
Sorry! Sharing with Website User is prohibited.,Извините! Поделиться с сайта Пользователю запрещается.,
Sorry! User should have complete access to their own record.,Извините! Пользователь должен иметь полный доступ к своей записи.,
Sorry! You are not permitted to view this page.,Извините! У вас нет разрешений для просмотра этой страницы.,
"Sorry, you're not authorized.","Извините, вы не авторизованы.",
Sort Field,Сортировать поле,
Sort Order,Порядок сортировки,
Sort field {0} must be a valid fieldname,Сортировка поля {0} должен быть действительным имя_поля,
Source Text,Исходный текст,
Spam,Спам,
Special Characters are not allowed,Спецсимволы не допустимы,
"Standard DocType cannot have default print format, use Customize Form","Стандартный DocType не может иметь формат печати по умолчанию, используйте Настроить форму",
Standard Print Format cannot be updated,Стандартный печатный бланк не может быть обновлен,
Standard Print Style cannot be changed. Please duplicate to edit.,Стандартный стиль печати не может быть изменен. Повторите попытку для редактирования.,
Standard Reports,Стандартные отчеты,
Standard Sidebar Menu,Стандартное боковое меню,
Standard roles cannot be disabled,Стандартные роли не могут быть отключены,
Standard roles cannot be renamed,Стандартные роли не могут быть переименованы,
Standings,Турнирная таблица,
Start Date Field,Поле начальной даты,
Start a conversation.,Начните разговор.,
Start entering data below this line,Начните вводить данные ниже этой линии,
Start new Format,Начать новую Формат,
Started,Начал,
Starting Frappe ...,Запуск Frappé ...,
Starts on,Начало,
States,Состояния,
"States for workflow (e.g. Draft, Approved, Cancelled).","Состояния рабочего-процесса (например: черновик, утверждён, отменён).",
Static Parameters,Статические параметры,
Stats based on last month's performance (from {0} to {1}),Статистика на основе результатов прошлого месяца (от {0} до {1}),
Stats based on last week's performance (from {0} to {1}),Статистика на основе результатов прошлой недели (от {0} до {1}),
Status: {0},Статус: {0},
Steps to verify your login,Шаги по проверке вашего логина,
Stores the JSON of last known versions of various installed apps. It is used to show release notes.,"Магазины JSON из последних известных версий различных установленных приложений. Он используется, чтобы показать примечания к выпуску.",
Straight rows of keys are easy to guess,Прямые ряды клавиш легко угадать,
Stripe Settings,Настройки Stripe,
Stripe payment gateway settings,Настройки платежного шлюза,
Style,Стиль,
Style Settings,Настройки стилей,
"Style represents the button color: Success - Green, Danger - Red, Inverse - Black, Primary - Dark Blue, Info - Light Blue, Warning - Orange","Стиль представляет цвет кнопки: Success - Green, Danger - Red, Inverse - Black, Primary - Dark Blue, Info - Light Blue, Warning - Orange",
Stylesheets for Print Formats,Таблицы стилей для печатных форматов,
"Sub-currency. For e.g. ""Cent""","Разменные единицы, например «цент»",
Sub-domain provided by erpnext.com,Суб-домен предоставляется erpnext.com,
Subdomain,Субдомен,
Subject Field,Поле темы,
Submit after importing,Отправить после импорта,
Submit an Issue,Отправить вопрос,
Submit this document to confirm,Подписать этот документ для подтверждения,
Submit {0} documents?,Отправить {0} документы?,
Submitting {0},Помещение {0},
Submitted Document cannot be converted back to draft. Transition row {0},Проведенный Документ не может быть преобразован обратно в проект. Переходная строка {0},
Submitting,Проведение,
Subscription Notification,Уведомление о подписке,
Subsidiary,Филиал,
Success Action,Успешное действие,
Success Message,Успех сообщение,
Success URL,Success URL,
Successful: {0} to {1},Успешное: {0} до {1},
Successfully Done,Успешно сделано,
Successfully Updated,Успешно обновлено,
Successfully updated translations,Успешно обновленные переводы,
Suggested Username: {0},Похожие Имя пользователя: {0},
Sum,Сумма,
Sum of {0},Сумма {0},
Support Email Address Not Specified,Адрес электронной почты поддержки не указан,
Suspend Sending,Приостановить Отправка,
Switch To Desk,Переключение на рабочий стол,
Symbol,Символ,
Sync,Синхронизация,
Sync on Migrate,Синхронизировать при переносе,
Syntax error in template,Синтаксическая ошибка в шаблоне,
System,Система,
System Page,Страница системы,
System Settings,Настройки системы,
System User,Пользователь системы,
System and Website Users,Пользователи сайта и системы,
Table,Таблица,
Table Field,Поле таблицы,
Table HTML,Таблица HTML,
Table MultiSelect,Таблица MultiSelect,
Table updated,Таблица обновлена,
Table {0} cannot be empty,Таблица {0} не может быть пустой,
Take Backup Now,Сделать резервную копию сейчас,
Take Photo,Сделать фото,
Team Members,Члены команды,
Team Members Heading,Члены команды Возглавлять,
Temporarily Disabled,Временно отключен,
Test Email Address,Проверить адрес электронной почты,
Text,Текст,
Text Align,Выравнивание текста,
Text Color,Цвет текста,
Text Content,Содержимое текста,
Text Editor,Редактор текста,
Text to be displayed for Link to Web Page if this form has a web page. Link route will be automatically generated based on `page_name` and `parent_website_route`,"Текст, отображаемый в течение ссылка на веб-страницу, если эта форма имеет веб-страницу. Маршрут Ссылка будет генерироваться автоматически на основе `page_name` и` parent_website_route`",
Thank you for your email,Спасибо за ваше письмо!,
Thank you for your interest in subscribing to our updates,Спасибо за ваш интерес и подписку,
Thank you for your message,Спасибо за ваше сообщение,
The CSV format is case sensitive,Формат CSV чувствителен к регистру,
The Condition '{0}' is invalid,Условие '{0}' является недействительным,
The First User: You,Первый пользователь: Вы,
"The application has been updated to a new version, please refresh this page","Приложение был обновлен до новой версии, пожалуйста, обновите эту страницу",
The attachments could not be correctly linked to the new document,Вложения не могут быть правильно связаны с новым документом,
The document could not be correctly assigned,Документ не может быть правильно назначен,
The document has been assigned to {0},Документ присвоен {0},
The first user will become the System Manager (you can change this later).,Первый пользователь станет менеджером системы (можно изменить это позже).,
The name that will appear in Google Calendar,"Имя, которое появится в Календаре Google",
The process for deletion of {0} data associated with {1} has been initiated.,"Процесс удаления данных {0}, связанных с {1}, был начат.",
The resource you are looking for is not available,"Ресурс, который вы ищете не доступен",
The system provides many pre-defined roles. You can add new roles to set finer permissions.,"Система предоставляет множество заранее определенных ролей. Вы можете добавить новые роли, чтобы установить более тонкие разрешения.",
The user from this field will be rewarded points,Пользователь из этого поля будет вознагражден баллами,
Theme,Тема,
Theme URL,URL темы,
There can be only one Fold in a form,В форме может быть только один Fold,
There is an error in your Address Template {0},Ошибка в вашем шаблоне адреса {0},
There is no data to be exported,Нет данных для экспорта,
There is some problem with the file url: {0},Существует некоторая проблема с файловой URL: {0},
There must be atleast one permission rule.,Там должно быть по крайней мере один правило разрешения.,
"There seems to be an issue with the server's braintree configuration. Don't worry, in case of failure, the amount will get refunded to your account.","Кажется, что проблема связана с конфигурацией Braintree сервера. Не беспокойтесь, в случае неудачи сумма будет возвращена на ваш счет.",
There should remain at least one System Manager,Должен быть хотя бы один менеджер системы,
There was an error saving filters,При сохранении фильтров произошла ошибка,
There were errors,Были ошибки,
There were errors while creating the document. Please try again.,"При создании документа возникли ошибки. Пожалуйста, попробуйте еще раз.",
There were errors while sending email. Please try again.,"При отправке электронной почты возникли ошибки. Пожалуйста, попробуйте ещё раз.",
"There were some errors setting the name, please contact the administrator","Были некоторые ошибки установки имени, пожалуйста, свяжитесь с администратором",
These values will be automatically updated in transactions and also will be useful to restrict permissions for this user on transactions containing these values.,"Эти значения будут автоматически обновляться в сделках, а также будет полезна для ограничения разрешений для этого пользователя по операциям, содержащих эти значения.",
Third Party Apps,Приложения сторонних разработчиков,
Third Party Authentication,Сторонняя аутентификация,
This Currency is disabled. Enable to use in transactions,Эта валюта отключена. Включить для использования в операциях,
This Kanban Board will be private,Эта канбан-доска будет личной,
This document cannot be reverted,Этот документ не может быть возвращен,
This document has been modified after the email was sent.,Этот документ был изменен после отправки электронного письма.,
This document has been reverted,Этот документ был возвращен,
This document is currently queued for execution. Please try again,"Этот документ в настоящее время в очередь на выполнение. Пожалуйста, попробуйте еще раз",
This email is autogenerated,Это письмо сгенерировано автоматически,
This email was sent to {0},Это письмо было отправлено на адрес {0},
This email was sent to {0} and copied to {1},Это письмо было отправлено на адрес {0} и его копии на {1},
This feature is brand new and still experimental,Эта функция является новой и до сих пор экспериментальная,
This field will appear only if the fieldname defined here has value OR the rules are true (examples):\nmyfield\neval:doc.myfield=='My Value'\neval:doc.age&gt;18,"Это поле появляется только в случае, если имя_поля определено здесь имеет значение или правила являются истинными (примеры): MyField Eval: doc.myfield == &#39;My Value&#39; Eval: doc.age&gt; 18",
This form does not have any input,Эта форма не имеет никакого поля для заполнения,
This form has been modified after you have loaded it,Эта форма была изменена после загрузки его,
This format is used if country specific format is not found,"Этот формат используется, когда формат конкретной страна не найден",
This goes above the slideshow.,Это идет над слайд-шоу.,
This is a background report. Please set the appropriate filters and then generate a new one.,"Это фоновый отчет. Пожалуйста, установите соответствующие фильтры, а затем сгенерируйте новый.",
This is a top-10 common password.,Это из топ-10 простых паролей.,
This is a top-100 common password.,Это из топ-100 простых паролей.,
This is a very common password.,Это очень простой пароль.,
This is an automatically generated reply,Это автоматически генерируется ответ,
This is similar to a commonly used password.,Это похоже на обычно используемый пароль.,
This is the template file generated with only the rows having some error. You should use this file for correction and import.,"Это файл шаблона, сгенерированный только строками с некоторой ошибкой. Вы должны использовать этот файл для исправления и импорта.",
This link has already been activated for verification.,Эта ссылка уже была активирована для проверки.,
This link is invalid or expired. Please make sure you have pasted correctly.,"Эта ссылка является недействительным или истек. Пожалуйста, убедитесь, что вы вставили правильно.",
This may get printed on multiple pages,Это будет напечатано на нескольких страницах,
This month,Этот месяц,
This query style is discontinued,Этот стиль запроса прекращен,
This report was generated on {0},Этот отчет был создан в {0},
This report was generated {0}.,Этот отчет был сгенерирован {0}.,
This request has not yet been approved by the user.,Этот запрос еще не был одобрен пользователем.,
This role update User Permissions for a user,Эта роль обновляет разрешения пользователя для пользователя,
This will log out {0} from all other devices,Это выведет {0} из всех других устройств,
This will permanently remove your data.,Это навсегда удалит ваши данные.,
Thumbnail URL,Миниатюра URL,
Time Interval,Интервал времени,
Time Series,Временные ряды,
Time Series Based On,Временные ряды на основе,
Time Zone,Часовой пояс,
Time Zones,Часовые пояса,
Time in seconds to retain QR code image on server. Min:<strong>240</strong>,"Время в секундах, чтобы сохранить изображение QR-кода на сервере. Мин.: <strong>240</strong>",
Timeline DocType,Хронология DocType,
Timeline Field,Хронология поля,
Timeline Links,Хронология ссылок,
Timeline Name,Хронология имени,
Timeline field must be a Link or Dynamic Link,Сроки поле должно быть Ссылка или Dynamic Link,
Timeline field must be a valid fieldname,Сроки поле должно быть действительным имя_поля,
Timeseries,Временные ряды,
Timestamp,Временная отметка,
Title Case,Название дела,
Title Field,Название поля,
Title Prefix,Название приставки,
Title field must be a valid fieldname,Название поля должно быть допустимым имя_поля,
To Date Field,Поле даты,
To Do,Список дел,
To User,Пользователю,
"To add dynamic subject, use jinja tags like\n\n<div><pre><code>New {{ doc.doctype }} #{{ doc.name }}</code></pre></div>","Чтобы добавить динамический объект, используйте теги jinja, например <div><pre> <code>New {{ doc.doctype }} #{{ doc.name }}</code> </pre> </div>",
"To add dynamic subject, use jinja tags like\n\n<div><pre><code>{{ doc.name }} Delivered</code></pre></div>","Чтобы добавить динамический объект, использовать теги, как дзиндзя <div><pre> <code>{{ doc.name }} Delivered</code> </pre> </div>",
To and CC,Получатели,
"To get the updated report, click on {0}.","Чтобы получить обновленный отчет, нажмите {0}.",
ToDo,Список задач,
Today,Cегодня,
Toggle Chart,Переключить диаграмму,
Toggle Charts,Переключить диаграммы,
Toggle Grid View,Просмотр сетки,
Toggle Sidebar,Переключить боковую панель,
Token,Токен,
Token is missing,Маркер отсутствует,
"Too many users signed up recently, so the registration is disabled. Please try back in an hour","Слишком много пользователей зарегистрировались в последнее время, так что регистрация отключена. Пожалуйста, повторите попытку через час",
Too many writes in one request. Please send smaller requests,"Слишком много пишет в одном запросе. Пожалуйста, пришлите меньшие запросы",
Top Bar Item,Пункт верхнего меню,
Top Bar Items,Пункты верхнего меню,
Top Performer,Лучший исполнитель,
Top Reviewer,Лучший рецензент,
Top {0},Топ {0},
Total Pages,Всего страниц,
Total Rows,Всего строк,
Total Subscribers,Всего Подписчики,
Total number of emails to sync in initial sync process ,Общее количество писем для синхронизации в начальном процессе синхронизации,
Totals Row,Итоговые строки,
Track Changes,Трекер изменений,
Track Email Status,Статус трекера электронной почты,
Track Field,Трекер поля,
Track Seen,Трекер посещение,
Track Views,Трекер просмотров,
"Track if your email has been opened by the recipient.\n<br>\nNote: If you're sending to multiple recipients, even if 1 recipient reads the email, it'll be considered ""Opened""","Отслеживайте, если ваш адрес электронной почты был открыт получателем. <br> Примечание. Если вы отправляете нескольким получателям, даже если один получатель читает электронное письмо, он будет считаться «открытым»,",
Track milestones for any document,Отслеживайте этапы для любого документа,
Transaction Hash,Сделка транзакций,
Transaction Log,Журнал транзакций,
Transaction Log Report,Отчет о транзакционном журнале,
Transition Rules,Переходные правила,
Transitions,Переходы,
Translatable,Переводимый,
Translate {0},Перевести {0},
Translated Text,Переведенный текст,
Translation,Перевод,
Translations,Переводы,
Trash,Мусор,
Tree,Дерево,
Trigger Method,Метод триггера,
Trigger Name,Имя триггера,
"Trigger on valid methods like ""before_insert"", ""after_update"", etc (will depend on the DocType selected)","Запуск по уважительным методы, такие как &quot;before_insert&quot;, &quot;after_update&quot;, и т.д. (будет зависеть от выбранного DocType)",
Try to avoid repeated words and characters,Старайтесь избегать повторяющихся слов и символов,
Try to use a longer keyboard pattern with more turns,Попробуйте использовать более длинный шаблон клавиатуры с большим количеством витков,
Two Factor Authentication,Двухфакторная аутентификация,
Two Factor Authentication method,Двухфакторный метод проверки подлинности,
Type something in the search box to search,Введите что-то в поле поиска для поиска,
Type:,Тип:,
UID,UID,
UIDNEXT,UIDNEXT,
UIDVALIDITY,UIDVALIDITY,
UNSEEN,НЕПРОЧИТАННЫЕ,
UPPER CASE,ВЕРХНИЙ РЕГИСТР,
"URIs for receiving authorization code once the user allows access, as well as failure responses. Typically a REST endpoint exposed by the Client App.\n<br>e.g. http://hostname//api/method/frappe.www.login.login_via_facebook","Идентификаторы URI для получения кода авторизации, как только пользователь разрешает доступ, а также ответы недостаточность. Как правило, конечная точка REST подвергается Клиентом App. <br> например, HTTP: //hostname//api/method/frappe.www.login.login_via_facebook",
URLs,URL-адрес,
Unable to find attachment {0},Не удалось найти вложение {0},
Unable to load camera.,Невозможно загрузить камеру.,
Unable to load: {0},Невозможно загрузить: {0},
Unable to open attached file. Did you export it as CSV?,"Невозможно открыть прикрепленный файл. Возможно, вы экспортировать его в CSV?",
Unable to read file format for {0},Не удалось прочитать формат файла для {0},
Unable to send emails at this time,Невозможно отправить письма в это время,
Unable to update event,Не удалось обновить событие,
Unable to write file format for {0},Невозможно записать формат файла для {0},
Unassign Condition,Отменить условие,
Under Development,В разработке,
Unfollow,Отписаться,
Unhandled Email,Необработанная электронная почта,
Unique,Уникальный,
Unknown Column: {0},Неизвестная колонка: {0},
Unknown User,Неизвестный пользователь,
"Unknown file encoding. Tried utf-8, windows-1250, windows-1252.","Неизвестная кодировка файла. Ни UTF-8, ни windows-1250, ни windows-1252.",
Unread,Не прочитано,
Unread Notification Sent,Не читать уведомления об отправке,
Unselect All,Снять все,
Unshared,Неразделенный,
Unsubscribe,Отказаться от подписки,
Unsubscribe Method,Метод отписки,
Unsubscribe Param,Отказаться от Param,
Unsupported File Format,Неподдерживаемый формат файла,
Unzip,Распаковать,
Unzipped {0} files,Распакованные файлы {0},
Unzipping files...,Распаковка файлов...,
Upcoming Events for Today,Предстоящие события на сегодня,
Update Field,Обновляемое поле,
Update Translations,Обновление переводов,
Update Value,Обновляемое значение,
Update many values at one time.,Обновить несколько значений одновременно.,
Update records,Обновить записи,
Updated,Обновлено,
Updated successfully,Успешно Обновлено,
Updated {0}: {1},Обновлено {0}: {1},
Updating,Обновление,
Updating {0},Обновление {0},
Upload Failed,Загрузка не удалась,
Uploaded To Dropbox,Загружено в Dropbox,
Use ASCII encoding for password,Использовать кодировку ASCII для пароля,
Use Different Email Login ID,Использовать другой идентификатор входа в электронную почту,
Use IMAP,Использование IMAP,
Use POST,Использовать POST,
Use SSL,Использовать SSL,
Use TLS,Использовать TLS,
"Use a few words, avoid common phrases.","Используйте несколько слов, избегайте общих фраз.",
Use of sub-query or function is restricted,Использование подзапроса или функции ограничено,
Use socketio to upload file,Использовать socketio для загрузки файла,
Use this fieldname to generate title,Используйте этот имя_поля генерировать название,
User '{0}' already has the role '{1}',Пользователь &#39;{0}&#39; уже имеет роль &#39;{1}&#39;,
User Cannot Create,Пользователь не может создавать,
User Cannot Search,Пользователь не может искать,
User Defaults,По умолчанию пользователя,
User Email,Электронная почта пользователя,
User Emails,Письма пользователя,
User Field,Поле пользователя,
User ID of a Blogger,ID пользователя-блоггера,
User Image,Изображение пользователя,
User Name,Имя пользователя,
User Permission,Разрешения пользователя,
User Permissions,Разрешения пользователей,
User Permissions are used to limit users to specific records.,Пользовательские разрешения используются для ограничения пользователей конкретными записями.,
User Permissions created sucessfully,Пользовательские разрешения созданы успешно,
User Roles,Роли пользователей,
User Social Login,Пользовательский социальный вход,
User Tags,Метки пользователя,
User Type,Тип пользователя,
User can login using Email id or Mobile number,"Пользователь может войти в систему, используя идентификатор электронной почты или номер мобильного телефона",
User can login using Email id or User Name,Пользователь может войти в систему с использованием Email-идентификатора или имени пользователя,
User editable form on Website.,Пользователь редактируемый вид на веб-сайте.,
User is mandatory for Share,Пользователь является обязательным для Поделиться,
User not allowed to delete {0}: {1},Пользователь не имеет права удалить {0}: {1},
User permission already exists,Пользовательское разрешение уже существует,
User permissions should not apply for this Link,Права пользователя не должны применяться для данного канала,
User {0} cannot be deleted,Пользователь {0} не может быть удален,
User {0} cannot be disabled,Пользователь {0} не может быть отключен,
User {0} cannot be renamed,Пользователь {0} не может быть переименован,
User {0} does not have access to this document,Пользователь {0} не имеет доступа к этому документу,
User {0} does not have doctype access via role permission for document {1},Пользователь {0} не имеет доступа к типу документа через разрешение роли для документа {1},
Username,Имя пользователя,
Username {0} already exists,Имя пользователя {0} уже существует,
Users with role {0}:,Пользователи с ролью {0}:,
Uses the Email Address Name mentioned in this Account as the Sender's Name for all emails sent using this Account.,"Использует имя адреса электронной почты, указанное в этой учетной записи, в качестве имени отправителя для всех электронных писем, отправленных с использованием этой учетной записи.",
Uses the Email Address mentioned in this Account as the Sender for all emails sent using this Account. ,"Использует адрес электронной почты, указанный в этом аккаунте в качестве отправителя для всех писем, отправляемых с использованием этой учетной записи.",
Valid,Действительный,
Valid Login id required.,Требуется действительный ID логин.,
Valid email and name required,Требуются действительные email и имя,
Value Based On,Значение на основе,
Value Change,Стоимость Изменение,
Value Changed,Значение изменено,
Value To Be Set,"Значение, которое должно быть установлено",
Value cannot be changed for {0},Значение не может быть изменено для {0},
Value for a check field can be either 0 or 1,"Значение для поля проверки может быть либо 0, либо 1",
Value for {0} cannot be a list,Значение {0} не может быть списком,
Value missing for,Нет значения для,
Value too big,Слишком большое значение,
Values Changed,Значения изменено,
Verfication Code,Код проверки,
Verification Link,Ссылка для проверки,
Verification code has been sent to your registered email address.,Код подтверждения отправлен на ваш адрес электронной почты регистрации.,
Verify,Проверить,
Verify Password,Подтвердите пароль,
Verifying...,Проверка...,
Version,Версия,
Version Updated,Версия обновлена,
View All,Посмотреть все,
View Comment,Посмотреть комментарий,
View List,Просмотр списка,
View Log,Посмотреть журнал,
View Permitted Documents,Просмотр разрешенных документов,
View Properties (via Customize Form),Просмотр свойств (через Настроить форме),
View Settings,Просмотр параметров,
View Website,Посмотреть сайт,
View document,Просмотр документа,
View report in your browser,Просмотр отчета в вашем браузере,
View this in your browser,Просмотреть это в вашем браузере,
View {0},Просмотреть {0},
Viewed By,Просмотрено,
Visit,Посетите нас по адресу,
Visitor,Посетитель,
We have received a request for deletion of {0} data associated with: {1},"Мы получили запрос на удаление {0} данных, связанных с: {1}",
We have received a request from you to download your {0} data associated with: {1},"Мы получили от вас запрос на загрузку данных {0}, связанных с: {1}",
Web Form,Веб форма,
Web Form Field,Поле веб формы,
Web Form Fields,Поля веб формы,
Web Page,Веб-страница,
Web Page Link Text,Текст ссылки веб-страницы,
Web Site,Веб-сайт,
Web View,Web View,
Webhook,Webhook,
Webhook Data,Данные Webhook,
Webhook Header,Заголовок Webhook,
Webhook Headers,Заголовки Webhook,
Webhook Request,Запрос на оповещения,
Webhook URL,URL веб-ссылки,
Webhooks calling API requests into web apps,"Webhooks, вызывающие запросы API в веб-приложениях",
Website Meta Tag,Метатег веб-сайта,
Website Route Meta,Сайт Маршрут Мета,
Website Route Redirect,Перенаправление маршрута веб-сайта,
Website Script,Сайт скрипта,
Website Sidebar,Боковая панель Веб-сайт,
Website Sidebar Item,Сайт Sidebar товара,
Website Slideshow,Сайт Слайд-шоу,
Website Slideshow Item,Сайт Слайд-шоу Пункт,
Website Theme,Тема сайта,
Website Theme Image,Изображение темы сайта,
Website Theme Image Link,Ссылка на изображение темы сайта,
Website User,Пользователь сайта,
Welcome Message,Приветственное сообщение,
"When you Amend a document after Cancel and save it, it will get a new number that is a version of the old number.","Когда вы Измените (Amend) документ после Отмены (Cancel) и сохраните его, он получит новый номер, который является версией старого номера.",
Width,Ширина,
Widths can be set in px or %.,Ширина может быть установлен в PX или%.,
Will be used in url (usually first name).,Будет использоваться в URL (обычно имя).,
Will be your login ID,Будет ли ваш идентификатор входа в систему,
Will only be shown if section headings are enabled,"Будет показано, только если заголовки разделов включены",
With Letter head,С буквенным заголовком,
With Letterhead,На фирменном бланке,
Workflow Action,Действия рабочего процесса,
Workflow Action Master,Мастер действий рабочего процесса,
Workflow Action Name,Название действия рабочего процесса,
Workflow Document State,Состояние документа рабочего процесса,
Workflow Name,Название рабочего процесса,
Workflow State,Состояние рабочего процесса,
Workflow State Field,Состояние поля рабочего процесса,
Workflow State not set,Состояние рабочего процесса не установлено,
Workflow Transition,Переход рабочего процесса,
Workflow state represents the current state of a document.,Состояние рабочего процесса представлено текущим состоянием документа.,
Write,Написать,
Wrong fieldname <b>{0}</b> in add_fetch configuration of custom script,Неверное имя поля <b>{0}</b> в конфигурации add_fetch пользовательского скрипта,
X Axis Field,Поле оси X,
Y Axis Fields,Поля оси Y,
Yandex.Mail,Яндекс.Почта,
Yesterday,Вчера,
You are connected to internet.,Вы подключены к Интернету.,
You are not allowed to create columns,Вы не можете создавать колонки,
You are not allowed to delete a standard Website Theme,Вы не можете удалить стандартную тему сайта,
You are not allowed to print this document,Вы не можете распечатать этот документ,
You are not allowed to print this report,У вас нет прав для печати этого отчета,
You are not allowed to send emails related to this document,"Вы не можете отправлять письма, связанные с этим документом",
You are not allowed to update this Web Form Document,Вы не можете обновить эту веб-форму документа,
You are not connected to Internet. Retry after sometime.,Вы не подключены к интернету. Повторите попытку через некоторое время.,
You are not permitted to access this page.,Вам не разрешен доступ к этой странице.,
You are not permitted to view the newsletter.,У Вас нет прав для просмотра данной новостной ленты,
You are now following this document. You will receive daily updates via email. You can change this in User Settings.,Вы подписаны на обновления данного документа. Вы будете получать ежедневные обновления по электронной почте. Вы можете изменить это в настройках пользователя.,
You can add dynamic properties from the document by using Jinja templating.,Вы можете добавить динамические свойства из документа с помощью шаблонов Jinja.,
You can also copy-paste this ,Вы также можете скопировать и вставить это ,
"You can change Submitted documents by cancelling them and then, amending them.","Вы можете изменить утвержденные документы, отменив их, а затем отредактировав.",
You can find things by asking 'find orange in customers',Можно искать что-либо написав «найти апельсин у клиентов»,
You can only upload upto 5000 records in one go. (may be less in some cases),"Вы можете загружать одновременно до 5000 записей. (Возможно меньше, в некоторых случаях)",
You can use Customize Form to set levels on fields.,Вы можете использовать Настройку формы (Customize Form) для установки уровней для полей.,
You can use wildcard %,Вы можете использовать подстановочные %,
You can't set 'Options' for field {0},Нельзя включить «Опции» для поля {0},
You can't set 'Translatable' for field {0},Вы не можете установить «Переводимый» для поля {0},
You cannot give review points to yourself,Вы не можете не можете начислять себе баллы обзора,
You cannot unset 'Read Only' for field {0},Нельзя отменить «только чтение» для поля {0},
You do not have enough permissions to access this resource. Please contact your manager to get access.,"У вас нет достаточных прав для доступа к этому ресурсу. Пожалуйста, обратитесь к своему менеджеру, чтобы получить доступ.",
You do not have enough permissions to complete the action,У Вас нет достаточных прав для завершения действия,
You do not have enough points,Вам не хватает баллов,
You do not have enough review points,Вам не хватает баллов обзора,
You don't have access to Report: {0},Вы не имеете доступа к отчету: {0},
You don't have any messages yet.,У вас пока нет сообщений.,
You don't have permission to access this file,У Вас нет разрешения на доступ к этому файлу,
You don't have permission to get a report on: {0},У Вас нет разрешения на получение отчета о: {0},
You don't have the permissions to access this document,У Вас нет разрешения на доступ к этому документу,
You gained {0} point,Вы набрали {0} балл,
You gained {0} points,Вы набрали {0} баллов,
You have a new message from: ,У вас есть новое сообщение от:,
You have been successfully logged out,Вы успешно вышли,
You have unsaved changes in this form. Please save before you continue.,"Есть несохранённые изменения в этой форме. Пожалуйста, сохраните прежде чем продолжить.",
You must login to submit this form,Для подтверждения этой формы необходимо авторизоваться,
You need to be in developer mode to edit a Standard Web Form,Вы должны быть в режиме разработчика для изменения стандартной веб-формы,
You need to be logged in and have System Manager Role to be able to access backups.,"Вы должны войти в систему (и иметь роль менеджера системы), чтобы иметь доступ к резервным копиям.",
You need to be logged in to access this {0}.,"Вы должны войти, чтобы получить доступ к {0}.",
"You need to have ""Share"" permission","Вы должны иметь разрешение ""Поделиться""",
You need write permission to rename,"Вам нужно разрешение на запись, чтобы переименовать",
You selected Draft or Cancelled documents,Вы выбрали черновик или отмененные документы,
You unfollowed this document,Вы отписались от этого документа,
Your Country,Ваша страна,
Your Language,Ваш язык,
Your Name,Ваше имя,
Your account has been locked and will resume after {0} seconds,Ваша учетная запись заблокирована и будет доступна через {0} секунд,
Your connection request to Google Calendar was successfully accepted,Ваш запрос на подключение к Календарю Google был успешно принят,
Your information has been submitted,Ваша информация была представлена,
Your login id is,Ваш ID для авторизации,
Your organization name and address for the email footer.,Название вашей организации и адрес для нижнего колонтитула электронной почты,
Your payment has been successfully registered.,Ваш платеж успешно зарегистрирован.,
Your payment has failed.,Ваш платёж не удался.,
Your payment is cancelled.,Ваш платёж отменён.,
Your payment was successfully accepted,Ваш платёж был успешно принят,
"Your query has been received. We will reply back shortly. If you have any additional information, please reply to this mail.","Ваш запрос получен. Мы ответим в ближайшее время. Если у вас есть какая-либо дополнительная информация, пожалуйста, ответьте на это письмо.",
"Your session has expired, please login again to continue.","Ваша сессия истекла, пожалуйста, войдите снова, чтобы продолжить.",
Zero,Ноль,
Zero means send records updated at anytime,При нуле обновленные записи отправляются в любое время,
adjust,настроить,
after_insert,после_вставки,
align-center,выровнять-по-центру,
align-justify,выровнять-по-ширине,
align-left,выровнять-по-левой-стороне,
align-right,выровнять-по-правой-стороне,
ap-northeast-1,ар-северо-восток-1,
ap-northeast-2,ар-северо-восток-2,
ap-northeast-3,ар-северо-восток-3,
ap-south-1,ар-юго-1,
ap-southeast-1,ар-юго-восток-1,
ap-southeast-2,ар-юго-восток-2,
arrow-down,стрелка-вниз,
arrow-left,стрелка-налево,
arrow-right,стрелка-направо,
arrow-up,стрелка-вверх,
asterisk,звёздочка,
backward,назад,
ban-circle,бан-кружок,
bell,колокольчик,
bookmark,закладка,
briefcase,портфель,
bullhorn,рупор,
ca-central-1,ча-центрально-1,
camera,Камера,
cancelled this document,отменил этот документ,
changed value of {0},изменил значение {0},
changed values for {0},измененные значения для {0},
chevron-down,шеврон-вниз,
chevron-left,шеврон-влево,
chevron-right,шеврон-право,
chevron-up,шеврон-вверх,
circle-arrow-down,круг-со-стрелкой-вниз,
circle-arrow-left,круг-со-стрелкой-налево,
circle-arrow-right,круг-со-стрелкой-направо,
circle-arrow-up,круг-со-стрелкой-вверх,
cn-north-1,сп-северо-1,
cn-northwest-1,сп-северо-запад-1,
cog,зубец,
darkgrey,темно-серый,
dd-mm-yyyy,дд-мм-гггг,
dd.mm.yyyy,дд.мм.гггг,
dd/mm/yyyy,дд/мм/гггг,
"document type..., e.g. customer","тип документа..., например, клиент",
domain name,доменное имя,
download-alt,скачать-альт,
"e.g. ""Support"", ""Sales"", ""Jerry Yang""","например ""Поддержка"",""Продажи"",""Джерри Янг""",
e.g. (55 + 434) / 4 or =Math.sin(Math.PI/2)...,"например, (55 + 434) / 4 или =Math.sin (Math.PI / 2)...",
e.g. pop.gmail.com / imap.gmail.com,например pop.gmail.com / imap.gmail.com,
e.g. <EMAIL>. All replies will come to this inbox.,например <EMAIL>. Все ответы будут приходить на этот почтовый ящик.,
e.g. smtp.gmail.com,например smtp.gmail.com,
e.g.:,например:,
eject,выбрасывать,
envelope,конверт,
eu-central-1,ес-центрально-1,
eu-north-1,ес-северо-1,
eu-west-1,ес-запад-1,
eu-west-2,ес-запад-2,
eu-west-3,ес-запад-3,
exclamation-sign,восклицательный знак-,
eye-close,глаз близко,
eye-open,глаз открыт,
facetime-video,FaceTime-видео,
fairlogin,fairlogin,
fast-backward,быстро назад,
fast-forward,быстрой перемотки вперед,
film,пленка,
fire,огонь,
folder-close,папка-закрыть,
folder-open,папка-открыть,
fullscreen,полноэкранный,
gained by {0} via automatic rule {1},получено {0} через автоматическое правило {1},
gained {0} points,набрал {0} очков,
gave {0} points,дал {0} баллов,
gift,подарок,
glass,стекло,
globe,глобус,
hand-down,рука-вниз,
hand-left,рука-влево,
hand-right,рука-вправо,
hand-up,рука-вверх,
headphones,наушники,
heart,сердце,
hub,хаб,
indent-left,отступ левого,
indent-right,отступ правом,
info-sign,Информация-знак,
italic,italic,
<EMAIL>,<EMAIL>,
just now,прямо сейчас,
leaf,лист,
lightblue,светло-синий,
list-alt,Список-альт,
magnet,магнит,
map-marker,Карта-маркер,
merged {0} into {1},объединены {0} в {1},
minus,минус,
minus-sign,минус-знак,
mm-dd-yyyy,мм-дд-гггг,
mm/dd/yyyy,мм/дд/гггг,
module name...,Модуль имя...,
new type of document,новый тип документа,
no failed attempts,нет неудачных попыток,
none of,ни один из,
ok,ok,
ok-circle,ok-circle,
ok-sign,в порядке-знак,
on_cancel,on_cancel,
on_change,по изменению,
on_submit,on_submit,
on_trash,on_trash,
on_update,on_update,
on_update_after_submit,on_update_after_submit,
only.,только.,
or,или,
pause,Пауза,
pencil,карандаш,
picture,картинка,
plane,самолет,
play,играть,
play-circle,играть-круг,
plus,плюс,
plus-sign,знак плюс,
qrcode,QR-код,
query-report,запрос-отчет,
question-sign,Вопрос-знак,
remove-circle,удалить-круг,
remove-sign,удалить-знак,
removed,удален,
renamed from {0} to {1},переименован из {0} в {1},
repeat,повторение,
resize-full,изменить размер-полный,
resize-horizontal,изменить размер горизонтальной,
resize-small,изменить размер-маленький,
resize-vertical,изменить размер вертикального,
restored {0} as {1},восстановлено {0} как {1},
retweet,ретвит,
road,путь,
sa-east-1,са-восток-1,
screenshot,Скриншот,
share-alt,Доля-альт,
shopping-cart,корзина,
show,показать,
signal,сигнал,
star,звезда,
star-empty,звезды пусто,
step-backward,шаг назад,
step-forward,шаг вперед,
submitted this document,представил этот документ,
text in document type,Текст в документе типа,
text-height,Текст-высота,
text-width,Текст ширины,
th,й,
th-large,й по величине,
th-list,го-лист,
thumbs-down,несогласие,
thumbs-up,палец вверх,
tint,оттенок,
toggle Tag,toggle Tag,
updated to {0},обновлен до {0},
us-east-1,мы-восток-1,
us-east-2,мы-восток-2,
us-west-1,мы-запад-1,
us-west-2,мы-запад-2,
use % as wildcard,используйте расширение%,
values separated by commas,"значения, разделенные запятыми",
via automatic rule {0} on {1},через автоматическое правило {0} в {1},
viewed,Количество просмотров,
volume-down,Объем вниз,
volume-off,Объем-офф,
volume-up,Объем-до,
warning-sign,знак-предупреждения,
wrench,гаечный ключ,
yyyy-mm-dd,гггг-мм-дд,
zoom-in,приблизить,
zoom-out,отдалить,
{0} Calendar,{0} Календарь,
{0} Chart,{0} Диаграмма,
{0} Dashboard,{0} Показатели,
{0} List,{0} Список,
{0} Modules,{0} Модули,
{0} Report,{0} Отчет,
{0} Settings not found,Параметры {0} не найдены,
{0} Tree,{0} Дерево,
{0} added,{0} добавлено,
{0} already exists. Select another name,{0} уже существует. Выберите другое имя,
{0} already unsubscribed,{0} уже отписан,
{0} already unsubscribed for {1} {2},{0} уже отписан от {1} {2},
{0} and {1},{0} и {1},
{0} appreciated on {1},{0} оценили {1},
{0} appreciated your work on {1} with {2} point,{0} оценил вашу работу по {1} с {2} баллом,
{0} appreciated your work on {1} with {2} points,{0} оценил вашу работу по {1} с {2} баллами,
{0} appreciated {1},{0} признателен {1},
{0} appreciation point for {1} {2},{0} благодарность за {1} {2},
{0} appreciation points for {1} {2},{0} баллы за {1} {2},
{0} assigned {1}: {2},{0} назначил(а) {1}: {2},
{0} cannot be set for Single types,{0} не может быть установлена для отдельных видов,
{0} comments,{0} комментариев,
{0} created successfully,{0} создано успешно,
{0} criticism point for {1} {2},{0} критическое замечание для {1} {2},
{0} criticism points for {1} {2},{0} критических баллов за {1} {2},
{0} criticized on {1},{0} подвергнут критике {1},
{0} criticized your work on {1} with {2} point,{0} подверг критике вашу работу с {1} баллом {2},
{0} criticized your work on {1} with {2} points,{0} подверг критике вашу работу {1} с {2} баллами,
{0} criticized {1},{0} раскритиковано {1},
{0} days ago,{0} дней назад,
{0} does not exist in row {1},{0} не существует в строке {1},
"{0} field cannot be set as unique in {1}, as there are non-unique existing values","Поле {0} не может быть установлено как уникальное в {1}, так как не являются уникальными существующие значения",
{0} has already assigned default value for {1}.,{0} уже назначил значение по умолчанию для {1}.,
{0} has been successfully added to the Email Group.,{0} был успешно добавлен в эту группу адресов электронной почты.,
{0} has left the conversation in {1} {2},{0} оставил разговор в {1} {2},
{0} hours ago,{0} часов назад,
{0} in row {1} cannot have both URL and child items,{0} в строке {1} не может содержаться URL и дочерние продукты,
{0} is a mandatory field,{0} является обязательным полем,
{0} is an invalid email address in 'Recipients',{0} - неверный адрес электронной почты в поле «Получатели»,
{0} is not a raw printing format.,{0} не является необработанным форматом печати.,
{0} is not a valid Email Address,{0} — недопустимый адрес электронной почты,
{0} is not a valid Workflow State. Please update your Workflow and try again.,{0} не является допустимым состоянием рабочего процесса. Обновите свой рабочий процесс и повторите попытку.,
{0} is now default print format for {1} doctype,{0} — теперь формат печати по умолчанию для {1} doctype,
{0} is saved,{0} сохранено,
{0} items selected,{0} элементов выбрано,
{0} logged in,{0} авторизирован,
{0} logged out: {1},{0} вышел: {1},
{0} minutes ago,{0} минут назад,
{0} months ago,{0} месяца назад,
{0} must be one of {1},{0} должен быть одним из {1},
{0} must be set first,{0} должен быть установлен первым,
{0} must be unique,{0} должен быть уникальным,
{0} not a valid State,{0} не является допустимым состоянием,
{0} not allowed to be renamed,{0} не могут быть переименованы,
{0} not found,{0} не найден,
{0} of {1},{0} из {1},
{0} or {1},{0} или {1},
{0} record deleted,{0} запись удалена,
{0} records deleted,{0} записей удалено,
{0} reverted your point on {1},{0} вернул ваш балл на {1},
{0} reverted your points on {1},{0} вернул ваши баллы на {1},
{0} reverted {1},{0} вернул {1},
{0} room must have atmost one user.,{0} номер должен иметь самого одного пользователя.,
{0} rows for {1},{0} строк для {1},
{0} saved successfully,{0} успешно сохранен,
{0} self assigned this task: {1},{0} самостоятельно назначил эту задачу: {1},
{0} shared this document with everyone,{0} поделился этим документом со всеми,
{0} shared this document with {1},{0} поделился этим документом  с {1},
{0} subscribers added,{0} подписчики добавлены,
{0} to stop receiving emails of this type,"{0}, чтобы прекратить получать электронные письма этого типа",
{0} to {1},{0} - {1},
{0} un-shared this document with {1},{0} закрыл этот документ от {1},
{0} updated,{0} обновлено,
{0} weeks ago,{0} недель назад,
{0} {1} added,{0} {1} добавлено,
{0} {1} already exists,{0} {1} уже существует,
"{0} {1} cannot be ""{2}"". It should be one of ""{3}""","{0} {1} не может быть ""{2}"". Это должно быть одним из ""{3}""",
{0} {1} cannot be a leaf node as it has children,"{0} {1} не может быть концевым узлом, так как он имеет потомков",
"{0} {1} does not exist, select a new target to merge","{0} {1} не существует, выберите новую цель для объединения",
{0} {1} not found,{0} {1} не найден,
{0} {1} to {2},{0} {1} до {2},
"{0}, Row {1}","{0}, Строка {1}",
"{0}: '{1}' ({3}) will get truncated, as max characters allowed is {2}","{0}: «{1}» ({3}) будет обрезано, т.к. допустимое максимальное значение {2} символов",
{0}: Cannot set Amend without Cancel,{0}: Не удается установить Изменить без Отменить,
{0}: Cannot set Assign Amend if not Submittable,"{0}: Не удается установить Назначить изменение, если не подлежит проведению",
{0}: Cannot set Assign Submit if not Submittable,"{0}: Не удается установить Назначить проведение, если не подлежит проведению",
{0}: Cannot set Cancel without Submit,{0}: Не удается установить Отмена без отправки,
{0}: Cannot set Import without Create,{0}: Не удается установить Импорт без Создать,
"{0}: Cannot set Submit, Cancel, Amend without Write","{0}: Не удается выполнить Подписать, Отменить, Изменить без Записать",
{0}: Cannot set import as {1} is not importable,{0}: Не удается установить импорт как {1} не является ввозу,
{0}: No basic permissions set,{0}: Не установлен базовый набор разрешений,
"{0}: Only one rule allowed with the same Role, Level and {1}","{0}: только одно правило допускается для той же роли, уровня и {1}",
{0}: Permission at level 0 must be set before higher levels are set,{0}:  Резрешение уровня 0 должно быть задано до установки более высоких уровней,
{0}: {1} in {2},{0}: {1} в {2},
{0}: {1} is set to state {2},{0}: {1} установлено состояние {2},
{app_title},{app_title},
{{{0}}} is not a valid fieldname pattern. It should be {{field_name}}.,{{{0}}} недопустимый шаблон имени поля. Должно быть {{field_name}}.,
Communication Link,Связь коммуникации,
Force User to Reset Password,Заставить пользователя сбросить пароль,
In Days,В днях,
Last Password Reset Date,Дата последнего сброса пароля,
The password of your account has expired.,Срок действия пароля вашей учетной записи истек.,
Workflow State transition not allowed from {0} to {1},Переход состояния рабочего процесса не разрешен из {0} в {1},
{0} must be after {1},{0} должно быть после {1},
{0}: Field '{1}' cannot be set as Unique as it has non-unique values,"{0}: поле &#39;{1}&#39; нельзя установить как уникальное, поскольку оно имеет неуникальные значения",
{0}: Field {1} in row {2} cannot be hidden and mandatory without default,{0}: поле {1} в строке {2} не может быть скрыто и является обязательным без значения по умолчанию,
{0}: Field {1} of type {2} cannot be mandatory,{0}: поле {1} типа {2} не может быть обязательным,
{0}: Fieldname {1} appears multiple times in rows {2},{0}: имя поля {1} появляется несколько раз в строках {2},
{0}: Fieldtype {1} for {2} cannot be unique,{0}: тип поля {1} для {2} не может быть уникальным,
{0}: Options must be a valid DocType for field {1} in row {2},{0}: параметры должны быть допустимым типом документа для поля {1} в строке {2},
{0}: Options required for Link or Table type field {1} in row {2},"{0}: параметры, необходимые для поля типа ссылки или таблицы {1} в строке {2}",
{0}: Options {1} must be the same as doctype name {2} for the field {3},{0}: параметры {1} должны совпадать с именем типа документа {2} для поля {3},
{0}:Fieldtype {1} for {2} cannot be indexed,{0}: тип поля {1} для {2} не может быть проиндексирован,
Make {0},Сделать {0},
A user who posts blogs.,"Пользователь, который публикует блоги.",
Applying: {0},Применяется: {0},
Fieldname {0} is restricted,Имя поля {0} ограничено,
Is Optional State,Необязательное состояние,
No values to show,Нет значений для отображения,
View Ref,Просмотр Ref,
Workflow Action is not created for optional states,Действие рабочего процесса не создано для необязательных состояний,
{0} values selected,Выбрано {0} значений,
"""amended_from"" field must be present to do an amendment.",Поле &quot;amend_from&quot; должно присутствовать для внесения изменений.,
(Mandatory),(Обязательное),
1 Google Calendar Event synced.,1 Google Calendar Событие синхронизировано.,
1 record will be exported,1 запись будет экспортирована,
1 week ago,1 неделю назад,
5 Records,5 записей,
A recurring {0} {1} has been created for you via Auto Repeat {2}.,Повторяющиеся {0} {1} были созданы для вас с помощью автоматического повторения {2}.,
API,API,
API Method,Метод API,
About {0} minute remaining,Около {0} минут осталось,
About {0} minutes remaining,Около {0} минут осталось,
About {0} seconds remaining,Осталось {0} секунд,
Access Log,Журнал доступа,
Access not allowed from this IP Address,Доступ с этого IP-адреса запрещен,
Action Type,Тип действия,
Activity Log by ,Журнал активности по ,
Add Fields,Добавить поля,
Administration,Администрирование,
After Cancel,После отмены,
After Delete,После удаления,
After Save,После сохранения,
After Save (Submitted Document),После сохранения (отправленный документ),
After Submit,После отправки,
Aggregate Function Based On,"Агрегатная функция, основанная на",
Aggregate Function field is required to create a dashboard chart,Поле Aggregate Function необходимо для создания диаграммы панели мониторинга.,
All Records,Все записи,
Allot Points To Assigned Users,Выделить баллы назначенным пользователям,
Allow Auto Repeat,Разрешить автоматическое повторение,
Allow Google Calendar Access,Разрешить доступ к календарю Google,
Allow Google Contacts Access,Разрешить доступ к контактам Google,
Allow Google Drive Access,Разрешить доступ Google Drive,
Allow Guest,Разрешить гостя,
Allow Guests to Upload Files,Разрешить гостям загружать файлы,
Also adding the status dependency field {0},Также добавляем поле зависимости статуса {0},
An error occurred while setting Session Defaults,Произошла ошибка при настройке параметров сеанса по умолчанию,
Annual,Ежегодный,
Append Emails to Sent Folder,Добавить электронные письма в отправленную папку,
Apply Assignment Rule,Применить правило назначения,
Apply Only Once,Применить только один раз,
Apply this rule only once per document,Применять это правило только один раз для каждого документа,
Approval Required,Требуется подтверждение,
Approved,Утверждено,
Are you sure you want to delete all rows?,"Вы уверены, что хотите удалить все строки?",
Are you sure you want to delete this post?,"Вы уверены, что хотите удалить эту запись?",
Are you sure you want to merge {0} with {1}?,"Вы уверены, что хотите объединить {0} с {1}?",
Assignment Day {0} has been repeated.,День назначения {0} был повторен.,
Assignment Days,Дни назначения,
Assignment Rule Day,День Правил Назначения,
Assignments,Назначения,
Attach a web link,Прикрепите веб-ссылку,
Authorize Google Calendar Access,Авторизовать доступ к календарю Google,
Authorize Google Contacts Access,Авторизовать доступ к контактам Google,
Authorize Google Drive Access,Авторизовать Google Drive Access,
Auto Repeat Document Creation Failed,Ошибка автоматического создания документа,
Auto Repeat Document Creation Failure,Ошибка автоматического создания документа,
Auto Repeat created for this document,Автоповтор создан для этого документа,
Auto Repeat failed for {0},Не удалось выполнить автоматическое повторение для {0},
Automatic Linking can be activated only for one Email Account.,Автоматическое связывание может быть активировано только для одной учетной записи электронной почты.,
Automatic Linking can be activated only if Incoming is enabled.,"Автоматическое связывание может быть активировано, только если включен входящий.",
Automatically generates recurring documents.,Автоматически генерирует повторяющиеся документы.,
Backing up to Google Drive.,Резервное копирование на Google Drive.,
Backup Folder ID,Идентификатор резервной папки,
Backup Folder Name,Имя папки резервного копирования,
Before Cancel,Перед отменой,
Before Delete,Перед удалением,
Before Insert,Перед вставкой,
Before Save,Перед сохранением,
Before Save (Submitted Document),Перед сохранением (отправленный документ),
Before Submit,Перед отправкой,
Blank Template,Пустой шаблон,
Callback URL,URL обратного вызова,
Cancel All Documents,Отменить все документы,
Cancelling documents,Отмена документов,
Cannot match column {0} with any field,Невозможно сопоставить столбец {0} ни с одним полем,
Change,Изменение,
Change User,Сменить пользователя,
Check the Error Log for more information: {0},Проверьте журнал ошибок для получения дополнительной информации: {0},
Clear Cache and Reload,Очистить кэш и перезагрузить,
Clear Filters,Очистить фильтры,
Click on <b>Authorize Google Drive Access</b> to authorize Google Drive Access.,"Нажмите <b>Авторизовать Google Drive Access,</b> чтобы авторизовать Google Drive Access.",
Click on a file to select it.,"Нажмите на файл, чтобы выбрать его.",
Click on the link below to approve the request,"Нажмите на ссылку ниже, чтобы подтвердить запрос",
Click on the lock icon to toggle public/private,"Нажмите на значок замка, чтобы переключить публичный / приватный",
Click on {0} to generate Refresh Token.,"Нажмите {0}, чтобы сгенерировать токен обновления.",
Close Condition,Закрыть условие,
Columns / Fields,Колонки / Поля,
"Configure notifications for mentions, assignments, energy points and more.","Настройте уведомления для упоминаний, назначений, энергетических очков и многое другое.",
Contact Email,Эл.почта для связи,
Contact Numbers,Контактные номера,
Contact Phone,Контактный телефон,
Contact Synced with Google Contacts.,Контакт синхронизирован с контактами Google.,
Context,Контекст,
Contribute Translations,Внести перевод,
Contributed,Внесенный,
Controller method get_razorpay_order missing,Метод контроллера get_razorpay_order отсутствует,
Copied to clipboard.,Скопировано в буфер обмена.,
Core Modules {0} cannot be searched in Global Search.,Основные модули {0} не могут быть найдены в глобальном поиске.,
Could not create Razorpay order. Please contact Administrator,"Не удалось создать заказ Razorpay. Пожалуйста, свяжитесь с администратором",
Could not create razorpay order,Не удалось создать заказ на бритву,
Create Log,Создать журнал,
Create your first {0},Создайте свой первый {0},
Created {0} records successfully.,Создано {0} записей успешно.,
Cron Format,Cron формат,
Daily Events should finish on the Same Day.,Ежедневные события должны заканчиваться в тот же день.,
Daily Long,Ежедневно,
Default Role on Creation,Роль по умолчанию при создании,
Default Theme,Тема по умолчанию,
Default {0},По умолчанию {0},
Delete All,Удалить все,
Do you want to cancel all linked documents?,Вы хотите отменить все связанные документы?,
DocType Action,DocType Action,
DocType Event,Событие DocType,
DocType Link,DocType Link,
Document Share,Поделиться документом,
Document Tag,Тег документа,
Document Title,Заголовок документа,
Document Type Field Mapping,Отображение поля типа документа,
Document Type Mapping,Отображение типа документа,
Document Type {0} has been repeated.,Тип документа {0} был повторен.,
Document renamed from {0} to {1},Документ переименован из {0} в {1},
Document type is required to create a dashboard chart,Тип документа необходим для создания диаграммы панели инструментов,
Documentation Link,Документация Ссылка,
Don't Import,Не импортировать,
Don't Send Emails,Не отправлять электронные письма,
"Drag and drop files, ","Перетащите файлы, ",
Drop,Бросить,
Drop Here,Бросить тут,
Drop files here,Поместите файлы сюда,
Dynamic Template,Динамический шаблон,
ERPNext Role,ERPNext роль,
Email / Notifications,Уведомления по электронной почте,
Email Account setup please enter your password for: {0},"Настройка учетной записи электронной почты, введите ваш пароль для: {0}",
Email Address whose Google Contacts are to be synced.,"Адрес электронной почты, чьи контакты Google должны быть синхронизированы.",
"Email ID must be unique, Email Account already exists for {0}","Идентификатор электронной почты должен быть уникальным, учетная запись электронной почты уже существует для {0}",
Email IDs,E-mail идентификаторы,
Enable Allow Auto Repeat for the doctype {0} in Customize Form,Разрешить автоматическое повторение для типа документа {0} в форме настройки,
Enable Automatic Linking in Documents,Включить автоматическое связывание в документах,
Enable Email Notifications,Включить уведомления по электронной почте,
Enable Google API in Google Settings.,Включить Google API в настройках Google.,
Enable Security,Включить безопасность,
Energy Point,Балл активности,
Enter Client Id and Client Secret in Google Settings.,Введите идентификатор клиента и секрет клиента в настройках Google.,
Enter Code displayed in OTP App.,"Введите код, отображаемый в приложении OTP.",
Event Configurations,Конфигурации событий,
Event Consumer,Потребитель событий,
Event Consumer Document Type,Тип документа потребителя события,
Event Consumer Document Types,Типы документов потребителя событий,
Event Producer,Продюсер событий,
Event Producer Document Type,Тип документа источника событий,
Event Producer Document Types,Типы документов источника событий,
Event Streaming,Потоковое событие,
Event Subscriber,Подписчик на событие,
Event Sync Log,Журнал синхронизации событий,
Event Synced with Google Calendar.,Событие синхронизировано с календарем Google.,
Event Update Log,Журнал обновлений событий,
Export 1 record,Экспортировать 1 запись,
Export Errored Rows,Экспорт строк с ошибками,
Export From,Экспорт из,
Export Type,Тип экспорта,
Export {0} records,Экспорт {0} записей,
Failed to connect to the Event Producer site. Retry after some time.,Не удалось подключиться к сайту источника событий. Повторите попытку через некоторое время.,
Failed to create an Event Consumer or an Event Consumer for the current site is already registered.,"Не удалось создать получателя событий или получателя событий для текущего сайта, который уже зарегистрирован.",
Failure,Неудача,
Fetching default Global Search documents.,Извлечение документов глобального поиска по умолчанию.,
Fetching posts...,Получение сообщений...,
Field To Check,Поле для проверки,
File Information,Информация о файле,
Filter By,Сортировать по,
Filtered Records,Отфильтрованные записи,
Filters applied for {0},Фильтры применены для {0},
Finished,Законченный,
First,Первый,
For Document Event,Для события документа,
"For more information, <a class=""text-muted"" href=""https://erpnext.com/docs/user/manual/en/setting-up/email/linking-emails-to-document"">click here</a>.","Для получения дополнительной информации <a class=""text-muted"" href=""https://erpnext.com/docs/user/manual/en/setting-up/email/linking-emails-to-document"">нажмите здесь</a> .",
"For more information, {0}.","Для получения дополнительной информации, {0}.",
"For performance, only the first 100 rows were processed.",Для производительности были обработаны только первые 100 строк.,
Form URL-Encoded,Форма URL-кодированная,
Frequently Visited Links,Часто посещаемые ссылки,
From Date,С,
From User,От пользователя,
Global Search DocType,Глобальный поиск DocType,
Global Search Document Types Reset.,Сброс типов документов глобального поиска.,
Global Search Settings,Настройки глобального поиска,
Global Shortcuts,Глобальные ярлыки,
Go,Вперёд,
Go to next record,Перейти к следующей записи,
Go to previous record,Перейти к предыдущей записи,
Google API Settings.,Настройки Google API.,
Google Calendar,Календарь Google,
"Google Calendar - Could not create Calendar for {0}, error code {1}.","Календарь Google. Не удалось создать календарь для {0}, код ошибки {1}.",
"Google Calendar - Could not delete Event {0} from Google Calendar, error code {1}.","Календарь Google - не удалось удалить событие {0} из календаря Google, код ошибки {1}.",
"Google Calendar - Could not fetch event from Google Calendar, error code {0}.","Календарь Google - не удалось получить событие из календаря Google, код ошибки {0}.",
"Google Calendar - Could not insert contact in Google Contacts {0}, error code {1}.","Календарь Google. Не удалось вставить контакт в контакты Google {0}, код ошибки {1}.",
"Google Calendar - Could not insert event in Google Calendar {0}, error code {1}.","Календарь Google. Не удалось вставить событие в календарь Google {0}, код ошибки {1}.",
"Google Calendar - Could not update Event {0} in Google Calendar, error code {1}.","Календарь Google - не удалось обновить событие {0} в календаре Google, код ошибки {1}.",
Google Calendar Event ID,Идентификатор события календаря Google,
Google Calendar Integration.,Интеграция календаря Google.,
Google Calendar has been configured.,Календарь Google был настроен.,
Google Contacts,Контакты Google,
"Google Contacts - Could not sync contacts from Google Contacts {0}, error code {1}.","Контакты Google - не удалось синхронизировать контакты из контактов Google {0}, код ошибки {1}.",
"Google Contacts - Could not update contact in Google Contacts {0}, error code {1}.","Контакты Google - не удалось обновить контакт в Контактах Google {0}, код ошибки {1}.",
Google Contacts Id,Идентификатор контактов Google,
Google Contacts Integration is disabled.,Интеграция с контактами Google отключена.,
Google Contacts Integration.,Интеграция с контактами Google.,
Google Contacts has been configured.,Контакты Google настроены.,
Google Drive,Google Drive,
Google Drive - Could not create folder in Google Drive - Error Code {0},Google Диск - Не удалось создать папку на Google Диск - Код ошибки {0},
Google Drive - Could not find folder in Google Drive - Error Code {0},Google Диск - Не удалось найти папку на Google Диск - Код ошибки {0},
Google Drive Backup Successful.,Резервное копирование на Google Диске успешно.,
Google Drive Backup.,Резервное копирование Google Диска.,
Google Drive Integration.,Интеграция с Google Drive.,
Google Drive has been configured.,Диск Google был настроен.,
Google Integration is disabled.,Интеграция с Google отключена.,
Google Settings,Настройки Google,
Group By,Группа по,
Group By Based On,Группировать по на основе,
Group By Type,Группировать по типу,
Group By field is required to create a dashboard chart,Поле «По группам» необходимо для создания диаграммы панели инструментов,
HH:mm,ЧЧ:мм,
HH:mm:ss,ЧЧ:мм:сс,
HOOK-.####,HOOK-.####,
HTML Page,HTML страница,
Has Mapping,Имеет отображение,
Hourly Long,Почасовая,
"If non-standard port (e.g. POP3: 995/110, IMAP: 993/143)","Если нестандартный порт (например, POP3: 995/110, IMAP: 993/143)",
If the document has different field names on the Producer and Consumer's end check this and set up the Mapping,"Если документ имеет разные имена полей на стороне источника и получателя, проверьте это и настройте отображение",
If this is checked the documents will have the same name as they have on the Event Producer's site,"Если этот флажок установлен, документы будут иметь то же имя, что и на сайте производителя событий.",
Illegal SQL Query,Неверный SQL-запрос,
Import File,Импортировать файл,
Import Log Preview,Предварительный просмотр журнала импорта,
Import Preview,Предварительный просмотр импорта,
Import Progress,Импорт прогресса,
Import Type,Тип импорта,
Import Warnings,Импорт предупреждений,
"Import template should be of type .csv, .xlsx or .xls","Шаблон импорта должен иметь тип .csv, .xlsx или .xls",
Import template should contain a Header and atleast one row.,Шаблон импорта должен содержать заголовок и не менее одной строки.,
Importing {0} of {1},Импорт {0} из {1},
"Importing {0} of {1}, {2}","Импорт {0} из {1}, {2}",
Include indentation,Включить отступ,
Incoming Change,Входящие изменения,
Invalid Filter Value,Неверное значение фильтра,
Invalid URL,Неправильный URL,
Invalid field name: {0},Неверное имя поля: {0},
Invalid file URL. Please contact System Administrator.,"Неверный URL файла. Пожалуйста, свяжитесь с системным администратором.",
Invalid include path,Неверный путь включения,
Invalid username or password,неправильное имя пользователя или пароль,
Is Primary,Основной,
Is Primary Mobile,Основной мобильный,
Is Primary Phone,Основной телефон,
Is Tree,Дерево,
JSON Request Body,Тело запроса JSON,
Javascript is disabled on your browser,Javascript отключен в вашем браузере,
Job,Работа,
Jump to field,Перейти к полю,
Keyboard Shortcuts,Горячие клавиши,
LDAP Group,LDAP Group,
LDAP Group Field,Поле группы LDAP,
LDAP Group Mapping,LDAP Group Mapping,
LDAP Group Mappings,Отображения группы LDAP,
LDAP Last Name Field,Поле Фамилия LDAP,
LDAP Middle Name Field,Поле второго имени LDAP,
LDAP Mobile Field,LDAP мобильный,
LDAP Phone Field,LDAP телефон,
LDAP User Creation and Mapping,Создание и сопоставление пользователей LDAP,
Landscape,Альбомный,
Last,Последний,
Last Backup On,Последнее резервное копирование включено,
Last Execution,Последнее исполнение,
Last Sync On,Последняя синхронизация,
Last Update,Последнее обновление,
Last refreshed,Последнее обновление,
Link Document Type,Тип документа ссылки,
Link Fieldname,Имя поля ссылки,
Loading import file...,Загрузка импортируемого файла...,
Local Document Type,Локальный тип документа,
Log Data,Данные журнала,
Main Section (HTML),Основной раздел (HTML),
Main Section (Markdown),Основной раздел (Markdown),
"Maintains a Log of all inserts, updates and deletions on Event Producer site for documents that have consumers.","Ведение журнала всех вставок, обновлений и удалений на сайте Event Producer для документов, имеющих потребителей.",
Maintains a log of every event consumed along with the status of the sync and a Resync button in case sync fails.,"Ведение журнала всех использованных событий, а также состояния синхронизации и кнопки Resync в случае сбоя синхронизации.",
Make all attachments private,Сделайте все вложения приватными,
Mandatory Depends On,Обязателен - зависит от,
Map Columns,Столбцы карты,
Map columns from {0} to fields in {1},Сопоставить столбцы из {0} с полями в {1},
Mapping column {0} to field {1},Отображение столбца {0} в поле {1},
Mark all as Read,Отметить все как прочитанное,
Maximum Points,Максимум баллов,
Maximum points allowed after multiplying points with the multiplier value\n(Note: For no limit leave this field empty or set 0),"Максимальное количество баллов, допустимое после умножения баллов на значение множителя (Примечание: для ограничения не оставляйте это поле пустым или установите 0)",
Me,Мне,
Mention,Упоминание,
Modules,Модули,
Monthly Long,Ежемесячно долго,
Naming Series,Именование серии,
Navigate Home,Навигация Домой,
Navigate list down,Переместиться вниз по списку,
Navigate list up,Навигация по списку вверх,
New Notification,Новое уведомление,
New {0}: {1},Новый {0}: {1},
Newsletter should have atleast one recipient,Бюллетень должен иметь как минимум одного получателя,
No Events Today,Сегодня нет событий,
No Google Calendar Event to sync.,Нет события в календаря Google для синхронизации.,
No More Activity,Нет больше активности,
No Name Specified for {0},Имя не указано для {0},
No Upcoming Events,Нет предстоящих событий,
No activity,Нет активности,
No conditions provided,Условия не предоставлены,
No contacts linked to document,"Нет контактов, связанных с документом",
No data to export,Нет данных для экспорта,
No documents found tagged with {0},Документы с тегом {0} не найдены,
No failed logs,Нет неудачных логов,
No filters found,Фильтры не найдены,
No more items to display,Нет больше элементов для отображения,
No more posts,Нет больше сообщений,
No new Google Contacts synced.,Новые контакты Google не синхронизированы.,
No pending or current jobs for this site,Нет ожидающих или текущих работ для этого сайта,
No posts yet,Постов пока нет,
No records will be exported,Никакие записи не будут экспортированы,
No results found for {0} in Global Search,Не найдено результатов для {0} в глобальном поиске,
No user found,Пользователь не найден,
Notification Log,Журнал уведомлений,
Notification Settings,Настройки уведомлений,
Notification Subscribed Document,Уведомление о подписке документа,
Notifications Disabled,Уведомления отключены,
Number of Groups,Количество групп,
OAuth Client ID,Идентификатор клиента OAuth,
OTP setup using OTP App was not completed. Please contact Administrator.,"Настройка OTP с использованием приложения OTP не была завершена. Пожалуйста, свяжитесь с администратором.",
Only one {0} can be set as primary.,Только один {0} может быть установлен в качестве основного.,
Open Awesomebar,Открыть Awesomebar,
Open Chat,Открытый чат,
Open Documents,Открытые документы,
Open Help,Открыть справку,
Open Settings,Открыть настройки,
Open list item,Открыть элемент списка,
Organizational Unit for Users,Организационная единица для пользователей,
Page Shortcuts,Ярлыки страниц,
Parent Field (Tree),Родительское поле (дерево),
Parent Field must be a valid fieldname,Родительское поле должно быть допустимым именем поля,
Pin Globally,Pin глобально,
Places,Места,
Please check the filter values set for Dashboard Chart: {},"Проверьте значения фильтра, установленные для диаграммы панели инструментов: {}",
Please enable pop-ups in your browser,"Пожалуйста, включите всплывающие окна в вашем браузере",
Please find attached {0}: {1},"Пожалуйста, найдите прикрепленный {0}: {1}",
Please select applicable Doctypes,"Пожалуйста, выберите подходящий тип документа",
Portrait,Портретная,
Press Alt Key to trigger additional shortcuts in Menu and Sidebar,"Нажмите клавишу Alt, чтобы вызвать дополнительные ярлыки в меню и боковой панели",
Print Settings...,Настройки печати...,
Producer Document Name,Название документа производителя,
Producer URL,URL производителя,
Property Depends On,Свойства зависят от,
Pull from Google Calendar,Загрузить из календаря Google,
Pull from Google Contacts,Загрузить из контактов Google,
Pulled from Google Calendar,Загружено из календаря Google,
Pulled from Google Contacts,Загружено из контактов Google,
Push to Google Calendar,Нажмите на Google календарь,
Push to Google Contacts,Нажмите на контакты Google,
Queue / Worker,Очередь / Рабочий,
RAW Information Log,Необработанный информационный журнал,
Raw Printing Settings...,Настройки необработанной печати...,
Read Only Depends On,Только для чтения - зависит от,
Recent Activity,Недавняя активность,
Reference document has been cancelled,Справочный документ был отменен,
Reload File,Перезагрузить файл,
Remote Document Type,Тип удаленного документа,
"Renamed files and replaced code in controllers, please check!","Переименуйте файлы и замените код в контроллерах, пожалуйста, проверьте!",
Repeat on Last Day of the Month,Повторите в последний день месяца,
Repeats {0},Повторяет {0},
Report Information,Информация об отчете,
Report with more than 10 columns looks better in Landscape mode.,Отчет с более чем 10 столбцами выглядит лучше в ландшафтном режиме.,
Request Structure,Структура запроса,
Restricted,Ограниченный,
Restrictions,ограничения,
Resync,Синхронизировать,
Row Number,Номер строки,
Row {0},Строка {0},
Run Jobs only Daily if Inactive For (Days),"Запускать задания только ежедневно, если неактивен в течение (дней)",
SMS was not sent. Please contact Administrator.,"SMS не было отправлено. Пожалуйста, свяжитесь с администратором.",
Saved Successfully,Успешно сохранено,
Scheduled Job,Запланированная работа,
Scheduled Job Log,Журнал запланированных работ,
Scheduled Job Type,Запланированный тип работы,
Scheduler Inactive,Планировщик неактивен,
Scheduler is inactive. Cannot import data.,Планировщик неактивен. Невозможно импортировать данные.,
Script Manager,Диспетчер скриптов,
Script Type,Тип скрипта,
Search Priorities,Приоритеты поиска,
Search Source Text,Поиск исходного текста,
Search by filename or extension,Поиск по имени файла или расширению,
Select Date Range,Выберите диапазон дат,
Select Field,Выберите поле,
Select Field...,Выберите поле...,
Select Filters,Выберите фильтры,
Edit Filters,Изменить фильтры,
Select Google Calendar to which event should be synced.,"Выберите календарь Google, к которому нужно синхронизировать событие.",
Select Google Contacts to which contact should be synced.,"Выберите Google Контакты, с которыми контакт должен быть синхронизирован.",
Select Group By...,Выбрать группу по...,
Select Mandatory,Выберите Обязательный,
Select atleast 2 actions,Выберите по крайней мере 2 действия,
Select list item,Выберите элемент списка,
Select multiple list items,Выберите несколько элементов списка,
Send an email to {0} to link it here,"Отправьте письмо на {0}, чтобы связать его здесь",
Server Action,Действие сервера,
Server Script,Серверный скрипт,
Session Default,Сессия по умолчанию,
Session Default Settings,Настройки сеанса по умолчанию,
Session Defaults,Сеансы по умолчанию,
Session Defaults Saved,Сеансы по умолчанию сохранены,
Set as Default Theme,Установить как тему по умолчанию,
Setting up Global Search documents.,Настройка документов глобального поиска.,
Show Document,Показать документ,
Show Failed Logs,Показать журнал ошибок,
Show Keyboard Shortcuts,Показать горячие клавиш,
Show More Activity,Показать больше активности,
Show Traceback,Показать трассировку,
Show Warnings,Показать предупреждения,
Showing only first {0} rows out of {1},Показаны только первые {0} строк из {1},
"Simple Python Expression, Example: Status in (""Invalid"")","Простое выражение Python, пример: статус в («Неверный»)",
Skipping Untitled Column,Пропуск столбца без названия,
Skipping column {0},Пропуск столбца {0},
Social Home,Социальный Дом,
Some columns might get cut off when printing to PDF. Try to keep number of columns under 10.,"Некоторые столбцы могут быть обрезаны при печати в PDF. Старайтесь, чтобы количество столбцов было меньше 10.",
Something went wrong during the token generation. Click on {0} to generate a new one.,"Что-то пошло не так во время генерации токенов. Нажмите {0}, чтобы создать новый.",
Submit After Import,Отправить после импорта,
Submitting...,Добавляю...,
Success! You are good to go 👍,Успех! Вы можете продолжать 👍,
Successful Transactions,Успешные транзакции,
Successfully Submitted!,Успешно отправлено!,
Successfully imported {0} record.,Успешно импортирована запись {0}.,
Successfully imported {0} records.,Успешно импортировано {0} записей.,
Successfully updated {0} record.,Успешно обновлена запись {0}.,
Successfully updated {0} records.,Успешно обновлено {0} записей.,
Sync Calendar,Синхронизировать календарь,
Sync Contacts,Синхронизация контактов,
Sync with Google Calendar,Синхронизировать с календарем Google,
Sync with Google Contacts,Синхронизировать с контактами Google,
Synced,Синхронизированные,
Syncing,Синхронизации,
Syncing {0} of {1},Синхронизация {0} из {1},
Tag Link,Ссылка на тег,
Take Backup,Взять резервную копию,
Template Error,Ошибка шаблона,
Template Options,Параметры шаблона,
Template Warnings,Шаблон предупреждений,
The Auto Repeat for this document has been disabled.,Автоповтор для этого документа был отключен.,
The following records needs to be created before we can import your file.,"Следующие записи должны быть созданы, прежде чем мы сможем импортировать ваш файл.",
The mapping configuration between two doctypes.,Конфигурация отображения между двумя типами документов.,
The site which is consuming your events.,"Сайт, который потребляет ваши события.",
The site you want to subscribe to for consuming events.,"Сайт, на который вы хотите подписаться на события потребления.",
The webhook will be triggered if this expression is true,"Веб-крючок будет запущен, если это выражение верно",
The {0} is already on auto repeat {1},{0} уже включен автоповтор {1},
There are some linked records which needs to be created before we can import your file. Do you want to create the following missing records automatically?,"Есть несколько связанных записей, которые необходимо создать, прежде чем мы сможем импортировать ваш файл. Вы хотите автоматически создать следующие отсутствующие записи?",
There should be atleast one row for the following tables: {0},Должна быть как минимум одна строка для следующих таблиц: {0},
There should be atleast one row for {0} table,Должна быть хотя бы одна строка для таблицы {0},
This action is only allowed for {},Это действие разрешено только для {},
This cannot be undone,Это не может быть отменено,
Time Format,Формат времени,
Time series based on is required to create a dashboard chart,Временной ряд на основе необходим для создания диаграммы панели инструментов,
Time {0} must be in format: {1},Время {0} должно быть в формате: {1},
"To configure Auto Repeat, enable ""Allow Auto Repeat"" from {0}.","Чтобы настроить автоматический повтор, включите «Разрешить автоматический повтор» из {0}.",
To enable it follow the instructions in the following link: {0},"Чтобы включить его, следуйте инструкциям по следующей ссылке: {0}",
"To use Google Calendar, enable {0}.","Чтобы использовать календарь Google, включите {0}.",
"To use Google Contacts, enable {0}.","Чтобы использовать контакты Google, включите {0}.",
"To use Google Drive, enable {0}.","Чтобы использовать Google Диск, включите {0}.",
Today's Events,Сегодняшние события,
Toggle Public/Private,Переключить Публичный/Приватный,
Tracks milestones on the lifecycle of a document if it undergoes multiple stages.,"Отслеживает этапы жизненного цикла документа, если он проходит несколько этапов.",
Tree structures are implemented using Nested Set,Древовидные структуры реализованы с использованием Nested Set,
Trigger Primary Action,Триггер Основное действие,
URL for documentation or help,URL для документации или помощи,
URL must start with 'http://' or 'https://',URL должен начинаться с «http://» или «https://»,
Unchanged,Без изменений,
Unpin,Открепить,
Untitled Column,Колонка без названия,
Untranslated,Непереведенные,
Upcoming Events,Предстоящие События,
Update Existing Records,Обновить существующие записи,
Updated To A New Version 🎉,Обновлено до новой версии 🎉,
"Updating {0} of {1}, {2}","Обновление {0} из {1}, {2}",
Upload file,Загрузить файл,
Upload {0} files,Загрузить {0} файлов,
Uploaded To Google Drive,Загружено на Google Диск,
Uploaded successfully,Загружен успешно,
Uploading {0} of {1},Загрузка {0} из {1},
Use SSL for Outgoing,Использовать SSL для исходящих,
Use Same Name,Используйте то же имя,
Used For Google Maps Integration.,Используется для интеграции с Google Maps.,
User ID Property,Свойство ID пользователя,
User Profile,Профиль пользователя,
User Settings,Пользовательские настройки,
User does not exist,Пользователь не существует,
User {0} has requested for data deletion,Пользователь {0} запросил удаление данных,
Users assigned to the reference document will get points.,"Пользователи, назначенные для справочного документа, получат баллы.",
Value must be one of {0},Значение должно быть одним из {0},
Value {0} missing for {1},Значение {0} отсутствует для {1},
Verification,Верификация,
Verification Code,Код верификации,
Verification code email not sent. Please contact Administrator.,"Письмо с кодом подтверждения не отправлено. Пожалуйста, свяжитесь с администратором.",
Verified,Проверенный,
Verifier,Проверяющий,
View Full Log,Посмотреть полный журнал,
"View Log of all print, download and export events","Просмотр журнала всех событий печати, загрузки и экспорта",
Visit Web Page,Посетить веб-страницу,
Webhook Secret,Webhook Secret,
Webhook Security,Безопасность Webhook,
Webhook Trigger,Webhook Trigger,
Weekly Long,Еженедельно Лонг,
"When enabled this will allow guests to upload files to your application, You can enable this if you wish to collect files from user without having them to log in, for example in job applications web form.","Когда эта опция включена, это позволит гостям загружать файлы в ваше приложение. Вы можете включить эту функцию, если хотите собирать файлы у пользователя без необходимости входа в него, например, в веб-форме рабочих приложений.",
Will run scheduled jobs only once a day for inactive sites. Default 4 days if set to 0.,"Запланированные задания будут запускаться только один раз в день для неактивных сайтов. По умолчанию 4 дня, если установлено значение 0.",
Workflow Status,Состояние рабочего процесса,
You are not allowed to export {} doctype,Вы не можете экспортировать {} doctype,
You can try changing the filters of your report.,Вы можете попробовать изменить фильтры вашего отчета.,
You do not have permissions to cancel all linked documents.,У вас нет прав для отмены всех связанных документов.,
You need to create these first: ,Вам сначала нужно создать: ,
You need to enable JavaScript for your app to work.,Вам нужно включить JavaScript для вашего приложения для работы.,
You need to install pycups to use this feature!,"Вам нужно установить pycups, чтобы использовать эту функцию!",
Your Target,Ваша цель,
"browse,","просматривать,",
cancelled this document {0},отменил этот документ {0},
changed value of {0} {1},измененное значение {0} {1},
changed values for {0} {1},измененные значения для {0} {1},
choose an,выбрать,
empty,пустой,
of,из,
or attach a,или прикрепить,
submitted this document {0},отправил этот документ {0},
"tag name..., e.g. #tag","имя тега ..., например #tag",
uploaded file,загруженный файл,
via Data Import,через импорт данных,
{0} Google Calendar Events synced.,{0} События календаря Google синхронизированы.,
{0} Google Contacts synced.,{0} Google Контакты синхронизированы.,
{0} are mandatory fields,{0} обязательные поля,
{0} are required,{0} требуется,
{0} assigned a new task {1} {2} to you,{0} назначил вам новое задание {1} {2},
{0} gained {1} point for {2} {3},{0} получил {1} балл за {2} {3},
{0} gained {1} points for {2} {3},{0} набрал {1} баллов за {2} {3},
{0} has no versions tracked.,{0} не отслеживает версии.,
{0} is not a valid report format. Report format should one of the following {1},{0} не является допустимым форматом отчета. Формат отчета должен быть одним из следующих {1},
{0} mentioned you in a comment in {1} {2},{0} упомянул вас в комментарии в {1} {2},
{0} of {1} ({2} rows with children),{0} из {1} ({2} строк с дочерними элементами),
{0} records will be exported,{0} записей будут экспортированы,
{0} shared a document {1} {2} with you,{0} поделился с вами документом {1} {2},
{0} should not be same as {1},{0} не должен совпадать с {1},
{0} translations pending,Ожидается перевод {0},
{0} {1} is linked with the following submitted documents: {2},{0} {1} связан со следующими представленными документами: {2},
"{0}: Failed to attach new recurring document. To enable attaching document in the auto repeat notification email, enable {1} in Print Settings","{0}: не удалось прикрепить новый повторяющийся документ. Чтобы включить вложение документа в электронное письмо с уведомлением о повторном включении, включите {1} в настройках печати.",
{0}: Fieldname cannot be one of {1},{0}: имя поля не может быть одним из {1},
{} Complete,{} Завершено,
← Back to upload files,← Вернуться к загрузке файлов,
Activity,Активность,
Add / Manage Email Accounts.,Добавление / Управление учетными записями электронной почты,
Add Child,Добавить потомка,
Add Multiple,Добавить несколько,
Add Participants,Добавить участников,
Added {0} ({1}),Добавлено {0} ({1}),
Address Line 1,Адрес (1-я строка),
Addresses,Адреса,
All,Все,
Brand,Бренд,
Browse,Обзор,
Cancelled,отменен,
Chart,Диаграмма,
Close,Закрыть,
Communication,Коммуникация,
Compact Item Print,Компактное отоборажение товара при печати,
Company,Организация,
Complete,Завершен,
Completed,Завершено,
Continue,Продолжать,
Country,Страна,
Creating {0},Создание {0},
Currency,Валюта,
Customize,Настроить,
Daily,Ежедневно,
Date,Дата,
Dear,Уважаемый(ая),
Default,По умолчанию,
Delete,Удалить,
Description,Описание,
Designation,Назначение,
Disabled,Отключено,
Doctype,Doctype,
Download Template,Скачать шаблон,
Due Date,Дата выполнения,
Duplicate,Дублировать,
Edit Profile,Редактировать профиль,
Email,Эл. адрес,
Enter Value,Введите значение,
Entity Type,Тип объекта,
Error,Ошибка,
Expired,Истек срок действия,
Export,Экспорт,
Export not allowed. You need {0} role to export.,Экспорт не допускается. Вам нужна роль {0} для экспорта.,
Field,Поле,
File Manager,Файловый менеджер,
Filters,Фильтры,
Get Items,Получить продукты,
Goal,Цель,
Group,Группа,
Group Node,Групповой узел,
Help,Помощь,
Help Article,Статья помощи,
Home,Главная,
Import Data from CSV / Excel files.,Импорт данных из файлов CSV / Excel.,
In Progress,Выполняется,
Intermediate,Промежуточный,
Invite as User,Пригласить в пользователя,
Loading...,Загрузка...,
Location,Местоположение,
Message,Сообщение,
Missing Values Required,Не заполнены обязательные поля,
Mobile No,Мобильный номер,
Month,Mесяц,
Name,Имя,
Newsletter,Рассылка новостей,
Not Allowed,Не разрешено,
Note,Заметки,
Offline,Не в сети,
Open,Создано,
Page {0} of {1},Страница {0} из {1},
Pending,В ожидании,
Phone,Телефон,
Please click on the following link to set your new password,"Пожалуйста, нажмите на следующую ссылку, чтобы установить новый пароль",
Please select another payment method. Stripe does not support transactions in currency '{0}',Выберите другой способ оплаты. Полоса не поддерживает транзакции в валюте &#39;{0}&#39;,
Please specify,"Пожалуйста, сформулируйте",
Printing,Печать,
Priority,Приоритет,
Project,Проект,
Quarterly,Ежеквартально,
Queued,В очереди,
Quick Entry,Быстрый доступ,
Reason,Причина,
Refreshing,Обновление,
Rename,Переименовать,
Reset,Сброс,
Review,Обзор,
Room,Комната,
Room Type,Тип номера,
Save,Сохранить,
Search results for,Результаты поиска,
Select All,Выбрать все,
Send,Отправить,
Sending,Отправка,
Server Error,Ошибка сервера,
Set,Задать,
Setup,Настройки,
Setup Wizard,Мастер установки,
Size,Размер,
Start,Начать,
Start Time,Стартовое время,
Status,Статус,
Submitted,Проведенный,
Tag,Тег,
Template,Шаблон,
Thursday,Четверг,
Title,Заголовок,
Total,Общая сумма,
Totals,Всего,
Tuesday,Вторник,
Type,Тип,
Update,Обновить,
User {0} is disabled,Пользователь {0} отключен,
Users and Permissions,Пользователи и Права,
Warehouse,Склад,
Welcome to {0},Добро пожаловать в {0},
Year,Год,
Yearly,Ежегодно,
You,Вы,
and,и,
{0} Name,{0} Имя,
{0} is required,{0} является обязательным,
ALL,ВСЕ,
Attach File,Прикрепить файл,
Barcode,Штрих-код,
Beginning with,Начиная с,
Bold,Жирный,
CANCELLED,ОТМЕНЕНО,
Calendar,Календарь,
Center,Центр,
Clear,Отчистить,
Comment,Комментарий,
Comments,Комментарии,
DRAFT,ЧЕРНОВИК,
Dashboard,Панель инструментов,
DocType,DocType,
Download,Скачать,
EMail,Эл. адрес,
Edit in Full Page,Редактировать на полной странице,
Email Inbox,Email Inbox,
File,Файл,
Forward,Переслать,
Icon,Икона,
In,В,
Inbox,Входящие,
Insert New Records,Вставить новые записи,
JavaScript,JavaScript,
LDAP Settings,Настройки LDAP,
Left,Слева,
Like,Лайк,
Link,Ссылка на сайт,
Logged in,Войти в систему,
New,Новый,
Not Found,Не найдено,
Not Like,Не как,
Notify by Email,Уведомить по электронной почте,
Now,Только что,
Off,Выключен,
One of,Один из,
Page,Страница,
Print,Распечатать,
Reference Name,Имя ссылки,
Refresh,Обновить,
Repeat,Повторить,
Right,Справа,
Roles HTML,Роли HTML,
Scheduled To Send,Планируется отправить,
Search Results for ,Результаты поиска для,
Send Notification To,Отправить уведомление на,
Success,Успешно,
Tags,Теги,
Time,Время,
Updated Successfully,Успешно Обновлено,
Upload,Загрузить,
User ,Пользователь ,
Value,Значение,
Web Link,Ссылка на сайт,
Your Email Address,Ваш адрес электронной почты,
Desktop,Настольный,
Usage Info,Информация об использовании,
Download Backups,Скачать резервные копии,
Recorder,Регистратор,
Role Permissions Manager,Менеджер разрешений на роль,
Translation Tool,Инструмент перевода,
Awaiting password,Ожидание пароля,
Current status,Текущее состояние,
Download template,Скачать шаблон,
Edit in full page,Редактировать на полной странице,
Email Id,Email ID,
Email address,Адрес электронной почты,
Ends on,Заканчивается на,
Half-yearly,Полугодовой,
Hidden,Скрытый,
Javascript,Javascript,
Ldap settings,Настройки Ldap,
Mobile number,Мобильный номер,
No,Нет,
Not found,Не обнаружена,
Notes:,Примечания:,
Notify by email,Уведомление по почте,
Permitted Documents For User,Разрешенные документы для пользователя,
Reference Docname,Ссылка Docname,
Reference Doctype,Ссылка DocType,
Reference name,Имя ссылки,
Roles Html,Роли Html,
Row #,Строка #,
Scheduled to send,Запланировано для отправки,
Select Doctype,Выберите Doctype,
Send Email for Successful backup,Отправить письмо об успешном завершении резервного копирования,
Sign up,Зарегистрироваться,
Time format,Формат времени,
Upload failed,Загрузка не удалась,
User Id,Идентификатор пользователя,
Yes,Да,
Your email address,Ваша электронная почта,
added,добавленной,
added {0},Добавлено  {0},
barcode,штрих-код,
beginning with,начиная с,
blue,синий,
bold,жирный,
book,книга,
calendar,календарь,
certificate,сертификат,
check,проверить,
clear,отчистить,
comment,комментарий,
comments,комментарии,
created,созданный,
danger,опасность,
dashboard,Панель приборов,
download,скачать,
edit,Редактировать,
email inbox,Входящая почта,
file,файл,
filter,фильтр,
flag,помечать,
font,шрифт,
forward,дальше,
green,зеленый,
home,дом,
icon,иконка,
inbox,входящие,
like,нравится,
link,ссылка,
list,список,
lock,запирать,
logged in,записан в,
message,сообщение,
module,модуль,
move,переместить,
music,музыка,
new,новый,
now,только что,
off,выкл,
one of,один из,
orange,оранжевый,
page,страница,
print,печать,
purple,пурпурный,
random,случайный,
red,красный,
refresh,обновление,
remove,удалить,
response,отклик,
search,поиск,
share,Поделиться,
stop,стоп,
success,успешно,
tag,тэг,
tags,теги,
tasks,задачи,
time,время,
trash,мусор,
upload,загрузить,
user,пользователь,
value,значение,
web link,ссылка,
yellow,желтый,
Not permitted,Не разрешено,
Add Chart to Dashboard,Добавить диаграмму на панель инструментов,
Add to Dashboard,Добавить на панель инструментов,
Google Translation,Гугл-перевод,
Important,Важный,
No Filters Set,Фильтры не установлены,
No Records Created,Записи не созданы,
Please Set Chart,"Пожалуйста, установите график",
Please create chart first,"Пожалуйста, сначала создайте диаграмму",
"Report has no data, please modify the filters or change the Report Name","В отчете нет данных, измените фильтры или измените название отчета.",
Select Dashboard,Выберите панель инструментов,
Y Field,Поле Y,
You need to be in developer mode to edit this document,"Вы должны быть в режиме разработчика, чтобы редактировать этот документ",
Cards,Карты,
Community Contribution,Вклад сообщества,
Count Filter,Счетный фильтр,
Dashboard Chart Field,Поле диаграммы приборной панели,
Desk Card,Настольная карта,
Desk Chart,Стол Диаграмма,
Desk Page,Рабочий стол,
Desk Shortcut,Сочетание клавиш,
Developer Mode Only,Только режим разработчика,
Disable User Customization,Отключить настройку пользователя,
For example: {} Open,Например: {} Открыто,
Link Cards,Карты ссылок,
Link To,Ссылка к,
Onboarding,Вводный,
Percentage,Процент,
Pie,Пирог,
Pin To Bottom,Закрепить внизу,
Pin To Top,Закрепить сверху,
Restrict to Domain,Ограничить доступ к домену,
Shortcuts,Ярлыки,
X Field,Поле X,
Y Axis,Ось Y,
workspace,рабочее пространство,
Setup > User,Настройка&gt; Пользователь,
Setup > Customize Form,Настройка&gt; Настройка формы,
Setup > User Permissions,Настройка&gt; Полномочия пользователя,
"Error connecting to QZ Tray Application...<br><br> You need to have QZ Tray application installed and running, to use the Raw Print feature.<br><br><a target=""_blank"" href=""https://qz.io/download/"">Click here to Download and install QZ Tray</a>.<br> <a target=""_blank"" href=""https://erpnext.com/docs/user/manual/en/setting-up/print/raw-printing"">Click here to learn more about Raw Printing</a>.","Ошибка подключения к приложению QZ Tray ... <br><br> Вам необходимо установить и запустить приложение QZ Tray, чтобы использовать функцию Raw Print. <br><br> <a href=""https://qz.io/download/"" target=""_blank"">Нажмите здесь, чтобы загрузить и установить QZ Tray</a> . <br> <a href=""https://erpnext.com/docs/user/manual/en/setting-up/print/raw-printing"" target=""_blank"">Нажмите здесь, чтобы узнать больше о Raw Printing</a> .",
No email account associated with the User. Please add an account under User > Email Inbox.,"Нет учетной записи электронной почты, связанной с пользователем. Пожалуйста, добавьте учетную запись в разделе Пользователь > Электронная почта Входящие",
"For comparison, use >5, <10 or =324. For ranges, use 5:10 (for values between 5 & 10).","Для сравнения используйте&gt; 5, &lt;10 или = 324. Для диапазонов используйте 5:10 (для значений от 5 до 10).",
No default Address Template found. Please create a new one from Setup > Printing and Branding > Address Template.,"Шаблон адреса по умолчанию не найден. Создайте новый, выбрав «Настройка» > «Печать и брендинг» > «Шаблон адреса».",
Please setup default Email Account from Setup > Email > Email Account,"Пожалуйста, настройте учетную запись электронной почты по умолчанию из меню «Настройка» > «Электронная почта»&gt;",
Email Account not setup. Please create a new Email Account from Setup > Email > Email Account,"Учетная запись электронной почты не настроена. Пожалуйста, создайте новую учетную запись электронной почты в «Настройки» > «Электронная почта» > «Учетная запись электронной почты»",
Attach file,Прикрепить файл,
Contribution Status,Статус вклада,
Contribution Document Name,Название документа о взносе,
Extends,Расширяется,
Extends Another Page,Расширяет другую страницу,
Please select target language for translation,"Пожалуйста, выберите целевой язык для перевода",
Select Language,Выбрать язык,
Confirm Translations,Подтвердить перевод,
Contributed Translations,Добавленные переводы,
Show Tags,Показать теги,
Hide Tags,Скрыть теги,
Do not have permission to access {0} bucket.,У вас нет разрешения на доступ к сегменту {0}.,
Allow document creation via Email,Разрешить создание документов по электронной почте,
Sender Field,Поле отправителя,
Logout All Sessions on Password Reset,Выйти из всех сеансов при сбросе пароля,
Logout From All Devices After Changing Password,Выход из системы со всех устройств после изменения пароля,
Send Notifications For Documents Followed By Me,"Отправлять уведомления о документах, подписанных мной",
Send Notifications For Email Threads,Отправлять уведомления по электронной почте,
Bypass Restricted IP Address Check If Two Factor Auth Enabled,"Обход проверки ограниченного IP-адреса, если включена двухфакторная аутентификация",
Reset LDAP Password,Сбросить пароль LDAP,
Confirm New Password,Подтвердите новый пароль,
Logout All Sessions,Выйти из всех сеансов,
Passwords do not match!,Пароли не совпадают!,
Dashboard Manager,Менеджер приборной панели,
Dashboard Settings,Настройки приборной панели,
Chart Configuration,Конфигурация диаграммы,
No Permitted Charts on this Dashboard,На этой панели инструментов нет разрешенных диаграмм,
No Permitted Charts,Нет разрешенных графиков,
Reset Chart,Сбросить график,
via {0},через {0},
{0} is not a valid Phone Number,{0} недействительный номер телефона,
Failed Transactions,Неудачные транзакции,
Value for field {0} is too long in {1}. Length should be lesser than {2} characters,Значение поля {0} слишком длинное в {1}. Длина должна быть меньше {2} симв.,
Data Too Long,Данные слишком длинные,
via Notification,через уведомление,
Log in to access this page.,"Авторизуйтесь, чтобы получить доступ к этой странице.",
Report Document Error,Сообщить об ошибке документа,
{0} is an invalid Data field.,{0} - недопустимое поле данных.,
Only Options allowed for Data field are:,"Только параметры, разрешенные для поля данных:",
Select a valid Subject field for creating documents from Email,Выберите допустимое поле темы для создания документов из электронной почты,
"Subject Field type should be Data, Text, Long Text, Small Text, Text Editor","Тип поля темы: Данные, Текст, Длинный текст, Мелкий текст, Текстовый редактор.",
Select a valid Sender Field for creating documents from Email,Выберите допустимое поле отправителя для создания документов из электронной почты,
Sender Field should have Email in options,В поле отправителя должно быть указано электронное письмо.,
Password changed successfully.,Пароль успешно изменен.,
Failed to change password.,Не удалось изменить пароль.,
No Entry for the User {0} found within LDAP!,В LDAP не найдено ни одной записи для пользователя {0}!,
No LDAP User found for email: {0},Не найдено ни одного пользователя LDAP для электронной почты: {0},
Prepared Report User,Подготовленный отчет Пользователь,
Scheduler Event,Планировщик событий,
Select Event Type,Выберите тип события,
Schedule Script,Сценарий расписания,
Duration,Продолжительность,
Donut,Донат,
Custom Options,Пользовательские параметры,
"Ex: ""colors"": [""#d1d8dd"", ""#ff5858""]","Пример: ""colors"": [""#d1d8dd"", ""#ff5858""]",
Confirmation Email Template,Шаблон письма с подтверждением,
Welcome Email Template,Шаблон приветственного письма,
Schedule Send,Расписание отправки,
Do you really want to send this email newsletter?,Вы действительно хотите отправить этот информационный бюллетень по электронной почте?,
Advanced Settings,Расширенные настройки,
Disable Comments,Отключить комментарии,
Comments on this blog post will be disabled if checked.,"Если этот флажок установлен, комментарии к этому сообщению в блоге будут отключены.",
CSS Class,CSS-класс,
Full Width,Полная ширина,
Page Builder,Конструктор страниц,
Page Building Blocks,Строительные блоки страницы,
Header and Breadcrumbs,Заголовок и панировочные сухари,
Add Custom Tags,Добавить собственные теги,
Web Page Block,Блок веб-страницы,
Web Template,Веб-шаблон,
Edit Values,Изменить значения,
Web Template Values,Ценности веб-шаблонов,
Add Container,Добавить контейнер,
Web Page View,Просмотр веб-страницы,
Path,Путь,
Referrer,Реферер,
Browser,Браузер,
Browser Version,Версия браузера,
Web Template Field,Поле веб-шаблона,
Section,Раздел,
Hide,Скрыть,
Enable In App Website Tracking,Включить отслеживание веб-сайтов в приложении,
Enable Google Indexing,Включить индексирование Google,
"To use Google Indexing, enable <a href=""#Form/Google Settings"">Google Settings</a>.","Чтобы использовать индексирование Google, включите <a href=""#Form/Google Settings"">настройки Google</a> .",
Authorize API Indexing  Access,Разрешить доступ к индексированию API,
Indexing Refresh Token,Индексирование токена обновления,
Indexing Authorization Code,Код авторизации индексации,
Theme Configuration,Конфигурация темы,
Font Properties,Свойства шрифта,
Button Rounded Corners,Кнопка с закругленными углами,
Button Shadows,Тени кнопок,
Button Gradients,Градиенты кнопок,
Light Color,Светлый цвет,
Stylesheet,Таблица стилей,
Custom SCSS,Пользовательский SCSS,
Navbar,Навигационна панель,
Source Message,Исходное сообщение,
Translated Message,Переведенное сообщение,
Using this console may allow attackers to impersonate you and steal your information. Do not enter or paste code that you do not understand.,Использование этой консоли может позволить злоумышленникам выдать себя за вас и украсть вашу информацию. Не вводите и не вставляйте непонятный код.,
{0} m,{0} м,
{0} h,{0} ч,
{0} d,{0} д,
{0} w,{0} н,
{0} M,{0} М,
{0} y,{0} г,
yesterday,вчера,
{0} years ago,{0} год назад,
New Chart,Новый график,
New Shortcut,Новый ярлык,
Edit Chart,Изменить диаграмму,
Edit Shortcut,Изменить ярлык,
Couldn't Load Desk,Не удалось загрузить стол,
"Something went wrong while loading Desk. <b>Please relaod the page</b>. If the problem persists, contact the Administrator","Что-то пошло не так при загрузке стола. <b>Пожалуйста, обновите страницу</b> . Если проблема не исчезнет, обратитесь к администратору.",
Customize Workspace,Настроить рабочее пространство,
Customizations Saved Successfully,Настройки успешно сохранены,
Something went wrong while saving customizations,Что-то пошло не так при сохранении настроек,
{} Dashboard,{} Панель приборов,
No changes in document,Без изменений в документе,
by Role,по роли,
Document is only editable by users with role,Документ могут редактировать только пользователи с ролью,
{0}: Other permission rules may also apply,{0}: могут применяться и другие правила разрешений.,
{0} Web page views,{0} Просмотры страниц,
Expand,Развернуть,
Collapse,Свернуть,
"Invalid Bearer token, please provide a valid access token with prefix 'Bearer'.","Недействительный токен на предъявителя, укажите действительный токен доступа с префиксом «На предъявителя».",
"Failed to decode token, please provide a valid base64-encoded token.",Не удалось декодировать токен. Укажите действительный токен в кодировке base64.,
"Invalid token, please provide a valid token with prefix 'Basic' or 'Token'.",Недействительный токен. Укажите действительный токен с префиксом &quot;Базовый&quot; или &quot;Токен&quot;.,
{0} is not a valid Name,{0} не является допустимым именем,
Your system is being updated. Please refresh again after a few moments.,"Ваша система обновляется. Пожалуйста, обновите снова через несколько секунд.",
{0} {1}: Submitted Record cannot be deleted. You must {2} Cancel {3} it first.,{0} {1}: отправленную запись нельзя удалить. Сначала вы должны {2} отменить {3} его.,
Error has occurred in {0},Ошибка произошла в {0},
Status Updated,Статус обновлен,
You can also copy-paste this {0} to your browser,Вы также можете скопировать и вставить этот {0} в свой браузер,
Enabled scheduled execution for script {0},Включено выполнение по расписанию для скрипта {0},
Scheduled execution for script {0} has updated,Запланированное выполнение для скрипта {0} обновлено,
The Link specified has either been used before or Invalid,"Указанная ссылка либо использовалась ранее, либо недействительна.",
Options for {0} must be set before setting the default value.,Перед установкой значения по умолчанию необходимо задать параметры для {0}.,
Default value for {0} must be in the list of options.,Значение по умолчанию для {0} должно быть в списке параметров.,
Google Indexing has been configured.,Индексирование Google настроено.,
Allow API Indexing Access,Разрешить доступ для индексирования API,
Allow Google Indexing Access,Разрешить доступ к индексированию Google,
Custom Documents,Пользовательские документы,
Could not save customization,Не удалось сохранить настройку,
Transgender,Трансгендер,
Genderqueer,Гендерный,
Non-Conforming,Несоответствие,
Prefer not to say,Предпочитаю не говорить,
Is Billing Contact,Контакт для выставления счетов,
Address And Contacts,Адрес и контакты,
Lead Conversion Time,Время конверсии Обращения,
Due Date Based On,Дата составления финансовой отчетности,
Linked Documents,Связанные документы,
Steps,Шаги,
email,Эл. адрес,
Component,Компонент,
Subtitle,Подзаголовок,
Prefix,Префикс,
Is Public,Публично,
This chart will be available to all Users if this is set,"Эта диаграмма будет доступна всем пользователям, если это установлено.",
Number Card,Номер карты,
Function,Функция,
Minimum,Минимум,
Maximum,Максимум,
This card will be available to all Users if this is set,"Эта карта будет доступна всем пользователям, если она установлена.",
Stats,Статистика,
Show Percentage Stats,Показать статистику в процентах,
Stats Time Interval,Статистика Интервал времени,
Show percentage difference according to this time interval,Показать процентную разницу в соответствии с этим временным интервалом,
Filters Section,Раздел фильтров,
Number Card Link,Номер карты Ссылка,
Card,Карта,
API Access,Доступ к API,
Access Key Secret,Секретный ключ доступа,
S3 Bucket Details,Детали хранилища S3,
Bucket Name,Название сегмента,
Backup Details,Детали резервного копирования,
Backup Files,Резервные файлы,
Backup public and private files along with the database.,Резервное копирование общедоступных и частных файлов вместе с базой данных.,
Set to 0 for no limit on the number of backups taken,"Установите значение 0, чтобы не было ограничений на количество создаваемых резервных копий.",
Meta Description,Мета описание,
Meta Image,Мета-изображение,
Google Snippet Preview,Предварительный просмотр фрагмента кода Google,
This is an example Google SERP Preview.,Это пример предварительного просмотра Google SERP.,
Add Gray Background,Добавить серый фон,
Hide Block,Скрыть блок,
This Week,Эта неделя,
This Month,Этот месяц,
This Quarter,Этот квартал,
This Year,Этот год,
All Time,Все время,
Select From Date,Выбрать с даты,
since yesterday,со вчерашнего дня,
since last week,с прошлой недели,
since last month,с прошлого месяца,
since last year,с прошлого года,
Show,Показать,
New Number Card,Карточка с новым номером,
Your Shortcuts,Ваши ярлыки,
You haven't added any Dashboard Charts or Number Cards yet.,Вы еще не добавили диаграммы или карточки с показателями.,
Click On Customize to add your first widget,"Нажмите Настроить, чтобы добавить свой первый виджет.",
Are you sure you want to reset all customizations?,"Вы уверены, что хотите сбросить все настройки?",
"Couldn't save, please check the data you have entered","Не удалось сохранить, проверьте данные, которые вы ввели",
Validation Error,Ошибка проверки,
"You can only upload JPG, PNG, PDF, or Microsoft documents.","Вы можете загружать только документы в форматах JPG, PNG, PDF или документы Microsoft.",
Reverting length to {0} for '{1}' in '{2}'. Setting the length as {3} will cause truncation of data.,Возврат длины к {0} для &#39;{1}&#39; в &#39;{2}&#39;. Установка длины как {3} вызовет усечение данных.,
'{0}' not allowed for type {1} in row {2},&#39;{0}&#39; не разрешено для типа {1} в строке {2},
Option {0} for field {1} is not a child table,Вариант {0} для поля {1} не является дочерней таблицей,
Invalid Option,Неверный вариант,
Request Body consists of an invalid JSON structure,Тело запроса состоит из недопустимой структуры JSON,
Invalid JSON,Неверный JSON,
Party GSTIN,Партия GSTIN,
GST State,Состояние НДС,
Andaman and Nicobar Islands,Андаманские и Никобарские острова,
Andhra Pradesh,Андхра-Прадеш,
Arunachal Pradesh,Аруначал-Прадеш,
Assam,Ассам,
Bihar,Бихар,
Chandigarh,Чандигарх,
Chhattisgarh,Чхаттисгарх,
Dadra and Nagar Haveli,Дадра и Нагар Хавели,
Daman and Diu,Даман и Диу,
Delhi,Дели,
Goa,Гоа,
Gujarat,Гуджарат,
Haryana,Харьяна,
Himachal Pradesh,Химачал-Прадеш,
Jammu and Kashmir,Джамму и Кашмир,
Jharkhand,Джаркханд,
Karnataka,Карнатака,
Kerala,Керала,
Lakshadweep Islands,Острова Лакшадвип,
Madhya Pradesh,Мадхья-Прадеш,
Maharashtra,Махараштра,
Manipur,Манипур,
Meghalaya,Мегхалая,
Mizoram,Мизорам,
Nagaland,Нагаленд,
Odisha,Одиша,
Other Territory,Другая территория,
Pondicherry,Пондичерри,
Punjab,Пенджаб,
Rajasthan,Раджастхан,
Sikkim,Сикким,
Tamil Nadu,Тамил Наду,
Telangana,Телангана,
Tripura,Трипура,
Uttar Pradesh,Уттар-Прадеш,
Uttarakhand,Уттаракханд,
West Bengal,Западная Бенгалия,
GST State Number,Номер штата GST,
Import from Google Sheets,Импорт из Google Таблиц,
Must be a publicly accessible Google Sheets URL,Должен быть общедоступным URL-адресом Google Таблиц.,
Refresh Google Sheet,Обновить таблицу Google,
Import File Errors and Warnings,Ошибки и предупреждения файла импорта,
"Successfully imported {0} records out of {1}. Click on Export Errored Rows, fix the errors and import again.","Успешно импортировано {0} записей из {1}. Щелкните «Экспортировать строки с ошибками», исправьте ошибки и повторите импорт.",
"Successfully imported {0} record out of {1}. Click on Export Errored Rows, fix the errors and import again.","Успешно импортирована {0} запись из {1}. Щелкните «Экспортировать строки с ошибками», исправьте ошибки и повторите импорт.",
"Successfully updated {0} records out of {1}. Click on Export Errored Rows, fix the errors and import again.","Успешно обновлено {0} записей из {1}. Щелкните «Экспортировать строки с ошибками», исправьте ошибки и повторите импорт.",
"Successfully updated {0} record out of {1}. Click on Export Errored Rows, fix the errors and import again.","Успешно обновлена {0} запись из {1}. Щелкните «Экспортировать строки с ошибками», исправьте ошибки и повторите импорт.",
Data Import Legacy,Устаревшая версия импорта данных,
Documents restored successfully,Документы успешно восстановлены,
Documents that were already restored,"Документы, которые уже были восстановлены",
Documents that failed to restore,"Документы, которые не удалось восстановить",
Document Restoration Summary,Сводка восстановления документа,
Hide Days,Скрыть дни,
Hide Seconds,Скрыть секунды,
Hide Border,Скрыть границу,
Index Web Pages for Search,Индексирование веб-страниц для поиска,
Action / Route,Действие / Маршрут,
Document Naming Rule,Правило именования документов,
Rule Conditions,Условия правила,
Digits,Цифры,
Example: 00001,Пример: 00001,
Counter,Счетчик,
Document Naming Rule Condition,Условие правила именования документов,
Installed Application,Установленное приложение,
Application Name,Имя приложения,
Application Version,Версия приложения,
Git Branch,Git Branch,
Installed Applications,Установленные приложения,
Navbar Item,Элемент навигационной панели,
Item Label,Метка товара,
Item Type,Тип объекта,
Separator,Разделитель,
Navbar Settings,Настройки навигационной панели,
Application Logo,Логотип приложения,
Logo Width,Ширина логотипа,
Dropdowns,Выпадающие списки,
Settings Dropdown,Раскрывающийся список настроек,
Help Dropdown,Раскрывающийся список справки,
Query / Script,Запрос / Скрипт,
"Filters will be accessible via <code>filters</code>. <br><br>Send output as <code>result = [result]</code>, or for old style <code>data = [columns], [result]</code>","Фильтры будут доступны через <code>filters</code> .<br><br> Отправить вывод как <code>result = [result]</code> , или для <code>data = [columns], [result]</code> старого стиля <code>data = [columns], [result]</code>",
Client Code,Код клиента,
Report Column,Столбец отчета,
Report Filter,Фильтр отчетов,
Wildcard Filter,Фильтр подстановочных знаков,
"Will add ""%"" before and after the query",Добавит &quot;%&quot; до и после запроса,
"Route: Example ""/desk""",Маршрут: пример &quot;/ desk&quot;,
Enable Onboarding,Включить адаптацию,
Password Reset Link Generation Limit,Предел создания ссылки для сброса пароля,
Hourly rate limit for generating password reset links,Лимит почасовой оплаты за создание ссылок для сброса пароля,
Include Web View Link in Email,Отправить документ Ссылка на веб-просмотр по электронной почте,
Enable Auto-deletion of Prepared Reports,Включить автоматическое удаление подготовленных отчетов,
Prepared Report Expiry Period (Days),Срок действия подготовленного отчета (дни),
System will automatically delete Prepared Reports after these many days since creation,Система автоматически удалит подготовленные отчеты по прошествии нескольких дней с момента создания.,
Package Document Type,Тип документа пакета,
Include Attachments,Включить вложения,
Overwrite,Перезаписать,
Package Publish Target,Цель публикации пакета,
Site URL,Адрес сайта,
Package Publish Tool,Инструмент публикации пакетов,
Click on the row for accessing filters.,Щелкните строку для доступа к фильтрам.,
Sites,Сайты,
Last Deployed On,Последнее развертывание,
Console Log,Журнал консоли,
"Set Default Options for all charts on this Dashboard (Ex: ""colors"": [""#d1d8dd"", ""#ff5858""])","Установите параметры по умолчанию для всех диаграмм на этой панели инструментов (например: «colors»: [«# d1d8dd», «# ff5858»])",
Use Report Chart,Использовать диаграмму отчета,
Heatmap,Тепловая карта,
Dynamic Filters,Динамические фильтры,
Dynamic Filters JSON,Динамические фильтры JSON,
Set Dynamic Filters,Установить динамические фильтры,
Click to Set Dynamic Filters,"Нажмите, чтобы установить динамические фильтры",
Hide Custom DocTypes and Reports,Скрыть пользовательские типы документов и отчеты,
Checking this will hide custom doctypes and reports cards in Links section,При установке этого флажка пользовательские типы документов и карточки отчетов в разделе &quot;Ссылки&quot; будут скрыты.,
DocType View,DocType View,
Which view of the associated DocType should this shortcut take you to?,К какому виду связанного DocType следует перейти по этому ярлыку?,
List View Settings,Настройки просмотра списка,
Maximum Number of Fields,Максимальное количество полей,
Module Onboarding,Подключение модуля,
System managers are allowed by default,Системные менеджеры разрешены по умолчанию,
Documentation URL,URL документации,
Is Complete,Завершено,
Alert,Предупреждение,
Document Link,Ссылка на документ,
Attached File,Прикрепленный файл,
Attachment Link,Прикрепленная ссылка,
Open Reference Document,Открыть справочный документ,
Custom Configuration,Пользовательская конфигурация,
Filters Configuration,Конфигурация фильтров,
Dynamic Filters Section,Раздел динамических фильтров,
Please create Card first,"Пожалуйста, сначала создайте карту",
Onboarding Permission,Разрешение на подключение,
Onboarding Step,Начальный этап,
Is Skipped,Пропущено,
Create Entry,Создать запись,
Update Settings,Обновить настройки,
Show Form Tour,Показать форму тура,
View Report,Посмотреть отчет,
Go to Page,Перейти на страницу,
Watch Video,Смотреть видео,
Show Full Form?,Показать полную форму?,
Show full form instead of a quick entry modal,Показать полную форму вместо модального окна быстрого ввода,
Report Reference Doctype,Ссылка на отчет Doctype,
Report Description,Описание отчета,
This will be shown to the user in a dialog after routing to the report,Это будет показано пользователю в диалоговом окне после перехода к отчету.,
Example: #Tree/Account,Пример: # Дерево / Аккаунт,
Callback Title,Заголовок обратного вызова,
Callback Message,Сообщение обратного вызова,
This will be shown in a modal after routing,Это будет показано в модальном окне после маршрутизации.,
Validate Field,Проверить поле,
Value to Validate,Значение для проверки,
Use % for any non empty value.,Используйте% для любого непустого значения.,
Video URL,URL видео,
Onboarding Step Map,Карта этапов адаптации,
Step,Шаг,
System Console,Системная консоль,
Console,Консоль,
To print output use <code>log(text)</code>,Для вывода на печать используйте <code>log(text)</code>,
Commit,Зафиксировать,
Execute Console script,Выполнить скрипт консоли,
Execute,Выполнить,
Create Contacts from Incoming Emails,Создание контактов из входящих писем,
Inbox User,Входящие пользователя,
Disabled Auto Reply,Отключен автоматический ответ,
Schedule Sending,Расписание отправки,
Message (Markdown),Сообщение (Markdown),
Message (HTML),Сообщение (HTML),
Send Attachments,Отправить вложения,
Testing,Тестирование,
System Notification,Системное уведомление,
Twilio Number,Число Twilio,
"To use WhatsApp for Business, initialize <a href=""#Form/Twilio Settings"">Twilio Settings</a>.","Чтобы использовать WhatsApp для бизнеса, инициализируйте <a href=""#Form/Twilio Settings"">настройки Twilio</a> .",
"To use Slack Channel, add a <a href=""#List/Slack%20Webhook%20URL/List"">Slack Webhook URL</a>.","Чтобы использовать Slack Channel, добавьте <a href=""#List/Slack%20Webhook%20URL/List"">URL-адрес Slack Webhook</a> .",
Send System Notification,Отправить системное уведомление,
"If enabled, the notification will show up in the notifications dropdown on the top right corner of the navigation bar.","Если этот параметр включен, уведомление будет отображаться в раскрывающемся списке уведомлений в правом верхнем углу панели навигации.",
Send To All Assignees,Отправить всем уполномоченным,
Receiver By Document Field,Получатель по полю документа,
Receiver By Role,Получатель по роли,
Child Table,Дочерняя таблица,
Remote Value Filters,Удаленные фильтры значений,
API Key of the user(Event Subscriber) on the producer site,API-ключ пользователя (подписчика на событие) на сайте производителя,
API Secret of the user(Event Subscriber) on the producer site,API Secret пользователя (подписчика на событие) на сайте производителя,
Paytm Settings,Настройки Paytm,
Merchant Key,Торговый ключ,
Staging,Постановка,
Industry Type ID,ID типа отрасли,
See https://docs.aws.amazon.com/general/latest/gr/s3.html for details.,Подробности см. На https://docs.aws.amazon.com/general/latest/gr/s3.html.,
af-south-1,аф-юг-1,
ap-east-1,ап-восток-1,
eu-south-1,ес-юг-1,
me-south-1,мне-юг-1,
Twilio Number Group,Группа номеров Twilio,
Twilio Settings,Настройки Twilio,
Auth Token,Токен аутентификации,
Read Time,Время на чтение,
in minutes,в минутах,
Featured,Рекомендуемые,
Hide CTA,Скрыть CTA,
"Description for listing page, in plain text, only a couple of lines. (max 200 characters)","Описание страницы с листингом в виде простого текста, всего пара строк. (не более 200 символов)",
Meta Title,Мета-заголовок,
Enable Social Sharing,Включить обмен в социальных сетях,
Show CTA in Blog,Показать CTA в блоге,
CTA,CTA,
CTA Label,Метка CTA,
CTA URL,CTA URL,
Default Portal Home,Главная страница портала по умолчанию,
"Example: ""/desk""","Пример: ""/desk""",
Social Link Settings,Настройки социальных ссылок,
Social Link Type,Тип социальной ссылки,
facebook,facebook,
linkedin,linkedin,
twitter,twitter,
"If Icon is set, it will be shown instead of Label","Если установлен значок, он будет отображаться вместо метки.",
Apply Document Permissions,Применить разрешения документа,
"For help see <a href=""https://frappeframework.com/docs/user/en/guides/portal-development/web-forms"" target=""_blank"">Client Script API and Examples</a>","Для получения справки см. <a href=""https://frappeframework.com/docs/user/en/guides/portal-development/web-forms"" target=""_blank"">API клиентских скриптов и примеры.</a>",
Dynamic Route,Динамический маршрут,
Map route parameters into form variables. Example <code>/project/&lt;name&gt;</code>,Сопоставьте параметры маршрута с переменными формы. Пример <code>/project/&lt;name&gt;</code>,
Context Script,Сценарий контекста,
"<p>Set context before rendering a template. Example:</p><p>\n</p><div><pre><code>\ncontext.project = frappe.get_doc(""Project"", frappe.form_dict.name)\n</code></pre></div>","<p>Установите контекст перед рендерингом шаблона. Пример:</p><p></p><div><pre> <code>context.project = frappe.get_doc(&quot;Project&quot;, frappe.form_dict.name)</code></pre></div>",
Title of the page,Заголовок страницы,
This title will be used as the title of the webpage as well as in meta tags,"Этот заголовок будет использоваться как заголовок веб-страницы, а также в метатегах.",
Makes the page public,Делает страницу общедоступной,
Checking this will publish the page on your website and it'll be visible to everyone.,"Если вы выберете этот параметр, страница будет опубликована на вашем веб-сайте и будет видна всем.",
URL of the page,URL страницы,
"This will be automatically generated when you publish the page, you can also enter a route yourself if you wish","Он будет автоматически сгенерирован при публикации страницы, вы также можете ввести маршрут самостоятельно, если хотите.",
Content type for building the page,Тип контента для построения страницы,
"You can select one from the following,","Вы можете выбрать один из следующих,",
Standard rich text editor with controls,Стандартный редактор форматированного текста с элементами управления,
Github flavoured markdown syntax,Синтаксис markdown в стиле Github,
HTML with jinja support,HTML с поддержкой jinja,
Frappe page builder using components,Конструктор страниц Frappe с использованием компонентов,
Checking this will show a text area where you can write custom javascript that will run on this page.,"При выборе этого параметра будет отображаться текстовая область, в которой вы можете написать собственный javascript, который будет запускаться на этой странице.",
Meta title for SEO,Мета-заголовок для SEO,
"By default the title is used as meta title, adding a value here will override it.","По умолчанию заголовок используется как мета-заголовок, добавление здесь значения переопределит его.",
"The meta description is an HTML attribute that provides a brief summary of a web page. Search engines such as Google often display the meta description in search results, which can influence click-through rates.","Метаописание - это атрибут HTML, который предоставляет краткое описание веб-страницы. Поисковые системы, такие как Google, часто отображают метаописание в результатах поиска, что может повлиять на рейтинг кликов.",
"The meta image is unique image representing the content of the page. Images for this Card should be at least 280px in width, and at least 150px in height.","Мета-изображение - это уникальное изображение, представляющее содержимое страницы. Изображения для этой карты должны быть не менее 280 пикселей в ширину и не менее 150 пикселей в высоту.",
Add Space on Top,Добавить место сверху,
Add Space on Bottom,Добавить пространство внизу,
Is Unique,Уникальный,
User Agent,Пользовательский агент,
Table Break,Таблица Break,
Hide Login,Скрыть логин,
Navbar Template,Шаблон навигационной панели,
Navbar Template Values,Значения шаблона навигационной панели,
Call To Action,Призыв к действию,
Call To Action URL,URL призыва к действию,
Footer Logo,Логотип нижнего колонтитула,
Footer Template,Шаблон нижнего колонтитула,
Footer Template Values,Значения шаблона нижнего колонтитула,
Enable Tracking Page Views,Включить отслеживание просмотров страниц,
"Checking this will enable tracking page views for blogs, web pages, etc.","Установка этого флажка позволит отслеживать просмотры страниц для блогов, веб-страниц и т. Д.",
Disable Signup for your site,Отключить регистрацию для вашего сайта,
Check this if you don't want users to sign up for an account on your site. Users won't get desk access unless you explicitly provide it.,"Установите этот флажок, если вы не хотите, чтобы пользователи регистрировали учетную запись на вашем сайте. Пользователи не получат доступ к рабочему столу, если вы явно не предоставите его.",
URL to go to on clicking the slideshow image,URL-адрес для перехода при нажатии на изображение слайд-шоу,
Custom Overrides,Пользовательские переопределения,
Ignored Apps,Игнорируемые приложения,
Include Theme from Apps,Включить тему из приложений,
Website Theme Ignore App,Приложение &quot;Игнорировать тему веб-сайта&quot;,
Are you sure you want to save this document?,"Вы уверены, что хотите сохранить этот документ?",
Refresh All,Обновить все,
"Level 0 is for document level permissions, higher levels for field level permissions.","Уровень 0 предназначен для разрешений на уровне документа, более высокие уровни - для разрешений на уровне поля.",
Website Analytics,Веб-аналитика,
d,д,Days (Field: Duration)
h,ч,Hours (Field: Duration)
m,м,Minutes (Field: Duration)
s,с,Seconds (Field: Duration)
Less,Меньше,
Not a valid DocType view:,Недопустимое представление DocType:,
Unknown View,Неизвестный вид,
Go Back,Вернуться назад,
Let's take you back to onboarding,Вернемся к адаптации,
Great Job,Прекрасная работа,
Looks Great,Выглядит отлично,
Looks like you didn't change the value,"Похоже, вы не меняли значение",
Oops,Упс,
Skip Step,Пропустить шаг,
"You're doing great, let's take you back to the onboarding page.","У вас все отлично, давайте вернемся на страницу адаптации.",
Good Work 🎉,Хорошая работа 🎉,
Submit this document to complete this step.,"Отправьте этот документ, чтобы завершить этот шаг.",
Great,Отлично,
You may continue with onboarding,Вы можете продолжить регистрацию,
You seem good to go!,"Кажется, ты в порядке!",
Onboarding Complete,Посадка завершена,
{0} Settings,{0} Настройки,
{0} Fields,{0} Поля,
Reset Fields,Очистить поля,
Select Fields,Выбрать поля,
Warning: Unable to find {0} in any table related to {1},"Предупреждение: невозможно найти {0} ни в одной таблице, связанной с {1}",
Tree view is not available for {0},Просмотр в виде дерева недоступен для {0},
Create Card,Создать карту,
Card Label,Метка карты,
Reports already in Queue,Отчеты уже в очереди,
Proceed Anyway,Все равно продолжайте,
Delete and Generate New,Удалить и создать новое,
1 Report,1 отчет,
{0} ({1}) (1 row mandatory),{0} ({1}) (1 строка обязательна),
Select Fields To Insert,Выберите поля для вставки,
Select Fields To Update,Выберите поля для обновления,
"This document is already amended, you cannot ammend it again","В этот документ уже внесены поправки, вы не можете изменить его снова",
Add to ToDo,Добавить в список задач,
{0} is currently {1},{0} в настоящее время {1},
{0} are currently {1},{0} в настоящее время {1},
Currently Replying,Отвечаю сейчас,
created {0},создано {0},
Change,Изменить,Coins
Too Many Requests,Слишком много запросов,
"Invalid Authorization headers, add a token with a prefix from one of the following: {0}.","Недопустимые заголовки авторизации, добавьте токен с одним из следующих префиксов: {0}.",
"Invalid Authorization Type {0}, must be one of {1}.","Недействительный тип авторизации {0}, должен быть одним из {1}.",
{} is not a valid date string.,{} не является допустимой строкой даты.,
Invalid Date,Недействительная дата,
Please select a valid date filter,"Пожалуйста, выберите действующий фильтр даты",
Value {0} must be in the valid duration format: d h m s,Значение {0} должно иметь допустимый формат продолжительности: д ч м с,
Google Sheets URL is invalid or not publicly accessible.,URL-адрес Google Таблиц недействителен или не является общедоступным.,
"Google Sheets URL must end with ""gid={number}"". Copy and paste the URL from the browser address bar and try again.",URL-адрес Google Таблиц должен заканчиваться на &quot;gid = {number}&quot;. Скопируйте и вставьте URL-адрес из адресной строки браузера и повторите попытку.,
Incorrect URL,Неверный URL,
"""{0}"" is not a valid Google Sheets URL","""{0}"" не является действительным URL-адресом Google Таблиц",
Duplicate Name,Повторяющееся имя,
"Please check the value of ""Fetch From"" set for field {0}","Проверьте значение параметра &quot;Получить из&quot;, установленное для поля {0}.",
Wrong Fetch From value,Неверное значение Fetch From,
A field with the name '{}' already exists in doctype {}.,Поле с именем &#39;{}&#39; уже существует в doctype {}.,
Custom Field {0} is created by the Administrator and can only be deleted through the Administrator account.,Настраиваемое поле {0} создается администратором и может быть удалено только через учетную запись администратора.,
Failed to send {0} Auto Email Report,Не удалось отправить отчет по электронной почте {0},
Test email sent to {0},Тестовое письмо отправлено на адрес {0},
Email queued to {0} recipients,Электронная почта поставлена в очередь получателям: {0},
Newsletter should have at least one recipient,У рассылки должен быть хотя бы один получатель.,
Please enable Twilio settings to send WhatsApp messages,"Пожалуйста, включите настройки Twilio для отправки сообщений WhatsApp",
"Not allowed to attach {0} document, please enable Allow Print For {0} in Print Settings","Не разрешено прикреплять документ {0}, включите &quot;Разрешить печать для {0}&quot; в настройках печати",
Signup Disabled,Регистрация отключена,
Signups have been disabled for this website.,Регистрация на этом веб-сайте отключена.,
Open Document,Открыть документ,
The comment cannot be empty,Комментарий не может быть пустым,
Hourly comment limit reached for: {0},Достигнут лимит почасовых комментариев для: {0},
Please add a valid comment.,"Пожалуйста, добавьте действительный комментарий.",
Document {0} Already Restored,Документ {0} уже восстановлен,
Restoring Deleted Document,Восстановление удаленного документа,
{function} of {fieldlabel},{function} из {fieldlabel},
Invalid template file for import,Неверный файл шаблона для импорта,
Invalid or corrupted content for import,Недействительный или поврежденный контент для импорта,
Value {0} must in {1} format,Значение {0} должно быть в формате {1},
{0} is a mandatory field asdadsf,{0} - обязательное поле asdadsf,
Could not map column {0} to field {1},Не удалось сопоставить столбец {0} с полем {1},
Skipping Duplicate Column {0},Пропуск повторяющегося столбца {0},
The column {0} has {1} different date formats. Automatically setting {2} as the default format as it is the most common. Please change other values in this column to this format.,"Столбец {0} имеет {1} разные форматы даты. Автоматическая установка {2} в качестве формата по умолчанию, поскольку он является наиболее распространенным. Измените другие значения в этом столбце на этот формат.",
You have reached the hourly limit for generating password reset links. Please try again later.,"Вы достигли лимита на создание ссылок для сброса пароля. Пожалуйста, попробуйте позже.",
Please hide the standard navbar items instead of deleting them,Скройте стандартные элементы навигационной панели вместо их удаления,
DocType's name should not start or end with whitespace,Имя DocType не должно начинаться или заканчиваться пробелом,
File name cannot have {0},Имя файла не может содержать {0},
{0} is not a valid file url,{0} не является допустимым URL-адресом файла,
Error Attaching File,Ошибка при прикреплении файла,
Please generate keys for the Event Subscriber User {0} first.,Сначала сгенерируйте ключи для пользователя-подписчика на событие {0}.,
Please set API Key and Secret on the producer and consumer sites first.,"Пожалуйста, сначала установите API Key и Secret на сайтах производителя и потребителя.",
User {0} not found on the producer site,Пользователь {0} не найден на сайте производителя,
Event Subscriber has to be a System Manager.,Подписчик на событие должен быть системным менеджером.,
Row #{0}: Invalid Local Fieldname,Строка № {0}: недопустимое локальное имя поля,
Row #{0}: Please set Mapping or Default Value for the field {1} since its a dependency field,"Строка № {0}: установите сопоставление или значение по умолчанию для поля {1}, поскольку это поле зависимости",
Row #{0}: Please set remote value filters for the field {1} to fetch the unique remote dependency document,"Строка № {0}: установите фильтры удаленных значений для поля {1}, чтобы получить уникальный документ удаленной зависимости.",
Paytm payment gateway settings,Настройки платежного шлюза Paytm,
"Company, Fiscal Year and Currency defaults","Компания, финансовый год и валюта по умолчанию",
Razorpay Signature Verification Failed,Ошибка проверки подписи Razorpay,
Google Drive - Could not locate - {0},Google Диск - не удалось найти - {0},
"Sync token was invalid and has been resetted, Retry syncing.",Токен синхронизации был недействителен и был сброшен. Повторите попытку синхронизации.,
Please select another payment method. Paytm does not support transactions in currency '{0}',"Пожалуйста, выберите другой способ оплаты. Paytm не поддерживает транзакции в валюте &#39;{0}&#39;",
Invalid Account SID or Auth Token.,Недействительный SID учетной записи или токен аутентификации.,
Please enable twilio settings before sending WhatsApp messages,"Пожалуйста, включите настройки Twilio перед отправкой сообщений WhatsApp",
Delivery Failed,Доставка не удалась,
Twilio WhatsApp Message Error,Ошибка сообщения Twilio WhatsApp,
A featured post must have a cover image,В избранном посте должна быть обложка.,
Load More,Показать больше,
Published on,Опубликован,
Enable developer mode to create a standard Web Template,"Включите режим разработчика, чтобы создать стандартный веб-шаблон",
Was this article helpful?,Эта статья была полезной?,
Thank you for your feedback!,Спасибо за ваш отзыв!,
New Mention on {0},Новое упоминание о {0},
Assignment Update on {0},Обновление задания на {0},
New Document Shared {0},Новый документ опубликован {0},
Energy Point Update on {0},Обновление баллов активности от {0},
You cannot create a dashboard chart from single DocTypes,Вы не можете создать диаграмму панели мониторинга из одного типа документов,
Invalid json added in the custom options: {0},В настраиваемые параметры добавлен недопустимый json: {0},
Invalid JSON in card links for {0},Недействительный JSON в ссылках на карточки для {0},
Standard Not Set,Стандарт не установлен,
Please set the following documents in this Dashboard as standard first.,"Пожалуйста, сначала установите следующие документы на этой панели инструментов как стандартные.",
Shared with the following Users with Read access:{0},Доступно следующим пользователям с доступом на чтение: {0},
Already in the following Users ToDo list:{0},Уже в следующем списке задач пользователей: {0},
Your assignment on {0} {1} has been removed by {2},Ваше задание на {0} {1} было удалено {2},
Invalid Credentials,Неверные учетные данные,
Print UOM after Quantity,Печать единиц измерения после количества,
Uncaught Server Exception,Неперехваченное исключение сервера,
There was an error building this page,При создании этой страницы произошла ошибка,
Hide Traceback,Скрыть трассировку,
Value from this field will be set as the due date in the ToDo,Значение из этого поля будет установлено как срок выполнения в ToDo,
New module created {0},Создан новый модуль {0},
"Report has no numeric fields, please change the Report Name","В отчете нет числовых полей, измените название отчета.",
There are documents which have workflow states that do not exist in this Workflow. It is recommended that you add these states to the Workflow and change their states before removing these states.,"Есть документы, в которых есть состояния рабочего процесса, которых нет в этом рабочем процессе. Рекомендуется добавить эти состояния в рабочий процесс и изменить их состояния перед удалением этих состояний.",
Worflow States Don't Exist,Состояния Worflow не существуют,
Save Anyway,Все равно сохранить,
Energy Points:,Баллы активности:,
Review Points:,Баллы обзора:,
Rank:,Рейтинг:,
Monthly Rank:,Месячный рейтинг:,
Invalid expression set in filter {0} ({1}),Недопустимое выражение в фильтре {0} ({1}),
Invalid expression set in filter {0},В фильтре {0} задано недопустимое выражение,
{0} {1} added to Dashboard {2},{0} {1} добавлен в Личный кабинет {2},
Set Filters for {0},Установить фильтры для {0},
Not permitted to view {0},Не разрешено просматривать {0},
Camera,Камера,
Invalid filter: {0},Недействительный фильтр: {0},
Let's Get Started,Давайте начнем,
Reports & Masters,Отчеты и мастеры,
New {0} {1} added to Dashboard {2},Новый {0} {1} добавлен на панель инструментов {2},
New {0} {1} created,Создан новый {0} {1},
New {0} Created,Новый {0} создан,
"Invalid ""depends_on"" expression set in filter {0}",В фильтре {0} задано недопустимое выражение &quot;depends_on&quot;,
{0} Reports,{0} Отчеты,
There is {0} with the same filters already in the queue:,В очереди уже есть {0} с такими фильтрами:,
There are {0} with the same filters already in the queue:,В очереди уже есть {0} с такими фильтрами:,
Are you sure you want to generate a new report?,"Вы уверены, что хотите создать новый отчет?",
{0}: {1} vs {2},{0}: {1} против {2},
Add a {0} Chart,Добавить диаграмму {0},
Currently you have {0} review points,В настоящее время у вас есть {0} баллов обзора,
{0} is not a valid DocType for Dynamic Link,{0} не является допустимым DocType для динамической ссылки,
via Assignment Rule,через Правило присвоения,
Based on Field,На основе поля,
Assign to the user set in this field,"Назначьте пользователю, указанному в этом поле",
Log Setting User,Пользователь настройки журнала,
Log Settings,Настройки журнала,
Error Log Notification,Уведомление журнала ошибок,
Users To Notify,Пользователи для уведомления,
Log Cleanup,Очистка журнала,
Clear Error log After,Очистить журнал ошибок через,
Clear Activity Log After,Очистить журнал активности через,
Clear Email Queue After,Очистить очередь электронной почты после,
Please save to edit the template.,"Сохраните, чтобы отредактировать шаблон.",
Google Analytics Anonymize IP,Google Analytics анонимизирует IP,
Incorrect email or password. Please check your login credentials.,"Неверный адрес электронной почты или пароль. Пожалуйста, проверьте свои учетные данные.",
Incorrect Configuration,Неправильная конфигурация,
You are not allowed to delete Standard Report,Вам не разрешено удалять стандартный отчет,
You have unseen {0},Вы не видели {0},
Log cleanup and notification configuration,Очистка журнала и настройка уведомлений,
State/Province,Штат / провинция,
Document Actions,Действия с документами,
Document Links,Ссылки на документы,
List Settings,Настройки списка,
Cannot delete standard link. You can hide it if you want,"Невозможно удалить стандартную ссылку. Вы можете скрыть это, если хотите",
Cannot delete standard action. You can hide it if you want,"Невозможно удалить стандартное действие. Вы можете скрыть это, если хотите",
Applied On,Применено,
Row Name,Название строки,
For DocType Link / DocType Action,Для DocType ссылки / DocType действия,
Cannot edit filters for standard charts,Невозможно редактировать фильтры для стандартных диаграмм,
Event Producer Last Update,Последнее обновление Event Producer,
Default for 'Check' type of field {0} must be either '0' or '1',"По умолчанию для типа поля &quot;Проверка&quot; {0} должен быть либо &quot;0&quot;, либо &quot;1&quot;.",
Non Negative,Не отрицательный,
Rules with higher priority number will be applied first.,Сначала будут применяться правила с более высоким номером приоритета.,
Open URL in a New Tab,Открыть URL в новой вкладке,
Align Right,Выровнять по правому краю,
Loading Filters...,Загрузка фильтров...,
Count Customizations,Подсчет настроек,
For Example: {} Open,Например: {} Открыто,
Choose Existing Card or create New Card,Выберите существующую карту или создайте новую карту,
Number Cards,Числовые карты,
Function Based On,Функция на основе,
Add Filters,Добавить фильтры,
Skip,Пропустить,
Dismiss,Отклонить,
Value cannot be negative for,Значение не может быть отрицательным для,
Value cannot be negative for {0}: {1},Значение не может быть отрицательным для {0}: {1},
Negative Value,Отрицательное значение,
Authentication failed while receiving emails from Email Account: {0}.,Ошибка аутентификации при получении писем из учетной записи электронной почты: {0}.,
Message from server: {0},Сообщение с сервера: {0},
Documentation,Документация,
User Forum,Форум пользователей,
Report an Issue,Сообщить об ошибке,
About,О системе,
My Profile,Мой профиль,
My Settings,Мои настройки,
Toggle Full Width,Переключить ширину,
Toggle Theme,Переключить тему,
Modules,Модули,
You created this,Вы создали это,
{0} created this,{0} создал(а) это,
You last edited this,Вы последний раз редактировали это,
{0} last edited this,{0} последний раз редактировалось это,
You viewed this,Вы просмотрели это,
{0} viewed this,{0} просмотрел(а) это,
Apply Filters,Применить фильтры,
+ Add a Filter,+ Добавить фильтр,
Show Saved,Показать сохраненные,
Hide Saved,Скрыть сохраненные,
Add Tags,Добавить теги,
Page Size,Формат страницы,
Set all public,Сделать публичными,
Set all private,Сделать приватными,
Drag and drop files here or upload from,Перетащите файлы сюда или загрузите из,
My Device,Моё устройство,
Library,Библиотека,
Add Row,Добавить строку,
Analytics,Аналитика,
Anonymous,анонимное,
Author,Автор,
Basic,Основной,
Billing,Выставление счетов,
Contact Details,Контактная информация,
Datetime,Дата и время,
Enable, Разрешить,
Event,Событие,
Full,Полный,
Insert,Вставить,
Interests,Интересы,
Language Name,Название языка,
License,Лицензия,
Limit,Предел,
Log,Запись в журнале,
Meeting,Встреча,
My Account,Мой аккаунт,
Newsletters,Информационная рассылка,
Password,Пароль,
Pincode,PIN код,
Please select prefix first,"Пожалуйста, выберите префикс первым",
Please set Email Address,"Пожалуйста, установите адрес электронной почты",
Please set the series to be used.,"Пожалуйста, установите серию, которая будет использоваться.",
Portal Settings,Настройки портала,
Reference Owner,Владелец ссылки,
Region,Область,
Report Builder,Конструктор отчетов,
Sample,Образец,
Saved,Сохраненные,
Series {0} already used in {1},Идентификатор {0} уже используется в {1},
Set as Default,"Установить ""По умолчанию""",
Shipping,Доставка,
Standard,Стандартный,
Test,Тест,
Traceback,Диагностика,
Unable to find DocType {0},Не удалось найти DocType {0},
Weekdays,Будни,
Workflow,Рабочий процесс,
You need to be logged in to access this page,"Вы должны быть войти в систему, чтобы открыть эту страницу",
County,Округ,
Images,Изображении,
Office,Офис,
Passive,Пассивный,
Permanent,Постоянный,
Plant,Завод,
Postal,Почтовый,
Previous,Предыдущая,
Shop,Магазин,
Subsidiary,Филиал,
There is some problem with the file url: {0},Существует некоторая проблема с файловой URL: {0},
"Special Characters except '-', '#', '.', '/', '{{' and '}}' not allowed in naming series {0}","Специальные символы, кроме &quot;-&quot;, &quot;#&quot;, &quot;।&quot;, &quot;/&quot;, &quot;{{&quot; и &quot;}}&quot;, не допускаются в серийных номерах {0}",
Export Type,Тип экспорта,
Last Sync On,Последняя синхронизация,
Webhook Secret,Webhook Secret,
Back to Home,Вернуться домой,
Customize,Настроить,
Edit Profile,Редактировать профиль,
File Manager,Файловый менеджер,
Invite as User,Пригласить в пользователя,
Newsletter,Рассылка новостей,
Printing,Печать,
Publish,Публиковать,
Refreshing,Обновление,
Select All,Выбрать все,
Set,Комплект,
Setup Wizard,Мастер установки,
Update Details,Обновить данные,
You,Вы,
{0} Name,{0} Имя,
Bold,Жирный,
Center,Центр,
Comment,Комментарий,
Not Found,Не найдено,
User Id,ID пользователя,
Position,Позиция,
Crop,Урожай,
Topic,Тема,
Public Transport,Общественный транспорт,
Request Data,Запросить данные,
Steps,Шаги,
Reference DocType,Ссылка DocType,
Select Transaction,Выберите операцию,
Help HTML,Помощь HTML,
Series List for this Transaction,Список Идентификаторов для этой транзакции,
User must always select,Пользователь всегда должен выбирать,
Check this if you want to force the user to select a series before saving. There will be no default if you check this.,"Отметьте это, если вы хотите заставить пользователя выбрать идентификатор перед сохранением. Там не будет варианта по умолчанию, если вы поставите эту отметку.",
Prefix,Префикс,
This is the number of the last created transaction with this prefix,Это число последнего созданного сделки с этим префиксом,
Update Series Number,Обновить серийный номер,
Validation Error,Ошибка проверки,
Andaman and Nicobar Islands,Андаманские и Никобарские острова,
Andhra Pradesh,Андхра-Прадеш,
Arunachal Pradesh,Аруначал-Прадеш,
Assam,Ассам,
Bihar,Бихар,
Chandigarh,Чандигарх,
Chhattisgarh,Чхаттисгарх,
Dadra and Nagar Haveli,Дадра и Нагар Хавели,
Daman and Diu,Даман и Диу,
Delhi,Дели,
Goa,Гоа,
Gujarat,Гуджарат,
Haryana,Харьяна,
Himachal Pradesh,Химачал-Прадеш,
Jammu and Kashmir,Джамму и Кашмир,
Jharkhand,Джаркханд,
Karnataka,Карнатака,
Kerala,Керала,
Lakshadweep Islands,Острова Лакшадвип,
Madhya Pradesh,Мадхья-Прадеш,
Maharashtra,Махараштра,
Manipur,Манипур,
Meghalaya,Мегхалая,
Mizoram,Мизорам,
Nagaland,Нагаленд,
Odisha,Одиша,
Other Territory,Другая территория,
Pondicherry,Пондичерри,
Punjab,Пенджаб,
Rajasthan,Раджастхан,
Sikkim,Сикким,
Tamil Nadu,Тамил Наду,
Telangana,Телангана,
Tripura,Трипура,
Uttar Pradesh,Уттар-Прадеш,
Uttarakhand,Уттаракханд,
West Bengal,Западная Бенгалия,
Published on,Опубликован в,
Bottom,Низ,
Top,Верх,
Amend,Исправить,
