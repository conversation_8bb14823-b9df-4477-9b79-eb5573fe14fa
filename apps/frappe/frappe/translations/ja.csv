A4,A4,
<PERSON> Endpoint,APIエンドポイント,
API Key,APIキー,
Access Token,アクセストークン,
Account,アカウント,
Accounts Manager,会計管理者,
Accounts User,会計ユーザー,
Action,アクション,
Actions,動作,
Active,アクティブ,
Add,追加,
Add Row,行の追加,
Address,住所,
Address Line 2,住所2行目,
Address Title,住所タイトル,
Address Type,住所タイプ,
Administrator,管理者,
All Day,一日中,
Allow Delete,削除を許可する,
Amended From,修正元,
Amount,額,
Applicable For,適用可能なもの,
Approval Status,承認ステータス,
Assign,割当,
Assign To,割当先,
Attachment,添付,
Attachments,添付,
Author,著者,
Auto Repeat,自動繰り返し,
Base URL,ベースURL,
Based On,参照元,
Beginner,初心者,
Billing,請求,
Cancel,キャンセル,
Category,カテゴリー,
Category Name,カテゴリ名,
City,都市,
City/Town,市町村,
Client,クライアント,
Client ID,クライアントID,
Client Secret,Client Secret,
Closed,クローズ,
Code,コード,
Collapse All,すべて折りたたみます,
Color,色,
Company Name,（会社名）,
Condition,条件,
Contact,連絡先,
Contact Details,連絡先の詳細,
Content,内容,
Content Type,コンテンツタイプ,
Create,作成,
Created By,によって作成された,
Current,現在,
Custom HTML,カスタムHTML,
Custom?,カスタム？,
Date Format,日付の表示形式,
Datetime,日時,
Day,日,
Default Letter Head,デフォルトレターヘッド,
Defaults,デフォルト,
Delivery Status,納品ステータス,
Department,部門,
Details,詳細,
Document Name,文書名,
Document Status,文書ステータス,
Document Type,文書タイプ,
Domain,ドメイン,
Domains,ドメイン,
Draft,ドラフト,
Edit,編集する,
Email Account,メールアカウント,
Email Address,電子メールアドレス,
Email ID,電子メールID,
Email Sent,メール送信済,
Email Template,メールテンプレート,
Enable,有効にする,
Enabled,有効,
End Date,終了日,
Error Code: {0},エラーコード：{0},
Error Log,エラーログ,
Event,イベント,
Expand All,すべて展開,
Fail,失敗,
Failed,失敗,
Fax,FAX,
Feedback,フィードバック,
Female,女性,
Field Name,フィールド名,
Fieldname,フィールド名,
Fields,フィールド,
First Name,お名前（名）,
Frequency,周波数,
Friday,金曜日,
From,から,
Full,フル,
Full Name,氏名,
Further nodes can be only created under 'Group' type nodes,これ以上のノードは「グループ」タイプのノードの下にのみ作成することができます,
Gender,性別,
GitHub Sync ID,GitHub Sync ID,
Guest,ゲスト,
Half Day,半日,
Half Yearly,半年ごと,
High,高,
Hourly,毎時,
Hub Sync ID,ハブ同期ID,
IP Address,IPアドレス,
Image,画像,
Image View,画像を見る,
Import Data,データのインポート,
Import Log,インポートログ,
Inactive,非アクティブ,
Insert,挿入,
Interests,興味,
Introduction,はじめに,
Is Active,アクティブ,
Is Completed,完成されました,
Is Default,デフォルト,
Kanban Board,かんばんボード,
Label,ラベル,
Language Name,言語名,
Last Name,お名前（姓）,
Leaderboard,リーダーボード,
Letter Head,レターヘッド,
Level,レベル,
Limit,リミット,
Log,ログ,
Logs,ログ,
Low,低,
Maintenance Manager,保守マネージャー,
Maintenance User,保守ユーザー,
Male,男性,
Mandatory,必須,
Mapping,マッピング,
Mapping Type,マッピングタイプ,
Medium,普通,
Meeting,会議,
Message Examples,メッセージの例,
Method,方法,
Middle Name,ミドルネーム,
Middle Name (Optional),ミドルネーム（任意）,
Monday,月曜日,
Monthly,月次,
More,続き,
More Information,詳細,
Move,移動,
My Account,自分のアカウント,
New Address,新しい住所,
New Contact,新しい連絡先,
Next,次,
No Data,データがありません,
No address added yet.,アドレスがまだ追加されていません,
No contacts added yet.,連絡先がまだ追加されていません,
No items found.,項目は見つかりませんでした。,
None,なし,
Not Permitted,許可されていません,
Not active,アクティブではありません,
Number,数,
Online,オンライン,
Operation,作業,
Options,オプション,
Other,その他,
Owner,所有者,
Page Missing or Moved,ページが存在しません（または移動されました）,
Parameter,パラメータ,
Password,パスワード,
Period,期間,
Pincode,郵便番号,
Please enable pop-ups,ポップアップを有効にしてください,
Please select Company,会社を選択してください,
Please select {0},{0}を選択してください,
Please set Email Address,メールアドレスを設定してください,
Portal Settings,ポータル設定,
Preview,プレビュー,
Primary,プライマリー,
Print Format,印刷書式,
Print Settings,印刷設定,
Print taxes with zero amount,金額ゼロの税金を印刷する,
Private,個人,
Property,属性,
Public,公開,
Published,公開済,
Purchase Manager,仕入マネージャー,
Purchase Master Manager,仕入マスターマネージャー,
Purchase User,仕入ユーザー,
Query Options,クエリーオプション,
Range,幅,
Rating,評価,
Received,受領,
Recipients,受信者,
Redirect URL,リダイレクトURL,
Reference,リファレンス,
Reference Date,参照日,
Reference Document,参照文書,
Reference Document Type,参照文書タイプ,
Reference Owner,参照オーナー,
Reference Type,参照タイプ,
Refresh Token,トークン再発行,
Region,地域,
Rejected,拒否,
Reopen,再オープン,
Replied,返答,
Report,レポート,
Report Builder,レポートビルダ,
Report Type,レポートタイプ,
Reports,レポート,
Response,応答,
Role,役割,
Route,ルート,
Sales Manager,営業部長,
Sales Master Manager,販売マスターマネージャー,
Sales User,販売ユーザー,
Salutation,敬称（例：Mr. Ms.）,
Sample,サンプル,
Saturday,土曜日,
Saved,保存済,
Scheduled,スケジュール設定済,
Search,検索,
Secret Key,秘密鍵,
Select,選択,
Select DocType,文書タイプを選択,
Send Now,今すぐ送信,
Sent,送信済,
Series {0} already used in {1},シリーズは、{0}はすでに{1}で使用されています,
Service,サービス,
Set as Default,デフォルトに設定,
Settings,設定,
Shipping,出荷,
Short Name,略名,
Slideshow,スライドショー,
Source,ソース,
Source Name,ソース名,
Standard,標準,
Start Date,開始日,
Start Import,インポートを開始する,
State,状態,
Stopped,停止,
Subject,タイトル,
Submit,提出,
Summary,概要,
Sunday,日曜日,
System Manager,システム管理者,
Target,ターゲット,
Task,タスク,
Tax Category,課税カテゴリー,
Test,テスト,
Thank you,ありがとうございます,
The page you are looking for is missing. This could be because it is moved or there is a typo in the link.,あなたが探しているページがありません。それが移動またはリンクにタイプミスがあるされているので、これは可能性があります。,
Timespan,期間,
To,に,
To Date,日付,
Traceback,トレースバック,
URL,URL,
Unsubscribed,購読解除,
User,ユーザー,
User ID,ユーザー ID,
Users,ユーザー,
Validity,正当性,
Warning,警告,
Website,ウェブサイト,
Website Manager,Webサイトマネージャー,
Website Settings,Webサイト設定,
Wednesday,水曜日,
Week,週,
Weekdays,平日,
Weekly,毎週,
Welcome email sent,ウェルカムメールが送信されました。,
Workflow,ワークフロー,
You need to be logged in to access this page,このページにアクセスするにはログインする必要があります,
old_parent,old_parent,
{0} is mandatory,{0}は必須です,
 to your browser,あなたのブラウザに,
"""Company History""",「沿革」,
"""Parent"" signifies the parent table in which this row must be added",「親」は、この行を追加する必要のある親テーブルを意味します,
"""Team Members"" or ""Management""",「チームメンバー」または「管理」,
&lt;head&gt; HTML,&lt;head&gt;HTML,
'In Global Search' not allowed for type {0} in row {1},行{1}の{0}型に対して &#39;グローバル検索中&#39;が許可されていません,
'In List View' not allowed for type {0} in row {1},「リスト表示」は行{1}のタイプ{0}では許可されません,
'Recipients' not specified,「受信者」が指定されていません,
(Ctrl + G),（Ctrl + G）,
** Failed: {0} to {1}: {2},**失敗しました：{0}の{1}：{2},
**Currency** Master,「通貨」マスター,
0 - Draft; 1 - Submitted; 2 - Cancelled,0 - ドラフト; 1 - 提出済; 2 - キャンセル,
0 is highest,0が上限です,
1 Currency = [?] Fraction\nFor e.g. 1 USD = 100 Cent,1通貨= [?] 小数部 例：1米ドル= 100 セント,
1 comment,1コメント,
1 hour ago,1時間前,
1 minute ago,1分前,
1 month ago,1ヶ月前,
1 year ago,1年前,
; not allowed in condition,;条件に許可されていない,
"<h4>Default Template</h4>\n<p>Uses <a href=""http://jinja.pocoo.org/docs/templates/"">Jinja Templating</a> and all the fields of Address (including Custom Fields if any) will be available</p>\n<pre><code>{{ address_line1 }}&lt;br&gt;\n{% if address_line2 %}{{ address_line2 }}&lt;br&gt;{% endif -%}\n{{ city }}&lt;br&gt;\n{% if state %}{{ state }}&lt;br&gt;{% endif -%}\n{% if pincode %} PIN:  {{ pincode }}&lt;br&gt;{% endif -%}\n{{ country }}&lt;br&gt;\n{% if phone %}Phone: {{ phone }}&lt;br&gt;{% endif -%}\n{% if fax %}Fax: {{ fax }}&lt;br&gt;{% endif -%}\n{% if email_id %}Email: {{ email_id }}&lt;br&gt;{% endif -%}\n</code></pre>","<h4>デフォルトのテンプレート<!-- H4--> \n <p>は（<a href=""http://jinja.pocoo.org/docs/templates/"">神社テンプレート化</a>と住所のすべてのフィールドを使用します{; BR＆GT; \n {％ならaddress_line2％} {{address_line2}}＆LT; BRの＆GTカスタムフィールドもしあれば）<!-- P--> \n &lt;前&gt; &lt;コード&gt; {{address_line1}}＆LT利用できるようになりますを含む％endifの - ％} \n {{都市}}＆LT; BRの＆GT; \n {％であれば、状態％} {{状態}}＆LT; BRの＆GT; {％endifの - ％} \n {％の場合PINコードの％} PIN：{{PINコード}}＆LT; BRの＆GT; {％endifの - ％} \n {{国}}＆LT; BRの＆GT; \n {％であれば、電話％}電話：{{電話}}＆LT; BRの＆GT; { ％endifの - ％} \n {％であれば、ファックス％}ファックス：{{ファクス}}＆LT; BRの＆GT; {％endifの - ％} \n {％email_id％}電子メールの場合：{{email_id}}＆LT; BRの＆GT ; {％endifの - ％} \n <!-- code-->を<!-- PRE--></p></h4>",
A Lead with this Email Address should exist,このメールアドレスを持つリードが存在している必要があります,
A list of resources which the Client App will have access to after the user allows it.<br> e.g. project,クライアントアプリケーションは、ユーザがそれを可能にした後にアクセスする必要がありますリソースのリスト。 <br>例えばプロジェクト,
A log of request errors,リクエストエラーのログ,
A new account has been created for you at {0},新しいアカウントは {0} に作成されています,
A symbol for this currency. For e.g. $,この通貨のシンボル（例：＄）,
A word by itself is easy to guess.,単語自体が推測容易です。,
API Endpoint Args,APIエンドポイント引数,
API Key cannot be  regenerated,APIキーを再生成できません,
API Password,APIパスワード,
API Secret,APIシークレット,
API Username,APIユーザー名,
ASC,ASC,
About Us Settings,会社の設定について,
About Us Team Member,問い合わせチームメンバーについて,
Accept Payment,支払承認,
Access Key ID,アクセスキーID,
Access Token URL,アクセストークンURL,
Action Failed,アクションは失敗しました。,
Action Timeout (Seconds),アクションタイムアウト（秒）,
"Actions for workflow (e.g. Approve, Cancel).",ワークフローのアクション（例：承認、キャンセル）,
Active Domains,アクティブドメイン,
Active Sessions,アクティブセッション,
Activity Log,活動ログ,
Activity log of all users.,全ユーザーの活動ログ,
Add / Manage Email Domains.,メールドメインの追加・管理,
Add / Update,追加/更新,
Add A New Rule,新しいルールを追加,
Add Another Comment,別のコメントを追加,
Add Attachment,添付ファイル追加,
Add Column,列の追加,
Add Contact,連絡先追加,
Add Contacts,連絡先を追加,
Add Filter,フィルタを追加,
Add Group,グループを追加,
Add New Permission Rule,新しいアクセス許可ルールの追加,
Add Review,レビューを追加,
Add Signature,署名を追加,
Add Subscribers,登録者を追加,
Add Total Row,合計行を追加,
Add Unsubscribe Link,解除リンクを追加,
Add User Permissions,ユーザー権限を追加する,
Add a New Role,新しい役割の追加,
Add a column,列を追加,
Add a comment,コメントを追加,
Add a new section,新しいセクションを追加,
Add a tag ...,タグを追加...,
Add all roles,すべての役割を追加する,
Add custom forms.,カスタムフォームを追加,
Add custom javascript to forms.,フォームへのカスタムJavaScriptを追加します。,
Add fields to forms.,フォームにフィールドを追加。,
Add meta tags to your web pages,Webページにメタタグを追加する,
Add script for Child Table,子テーブル用のスクリプトを追加する,
Add to table,テーブルに追加,
Add your own translations,独自の翻訳を追加,
"Added HTML in the &lt;head&gt; section of the web page, primarily used for website verification and SEO",主にWebサイトの検証とSEOのために使用するHTMLをセクションに追加,
Added {0},{0}を追加しました,
Adding System Manager to this User as there must be atleast one System Manager,少なくとも1システムマネージャーが存在しなければならないものとして、このユーザにシステムマネージャーの追加,
Additional Permissions,追加のアクセス許可,
Address Template,住所テンプレート,
Address Title is mandatory.,住所タイトルは必須です。,
Address and other legal information you may want to put in the footer.,住所やその他の法的情報（フッターに配置するなど）,
Addresses And Contacts,住所と連絡先,
Adds a client custom script to a DocType,クライアントのカスタムスクリプトをDocTypeに追加します,
Adds a custom field to a DocType,文書タイプにカスタムフィールドを追加,
Admin,管理者,
Administrator Logged In,管理者がログインしました,
Administrator accessed {0} on {1} via IP Address {2}.,管理者がIPアドレス{2}から{1}の{0}にアクセスしました。,
Advanced,高度な,
Advanced Control,高度な制御,
Advanced Search,詳細検索,
Align Labels to the Right,ラベルを右揃えにする,
Align Value,値を揃える,
All Images attached to Website Slideshow should be public,ウェブサイトのスライドショーに添付されているすべての画像は公開する必要があります,
All customizations will be removed. Please confirm.,全てのカスタマイズが削除されます。ご確認ください。,
"All possible Workflow States and roles of the workflow. Docstatus Options: 0 is""Saved"", 1 is ""Submitted"" and 2 is ""Cancelled""",ワークフローの状態とワークフローの役割です。 文書ステータスオプション：0は「保存済」、1は「提出済」、2は「キャンセル済」です。,
All-uppercase is almost as easy to guess as all-lowercase.,すべての大文字はすべて小文字として推測することはほとんど同じくらい簡単です。,
Allocated To,割当先,
Allow,許可,
Allow Bulk Edit,一括編集を許可する,
Allow Comments,コメントを許可する,
Allow Consecutive Login Attempts ,連続ログイン試行を許可する,
Allow Dropbox Access,Dropboxのアクセスを許可,
Allow Edit,編集を許可する,
Allow Guest to View,ユーザーレビュー表示することができます,
Allow Import (via Data Import Tool),インポートを許可する（データのインポートツールを経由して）,
Allow Incomplete Forms,不完全なフォームを許可する,
Allow Login After Fail,失敗後にログインを許可する,
Allow Login using Mobile Number,携帯電話番号でのログインを許可する,
Allow Login using User Name,ユーザー名でのログインを許可する,
Allow Modules,許可モジュール,
Allow Multiple,複数の許可,
Allow Print,印刷を許可する,
Allow Print for Cancelled,キャンセル済みの印刷を許可,
Allow Print for Draft,下書きの印刷を許可,
Allow Read On All Link Options,すべてのリンクオプションで読み取りを許可する,
Allow Rename,名前の変更を許可,
Allow Roles,役割を許可,
Allow Self Approval,自己承認を許可する,
Allow approval for creator of the document,文書作成者の承認を許可する,
Allow events in timeline,タイムラインでイベントを許可する,
Allow in Quick Entry,クイックエントリで許可する,
Allow on Submit,提出を許可,
Allow only one session per user,ユーザーごとに1つだけのセッションを許可します,
Allow page break inside tables,表内での改ページを許可する,
Allow saving if mandatory fields are not filled,必須フィールドが充填されていない場合は保存を許可します,
Allow user to login only after this hour (0-24),（0-24）以降の時間のみユーザーログインを許可,
Allow user to login only before this hour (0-24),（0-24）以前の時間のみユーザーログインを許可,
Allowed,許可されている,
Allowed In Mentions,言及で許可される,
"Allowing DocType, DocType. Be careful!",文書タイプを許可（慎重に！）,
Already Registered,既に登録されています,
Also adding the dependent currency field {0},依存する通貨フィールド {0} も追加する,
"Always add ""Draft"" Heading for printing draft documents",常に印刷ドラフト文書の見出し &quot;ドラフト&quot;を追加,
Always use Account's Email Address as Sender,常に送信者としてアカウントのメールアドレスを使用します,
Always use Account's Name as Sender's Name,送信者の名前として常にアカウントの名前を使用する,
Amend,修正,
Amending,修正,
Amount Based On Field,金額フィールドに基づいて,
Amount Field,金額フィールド,
Amount must be greater than 0.,量は0より大きくなければなりません。,
An error occured during the payment process. Please contact us.,支払い処理中にエラーが発生しました。お問い合わせください。,
An icon file with .ico extension. Should be 16 x 16 px. Generated using a favicon generator. [favicon-generator.org],.ico拡張子のアイコンファイル。 16x16ピクセルにする必要があります。ファビコンジェネレータを使用して生成されました。 [favicon-generator.org],
Ancestors Of,祖先,
Another transaction is blocking this one. Please try again in a few seconds.,別の取引がこれをブロックしています。数秒後にもう一度お試しください。,
"Another {0} with name {1} exists, select another name",{1} という名前の別の {0} が存在しますので、別の名前を選択してください,
Any string-based printer languages can be used. Writing raw commands requires knowledge of the printer's native language provided by the printer manufacturer. Please refer to the developer manual provided by the printer manufacturer on how to write their native commands. These commands are rendered on the server side using the Jinja Templating Language.,任意の文字列ベースのプリンタ言語を使用できます。生のコマンドを書くことはプリンター製造業者によって提供されたプリンターの母国語の知識を必要とします。ネイティブコマンドの書き方については、プリンタの製造元から提供されている開発者マニュアルを参照してください。これらのコマンドは、Jinja Templating Languageを使用してサーバー側にレンダリングされます。,
"Apart from System Manager, roles with Set User Permissions right can set permissions for other users for that Document Type.",システムマネージャとは別に、「ユーザー権限設定」の権限を持つ「役割」は、該当ドキュメントタイプに他のユーザーのアクセス権を設定することができます。,
Api Access,APIアクセス,
App,アプリ,
App Access Key,アプリのアクセスキー,
App Client ID,アプリのクライアントID,
App Client Secret,アプリのクライアントシークレット,
App Name,アプリケーション名,
App Secret Key,アプリの秘密鍵,
App not found,アプリケーションが見つかりません,
App {0} already installed,アプリ {0}は既にインストールされています,
App {0} is not installed,アプリケーション{0}がインストールされていません,
Append To,追加,
Append To can be one of {0},「追加先」は {0} のいずれかになります,
Append To is mandatory for incoming mails,「追加先」はメール受信のために必須です,
"Append as communication against this DocType (must have fields, ""Status"", ""Subject"")",この文書タイプに対するコミュニケーションの追加（「ステータス」「件名」のフィールドを持っている必要があります）,
Applicable Document Types,適用文書タイプ,
Apply,適用,
Apply Strict User Permissions,厳格なユーザー権限を適用,
Apply To All Document Types,すべての文書タイプに適用,
Apply this rule if the User is the Owner,ユーザーが所有者である場合にこのルールを適用,
Apply to all Documents Types,すべての文書タイプに適用,
Appreciate,感謝する,
Appreciation,感謝,
Archive,アーカイブ,
Archived,アーカイブ,
Archived Columns,アーカイブされた列,
Are you sure you want to delete the attachment?,添付ファイルを削除してもよろしいですか？,
Are you sure you want to relink this communication to {0}?,あなたはに{0}この通信を再リンクしてもよろしいですか？,
Are you sure?,本気ですか？,
Arial,Arial,
"As a best practice, do not assign the same set of permission rule to different Roles. Instead, set multiple Roles to the same User.",ベストプラクティスとして、異なる役割に対して同じセットのアクセス許可ルールを割り当てないでください。代わりに、同じユーザーに複数の役割を設定します。,
Assign Condition,条件を割り当てる,
Assign To Users,ユーザーに割り当て,
"Assign one by one, in sequence",順番に1つずつ割り当てる,
Assign to me,自分に割当,
Assign to the one who has the least assignments,割り当てが最も少ない方に割り当てます,
Assigned,割当済,
Assigned By,割当元,
Assigned By Full Name,フルネームによって割り当て,
Assigned By Me,自分に割当,
Assigned To,割当先,
Assigned To/Owner,所有者に割当,
Assignment,割当,
Assignment Complete,割当完了,
Assignment Completed,割当完了,
Assignment Rule,割り当て規則,
Assignment Rule User,割り当てルールのユーザー,
Assignment Rules,割り当て規則,
Assignment closed by {0},{0}が割当をクローズ済,
Assignment for {0} {1},{0} {1}の割り当て,
Atleast one field of Parent Document Type is mandatory,少なくとも親文書タイプの1つのフィールドは必須です,
Attach,添付する,
Attach Document Print,文書印刷を添付,
Attach Image,画像を添付,
Attach Print,印刷を添付,
Attach Your Picture,あなたの写真を添付,
Attach file for Import,インポート用の添付ファイル,
Attach files / urls and add in table.,ファイル/ URLを添付してテーブルに追加します。,
Attached To DocType,文書タイプに添付,
Attached To Field,フィールドに添付,
Attached To Name,名前に添付,
Attachment Limit (MB),添付ファイルの制限（MB）,
Attachment Removed,添付ファイル削除済,
Attempting Connection to QZ Tray...,QZトレイへの接続を試みています...,
Attempting to launch QZ Tray...,QZトレイを起動しようとしています...,
Auth URL Data,URLデータの認証,
Authenticating...,認証しています...,
Authentication,認証,
Authentication Apps you can use are: ,次の認証アプリケーションが使用可能です：,
Authentication Credentials,認証資格情報,
Authorization Code,認証コード,
Authorize URL,URLを承認する,
Authorized,認証済,
Auto,オート,
Auto Email Report,自動メールレポート,
Auto Name,自動命名,
Auto Reply Message,自動返信メッセージ,
Auto assignment failed: {0},自動割り当てに失敗しました：{0},
Automatically Assign Documents to Users,ユーザーに文書を自動的に割り当てる,
Automation,オートメーション,
Avatar,アバター,
Average,平均,
Average of {0},{0}の平均,
Avoid dates and years that are associated with you.,あなたに関連付いた日付・年は避けてください。,
Avoid recent years.,近い年は避けてください。,
Avoid sequences like abc or 6543 as they are easy to guess,推測可能となるABCや6543のような文字列を避けます,
Avoid years that are associated with you.,あなたに関連付いた年は避けてください。,
Awaiting Password,パスワード待ち,
Away,離席,
BCC,BCC,
Back to Desk,デスクに戻る,
Back to Login,ログインに戻る,
Background Color,背景色,
Background Email Queue,バックグラウンドメールキュー,
Background Jobs,バックグラウンドジョブ,
Background Workers,バックグラウンドワーカー,
Backup,バックアップ,
Backup Frequency,バックアップ頻度,
Backup Limit,バックアップ制限,
Backup job is already queued. You will receive an email with the download link,バックアップジョブはすでにキューに入れられています。ダウンロードリンクが記載されたメールが届きます,
Backups,バックアップ,
Banner,バナー,
Banner HTML,バナーのHTML,
Banner Image,バナー画像,
Banner is above the Top Menu Bar.,バナーは、上部のメニューバーの上にある。,
Bar,バー,
Base Distinguished Name (DN),ベースDN,
Based on Permissions For User,ユーザーのアクセス許可に基づく,
Beta,ベータ,
Better add a few more letters or another word,より良いいくつかのより多くの文字または別の単語を追加します,
Between,Between,
Bio,自己紹介,
Birth Date,生年月日,
Block Module,ブロックモジュール,
Block Modules,ブロックモジュール,
Blocked,ブロック済,
Blog,ブログ,
Blog Category,ブログカテゴリー,
Blog Intro,ブログイントロ,
Blog Introduction,ブログの紹介,
Blog Post,ブログの投稿,
Blog Settings,ブログ設定,
Blog Title,ブログのタイトル,
Blogger,ブロガー,
Bot,Bot,
Both DocType and Name required,文書タイプと名前が必要です。,
Both login and password required,ログインとパスワードの両方が必要,
Bounced,不達,
Braintree Settings,Braintreeの設定,
Braintree payment gateway settings,Braintree支払いゲートウェイの設定,
Brand HTML,ブランドのHTML,
Brand Image,ブランド画像,
Breadcrumbs,パンくずリスト,
Browser not supported,サポートされていないブラウザです,
Brute Force Security,ブルートフォースセキュリティ,
Build Report,レポートを作成,
Bulk Delete,一括削除,
Bulk Edit {0},一括編集{0},
Bulk Rename,一括名前変更,
Bulk Update,一括更新,
Button,ボタン,
Button Help,ボタンヘルプ,
Button Label,ボタンラベル,
Bypass Two Factor Auth for users who login from restricted IP Address,IPアドレス制限にてログインしたユーザーの２要素認証を回避,
Bypass restricted IP Address check If Two Factor Auth Enabled,制限されたIPアドレスチェックをバイパスする2つのファクタ認証が有効な場合,
CC,CC,
"CC, BCC & Email Template",CC、BCC＆Emailテンプレート,
CSS,CSS,
CSV,CSV,
Cache Cleared,キャッシュのクリア,
Calculate,計算,
Calendar Name,カレンダー名,
Calendar View,カレンダー表示,
Call,電話,
Can Read,閲覧可能,
Can Share,共有可能,
Can Write,書込可能,
Can't identify open {0}. Try something else.,{0}のオープン識別することはできません。何か他のものを試してみてください。,
Can't save the form as data import is in progress.,データのインポートが進行中のため、フォームを保存できません。,
Cancel {0} documents?,{0}文書をキャンセルしますか？,
Cancelled Document restored as Draft,取り消されたドキュメントが下書きとして復元されました,
Cancelling,キャンセル,
Cancelling {0},{0}をキャンセルしています,
Cannot Remove,削除できません,
Cannot cancel before submitting. See Transition {0},提出する前にキャンセルすることはできません。遷移{0}を参照してください。,
Cannot change docstatus from 0 to 2,文書ステータスを0から2に変更することはできません,
Cannot change docstatus from 1 to 0,文書ステータスを1から0に変更することはできません,
Cannot change header content,ヘッダーの内容を変更できません,
Cannot change state of Cancelled Document. Transition row {0},キャンセルされた文書の状態を変更することはできません。遷移行{0},
Cannot change user details in demo. Please signup for a new account at https://erpnext.com,デモでユーザーの詳細を変更することはできません。 https://erpnext.comで新規アカウントにサインアップしてください,
Cannot create a {0} against a child document: {1},子ドキュメントに対して{0}を作成できません：{1},
Cannot delete Home and Attachments folders,ホームおよび添付ファイルフォルダを削除することはできません,
Cannot delete file as it belongs to {0} {1} for which you do not have permissions,権限を持たない{0} {1}に属しているファイルを削除できません,
Cannot delete or cancel because {0} {1} is linked with {2} {3} {4},{0} {1}が{2} {3} {4}にリンクしているため、削除またはキャンセルできません,
Cannot delete standard field. You can hide it if you want,標準のフィールドを削除することはできません。必要に応じて非表示にできます,
Cannot delete {0},{0}は削除できません,
Cannot delete {0} as it has child nodes,子ノードがあるため、{0}を削除することはできません,
"Cannot edit Standard Notification. To edit, please disable this and duplicate it",標準通知を編集できません。編集するには、これを無効にして複製してください,
Cannot edit a standard report. Please duplicate and create a new report,標準レポートを編集することはできません。複製して新しいレポートを作成してください,
Cannot edit cancelled document,キャンセルされた文書を編集することはできません,
Cannot edit standard fields,標準フィールドを編集することはできません,
Cannot have multiple printers mapped to a single print format.,複数のプリンタを単一の印刷形式にマッピングすることはできません。,
Cannot link cancelled document: {0},キャンセルされた文書をリンクすることはできません：{0},
Cannot map because following condition fails: ,次の条件に問題があるため配置できません：,
Cannot move row,行を移動できません,
Cannot open instance when its {0} is open,自身の{0}が開いているときにインスタンスを開くことはできません,
Cannot open {0} when its instance is open,自身のインスタンスが開いているときに{0}を開くことはできません,
Cannot remove ID field,IDフィールドは削除できません,
Cannot set Notification on Document Type {0},文書タイプ{0}で通知を設定できません,
Cannot update {0},{0}を更新できません,
Cannot use sub-query in order by,順にサブクエリを使用することはできません,
Cannot {0} {1},できません{0} {1},
Capitalization doesn't help very much.,総額はあまり役立ちません。,
Card Details,カードの詳細,
Categorize blog posts.,ブログの記事を分類。,
Category Description,カテゴリ説明,
Cent,セント,
"Certain documents, like an Invoice, should not be changed once final. The final state for such documents is called Submitted. You can restrict which roles can Submit.",一部の文書（例：請求書）は、一回で最終変更すべきではありません。文書の最終的な状態は「提出」と呼ばれています。\n「提出」は「役割」によって制限することができます。,
Chain Integrity,チェーン・インテグリティ,
Chaining Hash,チェーンハッシュ,
Change Label (via Custom Translation),ラベルを変更（カスタム翻訳より）,
Change Password,パスワードを変更する,
"Change field properties (hide, readonly, permission etc.)",変更フィールドのプロパティ（非表示、読み取り専用、アクセス権など）,
Channel,チャネル,
Chart Name,チャート名,
Chart Options,チャートオプション,
Chart Source,チャートのソース,
Chart Type,グラフの種類,
Charts,チャート,
Chat,チャット,
Chat Background,チャットの背景,
Chat Message,チャットメッセージ,
Chat Operators,チャットオペレータ,
Chat Profile,チャットプロフィール,
Chat Profile for User {0} exists.,ユーザー{0}のチャットプロファイルが存在します。,
Chat Room,チャットルーム,
Chat Room Name,チャットルーム名,
Chat Room User,チャットルームユーザー,
Chat Token,チャットトークン,
Chat Type,チャットタイプ,
Chat messages and other notifications.,チャットメッセージや他の通知,
Check,チェック,
Check Request URL,リクエストURLを確認する,
"Check columns to select, drag to set order.",列を確認し、ドラッグして注文をセットしてください。,
Check this if you are testing your payment using the Sandbox API,あなたはサンドボックスAPIを使用してお支払いをテストしている場合は、これをチェック,
Check this to pull emails from your mailbox,自分のメールボックスからメールを受信する場合チェック,
Check which Documents are readable by a User,ユーザーが読み取り可能なドキュメントにチェック,
Checking one moment,一瞬の確認,
Checksum Version,チェックサムバージョン,
Child Table Mapping,子テーブルマッピング,
Child Tables are shown as a Grid in other DocTypes,子テーブルは他のDocTypeではグリッドとして表示されます,
Choose authentication method to be used by all users,すべてのユーザーが使用する認証方法を選択する,
Clear Error Logs,エラーログを消去,
Clear User Permissions,ユーザー権限の消去,
Clear all roles,すべての役割をクリア,
"Clearing end date, as it cannot be in the past for published pages.",公開されたページの過去の日付ではないため、終了日をクリアします。,
Click here to post bugs and suggestions,バグや提案を投稿するには、ここをクリックしてください,
Click here to verify,クリックして認証,
Click on the link below to complete your registration and set a new password,登録を完了し、新しいパスワードを設定するためには、次のリンクをクリックしてください,
Click on the link below to download your data,下のリンクをクリックしてデータをダウンロードしてください。,
Click on the link below to verify your request,下のリンクをクリックしてリクエストを確認してください。,
Click table to edit,表をクリックして編集,
Click to Set Filters,クリックしてフィルタを設定する,
Clicked,クリック済,
Client Credentials,Client Credentials,
Client Information,クライアント情報,
Client Script,クライアントスクリプト,
Client URLs,クライアントURL,
Client side script extensions in Javascript,JavaScriptのクライアントサイドスクリプト拡張子,
Collapsible,折り畳み,
Collapsible Depends On,折り畳み依存関係,
Column,列,
Column <b>{0}</b> already exist.,列<b>{0}は</b>すでに存在しています。,
Column Break,列区切り,
Column Labels:,列ラベル：,
Column Name,列名,
Column Name cannot be empty,列名を空にすることはできません,
Columns,列,
Columns based on,列に基づく,
Combination of Grant Type (<code>{0}</code>) and Response Type (<code>{1}</code>) not allowed,許可タイプ（ <code>{0}</code> ）と応答タイプ（ <code>{1}</code> ）の組み合わせは許可されていません,
Comment By,のコメント,
Comment Email,コメントメール,
Comment Type,コメントタイプ,
Comment can only be edited by the owner,コメントはオーナーのみが編集できます,
Commented on {0}: {1},{0}にコメント：{1},
Comments and Communications will be associated with this linked document,コメントとの通信は、このリンクされたドキュメントに関連付けられます,
Comments cannot have links or email addresses,コメントにリンクや電子メールアドレスを含めることはできません,
Common names and surnames are easy to guess.,一般的な名前と姓は推測するのは簡単です。,
Communicated via {0} on {1}: {2},介して通信{0} {1}：{2},
Communication Type,通信タイプ,
Company History,沿革,
Company Introduction,会社紹介,
Compiled Successfully,正常にコンパイルされた,
Complete By,までに完了,
Complete Registration,登録完了,
Complete Setup,完全セットアップ,
Compose Email,Eメールの作成,
Condition Detail,条件の詳細,
Conditions,条件,
Configure Chart,チャートの設定,
Configure Charts,チャートの設定,
Confirm,確認,
Confirm Deletion of Data,データ削除確認,
Confirm Request,要求を確認します,
Confirm Your Email,メール確認,
Confirmed,確認済,
Connected to QZ Tray!,QZトレイに接続しました！,
Connection Name,接続名,
Connection Success,接続成功,
Connection lost. Some features might not work.,接続切断。一部の機能が動作しない可能性があります。,
Connector Name,コネクタ名,
Connector Type,コネクタタイプ,
Contact Us Settings,お問い合わせの設定,
"Contact options, like ""Sales Query, Support Query"" etc each on a new line or separated by commas.",新しい行またはカンマで区切られた「セールスクエリ、サポートクエリ」などのような連絡先オプション,
Contacts,連絡先,
Content (HTML),コンテンツ（HTML）,
Content (Markdown),コンテンツ（値下げ）,
Content Hash,コンテンツハッシュ,
Content web page.,コンテンツのWebページ,
Conversation Tones,会話のトーン,
Copyright,著作権,
Core,コア,
Core DocTypes cannot be customized.,コアDocTypeはカスタマイズできません。,
Could not connect to outgoing email server,送信メールサーバーに接続できませんでした,
Could not find {0},見つけることができませんでした{0},
Could not find {0} in {1},{1}に{0}が見つかりませんでした,
Could not identify {0},識別できませんでした{0},
Count,カウント,
Country Name,国名,
County,郡,
Create Chart,グラフを作成,
Create New,新規作成,
Create Post,投稿を作成する,
Create User Email,ユーザーメールを作成する,
Create a New Format,新しい書式を作成,
Create a new record,新しいレコードを作成する,
Create a new {0},新しい{0}を作成する,
Create and Send Newsletters,ニュースレターの作成・送信,
Create and manage newsletter,ニュースレターの作成と管理,
Created,作成した,
Created Custom Field {0} in {1},{1}のカスタムフィールド{0}を作成,
Created On,作成,
Criticism,批判,
Criticize,批判する,
Ctrl + Down,Ctrl+↓,
Ctrl + Up,Ctrl+↑,
Ctrl+Enter to add comment,Ctrl+Enterキーでコメントを追加,
Currency Name,通貨名,
Currency Precision,通貨の精度,
Current Mapping,現在のマッピング,
Current Mapping Action,現在のマッピングアクション,
Current Mapping Delete Start,現在のマッピングの削除開始,
Current Mapping Start,現在のマッピングの開始,
Current Mapping Type,現在のマッピングタイプ,
Currently Viewing,現在の表示,
Currently updating {0},現在{0}を更新しています,
Custom,カスタム,
Custom Base URL,カスタムベースURL,
Custom CSS,カスタムCSS,
Custom DocPerm,カスタムDocPerm,
Custom Field,カスタムフィールド,
Custom Fields can only be added to a standard DocType.,カスタムフィールドは標準のDocTypeにのみ追加できます。,
Custom Fields cannot be added to core DocTypes.,カスタムフィールドはコアDocTypeに追加できません。,
Custom Format,カスタム書式,
Custom HTML Help,カスタムHTMLヘルプ,
Custom JS,カスタムJS,
Custom Menu Items,カスタムメニュー項目,
Custom Report,カスタムレポート,
Custom Reports,カスタム レポート,
Custom Role,カスタム役割,
Custom Script,カスタムスクリプト,
Custom Sidebar Menu,カスタムサイドバーメニュー,
Custom Translations,カスタム翻訳,
Customization,カスタマイズ,
Customizations Reset,カスタマイズのリセット,
Customizations for <b>{0}</b> exported to:<br>{1},<b>{0}の</b>カスタマイズをエクスポートしました： <br> {1},
Customize Form,フォームをカスタマイズ,
Customize Form Field,フォームフィールドをカスタマイズ,
"Customize Label, Print Hide, Default etc.",ラベル・印刷を隠す・デフォルトなどをカスタマイズ,
Customize...,カスタマイズ...,
"Customized Formats for Printing, Email",印刷・電子メールのカスタマイズ形式,
Customized HTML Templates for printing transactions.,取引印刷用のカスタマイズされたHTMLテンプレート,
Cut,カット,
DESC,DESC,
Daily Event Digest is sent for Calendar Events where reminders are set.,日次イベントダイジェストは、（リマインダーが設定されている）カレンダーイベントに送信されます。,
Danger,危険,
Dark Color,暗色,
Dashboard Chart,ダッシュボードチャート,
Dashboard Chart Link,ダッシュボードチャートリンク,
Dashboard Chart Source,ダッシュボードチャートのソース,
Dashboard Name,ダッシュボード名,
Dashboards,ダッシュボード,
Data,データ,
Data Export,データのエクスポート,
Data Import,データのインポート,
Data Import Template,データのインポートテンプレート,
Data Migration,データ移行,
Data Migration Connector,データ移行コネクタ,
Data Migration Mapping,データ移行マッピング,
Data Migration Mapping Detail,データ移行マッピングの詳細,
Data Migration Plan,データ移行計画,
Data Migration Plan Mapping,データ移行計画マッピング,
Data Migration Run,データ移行の実行,
Data missing in table,表に不足しているデータ,
Database Engine,データベースエンジン,
Database Name,データベース名,
Date and Number Format,日付と番号の書式,
Date {0} must be in format: {1},日付{0}は書式：{1}でなければなりません,
Dates are often easy to guess.,日付は、多くの場合、推測するのは簡単です。,
Day of Week,曜日,
Days After,後日,
Days Before,事前,
Days Before or After,前または後の日,
"Dear System Manager,",システムマネージャーさん,
"Dear User,",ユーザーの皆様、,
Dear {0},{0}様,
Default Address Template cannot be deleted,デフォルトのアドレステンプレートを削除することはできません,
Default Inbox,デフォルト受信トレイ,
Default Incoming,デフォルト収益,
Default Outgoing,デフォルト支出,
Default Print Format,デフォルト印刷フォーマット,
Default Print Language,デフォルトの印刷言語,
Default Redirect URI,デフォルトリダイレクトURI,
Default Role at Time of Signup,サインアップ時のデフォルト役割,
Default Sending,デフォルトの送信,
Default Sending and Inbox,デフォルトの送受信トレイ,
Default Sort Field,デフォルトのソートフィールド,
Default Sort Order,デフォルトのソート順,
Default Value,デフォルト値,
"Default: ""Contact Us""",デフォルト：「お問い合わせ」,
DefaultValue,デフォルト値,
Define workflows for forms.,フォームのワークフローを定義します,
Defines actions on states and the next step and allowed roles.,状態・次のステップ・許可されたロールのアクションを定義します,
Defines workflow states and rules for a document.,ドキュメントのためのワークフローの状況とルールを定義します。,
Delayed,遅延,
Delete Data,データを削除,
Delete comment?,コメントを削除しますか？,
Delete this record to allow sending to this email address,このメールアドレスに送信できるようにするには、このレコードを削除します,
Delete {0} items permanently?,永久{0}の項目を削除しますか？,
Deleted,削除済,
Deleted DocType,削除されたDOCTYPE,
Deleted Document,削除された文書,
Deleted Documents,削除されたドキュメント,
Deleted Name,削除された名前,
Deleting {0},削除{0},
Depends On,依存関係,
Descendants Of,の子孫,
Desk,デスク,
Desk Access,デスクのアクセス,
Desktop Icon,デスクトップアイコン,
Desktop Icon already exists,デスクトップアイコンは既に存在します,
Developer,開発者,
Did not add,追加しませんでした,
Did not cancel,キャンセルされませんでした,
Did not find {0} for {0} ({1}),{0}（{1}）により{0}が見つかりませんでした,
Did not remove,削除されませんでした。,
"Different ""States"" this document can exist in. Like ""Open"", ""Pending Approval"" etc.",このドキュメントには異なる「ステータス」を入れることができます。（「オープン」「承認待ち」など）,
Direct,直接,
Direct room with {0} already exists.,{0}のダイレクトルームは既に存在します。,
Disable Auto Refresh,自動更新を無効にする,
Disable Count,カウントを無効にする,
Disable Customer Signup link in Login page,ログインページの顧客の登録リンクを無効にする,
Disable Prepared Report,作成済みレポートを無効にする,
Disable Report,レポートを無効にする,
Disable SMTP server authentication,SMTPサーバー認証を無効にする,
Disable Sidebar Stats,サイドバー統計を無効にする,
Disable Signup,登録を無効にする,
Disable Standard Email Footer,標準メールフッターを無効にします,
Discard,破棄,
Display,表示,
Display Depends On,依存性を表示,
Do not allow user to change after set the first time,初回設定以降、ユーザーが変更することはできません,
Do not edit headers which are preset in the template,テンプレートにあらかじめ設定されているヘッダーは編集しないでください,
Do not send Emails,メールを送信しない,
Doc Event,文書イベント,
Doc Events,文書イベント,
Doc Status,文書ステータス,
DocField,文書フィールド,
DocPerm,文書管理,
DocShare,文書共有,
DocType <b>{0}</b> provided for the field <b>{1}</b> must have atleast one Link field,フィールド<b>{1</b> <b>}に</b>提供されるDocType <b>{0}に</b>は、少なくとも1つのリンクフィールドが必要です,
DocType can not be merged,文書タイプを混在させることはできません。,
DocType can only be renamed by Administrator,文書タイプは管理者のみ名前を変更することができます,
DocType is a Table / Form in the application.,文書タイプはアプリケーション内のテーブル／フォームです,
DocType must be Submittable for the selected Doc Event,DocTypeは、選択したDocイベントに対してSubmittableでなければなりません,
DocType on which this Workflow is applicable.,このワークフローには文書タイプが適用可能です,
"DocType's name should start with a letter and it can only consist of letters, numbers, spaces and underscores",文書タイプ名は文字、数字、スペース、アンダースコアで開始する必要があります,
Doctype required,Doctypeが必要,
Document,文書,
Document Follow,ドキュメントフォロー,
Document Follow Notification,伝票フォロー通知,
Document Queued,文書キュー,
Document Restored,文書の復元,
Document Share Report,文書共有レポート,
Document States,文書状態,
Document Type is not importable,文書タイプはインポートできません,
Document Type is not submittable,伝票タイプは送信できません,
Document Type to Track,追跡する伝票タイプ,
Document Types,文書タイプ,
Document can't saved.,文書を保存できませんでした。,
Document {0} has been set to state {1} by {2},文書{0}は{2}で状態{1}に設定されています,
Documents,文書,
Documents assigned to you and by you.,自分で割当した文書,
Domain Settings,ドメイン設定,
Domains HTML,ドメインHTML,
"Don't HTML Encode HTML tags like &lt;script&gt; or just characters like &lt; or &gt;, as they could be intentionally used in this field",&lt;script&gt; や単に &lt; または &gt; などの文字は、このフィールドでは意図的に使用されるため、HTMLエンコードしないでください。,
Don't Override Status,ステータスを上書きしないでください,
Don't create new records,新しいレコードを作成しない,
Don't have an account? Sign up,アカウントを持っていないのですか？サインアップ,
"Don't know, ask 'help'",わかりません。ヘルプを参照してください。,
Download Data,データをダウンロードする,
Download Files Backup,ダウンロードファイルバックアップ,
Download Link,ダウンロードリンク,
Download Report,ダウンロードレポート,
Download Your Data,データをダウンロードする,
Download link for your backup will be emailed on the following email address: {0},バックアップのダウンロードリンクは、次のメールアドレスに送信されます：{0},
Download with Data,データをダウンロード,
Drag and Drop tool to build and customize Print Formats.,印刷形式を構築しカスタマイズするためのドラッグ＆ドロップツール,
Drag elements from the sidebar to add. Drag them back to trash.,追加するにはサイドバーから要素をドラッグします。ゴミ箱にドラッグして戻します。,
Dropbox Access Key,Dropboxのアクセスキー,
Dropbox Access Secret,Dropboxのアクセスの秘密,
Dropbox Access Token,Dropboxアクセストークン,
Dropbox Settings,Dropboxの設定,
Dropbox Setup,Dropboxのセットアップ,
Dropbox access is approved!,Dropboxのアクセスが承認されます！,
Dropbox backup settings,Dropboxのバックアップの設定,
Duplicate Filter Name,重複するフィルタ名,
Dynamic Link,動的リンク,
Dynamic Report Filters,動的レポートフィルタ,
ESC,ESC,
Edit Auto Email Report Settings,自動メールレポートの設定を編集する,
Edit Custom HTML,カスタムHTMLを編集,
Edit DocType,文書タイプ編集,
Edit Filter,フィルタ編集,
Edit Format,形式を編集,
Edit HTML,HTMLを編集,
Edit Heading,見出しを編集,
Edit Properties,プロパティ編集,
Edit to add content,コンテンツを追加して編集,
Edit {0},{0}を編集する,
Editable Grid,編集可能なグリッド,
Editing Row,行の編集,
Eg. smsgateway.com/api/send_sms.cgi,例「smsgateway.com/api/send_sms.cgi」,
Email Account Name,メールアカウント名,
Email Account added multiple times,電子メールアカウントが複数回追加されました,
Email Addresses,メールアドレス,
Email Domain,メールドメイン,
"Email Domain not configured for this account, Create one?",このアカウントにはメールのドメインが設定されていません。新規作成しますか？,
Email Flag Queue,メールフラグキュー,
Email Footer Address,メールフッターの住所,
Email Group,メールグループ,
Email Group List,メールグループ一覧,
Email Group Member,メールグループメンバー,
Email Login ID,メールログインID,
Email Queue,メールキュー,
Email Queue Recipient,メールキューの受信者,
Email Queue records.,メールキューのレコード。,
Email Reply Help,メール返信ヘルプ,
Email Rule,メールルール,
Email Server,メールサーバ,
Email Settings,メール設定,
Email Signature,メール署名,
Email Status,メールステータス,
Email Sync Option,メール同期オプション,
Email Templates for common queries.,一般的な質問のための電子メールテンプレート。,
Email To,メール送信先,
Email Unsubscribe,メール配信停止,
Email has been marked as spam,メールがスパムとしてマークされています,
Email has been moved to trash,メールはゴミ箱に移動されました,
Email not sent to {0} (unsubscribed / disabled),メール{0}（無効/非購読）に送信されません,
Email not verified with {0},{0}で確認できないメール,
Emails are muted,メールはミュートされました,
Emails will be sent with next possible workflow actions,電子メールは次のワークフローアクションで送信されます,
Embed image slideshows in website pages.,ウェブサイトのページ内に画像のスライドショーを埋め込んでください。,
Enable / Disable Domains,ドメインの有効化/無効化,
Enable Auto Reply,自動返信を有効にする,
Enable Automatic Backup,自動バックアップを有効にする,
Enable Chat,チャットを有効にする,
Enable Comments,コメントを有効にする,
Enable Incoming,着信を有効にする,
Enable Outgoing,送信を有効にする,
Enable Password Policy,パスワードポリシーを有効にする,
Enable Print Server,プリントサーバーを有効にする,
Enable Raw Printing,生の印刷を有効にする,
Enable Report,レポートを有効にする,
Enable Scheduled Jobs,スケジュールされたジョブを有効にする,
Enable Social Login,ソーシャルログインを有効にする,
Enable Two Factor Auth,２要素認証を有効にする,
Enabled email inbox for user {0},ユーザー{0}のメール受信トレイを有効にしました,
"Encryption key is invalid, Please check site_config.json",暗号化キーが無効です。site_config.jsonを確認してください。,
End Date Field,終了日フィールド,
End Date cannot be before Start Date!,終了日は開始日より前にすることはできません！,
Endpoint URL,エンドポイントURL,
Energy Point Log,エネルギーポイントログ,
Energy Point Rule,エネルギーポイントルール,
Energy Point Settings,エネルギーポイント設定,
Energy Points,エネルギーポイント,
Enter Email Recipient(s),電子メールの受信者（複数可）を入力してください,
Enter Form Type,フォームタイプを入力してください,
"Enter default value fields (keys) and values. If you add multiple values for a field, the first one will be picked. These defaults are also used to set ""match"" permission rules. To see list of fields, go to ""Customize Form"".",デフォルト値フィールド（キー）と値を入力します。フィールドに複数の値を追加する場合、最初のものが選択されます。これらのデフォルトは「一致」の制限ルールを設定するために使用されます。フィールドのリストを表示するには「フォームのカスタマイズ」を参照してください。,
Enter folder name,フォルダ名を入力,
"Enter keys to enable login via Facebook, Google, GitHub.",Facebook・Google・GitHub経由でログインするためのキーを入力してください。,
Enter python module or select connector type,Pythonモジュールを入力するか、コネクタの種類を選択してください,
"Enter static url parameters here (Eg. sender=ERPNext, username=ERPNext, password=1234 etc.)","静的なURLパラメータを入力してください（例：sender=ERPNext, username=ERPNext, password=1234 など）",
Enter url parameter for message,メッセージのURLパラメータを入力してください,
Enter url parameter for receiver nos,受信者番号にURLパラメータを入力してください,
Enter your password,パスワードを入力,
Entity Name,エンティティ名,
Equals,＝,
Error Message,エラーメッセージ,
Error Report,エラーレポート,
Error Snapshot,エラースナップショット,
Error in Custom Script,カスタムスクリプトでエラーが発生しました,
Error in Notification,通知のエラー,
Error in Notification: {},通知エラー：{},
Error while connecting to email account {0},電子メールアカウント{0}に接続中にエラーが発生しました,
Error while evaluating Notification {0}. Please fix your template.,通知{0}を評価中にエラーが発生しました。テンプレートを修正してください。,
Error: Document has been modified after you have opened it,エラー：あなたが文書を開いた後に変更されています,
Error: Value missing for {0}: {1},エラー：{0}の値がありません：{1},
Errors in Background Events,バックグラウンドイベントのエラー,
Event Category,イベントカテゴリ,
Event Participants,イベント参加者,
Event Type,イベント タイプ,
Event and other calendars.,イベントや他のカレンダー,
Events in Today's Calendar,今日のカレンダーのイベント,
Everyone,全員,
Example,例,
Example Email Address,メールアドレス例,
Example: {{ subject }},例：{{subject}},
Excel,Excel,
Exception,例外,
Exception Type,例外タイプ,
Execution Time: {0} sec,実行時間：{0}秒,
Expert,専門家,
Expiration time,呼気時間,
Expire Notification On,通知を期限切れ,
Expires In,で有効期限,
Expiry time of QR Code Image Page,QRコード画像ページの有効期限,
Export All {0} rows?,すべての{0}行をエクスポートしますか？,
Export Custom Permissions,エクスポートカスタムアクセス許可,
Export Customizations,エクスポートカスタマイズ,
Export Data,データのエクスポート,
Export Data in CSV / Excel format.,CSV / Excel形式でデータをエクスポートします。,
Export Report: {0},レポートのエクスポート：{0},
Expose Recipients,受信者を公開,
"Expression, Optional",（任意）表現,
Facebook,Facebook,
Failed to complete setup,セットアップを完了できませんでした,
Failed to connect to server,サーバーへの接続に失敗しました,
Failed while amending subscription,定期購入の変更中に失敗しました,
FavIcon,FavIcon,
Feedback Request,フィードバック依頼,
Fetch From,フェッチ元,
Fetch If Empty,空の場合は取得,
Fetch Images,画像を取得する,
Fetch attached images from document,ドキュメントから添付されたイメージを取得する,
"Field ""route"" is mandatory for Web Views",ウェブビューでは「経路」フィールドは必須です,
"Field ""value"" is mandatory. Please specify value to be updated",フィールド「値」が必須です。更新する値を指定してください,
Field Description,フィールド説明,
Field Maps,フィールドマップ,
Field Type,フィールドタイプ,
"Field that represents the Workflow State of the transaction (if field is not present, a new hidden Custom Field will be created)",取引のワークフローの状態を表すフィールド（フィールドが存在しない場合は、新しい隠しカスタムフィールドが作成されます）,
Field to Track,追跡するフィールド,
Field type cannot be changed for {0},{0}のフィールドタイプは変更できません,
Field {0} not found.,フィールド{0}が見つかりません。,
Fieldname is limited to 64 characters ({0}),フィールド名が64文字に制限されています（{0}）,
Fieldname not set for Custom Field,カスタムフィールドにフィールド名が設定されていません,
Fieldname which will be the DocType for this link field.,このリンクフィールドのDocTypeになるフィールド名。,
Fieldname {0} cannot have special characters like {1},フィールド名{0}には、{1}などの特殊文字を含めることはできません,
Fieldname {0} conflicting with meta object,フィールド名{0}はメタオブジェクトと競合しています,
Fields Multicheck,フィールドのマルチチェック,
"Fields separated by comma (,) will be included in the ""Search By"" list of Search dialog box","カンマ（,）で区切られたフィールドは、検索ダイアログボックスの「検索対象」リストに含まれます",
Fieldtype,フィールドタイプ,
Fieldtype cannot be changed from {0} to {1} in row {2},行{2}のフィールドタイプは{0}から{1}に変更することはできません,
File '{0}' not found,ファイル &#39;{0}&#39;が見つかりません,
File Backup,ファイルバックアップ,
File Name,ファイル名,
File Size,ファイル サイズ,
File Type,ファイルの種類,
File URL,ファイルURL,
File Upload,ファイルアップロード,
File Upload Disconnected. Please try again.,ファイルのアップロードが切断されました。もう一度お試しください。,
File Upload in Progress. Please try again in a few moments.,進行中のファイルのアップロード。しばらくしてからもう一度お試しください。,
File backup is ready,ファイルのバックアップが完了しました,
File not attached,ファイルが添付されていません,
File size exceeded the maximum allowed size of {0} MB,ファイルサイズは{0} MBの最大許容サイズを超えました,
File too big,大きすぎるファイル,
File {0} does not exist,ファイル{0}が存在しません,
Files,ファイル,
Filter,フィルタ,
Filter Data,データのフィルタリング,
Filter List,フィルタリスト,
Filter Meta,メタをフィルタリング,
Filter Name,フィルタ名,
Filter Values,フィルタ値,
Filter must be a tuple or list (in a list),フィルタはタプルまたはリスト（リスト内）でなければなりません,
"Filter must have 4 values (doctype, fieldname, operator, value): {0}",フィルタには4つの値（doctype、fieldname、operator、value）が必要です：{0},
Filter...,フィルター...,
"Filtered by ""{0}""","""{0}""でフィルタリング",
Filters Display,フィルタの表示,
Filters JSON,フィルタJSON,
Filters saved,保存したフィルタ,
Find {0} in {1},{0} {1}で見つける,
First Level,第1レベル,
First Success Message,最初の成功メッセージ,
First Transaction,最初の取引,
First data column must be blank.,最初のデータ列は空白でなければなりません,
First set the name and save the record.,最初に名前を設定し、レコードを保存します。,
Flag,旗,
Float,小数,
Float Precision,小数精度,
Fold,フォールド,
Fold can not be at the end of the form,フォールドはフォームの最後にすることはできません,
Fold must come before a Section Break,フォールドはセクション区切りの前に来なければなりません,
Folder,フォルダ,
Folder name should not include '/' (slash),フォルダ名に &#39;/&#39;（スラッシュ）を含めないでください,
Folder {0} is not empty,フォルダ{0}が空ではありません,
Follow,た,
Followed by,に続く,
Following fields are missing:,次のフィールドが不足しています：,
Following fields have missing values:,次のフィールドが欠落している値が設定されています,
Font,フォント,
Font Size,フォントサイズ,
Fonts,フォント,
Footer,フッター,
Footer HTML,フッターHTML,
Footer Items,フッター項目,
Footer will display correctly only in PDF,フッターはPDFでのみ正しく表示されます,
For Document Type,伝票タイプ,
"For Links, enter the DocType as range.\nFor Select, enter list of Options, each on a new line.",リンクには、範囲として文書タイプを入力します。選択には、それぞれ新しい行に、オプションのリストを入力します。,
For User,ユーザーのための,
For Value,価値観,
"For currency {0}, the minimum transaction amount should be {1}",通貨{0}の場合、最小取引額は{1},
For example if you cancel and amend INV004 it will become a new document INV004-1. This helps you to keep track of each amendment.,例えば、あなたがINV004をキャンセルして修正した場合、新しいドキュメントINV004-1になります。これによって各修正を追跡するのに役立ちます。,
"For example: If you want to include the document ID, use {0}",例：文書IDを含める場合、{0}を使用,
"For updating, you can update only selective columns.",更新の際、選択した列のみを更新することができます。,
For {0} at level {1} in {2} in row {3},行{3}の{2}のレベル{1}の{0},
Force,力,
Force Show,強制表示,
Forgot Password,パスワード紛失,
Forgot Password?,パスワードをお忘れですか？,
Form Customization,フォームのカスタマイズ,
Form Settings,フォーム設定,
Format,フォーマット,
Format Data,書式データ,
Forward To Email Address,メールアドレスに転送,
Fraction,分数,
Fraction Units,分数単位,
Frames,フレーム,
Frappe,フラッペ,
Frappe Framework,Frappe Framework,
Friendly Title,フレンドリータイトル,
From Date Field,開始日フィールド,
From Document Type,伝票タイプから,
From Full Name,フルネームから,
Full Page,全ページ,
Fw: {0},Fw：{0},
GCalendar Sync ID,GCalendar同期ID,
GMail,Gmail,
Gantt,ガント,
Gateway,ゲートウェイ,
Gateway Controller,ゲートウェイコントローラ,
Gateway Settings,ゲートウェイ設定,
Generate Keys,鍵を生成する,
Generate New Report,新しいレポートを生成する,
Generated File,生成されたファイル,
Geo,地理,
Geolocation,ジオロケーション,
Get Alerts for Today,今日のアラートを取得,
Get Contacts,連絡先を取得する,
Get Fields,フィールドを取得する,
Get your globally recognized avatar from Gravatar.com,Gravatar.comからグローバルに認識されるアバターを入手,
GitHub,GitHub,
Give Review Points,レビューポイントを与える,
Global Unsubscribe,グローバル配信停止,
Go to the document,文書に行く,
Go to this URL after completing the form (only for Guest users),フォームに記入したらこのURLにアクセスしてください（ゲストユーザーのみ）,
Go to {0},{0}に移動します,
Go to {0} List,{0}リストに移動します,
Go to {0} Page,{0}ページに移動する,
Google,Google,
Google Analytics ID,GoogleアナリティクスID,
Google Calendar ID,GoogleカレンダーID,
Google Font,Googleフォント,
Google Services,Googleサービス,
Grant Type,助成金タイプ,
Group Name,グループ名,
Group name cannot be empty.,グループ名は空にすることはできません。,
Groups of DocTypes,文書タイプのグループ,
HTML,HTML,
HTML Editor,HTMLエディタ,
"HTML Header, Robots and Redirects",HTMLヘッダー、ロボット、リダイレクト,
HTML for header section. Optional,（任意）ヘッダー部分のHTML。,
Half,ハーフ,
Has  Attachment,添付ファイルあり,
Has Attachments,添付ファイルあり,
Has Domain,ドメインあり,
Has Role,役割あり,
Has Web View,Web表示あり,
Have an account? Login,アカウントをお持ちですか？ログイン,
Header,ヘッダー,
Header HTML,ヘッダHTML,
Header HTML set from attachment {0},添付ファイル{0}から設定されたヘッダーHTML,
Header Image,ヘッダー画像,
Headers,ヘッダー,
Heading,見出し,
Hello {0},こんにちは {0},
Hello!,こんにちは！,
Help Articles,ヘルプ記事,
Help Category,ヘルプカテゴリー,
Help on Search,検索のヘルプ,
"Help: To link to another record in the system, use ""#Form/Note/[Note Name]"" as the Link URL. (don't use ""http://"")","ヘルプ：システム内の別のレコードにリンクするには、「＃フォーム/備考/ [名前をメモ]「リンクのURLとして使用します。 （「http:// ""を使用しないでください）",
Helvetica,Helvetica,
Hi {0},こんにちは{0},
Hide Copy,コピーを非表示にする,
Hide Footer Signup,フッターのSignupを隠す,
Hide Sidebar and Menu,サイドバーとメニューを隠す,
Hide Standard Menu,標準メニューを非表示,
Hide Weekends,週末を隠す,
Hide details,詳細を隠す,
Hide footer in auto email reports,自動メールレポートのフッターを非表示にする,
Higher priority rule will be applied first,優先順位の高いルールが最初に適用されます,
Highlight,ハイライト,
"Hint: Include symbols, numbers and capital letters in the password",ヒント：パスワードに記号、数字、大文字を含める,
Home Page,ホームページ,
Home Settings,ホーム設定,
Home/Test Folder 1,ホーム/テスト フォルダ1,
Home/Test Folder 1/Test Folder 3,ホーム/テスト フォルダ1 /テスト フォルダ3,
Home/Test Folder 2,ホーム/テスト フォルダ2,
Host,ホスト,
Hostname,ホスト名,
"How should this currency be formatted? If not set, will use system defaults",この通貨はどのようなフォーマットにするべきですか？設定されていない場合は、システムデフォルトを使用します,
I found these: ,私はこれらが見つかりました：,
ID,ID,
ID (name) of the entity whose property is to be set,プロパティが設定されるエンティティのID（名前）,
Icon will appear on the button,アイコンがボタン上に表示されます,
Identity Details,アイデンティティの詳細,
Idx,インデックス,
"If Apply Strict User Permission is checked and User Permission is defined for a DocType for a User, then all the documents where value of the link is blank, will not be shown to that User",「厳密なユーザー権限を適用」がチェックされ、ユーザーの文書タイプにユーザー権限が定義されている場合、リンク値の無いドキュメントは全て、該当ユーザーには表示されません,
If Checked workflow status will not override status in list view,チェックワークフローステータスは、リストビューのステータスを上書きしない場合,
If Owner,所有者の場合,
"If a Role does not have access at Level 0, then higher levels are meaningless.",「役割」にレベル0のアクセス権が無い場合、より高いレベルには意味がありません。,
"If checked, all other workflows become inactive.",チェックすると、他のすべてのワークフローが非アクティブになります。,
"If checked, this field will be not overwritten based on Fetch From if a value already exists.",チェックした場合、値がすでに存在する場合、このフィールドはFetch Fromに基づいて上書きされません。,
"If checked, users will not see the Confirm Access dialog.",チェックした場合、ユーザーにアクセス確認ダイアログが表示されません。,
"If disabled, this role will be removed from all users.",無効にした場合、この役割は全ユーザーから削除されます。,
"If enabled,  user can login from any IP Address using Two Factor Auth, this can also be set for all users in System Settings",有効になっている場合、ユーザーはTwo Factor認証を使用して任意のIPアドレスからログインできます。これは、システム設定ですべてのユーザーに対して設定することもできます。,
"If enabled, all users can login from any IP Address using Two Factor Auth. This can also be set only for specific user(s) in User Page",有効にすると、すべてのユーザーはTwo Factor Authを使用して任意のIPアドレスからログインできます。これは、ユーザーページの特定のユーザーに対してのみ設定することもできます,
"If enabled, changes to the document are tracked and shown in timeline",有効にすると、ドキュメントへの変更が追跡され、タイムラインに表示されます。,
"If enabled, document views are tracked, this can happen multiple times",有効にすると、ドキュメントビューが追跡されます。これは複数回発生する可能性があります。,
"If enabled, the document is marked as seen, the first time a user opens it",有効になっている場合、ユーザーが最初に開いたときに、文書は閲覧済みとしてマークされます。,
"If enabled, the password strength will be enforced based on the Minimum Password Score value. A value of 2 being medium strong and 4 being very strong.",有効にすると、パスワード強度は最小パスワードスコア値に基づいて適用されます。 2は中程度の強さであり、4は非常に強い値です。,
"If enabled, users who login from Restricted IP Address, won't be prompted for Two Factor Auth",有効にすると、制限付きIPアドレスからログインしたユーザーは、Two Factor Auth,
"If enabled, users will be notified every time they login. If not enabled, users will only be notified once.",有効にすると、ユーザーがログインするたびに通知されます。有効にしない場合、ユーザーには一度だけ通知されます。,
If non standard port (e.g. 587),非標準ポート（例えば587）の場合,
"If non standard port (e.g. 587). If on Google Cloud, try port 2525.",非標準ポート（587など）の場合Google Cloudの場合は、ポート2525をお試しください。,
"If not set, the currency precision will depend on number format",設定されていない場合、通貨精度は数値形式に依存します,
If the condition is satisfied user will be rewarded with the points. eg. doc.status == 'Closed'\n,条件が満たされればユーザーはポイントで報われます。例えば。 doc.status == &#39;終了&#39;,
"If the user has any role checked, then the user becomes a ""System User"". ""System User"" has access to the desktop",ユーザーがチェック任意の役割を持っている場合、ユーザは、「システム・ユーザー」になります。 「システム・ユーザーは、「デスクトップへのアクセス権を持っています,
"If these instructions where not helpful, please add in your suggestions on GitHub Issues.",これらの説明が役に立たなかった場合は、GitHubのIssueに提案を追加してください。,
"If this is checked, rows with valid data will be imported and invalid rows will be dumped into a new file for you to import later.",これをチェックすると、有効なデータを持つ行がインポートされ、後でインポートするために無効な行が新しいファイルにダンプされます。,
If user is the owner,ユーザが所有者である場合,
"If you are updating, please select ""Overwrite"" else existing rows will not be deleted.",更新した場合、「上書き」を選択してください。他の既存の行は削除されません。,
If you are updating/overwriting already created records.,既に作成されたレコードを更新/上書きする場合。,
"If you are uploading new records, ""Naming Series"" becomes mandatory, if present.",新しいレコードをアップロードする際には「命名シリーズ」が必須になります（存在する場合）,
"If you are uploading new records, leave the ""name"" (ID) column blank.",新しいレコードをアップロードする場合、「名前」（ID）欄を空白のままにします,
If you don't want to create any new records while updating the older records.,古いレコードを更新しているときに新しいレコードを作成したくない場合。,
"If you set this, this Item will come in a drop-down under the selected parent.",これを設定すると、このアイテムは選択された親の下にあるドロップダウンにあらわれます。,
"If you think this is unauthorized, please change the Administrator password.",これが不正であると考えられる場合は、管理者パスワードを変更してください。,
"If your data is in HTML, please copy paste the exact HTML code with the tags.",データがHTMLである場合は、タグを使用して正確なHTMLコードをコピー＆ペーストしてください。,
Ignore User Permissions,ユーザー権限を無視,
Ignore XSS Filter,XSSフィルターを無視,
Ignore attachments over this size,このサイズ以上の添付ファイルを無視,
Ignore encoding errors,エンコーディングエラーを無視する,
Ignored: {0} to {1},無視されます：{0} {1},
Illegal Access Token. Please try again,アクセストークンが不正です。もう一度やり直してください,
Illegal Document Status for {0},{0}の不正な文書ステータス,
Image Field,画像フィールド,
Image Link,画像リンク,
Image field must be a valid fieldname,Imageフィールドは、有効なフィールド名でなければなりません,
Image field must be of type Attach Image,Imageフィールドには、画像を添付タイプでなければなりません,
Images,画像,
Implicit,暗黙,
Import,インポート,
Import Email From,メールインポート元,
Import Status,インポートステータス,
Import Subscribers,登録者インポート,
Import Zip,Zipのインポート,
In Filter,フィルター内,
In Global Search,グローバル検索内,
In Grid View,グリッドビュー内,
In Hours,時間内,
In List View,リストビュー内,
In Preview,プレビュー中,
In Reply To,応答,
In Standard Filter,標準フィルタ内,
In Valid Request,無効なリクエスト,
In points. Default is 9.,ポイント（デフォルトは9）,
In seconds,すぐに,
Include Search in Top Bar,トップバーに検索欄を含む,
"Include symbols, numbers and capital letters in the password",パスワードに記号・数字・大文字を含む,
Incoming email account not correct,受信メールアカウントが正しくありません,
Incomplete login details,不完全なログインの詳細,
Incorrect User or Password,不正なユーザーまたはパスワード,
Incorrect Verification code,不正な確認コード,
Incorrect value in row {0}: {1} must be {2} {3},行 {0} に誤った値：{1} は {2} {3}でなければなりません,
Incorrect value: {0} must be {1} {2},不正な値：{0} は {1} {2}でなければなりません,
Index,索引,
Indicator,インジケータ,
Info,情報,
Info:,情報：,
Initial Sync Count,最初の同期カウント,
InnoDB,InnoDB,
Insert Above,上に挿入,
Insert After,後に挿入,
Insert After cannot be set as {0},{0}に設定することはできませんした後に挿入します,
"Insert After field '{0}' mentioned in Custom Field '{1}', with label '{2}', does not exist",カスタムフィールド'{1}'にラベル'{2}'の「後に挿入」フィールド'{0}'は存在しません。,
Insert Below,下に挿入,
Insert Column Before {0},{0}の前に列を挿入する,
Insert Style,スタイルを挿入,
Insert new records,新しいレコードを挿入,
Instructions Emailed,メールでの指示,
Insufficient Permission for {0},{0}のアクセス権が不十分です,
Int,数値,
Integration Request,統合リクエスト,
Integration Request Service,統合リクエストサービス,
Integration Type,統合型,
Integrations,インテグレーション,
Integrations can use this field to set email delivery status,統合では、メール配信のステータスを設定するために、このフィールドを使用することができます,
Internal Server Error,内部サーバーエラー,
Internal record of document shares,文書共有の内部レコード,
Introduce your company to the website visitor.,ウェブサイトの訪問者にあなたの会社をご紹介します。,
Introductory information for the Contact Us Page,お問い合わせページのための入門的な情報,
Invalid,無効,
"Invalid ""depends_on"" expression",無効な「depends_on」式,
Invalid Access Key ID or Secret Access Key.,無効なアクセスキーIDまたはシークレットアクセスキーです。,
Invalid CSV Format,無効なCSV形式,
Invalid Home Page,無効なホームページ,
Invalid Link,無効なリンク,
Invalid Login Token,無効なログイントークン,
Invalid Login. Try again.,ログインが不正です。再試行してください。,
Invalid Mail Server. Please rectify and try again.,無効なメールサーバです。修正してもう一度やり直してください。,
Invalid Outgoing Mail Server or Port,無効な送信メールサーバーまたはポート,
Invalid Output Format,無効な出力フォーマット,
Invalid Password,無効なパスワード,
Invalid Password:,無効なパスワード：,
Invalid Request,無効なリクエスト,
Invalid Search Field {0},無効な検索フィールド{0},
Invalid Subscription,無効なサブスクリプション,
Invalid Token,無効なトークン,
Invalid User Name or Support Password. Please rectify and try again.,無効なユーザー名またはサポートパスワード。修正してからもう一度やり直してください。,
Invalid column,列が無効です,
Invalid field name {0},無効なフィールド名{0},
Invalid fieldname '{0}' in autoname,AUTONAMEに無効なフィールド名 &#39;{0}&#39;,
Invalid file path: {0},無効なファイルパス：{0},
Invalid login or password,無効なログインまたはパスワード,
Invalid module path,モジュールパスが無効です,
Invalid naming series (. missing),無効な命名シリーズ（「.」がありません）,
Invalid payment gateway credentials,無効な支払いゲートウェイの資格情報,
Invalid recipient address,無効な受信者のアドレス,
Invalid {0} condition,無効な{0}条件,
Inverse,反転,
Is,あります,
Is Attachments Folder,添付ファイルフォルダ,
Is Child Table,子表,
Is Custom Field,カスタムフィールド,
Is First Startup,初回起動,
Is Folder,フォルダ,
Is Global,グローバル,
Is Globally Pinned,グローバルに固定されていますか？,
Is Home Folder,ホームフォルダ,
Is Mandatory Field,必須フィールド,
Is Pinned,固定されている,
Is Primary Contact,主連絡先,
Is Private,プライベート,
Is Published Field,公開フィールド,
Is Published Field must be a valid fieldname,公開フィールドは有効なフィールド名でなければなりません,
Is Single,シングル,
Is Spam,スパム,
Is Standard,標準,
Is Submittable,提出可能,
Is Table,表,
Is Your Company Address,あなたの会社の住所,
It is risky to delete this file: {0}. Please contact your System Manager.,このファイル {0} を削除することは危険です。システム管理者にお問い合わせください。,
Item cannot be added to its own descendants,アイテムは自身の子孫に追加することはできません,
JS,JS,
JSON,JSON,
JavaScript Format: frappe.query_reports['REPORTNAME'] = {},JavaScript形式：frappe.query_reports ['REPORTNAME'] = {},
Javascript to append to the head section of the page.,Javascriptをページの先頭のセクションに追加する。,
Jinja,ジンジャ,
John Doe,ジョン・ドウ,
Kanban,かんばん,
Kanban Board Column,かんばんボード列,
Kanban Board Name,かんばんボード名,
Karma,蓄積ポイント,
Keep track of all update feeds,すべての更新フィードを追跡する,
Keeps track of all communications,すべての通信を追跡します,
Key,キー,
Knowledge Base,ナレッジベース,
Knowledge Base Contributor,ナレッジベースコントリビューター,
Knowledge Base Editor,ナレッジベースエディター,
LDAP Email Field,LDAPメールフィールド,
LDAP First Name Field,LDAP名前フィールド,
LDAP Not Installed,LDAPがインストールされていません,
LDAP Search String,LDAP検索文字列,
"LDAP Search String needs to end with a placeholder, eg sAMAccountName={0}",LDAP検索文字列はプレースホルダーで終わる必要があります。例えばsAMAccountName = {0},
LDAP Security,LDAPセキュリティ,
LDAP Server Url,LDAPサーバURL,
LDAP Username Field,LDAPユーザ名フィールド,
LDAP is not enabled.,LDAPが有効になっていません。,
Label Help,ラベルのヘルプ,
Label and Type,ラベルとタイプ,
Label is mandatory,ラベルは必須です,
Landing Page,ランディングページ,
Language,言語,
Language Code,言語コード,
"Language, Date and Time settings",言語、日付と時刻の設定,
Last Active,最後のアクティブ,
Last IP,最新のIP,
Last Known Versions,最後の既知のバージョン,
Last Login,最新ログイン,
Last Message,最新メッセージ,
Last Modified By,最終更新者,
Last Modified Date,最終更新日,
Last Modified On,最終更新,
Last Month,先月,
Last Point Allocation Date,最終ポイント割当日,
Last Quarter,前四半期,
Last Synced On,最後に同期しました,
Last Updated By,最終更新者,
Last Updated On,最終更新日,
Last User,最後のユーザー,
Last Week,先週,
Last Year,昨年,
Last synced {0},最後に同期しました{0},
Leave a Comment,コメントを残す,
Leave blank to repeat always,常に繰り返す場合は空白のままにします,
Leave this conversation,この会話を残します,
Left this conversation,会話から退席,
Length,長さ,
Length of {0} should be between 1 and 1000,{0}の長さは、1〜1000の間であるべきです,
Let's avoid repeated words and characters,繰り返し単語または文字列を回避してみましょう,
Let's prepare the system for first use.,システムを準備して使い始めましょう,
Letter,手紙,
Letter Head Based On,に基づくレターヘッド,
Letter Head Image,レターヘッド画像,
Letter Head Name,レターヘッドの名前,
Letter Head in HTML,HTMLでレターヘッド,
Level Name,レベル名,
Liked,いいね!済,
Liked By,いいね!,
Liked by {0},{0} にいいね!されています,
Likes,いいね！,
Limit Number of DB Backups,DBバックアップの数を制限する,
Line,ライン,
Link DocType,リンク文書タイプ,
Link Expired,リンクが切れた,
Link Name,リンク名,
Link Title,リンクタイトル,
"Link that is the website home page. Standard Links (index, login, products, blog, about, contact)",Webサイトホームページのリンク。\n標準リンク（インデックス、ログイン、商品、ブログ、運営者、連絡先）,
Link to the page you want to open. Leave blank if you want to make it a group parent.,開きたいページへのリンク。グループの親にする場合は空白のままにします。,
Linked,リンク済,
Linked With,とリンク,
Linked with {0},{0}とリンクしました,
Links,リンク,
List,リスト,
List Filter,リストフィルター,
List View Setting,リストビュー設定,
List a document type,ドキュメントタイプを一覧表示します,
"List as [{""label"": _(""Jobs""), ""route"":""jobs""}]","[{""label"": _(""Jobs""), ""route"":""jobs""}] のようなリスト",
List of backups available for download,ダウンロード可能なバックアップのリスト,
List of patches executed,実行されるパッチのリスト,
List of themes for Website.,ウェブサイトのテーマのリスト,
Load Balancing,負荷分散,
Loading,読み込み中,
Local DocType,ローカル文書タイプ,
Local Fieldname,ローカルフィールド名,
Local Primary Key,ローカルプライマリキー,
Locals,ローカル,
Log Details,ログ詳細,
Log of Scheduler Errors,スケジューラーのエラーログ,
Log of error during requests.,リクエスト中のエラーのログ。,
Log of error on automated events (scheduler).,自動化されたイベント（スケジューラ）のエラーログ。,
Logged Out,ログアウト,
Logged in as Guest or Administrator,ゲストまたは管理者としてログイン,
Login,ログイン,
Login After,ログイン後,
Login Before,ログイン前,
Login Id is required,ログインIDが必要です,
Login Required,ログインが必要,
Login Verification Code from {},{}のログイン確認コード,
Login and view in Browser,ブラウザでのログインと表示,
Login not allowed at this time,この時点でログインは許可されていません。,
"Login session expired, refresh page to retry",ログインセッションの有効期限が切れました。ページを更新して再試行してください。,
Login to comment,コメントするにはログインしてください,
Login token required,ログイントークンが必要です,
Login with LDAP,LDAPを使用してログイン,
Logout,ログアウト,
Long Text,長いテキスト,
Looks like something is wrong with this site's Paypal configuration.,このサイトのPaypal設定に何らかの問題があるようです。,
Looks like something is wrong with this site's payment gateway configuration. No payment has been made.,何かが、このサイトの決済ゲートウェイの設定に問題があるように見えます。ご入金がなされていません。,
"Looks like something went wrong during the transaction. Since we haven't confirmed the payment, Paypal will automatically refund you this amount. If it doesn't, please send us an email and mention the Correlation ID: {0}.",取引中に何らかの問題が生じたようです。支払が確認されていないため、Paypalから自動的に返金されますが、返金されない場合は相関ID {0} をメールにてお送りください。,
Madam,マダム,
Main Section,メインセクション,
"Make ""name"" searchable in Global Search",グローバル検索で検索可能にする &quot;名前&quot;,
Make use of longer keyboard patterns,長いキーボードパターンを利用します,
Manage Third Party Apps,サードパーティーのアプリケーション管理,
Mandatory Information missing:,必須情報の不足：,
Mandatory field: set role for,必須フィールド：役割設定先,
Mandatory field: {0},必須フィールド：{0},
"Mandatory fields required in table {0}, Row {1}",表{0} 行{1}には必須フィールドが必要です,
Mandatory fields required in {0},{0}に必要な必須フィールド,
Mandatory:,必須：,
Mapping Name,マッピング名,
Mappings,マッピング,
Mark as Read,既読としてマーク,
Mark as Spam,スパムとしてマーク,
Mark as Unread,未読としてマーク,
Markdown,Markdown,
Markdown Editor,マークダウンエディタ,
Marked As Spam,スパムとしてマーク,
Max 500 records at a time,一度に最大500レコード,
Max Attachment Size (in MB),添付ファイル最大容量（MB単位）,
Max Attachments,最大添付ファイル,
Max Length,最大の長さ,
Max Value,最大値,
Max width for type Currency is 100px in row {0},行{0}の通貨表記の最大幅は100pxです,
Maximum Attachment Limit for this record reached.,レコードの添付ファイルの最大制限値に到達しました,
Maximum {0} rows allowed,最大 {0} 行許容,
"Meaning of Submit, Cancel, Amend",提出、キャンセル、修正の意味,
Mention transaction completion page URL,取引完了ページのURLを掲示,
Mentions,覚え書き,
Menu,メニュー,
Merchant ID,販売者ID,
Merge with existing,既存のものとマージ,
Merging is only possible between Group-to-Group or Leaf Node-to-Leaf Node,マージはグループ同士またはリーフノード同士でのみ可能です,
Message Count,メッセージ数,
Message ID,メッセージID,
Message Parameter,メッセージパラメータ,
Message Preview,メッセージのプレビュー,
Message clipped,メッセージクリッピング,
Message not setup,メッセージは設定されません,
Message to be displayed on successful completion (only for Guest users),正常終了時に表示されるメッセージ（ゲストユーザのみ）,
Message-id,メッセージID,
Meta Tags,メタタグ,
Migration ID Field,移行IDフィールド,
Milestone,マイルストーン,
Milestone Tracker,マイルストーントラッカー,
Minimum Password Score,最小パスワードスコア,
Miss,ミス,
Missing Fields,行方不明のフィールド,
Missing parameter Kanban Board Name,かんばんボード名にパラメータが不足,
Missing parameters for login,ログインパラメータ不足,
Models (building blocks) of the Application,アプリケーションのモデル（ビルディング・ブロック）,
Modified By,更新者,
Module,モジュール,
Module Def,モジュール定義,
Module Name,モジュール名,
Module Not Found,モジュールが見つかりません,
Module Path,モジュールパス,
Module to Export,モジュールがエクスポートします,
Modules HTML,モジュールHTML,
Monospace,等幅,
More articles on {0},{0}上の他の記事,
More content for the bottom of the page.,より多くのコンテンツがページ下部にあります。,
Most Used,最多使用,
Move To,へ引っ越す,
Move To Trash,ゴミ箱に移動,
Move to Row Number,行番号に移動,
Mr,氏,
Mrs,夫人,
Ms,女史,
Multiple root nodes not allowed.,複数のルートノードは許可されていません。,
Multiplier Field,乗数フィールド,
"Must be of type ""Attach Image""",「画像添付」タイプでなければなりません,
Must have report permission to access this report.,このレポートにアクセスするには、レポートの権限が必要です。,
Must specify a Query to run,実行するクエリを指定する必要があります,
Mute Sounds,消音,
MyISAM,MyISAMテーブル,
Name Case,名入れ,
Name cannot contain special characters like {0},名前には {0} などの特殊文字を含めることはできません,
Name not set via prompt,プロンプトから名前が設定されていません,
Name of the Document Type (DocType) you want this field to be linked to. e.g. Customer,"このフィールドにリンクしたい文書タイプの名前。（例：""Customer""）",
Name of the new Print Format,新しい印刷形式の名前,
Name of {0} cannot be {1},{0}の名前は{1}にすることはできません,
Names and surnames by themselves are easy to guess.,姓・名自体が推測容易です。,
Naming,命名,
"Naming Options:\n<ol><li><b>field:[fieldname]</b> - By Field</li><li><b>naming_series:</b> - By Naming Series (field called naming_series must be present</li><li><b>Prompt</b> - Prompt user for a name</li><li><b>[series]</b> - Series by prefix (separated by a dot); for example PRE.#####</li>\n<li><b>format:EXAMPLE-{MM}morewords{fieldname1}-{fieldname2}-{#####}</b> - Replace all braced words (fieldnames, date words (DD, MM, YY), series) with their value. Outside braces, any characters can be used.</li></ol>",命名オプション： <ol><li> <b>フィールド：[フィールド名]</b> - フィールド別</li><li> <b>naming_series：</b> - 命名シリーズによる（nameing_seriesという名前のフィールドが存在しなければならない</li><li> <b>プロンプト</b> - ユーザーに名前を入力する</li><li> <b>[シリーズ]</b> - 接頭語によるシリーズ（ドットで区切られます）。たとえばPRE。##### </li><li> <b>形式：例 -  {MM} morewords {fieldname1}  -  {fieldname2}  -  {#####}</b> - すべてのブレースされた単語（フィールド名、日付ワード（DD、MM、YY）、シリーズ）をその値で置き換えます。中括弧の外側には、任意の文字を使用できます。 </li></ol>,
Naming Series mandatory,シリーズ名は必須です,
Nested set error. Please contact the Administrator.,入れ子集合のエラーです。\n管理者に連絡してください。,
New Activity,新しい活動,
New Chat,新しいチャット,
New Comment on {0}: {1},新しいコメント{0}：{1},
New Connection,新しい接続,
New Custom Print Format,新しいカスタム印刷フォーマット,
New Email,新しいメール,
New Email Account,新しいメールアカウント,
New Event,新しいイベント,
New Folder,新規フォルダ,
New Kanban Board,新しいかんばんボード,
New Message from Website Contact Page,サイトお問い合わせページから新規メッセージ,
New Name,新しい名前,
New Newsletter,新しいニュースレター,
New Password,新しいパスワード,
New Password Required.,新しいパスワードが必要です。,
New Print Format Name,新しい印刷フォーマット名,
New Report name,新しいレポート名,
New Value,新しい値,
New data will be inserted.,新しいデータが挿入されます。,
New updates are available,新しい更新が利用可能です,
New value to be set,新しい値を設定する必要があります,
New {0},新しい{0},
New {} releases for the following apps are available,次のアプリの新しい{}リリースが利用可能です,
Newsletter Email Group,ニュースレターの電子メールのグループ,
Newsletter Manager,ニュースレターマネージャー,
Newsletter has already been sent,ニュースレターはすでに送信されています,
"Newsletters to contacts, leads.",連絡先・リードへのニュースレター,
Next Action Email Template,次のアクション電子メールテンプレート,
Next Actions HTML,次のアクションHTML,
Next Schedule Date,次のスケジュール日,
Next Scheduled Date,次回の予定日,
Next State,次の状態,
Next Sync Token,次の同期トークン,
Next actions,次のアクション,
No Active Sessions,アクティブなセッションがありません,
No Copy,コピーがありません,
No Email Account,メールアカウントがありません,
No Email Accounts Assigned,割り当てられたメールアカウントがありません,
No Emails,メールがありません,
No Label,ラベルがありません,
No Permissions Specified,許可が指定されていません,
No Permissions set for this criteria.,この条件には権限が設定されていません。,
No Preview,プレビューなし,
No Preview Available,プレビュー不可,
No Printer is Available.,利用可能なプリンタがありません。,
No Results,結果がありません,
No Tags,タグがありません,
No alerts for today,今日のアラートはありません,
No comments yet,まだコメントはありません,
No comments yet. Start a new discussion.,コメントはまだありません。新しいディスカッションを開始します。,
No data found in the file. Please reattach the new file with data.,ファイルにデータが見つかりません。新しいファイルをデータで再接続してください。,
No document found for given filters,指定されたフィルタでドキュメントが見つかりません,
"No fields found that can be used as a Kanban Column. Use the Customize Form to add a Custom Field of type ""Select"".",かんばん列として使用できるフィールドが見つかりませんでした。 [選択]フォームを使用して、 &quot;選択&quot;タイプのカスタムフィールドを追加します。,
No file attached,添付ファイルがありません,
No further records,これ以上のレコードはありません,
No matching records. Search something new,一致するレコードはありません。新しい何かを検索,
"No need for symbols, digits, or uppercase letters.",記号・数字・大文字は必要ありません。,
No of Columns,列の数,
No of Rows (Max 500),行番号（最大500）,
No of emails remaining to be synced,メールのいいえが同期されるように残りません,
No permission for {0},{0} には許可がありません,
No permission to '{0}' {1},権限がありません '{0}' {1},
No permission to read {0},{0}を読むための権限がありません,
No permission to {0} {1} {2},権限がありません {0} {1} {2},
No records deleted,削除されたレコードはありません,
No records present in {0},{0}にレコードが存在しません,
No records tagged.,タグ付けされたレコードがありません,
No template found at path: {0},テンプレートが次のパスに見つかりません：{0},
No {0} found,{0}が見つかりません,
No {0} mail,{0}メールはありません,
No {0} permission,{0}の権限はありません,
None: End of Workflow,なし：ワークフローの終了,
Not Allowed: Disabled User,許可されていない：無効なユーザー,
Not Ancestors Of,祖先ではない,
Not Descendants Of,の子孫ではない,
Not Equals,等しくない,
Not In,Not In,
Not Linked to any record,レコードにリンクされていません,
Not Published,公開されていません,
Not Saved,保存されていません,
Not Seen,閲覧なし,
Not Sent,送信されていません,
Not Set,設定されていません,
Not a valid Comma Separated Value (CSV File),有効なカンマ区切り値（CSVファイル）がありません,
Not a valid User Image.,有効なユーザーイメージではありません。,
Not a valid Workflow Action,有効なワークフローアクションではありません,
Not a valid user,有効なユーザーではありません,
Not a zip file,zipファイルではありません,
Not allowed for {0}: {1},{0}には使用できません：{1},
"You are not allowed to access {0} because it is linked to {1} '{2}' in row {3}, field {4}",{0}にアクセスすることはできません。{3}行{4}フィールドにリンクされている{1} '{2}',
You are not allowed to access this {0} record because it is linked to {1} '{2}' in field {3},この{0}レコードにアクセスすることはできません。{3}フィールドにリンクされている{1} '{2}',
Not allowed to Import,インポートは許可されていません,
Not allowed to change {0} after submission,送信後{0}を変更することはできません,
Not allowed to print cancelled documents,キャンセルされた文書を印刷することはできません,
Not allowed to print draft documents,下書を印刷することはできません,
Not enough permission to see links,リンクを表示する権限がありません,
Not in Developer Mode,非開発者モード,
Not in Developer Mode! Set in site_config.json or make 'Custom' DocType.,開発者モードではありません！site_config.jsonを設定するか「カスタム」文書タイプを作成してください,
Note Seen By,から見たノート,
Note:,注意：,
Note: By default emails for failed backups are sent.,注：デフォルトでは、失敗したバックアップの電子メールが送信されます。,
Note: Changing the Page Name will break previous URL to this page.,注：ページ名を変更すると、このページの前のURLが破損します。,
"Note: For best results,  images must be of the same size and width must be greater than height.",注意：最良の結果を得るには、画像は同じサイズで、幅は高さよりも大きい必要があります。,
Note: Multiple sessions will be allowed in case of mobile device,注：複数のセッションは、モバイルデバイスの場合に許可されます,
Nothing to show,表示するものがありません,
Nothing to update,更新するものはありません,
Notification,お知らせ,
Notification Recipient,通知受信者,
Notification Tones,通知音,
Notifications,通知,
Notifications and bulk mails will be sent from this outgoing server.,通知および一括送信メールは、この送信サーバーから送信されます。,
Notify Users On Every Login,ログインごとにユーザーに通知する,
Notify if unreplied,返信されていない場合に通知,
Notify if unreplied for (in mins),返信されていない場合に通知（分）,
Notify users with a popup when they log in,ユーザーがログインする際ポップアップで通知する,
Number Format,数の書式,
Number of Backups,バックアップ数,
Number of DB Backups,DBバックアップの数,
Number of DB backups cannot be less than 1,DBバックアップの数は1未満ではありません,
Number of columns for a field in a Grid (Total Columns in a grid should be less than 11),グリッド内のフィールドの列数（グリッド内の合計の列が11未満でなければなりません）,
Number of columns for a field in a List View or a Grid (Total Columns should be less than 11),リストビューまたはグリッド内のフィールドの列数（合計列が11未満でなければなりません）,
OAuth Authorization Code,OAuth認証コード,
OAuth Bearer Token,OAuth Bearer Token,
OAuth Client,OAuth Client,
OAuth Provider Settings,OAuth Provider設定,
OTP App,OTPアプリ,
OTP Issuer Name,OTP Issuer Name,
OTP Secret has been reset. Re-registration will be required on next login.,OTP Secretがリセットされました。次のログイン時に再登録が必要になります。,
OTP secret can only be reset by the Administrator.,OTP Secretは管理者によってのみリセット可能です。,
Office,事務所,
Office 365,Office 365,
Old Password,以前のパスワード,
Old Password Required.,古いパスワードが必要です。,
Older backups will be automatically deleted,古いバックアップは自動的に削除されます,
"On {0}, {1} wrote:",{0}で{1}が記述：,
"Once submitted, submittable documents cannot be changed. They can only be Cancelled and Amended.",一度提出された提出可能な文書は変更することができません。キャンセルと修正のみ可能です。,
"Once you have set this, the users will only be able access documents (eg. Blog Post) where the link exists (eg. Blogger).",この設定によりユーザーはリンクのあるドキュメント（ブログ記事など）にアクセス可能となります（例：Blogger）。,
One Last Step,最後のステップ,
One Time Password (OTP) Registration Code from {},{}からのワンタイムパスワード（OTP）登録コード,
Only 200 inserts allowed in one request,1要求につき200件まで許容されます,
Only Administrator can delete Email Queue,管理者だけがメールキューを削除することができます,
Only Administrator can edit,管理者だけが編集することができます,
Only Administrator can save a standard report. Please rename and save.,標準レポートを保存することができるのは管理者だけです。名前を変更し、保存してください。,
Only Administrator is allowed to use Recorder,管理者のみがRecorderを使用することを許可されています,
Only Allow Edit For,編集のみ可,
Only Send Records Updated in Last X Hours,直近のX時間内に更新されたレコードのみを送信,
Only mandatory fields are necessary for new records. You can delete non-mandatory columns if you wish.,新しいレコードには必須フィールドのみが必要です。非必須の列は削除することができます。,
Only standard DocTypes are allowed to be customized from Customize Form.,標準のDocTypeだけがフォームのカスタマイズからカスタマイズできます。,
Only users involved in the document are listed,この文書に関与しているユーザーのみが一覧表示されます,
Only {0} emailed reports are allowed per user,唯一の{0}のレポートは、ユーザーごとに許可されている電子メールで送信,
Oops! Something went wrong,問題が起きました,
"Oops, you are not allowed to know that",許可されていません,
Open Link,リンクを開く,
Open Source Applications for the Web,Web用のオープンソースアプリケーション,
Open Translation,オープン翻訳,
Open a dialog with mandatory fields to create a new record quickly,必須項目を含むダイアログを開いて新しいレコードを素早く作成する,
Open a module or tool,モジュールまたはツールを開く,
Open your authentication app on your mobile phone.,携帯電話で認証アプリを開きます。,
Open {0},{0}を開く,
Opened,オープン済,
Operator must be one of {0},演算子は{0},
Option 1,オプション1,
Option 2,オプション2,
Option 3,オプション3,
Optional: Always send to these ids. Each Email Address on a new row,任意：常にこれらのIDに送ります。新しい行の各メールアドレス,
Optional: The alert will be sent if this expression is true,（任意）この式がtrueの場合、アラートが送信されます,
Options 'Dynamic Link' type of field must point to another Link Field with options as 'DocType',オプション「ダイナミックリンク」型のフィールドは「DocType」オプションなどの別のリンクフィールドを指している必要があります,
Options Help,オプションのヘルプ,
Options for select. Each option on a new line.,選択オプション。各オプションは新しい行となります。,
Options not set for link field {0},リンクフィールド{0}に設定されていないオプション,
Or login with,またはでログイン,
Order,注文,
Org History,沿革,
Org History Heading,沿革見出し,
Orientation,オリエンテーション,
Original Value,元の値,
Outgoing email account not correct,送信先メールアカウントが正しくありません,
Outlook.com,Outlook.com,
Output,出力,
PDF,PDF,
PDF Page Size,PDFページサイズ,
PDF Settings,PDF設定,
PDF generation failed,PDFの生成に失敗しました,
PDF generation failed because of broken image links,画像リンクが壊れているため、PDFの生成に失敗しました,
"PDF printing via ""Raw Print"" is not yet supported. Please remove the printer mapping in Printer Settings and try again.",「Raw Print」によるPDF印刷はまだサポートされていません。プリンタの設定でプリンタのマッピングを削除してから、もう一度やり直してください。,
Page HTML,ページのHTML,
Page Length,ページの長さ,
Page Name,ページ名,
Page Settings,ページ設定,
Page has expired!,ページの有効期限が切れました。,
Page not found,ページが見つかりません,
Page to show on the website\n,ウェブサイトに表示するページ,
Pages in Desk (place holders),デスクにあるページ（プレースホルダ）,
Parent,親,
Parent Error Snapshot,親エラースナップショット,
Parent Label,親ラベル,
Parent Table,親テーブル,
Parent is required to get child table data,子テーブルデータを取得するには親が必要です,
Parent is the name of the document to which the data will get added to.,Parentは、データが追加されるドキュメントの名前です。,
Partial Success,部分的な成功,
Partially Successful,部分的に成功,
Participants,参加者,
Passive,消極的,
Password Reset,パスワードのリセット,
Password Updated,パスワード更新,
Password for Base DN,ベースDNのパスワード,
Password is required or select Awaiting Password,パスワードが必要か、待ちパスワードを選択されています,
Password not found,パスワードが見つかりません,
Password reset instructions have been sent to your email,パスワードのリセット手順をメールで送信しました,
Paste,貼付,
Patch,パッチ,
Patch Log,パッチログ,
Path to CA Certs File,CA証明書ファイルへのパス,
Path to Server Certificate,サーバー証明書へのパス,
Path to private Key File,秘密鍵ファイルへのパス,
PayPal Settings,PayPalの設定,
PayPal payment gateway settings,PayPalの支払いゲートウェイの設定,
Payment Cancelled,お支払いキャンセル,
Payment Success,支払成功,
Pending Approval,承認待ちの,
Pending Verification,保留中の確認,
Percent,割合（%）,
Percent Complete,完了率,
Perm Level,権限レベル,
Permanent,恒久的な,
Permanently Cancel {0}?,完全に{0}をキャンセルしますか？,
Permanently Submit {0}?,完全に{0}を提出しますか？,
Permanently delete {0}?,{0}を完全に削除しますか？,
Permission Error,許可エラー,
Permission Level,権限レベル,
Permission Levels,権限レベル,
Permission Rules,権限ルール,
Permissions,権限,
Permissions are automatically applied to Standard Reports and searches.,権限は自動的に標準レポートと検索に適用されます。,
"Permissions are set on Roles and Document Types (called DocTypes) by setting rights like Read, Write, Create, Delete, Submit, Cancel, Amend, Report, Import, Export, Print, Email and Set User Permissions.",権限（閲覧、書込、作成、削除、提出、キャンセル、修正、レポート、インポート、エクスポート、印刷、メール、および設定ユーザー権限設定のような）を設定することで、役割と文書タイプにアクセス権限が設定されます。,
Permissions at higher levels are Field Level permissions. All Fields have a Permission Level set against them and the rules defined at that permissions apply to the field. This is useful in case you want to hide or make certain field read-only for certain Roles.,より高いレベルでの権限は、フィールドレベルの権限です。すべてのフィールドはそれぞれ、設定権限レベルと権限がフィールドに適用されることで定義されたルールがあります。\nこれは、特定の役割のための読み取り専用フィールドを非表示にしたり作成したりする場合に便利です。,
"Permissions at level 0 are Document Level permissions, i.e. they are primary for access to the document.",レベル0の権限は、文書レベルの権限、つまり文書にアクセスするための主権限です。,
Permissions get applied on Users based on what Roles they are assigned.,アクセス権限は、割り当てられている役割に基づいてユーザーに適用されます。,
Personal,個人情報,
Personal Data Deletion Request,個人データ削除依頼,
Personal Data Download Request,個人データダウンロード要求,
Phone No.,電話番号,
Pick Columns,列を選択,
Plant,プラント,
Please Duplicate this Website Theme to customize.,ウェブサイトのテーマをカスタマイズするには複製してください,
Please Enter Your Password to Continue,続行するパスワードを入力してください,
Please Install the ldap3 library via pip to use ldap functionality.,LDAP機能を使用するには、pipでldap3ライブラリをインストールしてください。,
Please Update SMS Settings,SMSの設定を更新してください,
Please add a subject to your email,あなたの電子メールに件名を追加してください,
Please ask your administrator to verify your sign-up,あなたのサインアップを検証するために、管理者にお問い合わせください,
Please attach a file first.,ファイルを添付してください,
Please attach an image file to set HTML,HTMLを設定するための画像ファイルを添付してください,
Please check your email for verification,検証のためにあなたの電子メールをチェックしてください,
Please check your registered email address for instructions on how to proceed. Do not close this window as you will have to return to it.,続行する方法については、登録された電子メールアドレスを確認してください。このウィンドウを閉じる必要はありません。,
Please close this window,このウィンドウを閉じてください,
Please confirm your action to {0} this document.,あなたの処置を{0}に確認してください。,
Please do not change the rows above {0},{0}の上の行を変更しないでください,
Please do not change the template headings.,テンプレートの見出しを変更しないでください。,
Please duplicate this to make changes,複製した後、変更してください,
Please enable developer mode to create new connection,新しい接続を作成するには開発者モードを有効にしてください,
Please ensure that your profile has an email address,プロフィールにメールアドレスが入力されているかご確認ください,
Please enter Access Token URL,アクセストークンURLを入力してください,
Please enter Authorize URL,承認URLを入力してください,
Please enter Base URL,ベースURLを入力してください,
Please enter Client ID before social login is enabled,ソーシャルログインを有効にする前にクライアントIDを入力してください,
Please enter Client Secret before social login is enabled,社会的なログインが有効になる前にクライアントの秘密を入力してください,
Please enter Redirect URL,リダイレクトURLを入力してください,
Please enter the password,パスワードを入力してください,
Please enter valid mobile nos,有効な携帯電話番号を入力してください,
Please enter values for App Access Key and App Secret Key,App Access KeyとApp Secret Keyを入力してください,
Please make sure that there are no empty columns in the file.,空のカラムがファイル内に存在しないことを確認してください。,
Please make sure the Reference Communication Docs are not circularly linked.,参照通信文書が循環リンクされていないことを確認してください。,
Please refresh to get the latest document.,最新の書類を取得するために更新してください,
Please save before attaching.,添付する前に保存してください,
Please save the Newsletter before sending,送信する前にニュースレターを保存してください,
Please save the document before assignment,割当の前に文書を保存してください,
Please save the document before removing assignment,割当を削除する前に文書を保存してください,
Please save the report first,最初のレポートを保存してください,
Please select DocType first,文書タイプを選択してください,
Please select Entity Type first,まずエンティティタイプを選択してください,
Please select Minimum Password Score,最小パスワードスコアを選択してください,
Please select a Amount Field.,金額フィールドを選択してください。,
Please select a file or url,ファイルまたはURLを選択してください,
Please select a new name to rename,名前を変更する新しい名前を選択してください,
Please select a valid csv file with data,有効なCSVファイルを選択してください,
Please select another payment method. PayPal does not support transactions in currency '{0}',他のお支払方法を選択してください。 PayPalは通貨「{0}」での取引をサポートしていません,
Please select another payment method. Razorpay does not support transactions in currency '{0}',他のお支払方法を選択してください。 Razorpayは通貨「{0}」での取引をサポートしていません,
Please select atleast 1 column from {0} to sort/group,{0}ソートする/グループからの少なくとも1列を選択してください,
Please select document type first.,まず文書タイプを選択してください。,
Please select the Document Type.,ドキュメントタイプを選択してください。,
Please set Base URL in Social Login Key for Frappe,FrappeのソーシャルログインキーにベースURLを設定してください,
Please set Dropbox access keys in your site config,サイト設定でDropboxのアクセスキーを設定してください,
Please set a printer mapping for this print format in the Printer Settings,プリンタ設定でこの印刷フォーマットのプリンタマッピングを設定してください。,
Please set filters,フィルタを設定してください,
Please set filters value in Report Filter table.,レポートフィルタテーブル内のフィルタ値を設定してください。,
"Please setup SMS before setting it as an authentication method, via SMS Settings",認証方法としてSMSを設定する前に、SMS設定を完了してください,
Please setup a message first,最初にメッセージを設定してください,
Please specify which date field must be checked,フィールドをチェックする必要のある日付を指定してください,
Please specify which value field must be checked,チェックする必要がある値フィールドを指定してください,
Please try again,もう一度やり直してください,
Please verify your Email Address,あなたのメールアドレスを確認してください,
Point Allocation Periodicity,ポイント割り当ての周期性,
Points,ポイント,
Points Given,与えられたポイント,
Port,ポート,
Portal Menu,ポータルメニュー,
Portal Menu Item,ポータルメニューアイテム,
Post,投稿,
Post Comment,コメントを投稿,
Postal,郵便,
Postal Code,郵便番号,
Postprocess Method,後処理方法,
Posts,投稿,
Posts by {0},投稿 {0},
Posts filed under {0},{0}配下の投稿,
Precision,精度,
Precision should be between 1 and 6,精度は1と6の間でなければなりません,
Predictable substitutions like '@' instead of 'a' don't help very much.,予測可能なように置換 &#39;@&#39;の代わりに &#39;&#39;あまり役立ちません。,
Preferred Billing Address,優先請求先住所,
Preferred Shipping Address,優先出荷住所,
Prepared Report,準備レポート,
Preparing Report,レポート作成,
Preprocess Method,前処理方法,
Press Enter to save,Enterキーを押して保存します,
Preview HTML,プレビューHTML,
Preview Message,プレビューメッセージ,
Previous,前,
Previous Hash,前のハッシュ,
Primary Color,原色,
Print Documents,ドキュメントを印刷する,
Print Format Builder,印刷書式ビルダー,
Print Format Help,印刷書式ヘルプ,
Print Format Type,印刷書式タイプ,
Print Format {0} is disabled,印刷書式{0}は無効になっています,
Print Hide,印刷を隠す,
Print Hide If No Value,値なしの場合プリントを隠します,
Print Sent to the printer!,印刷プリンタに送信します。,
Print Server,プリントサーバー,
Print Style,印刷スタイル,
Print Style Name,印刷スタイル名,
Print Style Preview,印刷スタイルプレビュー,
Print Width,印刷幅,
"Print Width of the field, if the field is a column in a table",フィールドがテーブルの列である場合、フィールドの幅で印刷,
Print with letterhead,レターヘッド付き印刷,
Printer,プリンター,
Printer Mapping,プリンタマッピング,
Printer Name,プリンタ名,
Printer Settings,プリンター設定,
Printing failed,印刷に失敗しました,
Private Key,秘密鍵,
Private and public Notes.,プライベートとパブリックの注意。,
ProTip: Add <code>Reference: {{ reference_doctype }} {{ reference_name }}</code> to send document reference,ProTip：追加<code>Reference: {{ reference_doctype }} {{ reference_name }}</code>送信するドキュメント参照,
Processing,処理,
Processing...,処理...,
Prof,教授,
Progress,進捗,
Property Setter,属性設定,
Property Setter overrides a standard DocType or Field property,属性設定は標準で文書タイプまたはフィールド属性を上書きします,
Property Type,属性タイプ,
Provider,プロバイダ,
Provider Name,プロバイダ名,
Public Key,公開鍵,
Publishable Key,公開可能なキー,
Published On,公開,
Pull,プル,
Pull Failed,プル失敗,
Pull Insert,プルインサート,
Pull Update,プルアップデート,
Push,プッシュ,
Push Delete,プッシュ削除,
Push Failed,プッシュ失敗,
Push Insert,プッシュインサート,
Push Update,プッシュアップデート,
Python Module,Pythonモジュール,
Pyver,Pyver,
QR Code,QRコード,
QR Code for Login Verification,ログイン照合用QRコード,
QZ Tray Connection Active!,QZトレイ接続がアクティブです。,
QZ Tray Failed: ,QZトレイが失敗しました：,
Quarter Day,クォーター日,
Query,クエリー,
Query Report,クエリーレポート,
Query must be a SELECT,質問は選択肢でなければなりません。,
Queue should be one of {0},キューは{0}のいずれかでなければなりません,
Queued for backup. It may take a few minutes to an hour.,バックアップのためにキューに入れられました。それは時間に数分かかることがあります。,
Queued for backup. You will receive an email with the download link,バックアップのために待機します。ダウンロードリンクが記載されたメールが届きます,
Quick Help for Setting Permissions,権限設定のクイックヘルプ,
Rating: ,評価：,
Raw Commands,生のコマンド,
Raw Email,生のEメール,
Raw Printing,生の印刷,
Razorpay Payment gateway settings,Razorpay支払いゲートウェイの設定,
Razorpay Settings,Razorpay設定,
Re: ,Re：,
Re: {0},Re: {0},
Read,読む,
Read Only,読み取り専用,
Read by Recipient,受信者が読む,
Read by Recipient On,受信者で読む,
Rebuild,再構築,
Receiver Parameter,受領者パラメータ,
Recent years are easy to guess.,近い年は推測するのが簡単です。,
Recipient,受信者,
Recipient Unsubscribed,受信者未登録,
Record does not exist,レコードが存在しません,
Records for following doctypes will be filtered,次のdoctypeのレコードはフィルタリングされます,
Redirect To,リダイレクト,
Redirect URI Bound To Auth Code,認証コードにバインドされたURIをリダイレクト,
Redirect URIs,URIをリダイレクト,
Redis cache server not running. Please contact Administrator / Tech support,Redisのキャッシュサーバが実行されていません。管理者/技術サポートにお問い合わせください,
Ref DocType,参照文書タイプ,
Ref Report DocType,RefレポートDocType,
Reference DocName,参照DocName,
Reference DocType and Reference Name are required,参考文書タイプと参照名が必要です,
Reference Report,レファレンスレポート,
Reference: {0} {1},参照：{0} {1},
Refreshing...,再読込しています...,
Register OAuth Client App,OAuthクライアントアプリの登録,
Registered but disabled,登録されているが無効,
Relapsed,再発しました,
Relapses,再発,
Relink,再リンク,
Relink Communication,再リンク通信,
Relinked,再リンク,
Reload,リロード,
Remember Last Selected Value,最終選択された値を覚えておいてください,
Remote,リモート,
Remote Fieldname,リモートフィールド名,
Remote ID,リモートID,
Remote Objectname,リモートオブジェクト名,
Remote Primary Key,リモートプライマリキー,
Remove,削除,
Remove Field,フィールド削除,
Remove Filter,フィルタの削除,
Remove Section,セクションを削除,
Remove Tag,タグ削除,
Remove all customizations?,すべてのカスタマイズを削除しますか？,
Removed {0},削除済 {0},
Rename many items by uploading a .csv file.,csvファイルをアップロードすることで多くのアイテムの名前を変更します,
Rename {0},名称変更 {0},
Repeat Header and Footer in PDF,PDFにヘッダーとフッターを繰り返し,
Repeat On,繰り返し,
Repeat Till,繰り返し終了,
Repeat on Day,日に繰り返す,
Repeat this Event,このイベントを繰り返し,
"Repeats like ""aaa"" are easy to guess",「AAA」のような繰り返しは容易に推測されます,
"Repeats like ""abcabcabc"" are only slightly harder to guess than ""abc""",「ABCABCABC」などの繰り返しは「ABC」よりわずかに推測が困難です,
Reply,返信,
Reply All,全員に返信,
Report End Time,レポート終了時間,
Report Filters,レポートフィルタ,
Report Hide,レポート非表示,
Report Manager,レポートマネージャ,
Report Name,レポート名,
Report Start Time,レポートの開始時間,
Report cannot be set for Single types,レポートはシングルタイプに設定することはできません,
Report of all document shares,すべての共有文書のレポート,
Report updated successfully,レポートを更新しました,
Report was not saved (there were errors),レポートは保存されませんでした（エラーが発生しています）,
Report {0},レポート {0},
Report {0} is disabled,レポート{0}は無効です,
Report:,報告する：,
Represents a User in the system.,システム内のユーザーを表します。,
Represents the states allowed in one document and role assigned to change the state.,状態を変更するために割り当てられた文書と役割で許可状況を表す,
Request Timed Out,リクエストタイムアウト,
Request URL,リクエストURL,
Require Trusted Certificate,信頼できる証明書を要求する,
Res: {0},結果：{0},
Reset OTP Secret,OTPシークレットをリセットする,
Reset Password,パスワード再設定,
Reset Password Key,パスワードキーリセット,
Reset Permissions for {0}?,{0}のアクセス権限をリセットしますか？,
Reset to defaults,デフォルト値にリセット,
Reset your password,あなたのパスワードをリセット,
Response Type,応答タイプ,
Restore,復元,
Restore Original Permissions,元の権限に復元,
Restore or permanently delete a document.,文書を復元または完全に削除します。,
Restore to default settings?,デフォルト設定に復元しますか？,
Restored,復元済,
Restrict IP,IPアドレス制限,
Restrict To Domain,ドメインに制限,
Restrict user for specific document,特定のドキュメントのユーザーを制限する,
Restrict user from this IP address only. Multiple IP addresses can be added by separating with commas. Also accepts partial IP addresses like (111.111.111),このIPアドレスのみからアクセス可能とします。\n複数のIPアドレスは、カンマで区切ることにより追加することができます。\nまた、部分的なIPアドレスも指定可能です（例：111.111.111）,
Resume Sending,送信を再開,
Retake,やり直し,
Retry,リトライ,
Return to the Verification screen and enter the code displayed by your authentication app,認証画面に戻り、認証アプリで表示されるコードを入力します,
Reverse Icon Color,アイコン色を反転,
Revert,元に戻す,
Revert Of,元に戻す,
Reverted,元に戻す,
Review Level,レビューレベル,
Review Levels,レビューレベル,
Review Points,レビューポイント,
Reviews,レビュー,
Revoke,取消,
Revoked,失効,
Rich Text,リッチテキスト,
Robots.txt,Robots.txt,
Role Name,役割名,
Role Permission for Page and Report,ページ・レポート用の役割権限,
Role Permissions,役割の権限,
Role Profile,役割プロファイル,
Role and Level,役割とレベル,
Roles,役割,
Roles Assigned,割当された役割,
Roles can be set for users from their User page.,役割は、ユーザーページからユーザーが設定することができます。,
Root {0} cannot be deleted,ルート{0}は削除できません,
Round Robin,ラウンドロビン,
Route History,ルート履歴,
Route Redirects,ルートリダイレクト,
Route to Success Link,成功リンクへの経路,
Row,行,
Row #{0}:,行 {0}：,
Row Index,行インデックス,
Row No,行番号,
Row Status,行ステータス,
Row Values Changed,行の値を変更します,
Row {0}: Not allowed to disable Mandatory for standard fields,行{0}：無効にすることはできません標準フィールドには必須,
Row {0}: Not allowed to enable Allow on Submit for standard fields,行{0}：標準フィールドの「提出」を「許可」にすることが許可されていません,
Rows Added,行が追加されました,
Rows Removed,削除された行,
Rule,ルール,
Rule Name,ルール名,
Rules defining transition of state in the workflow.,ワークフローの状態遷移を定義するルール,
"Rules for how states are transitions, like next state and which role is allowed to change state etc.",状態遷移のルール（例：次の状態とその状態の変更を許可される役割など）,
Run,実行,
Run scheduled jobs only if checked,スケジュールされたジョブを実行（チェックを入れた場合のみ）,
S3 Backup Settings,S3バックアップ設定,
S3 Backup complete!,S3バックアップ完了！,
SMS,SMS,
SMS Gateway URL,SMSゲートウェイURL,
SMS Parameter,SMSパラメータ,
SMS Settings,SMS設定,
SMS sent to following numbers: {0},次の番号に送信されたSMS：{0},
SMTP Server,SMTPサーバー,
SMTP Settings for outgoing emails,送信メール用のSMTP設定,
"SQL Conditions. Example: status=""Open""","SQL条件。例：status=""Open""",
SSL/TLS Mode,SSL / TLSモード,
Salesforce,Salesforce,
Same Field is entered more than once,同じフィールドが複数回入力されています,
Save API Secret: ,APIシークレットを保存する：,
Save As,名前を付けて保存,
Save Filter,フィルタを保存する,
Save Report,レポートを保存,
Save filters,フィルタを保存,
Saving,保存,
Saving...,保存中...,
Scan the QR Code and enter the resulting code displayed.,QRコードをスキャンし、表示されるコードを入力します。,
Scopes,スコープス,
Script,スクリプト,
Script Report,スクリプトレポート,
Script or Query reports,スクリプトまたはクエリレポート,
Script to attach to all web pages.,全てのWebページに添付するためのスクリプト,
Search Fields,検索フィールド,
Search Help,検索ヘルプ,
Search field {0} is not valid,検索フィールド{0}は有効ではありません,
Search for '{0}','{0}'を検索,
Search for anything,何かを検索,
Search in a document type,文書タイプで検索,
Search or Create a New Chat,検索または新しいチャットを作成,
Search or type a command,検索またはコマンド入力,
Search...,検索...,
Searching,検索中,
Searching ...,検索中...,
Section Break,セクション区切り,
Section Heading,セクション見出し,
Security,セキュリティ,
Security Settings,セキュリティ設定,
See all past reports.,過去の報告をすべて見る。,
See on Website,ウェブサイトで閲覧,
See the document at {0},{0}の文書を参照してください。,
Seems API Key or API Secret is wrong !!!,APIキーまたはAPIシークレットが間違っているようです！,
Seems Publishable Key or Secret Key is wrong !!!,公開可能な鍵または秘密鍵が間違っているようです。,
"Seems issue with server's razorpay config. Don't worry, in case of failure amount will get refunded to your account.",サーバー上のrazorpay設定に問題があるようです。失敗した分はあなたのアカウントに返金されますのでご安心ください。,
Seems token you are using is invalid!,使用しているトークンが無効のようです！,
Seen,閲覧済,
Seen By,で見られる,
Seen By Table,表から見ました,
Select Attachments,添付ファイルを選択,
Select Child Table,子テーブルを選択,
Select Column,列選択,
Select Columns,列を選択,
Select Document Type,文書タイプを選択,
Select Document Type or Role to start.,開始する文書タイプまたは役割を選択してください,
Select Document Types to set which User Permissions are used to limit access.,アクセス制限する「ユーザー権限」にドキュメントタイプを選択して設定してください,
Select File Format,ファイル形式を選択,
Select File Type,ファイルタイプを選択,
Select Language...,言語を選択...,
Select Languages,言語を選択,
Select Module,モジュールを選択,
Select Print Format,印刷形式を選択,
Select Print Format to Edit,編集する印刷形式を選択,
Select Role,役割を選択,
Select Table Columns for {0},{0}のテーブル列を選択,
Select Your Region,あなたの地域を選択,
Select a Brand Image first.,最初にブランドイメージを選択します。,
Select a DocType to make a new format,新しいフォーマットを作るための文書タイプを選択,
Select a chat to start messaging.,メッセージングを開始するチャットを選択します。,
Select a group node first.,はじめにグループノードを選択してください,
Select an existing format to edit or start a new format.,編集または新しいフォーマットを開始する場合、既存のフォーマットを選択してください,
Select an image of approx width 150px with a transparent background for best results.,最良の結果を得るため、透過背景で幅150ピクセル程度の画像を選択してください,
Select atleast 1 record for printing,印刷用に少なくとも1つのレコードを選択,
Select or drag across time slots to create a new event.,新規イベントを作成するには時間スロットを選択するかドラッグしてください,
Select records for assignment,割当のためのレコードを選択,
Select the label after which you want to insert new field.,新たなフィールドを挿入後にラベルを選択してください。,
"Select your Country, Time Zone and Currency",国、時間帯、通貨を選択,
Select {0},{0}を選択,
Self approval is not allowed,自己承認は許可されていません,
Send After,後送信,
Send Alert On,アラート送信をON,
Send Email Alert,電子メールアラートを送信する,
Send Email Print Attachments as PDF (Recommended),書類をPDFでメールに添付して送信（推奨）,
Send Email for Successful Backup,正常なバックアップのためにEメールを送信する,
Send Me A Copy of Outgoing Emails,送信メールのコピーを私に送ってください,
Send Notification to,に通知を送信,
Send Notifications To,通知送信先,
Send Print as PDF,PDFで書類を送信,
Send Read Receipt,開封確認を送信,
Send Unsubscribe Link,解除リンクを送信,
Send Welcome Email,ウェルカムメールを送信,
Send alert if date matches this field's value,日付がこのフィールドの値と一致した場合にアラートを送信,
Send alert if this field's value changes,フィールドの値が変更された場合アラートを送信,
Send an email reminder in the morning,午前中にリマインダーメールを送信,
Send days before or after the reference date,基準日の前または後に送信,
Send enquiries to this email address,このメールアドレスに問い合わせを送信,
Send me a copy,自分にコピーを送付,
Send only if there is any data,任意のデータがある場合にのみ送ります,
Send unsubscribe message in email,電子メールでの登録解除メッセージを送ります,
Sender,送信者,
Sender Email,送信者の電子メール,
Sendgrid,Sendgrid,
Sent Read Receipt,送信された開封確認,
Sent or Received,送受信済み,
Sent/Received Email,送信/受信メール,
Server IP,サーバーIP,
Session Expired,セッションの有効期限が切れ,
Session Expiry,セッションの有効期限,
Session Expiry Mobile,セッション有効期限（モバイル）,
Session Expiry in Hours e.g. 06:00,セッション有効期限切れ時刻（例：06:00）,
Session Expiry must be in format {0},セッションの有効期限は {0}のようなフォーマットでなければなりません,
Session Start Failed,セッションの開始に失敗しました,
Set Banner from Image,画像からバナーを設定,
Set Chart,チャートを設定する,
Set Filters,フィルタを設定する,
Set New Password,新しいパスワードを設定,
Set Number of Backups,バックアップのセット番号,
Set Only Once,1度だけ設定,
Set Password,パスワードを設定,
Set Permissions,アクセス権の設定,
Set Permissions on Document Types and Roles,書類タイプと役割にアクセス許可を設定,
Set Property After Alert,アラート後のプロパティの設定,
Set Quantity,数量を設定,
Set Role For,役割を設定,
Set User Permissions,ユーザーのアクセス権を設定,
Set Value,値を設定,
Set custom roles for page and report,ページ・レポート用のカスタム役割を設定,
"Set default format, page size, print style etc.",デフォルト書式、ページサイズ、印刷スタイル等を設定,
Set non-standard precision for a Float or Currency field,浮動小数や通貨フィールドの規格外精度を設定,
Set numbering series for transactions.,取引用の連番を設定,
Set up rules for user assignments.,ユーザー割り当てのルールを設定します。,
Setting this Address Template as default as there is no other default,他にデフォルトがないので、このアドレステンプレートをデフォルトとして設定します,
Setting up your system,システム設定,
Settings for About Us Page.,会社概要ページの設定,
Settings for Contact Us Page,お問い合わせページ設定,
Settings for Contact Us Page.,お問い合わせページの設定,
Settings for OAuth Provider,OAuthプロバイダー設定,
Settings for the About Us Page,会社概要ページ設定,
Setup Auto Email,自動メールを設定,
Setup Complete,セットアップ完了,
Setup Notifications based on various criteria.,さまざまな基準に基づいて通知を設定します。,
Setup Reports to be emailed at regular intervals,定期的に電子メールで送信するセットアップレポート,
"Setup of top navigation bar, footer and logo.",上部のナビゲーションバー・フッター・ロゴ設定,
Share,シェア,
Share URL,共有URL,
Share With,共有,
Share this document with,この文書を共有,
Share {0} with,{0}と共有,
Shared,共有済,
Shared With,共有済,
Shared with everyone,全員に共有,
Shared with {0},{0}と共有,
Shop,店,
Short keyboard patterns are easy to guess,ショートキーボードパターンが推測するのは簡単です,
Show Attachments,添付ファイルを表示,
Show Calendar,カレンダー表示,
Show Dashboard,ダッシュボードを表示,
Show Full Error and Allow Reporting of Issues to the Developer,完全なエラーを表示し、開発者に問題の報告を許可する,
Show Line Breaks after Sections,セクションの後に表示する改行,
Show Permissions,アクセス許可を表示,
Show Preview Popup,プレビューを表示するポップアップ,
Show Relapses,再発を表示,
Show Report,レポートを表示する,
Show Section Headings,セクション見出しを表示,
Show Sidebar,サイドバー表示,
Show Title,タイトルを表示,
Show Totals,合計を表示,
Show Weekends,週末を表示する,
Show all Versions,全バージョン表示,
Show as Grid,グリッドとして表示,
Show as cc,CCとして表示,
Show failed jobs,失敗したジョブ表示,
Show in Module Section,モジュールセクションに表示,
Show in filter,フィルタに表示する,
Show more details,詳細を表示,
Show only errors,エラーのみ表示,
"Show title in browser window as ""Prefix - title""",「接頭辞 - タイトル」のようにブラウザでタイトルを表示,
Showing only Numeric fields from Report,レポートから数値フィールドのみを表示する,
Sidebar Items,サイドバーのアイテム,
Sidebar Settings,サイドバー設定,
Sidebar and Comments,サイドバーとコメント,
Sign Up,サインアップ,
Sign Up is disabled,サインアップが無効になっています,
Signature,署名,
"Simple Python Expression, Example: Status in (""Closed"", ""Cancelled"")",単純なPython式、例：（ &quot;Closed&quot;、 &quot;Canceled&quot;）のステータス,
"Simple Python Expression, Example: status == 'Open' and type == 'Bug'",単純なPython表現、例：status == &#39;Open&#39;、type == &#39;Bug&#39;,
Simultaneous Sessions,同時セッション,
Single DocTypes cannot be customized.,単一のDocTypeはカスタマイズできません。,
Single Post (article).,シングルポスト（記事）,
Single Types have only one record no tables associated. Values are stored in tabSingles,シングルタイプにテーブルが関連付けられていない唯一のレコードがあります。値はtabSinglesに格納されています,
Skip Authorization,認証をスキップ,
Skip rows with errors,エラーのある行をスキップする,
Skype,Skype,
Slack,スラック,
Slack Channel,スラックチャンネル,
Slack Webhook Error,スラックWebhookエラー,
Slack Webhook URL,Webhook URLをゆるめる,
Slack Webhooks for internal integration,内部統合のための不十分なWebhooks,
Slideshow Items,スライドショーアイテム,
Slideshow Name,スライドショー名,
Slideshow like display for the website,スライドショー（例：ウェブサイト表示）,
Small Text,小さいテキスト,
Smallest Currency Fraction Value,最小通貨分数値,
Smallest circulating fraction unit (coin). For e.g. 1 cent for USD and it should be entered as 0.01,最小の小数単位（コイン）。例えば米ドルの場合、1セントを0.01として入力する必要があります,
Snapshot View,スナップショットの表示,
Social,ソーシャル,
Social Login Key,ソーシャルログインキー,
Social Login Provider,ソーシャルログインプロバイダ,
Social Logins,ソーシャルログイン,
Socketio is not connected. Cannot upload,Socketioが接続されていません。アップロードできません,
Soft-Bounced,送信失敗,
Some of the features might not work in your browser. Please update your browser to the latest version.,一部の機能がブラウザで動作しない場合があります。ブラウザを最新バージョンに更新してください。,
Something went wrong,何らかの問題が発生しました,
Something went wrong while generating dropbox access token. Please check error log for more details.,Dropboxアクセストークンを生成中に問題が発生しました。詳細についてはエラーログを確認してください。,
Sorry! I could not find what you were looking for.,探しものは見つかりませんでした。,
Sorry! Sharing with Website User is prohibited.,「ウェブサイトユーザー」と共有する権限がありません。,
Sorry! User should have complete access to their own record.,ユーザーは、自分のレコードへの完全なアクセス権限を持っている必要があります,
Sorry! You are not permitted to view this page.,このページを表示する権限がありません。,
"Sorry, you're not authorized.",申し訳ありませんが、あなたは承認されていません。,
Sort Field,並べ替えフィールド,
Sort Order,並び順,
Sort field {0} must be a valid fieldname,ソートフィールド{0}は、有効なフィールド名でなければなりません,
Source Text,ソーステキスト,
Spam,スパム,
SparkPost,SparkPost,
Special Characters are not allowed,特殊文字は使用できません,
"Standard DocType cannot have default print format, use Customize Form",標準の文書タイプは、デフォルトの印刷フォーマットを持つことはできません。カスタマイズフォームを使用してください,
Standard Print Format cannot be updated,標準の印刷フォーマットを更新することはできません,
Standard Print Style cannot be changed. Please duplicate to edit.,標準印刷スタイルは変更できません。編集のために複製してください。,
Standard Reports,標準レポート,
Standard Sidebar Menu,標準のサイドバーのメニュー,
Standard roles cannot be disabled,標準の役割は無効にすることはできません,
Standard roles cannot be renamed,標準の役割は名前を変更することができません,
Standings,順位表,
Start Date Field,開始日フィールド,
Start a conversation.,会話を開始する。,
Start entering data below this line,このラインより下からデータ入力を開始してください,
Start new Format,新しいフォーマットを開始,
StartTLS,StartTLS,
Started,始めた,
Starting Frappe ...,Frappéを開始中...,
Starts on,開始,
States,状態,
"States for workflow (e.g. Draft, Approved, Cancelled).",ワークフローの状態（例：ドラフト・承認・キャンセル）,
Static Parameters,静的パラメータ,
Stats based on last month's performance (from {0} to {1}),先月のパフォーマンスに基づく統計情報（{0}から{1}まで）,
Stats based on last week's performance (from {0} to {1}),先週のパフォーマンスに基づく統計（{0}から{1}まで）,
Status: {0},ステータス：{0},
Steps to verify your login,ログインの確認手順,
Stores the JSON of last known versions of various installed apps. It is used to show release notes.,インストール済アプリケーションの最新バージョンのJSONを保管します。リリースノートを表示するために使用されます。,
Straight rows of keys are easy to guess,キーのストレート行は推測するのは簡単です,
Stripe Settings,ストライプ設定,
Stripe payment gateway settings,ストライプ支払いゲートウェイの設定,
Style,スタイル,
Style Settings,スタイル設定,
"Style represents the button color: Success - Green, Danger - Red, Inverse - Black, Primary - Dark Blue, Info - Light Blue, Warning - Orange",スタイルはボタンの色を表しています。成功 - グリーン、危険 - 赤、逆 - ブラック、主 - ダークブルー、情報 - ライトブルー、警告 - オレンジ,
Stylesheets for Print Formats,印刷書式のスタイルシート,
"Sub-currency. For e.g. ""Cent""",サブ通貨（例：セント）,
Sub-domain provided by erpnext.com,erpnext.comが提供するサブドメイン,
Subdomain,サブドメイン,
Subject Field,件名フィールド,
Submit after importing,インポート後に提出する,
Submit an Issue,課題を投稿,
Submit this document to confirm,確認のため、この文書を提出,
Submit {0} documents?,{0}文書を提出しますか？,
Submitting {0},{0}を送信中です,
Submitted Document cannot be converted back to draft. Transition row {0},提出されたドキュメントは、ドラフトに変換しなおすことができません。遷移行{0},
Submitting,提出,
Subscription Notification,購読通知,
Subsidiary,子会社,
Success Action,成功行動,
Success Message,成功メッセージ,
Success URL,成功URL,
Successful: {0} to {1},成功：{0} {1}へ,
Successfully Done,成功しました,
Successfully Updated,正常に更新されました,
Successfully updated translations,翻訳を正常に更新しました,
Suggested Username: {0},推奨ユーザ名：{0},
Sum,和,
Sum of {0},{0}の合計,
Support Email Address Not Specified,指定されていないサポートEメールアドレス,
Suspend Sending,送信サスペンド,
Switch To Desk,デスクに切り替え,
Symbol,シンボル,
Sync,同期,
Sync on Migrate,移行上の同期,
Syntax error in template,テンプレートの構文エラー,
System,システム,
System Page,システムページ,
System Settings,システム設定,
System User,システムユーザー,
System and Website Users,システムとウェブサイトユーザー,
Table,表,
Table Field,表フィールド,
Table HTML,表HTML,
Table MultiSelect,テーブル複数選択,
Table updated,表を更新しました,
Table {0} cannot be empty,表{0}は空にできません。,
Take Backup Now,今すぐバックアップ,
Take Photo,写真を撮る,
Team Members,チームメンバー,
Team Members Heading,チームメンバーの方針,
Temporarily Disabled,一時的に無効,
Test Email Address,テスト電子メールアドレス,
Test Runner,テストランナー,
Test_Folder,Test_Folder,
Text,文章,
Text Align,行揃え,
Text Color,テキストの色,
Text Content,テキストコンテンツ,
Text Editor,テキストエディタ,
Text to be displayed for Link to Web Page if this form has a web page. Link route will be automatically generated based on `page_name` and `parent_website_route`,このフォームがウェブページを持っている場合、テキストは、ウェブページへのリンクとして表示されます。リンク先は `page_name`と` parent_website_route`に基づいて自動的に生成されます,
Thank you for your email,メールいただきありがとうございます,
Thank you for your interest in subscribing to our updates,アップデートに関心をお寄せいただきありがとうございます,
Thank you for your message,メッセージありがとうございました。,
The CSV format is case sensitive,CSV形式は大文字と小文字を区別します,
The Condition '{0}' is invalid,条件 '{0}' は無効です,
The First User: You,最初のユーザー：あなたです。,
"The application has been updated to a new version, please refresh this page",アプリケーションが新しいバージョンに更新されました。このページを更新してください,
The attachments could not be correctly linked to the new document,添付ファイルを新しい文書に正しくリンクできませんでした,
The document could not be correctly assigned,文書を正しく割り当てることができませんでした,
The document has been assigned to {0},ドキュメントは{0}に割り当てられています,
The first user will become the System Manager (you can change this later).,最初のユーザーは、システムマネージャとなります。（後で変更できます）,
The name that will appear in Google Calendar,Googleカレンダーに表示される名前,
The process for deletion of {0} data associated with {1} has been initiated.,{1}に関連付けられている{0}データの削除プロセスが開始されました。,
The resource you are looking for is not available,お探しのリソースは利用できません,
The system provides many pre-defined roles. You can add new roles to set finer permissions.,このシステムは、多くの事前定義された役割を提供しています。細かい権限を設定するための新しい役割を追加することができます。,
The user from this field will be rewarded points,このフィールドのユーザーにはポイントが与えられます,
Theme,テーマ,
Theme URL,テーマURL,
There can be only one Fold in a form,フォールドはフォーム内に1つしかない場合もあります,
There is an error in your Address Template {0},あなたのアドレステンプレート{0}にエラーがあります,
There is no data to be exported,エクスポートするデータがありません,
There is some problem with the file url: {0},ファイルURLに問題があります：{0},
There must be atleast one permission rule.,少なくとも1つの許可ルールが存在する必要があります。,
"There seems to be an issue with the server's braintree configuration. Don't worry, in case of failure, the amount will get refunded to your account.",サーバーのbraintree構成に問題があるようです。ご不便をおかけしても、ご返金いただけない場合は、お客様のアカウントに払い戻されます。,
There should remain at least one System Manager,少なくとも1つのシステムマネージャーを残さねばなりません,
There was an error saving filters,フィルタの保存中にエラーが発生しました,
There were errors,エラーが発生しました,
There were errors while creating the document. Please try again.,ドキュメントの作成中にエラーが発生しました。もう一度お試しください。,
There were errors while sending email. Please try again.,メールの送信中にエラーが発生しました。もう一度お試しください。,
"There were some errors setting the name, please contact the administrator",名前の設定でいくつかエラーが発生しました。管理者に連絡してください。,
These values will be automatically updated in transactions and also will be useful to restrict permissions for this user on transactions containing these values.,これらの値は自動的に処理の段階で更新され、また取引にこの値が含まれているユーザーの権限を制限するのに役立つでしょう。,
Third Party Apps,サードパーティのアプリ,
Third Party Authentication,第三者認証,
This Currency is disabled. Enable to use in transactions,この通貨は無効になっています。取引内で使用可能にすることが出来ます,
This Kanban Board will be private,このかんばんボードは、プライベートになります,
This document cannot be reverted,この文書を元に戻すことはできません,
This document has been modified after the email was sent.,この文書は、電子メールの送信後に変更されています。,
This document has been reverted,この文書は元に戻されました,
This document is currently queued for execution. Please try again,この文書では、現在の実行のためにキューイングされます。もう一度やり直してください,
This email is autogenerated,このメールは自動的に生成されています,
This email was sent to {0},{0}にメール送信されました,
This email was sent to {0} and copied to {1},このメールは{0}に送信され、{1}にコピーされました,
This feature is brand new and still experimental,この機能は、ブランドの新しい、まだ実験的なものです,
This field will appear only if the fieldname defined here has value OR the rules are true (examples):\nmyfield\neval:doc.myfield=='My Value'\neval:doc.age&gt;18,ここで定義されたフィールド名が値を持っているかのルールが真（例）である場合にのみ、このフィールドが表示されます：myFieldでのeval：doc.myfield == &#39;マイ値&#39;のeval：doc.age&gt; 18,
This form does not have any input,このフォームは入力されていません,
This form has been modified after you have loaded it,このフォームは読み込み後に変更されています,
This format is used if country specific format is not found,国別の書式が無い場合は、この書式が使用されます,
This goes above the slideshow.,スライドショーの上に配置されます,
This is a background report. Please set the appropriate filters and then generate a new one.,これはバックグラウンドレポートです。適切なフィルタを設定してから、新しいフィルタを生成してください。,
This is a top-10 common password.,これはトップ10内の一般的なパスワードです。,
This is a top-100 common password.,これはトップ100内の一般的なパスワードです。,
This is a very common password.,これは非常に一般的なパスワードです。,
This is an automatically generated reply,これは自動的に生成された返答です,
This is similar to a commonly used password.,これは、一般的に使用されるパスワードに似ています。,
This is the template file generated with only the rows having some error. You should use this file for correction and import.,これは、何らかのエラーを持つ行だけで生成されたテンプレートファイルです。このファイルを修正およびインポートに使用する必要があります。,
This link has already been activated for verification.,このリンクは確認のために既に有効化されています。,
This link is invalid or expired. Please make sure you have pasted correctly.,このリンクは無効または期限切れです。正しく貼り付けられていることを確認してください,
This may get printed on multiple pages,これは複数のページに印刷されるかもしれません,
This month,今月,
This query style is discontinued,このクエリスタイルは廃止されました,
This report was generated on {0},このレポートは{0},
This report was generated {0}.,このレポートは生成されました{0}。,
This request has not yet been approved by the user.,このリクエストはまだユーザーによって承認されていません。,
This role update User Permissions for a user,この「役割」はユーザーの「ユーザー権限」を更新します,
This will log out {0} from all other devices,これは他のすべてのデバイスから{0}をログアウトします,
This will permanently remove your data.,これはあなたのデータを永久に削除します。,
Throttled,抑えられた,
Thumbnail URL,サムネイルのURL,
Time Interval,時間間隔,
Time Series,時系列,
Time Series Based On,に基づく時系列,
Time Zone,時間帯,
Time Zones,時間帯,
Time in seconds to retain QR code image on server. Min:<strong>240</strong>,サーバー上にQRコードイメージを保持する時間（秒単位）。最小： <strong>240</strong>,
Timeline DocType,タイムラインDOCTYPE,
Timeline Field,タイムラインフィールド,
Timeline Links,タイムラインリンク,
Timeline Name,タイムライン名,
Timeline field must be a Link or Dynamic Link,タイムラインのフィールドは、リンクまたはダイナミックリンクでなければなりません,
Timeline field must be a valid fieldname,タイムラインのフィールドは、有効なフィールド名でなければなりません,
Timeseries,時系列,
Timestamp,タイムスタンプ,
Title Case,タイトルの大文字指定,
Title Field,タイトルフィールド,
Title Prefix,タイトルの接頭辞,
Title field must be a valid fieldname,タイトルフィールドは、有効なフィールド名である必要があります。,
To Date Field,日付フィールドへ,
To Do,やることリスト,
To User,ユーザーへ,
"To add dynamic subject, use jinja tags like\n\n<div><pre><code>New {{ doc.doctype }} #{{ doc.name }}</code></pre></div>",動的な件名を追加するには、jinjaタグを使用します。 <div><pre> <code>New {{ doc.doctype }} #{{ doc.name }}</code> </pre> </div>,
"To add dynamic subject, use jinja tags like\n\n<div><pre><code>{{ doc.name }} Delivered</code></pre></div>",動的な件名を追加するには、のような神社のタグを使用します<div><pre> <code>{{ doc.name }} Delivered</code> </pre> </div>,
To and CC,To・CC,
"To get the updated report, click on {0}.",更新されたレポートを入手するには、{0}をクリックしてください。,
ToDo,やることリスト,
Today,今日,
Toggle Chart,トグルチャート,
Toggle Charts,チャートの切り替え,
Toggle Grid View,グリッド表示の切り替え,
Toggle Sidebar,サイドバーの切り替え,
Token,トークン,
Token is missing,トークンがありません,
"Too many users signed up recently, so the registration is disabled. Please try back in an hour",ユーザー登録が集中したため、登録は無効になっています。しばらく後で再度実行してください,
Too many writes in one request. Please send smaller requests,1リクエスト内に記入が多すぎます。リクエストを小さくして送信してください,
Top Bar Item,トップバーアイテム,
Top Bar Items,トップバーアイテム,
Top Performer,トップパフォーマー,
Top Reviewer,トップレビューア,
Top {0},トップ{0},
Total Pages,合計ページ数,
Total Rows,合計行,
Total Subscribers,総登録者数,
Total number of emails to sync in initial sync process ,初期同期プロセスに同期するメールの総数,
Totals Row,合計行,
Track Changes,変更履歴の記録,
Track Email Status,電子メールステータスを追跡する,
Track Field,トラックフィールド,
Track Seen,トラックは見ました,
Track Views,トラックビュー,
"Track if your email has been opened by the recipient.\n<br>\nNote: If you're sending to multiple recipients, even if 1 recipient reads the email, it'll be considered ""Opened""",受信者が電子メールを開いたかどうかを追跡します。 <br>注：複数の受信者に送信する場合、1人の受信者が電子メールを読み取ったとしても、そのメールは「開封済み」とみなされます。,
Track milestones for any document,あらゆる文書のマイルストーンを追跡する,
Transaction Hash,トランザクションハッシュ,
Transaction Log,トランザクションログ,
Transaction Log Report,トランザクションログレポート,
Transition Rules,移行ルール,
Transitions,移行,
Translatable,翻訳可能,
Translate {0},{0}を翻訳する,
Translated Text,訳文,
Translation,翻訳,
Translations,翻訳,
Trash,ゴミ,
Tree,ツリー,
Trigger Method,トリガー・メソッド,
Trigger Name,トリガー名,
"Trigger on valid methods like ""before_insert"", ""after_update"", etc (will depend on the DocType selected)","""before_insert"" ""after_update"" など（選択した文書タイプによって異なります）の有効なメソッドを持つトリガー",
Try to avoid repeated words and characters,繰り返し単語や文字を避けるようにしてください,
Try to use a longer keyboard pattern with more turns,巻数で長いキーボード・パターンを使用してみてください,
Two Factor Authentication,２要素認証,
Two Factor Authentication method,２要素認証方式,
Type something in the search box to search,検索する検索ボックスに何かを入力,
Type:,タイプ：,
UID,UID,
UIDNEXT,UIDNEXT,
UIDVALIDITY,UIDVALIDITY,
UNSEEN,未読,
UPPER CASE,大文字,
"URIs for receiving authorization code once the user allows access, as well as failure responses. Typically a REST endpoint exposed by the Client App.\n<br>e.g. http://hostname//api/method/frappe.www.login.login_via_facebook",ユーザー一度認証コードを受信するためのURIは、アクセスを可能にするだけでなく、失敗応答。典型的には、RESTエンドポイントは、クライアントアプリケーションによって公開されました。 <br>例えばのhttp：//hostname//api/method/frappe.www.login.login_via_facebook,
URLs,URL,
Unable to find attachment {0},添付ファイル{0}が見つかりません,
Unable to load camera.,カメラをロードできません。,
Unable to load: {0},{0}がロードできません,
Unable to open attached file. Did you export it as CSV?,添付ファイルを開くことができません。あなたはそれをCSVとしてエクスポートしましたか？,
Unable to read file format for {0},{0}のファイル形式を読み取ることができません,
Unable to send emails at this time,メールを送信することができません。,
Unable to update event,イベントを更新することができません,
Unable to write file format for {0},{0}のファイル形式を書き込めません,
Unassign Condition,割り当て解除条件,
Under Development,開発中で,
Unfollow,フォローを解除,
Unhandled Email,未処理の電子メール,
Unique,ユニーク,
Unknown Column: {0},不明なコラム：{0},
Unknown User,未知のユーザ,
"Unknown file encoding. Tried utf-8, windows-1250, windows-1252.",未知の文字コードです。utf-8・Windows-1250・Windows-1252ではありませんでした。,
Unread,未読にする,
Unread Notification Sent,未読通知送信済,
Unselect All,すべての選択を解除,
Unshared,共有解除済,
Unsubscribe,登録解除,
Unsubscribe Method,配信停止方法,
Unsubscribe Param,配信停止パラメータ,
Unsupported File Format,サポートされていないファイル形式,
Unzip,解凍,
Unzipped {0} files,解凍された{0}ファイル,
Unzipping files...,ファイルを解凍しています...,
Upcoming Events for Today,今日の予定,
Update Field,フィールド更新,
Update Translations,翻訳を更新する,
Update Value,値更新,
Update many values at one time.,一度に多くの値を更新します。,
Update records,レコードを更新,
Updated,更新済,
Updated successfully,更新成功,
Updated {0}: {1},更新済{0}：{1},
Updating,更新,
Updating {0},{0}を更新しています,
Upload Failed,アップロードに失敗しました,
Uploaded To Dropbox,Dropboxにアップロード,
Use ASCII encoding for password,パスワードにASCIIエンコーディングを使用する,
Use Different Email Login ID,別のメールログインIDを使用する,
Use IMAP,IMAPを使用,
Use POST,POSTを使用する,
Use SSL,SSLを使用,
Use TLS,TLSを使用,
"Use a few words, avoid common phrases.",一般的なフレーズを避けるため、いくつかの単語を組み合わせてください。,
Use of sub-query or function is restricted,サブクエリまたは機能の使用は制限されています,
Use socketio to upload file,ファイルをアップロードするにはsocketioを使用してください,
Use this fieldname to generate title,タイトルを生成するには、このフィールド名を使用します,
User '{0}' already has the role '{1}',ユーザー '{0}'は既に役割 '{1}' を持っています,
User Cannot Create,ユーザーが作成できません。,
User Cannot Search,ユーザーが検索できません,
User Defaults,ユーザーデフォルト,
User Email,ユーザーのメール,
User Emails,ユーザーのメール,
User Field,ユーザーフィールド,
User ID of a Blogger,BloggerのユーザーID,
User Image,ユーザー画像,
User Name,ユーザー名,
User Permission,ユーザーのアクセス許可,
User Permissions,ユーザーのアクセス許可,
User Permissions are used to limit users to specific records.,ユーザー権限は、ユーザーを特定のレコードに制限するために使用されます。,
User Permissions created sucessfully,正常に作成されたユーザー権限,
User Roles,ユーザーの役割,
User Social Login,ユーザーソーシャルログイン,
User Tags,ユーザータグ,
User Type,ユーザー タイプ,
User can login using Email id or Mobile number,ユーザーは、電子メールIDまたは携帯電話番号を使用してログインできます,
User can login using Email id or User Name,ユーザーは電子メールIDまたはユーザー名を使用してログインできます,
User editable form on Website.,ウェブサイト上のユーザー編集可能なフォーム,
User is mandatory for Share,共有にはユーザーが必須です,
User not allowed to delete {0}: {1},ユーザーは{0}を削除できません：{1},
User permission already exists,ユーザー権限は既に存在します,
User permissions should not apply for this Link,このリンクにユーザー権限を反映すべきではありません,
User {0} cannot be deleted,ユーザー{0}を削除することはできません,
User {0} cannot be disabled,ユーザー{0}無効にすることはできません,
User {0} cannot be renamed,ユーザー{0}の名前を変更できません,
User {0} does not have access to this document,ユーザー{0}はこの文書にアクセスできません,
User {0} does not have doctype access via role permission for document {1},ユーザー{0}には、文書{1}のロール許可によるDoctypeアクセス権がありません,
Username,ユーザー名,
Username {0} already exists,ユーザー名{0}はすでに存在しています,
Users with role {0}:,役割{0}を持つユーザー：,
Uses the Email Address Name mentioned in this Account as the Sender's Name for all emails sent using this Account.,このアカウントを使用して送信されるすべての電子メールの送信者の名前として、このアカウントに記載されている電子メールアドレス名を使用します。,
Uses the Email Address mentioned in this Account as the Sender for all emails sent using this Account. ,このアカウントを使用して送信された全てのメールの送信者としてこのアカウントに記載されたメールアドレスを使用します。,
Valid,有効,
Valid Login id required.,有効なログインIDが必要です。,
Valid email and name required,有効なメールアドレスと名前が必要です。,
Value Based On,に基づく値,
Value Change,値変更,
Value Changed,値を変更済み,
Value To Be Set,設定する値,
Value cannot be changed for {0},{0}の値は変更することはできません。,
Value for a check field can be either 0 or 1,チェックフィールドの値は0か1のどちらかです,
Value for {0} cannot be a list,{0}の値は、リストにすることはできません,
Value missing for,値の記入漏れ,
Value too big,大きすぎる値,
Values Changed,変更された値,
Verdana,Verdana,
Verfication Code,検証コード,
Verification Link,確認リンク,
Verification code has been sent to your registered email address.,確認コードが登録されたメールアドレスに送信されました。,
Verify,検証,
Verify Password,パスワード照合,
Verifying...,確認しています...,
Version,バージョン,
Version Updated,更新されたバージョン,
View All,すべて見る,
View Comment,コメントを見る,
View List,リスト表示,
View Log,ビュー・ログ,
View Permitted Documents,許可された文書を表示,
View Properties (via Customize Form),ビュー属性（カスタマイズフォーム経由）,
View Settings,設定表示,
View Website,ウェブサイトを見る,
View document,ドキュメントを表示する,
View report in your browser,ブラウザでレポートを表示,
View this in your browser,ブラウザで表示,
View {0},ビュー{0},
Viewed By,閲覧者,
Visit,来訪,
Visitor,ビジター,
We have received a request for deletion of {0} data associated with: {1},{1}に関連付けられている{0}データの削除要求を受け取りました,
We have received a request from you to download your {0} data associated with: {1},{1}に関連付けられている{0}データをダウンロードするようにという要求がありました。,
Web Form,ウェブフォーム,
Web Form Field,Webフォームフィールド,
Web Form Fields,Webフォームフィールド,
Web Page,ウェブページ,
Web Page Link Text,Webページのリンクテキスト,
Web Site,ウェブサイト,
Web View,Web表示,
Webhook,Webhook,
Webhook Data,Webhookデータ,
Webhook Header,Webhookヘッダー,
Webhook Headers,Webhookヘッダー,
Webhook Request,Webhookリクエスト,
Webhook URL,Webhook URL,
Webhooks calling API requests into web apps,ウェブアプリケーションのAPIリクエストを実行するWebhooks,
Website Meta Tag,ウェブサイトメタタグ,
Website Route Meta,ウェブサイトルートメタ,
Website Route Redirect,ウェブサイトルートリダイレクト,
Website Script,ウェブサイトのスクリプト,
Website Sidebar,ウェブサイトのサイドバー,
Website Sidebar Item,ウェブサイトのサイドバー項目,
Website Slideshow,ウェブサイトのスライドショー,
Website Slideshow Item,ウェブサイトのスライドショーアイテム,
Website Theme,ウェブサイトのテーマ,
Website Theme Image,ウェブサイトのテーマ画像,
Website Theme Image Link,ウェブサイトのテーマ画像リンク,
Website User,ウェブサイトのユーザー,
Welcome Message,ウェルカムメッセージ,
"When you Amend a document after Cancel and save it, it will get a new number that is a version of the old number.",キャンセル後に書類を修正・保存した場合、その書類には新規の番号が付与されます。,
Width,幅,
Widths can be set in px or %.,幅はPXまたは％で設定できます。,
Will be used in url (usually first name).,URLで使用されます。（通常名前で表記されます）,
Will be your login ID,あなたのログインIDになります,
Will only be shown if section headings are enabled,セクションの見出しが有効になっている場合にのみ表示されます,
With Letter head,レターヘッド付,
With Letterhead,レターヘッド付き,
Workflow Action,ワークフローアクション,
Workflow Action Master,ワークフローアクションマスター,
Workflow Action Name,ワークフローアクション名,
Workflow Document State,ワークフロー文書のステータス,
Workflow Name,ワークフロー名,
Workflow State,ワークフローの状態,
Workflow State Field,ワークフローステータスのフィールド,
Workflow State not set,ワークフロー状態が設定されていません,
Workflow Transition,ワークフロー遷移,
Workflow state represents the current state of a document.,ワークフローの状態は、書類の現在の状態を表します,
Write,書き込む,
Wrong fieldname <b>{0}</b> in add_fetch configuration of custom script,カスタムスクリプトのadd_fetch設定でフィールド名<b>{0}</b>が間違っています,
X Axis Field,X軸フィールド,
XLSX,XLSX,
Y Axis Fields,Y軸のフィールド,
Yahoo Mail,Yahooメール,
Yandex.Mail,Yandex.Mail,
Yesterday,昨日,
You are connected to internet.,あなたはインターネットに接続しています。,
You are not allowed to create columns,列を作成することは許可されていません,
You are not allowed to delete a standard Website Theme,標準のWebサイトテーマを削除することは許可されていません,
You are not allowed to print this document,この文書を印刷することが許可されていません。,
You are not allowed to print this report,このレポートを印刷することは許可されていません,
You are not allowed to send emails related to this document,この書類に関連するメールを送信することが許可されていません。,
You are not allowed to update this Web Form Document,このWebフォーム文書を更新することはできません,
You are not connected to Internet. Retry after sometime.,インターネットに接続されていません。しばらく後で再試行してください。,
You are not permitted to access this page.,このページへのアクセスを許可されていません。,
You are not permitted to view the newsletter.,ニュースレターを閲覧することはできません。,
You are now following this document. You will receive daily updates via email. You can change this in User Settings.,あなたは現在この文書をフォローしています。あなたは電子メールを介して毎日の更新を受け取ります。これはユーザー設定で変更できます。,
You can add dynamic properties from the document by using Jinja templating.,Jinjaテンプレートを使用して文書から動的プロパティを追加することができます,
You can also copy-paste this ,これをコピー＆ペーストすることもできます,
"You can change Submitted documents by cancelling them and then, amending them.",提出した書類は、キャンセルし修正することによって変更することができます,
You can find things by asking 'find orange in customers',「顧客の中からオレンジ色を検索」と質問することで検索することができます,
You can only upload upto 5000 records in one go. (may be less in some cases),1回につき5000件までアップロードすることができます。,
You can use Customize Form to set levels on fields.,カスタマイズフォームを使用してフィールドの制限を設定できます,
You can use wildcard %,ワイルドカード（％）を使用することができます,
You can't set 'Options' for field {0},フィールド{0}に「オプション」を設定することはできません,
You can't set 'Translatable' for field {0},フィールド{0}に「翻訳可能」を設定することはできません,
You cannot give review points to yourself,あなたは自分自身にレビューポイントを与えることはできません,
You cannot unset 'Read Only' for field {0},フィールド {0} の「読み取り専用」設定を解除することはできません,
You do not have enough permissions to access this resource. Please contact your manager to get access.,このリソースにアクセスするための権限がありません。権限を得るためには管理者に連絡してください。,
You do not have enough permissions to complete the action,アクションを完了するための権限がありません,
You do not have enough points,あなたは十分なポイントがありません,
You do not have enough review points,レビューポイントが足りません,
You don't have access to Report: {0},レポートへのアクセス権がありません：{0},
You don't have any messages yet.,まだメッセージはありません。,
You don't have permission to access this file,このファイルにアクセスする権限がありません,
You don't have permission to get a report on: {0},あなたは次のレポートを取得する権限がありません：{0},
You don't have the permissions to access this document,この文書にアクセスする権限がありません,
You gained {0} point,{0}ポイントを獲得しました,
You gained {0} points,あなたは{0}ポイントを獲得しました,
You have a new message from: ,あなたからの新しいメッセージがあります：,
You have been successfully logged out,ログアウトが正常に完了しました,
You have unsaved changes in this form. Please save before you continue.,この書式に保存されていない変更事項があります。継続する前に、保存してください。,
You must login to submit this form,このフォームを送信するにはログインしてください,
You need to be in developer mode to edit a Standard Web Form,標準Webフォームを編集するためには、開発者モードにする必要があります,
You need to be logged in and have System Manager Role to be able to access backups.,バックアップにアクセスするには、ログインしていること、システムマネージャーの役割を持っていることが必要です,
You need to be logged in to access this {0}.,この {0} にアクセスするにはログインする必要があります,
"You need to have ""Share"" permission",「共有」権限が必要です,
You need write permission to rename,名前を変更するための権限が必要です,
You selected Draft or Cancelled documents,下書きまたはキャンセルされた文書を選択しました,
You unfollowed this document,この文書のフォローを解除しました,
Your Country,あなたの国,
Your Language,あなたの言語,
Your Name,あなたの名前,
Your account has been locked and will resume after {0} seconds,あなたのアカウントはロックされており、{0}秒後に再開します,
Your connection request to Google Calendar was successfully accepted,Googleカレンダーへの接続要求が正常に承認されました,
Your information has been submitted,情報が送信されました,
Your login id is,ログインIDは、,
Your organization name and address for the email footer.,メールフッター内の組織名と住所,
Your payment has been successfully registered.,お支払いが正常に登録されました。,
Your payment has failed.,支払が失敗しました。,
Your payment is cancelled.,支払がキャンセルされました。,
Your payment was successfully accepted,支払が正常に受信されました,
"Your query has been received. We will reply back shortly. If you have any additional information, please reply to this mail.",お問い合わせを受け付けました。すぐに折り返しご連絡いたします。追加の情報などがある場合は、このメールにご返信ください,
"Your session has expired, please login again to continue.",セッションが終了しました。ログインして続行してください。,
Zero,ゼロ,
Zero means send records updated at anytime,ゼロは、いつでも更新されたレコードを送信することを意味します,
_doctype,_doctype,
_report,_report,
adjust,調整する,
after_insert,after_insert,
align-center,中央寄せ,
align-justify,均等割付,
align-left,左寄せ,
align-right,右寄せ,
ap-northeast-1,ap-北東-1,
ap-northeast-2,AP北東2,
ap-northeast-3,AP北東3,
ap-south-1,ap-south-1,
ap-southeast-1,ap-southheast-1,
ap-southeast-2,ap-southheast-2,
arrow-down,下矢印,
arrow-left,左矢印,
arrow-right,右矢印,
arrow-up,上矢印,
asterisk,アスタリスク,
backward,後方,
ban-circle,×印,
bell,鐘,
bookmark,ブックマーク,
briefcase,書類鞄,
bullhorn,拡声器,
ca-central-1,ca-central-1,
camera,カメラ,
cancelled this document,この文書はキャンセルされました,
changed value of {0},{0}の変更された値,
changed values for {0},{0}の値を変更,
chevron-down,山カッコ（下）,
chevron-left,山カッコ（左）,
chevron-right,山カッコ（右）,
chevron-up,山カッコ（上）,
circle-arrow-down,円矢印（下）,
circle-arrow-left,円矢印（左）,
circle-arrow-right,円形矢印右,
circle-arrow-up,円矢印（上）,
cn-north-1,cn-north-1,
cn-northwest-1,cn-北西-1,
cog,歯車の歯,
darkgrey,濃い灰色,
dd-mm-yyyy,dd-mm-yyyy,
dd.mm.yyyy,dd.mm.yyyy,
dd/mm/yyyy,dd/mm/yyyy,
"document type..., e.g. customer",文書タイプ 例：顧客,
domain name,ドメイン名,
download-alt,ダウンロード-ALT,
"e.g. ""Support"", ""Sales"", ""Jerry Yang""",例「サポート」「販売」「ジェリー・ヤン」,
e.g. (55 + 434) / 4 or =Math.sin(Math.PI/2)...,例：(55 + 434) / 4 または  =Math.sin(Math.PI/2) ...,
e.g. pop.gmail.com / imap.gmail.com,例：pop.gmail.com / imap.gmail.com,
e.g. <EMAIL>. All replies will come to this inbox.,例「<EMAIL>」\n全ての返信はこの受信トレイに入ります,
e.g. smtp.gmail.com,例「smtp.gmail.com」,
e.g.:,例：,
eject,イジェクト,
envelope,封筒,
eu-central-1,eu-central-1,
eu-north-1,EU北1,
eu-west-1,eu-west-1,
eu-west-2,東西2,
eu-west-3,eu-west-3,
exclamation-sign,感嘆符記号,
eye-close,目を閉じる,
eye-open,目をあける,
facetime-video,FaceTimeのビデオ,
fairlogin,フェアログイン,
fast-backward,早戻し,
fast-forward,早送り,
film,フィルム,
fire,火災,
folder-close,フォルダを閉じる,
folder-open,フォルダ（開）,
fullscreen,フルスクリーン,
gained by {0} via automatic rule {1},自動ルール{1}により{0}が獲得しました,
gained {0} points,{0}ポイント獲得,
gave {0} points,{0}ポイントを獲得しました,
gift,ギフト,
glass,ガラス,
globe,地球,
hand-down,手（下）,
hand-left,手（左）,
hand-right,手（右）,
hand-up,手（上）,
hdd,ハードディスク,
headphones,ヘッドフォン,
heart,心,
hub,ハブ,
indent-left,インデント左,
indent-right,インデント右,
info-sign,インフォサイン,
italic,斜体,
<EMAIL>,<EMAIL>,
just now,たった今,
leaf,リーフ,
lightblue,ライトブルー,
list-alt,リスト（別種）,
magnet,マグネット,
map-marker,マップマーカー,
merged {0} into {1},マージされた{0} {1}へ,
minus,マイナス,
minus-sign,マイナス記号,
mm-dd-yyyy,mm-dd-yyyy,
mm/dd/yyyy,mm/dd/yyyy,
module name...,モジュール名...,
new type of document,文書の新しいタイプ,
no failed attempts,いいえ失敗した試み,
none of,のどれも,
ok,OK,
ok-circle,OKマーク,
ok-sign,OKサイン,
on_cancel,on_cancel,
on_change,on_change,
on_submit,on_submit,
on_trash,on_trash,
on_update,on_update,
on_update_after_submit,on_update_after_submit,
only.,のみ。,
or,または,
pause,一時停止,
pencil,鉛筆,
picture,画像,
plane,飛行機,
play,再生,
play-circle,play-circle,
plus,プラス,
plus-sign,プラス記号,
qrcode,QRコード,
query-report,query-report,
question-sign,質問記号,
remove-circle,削除アイコン,
remove-sign,削除記号,
removed,削除済,
renamed from {0} to {1},{0}から{1}に名前を変更,
repeat,繰り返す,
resize-full,全画面アイコン,
resize-horizontal,横幅変更アイコン,
resize-small,縮小アイコン,
resize-vertical,縦高変更アイコン,
restored {0} as {1},{0} を {1} として復元,
retweet,リツイート,
road,道路,
sa-east-1,東南1,
screenshot,スクリーンショット,
share-alt,共有,
shopping-cart,ショッピング·カート,
show,表示,
signal,信号,
star,星,
star-empty,星（空白）,
step-backward,戻る,
step-forward,進む,
submitted this document,この文書を提出,
text in document type,文書タイプのテキスト,
text-height,text-height,
text-width,テキスト幅,
th,th,
th-large,th-large,
th-list,th-list,
thumbs-down,不賛成,
thumbs-up,賛成,
tint,色調,
toggle Tag,トグルタグ,
updated to {0},{0}に更新,
us-east-1,私たち東-1,
us-east-2,アメリカ東2,
us-west-1,us-west-1,
us-west-2,私たち西2,
use % as wildcard,ワイルドカードとして％を使用して下さい。,
values separated by commas,カンマで区切られた値,
via automatic rule {0} on {1},{1}の自動ルール{0}を介して,
viewed,見た,
volume-down,音量を下げる,
volume-off,無音,
volume-up,音量を上げる,
warning-sign,警告サイン,
wrench,レンチ,
yyyy-mm-dd,yyyy-mm-dd,
zoom-in,ズームアップ,
zoom-out,ズームアウト,
{0} Calendar,{0}カレンダー,
{0} Chart,{0}グラフ,
{0} Dashboard,{0}ダッシュボード,
{0} List,{0}リスト,
{0} Modules,{0}モジュール,
{0} Report,{0}レポート,
{0} Settings not found,{0}の設定が見つかりません,
{0} Tree,{0}ツリー,
{0} added,{0}を追加,
{0} already exists. Select another name,{0}は既に存在します。別の名前を選択,
{0} already unsubscribed,{0} は登録解除済です,
{0} already unsubscribed for {1} {2},{0} は {1} {2} により登録解除済です,
{0} and {1},{0}と{1},
{0} appreciated on {1},{0} {1}に感謝,
{0} appreciated your work on {1} with {2} point,{0}、{2}ポイントで{1}へのあなたの仕事を評価しました,
{0} appreciated your work on {1} with {2} points,{0}が{2}ポイントで{1}への取り組みを評価しました,
{0} appreciated {1},{0}感謝しています{1},
{0} appreciation point for {1} {2},{1}の評価ポイント{0} {2},
{0} appreciation points for {1} {2},{1}の評価ポイント{0} {2},
{0} assigned {1}: {2},{0}は{1}に割り当てられました：{2},
{0} cannot be set for Single types,{0}シングルタイプに設定することはできません,
{0} comments,{0} コメント,
{0} created successfully,{0}が正常に作成されました,
{0} criticism point for {1} {2},{1}に対する{0}批判のポイント{2},
{0} criticism points for {1} {2},{1} {2}に対する{0}批判ポイント,
{0} criticized on {1},{0}は{1}を批判しました,
{0} criticized your work on {1} with {2} point,{0}は{1}に対する作業を{2}のポイントで批判しています,
{0} criticized your work on {1} with {2} points,{0}は{1}に対する作業を{2}ポイントで批判しています,
{0} criticized {1},{0}が{1}を批判しました,
{0} days ago,{0}日前,
{0} does not exist in row {1},行 {1} に {0} は存在しません,
"{0} field cannot be set as unique in {1}, as there are non-unique existing values",非ユニークの値が存在するため、フィールド {0} を{1} 内でユニークに設定することはできません,
{0} has already assigned default value for {1}.,{0}は既に{1}のデフォルト値を割り当てています。,
{0} has been successfully added to the Email Group.,{0}は正常に電子メールグループに追加されました。,
{0} has left the conversation in {1} {2},{0} が {1} {2} の会話から退出しました,
{0} hours ago,{0}時間前,
{0} in row {1} cannot have both URL and child items,行{1}の{0}は、URLと子アイテムの両方を持つことはできません,
{0} is a mandatory field,{0}は必須フィールドです,
{0} is an invalid email address in 'Recipients',{0}は「受信者」のメールアドレスが無効です,
{0} is not a raw printing format.,{0}は生の印刷形式ではありません。,
{0} is not a valid Email Address,{0}は有効なメールアドレスではありません,
{0} is not a valid Workflow State. Please update your Workflow and try again.,{0}は有効なワークフロー状態ではありません。ワークフローを更新してもう一度お試しください。,
{0} is now default print format for {1} doctype,{0}は{1}のdoctypeのデフォルトの印刷形式になりました,
{0} is saved,{0} を保存しました,
{0} items selected,選択した{0}アイテム,
{0} logged in,{0} ログイン,
{0} logged out: {1},{0}ログアウト：{1},
{0} minutes ago,{0}分前,
{0} months ago,{0}ヶ月前,
{0} must be one of {1},{0}は{1}のいずれかである必要があります,
{0} must be set first,{0}を最初に設定する必要があります,
{0} must be unique,{0}は重複出来ません,
{0} not a valid State,{0}は有効ではありません,
{0} not allowed to be renamed,{0}の名前は変更できません,
{0} not found,{0}が見つかりません,
{0} of {1},{1}の{0},
{0} or {1},{0}または{1},
{0} record deleted,{0}レコードが削除されました,
{0} records deleted,{0}レコードが削除されました,
{0} reverted your point on {1},{0}が{1}へのあなたの主張を元に戻しました,
{0} reverted your points on {1},{0}があなたのポイントを{1}に戻しました,
{0} reverted {1},{0}が{1}を元に戻しました,
{0} room must have atmost one user.,{0}部屋には、最大で1人のユーザーがいる必要があります。,
{0} rows for {1},{1}の{0}行,
{0} saved successfully,{0}は正常に保存されました,
{0} self assigned this task: {1},{0}はこのタスクを割り当てられました：{1},
{0} shared this document with everyone,{0}がこの文書を全員に共有しています,
{0} shared this document with {1},{0} がこの文書を{1}と共有,
{0} subscribers added,{0}登録者追加済,
{0} to stop receiving emails of this type,このタイプのメールの受信を停止するには{0},
{0} to {1},{0}から{1},
{0} un-shared this document with {1},{0} はこの文書を{1}と共有解除しました,
{0} updated,{0} 更新,
{0} weeks ago,{0}週間前,
{0} {1} added,{0} {1}が追加されました,
{0} {1} already exists,{0} {1}はすでに存在しています,
"{0} {1} cannot be ""{2}"". It should be one of ""{3}""","{0} {1} は""{2}""にすることはできません。""{3}""のいずれかでなければなりません",
{0} {1} cannot be a leaf node as it has children,{0} {1}には子ノードがあるため、リーフノードにすることはできません,
"{0} {1} does not exist, select a new target to merge",{0} {1}は存在しないため、マージする新しいターゲットを選択してください,
{0} {1} not found,{0} {1}が見つかりません,
{0} {1} to {2},{0} {1} {2}へ,
"{0}, Row {1}",{0}、行{1},
"{0}: '{1}' ({3}) will get truncated, as max characters allowed is {2}",{0}：最大文字数が {2} であるため '{1}'（{3}）は切り捨てられます,
{0}: Cannot set Amend without Cancel,{0}：キャンセルせずに修正を設定することはできません,
{0}: Cannot set Assign Amend if not Submittable,{0}：提出可能になっていない場合は、割当の修正が設定できません,
{0}: Cannot set Assign Submit if not Submittable,{0}：提出可能になっていない場合は、割当の提出が設定できません,
{0}: Cannot set Cancel without Submit,{0}：提出せずにキャンセルを設定することはできません,
{0}: Cannot set Import without Create,{0}：作成せずにインポートを設定することはできません,
"{0}: Cannot set Submit, Cancel, Amend without Write",{0}：書き込みせずに提出・キャンセル・修正を設定することはできません,
{0}: Cannot set import as {1} is not importable,{0}：{1}がインポート不可のためインポートを設定することはできません,
{0}: No basic permissions set,{0}：基本的なアクセス権が設定されていません,
"{0}: Only one rule allowed with the same Role, Level and {1}",{0}：同じ役割、レベル、{1} にには1つのルールのみ許可されます,
{0}: Permission at level 0 must be set before higher levels are set,{0}：より高いレベルを設定する前に、レベル0の権限を設定しなければなりません,
{0}: {1} in {2},{0}：{1} {2},
{0}: {1} is set to state {2},{0}：{1}が状態{2}に設定されています,
{app_title},{app_title},
{{{0}}} is not a valid fieldname pattern. It should be {{field_name}}.,{{{0}}}は有効なフィールド名のパターンではありません。これは{{}} field_nameにする必要があります。,
Communication Link,通信リンク,
Force User to Reset Password,ユーザーにパスワードのリセットを強制する,
In Days,日で,
Last Password Reset Date,最終パスワードリセット日,
The password of your account has expired.,アカウントのパスワードが期限切れです。,
Workflow State transition not allowed from {0} to {1},{0}から{1}へのワークフロー状態遷移は許可されていません,
{0} must be after {1},{0}は{1}の後になければなりません,
{0}: Field '{1}' cannot be set as Unique as it has non-unique values,{0}：フィールド &#39;{1}&#39;は一意でない値を持つため、一意に設定できません,
{0}: Field {1} in row {2} cannot be hidden and mandatory without default,{0}：行{2}のフィールド{1}はデフォルトで非表示にすることはできません必須,
{0}: Field {1} of type {2} cannot be mandatory,{0}：タイプ{2}のフィールド{1}は必須ではありません,
{0}: Fieldname {1} appears multiple times in rows {2},{0}：フィールド名{1}が行{2}に複数回現れています,
{0}: Fieldtype {1} for {2} cannot be unique,{0}：{2}のフィールドタイプ{1}は一意にできません,
{0}: Options must be a valid DocType for field {1} in row {2},{0}：オプションは、行{2}のフィールド{1}に対して有効なDocTypeでなければなりません,
{0}: Options required for Link or Table type field {1} in row {2},{0}：行{2}のリンクまたはテーブルタイプフィールド{1}に必要なオプション,
{0}: Options {1} must be the same as doctype name {2} for the field {3},{0}：オプション{1}はフィールド{3}のDoctype名{2}と同じでなければなりません,
{0}:Fieldtype {1} for {2} cannot be indexed,{0}：{2}のフィールドタイプ{1}は索引付けできません,
Make {0},{0}にする,
A user who posts blogs.,ブログを投稿したユーザー。,
Applying: {0},適用：{0},
Fieldname {0} is restricted,フィールド名{0}は制限されています,
Is Optional State,オプションの状態です,
No values to show,表示する値がありません,
View Ref,参照を見る,
Workflow Action is not created for optional states,オプションの状態のワークフローアクションは作成されません,
{0} values selected,{0}個の値が選択されました,
"""amended_from"" field must be present to do an amendment.",修正を行うには、「amended_from」フィールドが存在する必要があります。,
(Mandatory),（必須）,
1 Google Calendar Event synced.,1つのGoogleカレンダーイベントが同期されました。,
1 record will be exported,1レコードがエクスポートされます,
1 week ago,1週間前,
5 Records,5件,
A recurring {0} {1} has been created for you via Auto Repeat {2}.,繰り返し{0} {1}が自動繰り返し{2}によって作成されました。,
API,API,
API Method,APIメソッド,
About {0} minute remaining,残り約{0}分,
About {0} minutes remaining,残り約{0}分,
About {0} seconds remaining,残り約{0}秒,
Access Log,アクセスログ,
Access not allowed from this IP Address,このIPアドレスからのアクセスは許可されていません,
Action Type,アクションタイプ,
Activity Log by ,アクティビティログ,
Add Fields,フィールドを追加,
Administration,運営管理,
After Cancel,キャンセル後,
After Delete,削除後,
After Save,保存後,
After Save (Submitted Document),保存後（提出文書）,
After Submit,送信後,
Aggregate Function Based On,に基づく集約関数,
Aggregate Function field is required to create a dashboard chart,ダッシュボードチャートを作成するには、集計関数フィールドが必要です,
All Records,すべての記録,
Allot Points To Assigned Users,割り当てられたユーザーにポイントを割り当てる,
Allow Auto Repeat,自動繰り返しを許可,
Allow Google Calendar Access,Googleカレンダーアクセスを許可,
Allow Google Contacts Access,Google連絡先へのアクセスを許可する,
Allow Google Drive Access,Googleドライブのアクセスを許可,
Allow Guest,ゲストを許可,
Allow Guests to Upload Files,ゲストにファイルのアップロードを許可する,
Also adding the status dependency field {0},ステータス依存フィールド{0}も追加しています,
An error occurred while setting Session Defaults,セッションデフォルトの設定中にエラーが発生しました,
Annual,年次,
Append Emails to Sent Folder,メールを送信済みフォルダーに追加,
Apply Assignment Rule,割り当てルールを適用,
Apply Only Once,一度だけ適用,
Apply this rule only once per document,このルールをドキュメントごとに1回だけ適用します,
Approval Required,承認が必要,
Approved,承認済,
Are you sure you want to delete all rows?,すべての行を削除してもよろしいですか？,
Are you sure you want to delete this post?,この投稿を削除してもよろしいですか？,
Are you sure you want to merge {0} with {1}?,{0}を{1}とマージしてもよろしいですか？,
Assignment Day {0} has been repeated.,割り当て日{0}が繰り返されました。,
Assignment Days,割り当て日,
Assignment Rule Day,割り当てルールの日,
Assignments,課題,
Attach a web link,Webリンクを添付する,
Authorize Google Calendar Access,Googleカレンダーアクセスを認証する,
Authorize Google Contacts Access,Google連絡先へのアクセスを承認,
Authorize Google Drive Access,Googleドライブアクセスを認証する,
Auto Repeat Document Creation Failed,自動繰り返しドキュメント作成に失敗しました,
Auto Repeat Document Creation Failure,自動繰り返しドキュメント作成失敗,
Auto Repeat created for this document,この文書用に作成された自動繰り返し,
Auto Repeat failed for {0},{0}の自動繰り返しに失敗しました,
Automatic Linking can be activated only for one Email Account.,自動リンクは、1つのメールアカウントに対してのみ有効にできます。,
Automatic Linking can be activated only if Incoming is enabled.,自動リンクは、着信が有効になっている場合にのみアクティブにできます。,
Automatically generates recurring documents.,定期的な文書を自動的に生成します。,
Backing up to Google Drive.,Googleドライブにバックアップしています。,
Backup Folder ID,バックアップフォルダID,
Backup Folder Name,バックアップフォルダ名,
Before Cancel,キャンセルする前に,
Before Delete,削除する前に,
Before Insert,挿入前,
Before Save,保存する前に,
Before Save (Submitted Document),保存前（提出された文書）,
Before Submit,送信する前に,
Blank Template,空白のテンプレート,
Callback URL,コールバックURL,
Cancel All Documents,すべてのドキュメントをキャンセル,
Cancelling documents,ドキュメントをキャンセルする,
Cannot match column {0} with any field,列{0}をフィールドと一致させることはできません,
Change,変更,
Change User,ユーザーを変更,
Check the Error Log for more information: {0},エラーログで詳細を確認してください：{0},
Clear Cache and Reload,キャッシュをクリアしてリロードする,
Clear Filters,フィルターをクリア,
Click on <b>Authorize Google Drive Access</b> to authorize Google Drive Access.,[ <b>Googleドライブアクセスの承認]を</b>クリックして、 <b>Googleドライブアクセス</b>を承認します。,
Click on a file to select it.,ファイルをクリックして選択します。,
Click on the link below to approve the request,下のリンクをクリックしてリクエストを承認してください。,
Click on the lock icon to toggle public/private,ロックアイコンをクリックして、パブリック/プライベートを切り替えます,
Click on {0} to generate Refresh Token.,{0}をクリックしてリフレッシュトークンを生成してください。,
Close Condition,クローズ状態,
Columns / Fields,列/フィールド,
"Configure notifications for mentions, assignments, energy points and more.",メンション、割り当て、エネルギーポイントなどの通知を構成します。,
Contact Email,連絡先 メール,
Contact Numbers,連絡先,
Contact Phone,電話番号,
Contact Synced with Google Contacts.,Google連絡先と同期された連絡先。,
Context,環境,
Contribute Translations,翻訳に貢献する,
Contributed,寄稿,
Controller method get_razorpay_order missing,コントローラーメソッドget_razorpay_orderがありません,
Copied to clipboard.,クリップボードにコピーしました。,
Core Modules {0} cannot be searched in Global Search.,コアモジュール{0}はグローバル検索で検索できません。,
Could not create Razorpay order. Please contact Administrator,Razorpay注文を作成できませんでした。管理者に連絡してください,
Could not create razorpay order,razorpay注文を作成できませんでした,
Create Log,ログを作成,
Create your first {0},最初の{0}を作成します,
Created {0} records successfully.,{0}レコードが正常に作成されました。,
Cron,クロン,
Cron Format,cron形式,
Daily Events should finish on the Same Day.,毎日のイベントは同じ日に終了する必要があります。,
Daily Long,デイリーロング,
Default Role on Creation,作成時のデフォルトの役割,
Default Theme,デフォルトテーマ,
Default {0},デフォルト{0},
Delete All,すべて削除,
Do you want to cancel all linked documents?,すべてのリンクされたドキュメントをキャンセルしますか？,
DocType Action,DocTypeアクション,
DocType Event,DocTypeイベント,
DocType Link,DocTypeリンク,
Document Share,ドキュメント共有,
Document Tag,ドキュメントタグ,
Document Title,ドキュメントのタイトル,
Document Type Field Mapping,ドキュメントタイプフィールドマッピング,
Document Type Mapping,ドキュメントタイプマッピング,
Document Type {0} has been repeated.,ドキュメントタイプ{0}が繰り返されています。,
Document renamed from {0} to {1},ドキュメントの名前が{0}から{1}に変更されました,
Document type is required to create a dashboard chart,ダッシュボードチャートを作成するにはドキュメントタイプが必要です,
Documentation Link,ドキュメントリンク,
Don't Import,インポートしない,
Don't Send Emails,メールを送信しない,
"Drag and drop files, ",ファイルをドラッグアンドドロップし、,
Drop,ドロップ,
Drop Here,ここにドロップ,
Drop files here,ここにファイルをドロップします,
Dynamic Template,動的テンプレート,
ERPNext Role,ERPNext の役割,
Email / Notifications,メール/通知,
Email Account setup please enter your password for: {0},メールアカウントのセットアップ：{0}のパスワードを入力してください,
Email Address whose Google Contacts are to be synced.,Googleの連絡先を同期するメールアドレス。,
"Email ID must be unique, Email Account already exists for {0}",メールIDは一意である必要があります。{0}のメールアカウントは既に存在します,
Email IDs,メールアドレス,
Enable Allow Auto Repeat for the doctype {0} in Customize Form,カスタマイズフォームでDoctype {0}の自動繰り返しを許可を有効にします,
Enable Automatic Linking in Documents,文書内の自動リンクを有効にする,
Enable Email Notifications,メール通知を有効にする,
Enable Google API in Google Settings.,Google設定でGoogle APIを有効にします。,
Enable Security,セキュリティを有効にする,
Energy Point,エネルギーポイント,
Enter Client Id and Client Secret in Google Settings.,Google設定にクライアントIDとクライアントシークレットを入力します。,
Enter Code displayed in OTP App.,OTPアプリに表示されるコードを入力してください。,
Event Configurations,イベント構成,
Event Consumer,イベント消費者,
Event Consumer Document Type,イベントコンシューマドキュメントタイプ,
Event Consumer Document Types,イベントコンシューマドキュメントタイプ,
Event Producer,イベントプロデューサー,
Event Producer Document Type,イベントプロデューサーのドキュメントタイプ,
Event Producer Document Types,イベントプロデューサーのドキュメントタイプ,
Event Streaming,イベントストリーミング,
Event Subscriber,イベントサブスクライバー,
Event Sync Log,イベント同期ログ,
Event Synced with Google Calendar.,Googleカレンダーと同期されたイベント。,
Event Update Log,イベント更新ログ,
Export 1 record,1レコードをエクスポート,
Export Errored Rows,エラー行のエクスポート,
Export From,エクスポート元,
Export Type,輸出タイプ,
Export {0} records,{0}レコードをエクスポート,
Failed to connect to the Event Producer site. Retry after some time.,イベントプロデューサーサイトへの接続に失敗しました。しばらくしてから再試行してください。,
Failed to create an Event Consumer or an Event Consumer for the current site is already registered.,イベントコンシューマの作成に失敗したか、現在のサイトのイベントコンシューマが既に登録されています。,
Failure,失敗,
Fetching default Global Search documents.,デフォルトのグローバル検索ドキュメントの取得。,
Fetching posts...,投稿を取得しています...,
Field To Check,チェックするフィールド,
File Information,ファイル情報,
Filter By,フィルタリングする,
Filtered Records,フィルターされたレコード,
Filters applied for {0},{0}に適用されるフィルタ,
Finished,完成した,
First,最初,
For Document Event,ドキュメントイベント用,
"For more information, <a class=""text-muted"" href=""https://erpnext.com/docs/user/manual/en/setting-up/email/linking-emails-to-document"">click here</a>.","詳しくは、 <a class=""text-muted"" href=""https://erpnext.com/docs/user/manual/en/setting-up/email/linking-emails-to-document"">こちらをクリックしてください</a> 。",
"For more information, {0}.",詳細については、{0}。,
"For performance, only the first 100 rows were processed.",パフォーマンス上、最初の100行のみが処理されました。,
Form URL-Encoded,URLエンコードされたフォーム,
Frequently Visited Links,よく訪れるリンク,
From Date,開始日,
From User,ユーザーから,
Global Search DocType,グローバル検索DocType,
Global Search Document Types Reset.,グローバル検索ドキュメントタイプのリセット。,
Global Search Settings,グローバル検索設定,
Global Shortcuts,グローバルショートカット,
Go,移動,
Go to next record,次のレコードに進む,
Go to previous record,前のレコードに移動,
Google API Settings.,Google API設定,
Google Calendar,Googleカレンダー,
"Google Calendar - Could not create Calendar for {0}, error code {1}.",Googleカレンダー-{0}のカレンダーを作成できませんでした、エラーコード{1}。,
"Google Calendar - Could not delete Event {0} from Google Calendar, error code {1}.",Googleカレンダー-Googleカレンダーからイベント{0}を削除できませんでした、エラーコード{1}。,
"Google Calendar - Could not fetch event from Google Calendar, error code {0}.",Googleカレンダー-Googleカレンダーからイベントを取得できませんでした、エラーコード{0}。,
"Google Calendar - Could not insert contact in Google Contacts {0}, error code {1}.",Googleカレンダー-Googleコンタクト{0}に連絡先を挿入できませんでした、エラーコード{1}。,
"Google Calendar - Could not insert event in Google Calendar {0}, error code {1}.",Googleカレンダー-Googleカレンダー{0}にイベントを挿入できませんでした、エラーコード{1}。,
"Google Calendar - Could not update Event {0} in Google Calendar, error code {1}.",Googleカレンダー-Googleカレンダーのイベント{0}を更新できませんでした、エラーコード{1}。,
Google Calendar Event ID,GoogleカレンダーイベントID,
Google Calendar Integration.,Googleカレンダーの統合。,
Google Calendar has been configured.,Googleカレンダーが設定されました。,
Google Contacts,Googleの連絡先,
"Google Contacts - Could not sync contacts from Google Contacts {0}, error code {1}.",Google連絡先-Google連絡先{0}、エラーコード{1}から連絡先を同期できませんでした。,
"Google Contacts - Could not update contact in Google Contacts {0}, error code {1}.",Googleコンタクト-Googleコンタクト{0}の連絡先を更新できませんでした、エラーコード{1}。,
Google Contacts Id,Google連絡先ID,
Google Contacts Integration is disabled.,Google Contacts Integrationは無効になっています。,
Google Contacts Integration.,Googleの連絡先の統合。,
Google Contacts has been configured.,Google連絡先が設定されました。,
Google Drive,Googleドライブ,
Google Drive - Could not create folder in Google Drive - Error Code {0},Googleドライブ-Googleドライブにフォルダーを作成できませんでした-エラーコード{0},
Google Drive - Could not find folder in Google Drive - Error Code {0},Googleドライブ-Googleドライブでフォルダーが見つかりませんでした-エラーコード{0},
Google Drive Backup Successful.,Googleドライブのバックアップに成功しました。,
Google Drive Backup.,Googleドライブバックアップ。,
Google Drive Integration.,Googleドライブの統合。,
Google Drive has been configured.,Googleドライブが構成されました。,
Google Integration is disabled.,Google統合は無効です。,
Google Settings,Google設定,
Group By,グループ化,
Group By Based On,基づいてグループ化,
Group By Type,タイプ別グループ,
Group By field is required to create a dashboard chart,グループ化フィールドは、ダッシュボードチャートを作成するために必要です,
HH:mm,HH：mm,
HH:mm:ss,HH：mm：ss,
HOOK-.####,針-。＃＃＃＃,
HTML Page,HTMLページ,
Has Mapping,マッピングあり,
Hourly Long,毎時,
"If non-standard port (e.g. POP3: 995/110, IMAP: 993/143)",非標準ポート（例：POP3：995/110、IMAP：993/143）,
If the document has different field names on the Producer and Consumer's end check this and set up the Mapping,文書のプロデューサー側とコンシューマー側で異なるフィールド名がある場合、これを確認してマッピングを設定します,
If this is checked the documents will have the same name as they have on the Event Producer's site,これがチェックされている場合、ドキュメントはイベントプロデューサーのサイトにあるものと同じ名前になります。,
Illegal SQL Query,不正なSQLクエリ,
Import File,インポートファイル,
Import Log Preview,インポートログプレビュー,
Import Preview,インポートプレビュー,
Import Progress,インポートの進行状況,
Import Type,インポートタイプ,
Import Warnings,インポート警告,
"Import template should be of type .csv, .xlsx or .xls",インポートテンプレートは、.csv、.xlsx、または.xlsタイプである必要があります,
Import template should contain a Header and atleast one row.,インポートテンプレートには、ヘッダーと少なくとも1行を含める必要があります。,
Importing {0} of {1},{1}の{0}をインポートしています,
"Importing {0} of {1}, {2}",{1}、{2}の{0}をインポートしています,
Include indentation,インデントを含める,
Incoming Change,着信変更,
Invalid Filter Value,無効なフィルター値,
Invalid URL,無効なURL,
Invalid field name: {0},無効なフィールド名：{0},
Invalid file URL. Please contact System Administrator.,無効なファイルURL。システム管理者に連絡してください。,
Invalid include path,無効なインクルードパス,
Invalid username or password,ユーザー名かパスワードが無効,
Is Primary,プライマリです,
Is Primary Mobile,プライマリモバイル,
Is Primary Phone,プライマリ電話です,
Is Tree,木です,
JSON Request Body,JSONリクエストボディ,
Javascript is disabled on your browser,Javascriptがブラウザで無効になっています,
Job,ジョブ,
Jump to field,フィールドへジャンプ,
Keyboard Shortcuts,キーボードショートカット,
LDAP Group,LDAPグループ,
LDAP Group Field,LDAPグループフィールド,
LDAP Group Mapping,LDAPグループマッピング,
LDAP Group Mappings,LDAPグループマッピング,
LDAP Last Name Field,LDAP姓フィールド,
LDAP Middle Name Field,LDAPミドルネームフィールド,
LDAP Mobile Field,LDAPモバイルフィールド,
LDAP Phone Field,LDAP電話番号フィールド,
LDAP User Creation and Mapping,LDAPユーザーの作成とマッピング,
Landscape,景観,
Last,最終,
Last Backup On,最終バックアップオン,
Last Execution,最終実行,
Last Sync On,最後の同期オン,
Last Update,最後の更新,
Last refreshed,最終更新,
Link Document Type,リンクドキュメントタイプ,
Link Fieldname,リンクフィールド名,
Loading import file...,インポートファイルを読み込んでいます...,
Local Document Type,ローカルドキュメントタイプ,
Log Data,ログデータ,
Main Section (HTML),メインセクション（HTML）,
Main Section (Markdown),メインセクション（値下げ）,
"Maintains a Log of all inserts, updates and deletions on Event Producer site for documents that have consumers.",コンシューマーを持つドキュメントのイベントプロデューサーサイトでのすべての挿入、更新、削除のログを保持します。,
Maintains a log of every event consumed along with the status of the sync and a Resync button in case sync fails.,同期のステータスとともに、消費されたすべてのイベントのログと、同期が失敗した場合の再同期ボタンを保持します。,
Make all attachments private,すべての添付ファイルをプライベートにする,
Mandatory Depends On,必須の依存,
Map Columns,列のマップ,
Map columns from {0} to fields in {1},{0}の列を{1}のフィールドにマップします,
Mapping column {0} to field {1},列{0}をフィールド{1}にマッピングしています,
Mark all as Read,すべて既読にする,
Maximum Points,最大ポイント,
Maximum points allowed after multiplying points with the multiplier value\n(Note: For no limit leave this field empty or set 0),ポイントに乗数値を乗算した後に許可される最大ポイント（注：制限がない場合、このフィールドを空のままにするか、0に設定します）,
Me,私,
Mention,言及,
Modules,モジュール,
Monthly Long,毎月,
Naming Series,シリーズ名を付ける,
Navigate Home,ホームへ移動,
Navigate list down,リストを下に移動,
Navigate list up,リストを上に移動,
New Notification,新しい通知,
New {0}: {1},新しい{0}：{1},
Newsletter should have atleast one recipient,ニュースレターには少なくとも1人の受信者が必要です,
No Events Today,今日のイベントはありません,
No Google Calendar Event to sync.,同期するGoogleカレンダーイベントはありません。,
No More Activity,アクティビティなし,
No Name Specified for {0},{0}に名前が指定されていません,
No Upcoming Events,今後のイベントはありません,
No activity,アクティビティなし,
No conditions provided,条件なし,
No contacts linked to document,ドキュメントにリンクされている連絡先はありません,
No data to export,エクスポートするデータがありません,
No documents found tagged with {0},{0}でタグ付けされたドキュメントは見つかりませんでした,
No failed logs,失敗したログはありません,
No filters found,フィルターが見つかりません,
No more items to display,表示する項目がこれ以上ありません,
No more posts,これ以上の投稿はありません,
No new Google Contacts synced.,新しいGoogle連絡先が同期されていません。,
No pending or current jobs for this site,このサイトの保留中または現在のジョブはありません,
No posts yet,まだ投稿がありません,
No records will be exported,レコードはエクスポートされません,
No results found for {0} in Global Search,グローバル検索で{0}の結果が見つかりませんでした,
No user found,ユーザーが見つかりません,
Notification Log,通知ログ,
Notification Settings,通知設定,
Notification Subscribed Document,通知購読文書,
Notifications Disabled,無効な通知,
Number of Groups,グループ数,
OAuth Client ID,OAuthクライアントID,
OTP setup using OTP App was not completed. Please contact Administrator.,OTPアプリを使用したOTPセットアップは完了しませんでした。管理者に連絡してください。,
Only one {0} can be set as primary.,プライマリとして設定できる{0}は1つだけです。,
Open Awesomebar,Awesomebarを開く,
Open Chat,オープンチャット,
Open Documents,ドキュメントを開く,
Open Help,ヘルプを開く,
Open Settings,設定を開く,
Open list item,オープンリストアイテム,
Organizational Unit for Users,ユーザーの組織単位,
Page Shortcuts,ページショートカット,
Parent Field (Tree),親フィールド（ツリー）,
Parent Field must be a valid fieldname,親フィールドは有効なフィールド名でなければなりません,
Pin Globally,グローバルに固定,
Places,場所,
Please check the filter values set for Dashboard Chart: {},ダッシュボードチャートに設定されているフィルター値を確認してください：{},
Please enable pop-ups in your browser,ブラウザのポップアップを有効にしてください,
Please find attached {0}: {1},添付の{0}を見つけてください：{1},
Please select applicable Doctypes,該当するDoctypeを選択してください,
Portrait,ポートレート,
Press Alt Key to trigger additional shortcuts in Menu and Sidebar,Altキーを押すと、メニューとサイドバーにショートカットが追加されます。,
Print Settings...,印刷設定...,
Producer Document Name,プロデューサードキュメント名,
Producer URL,プロデューサーURL,
Property Depends On,プロパティ依存,
Pull from Google Calendar,Googleカレンダーから取得,
Pull from Google Contacts,Googleコンタクトからプル,
Pulled from Google Calendar,Googleカレンダーから取得,
Pulled from Google Contacts,Googleコンタクトから取得,
Push to Google Calendar,Googleカレンダーにプッシュ,
Push to Google Contacts,Googleコンタクトにプッシュ,
Queue / Worker,キュー/ワーカー,
RAW Information Log,RAW情報ログ,
Raw Printing Settings...,生の印刷設定...,
Read Only Depends On,読み取り専用に依存,
Recent Activity,最近の活動,
Reference document has been cancelled,参照文書は取り消されました,
Reload File,ファイルをリロード,
Remote Document Type,リモートドキュメントタイプ,
"Renamed files and replaced code in controllers, please check!",ファイルの名前を変更し、コントローラ内のコードを置き換えました。確認してください。,
Repeat on Last Day of the Month,月の最後の日に繰り返す,
Repeats {0},{0}を繰り返します,
Report Information,レポート情報,
Report with more than 10 columns looks better in Landscape mode.,横モードでは、10列以上のレポートが見やすくなります。,
Request Structure,リクエスト構造,
Restricted,制限あり,
Restrictions,制限事項,
Resync,再同期,
Row Number,行番号,
Row {0},行{0},
Run Jobs only Daily if Inactive For (Days),非アクティブ（日）の場合にのみジョブを毎日実行する,
SMS was not sent. Please contact Administrator.,SMSは送信されませんでした。管理者に連絡してください。,
Saved Successfully,正常に保存,
Scheduled Job,スケジュールされたジョブ,
Scheduled Job Log,スケジュールされたジョブログ,
Scheduled Job Type,スケジュールされたジョブタイプ,
Scheduler Inactive,スケジューラー非アクティブ,
Scheduler is inactive. Cannot import data.,スケジューラは非アクティブです。データをインポートできません。,
Script Manager,スクリプトマネージャー,
Script Type,スクリプトタイプ,
Search Priorities,検索の優先順位,
Search Source Text,ソーステキストを検索,
Search by filename or extension,ファイル名または拡張子で検索,
Select Date Range,期間を選択,
Select Field,フィールドを選択,
Select Field...,フィールドを選択...,
Select Filters,フィルタを選択,
Select Google Calendar to which event should be synced.,イベントを同期するGoogleカレンダーを選択します。,
Select Google Contacts to which contact should be synced.,連絡先を同期するGoogle連絡先を選択します。,
Select Group By...,グループ化を選択...,
Select Mandatory,必須選択,
Select atleast 2 actions,少なくとも2つのアクションを選択,
Select list item,リスト項目を選択,
Select multiple list items,複数のリストアイテムを選択,
Send an email to {0} to link it here,{0}にメールを送信して、ここにリンクしてください,
Server Action,サーバーアクション,
Server Script,サーバースクリプト,
Session Default,セッションデフォルト,
Session Default Settings,セッションのデフォルト設定,
Session Defaults,セッションのデフォルト,
Session Defaults Saved,保存されたセッションデフォルト,
Set as Default Theme,デフォルトテーマとして設定,
Setting up Global Search documents.,グローバル検索ドキュメントを設定します。,
Show Document,ドキュメントを表示,
Show Failed Logs,失敗したログを表示,
Show Keyboard Shortcuts,キーボードショートカットを表示,
Show More Activity,より多くのアクティビティを表示,
Show Traceback,トレースバックを表示,
Show Warnings,警告を表示,
Showing only first {0} rows out of {1},{1}のうち最初の{0}行のみを表示しています,
"Simple Python Expression, Example: Status in (""Invalid"")",単純なPython式、例：（「無効」のステータス）,
Skipping Untitled Column,無題の列をスキップする,
Skipping column {0},列{0}をスキップしています,
Social Home,ソーシャルホーム,
Some columns might get cut off when printing to PDF. Try to keep number of columns under 10.,PDFへの印刷時に一部の列が途切れる場合があります。列数を10未満に保つようにしてください。,
Something went wrong during the token generation. Click on {0} to generate a new one.,トークン生成中に問題が発生しました。 {0}をクリックして新しいものを生成してください。,
Submit After Import,インポート後に送信,
Submitting...,送信しています...,
Success! You are good to go 👍,成功！あなたは行ってもいいです,
Successful Transactions,成功したトランザクション,
Successfully Submitted!,正常に送信されました！,
Successfully imported {0} record.,{0}レコードを正常にインポートしました。,
Successfully imported {0} records.,{0}レコードを正常にインポートしました。,
Successfully updated {0} record.,{0}レコードを更新しました。,
Successfully updated {0} records.,{0}レコードを更新しました。,
Sync Calendar,カレンダーを同期,
Sync Contacts,連絡先を同期,
Sync with Google Calendar,Googleカレンダーと同期,
Sync with Google Contacts,Googleコンタクトと同期,
Synced,同期済み,
Syncing,同期中,
Syncing {0} of {1},{1}の{0}を同期しています,
Tag Link,タグリンク,
Take Backup,バックアップを取る,
Template Error,テンプレートエラー,
Template Options,テンプレートオプション,
Template Warnings,テンプレートの警告,
The Auto Repeat for this document has been disabled.,この文書の自動繰り返しは無効になっています。,
The following records needs to be created before we can import your file.,ファイルをインポートする前に、次のレコードを作成する必要があります。,
The mapping configuration between two doctypes.,2つのdoctype間のマッピング構成。,
The site which is consuming your events.,イベントを消費しているサイト。,
The site you want to subscribe to for consuming events.,イベントを消費するためにサブスクライブするサイト。,
The webhook will be triggered if this expression is true,この式がtrueの場合、webhookがトリガーされます,
The {0} is already on auto repeat {1},{0}は既に自動リピート中です{1},
There are some linked records which needs to be created before we can import your file. Do you want to create the following missing records automatically?,ファイルをインポートする前に、作成する必要があるリンクされたレコードがいくつかあります。次の不足しているレコードを自動的に作成しますか？,
There should be atleast one row for the following tables: {0},次のテーブルには少なくとも1行必要です：{0},
There should be atleast one row for {0} table,{0}テーブルには少なくとも1行必要です,
This action is only allowed for {},このアクションは{}に対してのみ許可されます,
This cannot be undone,これは、元に戻すことはできません,
Time Format,時間の形式,
Time series based on is required to create a dashboard chart,に基づく時系列は、ダッシュボードグラフを作成するために必要です,
Time {0} must be in format: {1},時間{0}の形式は{1}でなければなりません,
"To configure Auto Repeat, enable ""Allow Auto Repeat"" from {0}.",自動繰り返しを設定するには、{0}から「自動繰り返しを許可する」を有効にします。,
To enable it follow the instructions in the following link: {0},有効にするには、次のリンクの指示に従ってください：{0},
"To use Google Calendar, enable {0}.",Googleカレンダーを使用するには、{0}を有効にします。,
"To use Google Contacts, enable {0}.",Googleコンタクトを使用するには、{0}を有効にします。,
"To use Google Drive, enable {0}.",Googleドライブを使用するには、{0}を有効にします。,
Today's Events,今日のイベント,
Toggle Public/Private,パブリック/プライベートの切り替え,
Tracks milestones on the lifecycle of a document if it undergoes multiple stages.,文書が複数の段階にある場合、その文書のライフサイクルに関するマイルストーンを追跡します。,
Tree structures are implemented using Nested Set,ツリー構造は、ネストされたセットを使用して実装されます,
Trigger Primary Action,プライマリアクションをトリガする,
URL for documentation or help,ドキュメントまたはヘルプのURL,
URL must start with 'http://' or 'https://',URLは「http：//」または「https：//」で始まる必要があります,
Unchanged,変更なし,
Unpin,固定を解除,
Untitled Column,無題のコラム,
Untranslated,未翻訳,
Upcoming Events,今後のイベント,
Update Existing Records,既存のレコードを更新する,
Updated To A New Version 🎉,新しいバージョンに更新🎉,
"Updating {0} of {1}, {2}",{1}、{2}の{0}を更新しています,
Upload file,ファイルをアップロードする,
Upload {0} files,{0}ファイルをアップロード,
Uploaded To Google Drive,Googleドライブにアップロードしました,
Uploaded successfully,アップロードしました,
Uploading {0} of {1},{0}の{1}をアップロードしています,
Use SSL for Outgoing,発信にSSLを使用する,
Use Same Name,同じ名前を使用,
Used For Google Maps Integration.,Googleマップの統合に使用されます。,
User ID Property,ユーザーIDプロパティ,
User Profile,ユーザープロフィール,
User Settings,ユーザー設定,
User does not exist,ユーザーは存在しません,
User {0} has requested for data deletion,ユーザー{0}がデータ削除を要求しました,
Users assigned to the reference document will get points.,参照ドキュメントに割り当てられたユーザーはポイントを取得します。,
Value must be one of {0},値は{0}のいずれかでなければなりません,
Value {0} missing for {1},{1}の値{0}がありません,
Verification,検証,
Verification Code,検証コード,
Verification code email not sent. Please contact Administrator.,確認コードのメールは送信されません。管理者に連絡してください。,
Verified,確認済み,
Verifier,検証者,
View Full Log,完全なログを表示,
"View Log of all print, download and export events",すべての印刷、ダウンロード、エクスポートイベントのログを表示,
Visit Web Page,Webページにアクセス,
Webhook Secret,Webhookシークレット,
Webhook Security,Webhookセキュリティ,
Webhook Trigger,Webhookトリガー,
Weekly Long,ウィークリーロング,
"When enabled this will allow guests to upload files to your application, You can enable this if you wish to collect files from user without having them to log in, for example in job applications web form.",有効にすると、ゲストがアプリケーションにファイルをアップロードできるようになります。たとえば、求人応募のウェブフォームでログインせずにユーザーからファイルを収集する場合は、これを有効にできます。,
Will run scheduled jobs only once a day for inactive sites. Default 4 days if set to 0.,非アクティブなサイトに対してスケジュールされたジョブを1日1回のみ実行します。 0に設定されている場合、デフォルトの4日間。,
Workflow Status,ワークフローステータス,
You are not allowed to export {} doctype,{} Doctypeをエクスポートすることはできません,
You can try changing the filters of your report.,レポートのフィルターを変更してみてください。,
You do not have permissions to cancel all linked documents.,すべてのリンクされたドキュメントをキャンセルする権限がありません。,
You need to create these first: ,最初にこれらを作成する必要があります。,
You need to enable JavaScript for your app to work.,アプリが機能するにはJavaScriptを有効にする必要があります。,
You need to install pycups to use this feature!,この機能を使うにはpycupsをインストールする必要があります。,
Your Target,あなたのターゲット,
"browse,",ブラウズ、,
cancelled this document {0},このドキュメントをキャンセルしました{0},
changed value of {0} {1},{0} {1}の値を変更しました,
changed values for {0} {1},{0} {1}の値を変更しました,
choose an,選ぶ,
empty,空の,
of,の,
or attach a,または添付,
submitted this document {0},このドキュメントを送信しました{0},
"tag name..., e.g. #tag",タグ名...、例えば#tag,
uploaded file,アップロードされたファイル,
via Data Import,データインポート経由,
{0} Google Calendar Events synced.,{0} Googleカレンダーイベントが同期されました。,
{0} Google Contacts synced.,{0} Google連絡先が同期されました。,
{0} are mandatory fields,{0}は必須フィールドです,
{0} are required,{0}が必要です,
{0} assigned a new task {1} {2} to you,{0}が新しいタスク{1} {2}をあなたに割り当てました,
{0} gained {1} point for {2} {3},{0}は{2} {3}で{1}ポイントを獲得しました,
{0} gained {1} points for {2} {3},{0}は{2} {3}で{1}ポイントを獲得しました,
{0} has no versions tracked.,{0}にはバージョンが追跡されていません。,
{0} is not a valid report format. Report format should one of the following {1},{0}は有効なレポート形式ではありません。レポート形式は次のいずれかでなければなりません{1},
{0} mentioned you in a comment in {1} {2},{0}が、{1} {2}のコメントであなたに言及しました,
{0} of {1} ({2} rows with children),{1}の{0}（子を持つ{2}行）,
{0} records will be exported,{0}レコードがエクスポートされます,
{0} shared a document {1} {2} with you,{0}はドキュメント{1} {2}をあなたと共有しました,
{0} should not be same as {1},{0}は{1}と同じではいけません,
{0} translations pending,{0}件の翻訳が保留中,
{0} {1} is linked with the following submitted documents: {2},{0} {1}は、提出された次のドキュメントにリンクされています：{2},
"{0}: Failed to attach new recurring document. To enable attaching document in the auto repeat notification email, enable {1} in Print Settings",{0}：新しい定期的なドキュメントの添付に失敗しました。自動繰り返し通知メールでドキュメントの添付を有効にするには、印刷設定で{1}を有効にします,
{0}: Fieldname cannot be one of {1},{0}：フィールド名は{1}の1つにはできません,
{} Complete,{}完了,
← Back to upload files,←ファイルのアップロードに戻る,
Activity,活動,
Add / Manage Email Accounts.,メールアカウントの追加／管理,
Add Child,子を追加,
Add Multiple,複数追加,
Add Participants,参加者を追加,
Added {0} ({1}),追加された{0}（{1}）,
Address Line 1,住所 1行目,
Addresses,住所,
All,すべて,
Brand,ブランド,
Browse,ブラウズ,
Cancelled,キャンセル,
Chart,チャート,
Close,閉じる,
Communication,コミュニケーション,
Compact Item Print,コンパクトな項目を印刷,
Company,会社,
Complete,完了,
Completed,完了,
Continue,継続,
Country,国,
Creating {0},{0}の作成,
Currency,通貨,
Customize,カスタマイズ,
Daily,日次,
Date,日付,
Dear,親愛なる 様,
Default,初期値,
Delete,削除,
Description,説明,
Designation,肩書,
Disabled,無効,
Doctype,文書タイプ,
Download Template,テンプレートのダウンロード,
Due Date,期日,
Duplicate,複製,
Edit Profile,プロフィール編集,
Email,Eメール,
Enter Value,値を入力します,
Entity Type,エンティティタイプ,
Error,エラー,
Expired,期限切れ,
Export,エクスポート,
Export not allowed. You need {0} role to export.,エクスポートは許可されていません。役割{0}を必要とします。,
Field,フィールド,
File Manager,ファイルマネージャー,
Filters,フィルター,
Get Items,項目を取得,
Goal,目標,
Group,グループ,
Group Node,グループノード,
Help,ヘルプ,
Help Article,ヘルプ記事,
Home,ホーム,
Import Data from CSV / Excel files.,CSV / Excelファイルからデータをインポートする。,
In Progress,進行中,
Intermediate,中間体,
Invite as User,ユーザーとして招待,
Loading...,読み込んでいます...,
Location,場所,
Message,メッセージ,
Missing Values Required,欠損値が必要です,
Mobile No,携帯番号,
Month,月,
Name,名前,
Newsletter,ニュースレター,
Not Allowed,許可されていません,
Note,ノート,
Offline,オフライン,
Open,オープン,
Page {0} of {1},ページ {0} / {1},
Pending,保留,
Phone,電話,
Please click on the following link to set your new password,新しいパスワードを設定するには、次のリンクをクリックしてください,
Please select another payment method. Stripe does not support transactions in currency '{0}',別のお支払い方法を選択してください。Stripe は通貨「{0}」での取引をサポートしていません,
Please specify,指定してください,
Printing,印刷,
Priority,優先度,
Project,プロジェクト,
Quarterly,4半期ごと,
Queued,キュー追加済,
Quick Entry,クイックエントリー,
Reason,理由,
Refreshing,リフレッシュ,
Rename,名称変更,
Reset,リセット,
Review,見直し,
Room,教室,
Room Type,ルームタイプ,
Save,保存,
Search results for,検索結果,
Select All,すべて選択,
Send,送信,
Sending,送信中,
Server Error,サーバーエラー,
Set,設定,
Setup,セットアップ,
Setup Wizard,セットアップウィザード,
Size,サイズ,
Sr,Sr,
Start,開始,
Start Time,開始時間,
Status,ステータス,
Submitted,提出済,
Tag,鬼ごっこ,
Template,テンプレート,
Thursday,木曜日,
Title,タイトル,
Total,計,
Totals,合計,
Tuesday,火曜日,
Type,タイプ,
Update,更新,
User {0} is disabled,ユーザー{0}無効になっています,
Users and Permissions,ユーザーと権限,
Warehouse,倉庫,
Welcome to {0},{0}へようこそ,
Year,年,
Yearly,毎年,
You,あなた,
and,＆,
{0} Name,{0}の名前,
{0} is required,{0}が必要です,
ALL,すべて,
Attach File,ファイルを添付,
Barcode,バーコード,
Beginning with,ではじまる,
Bold,大胆な,
CANCELLED,キャンセル,
Calendar,カレンダー,
Center,中央,
Clear,晴れ,
Comment,コメント,
Comments,コメント,
DRAFT,下書き,
Dashboard,ダッシュボード,
DocType,DocType,
Download,ダウンロード,
EMail,メール,
Edit in Full Page,全ページで編集,
Email Inbox,メール受信ボックス,
File,ファイル,
Forward,進む,
Icon,アイコン,
In,In,
Inbox,受信トレイ,
Insert New Records,新しいレコードを挿入する,
JavaScript,JavaScript,
LDAP Settings,LDAP設定,
Left,退職,
Like,お気に入り,
Link,リンク,
Logged in,ログイン済み,
New,新着,
Not Found,見つかりません,
Not Like,みたいではなく,
Notify by Email,メールで通知,
Now,今,
Off,オフ,
One of,の一つ,
Page,ページ,
Print,印刷する,
Reference Name,参照名,
Refresh,再読込,
Repeat,繰り返し,
Right,右,
Roles HTML,役割HTML,
Scheduled To Send,送信するスケジュール,
Search Results for ,の検索結果,
Send Notification To,通知送信先,
Success,成功,
Tags,タグ,
Time,時間,
Updated Successfully,更新成功,
Upload,アップロード,
User ,ユーザー,
Value,値,
Web Link,ウェブリンク,
Your Email Address,あなたのメール アドレス,
Desktop,デスクトップ,
Usage Info,使用法情報,
Download Backups,バックアップダウンロード,
Recorder,レコーダー,
Role Permissions Manager,役割権限マネージャー,
Translation Tool,翻訳ツール,
Awaiting password,仮パスワード,
Current status,現在の状態,
Download template,テンプレートをダウンロード,
Edit in full page,全画面編集,
Email Id,メールアドレス,
Email address,メールアドレス,
Ends on,終了,
Half-yearly,半年ごと,
Hidden,隠された,
Javascript,Javascript,
Ldap settings,Ldap設定,
Mobile number,携帯電話番号,
Mx,Mx,
No,いいえ,
Not found,見つかりません,
Notes:,ノート：,
Notify by email,メールによる通知,
Permitted Documents For User,アクセス許可された文書,
Reference Docname,参照DocName,
Reference Doctype,参照文書型,
Reference name,参照名,
Roles Html,ロールHtml,
Row #,行番号,
Scheduled to send,送信予定,
Select Doctype,Doctypeを選択,
Send Email for Successful backup,成功したバックアップのためにメールを送信する,
Sign up,サインアップ,
Time format,時間の形式,
Upload failed,アップロードに失敗しました,
User Id,ユーザーID,
Yes,はい,
Your email address,メールアドレス,
added,追加しました,
added {0},{0}を追加しました,
barcode,バーコード,
beginning with,次の文字で開始：,
blue,青い,
bold,太字,
book,本,
calendar,カレンダー,
certificate,証明書,
check,チェック,
clear,クリア,
comment,コメント,
comments,コメント,
created,作成済,
danger,危険,
dashboard,ダッシュボード,
download,ダウンロード,
edit,編集,
email inbox,メール受信トレイ,
file,ファイル,
filter,フィルタ,
flag,旗,
font,フォント,
forward,転送,
green,緑,
home,ホーム,
icon,アイコン,
inbox,受信トレイ,
like,いいね！,
link,リンク,
list,リスト,
lock,ロック,
logged in,ログイン,
message,メッセージ,
module,モジュール,
move,移動する,
music,音楽,
new,新規,
now,今,
off,オフ,
one of,一部,
orange,オレンジ,
page,ページ,
print,印刷,
purple,紫,
random,ランダム,
red,赤,
refresh,リフレッシュ,
remove,削除する,
response,応答,
search,サーチ,
share,シェア,
stop,やめる,
success,成功,
tag,タグ,
tags,タグ,
tasks,タスク,
time,時間,
trash,ゴミ箱,
upload,アップロードする,
user,ユーザー,
value,値,
web link,Webリンク,
yellow,黄色,
Not permitted,許可されていません,
Add Chart to Dashboard,ダッシュボードにチャートを追加,
Add to Dashboard,ダッシュボードに追加,
Google Translation,Google翻訳,
Important,重要,
No Filters Set,フィルターが設定されていません,
No Records Created,レコードが作成されていません,
Please Set Chart,チャートを設定してください,
Please create chart first,最初にチャートを作成してください,
"Report has no data, please modify the filters or change the Report Name",レポートにデータがありません。フィルターを変更するか、レポート名を変更してください,
Select Dashboard,ダッシュボードを選択,
Y Field,Yフィールド,
You need to be in developer mode to edit this document,このドキュメントを編集するには、開発者モードである必要があります,
Cards,カード,
Community Contribution,コミュニティへの貢献,
Count Filter,カウントフィルター,
Dashboard Chart Field,ダッシュボードチャートフィールド,
Desk Card,デスクカード,
Desk Chart,デスクチャート,
Desk Page,デスクページ,
Desk Shortcut,デスクショートカット,
Developer Mode Only,開発者モードのみ,
Disable User Customization,ユーザーのカスタマイズを無効にする,
For example: {} Open,例：{}オープン,
Link Cards,リンクカード,
Link To,リンク先,
Onboarding,オンボーディング,
Percentage,パーセンテージ,
Pie,パイ,
Pin To Bottom,下にピン留め,
Pin To Top,トップにピン,
Restrict to Domain,ドメインに制限する,
Shortcuts,ショートカット,
X Field,Xフィールド,
Y Axis,Y軸,
workspace,ワークスペース,
Setup > User,設定&gt;ユーザー,
Setup > Customize Form,[設定]&gt; [フォームのカスタマイズ],
Setup > User Permissions,設定&gt;ユーザー権限,
"Error connecting to QZ Tray Application...<br><br> You need to have QZ Tray application installed and running, to use the Raw Print feature.<br><br><a target=""_blank"" href=""https://qz.io/download/"">Click here to Download and install QZ Tray</a>.<br> <a target=""_blank"" href=""https://erpnext.com/docs/user/manual/en/setting-up/print/raw-printing"">Click here to learn more about Raw Printing</a>.","QZトレイアプリケーションへの接続エラー... <br><br> Raw印刷機能を使用するには、QZ Trayアプリケーションをインストールして実行する必要があります。 <br><br> <a href=""https://qz.io/download/"" target=""_blank"">QZ Trayをダウンロードしてインストールするには、ここをクリックしてください</a> 。 <br> <a href=""https://erpnext.com/docs/user/manual/en/setting-up/print/raw-printing"" target=""_blank"">Raw印刷の詳細については、ここをクリックしてください</a> 。",
No email account associated with the User. Please add an account under User > Email Inbox.,ユーザーに関連付けられたメールアカウントはありません。 [ユーザー]&gt; [メール受信ボックス]でアカウントを追加してください。,
"For comparison, use >5, <10 or =324. For ranges, use 5:10 (for values between 5 & 10).",比較のために、&gt; 5、&lt;10または= 324を使用します。範囲の場合、5：10を使用します（5〜10の値の場合）。,
No default Address Template found. Please create a new one from Setup > Printing and Branding > Address Template.,デフォルトのアドレステンプレートが見つかりません。 [設定]&gt; [印刷とブランディング]&gt; [アドレステンプレート]から新しいものを作成してください。,
Please setup default Email Account from Setup > Email > Email Account,[設定]&gt; [メール]&gt; [メールアカウント]からデフォルトのメールアカウントを設定してください,
Email Account not setup. Please create a new Email Account from Setup > Email > Email Account,メールアカウントが設定されていません。 [設定]&gt; [メール]&gt; [メールアカウント]から新しいメールアカウントを作成してください,
Attach file,ファイルを添付する,
Contribution Status,貢献状況,
Contribution Document Name,寄稿文書名,
Extends,拡張します,
Extends Another Page,別のページを拡張する,
Please select target language for translation,翻訳の対象言語を選択してください,
Select Language,言語を選択する,
Confirm Translations,翻訳を確認する,
Contributed Translations,寄稿された翻訳,
Show Tags,タグを表示,
Do not have permission to access {0} bucket.,{0}バケットにアクセスする権限がありません。,
Allow document creation via Email,電子メールによるドキュメントの作成を許可する,
Sender Field,送信者フィールド,
Logout All Sessions on Password Reset,パスワードリセット時にすべてのセッションをログアウトする,
Logout From All Devices After Changing Password,パスワード変更後のすべてのデバイスからのログアウト,
Send Notifications For Documents Followed By Me,私がフォローしているドキュメントの通知を送信する,
Send Notifications For Email Threads,電子メールスレッドの通知を送信する,
Bypass Restricted IP Address Check If Two Factor Auth Enabled,二要素認証が有効になっているかどうか、制限付きIPアドレスチェックをバイパスする,
Reset LDAP Password,LDAPパスワードをリセットする,
Confirm New Password,新しいパスワードを確認,
Logout All Sessions,すべてのセッションからログアウトする,
Passwords do not match!,パスワードが一致していません！,
Dashboard Manager,ダッシュボードマネージャー,
Dashboard Settings,ダッシュボード設定,
Chart Configuration,チャート構成,
No Permitted Charts on this Dashboard,このダッシュボードに許可されたグラフはありません,
No Permitted Charts,許可されたチャートはありません,
Reset Chart,チャートをリセット,
via {0},{0}経由,
{0} is not a valid Phone Number,{0}は有効な電話番号ではありません,
Failed Transactions,失敗したトランザクション,
Value for field {0} is too long in {1}. Length should be lesser than {2} characters,{1}のフィールド{0}の値が長すぎます。長さは{2}文字より短くする必要があります,
Data Too Long,データが長すぎます,
via Notification,通知経由,
Log in to access this page.,このページにアクセスするには、ログインしてください。,
Report Document Error,ドキュメントエラーの報告,
{0} is an invalid Data field.,{0}は無効なデータフィールドです。,
Only Options allowed for Data field are:,データフィールドに許可されているオプションは次のとおりです。,
Select a valid Subject field for creating documents from Email,電子メールからドキュメントを作成するための有効な件名フィールドを選択します,
"Subject Field type should be Data, Text, Long Text, Small Text, Text Editor",件名フィールドタイプは、データ、テキスト、長いテキスト、小さなテキスト、テキストエディタである必要があります,
Select a valid Sender Field for creating documents from Email,電子メールからドキュメントを作成するための有効な送信者フィールドを選択します,
Sender Field should have Email in options,送信者フィールドにはオプションでEメールが必要です,
Password changed successfully.,パスワードは正常に変更されました。,
Failed to change password.,パスワードの変更に失敗しました。,
No Entry for the User {0} found within LDAP!,LDAP内にユーザー{0}のエントリが見つかりません！,
No LDAP User found for email: {0},電子メールのLDAPユーザーが見つかりません：{0},
Prepared Report User,作成されたレポートユーザー,
Scheduler Event,スケジューライベント,
Select Event Type,イベントタイプの選択,
Schedule Script,スケジュールスクリプト,
Duration,期間,
Donut,ドーナツ,
Custom Options,カスタムオプション,
"Ex: ""colors"": [""#d1d8dd"", ""#ff5858""]",例： &quot;colors&quot;：[&quot;＃d1d8dd&quot;、 &quot;＃ff5858&quot;],
Confirmation Email Template,確認メールテンプレート,
Welcome Email Template,ウェルカムメールテンプレート,
Schedule Send,スケジュール送信,
Do you really want to send this email newsletter?,このメールマガジンを本当に送信しますか？,
Advanced Settings,高度な設定,
Disable Comments,コメントを無効にする,
Comments on this blog post will be disabled if checked.,チェックすると、このブログ投稿へのコメントは無効になります。,
CSS Class,CSSクラス,
Full Width,全幅,
Page Builder,ページビルダー,
Page Building Blocks,ページビルディングブロック,
Header and Breadcrumbs,ヘッダーとブレッドクラム,
Add Custom Tags,カスタムタグを追加する,
Web Page Block,Webページブロック,
Web Template,Webテンプレート,
Edit Values,値の編集,
Web Template Values,Webテンプレートの値,
Add Container,コンテナを追加,
Web Page View,Webページビュー,
Path,パス,
Referrer,リファラー,
Browser,ブラウザ,
Browser Version,ブラウザバージョン,
Web Template Field,Webテンプレートフィールド,
Section,セクション,
Hide,隠す,
Enable In App Website Tracking,アプリ内ウェブサイトトラッキングを有効にする,
Enable Google Indexing,Googleインデックスを有効にする,
"To use Google Indexing, enable <a href=""#Form/Google Settings"">Google Settings</a>.","Googleインデックスを使用するには、 <a href=""#Form/Google Settings"">Google設定を</a>有効にし<a href=""#Form/Google Settings"">ます</a>。",
Authorize API Indexing  Access,APIインデックスアクセスを承認する,
Indexing Refresh Token,インデックス更新トークン,
Indexing Authorization Code,インデックス認証コード,
Theme Configuration,テーマ構成,
Font Properties,フォントのプロパティ,
Button Rounded Corners,ボタンの丸い角,
Button Shadows,ボタンシャドウ,
Button Gradients,ボタンのグラデーション,
Light Color,明色,
Stylesheet,スタイルシート,
Custom SCSS,カスタムSCSS,
Navbar,ナビゲーションバー,
Source Message,ソースメッセージ,
Translated Message,翻訳されたメッセージ,
Using this console may allow attackers to impersonate you and steal your information. Do not enter or paste code that you do not understand.,このコンソールを使用すると、攻撃者があなたになりすまして情報を盗む可能性があります。わからないコードを入力したり貼り付けたりしないでください。,
{0} m,{0} m,
{0} h,{0}時間,
{0} d,{0} d,
{0} w,{0} w,
{0} M,{0} M,
{0} y,{0} y,
yesterday,昨日,
{0} years ago,{0}年前,
New Chart,新しいチャート,
New Shortcut,新しいショートカット,
Edit Chart,チャートの編集,
Edit Shortcut,ショートカットの編集,
Couldn't Load Desk,デスクを読み込めませんでした,
"Something went wrong while loading Desk. <b>Please relaod the page</b>. If the problem persists, contact the Administrator",デスクの読み込み中に問題が発生しました。<b>ページをリロードしてください</b>。問題が解決しない場合は、管理者に連絡してください,
Customize Workspace,ワークスペースをカスタマイズする,
Customizations Saved Successfully,カスタマイズが正常に保存されました,
Something went wrong while saving customizations,カスタマイズの保存中に問題が発生しました,
{} Dashboard,{}ダッシュボード,
No changes in document,ドキュメントに変更はありません,
by Role,役割別,
Document is only editable by users with role,ドキュメントは、役割を持つユーザーのみが編集できます,
{0}: Other permission rules may also apply,{0}：他の許可ルールも適用される場合があります,
{0} Web page views,{0}ページビュー,
Expand,展開,
Collapse,崩壊,
"Invalid Bearer token, please provide a valid access token with prefix 'Bearer'.",無効なベアラートークン。プレフィックス「ベアラー」が付いた有効なアクセストークンを指定してください。,
"Failed to decode token, please provide a valid base64-encoded token.",トークンのデコードに失敗しました。有効なbase64エンコードトークンを入力してください。,
"Invalid token, please provide a valid token with prefix 'Basic' or 'Token'.",トークンが無効です。プレフィックスが「Basic」または「Token」の有効なトークンを入力してください。,
{0} is not a valid Name,{0}は有効な名前ではありません,
Your system is being updated. Please refresh again after a few moments.,システムが更新されています。しばらくしてからもう一度更新してください。,
{0} {1}: Submitted Record cannot be deleted. You must {2} Cancel {3} it first.,{0} {1}：送信されたレコードは削除できません。最初に{2}キャンセル{3}する必要があります。,
Error has occurred in {0},{0}でエラーが発生しました,
Status Updated,ステータスが更新されました,
You can also copy-paste this {0} to your browser,この{0}をブラウザにコピーして貼り付けることもできます,
Enabled scheduled execution for script {0},スクリプト{0}のスケジュールされた実行を有効にしました,
Scheduled execution for script {0} has updated,スクリプト{0}のスケジュールされた実行が更新されました,
The Link specified has either been used before or Invalid,指定されたリンクは、以前に使用されたか、無効です,
Options for {0} must be set before setting the default value.,デフォルト値を設定する前に、{0}のオプションを設定する必要があります。,
Default value for {0} must be in the list of options.,{0}のデフォルト値はオプションのリストに含まれている必要があります。,
Google Indexing has been configured.,Googleインデックスが設定されました。,
Allow API Indexing Access,APIインデックスアクセスを許可する,
Allow Google Indexing Access,Googleインデックスアクセスを許可する,
Custom Documents,カスタムドキュメント,
Could not save customization,カスタマイズを保存できませんでした,
Transgender,トランスジェンダー,
Genderqueer,ジェンダークワイア,
Non-Conforming,不適合,
Prefer not to say,どちらかというと言いたくない,
Is Billing Contact,請求先です,
Address And Contacts,住所と連絡先,
Lead Conversion Time,リード変換時間,
Due Date Based On,期日ベース,
Linked Documents,リンクされたドキュメント,
Steps,ステップ,
email,Eメール,
Component,成分,
Subtitle,字幕,
Prefix,接頭辞,
Is Public,公開されています,
This chart will be available to all Users if this is set,これが設定されている場合、このチャートはすべてのユーザーが利用できます,
Number Card,ナンバーカード,
Function,関数,
Minimum,最小,
Maximum,最大,
This card will be available to all Users if this is set,これが設定されている場合、このカードはすべてのユーザーが利用できます,
Stats,統計,
Show Percentage Stats,パーセンテージ統計を表示する,
Stats Time Interval,統計時間間隔,
Show percentage difference according to this time interval,この時間間隔に従ってパーセンテージの違いを表示する,
Filters Section,フィルタセクション,
Number Card Link,ナンバーカードリンク,
Card,カード,
API Access,APIアクセス,
Access Key Secret,アクセスキーシークレット,
S3 Bucket Details,S3バケットの詳細,
Bucket Name,バケット名,
Backup Details,バックアップの詳細,
Backup Files,バックアップファイル,
Backup public and private files along with the database.,データベースとともにパブリックファイルとプライベートファイルをバックアップします。,
Set to 0 for no limit on the number of backups taken,取得するバックアップの数に制限がない場合は、0に設定します,
Meta Description,メタ記述,
Meta Image,メタ画像,
Google Snippet Preview,Googleスニペットプレビュー,
This is an example Google SERP Preview.,これはGoogleSERPプレビューの例です。,
Add Gray Background,灰色の背景を追加,
Hide Block,ブロックを隠す,
This Week,今週,
This Month,今月,
This Quarter,この四半期,
This Year,今年,
All Time,いつも,
Select From Date,開始日を選択,
since yesterday,昨日から,
since last week,先週から,
since last month,先月から,
since last year,去年から,
Show,公演,
New Number Card,新しいナンバーカード,
Your Shortcuts,あなたのショートカット,
You haven't added any Dashboard Charts or Number Cards yet.,ダッシュボードチャートまたはナンバーカードはまだ追加していません。,
Click On Customize to add your first widget,[カスタマイズ]をクリックして、最初のウィジェットを追加します,
Are you sure you want to reset all customizations?,すべてのカスタマイズをリセットしてもよろしいですか？,
"Couldn't save, please check the data you have entered",保存できませんでした。入力したデータを確認してください,
Validation Error,検証エラー,
"You can only upload JPG, PNG, PDF, or Microsoft documents.",アップロードできるのは、JPG、PNG、PDF、またはMicrosoftドキュメントのみです。,
Reverting length to {0} for '{1}' in '{2}'. Setting the length as {3} will cause truncation of data.,&#39;{2}&#39;の &#39;{1}&#39;の長さを{0}に戻します。長さを{3}に設定すると、データが切り捨てられます。,
'{0}' not allowed for type {1} in row {2},行{2}のタイプ{1}には「{0}」は許可されていません,
Option {0} for field {1} is not a child table,フィールド{1}のオプション{0}は子テーブルではありません,
Invalid Option,無効なオプション,
Request Body consists of an invalid JSON structure,リクエスト本文が無効なJSON構造で構成されている,
Invalid JSON,無効なJSON,
Party GSTIN,パーティーGSTIN,
GST State,GSTの状態,
Andaman and Nicobar Islands,アンダマンニコバル諸島,
Andhra Pradesh,アンドラプラデーシュ,
Arunachal Pradesh,アルナーチャルプラデーシュ州,
Assam,アッサム,
Bihar,ビハール,
Chandigarh,チャンディーガル,
Chhattisgarh,チャッティースガル,
Dadra and Nagar Haveli,ダドラとナガルハベリ,
Daman and Diu,ダマンディーウ,
Delhi,デリー,
Goa,行きます,
Gujarat,グジャラート,
Haryana,ハリヤナ,
Himachal Pradesh,ヒマーチャルプラデーシュ州,
Jammu and Kashmir,ジャンムー・カシミール,
Jharkhand,ジャールカンド,
Karnataka,カルナータカ,
Kerala,ケララ,
Lakshadweep Islands,ラクシャディープ諸島,
Madhya Pradesh,マディヤプラデーシュ,
Maharashtra,マハラシュトラ,
Manipur,マニプール,
Meghalaya,メガラヤ,
Mizoram,ミゾラム,
Nagaland,ナガランド,
Odisha,オリッサ,
Other Territory,その他の地域,
Pondicherry,ポンディシェリ,
Punjab,パンジャーブ,
Rajasthan,ラージャスターン,
Sikkim,シッキム,
Tamil Nadu,タミル・ナードゥ,
Telangana,テランガーナ,
Tripura,トリプラ,
Uttar Pradesh,ウッタルプラデーシュ,
Uttarakhand,ウッタラーカンド州,
West Bengal,西ベンガル,
GST State Number,GST州番号,
Import from Google Sheets,Googleスプレッドシートからインポート,
Must be a publicly accessible Google Sheets URL,公的にアクセス可能なGoogleスプレッドシートのURLである必要があります,
Refresh Google Sheet,Googleスプレッドシートを更新,
Import File Errors and Warnings,インポートファイルのエラーと警告,
"Successfully imported {0} records out of {1}. Click on Export Errored Rows, fix the errors and import again.",{1}から{0}レコードを正常にインポートしました。 [エラー行のエクスポート]をクリックし、エラーを修正してから再度インポートします。,
"Successfully imported {0} record out of {1}. Click on Export Errored Rows, fix the errors and import again.",{1}から{0}レコードを正常にインポートしました。 [エラー行のエクスポート]をクリックし、エラーを修正してから再度インポートします。,
"Successfully updated {0} records out of {1}. Click on Export Errored Rows, fix the errors and import again.",{1}から{0}レコードが正常に更新されました。 [エラー行のエクスポート]をクリックし、エラーを修正してから再度インポートします。,
"Successfully updated {0} record out of {1}. Click on Export Errored Rows, fix the errors and import again.",{1}から{0}レコードが正常に更新されました。 [エラー行のエクスポート]をクリックし、エラーを修正してから再度インポートします。,
Data Import Legacy,データインポートレガシー,
Documents restored successfully,ドキュメントが正常に復元されました,
Documents that were already restored,すでに復元されたドキュメント,
Documents that failed to restore,復元に失敗したドキュメント,
Document Restoration Summary,ドキュメント復元の概要,
Hide Days,日を隠す,
Hide Seconds,秒を隠す,
Hide Border,ボーダーを隠す,
Index Web Pages for Search,検索用のインデックスWebページ,
Action / Route,アクション/ルート,
Document Naming Rule,ドキュメントの命名規則,
Rule Conditions,ルール条件,
Digits,数字,
Example: 00001,例：00001,
Counter,カウンター,
Document Naming Rule Condition,ドキュメントの命名規則の条件,
Installed Application,インストールされているアプリケーション,
Application Name,アプリケーション名,
Application Version,アプリケーションバージョン,
Git Branch,Gitブランチ,
Installed Applications,インストールされているアプリケーション,
Navbar Item,ナビゲーションバーアイテム,
Item Label,アイテムラベル,
Item Type,アイテムタイプ,
Separator,セパレーター,
Navbar Settings,ナビゲーションバーの設定,
Application Logo,アプリケーションロゴ,
Logo Width,ロゴの幅,
Dropdowns,ドロップダウン,
Settings Dropdown,設定ドロップダウン,
Help Dropdown,ドロップダウンをヘルプ,
Query / Script,クエリ/スクリプト,
"Filters will be accessible via <code>filters</code>. <br><br>Send output as <code>result = [result]</code>, or for old style <code>data = [columns], [result]</code>","フィルタは経由でアクセスすることができます<code>filters</code> 。<br><br> <code>result = [result]</code>として出力を送信<code>result = [result]</code> 、または古いスタイルの<code>data = [columns], [result]</code>",
Client Code,クライアントコード,
Report Column,レポート列,
Report Filter,レポートフィルター,
Wildcard Filter,ワイルドカードフィルター,
"Will add ""%"" before and after the query",クエリの前後に「％」を追加します,
"Route: Example ""/desk""",ルート：例「/ desk」,
Enable Onboarding,オンボーディングを有効にする,
Password Reset Link Generation Limit,パスワードリセットリンク生成制限,
Hourly rate limit for generating password reset links,パスワードリセットリンクを生成するための1時間あたりのレート制限,
Include Web View Link in Email,ドキュメントのWebビューリンクを電子メールで送信する,
Enable Auto-deletion of Prepared Reports,準備されたレポートの自動削除を有効にする,
Prepared Report Expiry Period (Days),作成されたレポートの有効期限（日）,
System will automatically delete Prepared Reports after these many days since creation,作成から数日が経過すると、システムは準備済みレポートを自動的に削除します,
Package Document Type,パッケージドキュメントタイプ,
Include Attachments,添付ファイルを含める,
Overwrite,上書きする,
Package Publish Target,パッケージ公開ターゲット,
Site URL,サイトのURL,
Package Publish Tool,パッケージ公開ツール,
Click on the row for accessing filters.,フィルタにアクセスするには、行をクリックします。,
Sites,サイト,
Last Deployed On,最終展開日,
Console Log,コンソールログ,
"Set Default Options for all charts on this Dashboard (Ex: ""colors"": [""#d1d8dd"", ""#ff5858""])",このダッシュボードのすべてのグラフのデフォルトオプションを設定します（例： &quot;colors&quot;：[&quot;＃d1d8dd&quot;、 &quot;＃ff5858&quot;]）,
Use Report Chart,レポートチャートを使用する,
Heatmap,ヒートマップ,
Dynamic Filters,動的フィルター,
Dynamic Filters JSON,動的フィルターJSON,
Set Dynamic Filters,動的フィルターを設定する,
Click to Set Dynamic Filters,クリックして動的フィルターを設定,
Hide Custom DocTypes and Reports,カスタムDocTypeとレポートを非表示にする,
Checking this will hide custom doctypes and reports cards in Links section,これをチェックすると、リンクセクションのカスタムDoctypeとレポートカードが非表示になります,
DocType View,DocTypeビュー,
Which view of the associated DocType should this shortcut take you to?,このショートカットを使用すると、関連付けられたDocTypeのどのビューに移動できますか？,
List View Settings,リストビュー設定,
Maximum Number of Fields,フィールドの最大数,
Module Onboarding,モジュールのオンボーディング,
System managers are allowed by default,システムマネージャはデフォルトで許可されています,
Documentation URL,ドキュメントのURL,
Is Complete,完了です,
Alert,アラート,
Document Link,ドキュメントリンク,
Attached File,添付ファイル,
Attachment Link,アタッチメントリンク,
Open Reference Document,参照ドキュメントを開く,
Custom Configuration,カスタム構成,
Filters Configuration,フィルタ構成,
Dynamic Filters Section,ダイナミックフィルターセクション,
Please create Card first,最初にカードを作成してください,
Onboarding Permission,オンボーディング許可,
Onboarding Step,オンボーディングステップ,
Is Skipped,スキップされます,
Create Entry,エントリを作成する,
Update Settings,設定を更新,
Show Form Tour,フォームツアーを表示,
View Report,レポートを見る,
Go to Page,ページに移動,
Watch Video,ビデオを見る,
Show Full Form?,フルフォームを表示しますか？,
Show full form instead of a quick entry modal,クイックエントリーモーダルの代わりに完全なフォームを表示,
Report Reference Doctype,レポートリファレンスDoctype,
Report Description,レポートの説明,
This will be shown to the user in a dialog after routing to the report,これは、レポートにルーティングした後、ダイアログでユーザーに表示されます,
Example: #Tree/Account,例：＃Tree / Account,
Callback Title,コールバックタイトル,
Callback Message,コールバックメッセージ,
This will be shown in a modal after routing,これは、ルーティング後にモーダルで表示されます,
Validate Field,フィールドの検証,
Value to Validate,検証する値,
Use % for any non empty value.,空でない値には％を使用します。,
Video URL,ビデオURL,
Onboarding Step Map,オンボーディングステップマップ,
Step,ステップ,
System Console,システムコンソール,
Console,コンソール,
To print output use <code>log(text)</code>,出力を印刷するには、 <code>log(text)</code>使用し<code>log(text)</code>,
Commit,コミット,
Execute Console script,コンソールスクリプトを実行する,
Execute,実行する,
Create Contacts from Incoming Emails,受信メールから連絡先を作成する,
Inbox User,受信トレイユーザー,
Disabled Auto Reply,自動返信を無効にする,
Schedule Sending,送信スケジュール,
Message (Markdown),メッセージ（マークダウン）,
Message (HTML),メッセージ（HTML）,
Send Attachments,添付ファイルを送信する,
Testing,テスト,
System Notification,システム通知,
Twilio Number,Twilio番号,
"To use WhatsApp for Business, initialize <a href=""#Form/Twilio Settings"">Twilio Settings</a>.","WhatsApp for Businessを使用するには、 <a href=""#Form/Twilio Settings"">Twilio設定を</a>初期化します。",
"To use Slack Channel, add a <a href=""#List/Slack%20Webhook%20URL/List"">Slack Webhook URL</a>.","Slack Channelを使用するには、 <a href=""#List/Slack%20Webhook%20URL/List"">Slack WebhookURLを</a>追加し<a href=""#List/Slack%20Webhook%20URL/List"">ます</a>。",
Send System Notification,システム通知を送信する,
"If enabled, the notification will show up in the notifications dropdown on the top right corner of the navigation bar.",有効にすると、通知はナビゲーションバーの右上隅にある通知ドロップダウンに表示されます。,
Send To All Assignees,すべての譲受人に送信,
Receiver By Document Field,ドキュメントフィールド別のレシーバー,
Receiver By Role,役割別の受信者,
Child Table,子テーブル,
Remote Value Filters,リモート値フィルター,
API Key of the user(Event Subscriber) on the producer site,プロデューサーサイトのユーザー（イベントサブスクライバー）のAPIキー,
API Secret of the user(Event Subscriber) on the producer site,プロデューサーサイトのユーザー（イベントサブスクライバー）のAPIシークレット,
Paytm Settings,Paytmの設定,
Merchant Key,マーチャントキー,
Staging,演出,
Industry Type ID,業種ID,
See https://docs.aws.amazon.com/general/latest/gr/s3.html for details.,詳細については、https：//docs.aws.amazon.com/general/latest/gr/s3.htmlを参照してください。,
af-south-1,af-south-1,
ap-east-1,ap-east-1,
eu-south-1,eu-south-1,
me-south-1,me-south-1,
Twilio Number Group,Twilio番号グループ,
Twilio Settings,Twilio設定,
Auth Token,認証トークン,
Read Time,読み取り時間,
in minutes,分で,
Featured,特徴,
Hide CTA,CTAを非表示,
"Description for listing page, in plain text, only a couple of lines. (max 200 characters)",プレーンテキストでのリストページの説明、数行のみ。 （最大200文字）,
Meta Title,メタタイトル,
Enable Social Sharing,ソーシャルシェアリングを有効にする,
Show CTA in Blog,ブログにCTAを表示する,
CTA,CTA,
CTA Label,CTAラベル,
CTA URL,CTA URL,
Default Portal Home,デフォルトのポータルホーム,
"Example: ""/desk""",例：「/ desk」,
Social Link Settings,ソーシャルリンク設定,
Social Link Type,ソーシャルリンクタイプ,
facebook,フェイスブック,
linkedin,LinkedIn,
twitter,ツイッター,
"If Icon is set, it will be shown instead of Label",アイコンが設定されている場合、ラベルの代わりに表示されます,
Apply Document Permissions,ドキュメントのアクセス許可を適用する,
"For help see <a href=""https://frappeframework.com/docs/user/en/guides/portal-development/web-forms"" target=""_blank"">Client Script API and Examples</a>","ヘルプについては、<a href=""https://frappeframework.com/docs/user/en/guides/portal-development/web-forms"" target=""_blank"">クライアントスクリプトAPIと例</a>を参照してください",
Dynamic Route,ダイナミックルート,
Map route parameters into form variables. Example <code>/project/&lt;name&gt;</code>,ルートパラメータをフォーム変数にマップします。例<code>/project/&lt;name&gt;</code>,
Context Script,コンテキストスクリプト,
"<p>Set context before rendering a template. Example:</p><p>\n</p><div><pre><code>\ncontext.project = frappe.get_doc(""Project"", frappe.form_dict.name)\n</code></pre></div>","<p>テンプレートをレンダリングする前にコンテキストを設定します。例：</p><p></p><div><pre> <code>context.project = frappe.get_doc(&quot;Project&quot;, frappe.form_dict.name)</code></pre></div>",
Title of the page,ページのタイトル,
This title will be used as the title of the webpage as well as in meta tags,このタイトルは、メタタグだけでなくWebページのタイトルとしても使用されます。,
Makes the page public,ページを公開します,
Checking this will publish the page on your website and it'll be visible to everyone.,これをチェックすると、ページがWebサイトに公開され、すべての人に表示されます。,
URL of the page,ページのURL,
"This will be automatically generated when you publish the page, you can also enter a route yourself if you wish",これは、ページを公開すると自動的に生成されます。必要に応じて、自分でルートを入力することもできます。,
Content type for building the page,ページを構築するためのコンテンツタイプ,
"You can select one from the following,",以下から1つ選択できます。,
Standard rich text editor with controls,コントロール付きの標準リッチテキストエディタ,
Github flavoured markdown syntax,Githubフレーバーのマークダウン構文,
HTML with jinja support,jinjaをサポートするHTML,
Frappe page builder using components,コンポーネントを使用したFrappeページビルダー,
Checking this will show a text area where you can write custom javascript that will run on this page.,これをチェックすると、このページで実行されるカスタムJavaScriptを記述できるテキスト領域が表示されます。,
Meta title for SEO,SEOのメタタイトル,
"By default the title is used as meta title, adding a value here will override it.",デフォルトでは、タイトルはメタタイトルとして使用されます。ここに値を追加すると、そのタイトルが上書きされます。,
"The meta description is an HTML attribute that provides a brief summary of a web page. Search engines such as Google often display the meta description in search results, which can influence click-through rates.",メタディスクリプションは、Webページの簡単な要約を提供するHTML属性です。 Googleなどの検索エンジンは、検索結果にメタディスクリプションを表示することが多く、クリック率に影響を与える可能性があります。,
"The meta image is unique image representing the content of the page. Images for this Card should be at least 280px in width, and at least 150px in height.",メタ画像は、ページのコンテンツを表す一意の画像です。このカードの画像は、幅が280ピクセル以上、高さが150ピクセル以上である必要があります。,
Add Space on Top,上にスペースを追加,
Add Space on Bottom,下部にスペースを追加,
Is Unique,ユニークです,
User Agent,ユーザーエージェント,
Table Break,テーブルブレイク,
Hide Login,ログインを隠す,
Navbar Template,ナビゲーションバーテンプレート,
Navbar Template Values,ナビゲーションバーテンプレート値,
Call To Action,アクションの呼び出し,
Call To Action URL,アクションへの呼び出しURL,
Footer Logo,フッターロゴ,
Footer Template,フッターテンプレート,
Footer Template Values,フッターテンプレート値,
Enable Tracking Page Views,ページビューの追跡を有効にする,
"Checking this will enable tracking page views for blogs, web pages, etc.",これをチェックすると、ブログやWebページなどのページビューを追跡できるようになります。,
Disable Signup for your site,サイトのサインアップを無効にする,
Check this if you don't want users to sign up for an account on your site. Users won't get desk access unless you explicitly provide it.,ユーザーにサイトのアカウントにサインアップさせたくない場合は、これをチェックしてください。明示的に指定しない限り、ユーザーはデスクにアクセスできません。,
URL to go to on clicking the slideshow image,スライドショー画像をクリックして移動するURL,
Custom Overrides,カスタムオーバーライド,
Ignored Apps,無視されたアプリ,
Include Theme from Apps,アプリのテーマを含める,
Website Theme Ignore App,ウェブサイトのテーマはアプリを無視します,
Are you sure you want to save this document?,このドキュメントを保存してもよろしいですか？,
Refresh All,すべて更新,
"Level 0 is for document level permissions, higher levels for field level permissions.",レベル0はドキュメントレベルの権限用であり、より高いレベルはフィールドレベルの権限用です。,
Website Analytics,ウェブサイト分析,
d,d,Days (Field: Duration)
h,h,Hours (Field: Duration)
m,m,Minutes (Field: Duration)
s,s,Seconds (Field: Duration)
Less,もっと少なく,
Not a valid DocType view:,有効なDocTypeビューではありません：,
Unknown View,不明なビュー,
Go Back,戻る,
Let's take you back to onboarding,オンボーディングに戻りましょう,
Great Job,よくやった,
Looks Great,素晴らしく見える,
Looks like you didn't change the value,値を変更していないようです,
Oops,おっと,
Skip Step,ステップをスキップ,
"You're doing great, let's take you back to the onboarding page.",順調です。オンボーディングページに戻りましょう。,
Good Work 🎉,グッドワーク🎉,
Submit this document to complete this step.,この手順を完了するには、このドキュメントを送信してください。,
Great,すごい,
You may continue with onboarding,オンボーディングを続行できます,
You seem good to go!,行ってよかった！,
Onboarding Complete,オンボーディング完了,
{0} Settings,{0}設定,
{0} Fields,{0}フィールド,
Reset Fields,フィールドのリセット,
Select Fields,フィールドを選択,
Warning: Unable to find {0} in any table related to {1},警告：{1}に関連するテーブルで{0}が見つかりません,
Tree view is not available for {0},ツリービューは{0}では使用できません,
Create Card,カードを作成する,
Card Label,カードラベル,
Reports already in Queue,すでにキューにあるレポート,
Proceed Anyway,とにかく続行,
Delete and Generate New,新規の削除と生成,
1 Report,1レポート,
{0} ({1}) (1 row mandatory),{0}（{1}）（1行必須）,
Select Fields To Insert,挿入するフィールドを選択します,
Select Fields To Update,更新するフィールドを選択します,
"This document is already amended, you cannot ammend it again",このドキュメントはすでに修正されています。再度修正することはできません。,
Add to ToDo,ToDoに追加,
{0} is currently {1},{0}は現在{1}です,
{0} are currently {1},{0}は現在{1}です,
Currently Replying,現在返信中,
created {0},作成された{0},
Change,変化する,Coins
Too Many Requests,リクエストが多すぎます,
"Invalid Authorization headers, add a token with a prefix from one of the following: {0}.",無効な承認ヘッダー。次のいずれかのプレフィックスを持つトークンを追加します：{0}。,
"Invalid Authorization Type {0}, must be one of {1}.",無効な認証タイプ{0}、{1}のいずれかである必要があります。,
{} is not a valid date string.,{}は有効な日付文字列ではありません。,
Invalid Date,無効な日付,
Please select a valid date filter,有効な日付フィルターを選択してください,
Value {0} must be in the valid duration format: d h m s,値{0}は有効な期間形式である必要があります：dhms,
Google Sheets URL is invalid or not publicly accessible.,GoogleスプレッドシートのURLが無効であるか、一般公開されていません。,
"Google Sheets URL must end with ""gid={number}"". Copy and paste the URL from the browser address bar and try again.",GoogleスプレッドシートのURLは「gid = {number}」で終わる必要があります。ブラウザのアドレスバーからURLをコピーして貼り付け、再試行してください。,
Incorrect URL,間違ったURL,
"""{0}"" is not a valid Google Sheets URL",&quot;{0}&quot;は有効なGoogleスプレッドシートのURLではありません,
Duplicate Name,重複する名前,
"Please check the value of ""Fetch From"" set for field {0}",フィールド{0}に設定されている「FetchFrom」の値を確認してください,
Wrong Fetch From value,値からのフェッチが間違っています,
A field with the name '{}' already exists in doctype {}.,&#39;{}&#39;という名前のフィールドは、doctype {}にすでに存在します。,
Custom Field {0} is created by the Administrator and can only be deleted through the Administrator account.,カスタムフィールド{0}は管理者によって作成され、管理者アカウントを介してのみ削除できます。,
Failed to send {0} Auto Email Report,{0}自動メールレポートの送信に失敗しました,
Test email sent to {0},{0}に送信されたテストメール,
Email queued to {0} recipients,{0}の受信者にキューに入れられた電子メール,
Newsletter should have at least one recipient,ニュースレターには少なくとも1人の受信者が必要です,
Please enable Twilio settings to send WhatsApp messages,WhatsAppメッセージを送信するには、Twilio設定を有効にしてください,
"Not allowed to attach {0} document, please enable Allow Print For {0} in Print Settings",{0}ドキュメントの添付は許可されていません。印刷設定で[{0}の印刷を許可]を有効にしてください,
Signup Disabled,サインアップが無効,
Signups have been disabled for this website.,このウェブサイトへの登録は無効になっています。,
Open Document,ドキュメントを開く,
The comment cannot be empty,コメントを空にすることはできません,
Hourly comment limit reached for: {0},1時間あたりのコメント制限に達しました：{0},
Please add a valid comment.,有効なコメントを追加してください。,
Document {0} Already Restored,ドキュメント{0}はすでに復元されています,
Restoring Deleted Document,削除されたドキュメントの復元,
{function} of {fieldlabel},{fieldlabel}の{function},
Invalid template file for import,インポート用の無効なテンプレートファイル,
Invalid or corrupted content for import,インポートするコンテンツが無効または破損しています,
Value {0} must in {1} format,値{0}は{1}形式である必要があります,
{0} is a mandatory field asdadsf,{0}は必須フィールドasdadsfです,
Could not map column {0} to field {1},列{0}をフィールド{1}にマップできませんでした,
Skipping Duplicate Column {0},重複する列をスキップする{0},
The column {0} has {1} different date formats. Automatically setting {2} as the default format as it is the most common. Please change other values in this column to this format.,列{0}には{1}の異なる日付形式があります。最も一般的なデフォルト形式として{2}を自動的に設定します。この列の他の値をこの形式に変更してください。,
You have reached the hourly limit for generating password reset links. Please try again later.,パスワードリセットリンクを生成するための1時間あたりの制限に達しました。後でもう一度やり直してください。,
Please hide the standard navbar items instead of deleting them,標準のナビゲーションバーアイテムを削除するのではなく非表示にしてください,
DocType's name should not start or end with whitespace,DocTypeの名前は空白で開始または終了しないでください,
File name cannot have {0},ファイル名に{0}を含めることはできません,
{0} is not a valid file url,{0}は有効なファイルURLではありません,
Error Attaching File,ファイルの添付中にエラーが発生しました,
Please generate keys for the Event Subscriber User {0} first.,最初にイベントサブスクライバーユーザー{0}のキーを生成してください。,
Please set API Key and Secret on the producer and consumer sites first.,最初にプロデューサーサイトとコンシューマーサイトでAPIキーとシークレットを設定してください。,
User {0} not found on the producer site,プロデューサーサイトにユーザー{0}が見つかりません,
Event Subscriber has to be a System Manager.,イベントサブスクライバーはシステムマネージャーである必要があります。,
Row #{0}: Invalid Local Fieldname,行＃{0}：無効なローカルフィールド名,
Row #{0}: Please set Mapping or Default Value for the field {1} since its a dependency field,行＃{0}：依存フィールドであるため、フィールド{1}にマッピングまたはデフォルト値を設定してください,
Row #{0}: Please set remote value filters for the field {1} to fetch the unique remote dependency document,行＃{0}：フィールド{1}にリモート値フィルターを設定して、一意のリモート依存関係ドキュメントをフェッチしてください,
Paytm payment gateway settings,Paytmペイメントゲートウェイの設定,
"Company, Fiscal Year and Currency defaults",会社、会計年度、通貨のデフォルト,
Razorpay Signature Verification Failed,Razorpay署名の検証に失敗しました,
Google Drive - Could not locate - {0},Googleドライブ-見つかりませんでした-{0},
"Sync token was invalid and has been resetted, Retry syncing.",同期トークンが無効でリセットされました。同期を再試行してください。,
Please select another payment method. Paytm does not support transactions in currency '{0}',別のお支払い方法を選択してください。 Paytmは通貨「{0}」での取引をサポートしていません,
Invalid Account SID or Auth Token.,アカウントSIDまたは認証トークンが無効です。,
Please enable twilio settings before sending WhatsApp messages,WhatsAppメッセージを送信する前に、twilio設定を有効にしてください,
Delivery Failed,配達に失敗しました,
Twilio WhatsApp Message Error,TwilioWhatsAppメッセージエラー,
A featured post must have a cover image,注目の投稿にはカバー画像が必要です,
Load More,もっと読み込む,
Published on,に公開,
Enable developer mode to create a standard Web Template,開発者モードを有効にして、標準のWebテンプレートを作成します,
Was this article helpful?,この記事は役に立ちましたか？,
Thank you for your feedback!,ご意見ありがとうございます！,
New Mention on {0},{0}の新しい言及,
Assignment Update on {0},{0}の割り当ての更新,
New Document Shared {0},新しいドキュメント共有{0},
Energy Point Update on {0},{0}のエネルギーポイントの更新,
You cannot create a dashboard chart from single DocTypes,単一のDocTypeからダッシュボードチャートを作成することはできません,
Invalid json added in the custom options: {0},カスタムオプションに無効なjsonが追加されました：{0},
Invalid JSON in card links for {0},{0}のカードリンクのJSONが無効です,
Standard Not Set,標準が設定されていません,
Please set the following documents in this Dashboard as standard first.,まず、このダッシュボードで以下のドキュメントを標準として設定してください。,
Shared with the following Users with Read access:{0},読み取りアクセス権を持つ次のユーザーと共有：{0},
Already in the following Users ToDo list:{0},すでに次のユーザーのToDoリストにあります：{0},
Your assignment on {0} {1} has been removed by {2},{0} {1}の割り当ては{2}によって削除されました,
Invalid Credentials,無効な資格情報,
Print UOM after Quantity,数量の後に単位を印刷する,
Uncaught Server Exception,キャッチされないサーバーの例外,
There was an error building this page,このページの作成中にエラーが発生しました,
Hide Traceback,トレースバックを非表示,
Value from this field will be set as the due date in the ToDo,このフィールドの値は、ToDoの期日として設定されます,
New module created {0},新しいモジュールが作成されました{0},
"Report has no numeric fields, please change the Report Name",レポートに数値フィールドがありません。レポート名を変更してください,
There are documents which have workflow states that do not exist in this Workflow. It is recommended that you add these states to the Workflow and change their states before removing these states.,このワークフローに存在しないワークフロー状態を持つドキュメントがあります。これらの状態を削除する前に、これらの状態をワークフローに追加し、状態を変更することをお勧めします。,
Worflow States Don't Exist,ワーフロー状態は存在しません,
Save Anyway,とにかく保存,
Energy Points:,エネルギーポイント：,
Review Points:,レビューポイント：,
Rank:,ランク：,
Monthly Rank:,月間ランク：,
Invalid expression set in filter {0} ({1}),フィルタ{0}（{1}）に無効な式が設定されています,
Invalid expression set in filter {0},フィルタ{0}に無効な式が設定されています,
{0} {1} added to Dashboard {2},{0} {1}がダッシュボードに追加されました{2},
Set Filters for {0},{0}のフィルターを設定する,
Not permitted to view {0},{0}の表示は許可されていません,
Camera,カメラ,
Invalid filter: {0},無効なフィルター：{0},
Let's Get Started,始めましょう,
Reports & Masters,レポート＆マスター,
New {0} {1} added to Dashboard {2},ダッシュボード{2}に追加された新しい{0} {1},
New {0} {1} created,新しい{0} {1}が作成されました,
New {0} Created,新規{0}が作成されました,
"Invalid ""depends_on"" expression set in filter {0}",フィルタ{0}に設定された無効な「depends_on」式,
{0} Reports,{0}レポート,
There is {0} with the same filters already in the queue:,同じフィルターを持つ{0}がすでにキューにあります：,
There are {0} with the same filters already in the queue:,同じフィルターを持つ{0}がすでにキューにあります：,
Are you sure you want to generate a new report?,新しいレポートを生成してもよろしいですか？,
{0}: {1} vs {2},{0}：{1}対{2},
Add a {0} Chart,{0}チャートを追加する,
Currently you have {0} review points,現在、{0}件のレビューポイントがあります,
{0} is not a valid DocType for Dynamic Link,{0}は動的リンクの有効なDocTypeではありません,
via Assignment Rule,割り当てルールを介して,
Based on Field,フィールドに基づく,
Assign to the user set in this field,このフィールドで設定したユーザーに割り当てます,
Log Setting User,ログ設定ユーザー,
Log Settings,ログ設定,
Error Log Notification,エラーログ通知,
Users To Notify,通知するユーザー,
Log Cleanup,ログのクリーンアップ,
Clear Error log After,後でエラーログをクリアする,
Clear Activity Log After,後でアクティビティログをクリアする,
Clear Email Queue After,後にメールキューをクリアする,
Please save to edit the template.,テンプレートを編集するには保存してください。,
Google Analytics Anonymize IP,GoogleAnalyticsはIPを匿名化します,
Incorrect email or password. Please check your login credentials.,メールアドレスまたはパスワードが正しくありません。ログイン資格情報を確認してください。,
Incorrect Configuration,正しくない構成,
You are not allowed to delete Standard Report,標準レポートを削除することはできません,
You have unseen {0},見えない{0}があります,
Log cleanup and notification configuration,ログのクリーンアップと通知の構成,
State/Province,州/県,
Document Actions,アクションの文書化,
Document Links,ドキュメントリンク,
List Settings,リスト設定,
Cannot delete standard link. You can hide it if you want,標準リンクは削除できません。必要に応じて非表示にできます,
Cannot delete standard action. You can hide it if you want,標準アクションは削除できません。必要に応じて非表示にできます,
Applied On,適用日,
Row Name,行名,
For DocType Link / DocType Action,DocTypeリンク/ DocTypeアクションの場合,
Cannot edit filters for standard charts,標準チャートのフィルターは編集できません,
Event Producer Last Update,イベントプロデューサー最終更新,
Default for 'Check' type of field {0} must be either '0' or '1',フィールド{0}の「チェック」タイプのデフォルトは「0」または「1」のいずれかでなければなりません,
Non Negative,非負,
Rules with higher priority number will be applied first.,優先順位の高いルールが最初に適用されます。,
Open URL in a New Tab,新しいタブでURLを開く,
Align Right,右揃え,
Loading Filters...,フィルタを読み込んでいます。,
Count Customizations,カウントのカスタマイズ,
For Example: {} Open,例：{}開く,
Choose Existing Card or create New Card,既存のカードを選択するか、新しいカードを作成します,
Number Cards,ナンバーカード,
Function Based On,に基づく機能,
Add Filters,フィルタを追加する,
Skip,スキップ,
Dismiss,退出させる,
Value cannot be negative for,の値を負にすることはできません,
Value cannot be negative for {0}: {1},{0}の値を負にすることはできません：{1},
Negative Value,負の値,
Authentication failed while receiving emails from Email Account: {0}.,電子メールアカウントからの電子メールの受信中に認証に失敗しました：{0}。,
Message from server: {0},サーバーからのメッセージ：{0},
Add Row,行の追加,
Analytics,分析,
Anonymous,匿名,
Author,著者,
Basic,基本,
Billing,請求,
Contact Details,連絡先の詳細,
Datetime,日時,
Enable,有効にする,
Event,イベント,
Full,フル,
Insert,挿入,
Interests,興味,
Language Name,言語名,
License,運転免許,
Limit,リミット,
Log,ログ,
Meeting,会議,
My Account,自分のアカウント,
Newsletters,ニュースレター,
Password,パスワード,
Pincode,郵便番号,
Please select prefix first,接頭辞を選択してください,
Please set Email Address,メールアドレスを設定してください,
Please set the series to be used.,使用するシリーズを設定してください。,
Portal Settings,ポータル設定,
Reference Owner,参照オーナー,
Region,地域,
Report Builder,レポートビルダ,
Sample,サンプル,
Saved,保存済,
Series {0} already used in {1},シリーズは、{0}はすでに{1}で使用されています,
Set as Default,デフォルトに設定,
Shipping,出荷,
Standard,標準,
Test,テスト,
Traceback,トレースバック,
Unable to find DocType {0},DocType {0}を見つけることができません,
Weekdays,平日,
Workflow,ワークフロー,
You need to be logged in to access this page,このページにアクセスするにはログインする必要があります,
County,郡,
Images,画像,
Office,事務所,
Passive,消極的,
Permanent,恒久的な,
Plant,プラント,
Postal,郵便,
Previous,前,
Shop,店,
Subsidiary,子会社,
There is some problem with the file url: {0},ファイルURLに問題があります：{0},
"Special Characters except '-', '#', '.', '/', '{{' and '}}' not allowed in naming series {0}",&quot; - &quot;、 &quot;＃&quot;、 &quot;。&quot;、 &quot;/&quot;、 &quot;{{&quot;、および &quot;}}&quot;以外の特殊文字は、一連の名前付けでは使用できません {0},
Export Type,輸出タイプ,
Last Sync On,最後の同期オン,
Webhook Secret,Webhookシークレット,
Back to Home,家に帰る,
Customize,カスタマイズ,
Edit Profile,プロフィール編集,
File Manager,ファイルマネージャー,
Invite as User,ユーザーとして招待,
Newsletter,ニュースレター,
Printing,印刷,
Publish,公開,
Refreshing,リフレッシュ,
Select All,すべて選択,
Set,設定,
Setup Wizard,セットアップウィザード,
Update Details,更新の詳細,
You,あなた,
{0} Name,{0}の名前,
Bold,大胆な,
Center,中央,
Comment,コメント,
Not Found,見つかりません,
User Id,ユーザーID,
Position,ポジション,
Crop,作物,
Topic,トピック,
Public Transport,公共交通機関,
Request Data,リクエストデータ,
Steps,ステップ,
Reference DocType,参照DocType,
Select Transaction,取引を選択,
Help HTML,HTMLヘルプ,
Series List for this Transaction,この取引のシリーズ一覧,
User must always select,ユーザーは常に選択する必要があります,
Check this if you want to force the user to select a series before saving. There will be no default if you check this.,あなたが保存する前に、一連の選択をユーザに強制したい場合は、これを確認してください。これをチェックするとデフォルトはありません。,
Prefix,接頭辞,
This is the number of the last created transaction with this prefix,この接頭辞が付いた最新の取引番号です,
Update Series Number,シリーズ番号更新,
Validation Error,検証エラー,
Andaman and Nicobar Islands,アンダマンニコバル諸島,
Andhra Pradesh,アンドラプラデーシュ,
Arunachal Pradesh,アルナーチャルプラデーシュ州,
Assam,アッサム,
Bihar,ビハール,
Chandigarh,チャンディーガル,
Chhattisgarh,チャッティースガル,
Dadra and Nagar Haveli,ダドラとナガルハベリ,
Daman and Diu,ダマンディーウ,
Delhi,デリー,
Goa,行きます,
Gujarat,グジャラート,
Haryana,ハリヤナ,
Himachal Pradesh,ヒマーチャルプラデーシュ州,
Jammu and Kashmir,ジャンムー・カシミール,
Jharkhand,ジャールカンド,
Karnataka,カルナータカ,
Kerala,ケララ,
Lakshadweep Islands,ラクシャディープ諸島,
Madhya Pradesh,マディヤプラデーシュ,
Maharashtra,マハラシュトラ,
Manipur,マニプール,
Meghalaya,メガラヤ,
Mizoram,ミゾラム,
Nagaland,ナガランド,
Odisha,オリッサ,
Other Territory,その他の地域,
Pondicherry,ポンディシェリ,
Punjab,パンジャーブ,
Rajasthan,ラージャスターン,
Sikkim,シッキム,
Tamil Nadu,タミル・ナードゥ,
Telangana,テランガーナ,
Tripura,トリプラ,
Uttar Pradesh,ウッタルプラデーシュ,
Uttarakhand,ウッタラーカンド州,
West Bengal,西ベンガル,
Published on,に公開,
Bottom,下,
Top,上,
