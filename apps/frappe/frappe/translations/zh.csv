A4,A4,
<PERSON> Endpoint,应用程序界面端点,
API Key,应用程序界面密钥,
Access Token,访问令牌,
Account,科目,
Accounts Manager,会计经理,
Accounts User,会计人员,
Action,行动,
Actions,操作,
Active,活动,
Add,添加,
Add Row,添加行,
Address,地址,
Address Line 2,地址行2,
Address Title,地址名称,
Address Type,地址类型,
Administrator,管理员,
All Day,全日,
Allow Delete,允许删除,
Amended From,修订源,
Amount,金额,
Applicable For,适用于,
Approval Status,审批状态,
Assign,分配,
Assign To,分配给,
Attachment,附件,
Attachments,附件,
Author,作者,
Auto Repeat,自动重复,
Base URL,基本网址,
Based On,基于,
Beginner,初学者,
Billing,账单,
Cancel,取消,
Category,类别,
Category Name,分类名称,
City,城市,
City/Town,市/镇,
Client,客户,
Client ID,客户端ID,
Client Secret,客户秘密,
Closed,已关闭,
Code,代码,
Collapse All,全部收缩,
Color,颜色,
Company Name,公司名称,
Condition,条件,
Contact,联系人,
Contact Details,联系人信息,
Content,内容,
Content Type,内容类型,
Create,创建,
Created By,创建人,
Current,当前,
Custom HTML,自定义HTML,
Custom?,自定义？,
Date Format,日期格式,
Datetime,时间日期,
Day,天,
Default Letter Head,默认信头,
Defaults,默认,
Delivery Status,交货状态,
Department,部门,
Details,详细信息,
Document Name,文档名称,
Document Status,文档状态,
Document Type,文档类型,
Domain,领域,
Domains,域,
Draft,草案,
Edit,编辑,
Email Account,邮件帐户,
Email Address,电子邮箱,
Email ID,电子邮件ID,
Email Sent,邮件已发送,
Email Template,电子邮件模板,
Enable,启用,
Enabled,已启用,
End Date,结束日期,
Error Code: {0},错误代码:{0},
Error Log,错误日志,
Event,事件,
Expand All,展开全部,
Fail,失败,
Failed,失败,
Fax,传真,
Feedback,反馈,
Female,女,
Field Name,字段名称,
Fieldname,字段名,
Fields,字段,
First Name,名,
Frequency,频率,
Friday,星期五,
From,从,
Full,充分,
Full Name,全名,
Further nodes can be only created under 'Group' type nodes,只能在“组”节点下新建节点,
Gender,性别,
GitHub Sync ID,GitHub同步ID,
Guest,访客,
Half Day,半天,
Half Yearly,半年度,
High,高,
Hourly,每小时,
Hub Sync ID,集线器同步ID,
IP Address,IP地址,
Image,图像,
Image View,图像视图,
Import Data,导入数据,
Import Log,导入日志,
Inactive,非活动的,
Insert,插入,
Interests,兴趣,
Introduction,介绍,
Is Active,是活动的,
Is Completed,完成了,
Is Default,是否默认,
Kanban Board,看板,
Label,标签,
Language Name,语言名称,
Last Name,姓,
Leaderboard,管理者看板,
Letter Head,信头,
Level,级别,
Limit,限制,
Log,日志,
Logs,日志,
Low,低,
Maintenance Manager,维护经理,
Maintenance User,维护用户,
Male,男性,
Mandatory,强制性,
Mapping,映射,
Mapping Type,映射类型,
Medium,中,
Meeting,会议,
Message Examples,消息示例,
Method,方法,
Middle Name,中间名,
Middle Name (Optional),中间名（可选）,
Monday,星期一,
Monthly,每月,
More,更多,
More Information,更多信息,
Move,移动,
My Account,我的账户,
New Address,新地址,
New Contact,新建联系人,
Next,下一个,
No Data,无数据,
No address added yet.,未添加地址。,
No contacts added yet.,暂无联系人。,
No items found.,未找到任何项目。,
None,没有,
Not Permitted,不允许,
Not active,非活动,
Number,数,
Online,线上,
Operation,操作,
Options,选项,
Other,其他,
Owner,业主,
Page Missing or Moved,页面丢失或已被移动,
Parameter,参数,
Password,密码,
Period,期,
Pincode,PIN代码,
Please enable pop-ups,请启用弹出窗口,
Please select Company,请选择公司,
Please select {0},请选择{0},
Please set Email Address,请设置电子邮件地址,
Portal Settings,门户网站设置,
Preview,预览,
Primary,初级,
Print Format,打印格式,
Print Settings,打印设置,
Print taxes with zero amount,打印零金额的税,
Private,私人,
Property,属性,
Public,公,
Published,发布时间,
Purchase Manager,采购经理,
Purchase Master Manager,采购方面的主要经理,
Purchase User,购买用户,
Query Options,查询选项,
Range,范围,
Rating,评分,
Received,收到,
Recipients,收件人,
Redirect URL,URL重定向,
Reference,参考,
Reference Date,参考日期,
Reference Document,参考文献,
Reference Document Type,参考文档类型,
Reference Owner,参考者,
Reference Type,参考类型,
Refresh Token,刷新令牌,
Region,区域,
Rejected,拒绝,
Reopen,重新打开,
Replied,回答,
Report,报告,
Report Builder,报表生成器,
Report Type,报告类型,
Reports,报告,
Response,响应,
Role,角色,
Route,路线,
Sales Manager,销售经理,
Sales Master Manager,销售经理大师,
Sales User,销售用户,
Salutation,称呼,
Sample,样本,
Saturday,星期六,
Saved,已保存,
Scheduled,已计划,
Search,搜索,
Secret Key,密钥,
Select,选择,
Select DocType,选择文档类型,
Send Now,立即发送,
Sent,已发送,
Series {0} already used in {1},系列{0}已经被{1}使用,
Service,服务,
Set as Default,设置为默认,
Settings,设置,
Shipping,运输中,
Short Name,简称,
Slideshow,幻灯片,
Source,源,
Source Name,源名称,
Standard,标准,
Start Date,开始日期,
Start Import,开始导入,
State,州,
Stopped,已停止,
Subject,主题,
Submit,提交,
Summary,概要,
Sunday,星期天,
System Manager,系统管理员,
Target,目标,
Task,任务,
Tax Category,税种,
Test,测试,
Thank you,谢谢,
The page you are looking for is missing. This could be because it is moved or there is a typo in the link.,您正在访问的网页不存在。可能是网页被移到别处了或您输入的网址不正确。,
Timespan,时间跨度,
To,至,
To Date,至今,
Traceback,回溯,
URL,网址,
Unsubscribed,已退订,
User,用户,
User ID,用户ID,
Users,用户,
Validity,有效性,
Warning,警告,
Website,网站,
Website Manager,网站管理员,
Website Settings,网站设置,
Wednesday,星期三,
Week,周,
Weekdays,工作日,
Weekly,每周,
Welcome email sent,欢迎电子邮件已发送,
Workflow,工作流程,
You need to be logged in to access this page,您需要登录才能访问该页面,
old_parent,旧的_父系,
{0} is mandatory,{0}是必填项,
 to your browser,到你的浏览器,
"""Company History""",“公司历史”,
"""Parent"" signifies the parent table in which this row must be added",“上级”表示本条记录必须依赖添加的父表,
"""Team Members"" or ""Management""",“团队成员”或“管理”,
&lt;head&gt; HTML,&lt;HEAD&gt; HTML,
'In Global Search' not allowed for type {0} in row {1},行{1}中的类型{0}不允许“全局搜索”,
'In List View' not allowed for type {0} in row {1},行{1}中的类型{0}不允许选择“显示在列表视图中”,
'Recipients' not specified,&#39;收件人&#39;未指定,
(Ctrl + G),（按Ctrl + G）,
** Failed: {0} to {1}: {2},**失败：{0} {1}：{2},
**Currency** Master,** 货币**管理,
0 - Draft; 1 - Submitted; 2 - Cancelled,0 - 草案; 1 - 已提交; 2 - 已取消,
0 is highest,0是最高的,
1 Currency = [?] Fraction\nFor e.g. 1 USD = 100 Cent,1货币单位= [？]小额单位。例如1美元= 100美分,
1 comment,1条评论,
1 hour ago,1小时前,
1 minute ago,1分钟前,
1 month ago,一个月前,
1 year ago,一年前,
; not allowed in condition,不满足条件,
"<h4>Default Template</h4>\n<p>Uses <a href=""http://jinja.pocoo.org/docs/templates/"">Jinja Templating</a> and all the fields of Address (including Custom Fields if any) will be available</p>\n<pre><code>{{ address_line1 }}&lt;br&gt;\n{% if address_line2 %}{{ address_line2 }}&lt;br&gt;{% endif -%}\n{{ city }}&lt;br&gt;\n{% if state %}{{ state }}&lt;br&gt;{% endif -%}\n{% if pincode %} PIN:  {{ pincode }}&lt;br&gt;{% endif -%}\n{{ country }}&lt;br&gt;\n{% if phone %}Phone: {{ phone }}&lt;br&gt;{% endif -%}\n{% if fax %}Fax: {{ fax }}&lt;br&gt;{% endif -%}\n{% if email_id %}Email: {{ email_id }}&lt;br&gt;{% endif -%}\n</code></pre>","<h4>默认模板<!-- H4--> \n <p> &lt;a用途神社href=""http://jinja.pocoo.org/docs/templates/""&gt;模板和地址的所有领域（包括自定义字段如果有的话）将可<!-- P--> \n &lt;前&gt; &lt;代码&gt; {{address_line1}}＆LT; BR＆GT; \n {％，如果address_line2％} {{address_line2}}＆LT; BR＆GT; { ENDIF％ - ％} \n {{城市}}＆LT; BR＆GT; \n {％，如果状态％} {{状态}}＆LT; BR＆GT; {％ENDIF - ％} {\n％，如果PIN代码％} PIN：{{PIN码}}＆LT; BR＆GT; {％ENDIF - ％} \n {{国家}}＆LT; BR＆GT; \n {％，如果电话％}电话：{{电话}}＆LT; BR＆GT; { ％ENDIF - ％} \n {％，如果传真％}传真：{{传真}}＆LT; BR＆GT; {％ENDIF - ％} \n {％，如果email_id％}电子邮件：{{email_id}}＆LT; BR＆GT {％ENDIF - ％} \n &lt;/代码&gt; <!-- PRE--></p></h4>",
A Lead with this Email Address should exist,与此电子邮件地址相关的商机应存在,
A list of resources which the Client App will have access to after the user allows it.<br> e.g. project,资源的列表，它的客户端应用程序必须将用户允许后访问。 <br>如项目,
A log of request errors,错误请求的日志,
A new account has been created for you at {0},已为您创建新帐户{0},
A symbol for this currency. For e.g. $,货币符号。例如$,
A word by itself is easy to guess.,很容易猜到的一个字,
API Endpoint Args,应用程序界面端点参数,
API Key cannot be  regenerated,应用程序界面密钥无法重新生成,
API Password,应用程序界面密码,
API Secret,应用程序界面秘密,
API Username,应用程序界面用户名,
ASC,ASC码,
About Us Settings,关于我们设置,
About Us Team Member,关于我们 团队成员,
Accept Payment,接受支付,
Access Key ID,访问密钥ID,
Access Token URL,访问令牌链接地址,
Action Failed,操作失败,
Action Timeout (Seconds),动作超时（秒）,
"Actions for workflow (e.g. Approve, Cancel).",工作流操作（例如审批，取消） 。,
Active Domains,活动域,
Active Sessions,活动会话,
Activity Log,活动日志,
Activity log of all users.,所有用户的活动日志。,
Add / Manage Email Domains.,添加/管理电子邮件域。,
Add / Update,添加/更新,
Add A New Rule,新建规则,
Add Another Comment,添加另一个评论,
Add Attachment,添加附件,
Add Column,添加列,
Add Contact,增加联系人,
Add Contacts,添加联系人,
Add Filter,添加过滤器,
Add Group,添加组,
Add New Permission Rule,新建权限规则,
Add Review,添加评论,
Add Signature,添加签名,
Add Subscribers,添加订阅,
Add Total Row,添加总计行,
Add Unsubscribe Link,添加退订链接,
Add User Permissions,添加用户权限,
Add a New Role,新建角色,
Add a column,添加列,
Add a comment,添加评论,
Add a new section,添加新部分,
Add a tag ...,添加标签...,
Add all roles,添加所有角色,
Add custom forms.,添加自定义表单。,
Add custom javascript to forms.,为表单添加自定义的Java Script。,
Add fields to forms.,为表单添加字段。,
Add meta tags to your web pages,将元标记添加到您的网页,
Add script for Child Table,添加子表的脚本,
Add to table,添加到表格,
Add your own translations,添加您自己的翻译,
"Added HTML in the &lt;head&gt; section of the web page, primarily used for website verification and SEO",在&lt;head&gt;添加HTML网页的部分，主要用于网站的验证和搜索引擎优化,
Added {0},添加了{0},
Adding System Manager to this User as there must be atleast one System Manager,正在为此用户添加“系统管理员”角色，因为至少需要一个系统管理员,
Additional Permissions,额外的权限,
Address Template,地址模板,
Address Title is mandatory.,地址标题是必须项。,
Address and other legal information you may want to put in the footer.,地址和其他法律信息最好放进页脚。,
Addresses And Contacts,地址和联系方式,
Adds a client custom script to a DocType,将客户端自定义脚本添加到DocType,
Adds a custom field to a DocType,为文档类型添加一个自定义字段,
Admin,管理员,
Administrator Logged In,已登录管理员,
Administrator accessed {0} on {1} via IP Address {2}.,通过{0}在{1}通过IP地址{2}访问的管理员。,
Advanced,高级,
Advanced Control,高级控制,
Advanced Search,高级搜索,
Align Labels to the Right,将标签右对齐,
Align Value,对齐值,
All Images attached to Website Slideshow should be public,所有连接到网站幻灯片的图片应该是公开的,
All customizations will be removed. Please confirm.,所有自定义更改都将被删除，请确认,
"All possible Workflow States and roles of the workflow. Docstatus Options: 0 is""Saved"", 1 is ""Submitted"" and 2 is ""Cancelled""",所有可能的工作流状态和工作流程的作用。 文档状态选项：0是“已保存”/ 1是“已提交”/ 2是“取消”。,
All-uppercase is almost as easy to guess as all-lowercase.,全大写几乎一样容易猜到全部小写。,
Allocated To,分配给,
Allow,允许,
Allow Bulk Edit,允许批量修改,
Allow Comments,允许评论,
Allow Consecutive Login Attempts ,允许连续登录尝试,
Allow Dropbox Access,允许访问Dropbox,
Allow Edit,允许编辑,
Allow Guest to View,允许访客查看,
Allow Import (via Data Import Tool),允许导入（通过数据导入工具）,
Allow Incomplete Forms,允许不完整的表格,
Allow Login After Fail,允许在失败后登录,
Allow Login using Mobile Number,允许使用手机号登录,
Allow Login using User Name,允许使用用户名登录,
Allow Modules,允许模块,
Allow Multiple,允许多个,
Allow Print,允许打印,
Allow Print for Cancelled,允许打印为已取消,
Allow Print for Draft,允许打印草稿,
Allow Read On All Link Options,允许读取所有链接选项,
Allow Rename,允许重命名,
Allow Roles,允许角色,
Allow Self Approval,允许自我批准,
Allow approval for creator of the document,允许批准文档的创建者,
Allow events in timeline,允许时间轴中的事件,
Allow in Quick Entry,允许快速输入,
Allow on Submit,允许提交,
Allow only one session per user,允许每个用户只有一个会话,
Allow page break inside tables,允许在表格分页符,
Allow saving if mandatory fields are not filled,允许保存如果必填字段未填写,
Allow user to login only after this hour (0-24),仅允许用户在此时后登录（0-24时）,
Allow user to login only before this hour (0-24),仅允许用户在此时前登录（0-24时）,
Allowed,已允许,
Allowed In Mentions,允许被“提到”,
"Allowing DocType, DocType. Be careful!",允许的DOCTYPE，的DocType 。要小心！,
Already Registered,已注册,
Also adding the dependent currency field {0},还要添加从属货币字段{0},
"Always add ""Draft"" Heading for printing draft documents",随时添加“草案”标题打印文件草案,
Always use Account's Email Address as Sender,始终使用帐户的电子邮件地址作为发件人,
Always use Account's Name as Sender's Name,始终使用帐户名称作为发件人姓名,
Amend,修订,
Amending,修订,
Amount Based On Field,用量以现场,
Amount Field,金额字段,
Amount must be greater than 0.,量必须大于0。,
An error occured during the payment process. Please contact us.,付款过程中发生错误。请联系我们。,
An icon file with .ico extension. Should be 16 x 16 px. Generated using a favicon generator. [favicon-generator.org],一个图标文件扩展名为.ico。应为16×16像素。使用图标生成器生成。 [favicon-generator.org],
Ancestors Of,祖先,
Another transaction is blocking this one. Please try again in a few seconds.,另一个交易禁用这个。请几分钟后再试。,
"Another {0} with name {1} exists, select another name",具有相同名称{1}的{0}已存在，请更名。,
Any string-based printer languages can be used. Writing raw commands requires knowledge of the printer's native language provided by the printer manufacturer. Please refer to the developer manual provided by the printer manufacturer on how to write their native commands. These commands are rendered on the server side using the Jinja Templating Language.,可以使用任何基于字符串的打印机语言。编写原始命令需要了解打印机制造商提供的打印机本机语言。请参阅打印机制造商提供的开发人员手册，了解如何编写本机命令。这些命令使用Jinja模板语言在服务器端呈现。,
"Apart from System Manager, roles with Set User Permissions right can set permissions for other users for that Document Type.",除了系统管理员，有“设置用户权限”的角色可以为其他用户设置某文档类型的权限。,
Api Access,应用程序界面访问,
App,应用,
App Access Key,应用程序访问密钥,
App Client ID,应用程序客户端ID,
App Client Secret,应用程序客户端密钥,
App Name,应用程序名称,
App Secret Key,应用保密密钥,
App not found,应用程序未找到,
App {0} already installed,已安装App {0},
App {0} is not installed,未安装应用程序{0},
Append To,追加到,
Append To can be one of {0},追加到可以是{0}之一,
Append To is mandatory for incoming mails,传入邮件加到追加到是强制性的,
"Append as communication against this DocType (must have fields, ""Status"", ""Subject"")",作为此文档类型的交流信息添加(必须有“状态”和“主题”字段),
Applicable Document Types,适用的文件类型,
Apply,应用,
Apply Strict User Permissions,申请严格的用户权限,
Apply To All Document Types,适用于所有文档类型,
Apply this rule if the User is the Owner,应用此规则如果用户是所有者,
Apply to all Documents Types,适用于所有文档类型,
Appreciate,欣赏,
Appreciation,升值,
Archive,档案,
Archived,已存档,
Archived Columns,归档列,
Are you sure you want to delete the attachment?,您确定要删除的附件？,
Are you sure you want to relink this communication to {0}?,您确定要重新链接该通信{0}？,
Are you sure?,你确定吗？,
Arial,Arial字体,
"As a best practice, do not assign the same set of permission rule to different Roles. Instead, set multiple Roles to the same User.",一种最佳的做法是，不要给不同的角色分配同样的权限规则，而是向同一用户分配多个角色。,
Assign Condition,分配条件,
Assign To Users,分配给用户,
"Assign one by one, in sequence",按顺序逐个分配,
Assign to me,分配给我,
Assign to the one who has the least assignments,分配给分配最少的人,
Assigned,已分配,
Assigned By,通过分配,
Assigned By Full Name,分配者全名,
Assigned By Me,由我指定,
Assigned To,已分配给,
Assigned To/Owner,已分配给/所有者,
Assignment,分配,
Assignment Complete,指派完成,
Assignment Completed,指派完成,
Assignment Rule,作业规则,
Assignment Rule User,分配规则用户,
Assignment Rules,作业规则,
Assignment closed by {0},指配由{0}关闭,
Assignment for {0} {1},{0} {1}的分配,
Atleast one field of Parent Document Type is mandatory,父文件类型的至少一个字段是强制性的,
Attach,附件,
Attach Document Print,附加文档打印,
Attach Image,附加图片,
Attach Print,附加打印,
Attach Your Picture,附上你的照片,
Attach file for Import,附加文件进行导入,
Attach files / urls and add in table.,附加文件/网址并添加到表格中。,
Attached To DocType,已附加到文档类型,
Attached To Field,已附加到字段,
Attached To Name,已附加到名称,
Attachment Limit (MB),附件限制(MB),
Attachment Removed,附件已删除,
Attempting Connection to QZ Tray...,尝试连接QZ托盘......,
Attempting to launch QZ Tray...,试图推出QZ Tray ......,
Auth URL Data,身份验证URL数据,
Authenticating...,验证...,
Authentication,认证,
Authentication Apps you can use are: ,您可以使用的验证应用程序是：,
Authentication Credentials,认证凭据,
Authorization Code,授权码,
Authorize URL,授权URL,
Authorized,合法,
Auto,自动,
Auto Email Report,自动邮件报告,
Auto Name,自动名称,
Auto Reply Message,自动回复留言,
Auto assignment failed: {0},自动分配失败：{0},
Automatically Assign Documents to Users,自动为用户分配文档,
Automation,自动化,
Avatar,头像,
Average,平均,
Average of {0},平均值{0},
Avoid dates and years that are associated with you.,避免日期和与你相关的年。,
Avoid recent years.,避免最近几年。,
Avoid sequences like abc or 6543 as they are easy to guess,避免像ABC或6543的序列，因为它们容易被猜中,
Avoid years that are associated with you.,避免了与你相关的年。,
Awaiting Password,等待密码,
Away,远,
BCC,BCC,
Back to Desk,返回主页,
Back to Login,返回登陆,
Background Color,背景颜色,
Background Email Queue,后台电子邮件队列,
Background Jobs,后台作业,
Background Workers,后台进程,
Backup,备份,
Backup Frequency,备份频率,
Backup Limit,备份限制,
Backup job is already queued. You will receive an email with the download link,备份作业已经排队。您将收到一封包含下载链接的电子邮件,
Backups,备份,
Banner,横幅,
Banner HTML,横幅HTML,
Banner Image,横幅图片,
Banner is above the Top Menu Bar.,横幅在顶部菜单上方。,
Bar,酒吧,
Base Distinguished Name (DN),基本专有名称（DN）,
Based on Permissions For User,基于授予用户的许可,
Beta,Beta版,
Better add a few more letters or another word,最好加几个字母或一个字,
Between,之间,
Bio,个人简历,
Birth Date,出生日期,
Block Module,封锁模块,
Block Modules,封锁模块,
Blocked,已限制,
Blog,博客,
Blog Category,博客分类,
Blog Intro,博客介绍,
Blog Introduction,博客介绍,
Blog Post,博客文章,
Blog Settings,博客设置,
Blog Title,博客标题,
Blogger,博客作者,
Bot,聊天机器人,
Both DocType and Name required,文档类型和名称是必须项,
Both login and password required,登录名和密码是必须项,
Bounced,退回,
Braintree Settings,Braintree设置,
Braintree payment gateway settings,Braintree支付网关设置,
Brand HTML,品牌HTML,
Brand Image,品牌形象,
Breadcrumbs,面包屑导航,
Browser not supported,浏览器不支持,
Brute Force Security,蛮力安全,
Build Report,生成报告,
Bulk Delete,批量删除,
Bulk Edit {0},批量编辑{0},
Bulk Rename,批量重命名,
Bulk Update,批量更新,
Button,按钮,
Button Help,按钮帮助,
Button Label,按钮标签,
Bypass Two Factor Auth for users who login from restricted IP Address,为从受限IP地址登录的用户绕过双因素身份验证,
Bypass restricted IP Address check If Two Factor Auth Enabled,绕过受限制的IP地址检查如果双因素验证启用,
CC,抄送,
"CC, BCC & Email Template",抄送，密件抄送＆电子邮件模板,
CSS,CSS,
CSV,CSV,
Cache Cleared,缓存已清除,
Calculate,计算,
Calendar Name,日历名称,
Calendar View,日历视图,
Call,通话,
Can Read,可阅读,
Can Share,可以共享,
Can Write,可以写,
Can't identify open {0}. Try something else.,无法识别开{0}。尝试别的东西。,
Can't save the form as data import is in progress.,数据导入过程中无法保存表单。,
Cancel {0} documents?,取消{0}文件？,
Cancelled Document restored as Draft,已取消的文档已恢复为草稿,
Cancelling,取消,
Cancelling {0},取消{0},
Cannot Remove,无法删除,
Cannot cancel before submitting. See Transition {0},不能在提交前取消，详情参考过渡{0},
Cannot change docstatus from 0 to 2,不能将文档状态由0改为2,
Cannot change docstatus from 1 to 0,不能将文档状态由1改为0,
Cannot change header content,无法更改标题内容,
Cannot change state of Cancelled Document. Transition row {0},不能改变已取消文档的状态。过渡行{0},
Cannot change user details in demo. Please signup for a new account at https://erpnext.com,演示中无法更改用户详细信息。请在https://erpnext.com注册一个新帐户,
Cannot create a {0} against a child document: {1},无法针对子文件创建{0}：{1},
Cannot delete Home and Attachments folders,无法删除主和附件文件夹,
Cannot delete file as it belongs to {0} {1} for which you do not have permissions,无法删除属于您没有权限的{0} {1}的文件,
Cannot delete or cancel because {0} {1} is linked with {2} {3} {4},由于{0} {1}与{2} {3} {4}链接，无法删除或取消,
Cannot delete standard field. You can hide it if you want,无法删除标准的字段。你可以将其隐藏，如果你想,
Cannot delete {0},无法删除{0},
Cannot delete {0} as it has child nodes,无法删除{0} ，因为它有子节点,
"Cannot edit Standard Notification. To edit, please disable this and duplicate it",无法编辑标准通知。要进行编辑，请禁用并复制它,
Cannot edit a standard report. Please duplicate and create a new report,不能编辑标准的报告。请复制并创建一个新的报告,
Cannot edit cancelled document,无法编辑已取消文档,
Cannot edit standard fields,不能编辑标准字段,
Cannot have multiple printers mapped to a single print format.,不能将多个打印机映射到单个打印格式。,
Cannot link cancelled document: {0},不能链接到已取消文件{0},
Cannot map because following condition fails: ,无法对应，因为以下条件失败：,
Cannot move row,不能移动排,
Cannot open instance when its {0} is open,无法打开实例时，它的{0}是打开的,
Cannot open {0} when its instance is open,无法打开{0} ，当它的实例已打开,
Cannot remove ID field,无法删除ID字段,
Cannot set Notification on Document Type {0},无法在文档类型{0}上设置通知,
Cannot update {0},无法更新{0},
Cannot use sub-query in order by,不能按。。。次序使用子查询,
Cannot {0} {1},无法{0} {1},
Capitalization doesn't help very much.,资本没有太大帮助。,
Card Details,卡详情,
Categorize blog posts.,分类博客文章。,
Category Description,类别说明,
Cent,一分钱,
"Certain documents, like an Invoice, should not be changed once final. The final state for such documents is called Submitted. You can restrict which roles can Submit.",某些文档，例如费用清单，一旦进入最终状态(即“已提交”)就不能再更改。你可以限制可以提交的角色。,
Chain Integrity,链完整性,
Chaining Hash,链接哈希,
Change Label (via Custom Translation),更改标签（通过自定义转换）,
Change Password,更改密码,
"Change field properties (hide, readonly, permission etc.)",更改字段属性（隐藏，只读，权限等）,
Channel,渠道,
Chart Name,图表名称,
Chart Options,图表选项,
Chart Source,图表来源,
Chart Type,图表类型,
Charts,图表,
Chat,聊天,
Chat Background,聊天背景,
Chat Message,聊天消息,
Chat Operators,聊天运营商,
Chat Profile,聊天档案,
Chat Profile for User {0} exists.,用户{0}的聊天配置文件存在。,
Chat Room,聊天室,
Chat Room Name,聊天室名称,
Chat Room User,聊天室用户,
Chat Token,聊天令牌,
Chat Type,聊天类型,
Chat messages and other notifications.,聊天信息和其他通知。,
Check,校验,
Check Request URL,检查请求URL,
"Check columns to select, drag to set order.",勾选列来选择，拖动列来排序。,
Check this if you are testing your payment using the Sandbox API,检查这个，如果你正在测试使用沙盒API付款,
Check this to pull emails from your mailbox,要从你的邮箱下载邮件请勾选此项,
Check which Documents are readable by a User,查看哪些文件用户可读到,
Checking one moment,检查一个时刻,
Checksum Version,校验版本,
Child Table Mapping,子表映射,
Child Tables are shown as a Grid in other DocTypes,子表在其他DocType中显示为网格,
Choose authentication method to be used by all users,选择所有用户使用的身份验证方法,
Clear Error Logs,清除错误日志,
Clear User Permissions,清除用户权限,
Clear all roles,清除所有角色,
"Clearing end date, as it cannot be in the past for published pages.",清除结束日期，因为发布的页面不能在过去。,
Click here to post bugs and suggestions,点击这里发布错误和建议,
Click here to verify,点击这里核实,
Click on the link below to complete your registration and set a new password,点击下面的链接完成注册，并设置新密码,
Click on the link below to download your data,点击下面的链接下载您的数据,
Click on the link below to verify your request,点击下面的链接验证您的请求,
Click table to edit,点击表格编辑,
Click to Set Filters,单击以设置过滤器,
Clicked,已点击,
Client Credentials,客户端凭证,
Client Information,客户信息,
Client Script,客户端脚本,
Client URLs,客户端网址,
Client side script extensions in Javascript,在JavaScript客户端脚本扩展,
Collapsible,可折叠,
Collapsible Depends On,可折叠取决于,
Column,列,
Column <b>{0}</b> already exist.,列<b>{0}</b>已经存在。,
Column Break,分栏符,
Column Labels:,列标签：,
Column Name,列名,
Column Name cannot be empty,列名不能为空,
Columns,列,
Columns based on,基于列,
Combination of Grant Type (<code>{0}</code>) and Response Type (<code>{1}</code>) not allowed,授予类型（ <code>{0}</code> ）和响应类型（ <code>{1}</code> ）的组合不允许,
Comment By,评论人,
Comment Email,评论电邮,
Comment Type,评论类型,
Comment can only be edited by the owner,评论只能由所有者进行编辑,
Commented on {0}: {1},评论时间{0}：{1},
Comments and Communications will be associated with this linked document,评论与通讯将与此链接的文档关联,
Comments cannot have links or email addresses,评论不能包含链接或电子邮件地址,
Common names and surnames are easy to guess.,常见名字和姓氏很容易被猜到。,
Communicated via {0} on {1}: {2},通过传达{0}在{1} {2},
Communication Type,通信类型,
Company History,公司历史,
Company Introduction,公司简介,
Compiled Successfully,编译成功,
Complete By,由...完成,
Complete Registration,完成注册,
Complete Setup,完成安装,
Compose Email,写邮件,
Condition Detail,状况细节,
Conditions,条件,
Configure Chart,配置图表,
Configure Charts,配置图表,
Confirm,确认,
Confirm Deletion of Data,确认删除数据,
Confirm Request,确认申请,
Confirm Your Email,确认您的电子邮件,
Confirmed,确认,
Connected to QZ Tray!,连接到QZ托盘！,
Connection Name,连接名称,
Connection Success,连接成功,
Connection lost. Some features might not work.,连接丢失。某些功能可能无法使用。,
Connector Name,连接器名称,
Connector Type,连接器类型,
Contact Us Settings,联系我们设置,
"Contact options, like ""Sales Query, Support Query"" etc each on a new line or separated by commas.",联系人选项，如“销售查询，支持查询”等每个新行或以逗号分隔。,
Contacts,往来,
Content (HTML),内容（HTML）,
Content (Markdown),内容（降价）,
Content Hash,内容哈希值,
Content web page.,内容的网页。,
Conversation Tones,对话语调,
Copyright,版权,
Core,核心,
Core DocTypes cannot be customized.,核心DocType无法自定义。,
Could not connect to outgoing email server,无法连接到外发邮件服务器,
Could not find {0},找不到{0},
Could not find {0} in {1},找不到{0} {1},
Could not identify {0},无法识别{0},
Count,计数,
Country Name,国家名称,
County,县,
Create Chart,创建图表,
Create New,创建新的,
Create Post,创建帖子,
Create User Email,创建用户电子邮件,
Create a New Format,创建新格式,
Create a new record,创建一个新记录,
Create a new {0},创建一个新的{0},
Create and Send Newsletters,创建和发送新闻邮件,
Create and manage newsletter,建立并管理实时通讯,
Created,创建,
Created Custom Field {0} in {1},在{1}创建了自定义字段{0},
Created On,创建于,
Criticism,批评,
Criticize,批评,
Ctrl + Down,按Ctrl +向下,
Ctrl + Up,按Ctrl +向上,
Ctrl+Enter to add comment,Ctrl + Enter以添加评论,
Currency Name,货币名称,
Currency Precision,货币精确度,
Current Mapping,当前映射,
Current Mapping Action,当前映射行为,
Current Mapping Delete Start,当前映射删除开始,
Current Mapping Start,当前映射开始,
Current Mapping Type,当前映射类型,
Currently Viewing,目前查看,
Currently updating {0},当前正在更新{0},
Custom,自定义,
Custom Base URL,自定义基准网址,
Custom CSS,自定义CSS,
Custom DocPerm,自DocPerm,
Custom Field,自定义字段,
Custom Fields can only be added to a standard DocType.,自定义字段只能添加到标准DocType。,
Custom Fields cannot be added to core DocTypes.,自定义字段无法添加到核心DocType。,
Custom Format,自定义格式,
Custom HTML Help,自定义HTML帮助,
Custom JS,自定义JS,
Custom Menu Items,自定义菜单项,
Custom Report,自定义报告,
Custom Reports,自定义报告,
Custom Role,自定义角色,
Custom Script,自定义脚本,
Custom Sidebar Menu,自定义工具栏菜单,
Custom Translations,翻译定制,
Customization,定制,
Customizations Reset,自定义重置,
Customizations for <b>{0}</b> exported to:<br>{1},<b>{0}的</b>自定义已导出到： <br> {1},
Customize Form,自定义表单,
Customize Form Field,自定义表单域,
"Customize Label, Print Hide, Default etc.",自定义标签，打印隐藏，默认值等。,
Customize...,定制...,
"Customized Formats for Printing, Email",自定义打印格式，电子邮件,
Customized HTML Templates for printing transactions.,定制HTML模板打印交易。,
Cut,剪切,
DESC,DESC,
Daily Event Digest is sent for Calendar Events where reminders are set.,如果设置了日历事件提醒会发送每日事件摘要。,
Danger,危险,
Dark Color,深色,
Dashboard Chart,仪表板图表,
Dashboard Chart Link,仪表板图表链接,
Dashboard Chart Source,仪表板图表来源,
Dashboard Name,仪表板名称,
Dashboards,仪表板,
Data,数据,
Data Export,数据导出,
Data Import,数据导入,
Data Import Template,数据导入模板,
Data Migration,数据迁移,
Data Migration Connector,数据迁移连接器,
Data Migration Mapping,数据迁移映射,
Data Migration Mapping Detail,数据迁移映射细节,
Data Migration Plan,数据迁移计划,
Data Migration Plan Mapping,数据迁移计划映射,
Data Migration Run,数据迁移运行,
Data missing in table,表内缺失数据,
Database Engine,数据库引擎,
Database Name,数据库名称,
Date and Number Format,日期和数字格式,
Date {0} must be in format: {1},日期{0}必须采用格式：{1},
Dates are often easy to guess.,日期往往容易被猜中。,
Day of Week,星期几,
Days After,天后,
Days Before,前几天,
Days Before or After,日前或后,
"Dear System Manager,",亲爱的系统管理经理，,
"Dear User,",亲爱的用户，,
Dear {0},亲爱的{0},
Default Address Template cannot be deleted,默认地址模板不能删除,
Default Inbox,默认收件箱,
Default Incoming,默认收入,
Default Outgoing,默认支出,
Default Print Format,默认打印格式,
Default Print Language,默认打印语言,
Default Redirect URI,默认重定向URI,
Default Role at Time of Signup,在注册时间默认角色,
Default Sending,默认发送账号,
Default Sending and Inbox,默认发送和收件账号,
Default Sort Field,默认排序字段,
Default Sort Order,默认排序顺序,
Default Value,默认值,
"Default: ""Contact Us""",默认：“联系我们”,
DefaultValue,默认值,
Define workflows for forms.,定义表单工作流。,
Defines actions on states and the next step and allowed roles.,定义针对状态的行动，下一步操作和允许的角色。,
Defines workflow states and rules for a document.,定义文档工作流状态和规则。,
Delayed,延迟,
Delete Data,删除数据,
Delete comment?,删除评论？,
Delete this record to allow sending to this email address,删除此记录允许发送此邮件地址,
Delete {0} items permanently?,删除{0}项目永久？,
Deleted,已删除,
Deleted DocType,删除的DocType,
Deleted Document,删除的文档,
Deleted Documents,已删除的文件,
Deleted Name,删除名称,
Deleting {0},删除{0},
Depends On,取决于,
Descendants Of,由... 派生,
Desk,主页（桌面）,
Desk Access,桌面访问,
Desktop Icon,桌面图标,
Desktop Icon already exists,桌面图标已经存在,
Developer,开发者,
Did not add,没有添加,
Did not cancel,没有取消,
Did not find {0} for {0} ({1}),没有为{0} 找到{0}( {1} ),
Did not remove,没有移除,
"Different ""States"" this document can exist in. Like ""Open"", ""Pending Approval"" etc.",这个文档允许有多种状态，例如“开放”，“等待审批”等。,
Direct,直接,
Direct room with {0} already exists.,已存在{0}的直接房间。,
Disable Auto Refresh,禁用自动刷新,
Disable Count,禁用计数,
Disable Customer Signup link in Login page,禁用登录页面的客户注册链接,
Disable Prepared Report,禁用准备报告,
Disable Report,禁用报告,
Disable SMTP server authentication,禁用SMTP服务器验证,
Disable Sidebar Stats,禁用补充工具栏统计信息,
Disable Signup,禁止注册,
Disable Standard Email Footer,禁用标准电子邮件页脚,
Discard,丢弃,
Display,显示,
Display Depends On,显示的内容取决于,
Do not allow user to change after set the first time,不允许用户首次设置后再更改,
Do not edit headers which are preset in the template,不要编辑模板中预设的标题,
Do not send Emails,不要发送电子邮件,
Doc Event,文件事件,
Doc Events,文件活动,
Doc Status,文档状态,
DocField,文档字段,
DocPerm,文档权限,
DocShare,文档分享,
DocType <b>{0}</b> provided for the field <b>{1}</b> must have atleast one Link field,为字段<b>{1</b> <b>}</b>提供的DocType <b>{0}</b>必须至少有一个Link字段,
DocType can not be merged,文档类型不能合并,
DocType can only be renamed by Administrator,的DocType只能由管理员进行重命名,
DocType is a Table / Form in the application.,文档类型在本程序里是一个表/表格。,
DocType must be Submittable for the selected Doc Event,DocType必须为所选Doc事件提交,
DocType on which this Workflow is applicable.,此工作流适用的文档类型。,
"DocType's name should start with a letter and it can only consist of letters, numbers, spaces and underscores",文件类型的名称应以字母开始，它只能由字母，数字，空格和下划线,
Doctype required,Doctype需要,
Document,文档,
Document Follow,文件关注,
Document Follow Notification,文件跟踪通知,
Document Queued,文档排队,
Document Restored,文件恢复,
Document Share Report,文档分享报告,
Document States,文档状态,
Document Type is not importable,凭证类型不可导入,
Document Type is not submittable,文档类型不可提交,
Document Type to Track,要跟踪的文档类型,
Document Types,文档类型,
Document can't saved.,文件无法保存。,
Document {0} has been set to state {1} by {2},文档{0}已被{2}设置为状态{1},
Documents,文档,
Documents assigned to you and by you.,分配给您和您的文档。,
Domain Settings,域设置,
Domains HTML,域名HTML,
"Don't HTML Encode HTML tags like &lt;script&gt; or just characters like &lt; or &gt;, as they could be intentionally used in this field",不要HTML编码的HTML标签，如&lt;SCRIPT&gt;或者就像字符&lt;或&gt;，因为他们可以在此字段中故意使用,
Don't Override Status,不要覆盖状态,
Don't create new records,不要创建新的记录,
Don't have an account? Sign up,还没有账号？注册,
"Don't know, ask 'help'",不知道，问&#39;帮助&#39;,
Download Data,下载数据,
Download Files Backup,下载文件备份,
Download Link,下载链接,
Download Report,下载报告,
Download Your Data,下载您的数据,
Download link for your backup will be emailed on the following email address: {0},备份的下载链接将被发送至以下电子邮件地址：{0},
Download with Data,下载数据,
Drag and Drop tool to build and customize Print Formats.,拖放工具可以用来创建和定制打印格式。,
Drag elements from the sidebar to add. Drag them back to trash.,从侧边栏拖动元素添加，将元素拖回可以删除。,
Dropbox Access Key,Dropbox的Key,
Dropbox Access Secret,Dropbox的密码,
Dropbox Access Token,Dropbox访问令牌,
Dropbox Settings,Dropbox的设置,
Dropbox Setup,Dropbox的设置,
Dropbox access is approved!,Dropbox的访问被批准！,
Dropbox backup settings,Dropbox的备份设置,
Duplicate Filter Name,重复的过滤器名称,
Dynamic Link,动态链接,
Dynamic Report Filters,动态报告过滤器,
ESC,退出,
Edit Auto Email Report Settings,修改电子邮件报告设置,
Edit Custom HTML,编辑自定义HTML,
Edit DocType,编辑的DocType,
Edit Filter,编辑过滤器,
Edit Format,编辑格式,
Edit HTML,编辑HTML,
Edit Heading,编辑标题,
Edit Properties,编辑属性,
Edit to add content,编辑以添加内容,
Edit {0},编辑{0},
Editable Grid,编辑网格,
Editing Row,编辑行,
Eg. smsgateway.com/api/send_sms.cgi,例如：smsgateway.com/API/send_sms.cgi,
Email Account Name,邮件帐户名称,
Email Account added multiple times,多次添加的电子邮件帐户,
Email Addresses,电子邮件地址,
Email Domain,电子邮件域名,
"Email Domain not configured for this account, Create one?",电子邮件域名未配置此帐户，创建一个吗？,
Email Flag Queue,电子邮件标志队列,
Email Footer Address,电子邮件地址页脚,
Email Group,电子邮件组,
Email Group List,电子邮件组列表,
Email Group Member,电子邮件组成员,
Email Login ID,电子邮件登录ID,
Email Queue,电子邮件队列,
Email Queue Recipient,电子邮件队列收件人,
Email Queue records.,电子邮件队列记录。,
Email Reply Help,电邮答复帮助,
Email Rule,电子邮件规则,
Email Server,电子邮件服务器,
Email Settings,邮件设置,
Email Signature,邮件签名,
Email Status,电子邮件状态,
Email Sync Option,电子邮件同步选项,
Email Templates for common queries.,常见查询的电子邮件模板。,
Email To,通过电子邮件发送给,
Email Unsubscribe,电子邮件退订,
Email has been marked as spam,电子邮件已被标记为垃圾邮件,
Email has been moved to trash,电子邮件已被移至垃圾桶,
Email not sent to {0} (unsubscribed / disabled),电子邮件不会被发送到{0}（退订/禁用）,
Email not verified with {0},电子邮件未通过{0}验证,
Emails are muted,邮件已被静音,
Emails will be sent with next possible workflow actions,电子邮件将与下一个可能的工作流操作一起发送,
Embed image slideshows in website pages.,将图片幻灯片嵌入网站页面。,
Enable / Disable Domains,启用/禁用域,
Enable Auto Reply,启用自动回复,
Enable Automatic Backup,启用自动备份,
Enable Chat,启用聊天,
Enable Comments,启用评论,
Enable Incoming,通过该邮箱接收邮件,
Enable Outgoing,通过该邮箱发送邮件,
Enable Password Policy,启用密码策略,
Enable Print Server,启用打印服务器,
Enable Raw Printing,启用原始打印,
Enable Report,启用报告,
Enable Scheduled Jobs,启用计划任务,
Enable Social Login,启用社交登录,
Enable Two Factor Auth,启用双因素认证,
Enabled email inbox for user {0},已为用户{0}启用电子邮件收件箱,
"Encryption key is invalid, Please check site_config.json",加密密钥无效，请检查site_config.json,
End Date Field,结束日期字段,
End Date cannot be before Start Date!,结束日期不能在开始日期之前！,
Endpoint URL,端点URL,
Energy Point Log,能量点日志,
Energy Point Rule,能量点规则,
Energy Point Settings,能量点设置,
Energy Points,能量点,
Enter Email Recipient(s),输入电子邮件收件人（S）,
Enter Form Type,输入表单类型,
"Enter default value fields (keys) and values. If you add multiple values for a field, the first one will be picked. These defaults are also used to set ""match"" permission rules. To see list of fields, go to ""Customize Form"".",输入默认值字段（键）和值。如果你为一个字段中添加多个值，第一个将有所回升。这些默认值也用于设置“匹配”权限规则。要查看字段列表，进入“自定义窗体”。,
Enter folder name,输入文件夹名称,
"Enter keys to enable login via Facebook, Google, GitHub.","输入登录信息以启用Facebook, Google或Github账号登录。",
Enter python module or select connector type,输入python模块或选择连接器类型,
"Enter static url parameters here (Eg. sender=ERPNext, username=ERPNext, password=1234 etc.)","请输入静态的URL参数(例如 sender=ERPNext, username=ERPNext, password=1234 etc.)",
Enter url parameter for message,请输入消息的URL参数,
Enter url parameter for receiver nos,请输入收件人编号的URL参数,
Enter your password,输入密码,
Entity Name,实体名称,
Equals,等号,
Error Message,错误信息,
Error Report,错误报告,
Error Snapshot,错误快照,
Error in Custom Script,错误自定义脚本,
Error in Notification,通知错误,
Error in Notification: {},通知错误：{},
Error while connecting to email account {0},连接到电子邮件帐户{0}时出错,
Error while evaluating Notification {0}. Please fix your template.,评估通知{0}时出错。请修复您的模板。,
Error: Document has been modified after you have opened it,错误：文档在你打开后已被修改,
Error: Value missing for {0}: {1},错误：{0}缺少值：{1},
Errors in Background Events,在后台活动错误,
Event Category,活动类别,
Event Participants,活动参与者,
Event Type,事件类型,
Event and other calendars.,事件和其他日历。,
Events in Today's Calendar,今日事件,
Everyone,所有人,
Example,例如,
Example Email Address,示例邮件地址,
Example: {{ subject }},示例：{{subject}},
Excel,Excel,
Exception,例外,
Exception Type,异常类型,
Execution Time: {0} sec,执行时间：{0}秒,
Expert,专家,
Expiration time,到期时间,
Expire Notification On,到期通知在,
Expires In,过期日期在,
Expiry time of QR Code Image Page,QR码图像页面的到期时间,
Export All {0} rows?,导出所有{0}行？,
Export Custom Permissions,导出客户许可,
Export Customizations,导出自定义,
Export Data,导出数据,
Export Data in CSV / Excel format.,以CSV / Excel格式导出数据。,
Export Report: {0},导出报告：{0},
Expose Recipients,揭露收件人,
"Expression, Optional",表达，可选,
Facebook,脸书,
Failed to complete setup,无法完成设置,
Failed to connect to server,无法连接服务器,
Failed while amending subscription,修改订阅时失败,
FavIcon,网站图标,
Feedback Request,反馈请求,
Fetch From,取自,
Fetch If Empty,获取如果为空,
Fetch Images,获取图像,
Fetch attached images from document,从文档中获取附加图像,
"Field ""route"" is mandatory for Web Views",Web视图必须使用字段“路由”,
"Field ""value"" is mandatory. Please specify value to be updated",字段“值”是强制性的。请指定值进行更新,
Field Description,字段说明,
Field Maps,字段地图,
Field Type,字段类型,
"Field that represents the Workflow State of the transaction (if field is not present, a new hidden Custom Field will be created)",字段代表交易的工作流状态（如果字段不存在，系统会创建一个隐藏的自定义字段）,
Field to Track,追踪领域,
Field type cannot be changed for {0},{0}不能更改字段类型,
Field {0} not found.,找不到字段{0}。,
Fieldname is limited to 64 characters ({0}),字段名被限制为64个字符（{0}）,
Fieldname not set for Custom Field,必须为自定义字段设置设置字段名,
Fieldname which will be the DocType for this link field.,字段名将作为这个文档类型的链接字段。,
Fieldname {0} cannot have special characters like {1},字段名{0}不能有特殊字符，如{1},
Fieldname {0} conflicting with meta object,字段名{0}与元对象冲突,
Fields Multicheck,字段多重检查,
"Fields separated by comma (,) will be included in the ""Search By"" list of Search dialog box",字段以逗号分隔（，）将被列入“通过搜索”的搜索对话框的列表,
Fieldtype,字段类型,
Fieldtype cannot be changed from {0} to {1} in row {2},排{2}中的字段类型不能从{0}更改为{1},
File '{0}' not found,文件&#39;{0}&#39;未找到,
File Backup,文件备份,
File Name,文件名,
File Size,文件大小,
File Type,文件类型,
File URL,文件的URL,
File Upload,上传文件,
File Upload Disconnected. Please try again.,文件上传已断开连接。请再试一次。,
File Upload in Progress. Please try again in a few moments.,文件上传正在进行中。请稍后重试。,
File backup is ready,文件备份就绪,
File not attached,文件未附加,
File size exceeded the maximum allowed size of {0} MB,文件大小超过允许的{0} MB,
File too big,文件太大,
File {0} does not exist,文件{0}不存在,
Files,档,
Filter,过滤,
Filter Data,过滤数据,
Filter List,过滤器列表,
Filter Meta,过滤元,
Filter Name,过滤器名称,
Filter Values,过滤值,
Filter must be a tuple or list (in a list),过滤器必须是元组或列表（在列表中）,
"Filter must have 4 values (doctype, fieldname, operator, value): {0}",过滤器必须有4个值（文件类型，字段名，操作者，值）：{0},
Filter...,过滤器...,
"Filtered by ""{0}""",通过过滤“{0}”,
Filters Display,显示过滤器,
Filters JSON,过滤JSON,
Filters saved,过滤器保存,
Find {0} in {1},在{1}中找到{0},
First Level,第一级,
First Success Message,第一个成功消息,
First Transaction,第一笔交易,
First data column must be blank.,第一个数据列必须为空。,
First set the name and save the record.,首先设置名称并保存记录。,
Flag,旗,
Float,浮点数,
Float Precision,浮点数精度,
Fold,折叠,
Fold can not be at the end of the form,不能在表单的末端折叠,
Fold must come before a Section Break,折叠一定要来一个分节符前,
Folder,文件夹,
Folder name should not include '/' (slash),文件夹名称不应包含“/”（斜杠）,
Folder {0} is not empty,文件夹{0}非空,
Follow,跟随,
Followed by,其次是,
Following fields are missing:,以下字段缺失：,
Following fields have missing values:,以下字段缺少值：,
Font,字形,
Font Size,字体大小,
Fonts,字体,
Footer,页脚,
Footer HTML,页脚HTML,
Footer Items,页脚项目,
Footer will display correctly only in PDF,页脚仅以PDF格式正确显示,
For Document Type,对于文档类型,
"For Links, enter the DocType as range.\nFor Select, enter list of Options, each on a new line.",对于链接，输入文档类型的范围。对于选择，输入选项列表，每一个新的生产线。,
For User,对于用户,
For Value,为价值,
"For currency {0}, the minimum transaction amount should be {1}",对于货币{0}，最小交易金额应为{1},
For example if you cancel and amend INV004 it will become a new document INV004-1. This helps you to keep track of each amendment.,例如如取消和修订INV004将成为一个新的文档INV004-1，这有利于你跟踪每次修订。,
"For example: If you want to include the document ID, use {0}",例如：如果要包括文件ID，使用{0},
"For updating, you can update only selective columns.",您只能更新选择的列。,
For {0} at level {1} in {2} in row {3},对行{3}，{2}中级别{1}的{0},
Force,力,
Force Show,炫耀武力,
Forgot Password,忘记密码,
Forgot Password?,忘了密码？,
Form Customization,表单自定义,
Form Settings,表单设置,
Format,格式,
Format Data,格式化数据,
Forward To Email Address,转发到邮件地址,
Fraction,分数,
Fraction Units,部分单位,
Frames,框架,
Frappe,果汁刨冰,
Frappe Framework,Frappe框架,
Friendly Title,友情标题,
From Date Field,从日期字段,
From Document Type,从文档类型,
From Full Name,从全名,
Full Page,全页,
Fw: {0},FW：{0},
GCalendar Sync ID,GCalendar同步ID,
GMail,GMail,
Gantt,甘特图,
Gateway,网关,
Gateway Controller,网关控制器,
Gateway Settings,网关设置,
Generate Keys,生成密钥,
Generate New Report,生成新报告,
Generated File,生成的文件,
Geo,GEO,
Geolocation,地理位置,
Get Alerts for Today,获得对于今天的通知,
Get Contacts,获取联系人,
Get Fields,获得领域,
Get your globally recognized avatar from Gravatar.com,从Gravatar.com获取您的全球公认的化身,
GitHub,GitHub,
Give Review Points,给予评论点,
Global Unsubscribe,全局退订,
Go to the document,转到文档,
Go to this URL after completing the form (only for Guest users),完成表单后转到此网址（仅限访客用户）,
Go to {0},转到{0},
Go to {0} List,转到{0}列表,
Go to {0} Page,转到{0}页面,
Google,谷歌,
Google Analytics ID,谷歌Analytics ID,
Google Calendar ID,Google日历ID,
Google Font,谷歌字体,
Google Services,Google服务,
Grant Type,准予类型,
Group Name,团队名字,
Group name cannot be empty.,组名不能为空。,
Groups of DocTypes,文档类型的组,
HTML,HTML,
HTML Editor,HTML编辑器,
"HTML Header, Robots and Redirects",HTML标头，机器人和重定向,
HTML for header section. Optional,HTML的标题部分。可选,
Half,半,
Has  Attachment,有附件,
Has Attachments,有附件,
Has Domain,有域名,
Has Role,有角色,
Has Web View,有Web视图,
Have an account? Login,有账户？登录,
Header,头,
Header HTML,标题HTML,
Header HTML set from attachment {0},从附件{0}设置的标头HTML,
Header Image,标题图像,
Headers,头,
Heading,标题,
Hello {0},你好{0},
Hello!,你好！,
Help Articles,帮助文章,
Help Category,帮助分类,
Help on Search,搜索帮助,
"Help: To link to another record in the system, use ""#Form/Note/[Note Name]"" as the Link URL. (don't use ""http://"")",帮助：要链接到系统中的另一个记录，请使用“#表单/记录/ [记录名]”作为链接。不要使用“http://”格式的链接。,
Helvetica,黑体,
Hi {0},你好{0},
Hide Copy,隐藏副本,
Hide Footer Signup,隐藏页脚注册,
Hide Sidebar and Menu,隐藏补充工具栏和菜单,
Hide Standard Menu,隐藏标准菜单,
Hide Weekends,隐藏周末,
Hide details,隐藏详情,
Hide footer in auto email reports,在自动电子邮件报告中隐藏页脚,
Higher priority rule will be applied first,首先应用更高优先级的规则,
Highlight,高亮,
"Hint: Include symbols, numbers and capital letters in the password",提示：在密码中加入符号，数字和大写字母,
Home Page,主页,
Home Settings,家庭设置,
Home/Test Folder 1,主页/测试文件夹1,
Home/Test Folder 1/Test Folder 3,主页/测试文件夹1 /测试文件夹3,
Home/Test Folder 2,主页/测试文件夹2,
Host,主办,
Hostname,主机名,
"How should this currency be formatted? If not set, will use system defaults",货币应该如何格式化？未设置将使用系统默认,
I found these: ,我发现这些：,
ID,ID,
ID (name) of the entity whose property is to be set,将被设定属性的条目ID(名称),
Icon will appear on the button,图标将显示在按钮上,
Identity Details,身份信息,
Idx,IDX,
"If Apply Strict User Permission is checked and User Permission is defined for a DocType for a User, then all the documents where value of the link is blank, will not be shown to that User",如果选中了“严格用户权限”，并为用户定义了“用户权限”，则该链接的值为空的所有文档将不会显示给该用户,
If Checked workflow status will not override status in list view,如果选中，工作流状态不会覆盖列表视图中的状态,
If Owner,如果业主,
"If a Role does not have access at Level 0, then higher levels are meaningless.",如果角色没有0级的访问权限，那么无需设置更高级权限,
"If checked, all other workflows become inactive.",如果勾选，那么其他的工作流将变为非活动。,
"If checked, this field will be not overwritten based on Fetch From if a value already exists.",如果选中，则如果值已存在，则不会基于Fetch From覆盖此字段。,
"If checked, users will not see the Confirm Access dialog.",如果选中，用户将不会看到确认访问对话框。,
"If disabled, this role will be removed from all users.",如果禁用了，这个角色将会从所有用户中删除。,
"If enabled,  user can login from any IP Address using Two Factor Auth, this can also be set for all users in System Settings",如果启用，用户可以使用双因素身份验证从任何IP地址登录，也可以在系统设置中为所有用户设置,
"If enabled, all users can login from any IP Address using Two Factor Auth. This can also be set only for specific user(s) in User Page",如果启用，所有用户都可以使用双因素身份验证从任何IP地址登录。这也可以只为用户页面中的特定用户设置,
"If enabled, changes to the document are tracked and shown in timeline",如果启用，则会跟踪对文档的更改并在时间轴中显示,
"If enabled, document views are tracked, this can happen multiple times",如果启用，则会跟踪文档视图，这可能会多次发生,
"If enabled, the document is marked as seen, the first time a user opens it",如果启用，则在用户第一次打开文档时将文档标记为已显示,
"If enabled, the password strength will be enforced based on the Minimum Password Score value. A value of 2 being medium strong and 4 being very strong.",如果启用，密码强度将根据最低密码分数值执行。值2为中等强度，4为非常强。,
"If enabled, users who login from Restricted IP Address, won't be prompted for Two Factor Auth",如果启用，从限制IP地址登录的用户将不会被提示输入双因素身份验证,
"If enabled, users will be notified every time they login. If not enabled, users will only be notified once.",如果启用，用户每次登录时都会收到通知。如果没有启用，用户只会收到一次通知。,
If non standard port (e.g. 587),如果为非标准端口（如587）,
"If non standard port (e.g. 587). If on Google Cloud, try port 2525.",如果非标准端口（例如587）。如果在Google Cloud上，请尝试使用端口2525。,
"If not set, the currency precision will depend on number format",如果未设置，则货币精度将取决于数字格式,
If the condition is satisfied user will be rewarded with the points. eg. doc.status == 'Closed'\n,如果条件满足，用户将获得积分奖励。例如。 doc.status ==&#39;已关闭&#39;,
"If the user has any role checked, then the user becomes a ""System User"". ""System User"" has access to the desktop",如果用户有任何检查的作用，那么用户就变成了“系统用户”。 “系统用户”访问桌面,
"If these instructions where not helpful, please add in your suggestions on GitHub Issues.",如果这些说明没有帮助，请在Github提交你的建议。,
"If this is checked, rows with valid data will be imported and invalid rows will be dumped into a new file for you to import later.",如果选中此选项，将导入包含有效数据的行，并将无效行转储到新文件中以供稍后导入。,
If user is the owner,如果用户是所有者,
"If you are updating, please select ""Overwrite"" else existing rows will not be deleted.",如果你的操作是更新，请选择“覆盖”否则现有的行不会删除。,
If you are updating/overwriting already created records.,如果您正在更新/覆盖已经创建的记录。,
"If you are uploading new records, ""Naming Series"" becomes mandatory, if present.",如果你在上传新纪录，那么“名录”必须指定。,
"If you are uploading new records, leave the ""name"" (ID) column blank.",如果你在上传新纪录，那么“名称”列必须留空。,
If you don't want to create any new records while updating the older records.,如果您不想在更新旧记录时创建新记录。,
"If you set this, this Item will come in a drop-down under the selected parent.",如果勾选，这个项目将出现在选定上级的下级菜单。,
"If you think this is unauthorized, please change the Administrator password.",如果你认为这是未经授权的，请更改管理员密码。,
"If your data is in HTML, please copy paste the exact HTML code with the tags.",如果你的数据是HTML，请复制粘贴的标签准确的HTML代码。,
Ignore User Permissions,忽略用户权限,
Ignore XSS Filter,忽略XSS过滤器,
Ignore attachments over this size,忽略超过此大小的附件,
Ignore encoding errors,忽略编码错误,
Ignored: {0} to {1},忽略：{0} {1},
Illegal Access Token. Please try again,非法访问令牌。请再试一次,
Illegal Document Status for {0},{0}的非法文档状态,
Image Field,图像字段,
Image Link,图片链接,
Image field must be a valid fieldname,图像字段必须是有效的字段名,
Image field must be of type Attach Image,图像字段的类型必须为附件图像,
Images,图片,
Implicit,隐式,
Import,导入,
Import Email From,导入电子邮件..,
Import Status,导入状态,
Import Subscribers,进口认购,
Import Zip,导入邮编,
In Filter,在过滤器中,
In Global Search,在全局搜索,
In Grid View,在网格视图,
In Hours,以小时为单位,
In List View,在列表视图,
In Preview,在预览中,
In Reply To,在回答,
In Standard Filter,在标准过滤器,
In Valid Request,非法请求,
In points. Default is 9.,以点为单位。默认值是9。,
In seconds,很快,
Include Search in Top Bar,包括在顶栏搜索,
"Include symbols, numbers and capital letters in the password",在密码中加入符号，数字和大写字母,
Incoming email account not correct,收到的电子邮件帐户不正确,
Incomplete login details,登录详细信息不完整,
Incorrect User or Password,不正确的用户或密码,
Incorrect Verification code,验证码不正确,
Incorrect value in row {0}: {1} must be {2} {3},行{0}中的值有错误，{1}必须是{2} {3},
Incorrect value: {0} must be {1} {2},值有错误，{0}必须是{1} {2},
Index,索引,
Indicator,指示符,
Info,信息,
Info:,信息：,
Initial Sync Count,初始同步计数,
InnoDB,InnoDB,
Insert Above,在上面插入,
Insert After,在后边插入,
Insert After cannot be set as {0},在后边插入不能设置为{0},
"Insert After field '{0}' mentioned in Custom Field '{1}', with label '{2}', does not exist",插入自定义字段“{0}”提到的后场“{1}”，标记为“{2}”不存在,
Insert Below,下面插入,
Insert Column Before {0},在{0}之前插入列,
Insert Style,插入样式,
Insert new records,插入新记录,
Instructions Emailed,说明已通过电子邮件发送,
Insufficient Permission for {0},{0}的权限不足,
Int,整数,
Integration Request,集成请求,
Integration Request Service,集成请求服务,
Integration Type,整合型,
Integrations,集成,
Integrations can use this field to set email delivery status,集成可以使用此字段来设置邮件发送状态,
Internal Server Error,内部服务器错误,
Internal record of document shares,文件分享的内部记录,
Introduce your company to the website visitor.,向网站访客介绍贵公司。,
Introductory information for the Contact Us Page,联系我们页面的介绍信息,
Invalid,无效,
"Invalid ""depends_on"" expression",“depends_on”表达式无效,
Invalid Access Key ID or Secret Access Key.,无效的访问密钥ID或秘密访问密钥。,
Invalid CSV Format,CSV格式无效,
Invalid Home Page,无效的主页,
Invalid Link,无效链接,
Invalid Login Token,无效的登录令牌,
Invalid Login. Try again.,登录无效。再试一次。,
Invalid Mail Server. Please rectify and try again.,无效的邮件服务器，请纠正后重试。,
Invalid Outgoing Mail Server or Port,无效的邮件发送服务器或端口,
Invalid Output Format,无效的输出格式,
Invalid Password,无效的密码,
Invalid Password:,无效的密码：,
Invalid Request,无效请求,
Invalid Search Field {0},无效的搜索字段{0},
Invalid Subscription,订阅无效,
Invalid Token,令牌无效,
Invalid User Name or Support Password. Please rectify and try again.,无效的用户名或支持密码。请纠正然后重试。,
Invalid column,无效的列,
Invalid field name {0},字段名称{0}无效,
Invalid fieldname '{0}' in autoname,在自动命名无效字段名“{0}”,
Invalid file path: {0},无效的文件路径：{0},
Invalid login or password,无效的登录名或密码,
Invalid module path,无效的模块路径,
Invalid naming series (. missing),名录无效(不存在),
Invalid payment gateway credentials,无效的支付网关凭据,
Invalid recipient address,无效的收件人地址,
Invalid {0} condition,{0}条件无效,
Inverse,逆,
Is,是,
Is Attachments Folder,是附件文件夹,
Is Child Table,是否子表,
Is Custom Field,是自定义字段,
Is First Startup,是第一次启动,
Is Folder,在文件夹,
Is Global,是全局性的,
Is Globally Pinned,是全局固定的,
Is Home Folder,是主文件夹,
Is Mandatory Field,是否必须项,
Is Pinned,固定,
Is Primary Contact,是否主要联络人,
Is Private,是私有,
Is Published Field,发布现场,
Is Published Field must be a valid fieldname,发布现场必须是有效的字段名,
Is Single,是模块配置表？,
Is Spam,是垃圾邮件,
Is Standard,是否为标准,
Is Submittable,是否可以提交,
Is Table,为表,
Is Your Company Address,是你的公司地址,
It is risky to delete this file: {0}. Please contact your System Manager.,删除这个文件是有风险的：{0}。请联系您的系统管理员。,
Item cannot be added to its own descendants,项目不能被添加到自己的后代,
JS,JS,
JSON,JSON,
JavaScript Format: frappe.query_reports['REPORTNAME'] = {},JavaScript格式：frappe.query_reports ['REPORTNAME'] = {},
Javascript to append to the head section of the page.,附加到页面头部的Javascript。,
Jinja,神社,
John Doe,John Doe,
Kanban,看板,
Kanban Board Column,看板列,
Kanban Board Name,看板名称,
Karma,业值,
Keep track of all update feeds,跟踪所有更新提要,
Keeps track of all communications,跟踪所有通信,
Key,键值,
Knowledge Base,知识库,
Knowledge Base Contributor,知识库贡献者,
Knowledge Base Editor,知识库编辑器,
LDAP Email Field,LDAP电子邮件字段,
LDAP First Name Field,LDAP名现场,
LDAP Not Installed,LDAP未安装,
LDAP Search String,LDAP搜索字符串,
"LDAP Search String needs to end with a placeholder, eg sAMAccountName={0}",LDAP搜索字符串需要以占位符结束，例如sAMAccountName = {0},
LDAP Security,LDAP安全性,
LDAP Server Url,LDAP服务器URL,
LDAP Username Field,LDAP用户名字段,
LDAP is not enabled.,LDAP未启用。,
Label Help,标签帮助,
Label and Type,标签和类型,
Label is mandatory,标签是必须项,
Landing Page,着陆页,
Language,语言,
Language Code,语言代码,
"Language, Date and Time settings",语言，日期和时间设置,
Last Active,最后活动,
Last IP,最后登录IP,
Last Known Versions,最后已知版本,
Last Login,最后登录,
Last Message,最后的消息,
Last Modified By,上次修改者,
Last Modified Date,上次修改日期,
Last Modified On,上次修改,
Last Month,上个月,
Last Point Allocation Date,最后一点分配日期,
Last Quarter,上个季度,
Last Synced On,最后同步,
Last Updated By,最后更新人,
Last Updated On,最后更新日期,
Last User,最后用户,
Last Week,上个星期,
Last Year,去年,
Last synced {0},上次同步{0},
Leave a Comment,发表评论,
Leave blank to repeat always,留空将总重复,
Leave this conversation,离开这个谈话,
Left this conversation,离开了这个谈话,
Length,长短,
Length of {0} should be between 1 and 1000,的{0}长度应介于1和1000之间,
Let's avoid repeated words and characters,让我们避免重复的字和字符,
Let's prepare the system for first use.,让我们为首次使用准备系统。,
Letter,信,
Letter Head Based On,基于的信头,
Letter Head Image,信头图像,
Letter Head Name,信头名称,
Letter Head in HTML,HTML格式信头,
Level Name,级别名称,
Liked,喜欢,
Liked By,谁喜欢,
Liked by {0},{0}喜欢,
Likes,喜欢,
Limit Number of DB Backups,限制数据库备份数量,
Line,线,
Link DocType,链接的文件类型,
Link Expired,链接已过期,
Link Name,链接名称,
Link Title,链接名称,
"Link that is the website home page. Standard Links (index, login, products, blog, about, contact)","网站主页的链接。标准链接如(目录, 登录, 产品, 博客, 关于, 联系人)",
Link to the page you want to open. Leave blank if you want to make it a group parent.,链接到你想打开的页面。如果你想使之成为集团母公司留空。,
Linked,链接,
Linked With,挂具,
Linked with {0},与{0}关联,
Links,链接,
List,列表,
List Filter,列表过滤器,
List View Setting,列表视图设置,
List a document type,列出某文件类型,
"List as [{""label"": _(""Jobs""), ""route"":""jobs""}]",名单[{“标签”：_（“作业”），“路线”：“工作”}],
List of backups available for download,可供下载的备份目录,
List of patches executed,已应用补丁列表,
List of themes for Website.,主题的网站名单。,
Load Balancing,负载均衡,
Loading,载入中,
Local DocType,本地文件类型,
Local Fieldname,本地字段名称,
Local Primary Key,本地主键,
Locals,当地人,
Log Details,日志详情,
Log of Scheduler Errors,日程安排程序错误日志,
Log of error during requests.,错误的过程中请求日志。,
Log of error on automated events (scheduler).,自动事件的错误日志。,
Logged Out,登出,
Logged in as Guest or Administrator,登录为来宾或管理员,
Login,登录,
Login After,登录后,
Login Before,登录前,
Login Id is required,登录ID是必需的,
Login Required,需要登录信息,
Login Verification Code from {},登录验证码从{},
Login and view in Browser,在浏览器中登录和查看,
Login not allowed at this time,不允许在这个时候登录,
"Login session expired, refresh page to retry",登录会话过期，刷新页面重试,
Login to comment,登录发表评论,
Login token required,需要登录令牌,
Login with LDAP,与LDAP登录,
Logout,注销,
Long Text,长文本,
Looks like something is wrong with this site's Paypal configuration.,看起来这个网站的Paypal配置有问题。,
Looks like something is wrong with this site's payment gateway configuration. No payment has been made.,看来该网站的支付网关配置不正确，无法付款。,
"Looks like something went wrong during the transaction. Since we haven't confirmed the payment, Paypal will automatically refund you this amount. If it doesn't, please send us an email and mention the Correlation ID: {0}.",交易中似乎发生错误。由于我方尚未确认付款，贝宝（paypal)会自动给您全额退款。如果没有退款，请给我们发邮件并注明相关ID：{0}。,
Madam,夫人,
Main Section,主要部分,
"Make ""name"" searchable in Global Search",让“名称”字段在全局搜索框中可搜索,
Make use of longer keyboard patterns,利用较长的键盘模式,
Manage Third Party Apps,管理第三方应用,
Mandatory Information missing:,强制性信息丢失：,
Mandatory field: set role for,必须填写：用于设置角色,
Mandatory field: {0},强制字段：{0},
"Mandatory fields required in table {0}, Row {1}",在表{0}需要强制字段，行{1},
Mandatory fields required in {0},在需要的必填字段{0},
Mandatory:,必须项：,
Mapping Name,映射名称,
Mappings,映射,
Mark as Read,标记为已读,
Mark as Spam,标记为垃圾邮件,
Mark as Unread,标记为未读,
Markdown,Markdown,
Markdown Editor,Markdown 编辑器,
Marked As Spam,标记为垃圾邮件,
Max 500 records at a time,最大500条记录在一个时间,
Max Attachment Size (in MB),最大附件大小（MB）,
Max Attachments,最大附件,
Max Length,最长长度,
Max Value,最大值,
Max width for type Currency is 100px in row {0},行{0}中，货币类型的最大宽度是100像素,
Maximum Attachment Limit for this record reached.,已达此记录最大附件限制。,
Maximum {0} rows allowed,仅允许最多{0}行,
"Meaning of Submit, Cancel, Amend",提交，取消，修订的含义,
Mention transaction completion page URL,提及交易完成的页面网址,
Mentions,提及,
Menu,菜单,
Merchant ID,商家ID,
Merge with existing,与现有合并,
Merging is only possible between Group-to-Group or Leaf Node-to-Leaf Node,只有组和组，叶节点和叶节点之间能合并,
Message Count,消息计数,
Message ID,消息ID,
Message Parameter,消息参数,
Message Preview,消息预览,
Message clipped,消息被省略,
Message not setup,消息未设置,
Message to be displayed on successful completion (only for Guest users),成功完成后显示的消息（仅限访客用户）,
Message-id,邮件ID,
Meta Tags,元标签,
Migration ID Field,迁移ID字段,
Milestone,里程碑,
Milestone Tracker,里程碑跟踪器,
Minimum Password Score,最低密码分数,
Miss,小姐,
Missing Fields,丢失的字段,
Missing parameter Kanban Board Name,缺少参数看板名称,
Missing parameters for login,缺少参数登录,
Models (building blocks) of the Application,该应用程序的模型（积木）,
Modified By,修改者,
Module,模,
Module Def,模块定义,
Module Name,模块名称,
Module Not Found,模块未找到,
Module Path,模块路径,
Module to Export,模块导出,
Modules HTML,HTML模块,
Monospace,等宽,
More articles on {0},更多的文章{0},
More content for the bottom of the page.,页面底部的更多内容。,
Most Used,最常用,
Move To,搬去,
Move To Trash,移到废纸篓,
Move to Row Number,移至行号,
Mr,先生,
Mrs,太太,
Ms,女士,
Multiple root nodes not allowed.,不允许多个根节点。,
Multiplier Field,乘数场,
"Must be of type ""Attach Image""",类型必须为“附加图片”,
Must have report permission to access this report.,必须有报告权限才能访问此报告。,
Must specify a Query to run,必须指定要运行的查询,
Mute Sounds,静音,
MyISAM,MyISAM数据,
Name Case,名称大小写,
Name cannot contain special characters like {0},姓名不能包含特殊字符，如{0},
Name not set via prompt,名称未通过提示符设置,
Name of the Document Type (DocType) you want this field to be linked to. e.g. Customer,此字段链接到的文档类型名称，例如客户,
Name of the new Print Format,新打印格式的名称,
Name of {0} cannot be {1},{0}的名称不能为{1},
Names and surnames by themselves are easy to guess.,自己名字和姓氏很容易被猜到。,
Naming,命名,
"Naming Options:\n<ol><li><b>field:[fieldname]</b> - By Field</li><li><b>naming_series:</b> - By Naming Series (field called naming_series must be present</li><li><b>Prompt</b> - Prompt user for a name</li><li><b>[series]</b> - Series by prefix (separated by a dot); for example PRE.#####</li>\n<li><b>format:EXAMPLE-{MM}morewords{fieldname1}-{fieldname2}-{#####}</b> - Replace all braced words (fieldnames, date words (DD, MM, YY), series) with their value. Outside braces, any characters can be used.</li></ol>",命名选项： <ol><li> <b>字段：[fieldname]</b> - 按字段</li><li> <b>naming_series：</b> - 通过命名系列（必须存在名为naming_series的字段</li><li> <b>提示</b> - 提示用户输入名称</li><li> <b>[系列]</b> - 前缀系列（以点分隔）;例如PRE。##### </li><li> <b>格式：示例 -  {MM} morewords {fieldname1}  -  {fieldname2}  -  {#####}</b> - 将所有支撑的单词（字段名，日期字（DD，MM，YY），系列）替换为其值。在大括号外，可以使用任何字符。 </li></ol>,
Naming Series mandatory,名录必须填写,
Nested set error. Please contact the Administrator.,嵌套错误。请联系管理员。,
New Activity,新活动,
New Chat,新聊天,
New Comment on {0}: {1},对{0}的新评论：{1},
New Connection,新的连接,
New Custom Print Format,新的自定义打印格式,
New Email,新的电子邮件,
New Email Account,新的电子邮件帐户,
New Event,新事件,
New Folder,新建文件夹,
New Kanban Board,新看板,
New Message from Website Contact Page,从网站的联系页面新消息,
New Name,新名称,
New Newsletter,新的通讯,
New Password,新密码,
New Password Required.,需要新密码,
New Print Format Name,新的打印格式名称,
New Report name,新的报告名称,
New Value,新价值,
New data will be inserted.,将插入新数据。,
New updates are available,有新的更新,
New value to be set,要设置的新值,
New {0},新建 {0},
New {} releases for the following apps are available,可以使用以下应用程序的新{}版本,
Newsletter Email Group,电子邮件通讯组,
Newsletter Manager,通讯经理,
Newsletter has already been sent,通讯已发送过,
"Newsletters to contacts, leads.",发给联系人和潜在客户的通讯,
Next Action Email Template,下一行动电子邮件模板,
Next Actions HTML,下一步操作HTML,
Next Schedule Date,下一个附表日期,
Next Scheduled Date,下一个预定日期,
Next State,下一状态,
Next Sync Token,下一个同步令牌,
Next actions,下一步行动,
No Active Sessions,没有活动会话,
No Copy,没有复制,
No Email Account,没有电子邮件帐户,
No Email Accounts Assigned,没有电子邮件帐户分配,
No Emails,没有电子邮件,
No Label,无标签,
No Permissions Specified,未指定权限,
No Permissions set for this criteria.,没有符合条件的权限。,
No Preview,没有预览,
No Preview Available,预览不可用,
No Printer is Available.,没有打印机可用。,
No Results,没有结果,
No Tags,没有标签,
No alerts for today,没有警报今天,
No comments yet,暂无评论,
No comments yet. Start a new discussion.,还没有评论。开始一个新的讨论。,
No data found in the file. Please reattach the new file with data.,在文件中找不到数据。请用数据重新附加新文件。,
No document found for given filters,没有找到给定过滤器的文档,
"No fields found that can be used as a Kanban Column. Use the Customize Form to add a Custom Field of type ""Select"".",找不到可用作看板列的字段。使用“自定义表单”添加“选择”类型的自定义字段。,
No file attached,没有附加的文件,
No further records,没有进一步的记录,
No matching records. Search something new,没有符合条件的记录。搜索新的东西,
"No need for symbols, digits, or uppercase letters.",无需符号，数字和大写字母。,
No of Columns,列编号,
No of Rows (Max 500),不排（最大500）,
No of emails remaining to be synced,剩余的需同步的电子邮件数,
No permission for {0},对于无许可{0},
No permission to '{0}' {1},没有权限“{0}” {1},
No permission to read {0},没有读取{0}的权限,
No permission to {0} {1} {2},无权{0} {1} {2},
No records deleted,没有记录被删除,
No records present in {0},{0}中没有记录,
No records tagged.,没有记录标记。,
No template found at path: {0},从{0}路径中找不到模板,
No {0} found,没有找到{0},
No {0} mail,没有{0}邮件,
No {0} permission,无{0}权限,
None: End of Workflow,无：结束的工作流程,
Not Allowed: Disabled User,不允许：禁用用户,
Not Ancestors Of,不是祖先,
Not Descendants Of,不是后代,
Not Equals,不等于,
Not In,在不,
Not Linked to any record,未链接到任何记录,
Not Published,未发布,
Not Saved,尚未保存,
Not Seen,未阅读,
Not Sent,未发送,
Not Set,未设置,
Not a valid Comma Separated Value (CSV File),不是一个有效的CSV文件,
Not a valid User Image.,不是有效的用户映像。,
Not a valid Workflow Action,不是有效的工作流程操作,
Not a valid user,不是有效的用户,
Not a zip file,没有一个zip文件,
Not allowed for {0}: {1},不允许{0}：{1},
"You are not allowed to access {0} because it is linked to {1} '{2}' in row {3}, field {4}",您无权访问{0}，因为它链接到{1}“{2}”在行{3}，字段{4},
You are not allowed to access this {0} record because it is linked to {1} '{2}' in field {3},您无权访问此{0}记录，因为它链接到{1}“{2}”在字段{3},
Not allowed to Import,不允许导入,
Not allowed to change {0} after submission,不允许提交后更改{0},
Not allowed to print cancelled documents,不允许打印已取消文件,
Not allowed to print draft documents,不允许打印文件草案,
Not enough permission to see links,没有足够的权限查看链接,
Not in Developer Mode,不在开发模式,
Not in Developer Mode! Set in site_config.json or make 'Custom' DocType.,未开启开发模式！请在site_config.json中设置或创建一个自定义文档类型,
Note Seen By,注意 已阅,
Note:,注意：,
Note: By default emails for failed backups are sent.,注意：默认情况下，会发送失败备份的电子邮件。,
Note: Changing the Page Name will break previous URL to this page.,注意：更改页面名称将会断开之前链接到此页面URL。,
"Note: For best results,  images must be of the same size and width must be greater than height.",注意：为获得最佳效果，图像必须大小相同，宽度必须大于高度。,
Note: Multiple sessions will be allowed in case of mobile device,注：在移动设备的情况下多个会话将被允许,
Nothing to show,没有显示,
Nothing to update,无需更新,
Notification,通知,
Notification Recipient,通知收件人,
Notification Tones,通知音,
Notifications,通知,
Notifications and bulk mails will be sent from this outgoing server.,通知和群发邮件将从此外发服务器发送。,
Notify Users On Every Login,每次登录时通知用户,
Notify if unreplied,如果没有回复则通知,
Notify if unreplied for (in mins),如果（分钟）没有回复则通知,
Notify users with a popup when they log in,用户登录时有弹出一个通知,
Number Format,数字格式,
Number of Backups,备份数量,
Number of DB Backups,数据库备份数,
Number of DB backups cannot be less than 1,数据库备份数不能少于1,
Number of columns for a field in a Grid (Total Columns in a grid should be less than 11),网格中的一个字段的行数（在网格总列应小于11）,
Number of columns for a field in a List View or a Grid (Total Columns should be less than 11),列数在列表视图或网格场（合计列应小于11）,
OAuth Authorization Code,开放式验证的授权码,
OAuth Bearer Token,开放式验证的承载令牌,
OAuth Client,开放式验证客户端,
OAuth Provider Settings,开放式验证的提供商设置,
OTP App,OTP应用程序,
OTP Issuer Name,OTP发行人名称,
OTP Secret has been reset. Re-registration will be required on next login.,OTP Secret已被重置。下次登录时需要重新注册。,
OTP secret can only be reset by the Administrator.,OTP秘密只能由管理员重置。,
Office,办公室,
Office 365,Office 365,
Old Password,旧密码,
Old Password Required.,需要旧密码,
Older backups will be automatically deleted,旧的备份将被自动删除,
"On {0}, {1} wrote:",{0}，{1}写道：,
"Once submitted, submittable documents cannot be changed. They can only be Cancelled and Amended.",提交后，无法更改可提交的文档。它们只能被取消和修改。,
"Once you have set this, the users will only be able access documents (eg. Blog Post) where the link exists (eg. Blogger).",一旦你设置这个，用户将只能访问在其链路存在时（如博主的）文档（如博客文章） 。,
One Last Step,最后一步,
One Time Password (OTP) Registration Code from {},来自{}的一次性密码（OTP）注册码,
Only 200 inserts allowed in one request,一个请求只允许200个插入,
Only Administrator can delete Email Queue,只有管理员可以删除邮件队列,
Only Administrator can edit,只有管理员可以编辑,
Only Administrator can save a standard report. Please rename and save.,只有管理员可以保存一个标准的报告。请重新命名并保存。,
Only Administrator is allowed to use Recorder,只允许管理员使用记录器,
Only Allow Edit For,只允许编辑,
Only Send Records Updated in Last X Hours,只发送记录在最后X小时更新,
Only mandatory fields are necessary for new records. You can delete non-mandatory columns if you wish.,只有必填字段所必需的新记录。如果你愿意，你可以删除非强制性列。,
Only standard DocTypes are allowed to be customized from Customize Form.,只允许从“自定义表单”自定义标准DocType。,
Only users involved in the document are listed,仅列出文档中涉及的用户,
Only {0} emailed reports are allowed per user,每个用户只允许{0}个电子邮件报告,
Oops! Something went wrong,糟糕！出错了,
"Oops, you are not allowed to know that",哎呀，你不允许知道这个,
Open Link,打开链接,
Open Source Applications for the Web,为Web的开源应用程序,
Open Translation,打开翻译,
Open a dialog with mandatory fields to create a new record quickly,打开包含必填字段的对话框以快速创建新记录,
Open a module or tool,打开一个模块或工具,
Open your authentication app on your mobile phone.,在您的手机上打开您的认证应用程序。,
Open {0},打开{0},
Opened,开业,
Operator must be one of {0},运算符必须是{0}之一,
Option 1,选项1,
Option 2,选项2,
Option 3,选项3,
Optional: Always send to these ids. Each Email Address on a new row,可选：总是发送给这些ID。在新行的每个电子邮件地址,
Optional: The alert will be sent if this expression is true,可选：警报会在这个表达式为true发送,
Options 'Dynamic Link' type of field must point to another Link Field with options as 'DocType',选择“动态链接”类型的字段都必须指向另一个链接字段的选项为'的文件类型“,
Options Help,选项帮助,
Options for select. Each option on a new line.,选择选项。在新的一行每个选项。,
Options not set for link field {0},对于链接字段没有设置选项{0},
Or login with,或用...登录,
Order,订购,
Org History,组织历史,
Org History Heading,组织历史航向,
Orientation,方向,
Original Value,原始值,
Outgoing email account not correct,发出电子邮件帐户不正确,
Outlook.com,Outlook.com,
Output,产量,
PDF,PDF,
PDF Page Size,PDF页面大小,
PDF Settings,PDF设置,
PDF generation failed,PDF生成失败,
PDF generation failed because of broken image links,PDF生成，因为破碎的图像链接失败,
"PDF printing via ""Raw Print"" is not yet supported. Please remove the printer mapping in Printer Settings and try again.",尚不支持通过“原始打印”进行PDF打印。请在“打印机设置”中删除打印机映射，然后重试。,
Page HTML,页面的HTML,
Page Length,页面长度,
Page Name,网页名称,
Page Settings,页面设置,
Page has expired!,页已过期！,
Page not found,找不到网页,
Page to show on the website\n,在网站上显示页面,
Pages in Desk (place holders),主页（桌面）中的网页（占位符）,
Parent,上级,
Parent Error Snapshot,上级错误快照,
Parent Label,上级标签,
Parent Table,上级表,
Parent is required to get child table data,上级表需要获取子表数据,
Parent is the name of the document to which the data will get added to.,Parent是将数据添加到的文档的名称。,
Partial Success,部分成功,
Partially Successful,部分成功,
Participants,参与者,
Passive,被动,
Password Reset,密码重置,
Password Updated,密码更新,
Password for Base DN,密码基础DN,
Password is required or select Awaiting Password,需要密码，或者选择等待密码,
Password not found,密码未找到,
Password reset instructions have been sent to your email,密码重置说明已发送到您的电子邮件,
Paste,粘贴,
Patch,补丁,
Patch Log,补丁日志,
Path to CA Certs File,CA Certs文件的路径,
Path to Server Certificate,服务器证书的路径,
Path to private Key File,私钥文件的路径,
PayPal Settings,贝宝设置,
PayPal payment gateway settings,贝宝支付网关设置,
Payment Cancelled,付款已取消,
Payment Success,支付成功,
Pending Approval,待批准,
Pending Verification,待验证,
Percent,百分之,
Percent Complete,完成百分比,
Perm Level,权限等级,
Permanent,常驻,
Permanently Cancel {0}?,永久取消{0} ？,
Permanently Submit {0}?,永久提交{0} ？,
Permanently delete {0}?,永久删除{0} ？,
Permission Error,权限错误,
Permission Level,权限级别,
Permission Levels,权限级别,
Permission Rules,权限规则,
Permissions,权限,
Permissions are automatically applied to Standard Reports and searches.,权限会自动应用于标准报告和搜索。,
"Permissions are set on Roles and Document Types (called DocTypes) by setting rights like Read, Write, Create, Delete, Submit, Cancel, Amend, Report, Import, Export, Print, Email and Set User Permissions.",权限是根据角色和文件类型（也称DocTypes）设置的，如设置权利如喜欢读权限，写，创建，删除，提交，取消，修改，报表，导入，导出，打印，电子邮件和设置用户权限,
Permissions at higher levels are Field Level permissions. All Fields have a Permission Level set against them and the rules defined at that permissions apply to the field. This is useful in case you want to hide or make certain field read-only for certain Roles.,在更高级别的权限是现场级的权限。所有领域都对他们权限级别设置，并在该权限适用于该领域中定义的规则。要隐藏或使某些领域的只读某些角色，这是在的情况下非常有用。,
"Permissions at level 0 are Document Level permissions, i.e. they are primary for access to the document.",0级权限是文档级的权限，也就是说，它们是主要用于访问文件。,
Permissions get applied on Users based on what Roles they are assigned.,权限基于角色的分配,
Personal,个人,
Personal Data Deletion Request,个人数据删除请求,
Personal Data Download Request,个人资料下载请求,
Phone No.,电话号码,
Pick Columns,摘列,
Plant,厂,
Please Duplicate this Website Theme to customize.,请复制此网址主题定制。,
Please Enter Your Password to Continue,请输入您的密码以继续,
Please Install the ldap3 library via pip to use ldap functionality.,请通过pip安装ldap3库以使用ldap功能。,
Please Update SMS Settings,请更新短信设置,
Please add a subject to your email,请在您的电子邮件中添加主题,
Please ask your administrator to verify your sign-up,请向管理员询问，以确认您的注册,
Please attach a file first.,请附上文件第一。,
Please attach an image file to set HTML,请附上图像文件以设置HTML,
Please check your email for verification,请检查您的电子邮件验证,
Please check your registered email address for instructions on how to proceed. Do not close this window as you will have to return to it.,请查看您注册的电子邮件地址以获取有关如何继续的说明。不要关闭这个窗口，因为你必须返回它。,
Please close this window,请关闭此窗口,
Please confirm your action to {0} this document.,请确认您对{0}此文档的操作。,
Please do not change the rows above {0},请不要更改上面的行{0},
Please do not change the template headings.,请不要更改模板标题。,
Please duplicate this to make changes,请复制此做出改变,
Please enable developer mode to create new connection,请启用开发人员模式以创建新的连接,
Please ensure that your profile has an email address,请确保您的个人资料有一个电子邮件地址，,
Please enter Access Token URL,请输入访问令牌URL,
Please enter Authorize URL,请输入授权网址,
Please enter Base URL,请输入基本网址,
Please enter Client ID before social login is enabled,在启用社交登录之前，请输入客户端ID,
Please enter Client Secret before social login is enabled,在启用社交登录之前，请输入客户端密码,
Please enter Redirect URL,请输入重定向网址,
Please enter the password,请输入密码,
Please enter valid mobile nos,请输入有效的手机号,
Please enter values for App Access Key and App Secret Key,为App访问键和App保密密钥请输入值,
Please make sure that there are no empty columns in the file.,请确保没有空列在文件中。,
Please make sure the Reference Communication Docs are not circularly linked.,请确保参考通信文档不是循环链接的。,
Please refresh to get the latest document.,请刷新以获得最新的文档。,
Please save before attaching.,请加附件前保存。,
Please save the Newsletter before sending,请在发送之前保存通讯,
Please save the document before assignment,请在指派前保存文件,
Please save the document before removing assignment,请删除分配之前保存的文档,
Please save the report first,请先保存报表,
Please select DocType first,请首先选择的文件类型,
Please select Entity Type first,请先选择实体类型,
Please select Minimum Password Score,请选择最低密码分数,
Please select a Amount Field.,请选择一个金额字段。,
Please select a file or url,请选择一个文件或URL,
Please select a new name to rename,请选择一个新名称进行重命名,
Please select a valid csv file with data,请选择与数据的有效csv文件,
Please select another payment method. PayPal does not support transactions in currency '{0}',请选择其他付款方式。贝宝不支持货币交易“{0}”,
Please select another payment method. Razorpay does not support transactions in currency '{0}',请选择其他付款方式。 Razorpay不支持货币交易“{0}”,
Please select atleast 1 column from {0} to sort/group,请从{0}到排序/组选择至少 1列,
Please select document type first.,请先选择文件类型。,
Please select the Document Type.,请选择文件类型。,
Please set Base URL in Social Login Key for Frappe,请在Frappe的社交登录密钥中设置基本网址,
Please set Dropbox access keys in your site config,请在您的网站配置设置Dropbox的访问键,
Please set a printer mapping for this print format in the Printer Settings,请在“打印机设置”中为此打印格式设置打印机映射,
Please set filters,请设置过滤器,
Please set filters value in Report Filter table.,请设置在报告过滤表过滤器值。,
"Please setup SMS before setting it as an authentication method, via SMS Settings",请先通过SMS设置将其设置为身份验证方式，然后设置短信,
Please setup a message first,请先设置一条消息,
Please specify which date field must be checked,请指定日期字段必须检查,
Please specify which value field must be checked,请指定值字段必须检查,
Please try again,请再试一次,
Please verify your Email Address,请验证您的邮箱地址,
Point Allocation Periodicity,点分配周期,
Points,点,
Points Given,得分,
Port,端口,
Portal Menu,门户网站菜单,
Portal Menu Item,门户菜单项,
Post,发送,
Post Comment,发表评论,
Postal,邮政,
Postal Code,邮政编码,
Postprocess Method,后处理方法,
Posts,帖子,
Posts by {0},帖子{0},
Posts filed under {0},帖子下提出{0},
Precision,精确,
Precision should be between 1 and 6,精度应为1和6之间,
Predictable substitutions like '@' instead of 'a' don't help very much.,可预见的替换像&#39;@&#39;而不是&#39;一&#39;不要太大帮助。,
Preferred Billing Address,首选帐单地址,
Preferred Shipping Address,首选送货地址,
Prepared Report,准备报告,
Preparing Report,准备报告,
Preprocess Method,预处理方法,
Press Enter to save,按Enter键保存,
Preview HTML,预览HTML,
Preview Message,预览消息,
Previous,以前,
Previous Hash,先前的哈希,
Primary Color,原色,
Print Documents,打印文件,
Print Format Builder,打印格式生成器,
Print Format Help,打印格式帮助,
Print Format Type,打印格式类型,
Print Format {0} is disabled,打印格式{0}被禁用,
Print Hide,打印隐藏,
Print Hide If No Value,打印隐藏如果没有值,
Print Sent to the printer!,打印发送到打印机！,
Print Server,打印服务器,
Print Style,打印样式,
Print Style Name,打印样式名称,
Print Style Preview,打印样式预览,
Print Width,打印宽度,
"Print Width of the field, if the field is a column in a table",打印领域的宽度，如果该字段是一个表中的列,
Print with letterhead,打印信头,
Printer,打印机,
Printer Mapping,打印机映射,
Printer Name,打印机名称,
Printer Settings,打印机设置,
Printing failed,打印失败,
Private Key,私钥,
Private and public Notes.,私人和公共的注意事项。,
ProTip: Add <code>Reference: {{ reference_doctype }} {{ reference_name }}</code> to send document reference,ProTip：添加<code>Reference: {{ reference_doctype }} {{ reference_name }}</code>发送文档引用,
Processing,处理,
Processing...,处理...,
Prof,教授,
Progress,进展,
Property Setter,属性setter,
Property Setter overrides a standard DocType or Field property,属性设置者覆盖标准的文件类型或字段属性,
Property Type,属性类型,
Provider,提供商,
Provider Name,提供者名称,
Public Key,公钥,
Publishable Key,可发布密钥,
Published On,发表于,
Pull,下拉,
Pull Failed,下拉失败,
Pull Insert,下拉插入,
Pull Update,下拉更新,
Push,推送,
Push Delete,推删除,
Push Failed,推送失败,
Push Insert,推入插入,
Push Update,推送更新,
Python Module,Python模块,
Pyver,Pyver,
QR Code,二维码,
QR Code for Login Verification,用于登录验证的QR码,
QZ Tray Connection Active!,QZ托盘连接有效！,
QZ Tray Failed: ,QZ托盘失败：,
Quarter Day,季日,
Query,查询,
Query Report,查询报告,
Query must be a SELECT,查询必须是一个SELECT语句,
Queue should be one of {0},队列应该是{0},
Queued for backup. It may take a few minutes to an hour.,排队等待备份。这可能需要几分钟到一个小时。,
Queued for backup. You will receive an email with the download link,排队备份。您将收到一封包含下载链接的电子邮件,
Quick Help for Setting Permissions,权限设置的快速帮助,
Rating: ,评分：,
Raw Commands,原始命令,
Raw Email,原始电子邮件,
Raw Printing,原始印刷,
Razorpay Payment gateway settings,Razorpay支付网关设置,
Razorpay Settings,Razorpay设置,
Re: ,回复：,
Re: {0},回复：{0},
Read,阅读,
Read Only,只读,
Read by Recipient,由收件人阅读,
Read by Recipient On,由收件人阅读,
Rebuild,重建,
Receiver Parameter,接收人参数,
Recent years are easy to guess.,最近的几年数字很容易被猜到。,
Recipient,收件人,
Recipient Unsubscribed,收件人取消订阅,
Record does not exist,记录不存在,
Records for following doctypes will be filtered,以下文档类型的记录将被过滤,
Redirect To,重定向到,
Redirect URI Bound To Auth Code,重定向URI势必授权码,
Redirect URIs,重定向的URI,
Redis cache server not running. Please contact Administrator / Tech support,Redis缓存服务器无法运行。请联系管理员/技术支持,
Ref DocType,参考的DocType,
Ref Report DocType,参考报告DocType,
Reference DocName,参考DocName,
Reference DocType and Reference Name are required,需要参考文档类型和参考名称,
Reference Report,参考报告,
Reference: {0} {1},参考：{0} {1},
Refreshing...,正在刷新...,
Register OAuth Client App,注册开放式验证客户端应用程序,
Registered but disabled,注册但被禁用,
Relapsed,复发,
Relapses,复发,
Relink,重新链接,
Relink Communication,重新链接通讯,
Relinked,重新链接,
Reload,重新载入,
Remember Last Selected Value,记得上次选定的值,
Remote,远程,
Remote Fieldname,远程字段名称,
Remote ID,远程ID,
Remote Objectname,远程对象名称,
Remote Primary Key,远程主键,
Remove,拆除,
Remove Field,删除字段,
Remove Filter,删除过滤器,
Remove Section,删除部分,
Remove Tag,删除标签,
Remove all customizations?,删除所有自定义？,
Removed {0},删除{0},
Rename many items by uploading a .csv file.,通过上传。csv文件重命名多个项目。,
Rename {0},重命名{0},
Repeat Header and Footer in PDF,重复页眉和页脚的PDF,
Repeat On,重复开,
Repeat Till,重复直到,
Repeat on Day,一天重复,
Repeat this Event,重复此事件,
"Repeats like ""aaa"" are easy to guess",像“AAA”重复很容易被猜到,
"Repeats like ""abcabcabc"" are only slightly harder to guess than ""abc""",重复像“ABCABCABC”只比“ABC”较难猜测一点,
Reply,回复,
Reply All,全部回复,
Report End Time,报告结束时间,
Report Filters,报告过滤器,
Report Hide,报告隐藏,
Report Manager,报告管理,
Report Name,报告名称,
Report Start Time,报告开始时间,
Report cannot be set for Single types,报告不能单类型设置,
Report of all document shares,所有的文件共享报告,
Report updated successfully,报告已成功更新,
Report was not saved (there were errors),报告尚未保存（有错误）,
Report {0},报告{0},
Report {0} is disabled,报告{0}无效,
Report:,报告：,
Represents a User in the system.,表示系统中一个用户。,
Represents the states allowed in one document and role assigned to change the state.,代表一个文档和角色分配给改变国家允许的状态。,
Request Timed Out,请求超时,
Request URL,请求URL,
Require Trusted Certificate,需要可信证书,
Res: {0},分辨率：{0},
Reset OTP Secret,重置OTP密码,
Reset Password,重设密码,
Reset Password Key,重设密码钥匙,
Reset Permissions for {0}?,重置权限{0} ？,
Reset to defaults,重置为默认值,
Reset your password,重置你的密码,
Response Type,响应类型,
Restore,恢复,
Restore Original Permissions,恢复原始权限,
Restore or permanently delete a document.,恢复或永久删除文档。,
Restore to default settings?,恢复到默认设置？,
Restored,恢复,
Restrict IP,限制IP,
Restrict To Domain,限制到域,
Restrict user for specific document,对特定文档限制用户,
Restrict user from this IP address only. Multiple IP addresses can be added by separating with commas. Also accepts partial IP addresses like (111.111.111),仅限制此IP地址的用户。多个IP地址，可以通过用逗号分隔的补充。也接受像（111.111.111）部分的IP地址,
Resume Sending,发送简历,
Retake,重拍,
Retry,重试,
Return to the Verification screen and enter the code displayed by your authentication app,返回验证屏幕，并输入您的身份验证应用程序显示的代码,
Reverse Icon Color,反向图标颜色,
Revert,还原,
Revert Of,还原,
Reverted,回复,
Review Level,审查级别,
Review Levels,评论级别,
Review Points,评论点,
Reviews,评测,
Revoke,撤消,
Revoked,撤销,
Rich Text,富文本,
Robots.txt,robots.txt,
Role Name,角色名称,
Role Permission for Page and Report,角色权限页和报告,
Role Permissions,角色权限,
Role Profile,角色配置文件,
Role and Level,角色和级别,
Roles,角色,
Roles Assigned,角色分配,
Roles can be set for users from their User page.,角色可以从他们的用户页面的用户进行设置。,
Root {0} cannot be deleted,根{0}无法删除,
Round Robin,Round Robin,
Route History,路线历史,
Route Redirects,路线重定向,
Route to Success Link,通往成功链接的路线,
Row,行,
Row #{0}:,行＃{0}：,
Row Index,行索引,
Row No,行号,
Row Status,行状态,
Row Values Changed,行值更改,
Row {0}: Not allowed to disable Mandatory for standard fields,行{0}：不允许禁用标准域的强制性,
Row {0}: Not allowed to enable Allow on Submit for standard fields,行{0}：不允许启用允许对提交的标准字段,
Rows Added,行新增,
Rows Removed,删除行,
Rule,规则,
Rule Name,规则名称,
Rules defining transition of state in the workflow.,规则定义的工作流状态的过渡。,
"Rules for how states are transitions, like next state and which role is allowed to change state etc.",规则的状态是如何过渡，就像下一个状态以及作用是允许改变状态等。,
Run,跑,
Run scheduled jobs only if checked,只有勾选后，才会运行计划任务,
S3 Backup Settings,S3备份设置,
S3 Backup complete!,S3备份完成！,
SMS,短信,
SMS Gateway URL,短信网关的URL,
SMS Parameter,短信参数,
SMS Settings,短信设置,
SMS sent to following numbers: {0},短信发送至以下号码：{0},
SMTP Server,SMTP服务器,
SMTP Settings for outgoing emails,外发邮件的SMTP设置,
"SQL Conditions. Example: status=""Open""",SQL条件。例如：状态=“打开”,
SSL/TLS Mode,SSL / TLS模式,
Salesforce,销售队伍,
Same Field is entered more than once,相同字段不止一次输入,
Save API Secret: ,保存API密码：,
Save As,另存为,
Save Filter,保存过滤器,
Save Report,保存报告,
Save filters,保存过滤器,
Saving,保存,
Saving...,保存...,
Scan the QR Code and enter the resulting code displayed.,扫描QR码并输入显示的结果代码。,
Scopes,领域,
Script,脚本,
Script Report,脚本报告,
Script or Query reports,脚本或查询报告,
Script to attach to all web pages.,插入到所有网页的脚本,
Search Fields,搜索字段,
Search Help,搜索帮助,
Search field {0} is not valid,搜索栏{0}无效,
Search for '{0}',搜索“{0}”,
Search for anything,搜索任何内容,
Search in a document type,在文档类型中搜索,
Search or Create a New Chat,搜索或创建一个新的聊天,
Search or type a command,搜索或输入命令,
Search...,搜索...,
Searching,搜索,
Searching ...,正在搜寻...,
Section Break,分节符,
Section Heading,标题节,
Security,安全,
Security Settings,安全设置,
See all past reports.,查看所有过去的报告。,
See on Website,查看网站,
See the document at {0},请参阅{0}处的文档,
Seems API Key or API Secret is wrong !!!,似乎API密钥或API的秘密是错误的！,
Seems Publishable Key or Secret Key is wrong !!!,似乎可发布的密钥或秘密密钥错误！,
"Seems issue with server's razorpay config. Don't worry, in case of failure amount will get refunded to your account.",似乎与服务器的配置razorpay问题。别担心，在失败量情况将得到退还到您的帐户。,
Seems token you are using is invalid!,似乎你使用的令牌是无效的！,
Seen,可见,
Seen By,看到通过,
Seen By Table,通过看表,
Select Attachments,选择附件,
Select Child Table,选择子表,
Select Column,选择列,
Select Columns,选择列,
Select Document Type,选择文档类型,
Select Document Type or Role to start.,选择文档类型或角色来开始。,
Select Document Types to set which User Permissions are used to limit access.,选择用户权限针对的文档类型。,
Select File Format,选择文件格式,
Select File Type,选择文件类型,
Select Language...,选择语言...,
Select Languages,选择语言,
Select Module,选择模块,
Select Print Format,选择打印格式,
Select Print Format to Edit,选择要编辑的打印格式,
Select Role,选择角色,
Select Table Columns for {0},选择{0}的列,
Select Your Region,选择您的地区,
Select a Brand Image first.,首先选择一个品牌形象。,
Select a DocType to make a new format,请选择新格式的原始文档类型,
Select a chat to start messaging.,选择一个聊天开始消息。,
Select a group node first.,请先选择一个组节点。,
Select an existing format to edit or start a new format.,选择现有格式或新建一个格式。,
Select an image of approx width 150px with a transparent background for best results.,为了最佳效果，请选择一个宽度约150像素，背景透明的图片。,
Select atleast 1 record for printing,选择打印ATLEAST 1项纪录,
Select or drag across time slots to create a new event.,选择或拖动整个时隙，以创建一个新的事件。,
Select records for assignment,选择分配记录,
Select the label after which you want to insert new field.,选择标签，在其后插入新字段。,
"Select your Country, Time Zone and Currency",设置你的国家，时区和货币,
Select {0},选择{0},
Self approval is not allowed,不允许自我批准,
Send After,发送后,
Send Alert On,开启警报发送,
Send Email Alert,发送邮件提醒,
Send Email Print Attachments as PDF (Recommended),使用PDF格式发送打印附件（推荐）,
Send Email for Successful Backup,发送电子邮件以便成功备份,
Send Me A Copy of Outgoing Emails,给我发送外发电子邮件的副本,
Send Notification to,发送通知给,
Send Notifications To,将通知发给,
Send Print as PDF,使用PDF格式发送打印,
Send Read Receipt,发送阅读回执,
Send Unsubscribe Link,发送退订链接,
Send Welcome Email,发送欢迎电子邮件,
Send alert if date matches this field's value,如果日期匹配该字段的值则发送警报,
Send alert if this field's value changes,如果这个字段的值改变则发送警报,
Send an email reminder in the morning,在早上发送邮件提醒,
Send days before or after the reference date,之前或基准日后发送天,
Send enquiries to this email address,向此邮件地址发送询价,
Send me a copy,给我发一份拷贝,
Send only if there is any data,仅发送，如果有任何数据,
Send unsubscribe message in email,发送电子邮件退订消息,
Sender,发件人,
Sender Email,发件人电子邮件,
Sendgrid,Sendgrid,
Sent Read Receipt,发送阅读回执,
Sent or Received,已发送或已接收,
Sent/Received Email,已发送/已接收电子邮件,
Server IP,服务器IP,
Session Expired,会话已过期,
Session Expiry,会话过期,
Session Expiry Mobile,会话过期移动,
Session Expiry in Hours e.g. 06:00,会话过期的时间(小时)，例如06:00,
Session Expiry must be in format {0},会话到期格式必须是{0},
Session Start Failed,会话开始失败,
Set Banner from Image,从图像设置横幅,
Set Chart,设置图表,
Set Filters,设置过滤器,
Set New Password,设置新密码,
Set Number of Backups,设置备份数量,
Set Only Once,仅设置一次,
Set Password,设置密码,
Set Permissions,设置权限,
Set Permissions on Document Types and Roles,为文件类型和角色设置权限,
Set Property After Alert,警报后设置属性,
Set Quantity,设置数量,
Set Role For,为...设置角色,
Set User Permissions,设置用户权限,
Set Value,设定值,
Set custom roles for page and report,对于页面和报表设置自定义角色,
"Set default format, page size, print style etc.",设置默认格式，页面大小，打印样式等。,
Set non-standard precision for a Float or Currency field,为浮点或货币字段设置非标准的精度。,
Set numbering series for transactions.,为交易设置编号系列。,
Set up rules for user assignments.,为用户分配设置规则。,
Setting this Address Template as default as there is no other default,将此地址模板设置为默认，因为没有其他的默认项,
Setting up your system,设置您的系统,
Settings for About Us Page.,关于我们的页面的设置。,
Settings for Contact Us Page,联系我们页面的设置,
Settings for Contact Us Page.,联系我们页面的设置。,
Settings for OAuth Provider,对开放式验证的提供商的设置,
Settings for the About Us Page,关于我们页面的设置,
Setup Auto Email,设置自动电子邮件,
Setup Complete,设置完成,
Setup Notifications based on various criteria.,基于各种标准的设置通知。,
Setup Reports to be emailed at regular intervals,设置报告到定期通过电子邮件发送,
"Setup of top navigation bar, footer and logo.",顶部导航栏，页脚和Logo的设置。,
Share,分享,
Share URL,分享网址,
Share With,分享,
Share this document with,分享这个文件,
Share {0} with,分享{0},
Shared,共享,
Shared With,共享,
Shared with everyone,共享给所有人,
Shared with {0},共享{0},
Shop,商店,
Short keyboard patterns are easy to guess,短键盘模式容易被猜中,
Show Attachments,显示附件,
Show Calendar,显示日历,
Show Dashboard,显示仪表板,
Show Full Error and Allow Reporting of Issues to the Developer,显示完整错误并允许向开发人员报告问题,
Show Line Breaks after Sections,章节后，显示换行符,
Show Permissions,显示权限,
Show Preview Popup,显示预览弹出窗口,
Show Relapses,显示复发,
Show Report,显示报告,
Show Section Headings,显示部分标题,
Show Sidebar,显示侧边栏,
Show Title,显示标题,
Show Totals,显示总计,
Show Weekends,显示周末,
Show all Versions,显示所有版本,
Show as Grid,显示为网格,
Show as cc,显示为抄送,
Show failed jobs,显示失败的作业,
Show in Module Section,在显示模块部分显示,
Show in filter,在过滤器中显示,
Show more details,显示更多详情,
Show only errors,只显示错误,
"Show title in browser window as ""Prefix - title""",标题显示在浏览器窗口中的“前缀 - 标题”,
Showing only Numeric fields from Report,仅显示来自报告的数字字段,
Sidebar Items,边栏项目,
Sidebar Settings,侧边栏的设置,
Sidebar and Comments,边栏和评论,
Sign Up,注册,
Sign Up is disabled,注册被禁用,
Signature,签名,
"Simple Python Expression, Example: Status in (""Closed"", ""Cancelled"")",简单的Python表达式，示例：状态（“已关闭”，“已取消”）,
"Simple Python Expression, Example: status == 'Open' and type == 'Bug'",简单的Python表达式，例如：status ==&#39;Open&#39;并输入==&#39;Bug&#39;,
Simultaneous Sessions,并发会话,
Single DocTypes cannot be customized.,单个DocType无法自定义。,
Single Post (article).,单个帖子（文章）。,
Single Types have only one record no tables associated. Values are stored in tabSingles,单独类型只有一条记录，没有表格。它的值将保存在tabSingles内。,
Skip Authorization,跳过授权,
Skip rows with errors,跳过有错误的行,
Skype,Skype,
Slack,松弛,
Slack Channel,松弛频道,
Slack Webhook Error,Slack Webhook错误,
Slack Webhook URL,Slack Webhook网址,
Slack Webhooks for internal integration,Slack Webhooks用于内部集成,
Slideshow Items,幻灯片项目,
Slideshow Name,幻灯片名称,
Slideshow like display for the website,像幻灯片那样显示，用于网站。,
Small Text,短文,
Smallest Currency Fraction Value,最小的货币分数值,
Smallest circulating fraction unit (coin). For e.g. 1 cent for USD and it should be entered as 0.01,最小的循环部分单元（硬币）。对于如1％用于美元，因此应输入为0.01,
Snapshot View,快照视图,
Social,社会,
Social Login Key,社交登录密钥,
Social Login Provider,社交登录提供商,
Social Logins,社交登录,
Socketio is not connected. Cannot upload,Socketio未连接。无法上传,
Soft-Bounced,软退回,
Some of the features might not work in your browser. Please update your browser to the latest version.,您的浏览器中的某些功能可能无法正常工作。请将您的浏览器更新到最新版本。,
Something went wrong,出了些问题,
Something went wrong while generating dropbox access token. Please check error log for more details.,在生成dropbox访问令牌时发生错误。请检查错误日志以获取更多详细信息。,
Sorry! I could not find what you were looking for.,抱歉，无法找你要的信息。,
Sorry! Sharing with Website User is prohibited.,抱歉，已禁止和网站用户共享。,
Sorry! User should have complete access to their own record.,抱歉，用户必须对自己的记录拥有完全的访问权。,
Sorry! You are not permitted to view this page.,抱歉，你不允许查看此页面。,
"Sorry, you're not authorized.",对不起，你没有授权。,
Sort Field,字段排序,
Sort Order,排序,
Sort field {0} must be a valid fieldname,排序字段{0}必须是有效的字段名,
Source Text,源文本,
Spam,垃圾邮件,
SparkPost,SparkPost,
Special Characters are not allowed,特殊字符是不允许,
"Standard DocType cannot have default print format, use Customize Form",不能为标准文件类型设置默认打印格式，请使用自定义表单,
Standard Print Format cannot be updated,不能更新标准打印格式,
Standard Print Style cannot be changed. Please duplicate to edit.,标准打印样式无法更改。请重复编辑。,
Standard Reports,标准报告,
Standard Sidebar Menu,标准工具栏菜单,
Standard roles cannot be disabled,标准的角色不能被禁用,
Standard roles cannot be renamed,标准的角色不能被重命名,
Standings,排名,
Start Date Field,开始日期字段,
Start a conversation.,开始对话。,
Start entering data below this line,请在此线下开始输入数据,
Start new Format,开始新的格式,
StartTLS,启动TLS,
Started,入门,
Starting Frappe ...,开始Frappé...,
Starts on,开始于,
States,状况,
"States for workflow (e.g. Draft, Approved, Cancelled).",工作流程状况（例如草稿，已批准，已取消） 。,
Static Parameters,静态参数,
Stats based on last month's performance (from {0} to {1}),基于上个月表现的统计数据（从{0}到{1}）,
Stats based on last week's performance (from {0} to {1}),基于上周表现的统计数据（从{0}到{1}）,
Status: {0},状态：{0},
Steps to verify your login,验证您的登录的步骤,
Stores the JSON of last known versions of various installed apps. It is used to show release notes.,储存各种已安装的应用程序的最后为人所知的版本的JSON。它是用来显示发布说明。,
Straight rows of keys are easy to guess,键的直排容易被猜中,
Stripe Settings,条纹设置,
Stripe payment gateway settings,条纹支付网关设置,
Style,风格,
Style Settings,样式设置,
"Style represents the button color: Success - Green, Danger - Red, Inverse - Black, Primary - Dark Blue, Info - Light Blue, Warning - Orange",风格代表按钮的颜色：成功 - 绿色，危险 - 红色，逆向 - 黑色，主要 - 深蓝色，信息 - 浅蓝，警告 - 橙色,
Stylesheets for Print Formats,打印格式样式表,
"Sub-currency. For e.g. ""Cent""",子货币，例如分,
Sub-domain provided by erpnext.com,由erpnext.com提供的子域名,
Subdomain,子域名,
Subject Field,主题字段,
Submit after importing,导入后提交,
Submit an Issue,提交问题,
Submit this document to confirm,提交该文件以确认,
Submit {0} documents?,提交{0}文件？,
Submitting {0},正在提交{0},
Submitted Document cannot be converted back to draft. Transition row {0},行{0}中的已提交的文档不能转换为草稿。,
Submitting,提交,
Subscription Notification,订阅通知,
Subsidiary,子机构,
Success Action,成功行动,
Success Message,成功消息,
Success URL,成功URL,
Successful: {0} to {1},成功：{0} {1},
Successfully Done,成功完成,
Successfully Updated,更新成功,
Successfully updated translations,成功更新翻译,
Suggested Username: {0},建议用户名：{0},
Sum,和,
Sum of {0},{0}的总和,
Support Email Address Not Specified,支持未指定的电子邮件地址,
Suspend Sending,暂停发送,
Switch To Desk,切换到主页（桌面）,
Symbol,符号,
Sync,同步,
Sync on Migrate,同步上迁移,
Syntax error in template,模板语法错误,
System,系统,
System Page,系统页面,
System Settings,系统设置,
System User,系统用户,
System and Website Users,系统和网站用户,
Table,表,
Table Field,表字段,
Table HTML,表HTML,
Table MultiSelect,表MultiSelect,
Table updated,表更新,
Table {0} cannot be empty,表{0}不能为空,
Take Backup Now,立即备份,
Take Photo,拍照,
Team Members,团队成员,
Team Members Heading,团队成员标题,
Temporarily Disabled,暂时禁用,
Test Email Address,测试电子邮件地址,
Test Runner,测试运行,
Test_Folder,测试_文件夹,
Text,文本,
Text Align,文本对齐,
Text Color,文字颜色,
Text Content,文本内容,
Text Editor,文本编辑器,
Text to be displayed for Link to Web Page if this form has a web page. Link route will be automatically generated based on `page_name` and `parent_website_route`,如果此表单有网页的话，请输入页面链接的显示文本。链接会自动按照`页面名称`和`上级网站链接`生成,
Thank you for your email,谢谢您的邮件,
Thank you for your interest in subscribing to our updates,感谢您订阅我们的更新,
Thank you for your message,谢谢您的留言,
The CSV format is case sensitive,CSV格式区分大小写,
The Condition '{0}' is invalid,条件“{0}”无效,
The First User: You,第一个用户：您,
"The application has been updated to a new version, please refresh this page",该应用程序已被更新到新版本，请刷新本页面,
The attachments could not be correctly linked to the new document,附件无法正确链接到新文档,
The document could not be correctly assigned,无法正确分配文档,
The document has been assigned to {0},该文档已分配给{0},
The first user will become the System Manager (you can change this later).,第一个用户将成为系统管理员，您以后可以更改。,
The name that will appear in Google Calendar,将显示在Google日历中的名称,
The process for deletion of {0} data associated with {1} has been initiated.,删除与{1}关联的{0}数据的过程已启动。,
The resource you are looking for is not available,您正在查找的资源不可用,
The system provides many pre-defined roles. You can add new roles to set finer permissions.,该系统提供了许多预先定义的角色。您可以添加新的角色设定更精细的权限。,
The user from this field will be rewarded points,来自此字段的用户将获得奖励积分,
Theme,主题,
Theme URL,主题网址,
There can be only one Fold in a form,一个表单只能有一个折叠,
There is an error in your Address Template {0},在你的地址模板{0}有一个错误,
There is no data to be exported,没有要导出的数据,
There is some problem with the file url: {0},有一些问题与文件的URL：{0},
There must be atleast one permission rule.,至少要包含一个权限规则。,
"There seems to be an issue with the server's braintree configuration. Don't worry, in case of failure, the amount will get refunded to your account.",服务器的braintree配置似乎有问题。别担心，如果失败，这笔款项将退还给您的帐户。,
There should remain at least one System Manager,最少应保留一个系统管理员,
There was an error saving filters,保存过滤器时出错,
There were errors,曾有些错误发生,
There were errors while creating the document. Please try again.,创建文档时曾出错。请再试一次。,
There were errors while sending email. Please try again.,邮件发送曾发生错误，请重试。,
"There were some errors setting the name, please contact the administrator",设置名称时出现错误，请与管理员联系,
These values will be automatically updated in transactions and also will be useful to restrict permissions for this user on transactions containing these values.,这些值将在交易中自动更新，也将是有益的权限限制在含有这些值交易这个用户。,
Third Party Apps,第三方应用,
Third Party Authentication,第三方认证,
This Currency is disabled. Enable to use in transactions,这个币种禁用。在交易栏目中启用,
This Kanban Board will be private,这看板将是私有的,
This document cannot be reverted,此文档无法还原,
This document has been modified after the email was sent.,发送电子邮件后，此文档已被修改。,
This document has been reverted,该文件已被还原,
This document is currently queued for execution. Please try again,这份文件目前正在排队等待执行。请再试一次,
This email is autogenerated,此电子邮件是自动生成的,
This email was sent to {0},这封电子邮件被发送到{0},
This email was sent to {0} and copied to {1},此电子邮件发送到{0}，并复制到{1},
This feature is brand new and still experimental,此功能是全新的，仍处于试验阶段,
This field will appear only if the fieldname defined here has value OR the rules are true (examples):\nmyfield\neval:doc.myfield=='My Value'\neval:doc.age&gt;18,该字段只有在此处定义的字段名具有值或规则是真时才显示（例子）：myfield eval:doc.myfield=='My Value' eval:doc.age&gt;18,
This form does not have any input,这个表单没有任何输入,
This form has been modified after you have loaded it,这个表单在你加载后已被修改,
This format is used if country specific format is not found,如果找不到国家特定格式将采用此格式,
This goes above the slideshow.,这将置在幻灯片上面。,
This is a background report. Please set the appropriate filters and then generate a new one.,这是一份后台报告。请设置适当的过滤器，然后生成一个新过滤器。,
This is a top-10 common password.,这是一个前10名的常用密码。,
This is a top-100 common password.,这是一个前100名的常用密码。,
This is a very common password.,这是一个非常普遍的密码。,
This is an automatically generated reply,这是一个自动生成的回复,
This is similar to a commonly used password.,这类似于一个通常使用的密码。,
This is the template file generated with only the rows having some error. You should use this file for correction and import.,这是只是行有一些错误而生成的模板文件。您应该使用此文件进行更正和导入。,
This link has already been activated for verification.,此链接已激活以进行验证。,
This link is invalid or expired. Please make sure you have pasted correctly.,此链接是无效或过期。请确保你已经正确粘贴。,
This may get printed on multiple pages,这可能会打印在多个页面上,
This month,这个月,
This query style is discontinued,此查询样式已停止,
This report was generated on {0},此报告是在{0}上生成的,
This report was generated {0}.,此报告已生成{0}。,
This request has not yet been approved by the user.,此请求尚未得到用户的批准。,
This role update User Permissions for a user,这个角色更新一个用户的用户权限,
This will log out {0} from all other devices,这将从所有其他设备注销{0},
This will permanently remove your data.,这将永久删除您的数据。,
Throttled,节流,
Thumbnail URL,缩略图网址,
Time Interval,时间间隔,
Time Series,时间序列,
Time Series Based On,基于时间序列,
Time Zone,时区,
Time Zones,时区,
Time in seconds to retain QR code image on server. Min:<strong>240</strong>,在服务器上保留QR码图像的秒数。最小： <strong>240</strong>,
Timeline DocType,时间轴的文件类型,
Timeline Field,时间轴字段,
Timeline Links,时间线链接,
Timeline Name,时间轴名称,
Timeline field must be a Link or Dynamic Link,时间轴字段必须是一个链接或动态链接,
Timeline field must be a valid fieldname,时间轴场必须是有效的字段名,
Timeseries,时间序列,
Timestamp,时间戳,
Title Case,标题案例,
Title Field,标题字段,
Title Prefix,标题前缀,
Title field must be a valid fieldname,标题字段必须是有效的字段名,
To Date Field,到日期字段,
To Do,待办事项,
To User,给用户,
"To add dynamic subject, use jinja tags like\n\n<div><pre><code>New {{ doc.doctype }} #{{ doc.name }}</code></pre></div>",要添加动态主题，请使用jinja标签<div><pre> <code>New {{ doc.doctype }} #{{ doc.name }}</code> </pre> </div>,
"To add dynamic subject, use jinja tags like\n\n<div><pre><code>{{ doc.name }} Delivered</code></pre></div>",要添加动态主题，用神社标签，如<div><pre> <code>{{ doc.name }} Delivered</code> </pre> </div>,
To and CC,收件人和抄送,
"To get the updated report, click on {0}.",要获取更新的报告，请单击{0}。,
ToDo,待办事项,
Today,今天,
Toggle Chart,切换图表,
Toggle Charts,切换图表,
Toggle Grid View,切换网格视图,
Toggle Sidebar,切换边栏,
Token,令牌,
Token is missing,令牌丢失,
"Too many users signed up recently, so the registration is disabled. Please try back in an hour",最近有太多用户注册，导致注册功能被自动临时禁用了，请一个小时后重试。,
Too many writes in one request. Please send smaller requests,请求中包括内容。请发送少些内容的请求,
Top Bar Item,顶栏项目,
Top Bar Items,顶栏项目,
Top Performer,最佳表演者,
Top Reviewer,评论员,
Top {0},最高{0},
Total Pages,总页数,
Total Rows,总行数,
Total Subscribers,用户总数,
Total number of emails to sync in initial sync process ,邮件的总数在初始同步过程同步,
Totals Row,总计行,
Track Changes,跟踪变化,
Track Email Status,跟踪电子邮件状态,
Track Field,田径场,
Track Seen,让系统记录谁查看过,
Track Views,跟踪视图,
"Track if your email has been opened by the recipient.\n<br>\nNote: If you're sending to multiple recipients, even if 1 recipient reads the email, it'll be considered ""Opened""",跟踪收件人是否已打开您的电子邮件。 <br>注意：如果您要向多个收件人发送邮件，即使有1个收件人阅读该邮件，也会被视为“已打开”,
Track milestones for any document,跟踪任何文档的里程碑,
Transaction Hash,交易哈希,
Transaction Log,交易日志,
Transaction Log Report,交易日志报告,
Transition Rules,迁移规则,
Transitions,迁移,
Translatable,可翻译,
Translate {0},翻译{0},
Translated Text,翻译文本,
Translation,翻译,
Translations,翻译,
Trash,垃圾,
Tree,树,
Trigger Method,触发方式,
Trigger Name,触发器名称,
"Trigger on valid methods like ""before_insert"", ""after_update"", etc (will depend on the DocType selected)",在有效的方法如：“before_insert”，“after_update”触发（取决于所选的文档类型）,
Try to avoid repeated words and characters,尽量避免重复的单词和字符,
Try to use a longer keyboard pattern with more turns,尝试使用更多的匝数较长的键盘模式,
Two Factor Authentication,双因素认证,
Two Factor Authentication method,双因素认证方法,
Type something in the search box to search,在搜索框中输入一些文字开始搜索,
Type:,类型：,
UID,UID,
UIDNEXT,UIDNEXT,
UIDVALIDITY,UIDVALIDITY,
UNSEEN,反转已查看,
UPPER CASE,大写字母,
"URIs for receiving authorization code once the user allows access, as well as failure responses. Typically a REST endpoint exposed by the Client App.\n<br>e.g. http://hostname//api/method/frappe.www.login.login_via_facebook",的URI，用于接收授权代码一旦用户允许访问，以及失败的响应。通常，REST端点的客户端应用程序暴露出来。 <br>例如http：//hostname//api/method/frappe.www.login.login_via_facebook,
URLs,网址,
Unable to find attachment {0},找不到附件{0},
Unable to load camera.,无法加载相机。,
Unable to load: {0},无法加载： {0},
Unable to open attached file. Did you export it as CSV?,无法打开附加的文件。导出为CSV？,
Unable to read file format for {0},无法读取{0}的文件格式,
Unable to send emails at this time,暂时无法发送电子邮件,
Unable to update event,无法更新事件,
Unable to write file format for {0},无法写入{0}的文件格式,
Unassign Condition,取消分配条件,
Under Development,正在开发中,
Unfollow,取消关注,
Unhandled Email,未处理的邮件,
Unique,唯一,
Unknown Column: {0},未知列： {0},
Unknown User,未知用户,
"Unknown file encoding. Tried utf-8, windows-1250, windows-1252.",未知文件编码。已经尝试使用UTF-8，windows-1250，windows-1252编码。,
Unread,未读,
Unread Notification Sent,未读发送通知,
Unselect All,全部取消选择,
Unshared,非共享,
Unsubscribe,退订,
Unsubscribe Method,退订方法,
Unsubscribe Param,退订参数,
Unsupported File Format,不支持的文件格式,
Unzip,解压缩,
Unzipped {0} files,解压缩{0}个文件,
Unzipping files...,解压缩文件...,
Upcoming Events for Today,今日活动,
Update Field,更新字段,
Update Translations,更新翻译,
Update Value,更新值,
Update many values at one time.,同时更新多个值。,
Update records,更新记录,
Updated,已更新,
Updated successfully,更新成功,
Updated {0}: {1},更新{0}：{1},
Updating,更新,
Updating {0},更新{0},
Upload Failed,上传失败,
Uploaded To Dropbox,上传到Dropbox,
Use ASCII encoding for password,使用ASCII编码作为密码,
Use Different Email Login ID,使用不同的电子邮件登录ID,
Use IMAP,使用IMAP,
Use POST,使用POST,
Use SSL,使用SSL,
Use TLS,使用TLS,
"Use a few words, avoid common phrases.",用几个字，避免常用短语。,
Use of sub-query or function is restricted,子查询或功能的使用受到限制,
Use socketio to upload file,使用socketio上传文件,
Use this fieldname to generate title,使用该字段名来生成标题,
User '{0}' already has the role '{1}',用户“{0}”已经拥有了角色“{1}”,
User Cannot Create,用户无法创建,
User Cannot Search,用户不能搜索,
User Defaults,用户默认,
User Email,用户电子邮件,
User Emails,用户电子邮件,
User Field,用户字段,
User ID of a Blogger,博客作者的用户ID,
User Image,用户图片,
User Name,用户名,
User Permission,用户权限,
User Permissions,用户权限,
User Permissions are used to limit users to specific records.,用户权限用于将用户限制到特定的记录。,
User Permissions created sucessfully,用户权限已成功创建,
User Roles,用户角色,
User Social Login,用户社交登录,
User Tags,用户标签,
User Type,用户类型,
User can login using Email id or Mobile number,用户可以使用电子邮件ID或手机号登录,
User can login using Email id or User Name,用户可以使用电子邮件ID或用户名登录,
User editable form on Website.,网站上的用户可编辑表单,
User is mandatory for Share,请选择要分享的用户,
User not allowed to delete {0}: {1},用户不能删除{0}：{1},
User permission already exists,用户权限已经存在,
User permissions should not apply for this Link,用户权限不应应用在此链接上,
User {0} cannot be deleted,用户{0}不能被删除,
User {0} cannot be disabled,用户{0}不能被禁用,
User {0} cannot be renamed,用户{0}无法被重命名,
User {0} does not have access to this document,用户{0}无权访问此文档,
User {0} does not have doctype access via role permission for document {1},用户{0}没有通过文档{1}的角色权限访问doctype,
Username,用户名,
Username {0} already exists,用户名{0}已存在,
Users with role {0}:,拥有角色{0}的用户：,
Uses the Email Address Name mentioned in this Account as the Sender's Name for all emails sent using this Account.,使用此帐户中提到的电子邮件地址名称作为使用此帐户发送的所有电子邮件的发件人姓名。,
Uses the Email Address mentioned in this Account as the Sender for all emails sent using this Account. ,使用此帐号的邮箱作为所有外发电子邮件的发件人。,
Valid,有效,
Valid Login id required.,请输入有效的登录ID。,
Valid email and name required,需要有效的电子邮件和姓名,
Value Based On,价值观基于,
Value Change,更改值,
Value Changed,值已更改,
Value To Be Set,价值待定,
Value cannot be changed for {0},值不能被改变为{0},
Value for a check field can be either 0 or 1,选项的值可以是0或1,
Value for {0} cannot be a list,{0}不能是列表值,
Value missing for,缺少值,
Value too big,值过大,
Values Changed,值已更改,
Verdana,宋体,
Verfication Code,验证码,
Verification Link,验证链接,
Verification code has been sent to your registered email address.,验证码已发送到您注册的电子邮件地址。,
Verify,确认,
Verify Password,确认密码,
Verifying...,验证中...,
Version,版本,
Version Updated,版本更新,
View All,查看全部,
View Comment,查看评论,
View List,查看列表,
View Log,查看日志,
View Permitted Documents,查看允许的文件,
View Properties (via Customize Form),视图属性（通过自定义窗体）,
View Settings,视图设置,
View Website,查看网站,
View document,查看文档,
View report in your browser,在浏览器中查看报告,
View this in your browser,在浏览器查看,
View {0},查看{0},
Viewed By,由...观看,
Visit,访问,
Visitor,游客,
We have received a request for deletion of {0} data associated with: {1},我们收到了删除与以下相关的{0}数据的请求：{1},
We have received a request from you to download your {0} data associated with: {1},我们已收到您的请求，要求您下载与{1}相关联的{0}数据,
Web Form,Web表单,
Web Form Field,Web表单字段,
Web Form Fields,Web表单字段,
Web Page,网页,
Web Page Link Text,网页链接文本,
Web Site,网站,
Web View,Web视图,
Webhook,网络挂接,
Webhook Data,Webhook数据,
Webhook Header,Webhook标题,
Webhook Headers,Webhook标题,
Webhook Request,Webhook请求,
Webhook URL,Webhook URL,
Webhooks calling API requests into web apps,Webhook将API请求调用到Web应用程序中,
Website Meta Tag,网站元标记,
Website Route Meta,网站路线元,
Website Route Redirect,网站路线重定向,
Website Script,网站脚本,
Website Sidebar,网站边栏,
Website Sidebar Item,网站侧栏项目,
Website Slideshow,网站幻灯片,
Website Slideshow Item,网站幻灯片项目,
Website Theme,网站主题,
Website Theme Image,网站主题图片,
Website Theme Image Link,网站主题图像链接,
Website User,网站用户,
Welcome Message,欢迎消息,
"When you Amend a document after Cancel and save it, it will get a new number that is a version of the old number.",当你修订一个已取消和保存的文档时，文档会复制为一个副本。,
Width,宽度,
Widths can be set in px or %.,宽度可以设置为px像素或%百分比。,
Will be used in url (usually first name).,会用在URL链接中（通常是名字）。,
Will be your login ID,将是您的登录ID,
Will only be shown if section headings are enabled,如果章节标题启用才会显示,
With Letter head,随着信头,
With Letterhead,带信头,
Workflow Action,工作流操作,
Workflow Action Master,工作流操作主表,
Workflow Action Name,工作流操作名称,
Workflow Document State,工作流文档状态,
Workflow Name,工作流名称,
Workflow State,工作流状态,
Workflow State Field,工作流状态字段,
Workflow State not set,工作流程状态未设置,
Workflow Transition,工作流程转换,
Workflow state represents the current state of a document.,工作流状态表明文档目前的状态。,
Write,写,
Wrong fieldname <b>{0}</b> in add_fetch configuration of custom script,自定义脚本的add_fetch配置中的字段名为<b>{0}</b>,
X Axis Field,X轴字段,
XLSX,XLSX,
Y Axis Fields,Y轴字段,
Yahoo Mail,雅虎邮箱,
Yandex.Mail,Yandex.Mail,
Yesterday,昨天,
You are connected to internet.,你已连接到互联网。,
You are not allowed to create columns,你无权来创建列,
You are not allowed to delete a standard Website Theme,你不允许删除标准的网站主题,
You are not allowed to print this document,你不允许打印此文档,
You are not allowed to print this report,您不允许打印此报告,
You are not allowed to send emails related to this document,你不允许发送与此文档相关的电子邮件,
You are not allowed to update this Web Form Document,你不允许更新此Web窗体文件,
You are not connected to Internet. Retry after sometime.,你没有连接到互联网。在某个时间后重试。,
You are not permitted to access this page.,你没有权限访问此页面。,
You are not permitted to view the newsletter.,您不能查看简报。,
You are now following this document. You will receive daily updates via email. You can change this in User Settings.,您现在正在关注此文档。您将通过电子邮件收到每日更新。您可以在“用户设置”中进行更改。,
You can add dynamic properties from the document by using Jinja templating.,您可以通过Jinja模板来添加文档的动态属性。,
You can also copy-paste this ,您也可以复制粘贴它,
"You can change Submitted documents by cancelling them and then, amending them.",您可以通过撤销已提交的文件，然后来修改文档。,
You can find things by asking 'find orange in customers',你可以通过问“找到橙客户找东西,
You can only upload upto 5000 records in one go. (may be less in some cases),一次最多只能上传5000条记录(某些情况下可能更少),
You can use Customize Form to set levels on fields.,你可以使用自定义表单设置字段的级别。,
You can use wildcard %,可以使用通配符％,
You can't set 'Options' for field {0},您不能为字段{0}设置“选项”,
You can't set 'Translatable' for field {0},您无法为字段{0}设置“可翻译”,
You cannot give review points to yourself,你不能给自己提供评论点,
You cannot unset 'Read Only' for field {0},你不能为字段{0}取消“只读”设置,
You do not have enough permissions to access this resource. Please contact your manager to get access.,您没有足够的权限来访问该资源。请联系您的经理，以获得访问权。,
You do not have enough permissions to complete the action,您没有足够权限执行此项任务,
You do not have enough points,你没有足够的积分,
You do not have enough review points,您没有足够的评论点,
You don't have access to Report: {0},您没有权限访问报表：{0},
You don't have any messages yet.,你还没有任何消息。,
You don't have permission to access this file,您没有权限访问该文件,
You don't have permission to get a report on: {0},你没有权限获得{0}的报表,
You don't have the permissions to access this document,您没有访问此文件的权限,
You gained {0} point,你获得了{0}分,
You gained {0} points,你获得了{0}分,
You have a new message from: ,您收到以下新消息：,
You have been successfully logged out,您已成功注销,
You have unsaved changes in this form. Please save before you continue.,本表单中存在未保存的修改，请先保存后继续使用。,
You must login to submit this form,您必须登录才能提交此表单,
You need to be in developer mode to edit a Standard Web Form,你需要在开发模式编辑标准Web窗体,
You need to be logged in and have System Manager Role to be able to access backups.,您需要先登录，并具有系统管理员角色才能够访问备份。,
You need to be logged in to access this {0}.,您需要登录才能访问此{0}。,
"You need to have ""Share"" permission",你需要有“共享”权限,
You need write permission to rename,你需要写权限来重命名,
You selected Draft or Cancelled documents,您选择了草稿或已取消的文档,
You unfollowed this document,你取消了这份文件,
Your Country,你的国家,
Your Language,你的语言,
Your Name,您的姓名,
Your account has been locked and will resume after {0} seconds,您的帐户已被锁定，并将在{0}秒后恢复,
Your connection request to Google Calendar was successfully accepted,您的Google日历连接请求已成功接受,
Your information has been submitted,您的信息已提交,
Your login id is,您的登录ID是,
Your organization name and address for the email footer.,电子邮件页脚上您的组织名称和地址。,
Your payment has been successfully registered.,您的付款已成功注册。,
Your payment has failed.,您的付款失败。,
Your payment is cancelled.,您的付款已取消。,
Your payment was successfully accepted,您的付款已成功接受,
"Your query has been received. We will reply back shortly. If you have any additional information, please reply to this mail.",您的问题已收到。我们将尽快回复邮件。如果您还有任何其他的信息，请回覆此邮件。,
"Your session has expired, please login again to continue.",您的会话已过期，请再次登录以继续。,
Zero,零,
Zero means send records updated at anytime,零表示随时更新发送记录,
_doctype,_doctype,
_report,_报告,
adjust,调整,
after_insert,after_insert,
align-center,中心对准,
align-justify,段落两端对齐,
align-left,左对齐,
align-right,右对齐,
ap-northeast-1,AP-东北-1,
ap-northeast-2,AP-东北-2,
ap-northeast-3,AP-东北-3,
ap-south-1,AP-南1,
ap-southeast-1,AP-东南-1,
ap-southeast-2,AP-东南-2,
arrow-down,向下箭头,
arrow-left,向左箭头,
arrow-right,向右箭头,
arrow-up,向上箭头,
asterisk,星号,
backward,向后,
ban-circle,禁至圆圈,
bell,铃声,
bookmark,书签,
briefcase,公文包,
bullhorn,扩音器,
ca-central-1,CA-中央-1,
camera,相机,
cancelled this document,取消此文件,
changed value of {0},的改变后的值{0},
changed values for {0},用于改变的值{0},
chevron-down,V形向下,
chevron-left,V形向左,
chevron-right,V形向右,
chevron-up,V形向上,
circle-arrow-down,圆圈箭头向下,
circle-arrow-left,圆圈箭头向左,
circle-arrow-right,圆圈箭头向右,
circle-arrow-up,圆圈箭头向上,
cn-north-1,CN  - 北 -  1,
cn-northwest-1,CN-西北-1,
cog,系统中的一小部分,
darkgrey,深灰色,
dd-mm-yyyy,日-月-年,
dd.mm.yyyy,日.月.年,
dd/mm/yyyy,日/月/年,
"document type..., e.g. customer",文档类型...，如客户,
domain name,域名,
download-alt,download-alt,
"e.g. ""Support"", ""Sales"", ""Jerry Yang""",例如“支持“，”销售“，”杨杰“,
e.g. (55 + 434) / 4 or =Math.sin(Math.PI/2)...,例如：（55 + 434）/ 4 =或Math.sin（Math.PI / 2）...,
e.g. pop.gmail.com / imap.gmail.com,例如pop.gmail.com / imap.gmail.com,
e.g. <EMAIL>. All replies will come to this inbox.,例如如**********************。所有的回复将被发送到这个收件箱。,
e.g. smtp.gmail.com,例如smtp.gmail.com,
e.g.:,例如：,
eject,退出,
envelope,envelope,
eu-central-1,EU-中央-1,
eu-north-1,欧盟 - 北 -  1,
eu-west-1,欧盟 - 西1,
eu-west-2,欧盟 - 西2,
eu-west-3,欧盟 - 西3号,
exclamation-sign,惊叹号,
eye-close,眼开,
eye-open,眼闭,
facetime-video,facetime-video,
fairlogin,直接登录,
fast-backward,快退,
fast-forward,快进,
film,film,
fire,fire,
folder-close,文件夹-关闭,
folder-open,文件夹-打开,
fullscreen,全屏,
gained by {0} via automatic rule {1},由{0}通过自动规则{1}获得,
gained {0} points,获得{0}分,
gave {0} points,给了{0}分,
gift,礼物,
glass,玻璃,
globe,全局,
hand-down,传下来的,
hand-left,hand-left,
hand-right,hand-right,
hand-up,hand-up,
hdd,hdd,
headphones,头戴耳机,
heart,心脏,
hub,集线器,
indent-left,靠左缩进,
indent-right,靠右缩进,
info-sign,info-sign,
italic,斜体,
<EMAIL>,<EMAIL>,
just now,刚刚,
leaf,叶子,
lightblue,浅蓝,
list-alt,备选清单,
magnet,magnet,
map-marker,映射-记号,
merged {0} into {1},{0}合并为{1},
minus,minus,
minus-sign,减号-标记,
mm-dd-yyyy,月-日-年,
mm/dd/yyyy,月/日/年,
module name...,模块的名称...,
new type of document,新类型的文件,
no failed attempts,没有失败的尝试,
none of,没有,
ok,ok,
ok-circle,ok-circle,
ok-sign,ok-sign,
on_cancel,on_cancel,
on_change,on_change,
on_submit,on_submit,
on_trash,on_trash,
on_update,on_update,
on_update_after_submit,on_update_after_submit,
only.,而已。,
or,或,
pause,暂停,
pencil,铅笔,
picture,图片,
plane,机,
play,玩,
play-circle,玩圈,
plus,加,
plus-sign,加号,
qrcode,二维码,
query-report,查询的报告,
question-sign,问题-签名,
remove-circle,删除圈,
remove-sign,删除符号,
removed,去除,
renamed from {0} to {1},从更名{0}到{1},
repeat,重复,
resize-full,调整全屏,
resize-horizontal,调整大小，水平,
resize-small,调整大小小,
resize-vertical,调整垂直,
restored {0} as {1},恢复{0}为{1},
retweet,转推,
road,道路,
sa-east-1,SA-东1,
screenshot,截屏,
share-alt,share-alt,
shopping-cart,购物车,
show,显示,
signal,signal,
star,star,
star-empty,star-empty,
step-backward,step-backward,
step-forward,step-forward,
submitted this document,提交这份文件,
text in document type,文件类型的文本,
text-height,文字高度,
text-width,文字宽度,
th,th,
th-large,th-large,
th-list,th-list,
thumbs-down,拇指向下,
thumbs-up,竖起大拇指,
tint,色彩,
toggle Tag,切换标签,
updated to {0},更新{0},
us-east-1,美国 - 东 -  1,
us-east-2,美东2,
us-west-1,美西1,
us-west-2,美西2,
use % as wildcard,使用％作为通配符,
values separated by commas,用逗号分隔的值,
via automatic rule {0} on {1},通过{1}上的自动规则{0},
viewed,观看,
volume-down,降低音量,
volume-off,关闭音量,
volume-up,提升音量,
warning-sign,警告标识,
wrench,wrench,
yyyy-mm-dd,年-月-日,
zoom-in,放大,
zoom-out,缩小,
{0} Calendar,{0}日历,
{0} Chart,{0}图表,
{0} Dashboard,{0}仪表板,
{0} List,{0}列表,
{0} Modules,{0}模块,
{0} Report,{0}报告,
{0} Settings not found,{0}没有找到设置,
{0} Tree,{0} 树,
{0} added,{0} 成功添加,
{0} already exists. Select another name,{0}已存在。选择其他名称,
{0} already unsubscribed,{0}已经退订,
{0} already unsubscribed for {1} {2},{0}已经退订了{1} {2},
{0} and {1},{0}和{1},
{0} appreciated on {1},{0}赞赏{1},
{0} appreciated your work on {1} with {2} point,{0}赞赏您在{1}点上的工作{2},
{0} appreciated your work on {1} with {2} points,{0}赞赏您使用{2}积分在{1}上的工作,
{0} appreciated {1},{0}赞赏{1},
{0} appreciation point for {1} {2},{0} {1} {2}的升值点,
{0} appreciation points for {1} {2},{0} {1} {2}的升值点,
{0} assigned {1}: {2},{0}已分配{1}：{2},
{0} cannot be set for Single types,{0}不能设置为单例类型,
{0} comments,{0}条评论,
{0} created successfully,{0}已成功创建,
{0} criticism point for {1} {2},{0}对{1} {2}的批评点,
{0} criticism points for {1} {2},{0}对{1} {2}的批评点,
{0} criticized on {1},{0}批评{1},
{0} criticized your work on {1} with {2} point,{0}以{2}点批评了您在{1}上的工作,
{0} criticized your work on {1} with {2} points,{0}以{2}分批评你在{1}上的工作,
{0} criticized {1},{0}批评{1},
{0} days ago,{0}天前,
{0} does not exist in row {1},{0}不存在于行{1}中,
"{0} field cannot be set as unique in {1}, as there are non-unique existing values",{0}字段不能在{1}中设置为唯一，因为这里存在非唯一的数值,
{0} has already assigned default value for {1}.,{0}已为{1}分配了默认值。,
{0} has been successfully added to the Email Group.,{0} 已经被成功的加入到邮件组之中。,
{0} has left the conversation in {1} {2},{0}已经离开对话{1} {2},
{0} hours ago,{0} 小时前,
{0} in row {1} cannot have both URL and child items,行{1}中的{0}不能同时有URL和子项,
{0} is a mandatory field,{0}是必填字段,
{0} is an invalid email address in 'Recipients',{0}是“收件人”中的无效电子邮件地址,
{0} is not a raw printing format.,{0}不是原始打印格式。,
{0} is not a valid Email Address,{0} 不是有效的邮箱地址,
{0} is not a valid Workflow State. Please update your Workflow and try again.,{0}不是有效的工作流程状态。请更新您的工作流程，然后重试。,
{0} is now default print format for {1} doctype,{0}现在是{1}类型的默认打印格式,
{0} is saved,{0}已保存,
{0} items selected,{0}项目已选择,
{0} logged in,{0}已登录,
{0} logged out: {1},{0} 登出: {1},
{0} minutes ago,{0}分钟前,
{0} months ago,{0}个月前,
{0} must be one of {1},{0}必须属于{1},
{0} must be set first,{0}必须首先设置,
{0} must be unique,{0}必须是唯一的,
{0} not a valid State,{0}不是有效的国家,
{0} not allowed to be renamed,{0}不允许改名,
{0} not found,{0}未找到,
{0} of {1},第{0}项 / 共{1}项,
{0} or {1},{0}或{1},
{0} record deleted,{0}记录已删除,
{0} records deleted,已删除{0}条记录,
{0} reverted your point on {1},{0}在{1}上恢复了你的观点,
{0} reverted your points on {1},{0}在{1}上恢复了积分,
{0} reverted {1},{0}还原{1},
{0} room must have atmost one user.,{0}教室必须最多容纳一个用户。,
{0} rows for {1},{0} 行 {1},
{0} saved successfully,{0}已成功保存,
{0} self assigned this task: {1},{0}自行分配此任务：{1},
{0} shared this document with everyone,{0}与每个人共享了该文件,
{0} shared this document with {1},{0}向{1}共享了这个文件,
{0} subscribers added,{0}新增用户,
{0} to stop receiving emails of this type,{0}停止接收此类邮件,
{0} to {1},{0}到{1},
{0} un-shared this document with {1},{0}关闭了此文件对{1}的共享,
{0} updated,{0}已更新,
{0} weeks ago,{0}周前,
{0} {1} added,已添加{0} {1},
{0} {1} already exists,{0} {1}已经存在,
"{0} {1} cannot be ""{2}"". It should be one of ""{3}""",{0} {1}不能为“{2}”。它应该是一个“{3}”,
{0} {1} cannot be a leaf node as it has children,{0} {1}不能是一个叶节点，因为它有下级,
"{0} {1} does not exist, select a new target to merge",{0} {1}不存在，选择一个新的目标合并,
{0} {1} not found,{0} {1}未找到,
{0} {1} to {2},{0} {1} 到 {2},
"{0}, Row {1}",{0}，列{1},
"{0}: '{1}' ({3}) will get truncated, as max characters allowed is {2}",{0}：“{1}”（{3}）将被截断，因最大允许字符数为{2},
{0}: Cannot set Amend without Cancel,{0} ：没有“取消”的情况下不能设置“修订”,
{0}: Cannot set Assign Amend if not Submittable,{0} ：没有“提交”的情况下不能分配“修订”,
{0}: Cannot set Assign Submit if not Submittable,{0} ：没有“提交”的情况下不能分配“提交”,
{0}: Cannot set Cancel without Submit,{0} ：没有“提交”的情况下不能分配“取消”,
{0}: Cannot set Import without Create,{0} ：没有“创建”的情况下不能分配“导入”,
"{0}: Cannot set Submit, Cancel, Amend without Write",{0} ：没有写入的情况下不能设置“提交”，“取消”，“修订”,
{0}: Cannot set import as {1} is not importable,{0} ：{1}无法导入所以不能设置“导入”,
{0}: No basic permissions set,{0} ：基本权限未设置,
"{0}: Only one rule allowed with the same Role, Level and {1}",{0}：具有相同的角色，级别和允许只有一个规则{1},
{0}: Permission at level 0 must be set before higher levels are set,{0} ：更高级别的权限设置前请先设置0级权限,
{0}: {1} in {2},{0}：{1}在{2},
{0}: {1} is set to state {2},{0}：{1}设置为状态{2},
{app_title},{应用名称},
{{{0}}} is not a valid fieldname pattern. It should be {{field_name}}.,{{{0}}}是不是一个有效的字段名模式。它应该是{{FIELD_NAME}}。,
Communication Link,通讯链接,
Force User to Reset Password,强制用户重置密码,
In Days,在天,
Last Password Reset Date,上次密码重置日期,
The password of your account has expired.,您帐户的密码已过期。,
Workflow State transition not allowed from {0} to {1},不允许从{0}到{1}的工作流状态转换,
{0} must be after {1},{0}必须在{1}之后,
{0}: Field '{1}' cannot be set as Unique as it has non-unique values,{0}：字段“{1}”无法设置为“唯一”，因为它具有非唯一值,
{0}: Field {1} in row {2} cannot be hidden and mandatory without default,{0}：行{2}中的字段{1}无法隐藏，并且在没有默认情况下是必需的,
{0}: Field {1} of type {2} cannot be mandatory,{0}：类型{2}的字段{1}不能是必需的,
{0}: Fieldname {1} appears multiple times in rows {2},{0}：字段名{1}在行{2}中多次出现,
{0}: Fieldtype {1} for {2} cannot be unique,{0}：{2}的字段类型{1}不能是唯一的,
{0}: Options must be a valid DocType for field {1} in row {2},{0}：选项必须是行{2}中字段{1}的有效DocType,
{0}: Options required for Link or Table type field {1} in row {2},{0}：行{2}中的链接或表类型字段{1}所需的选项,
{0}: Options {1} must be the same as doctype name {2} for the field {3},{0}：选项{1}必须与字段{3}的文档类型名称{2}相同,
{0}:Fieldtype {1} for {2} cannot be indexed,{0}：无法为{2}的字段类型{1}编制索引,
Make {0},制作{0},
A user who posts blogs.,发布博客的用户。,
Applying: {0},申请：{0},
Fieldname {0} is restricted,字段名{0}受限制,
Is Optional State,是可选国家,
No values to show,没有要显示的值,
View Ref,查看参考,
Workflow Action is not created for optional states,不为可选状态创建工作流操作,
{0} values selected,已选择{0}个值,
"""amended_from"" field must be present to do an amendment.",必须出现“modified_from”字段才能进行修改。,
(Mandatory),（必须）,
1 Google Calendar Event synced.,1个Google日历活动已同步。,
1 record will be exported,将导出1条记录,
1 week ago,1周前,
5 Records,5条记录,
A recurring {0} {1} has been created for you via Auto Repeat {2}.,通过自动重复{2}为您创建了一个重复的{0} {1}。,
API,应用程序界面,
API Method,API方法,
About {0} minute remaining,剩余约{0}分钟,
About {0} minutes remaining,剩余约{0}分钟,
About {0} seconds remaining,剩余约{0}秒,
Access Log,访问日志,
Access not allowed from this IP Address,不允许从该IP地址访问,
Action Type,动作类型,
Activity Log by ,活动记录,
Add Fields,添加字段,
Administration,管理,
After Cancel,取消后,
After Delete,删除后,
After Save,保存后,
After Save (Submitted Document),保存后（提交的文档）,
After Submit,提交后,
Aggregate Function Based On,基于的聚合函数,
Aggregate Function field is required to create a dashboard chart,创建仪表盘图需要“汇总功能”字段,
All Records,所有记录,
Allot Points To Assigned Users,向分配的用户分配点,
Allow Auto Repeat,允许自动重复,
Allow Google Calendar Access,允许Google日历访问权限,
Allow Google Contacts Access,允许Google通讯录访问,
Allow Google Drive Access,允许访问谷歌云端硬盘,
Allow Guest,允许访客,
Allow Guests to Upload Files,允许访客上传文件,
Also adding the status dependency field {0},还添加状态依赖项字段{0},
An error occurred while setting Session Defaults,设置会话默认值时发生错误,
Annual,全年,
Append Emails to Sent Folder,将电子邮件追加到已发送文件夹,
Apply Assignment Rule,应用分配规则,
Apply Only Once,仅申请一次,
Apply this rule only once per document,每个文档仅应用一次此规则,
Approval Required,需要批准,
Approved,已批准,
Are you sure you want to delete all rows?,您确定要删除所有行吗？,
Are you sure you want to delete this post?,你确定你要删除这个帖子？,
Are you sure you want to merge {0} with {1}?,您确定要将{0}与{1}合并吗？,
Assignment Day {0} has been repeated.,分配日{0}已重复。,
Assignment Days,作业日,
Assignment Rule Day,分配规则日,
Assignments,作业,
Attach a web link,附加网络链接,
Authorize Google Calendar Access,授权Google日历访问权限,
Authorize Google Contacts Access,授权Google通讯录访问权限,
Authorize Google Drive Access,授权Google Drive Access,
Auto Repeat Document Creation Failed,自动重复文档创建失败,
Auto Repeat Document Creation Failure,自动重复文档创建失败,
Auto Repeat created for this document,为此文档创建了自动重复,
Auto Repeat failed for {0},{0}自动重复失败,
Automatic Linking can be activated only for one Email Account.,只能为一个电子邮件帐户激活自动链接。,
Automatic Linking can be activated only if Incoming is enabled.,仅当启用了“传入”时，才能激活自动链接。,
Automatically generates recurring documents.,自动生成定期文档。,
Backing up to Google Drive.,备份到Google云端硬盘。,
Backup Folder ID,备份文件夹ID,
Backup Folder Name,备份文件夹名称,
Before Cancel,取消之前,
Before Delete,删除之前,
Before Insert,插入之前,
Before Save,保存之前,
Before Save (Submitted Document),保存之前（提交的文档）,
Before Submit,提交之前,
Blank Template,空白模板,
Callback URL,回调网址,
Cancel All Documents,取消所有文件,
Cancelling documents,取消文件,
Cannot match column {0} with any field,无法将列{0}与任何字段匹配,
Change,变化,
Change User,改变用户,
Check the Error Log for more information: {0},检查错误日志以获取更多信息：{0},
Clear Cache and Reload,清除缓存和重新加载,
Clear Filters,清除过滤器,
Click on <b>Authorize Google Drive Access</b> to authorize Google Drive Access.,点击<b>授权Google云端硬盘访问权限</b>以授权Google云端硬盘访问权限。,
Click on a file to select it.,单击文件以选择它。,
Click on the link below to approve the request,单击下面的链接批准该请求,
Click on the lock icon to toggle public/private,单击锁定图标以切换公共/私人,
Click on {0} to generate Refresh Token.,单击{0}以生成刷新令牌。,
Close Condition,关闭条件,
Columns / Fields,列/字段,
"Configure notifications for mentions, assignments, energy points and more.",配置有关提及，任务，能量点等的通知。,
Contact Email,联络人电邮,
Contact Numbers,联络电话,
Contact Phone,联系电话,
Contact Synced with Google Contacts.,与Google通讯录联系。,
Context,语境,
Contribute Translations,贡献翻译,
Contributed,供稿,
Controller method get_razorpay_order missing,控制器方法get_razorpay_order丢失,
Copied to clipboard.,复制到剪贴板。,
Core Modules {0} cannot be searched in Global Search.,无法在全局搜索中搜索核心模块{0}。,
Could not create Razorpay order. Please contact Administrator,无法创建Razorpay订单。请联系管理员,
Could not create razorpay order,无法创建razorpay订单,
Create Log,创建日志,
Create your first {0},创建您的第一个{0},
Created {0} records successfully.,已成功创建{0}条记录。,
Cron,克朗,
Cron Format,Cron格式,
Daily Events should finish on the Same Day.,每日活动应在同一天结束。,
Daily Long,每日长,
Default Role on Creation,创建时的默认角色,
Default Theme,默认主题,
Default {0},默认{0},
Delete All,删除所有,
Do you want to cancel all linked documents?,您要取消所有链接的文档吗？,
DocType Action,DocType操作,
DocType Event,DocType事件,
DocType Link,DocType链接,
Document Share,文件共享,
Document Tag,文件标签,
Document Title,文件名,
Document Type Field Mapping,文档类型字段映射,
Document Type Mapping,文件类型对应,
Document Type {0} has been repeated.,文档类型{0}已重复。,
Document renamed from {0} to {1},文档从{0}重命名为{1},
Document type is required to create a dashboard chart,创建仪表板图表需要文档类型,
Documentation Link,文档链接,
Don't Import,不导入,
Don't Send Emails,不要发送电子邮件,
"Drag and drop files, ",拖放文件，,
Drop,下降,
Drop Here,放在这里,
Drop files here,删除文件,
Dynamic Template,动态模板,
ERPNext Role,ERPNext角色,
Email / Notifications,邮件通知,
Email Account setup please enter your password for: {0},电子邮件帐户设置，请输入您的密码：{0},
Email Address whose Google Contacts are to be synced.,要同步其Google通讯录的电子邮件地址。,
"Email ID must be unique, Email Account already exists for {0}",电子邮件ID必须是唯一的，{0}的电子邮件帐户已经存在,
Email IDs,电子邮件ID,
Enable Allow Auto Repeat for the doctype {0} in Customize Form,在“自定义表单”中为doctype {0}启用“允许自动重复”,
Enable Automatic Linking in Documents,启用文档中的自动链接,
Enable Email Notifications,启用电子邮件通知,
Enable Google API in Google Settings.,在Google设置中启用Google API。,
Enable Security,启用安全性,
Energy Point,能量点,
Enter Client Id and Client Secret in Google Settings.,在Google设置中输入客户端ID和客户端密钥。,
Enter Code displayed in OTP App.,输入OTP App中显示的代码。,
Event Configurations,事件配置,
Event Consumer,活动消费者,
Event Consumer Document Type,事件使用者文档类型,
Event Consumer Document Types,事件消费者文档类型,
Event Producer,活动制作人,
Event Producer Document Type,事件生产者文档类型,
Event Producer Document Types,事件生产者文档类型,
Event Streaming,事件流,
Event Subscriber,事件订阅者,
Event Sync Log,事件同步日志,
Event Synced with Google Calendar.,活动与Google日历同步。,
Event Update Log,事件更新日志,
Export 1 record,导出1条记录,
Export Errored Rows,导出错误的行,
Export From,从中导出,
Export Type,导出类型,
Export {0} records,导出{0}条记录,
Failed to connect to the Event Producer site. Retry after some time.,无法连接到事件生产者站点。一段时间后重试。,
Failed to create an Event Consumer or an Event Consumer for the current site is already registered.,无法创建事件使用者或当前站点的事件使用者已被注册。,
Failure,失败,
Fetching default Global Search documents.,正在获取默认的全局搜索文档。,
Fetching posts...,获取帖子......,
Field To Check,现场检查,
File Information,文件信息,
Filter By,过滤,
Filtered Records,筛选记录,
Filters applied for {0},适用于{0}的过滤器,
Finished,成品,
First,第一,
For Document Event,对于文件活动,
"For more information, <a class=""text-muted"" href=""https://erpnext.com/docs/user/manual/en/setting-up/email/linking-emails-to-document"">click here</a>.","有关更多信息， <a class=""text-muted"" href=""https://erpnext.com/docs/user/manual/en/setting-up/email/linking-emails-to-document"">请单击此处</a> 。",
"For more information, {0}.",有关更多信息，请{0}。,
"For performance, only the first 100 rows were processed.",为了提高性能，仅处理了前100行。,
Form URL-Encoded,表单URL编码,
Frequently Visited Links,经常访问的链接,
From Date,起始日期,
From User,来自用户,
Global Search DocType,全局搜索DocType,
Global Search Document Types Reset.,全局搜索文档类型重置。,
Global Search Settings,全局搜索设置,
Global Shortcuts,全球捷径,
Go,走,
Go to next record,转到下一条记录,
Go to previous record,转到上一条记录,
Google API Settings.,Google API设置。,
Google Calendar,Google日历,
"Google Calendar - Could not create Calendar for {0}, error code {1}.",Google日历 - 无法为{0}创建日历，错误代码为{1}。,
"Google Calendar - Could not delete Event {0} from Google Calendar, error code {1}.",Google日历 - 无法从Google日历中删除事件{0}，错误代码为{1}。,
"Google Calendar - Could not fetch event from Google Calendar, error code {0}.",Google日历 - 无法从Google日历中获取事件，错误代码为{0}。,
"Google Calendar - Could not insert contact in Google Contacts {0}, error code {1}.",Google日历 - 无法在Google通讯录{0}中插入联系人，错误代码为{1}。,
"Google Calendar - Could not insert event in Google Calendar {0}, error code {1}.",Google日历 - 无法在Google日历{0}中插入事件，错误代码为{1}。,
"Google Calendar - Could not update Event {0} in Google Calendar, error code {1}.",Google日历 - 无法更新Google日历中的活动{0}，错误代码为{1}。,
Google Calendar Event ID,Google日历活动ID,
Google Calendar Integration.,Google日历集成。,
Google Calendar has been configured.,Google日历已配置完毕。,
Google Contacts,Google通讯录,
"Google Contacts - Could not sync contacts from Google Contacts {0}, error code {1}.",Google通讯录 - 无法同步Google通讯录{0}中的联系人，错误代码{1}。,
"Google Contacts - Could not update contact in Google Contacts {0}, error code {1}.",Google通讯录 - 无法更新Google通讯录{0}中的联系人，错误代码{1}。,
Google Contacts Id,Google通讯录ID,
Google Contacts Integration is disabled.,Google联系人集成已停用。,
Google Contacts Integration.,Google通讯录集成。,
Google Contacts has been configured.,已配置Google通讯录。,
Google Drive,谷歌云端硬盘,
Google Drive - Could not create folder in Google Drive - Error Code {0},Google云端硬盘 - 无法在Google云端硬盘中创建文件夹 - 错误代码{0},
Google Drive - Could not find folder in Google Drive - Error Code {0},Google云端硬盘 - 在Google云端硬盘中找不到文件夹 - 错误代码{0},
Google Drive Backup Successful.,Google云端硬盘备份成功。,
Google Drive Backup.,Google云端硬盘备份。,
Google Drive Integration.,Google云端硬盘集成。,
Google Drive has been configured.,已配置Google云端硬盘。,
Google Integration is disabled.,Google集成已停用。,
Google Settings,Google设置,
Group By,通过...分组,
Group By Based On,分组依据,
Group By Type,按类型分组,
Group By field is required to create a dashboard chart,“分组依据”字段是创建仪表盘图表所必需的,
HH:mm,高度：毫米,
HH:mm:ss,HH：mm：ss,
HOOK-.####,钩-。＃＃＃＃,
HTML Page,HTML页面,
Has Mapping,有映射,
Hourly Long,每小时长,
"If non-standard port (e.g. POP3: 995/110, IMAP: 993/143)",如果是非标准端口（例如POP3：995/110，IMAP：993/143）,
If the document has different field names on the Producer and Consumer's end check this and set up the Mapping,如果文档在生产者端和消费者端具有不同的字段名称，请检查并设置映射,
If this is checked the documents will have the same name as they have on the Event Producer's site,如果选中此选项，则文档的名称将与事件生产者网站上的名称相同,
Illegal SQL Query,非法SQL查询,
Import File,导入文件,
Import Log Preview,导入日志预览,
Import Preview,导入预览,
Import Progress,进口进度,
Import Type,导入类型,
Import Warnings,导入警告,
"Import template should be of type .csv, .xlsx or .xls",导入模板的类型应为.csv，.xlsx或.xls,
Import template should contain a Header and atleast one row.,导入模板应包含标题和至少一行。,
Importing {0} of {1},导入{1}的{0},
"Importing {0} of {1}, {2}",导入{1}，{2}中的{0},
Include indentation,包括缩进,
Incoming Change,即将到来的变化,
Invalid Filter Value,无效的过滤器值,
Invalid URL,无效的网址,
Invalid field name: {0},字段名称无效：{0},
Invalid file URL. Please contact System Administrator.,无效的文件URL。请联系系统管理员。,
Invalid include path,包含路径无效,
Invalid username or password,用户名或密码无效,
Is Primary,是主要的,
Is Primary Mobile,是主要手机,
Is Primary Phone,是主要电话,
Is Tree,是树,
JSON Request Body,JSON请求正文,
Javascript is disabled on your browser,您的浏览器禁用了Javascript,
Job,工作,
Jump to field,跳到现场,
Keyboard Shortcuts,键盘快捷键,
LDAP Group,LDAP组,
LDAP Group Field,LDAP组字段,
LDAP Group Mapping,LDAP组映射,
LDAP Group Mappings,LDAP组映射,
LDAP Last Name Field,LDAP姓氏字段,
LDAP Middle Name Field,LDAP中间名字段,
LDAP Mobile Field,LDAP移动字段,
LDAP Phone Field,LDAP电话字段,
LDAP User Creation and Mapping,LDAP用户创建和映射,
Landscape,景观,
Last,持续,
Last Backup On,上次备份,
Last Execution,最后执行,
Last Sync On,上次同步开启,
Last Update,最后更新,
Last refreshed,最后刷新了,
Link Document Type,链接文件类型,
Link Fieldname,链接字段名称,
Loading import file...,正在载入汇入档案...,
Local Document Type,本地文件类型,
Log Data,日志数据,
Main Section (HTML),主要部分（HTML）,
Main Section (Markdown),主要部分（降价）,
"Maintains a Log of all inserts, updates and deletions on Event Producer site for documents that have consumers.",在事件生产者网站上维护具有使用者的文档的所有插入，更新和删除的日志。,
Maintains a log of every event consumed along with the status of the sync and a Resync button in case sync fails.,保留所有消耗的事件以及同步状态和“重新同步”按钮的日志，以防同步失败。,
Make all attachments private,将所有附件设为私有,
Mandatory Depends On,强制取决于,
Map Columns,地图列,
Map columns from {0} to fields in {1},将列从{0}映射到{1}中的字段,
Mapping column {0} to field {1},将列{0}映射到字段{1},
Mark all as Read,标记为已读,
Maximum Points,最高积分,
Maximum points allowed after multiplying points with the multiplier value\n(Note: For no limit leave this field empty or set 0),将点乘以乘数值后允许的最大点（注意：无限制，将此字段留空或设置为0）,
Me,我,
Mention,提到,
Modules,模组,
Monthly Long,每月长,
Naming Series,名录,
Navigate Home,导航回家,
Navigate list down,向下导航列表,
Navigate list up,导航列表,
New Notification,新通知,
New {0}: {1},新{0}：{1},
Newsletter should have atleast one recipient,通讯应该至少有一个收件人,
No Events Today,今天没有活动,
No Google Calendar Event to sync.,没有要同步的Google日历活动。,
No More Activity,没有更多的活动,
No Name Specified for {0},没有为{0}指定名称,
No Upcoming Events,没有即将发生的事件,
No activity,没有活动,
No conditions provided,没有提供条件,
No contacts linked to document,没有联系人链接到文档,
No data to export,没有要导出的数据,
No documents found tagged with {0},找不到标有{0}的文档,
No failed logs,没有失败的日志,
No filters found,找不到过滤器,
No more items to display,没有更多要显示的项目,
No more posts,没有更多的帖子,
No new Google Contacts synced.,没有新的Google通讯录同步。,
No pending or current jobs for this site,此站点没有待处理或当前作业,
No posts yet,还没有帖子,
No records will be exported,没有记录将被导出,
No results found for {0} in Global Search,在全局搜索中找不到与{0}相关的结果,
No user found,找不到用户,
Notification Log,通知日志,
Notification Settings,通知设置,
Notification Subscribed Document,通知订阅文件,
Notifications Disabled,通知已禁用,
Number of Groups,组数,
OAuth Client ID,OAuth客户端ID,
OTP setup using OTP App was not completed. Please contact Administrator.,使用OTP应用程序的OTP设置未完成。请联系管理员。,
Only one {0} can be set as primary.,只能将一个{0}设置为主。,
Open Awesomebar,打开Awesomebar,
Open Chat,打开聊天,
Open Documents,打开文件,
Open Help,打开帮助,
Open Settings,打开设置,
Open list item,打开列表项,
Organizational Unit for Users,用户组织单位,
Page Shortcuts,页面快捷方式,
Parent Field (Tree),父田（树）,
Parent Field must be a valid fieldname,父字段必须是有效的字段名称,
Pin Globally,全球销,
Places,地方,
Please check the filter values set for Dashboard Chart: {},请检查为仪表盘图表设置的过滤器值：{},
Please enable pop-ups in your browser,请在浏览器中启用弹出窗口,
Please find attached {0}: {1},请查找附件{0}：{1},
Please select applicable Doctypes,请选择适用的Doctypes,
Portrait,肖像,
Press Alt Key to trigger additional shortcuts in Menu and Sidebar,按Alt键可在菜单和侧栏中触发其他快捷方式,
Print Settings...,打印设置...,
Producer Document Name,生产者文件名称,
Producer URL,生产者网址,
Property Depends On,属性取决于,
Pull from Google Calendar,从Google日历中提取,
Pull from Google Contacts,从Google通讯录中提取,
Pulled from Google Calendar,从Google日历中拉出,
Pulled from Google Contacts,来自Google通讯录,
Push to Google Calendar,推送到Google日历,
Push to Google Contacts,推送到Google通讯录,
Queue / Worker,队列/工人,
RAW Information Log,RAW信息日志,
Raw Printing Settings...,原始打印设置...,
Read Only Depends On,只读取决于,
Recent Activity,近期活动,
Reference document has been cancelled,参考文件已被取消,
Reload File,重新载入档案,
Remote Document Type,远端文件类型,
"Renamed files and replaced code in controllers, please check!",重命名文件并替换控制器中的代码，请检查！,
Repeat on Last Day of the Month,在本月的最后一天重复,
Repeats {0},重复{0},
Report Information,报告信息,
Report with more than 10 columns looks better in Landscape mode.,在横向模式下，超过10列的报告看起来更好。,
Request Structure,请求结构,
Restricted,受限制的,
Restrictions,限制条件,
Resync,重新同步,
Row Number,行号,
Row {0},第{0}行,
Run Jobs only Daily if Inactive For (Days),仅在（天）不活动的情况下每天运行作业,
SMS was not sent. Please contact Administrator.,短信未发送。请联系管理员。,
Saved Successfully,保存成功,
Scheduled Job,预定工作,
Scheduled Job Log,计划作业日志,
Scheduled Job Type,预定作业类型,
Scheduler Inactive,调度程序无效,
Scheduler is inactive. Cannot import data.,调度程序处于非活动状态。无法导入数据。,
Script Manager,脚本管理器,
Script Type,脚本类型,
Search Priorities,搜索优先级,
Search Source Text,搜索源文本,
Search by filename or extension,按文件名或扩展名搜索,
Select Date Range,选择日期范围,
Select Field,选择字段,
Select Field...,选择字段...,
Select Filters,选择过滤器,
Select Google Calendar to which event should be synced.,选择要同步哪个事件的Google日历。,
Select Google Contacts to which contact should be synced.,选择要同步联系人的Google通讯录。,
Select Group By...,选择分组依据...,
Select Mandatory,强制性选择,
Select atleast 2 actions,选择至少2个动作,
Select list item,选择列表项,
Select multiple list items,选择多个列表项,
Send an email to {0} to link it here,发送电子邮件至{0}以在此处链接,
Server Action,服务器动作,
Server Script,服务器脚本,
Session Default,会话默认,
Session Default Settings,会话默认设置,
Session Defaults,会话默认值,
Session Defaults Saved,会话默认值已保存,
Set as Default Theme,设为默认主题,
Setting up Global Search documents.,设置全局搜索文档。,
Show Document,显示文件,
Show Failed Logs,显示失败的日志,
Show Keyboard Shortcuts,显示键盘快捷键,
Show More Activity,显示更多活动,
Show Traceback,显示回溯,
Show Warnings,显示警告,
Showing only first {0} rows out of {1},仅显示{1}中的前{0}行,
"Simple Python Expression, Example: Status in (""Invalid"")",简单的Python表达式，示例：状态（“无效”）,
Skipping Untitled Column,跳过无标题列,
Skipping column {0},跳过列{0},
Social Home,社会之家,
Some columns might get cut off when printing to PDF. Try to keep number of columns under 10.,打印到PDF时，某些列可能会被切断。尽量保持列数低于10。,
Something went wrong during the token generation. Click on {0} to generate a new one.,在令牌生成期间出了点问题。单击{0}以生成新的。,
Submit After Import,导入后提交,
Submitting...,提交中...,
Success! You are good to go 👍,成功！你很高兴去👍,
Successful Transactions,成功交易,
Successfully Submitted!,提交成功！,
Successfully imported {0} record.,已成功导入{0}记录。,
Successfully imported {0} records.,成功导入了{0}条记录。,
Successfully updated {0} record.,成功更新了{0}条记录。,
Successfully updated {0} records.,已成功更新{0}条记录。,
Sync Calendar,同步日历,
Sync Contacts,通讯录同步,
Sync with Google Calendar,与Google日历同步,
Sync with Google Contacts,与Google通讯录同步,
Synced,已同步,
Syncing,同步,
Syncing {0} of {1},同步{1}的{0},
Tag Link,标签链接,
Take Backup,备份,
Template Error,模板错误,
Template Options,模板选项,
Template Warnings,模板警告,
The Auto Repeat for this document has been disabled.,此文档的自动重复已被禁用。,
The following records needs to be created before we can import your file.,在导入文件之前，需要创建以下记录。,
The mapping configuration between two doctypes.,两个文档类型之间的映射配置。,
The site which is consuming your events.,正在使用您的事件的站点。,
The site you want to subscribe to for consuming events.,您要订阅消费事件的站点。,
The webhook will be triggered if this expression is true,如果此表达式为true，则将触发webhook,
The {0} is already on auto repeat {1},{0}已经自动重复{1},
There are some linked records which needs to be created before we can import your file. Do you want to create the following missing records automatically?,在我们导入您的文件之前，需要创建一些链接记录。您是否要自动创建以下丢失的记录？,
There should be atleast one row for the following tables: {0},下表至少应有一行：{0},
There should be atleast one row for {0} table,{0}表至少应有一行,
This action is only allowed for {},此操作仅适用于{},
This cannot be undone,这不能被撤消,
Time Format,时间格式,
Time series based on is required to create a dashboard chart,需要基于时间序列来创建仪表板图表,
Time {0} must be in format: {1},时间{0}必须采用以下格式：{1},
"To configure Auto Repeat, enable ""Allow Auto Repeat"" from {0}.",要配置自动重复，请从{0}启用“允许自动重复”。,
To enable it follow the instructions in the following link: {0},要启用它，请遵循以下链接中的说明：{0},
"To use Google Calendar, enable {0}.",要使用Google日历，请启用{0}。,
"To use Google Contacts, enable {0}.",要使用Google通讯录，请启用{0}。,
"To use Google Drive, enable {0}.",要使用Google云端硬盘，请启用{0}。,
Today's Events,今日活动,
Toggle Public/Private,切换公共/私人,
Tracks milestones on the lifecycle of a document if it undergoes multiple stages.,如果文档经历多个阶段，则跟踪文档生命周期中的里程碑。,
Tree structures are implemented using Nested Set,树结构使用嵌套集实现,
Trigger Primary Action,触发主要操作,
URL for documentation or help,文档或帮助的URL,
URL must start with 'http://' or 'https://',网址必须以“http：//”或“https：//”开头,
Unchanged,不变的,
Unpin,取消固定,
Untitled Column,无标题栏,
Untranslated,未翻译,
Upcoming Events,即将举行的活动,
Update Existing Records,更新现有记录,
Updated To A New Version 🎉,更新到新版本🎉,
"Updating {0} of {1}, {2}",更新{1}，{2}中的{0},
Upload file,上传文件,
Upload {0} files,上传{0}个文件,
Uploaded To Google Drive,已上传到Google云端硬盘,
Uploaded successfully,已成功上传,
Uploading {0} of {1},上传{1}的{0},
Use SSL for Outgoing,使用SSL进行传出,
Use Same Name,使用相同的名称,
Used For Google Maps Integration.,用于Google Maps Integration。,
User ID Property,用户ID属性,
User Profile,用户资料,
User Settings,用户设置,
User does not exist,用户不存在,
User {0} has requested for data deletion,用户{0}已请求删除数据,
Users assigned to the reference document will get points.,分配给参考文档的用户将获得积分。,
Value must be one of {0},值必须为{0}之一,
Value {0} missing for {1},缺少{1}的值{0},
Verification,验证,
Verification Code,验证码,
Verification code email not sent. Please contact Administrator.,验证码电子邮件未发送。请联系管理员。,
Verified,验证,
Verifier,验证者,
View Full Log,查看完整日志,
"View Log of all print, download and export events",查看所有打印，下载和导出事件的日志,
Visit Web Page,访问网页,
Webhook Secret,Webhook的秘密,
Webhook Security,Webhook安全,
Webhook Trigger,Webhook Trigger,
Weekly Long,每周长,
"When enabled this will allow guests to upload files to your application, You can enable this if you wish to collect files from user without having them to log in, for example in job applications web form.",启用后，这将允许访客将文件上载到您的应用程序。如果您希望从用户收集文件而无需他们登录，则可以启用此功能，例如在作业应用程序Web表单中。,
Will run scheduled jobs only once a day for inactive sites. Default 4 days if set to 0.,对于非活动站点，每天仅运行一次计划的作业。如果设置为0，则默认为4天。,
Workflow Status,工作流程状态,
You are not allowed to export {} doctype,您不能导出{} doctype,
You can try changing the filters of your report.,您可以尝试更改报告的过滤器。,
You do not have permissions to cancel all linked documents.,您无权取消所有链接的文档。,
You need to create these first: ,您需要先创建这些：,
You need to enable JavaScript for your app to work.,您需要为应用程序启用JavaScript。,
You need to install pycups to use this feature!,您需要安装pycups才能使用此功能！,
Your Target,您的目标,
"browse,",浏览，,
cancelled this document {0},取消了此文档{0},
changed value of {0} {1},{0} {1}的值已更改,
changed values for {0} {1},{0} {1}的值已更改,
choose an,选择一个,
empty,空,
of,的,
or attach a,或附上一个,
submitted this document {0},提交了该文档{0},
"tag name..., e.g. #tag",标签名称...，例如#tag,
uploaded file,上传的文件,
via Data Import,通过数据导入,
{0} Google Calendar Events synced.,已同步{0} Google日历活动。,
{0} Google Contacts synced.,{0} Google通讯录同步。,
{0} are mandatory fields,{0}是必填字段,
{0} are required,{0}是必需的,
{0} assigned a new task {1} {2} to you,{0}为您分配了新任务{1} {2},
{0} gained {1} point for {2} {3},{0}为{2} {3}赢得了{1}点,
{0} gained {1} points for {2} {3},{0}的{2} {3}获得了{1}分,
{0} has no versions tracked.,{0}没有跟踪版本。,
{0} is not a valid report format. Report format should one of the following {1},{0}不是有效的报告格式。报告格式应为以下{1}之一,
{0} mentioned you in a comment in {1} {2},{0}在{1} {2}的评论中提到了您,
{0} of {1} ({2} rows with children),{1}的{0}（有子项的{2}行）,
{0} records will be exported,{0}条记录将被导出,
{0} shared a document {1} {2} with you,{0}与您共享了一个文档{1} {2},
{0} should not be same as {1},{0}不应与{1}相同,
{0} translations pending,{0}个翻译待定,
{0} {1} is linked with the following submitted documents: {2},{0} {1}与以下提交的文档链接：{2},
"{0}: Failed to attach new recurring document. To enable attaching document in the auto repeat notification email, enable {1} in Print Settings",{0}：无法附加新的重复文件。要在自动重复通知电子邮件中附加文档，请在“打印设置”中启用{1},
{0}: Fieldname cannot be one of {1},{0}：字段名称不能是{1}之一,
{} Complete,{}完成,
← Back to upload files,←返回上传文件,
Activity,活动,
Add / Manage Email Accounts.,添加/管理电子邮件帐户。,
Add Child,添加子项,
Add Multiple,添加多个,
Add Participants,添加参与者,
Added {0} ({1}),已添加{0}（{1}）,
Address Line 1,地址行1,
Addresses,地址,
All,所有,
Brand,品牌,
Browse,浏览,
Cancelled,取消,
Chart,图表,
Close,关闭,
Communication,通讯,
Compact Item Print,紧凑型项目打印,
Company,公司,
Complete,完成,
Completed,已完成,
Continue,继续,
Country,国家,
Creating {0},创建{0},
Currency,货币,
Customize,定制,
Daily,每日,
Date,日期,
Dear,亲爱,
Default,默认,
Delete,删除,
Description,描述,
Designation,职位,
Disabled,禁用,
Doctype,文档类型,
Download Template,下载模板,
Due Date,到期日,
Duplicate,复制,
Edit Profile,编辑个人资料,
Email,电子邮件,
Enter Value,输入值,
Entity Type,实体类型,
Error,错误,
Expired,已过期,
Export,导出,
Export not allowed. You need {0} role to export.,不允许导出，您没有{0}的角色。,
Field,字段,
File Manager,文件管理器,
Filters,过滤器,
Get Items,获取物料,
Goal,目标,
Group,组,
Group Node,组节点,
Help,帮助,
Help Article,帮助文章,
Home,家,
Import Data from CSV / Excel files.,从CSV / Excel文件导入数据。,
In Progress,进行中,
Intermediate,中间,
Invite as User,邀请成为用户,
Loading...,载入中...,
Location,位置,
Message,信息,
Missing Values Required,缺少需要的值,
Mobile No,手机号码,
Month,月,
Name,名称,
Newsletter,通讯,
Not Allowed,不允许,
Note,注,
Offline,离线,
Open,开,
Page {0} of {1},第{0} {1},
Pending,有待,
Phone,电话,
Please click on the following link to set your new password,请点击以下链接来设置新密码,
Please select another payment method. Stripe does not support transactions in currency '{0}',请选择其他付款方式。 Stripe不支持货币“{0}”的交易,
Please specify,请注明,
Printing,打印,
Priority,优先,
Project,项目,
Quarterly,季度,
Queued,排队,
Quick Entry,快速入门,
Reason,原因,
Refreshing,正在刷新...,
Rename,重命名,
Reset,重启,
Review,评论,
Room,房间,
Room Type,房型,
Save,保存,
Search results for,为。。。。寻找结果,
Select All,全选,
Send,发送,
Sending,发送中,
Server Error,服务器错误,
Set,设置,
Setup,设置,
Setup Wizard,设置向导,
Size,尺寸,
Sr,锶,
Start,开始,
Start Time,开始时间,
Status,状态,
Submitted,已提交,
Tag,标签,
Template,模板,
Thursday,星期四,
Title,标题,
Total,总,
Totals,总计,
Tuesday,星期二,
Type,类型,
Update,更新,
User {0} is disabled,用户{0}已禁用,
Users and Permissions,用户和权限,
Warehouse,仓库,
Welcome to {0},欢迎{0},
Year,年,
Yearly,每年,
You,你,
and,和,
{0} Name,{0}名称,
{0} is required,{0}是必填项,
ALL,所有,
Attach File,附加文件,
Barcode,条码,
Beginning with,以。。。开始,
Bold,胆大,
CANCELLED,已取消,
Calendar,日历,
Center,中心,
Clear,明确,
Comment,评论,
Comments,评论,
DRAFT,草稿,
Dashboard,仪表板,
DocType,DocType,
Download,下载,
EMail,邮件,
Edit in Full Page,全页编辑,
Email Inbox,电子邮件收件箱,
File,文件,
Forward,向前,
Icon,图标,
In,在,
Inbox,收件箱,
Insert New Records,插入新记录,
JavaScript,Javascript,
LDAP Settings,LDAP设定,
Left,左边,
Like,喜欢,
Link,链接,
Logged in,登录,
New,新,
Not Found,未找到,
Not Like,不类似,
Notify by Email,通过电子邮件通知,
Now,现在,
Off,关,
One of,之一,
Page,页,
Print,打印,
Reference Name,参考名称,
Refresh,刷新,
Repeat,重复,
Right,右边,
Roles HTML,角色的HTML,
Scheduled To Send,已计划发送,
Search Results for ,为。。。。寻找结果,
Send Notification To,通知发送到,
Success,成功,
Tags,标签,
Time,时间,
Updated Successfully,更新成功,
Upload,上传,
User ,用户,
Value,值,
Web Link,网页链接,
Your Email Address,您的电子邮件地址,
Desktop,桌面,
Usage Info,使用信息,
Download Backups,下载备份,
Recorder,录音机,
Role Permissions Manager,角色权限管理,
Translation Tool,翻译工具,
Awaiting password,等待密码,
Current status,当前状态,
Download template,下载范本,
Edit in full page,整页编辑,
Email Id,电子邮件ID,
Email address,电子邮箱,
Ends on,结束于,
Half-yearly,每半年,
Hidden,隐,
Javascript,的JavaScript,
Ldap settings,LDAP设定,
Mobile number,手机号码,
Mx,MX,
No,No,
Not found,未找到,
Notes:,笔记：,
Notify by email,通过电子邮件通知,
Permitted Documents For User,许可文件对于用户,
Reference Docname,参考DocName,
Reference Doctype,参考文档类型,
Reference name,参考名称,
Roles Html,角色HTML,
Row #,第＃行,
Scheduled to send,预定发送,
Select Doctype,选择DocType,
Send Email for Successful backup,备份成功发送电子邮件,
Sign up,报名,
Time format,时间格式,
Upload failed,上传失败,
User Id,用户身份,
Yes,是,
Your email address,您的电子邮件地址,
added,添加,
added {0},添加{0},
barcode,条码,
beginning with,以此开头,
blue,蓝色,
bold,加粗,
book,书,
calendar,日历,
certificate,证书,
check,检查,
clear,清除,
comment,评论,
comments,注释,
created,创建,
danger,危险,
dashboard,仪表板,
download,下载,
edit,编辑,
email inbox,收件箱,
file,文件,
filter,过滤器,
flag,标志,
font,字体,
forward,转发,
green,绿色,
home,家,
icon,图标,
inbox,收件箱,
like,Like,
link,链接,
list,名单,
lock,锁定,
logged in,已登录,
message,信息,
module,模块,
move,移动,
music,音乐,
new,新建,
now,现在,
off,关闭,
one of,其中,
orange,橙子,
page,页面,
print,打印,
purple,紫色,
random,随机,
red,红色,
refresh,刷新,
remove,去掉,
response,响应,
search,搜索,
share,分享,
stop,停止,
success,成功,
tag,标签,
tags,标签,
tasks,任务,
time,时间,
trash,垃圾,
upload,上载,
user,用户,
value,值,
web link,网站链接,
yellow,黄色,
Not permitted,不允许,
Add Chart to Dashboard,将图表添加到仪表板,
Add to Dashboard,添加到仪表板,
Google Translation,谷歌翻译,
Important,重要,
No Filters Set,未设置过滤器,
No Records Created,未创建记录,
Please Set Chart,请设置图表,
Please create chart first,请先创建图表,
"Report has no data, please modify the filters or change the Report Name",报告中没有数据，请修改过滤器或更改报告名称,
Select Dashboard,选择仪表板,
Y Field,Y场,
You need to be in developer mode to edit this document,您需要处于开发人员模式才能编辑此文档,
Cards,牌,
Community Contribution,社区贡献,
Count Filter,计数过滤器,
Dashboard Chart Field,仪表盘图表字段,
Desk Card,桌面卡,
Desk Chart,桌面图,
Desk Page,桌面页面,
Desk Shortcut,桌面快捷方式,
Developer Mode Only,仅开发人员模式,
Disable User Customization,禁用用户自定义,
For example: {} Open,例如：{}打开,
Link Cards,链接卡,
Link To,链接到,
Onboarding,入职,
Percentage,百分比,
Pie,馅饼,
Pin To Bottom,固定到底部,
Pin To Top,固定到顶部,
Restrict to Domain,限制域名,
Shortcuts,快捷键,
X Field,X场,
Y Axis,Y轴,
workspace,工作区,
Setup > User,设置&gt;用户,
Setup > Customize Form,设置&gt;自定义表格,
Setup > User Permissions,设置&gt;用户权限,
"Error connecting to QZ Tray Application...<br><br> You need to have QZ Tray application installed and running, to use the Raw Print feature.<br><br><a target=""_blank"" href=""https://qz.io/download/"">Click here to Download and install QZ Tray</a>.<br> <a target=""_blank"" href=""https://erpnext.com/docs/user/manual/en/setting-up/print/raw-printing"">Click here to learn more about Raw Printing</a>.","连接到QZ托盘应用程序时出错... <br><br>您需要安装并运行QZ Tray应用程序，才能使用Raw Print功能。 <br><br> <a href=""https://qz.io/download/"" target=""_blank"">单击此处下载并安装QZ托盘</a> 。 <br> <a href=""https://erpnext.com/docs/user/manual/en/setting-up/print/raw-printing"" target=""_blank"">单击此处以了解有关原始印刷的更多信息</a> 。",
No email account associated with the User. Please add an account under User > Email Inbox.,没有与该用户关联的电子邮件帐户。请在“用户”&gt;“电子邮件收件箱”下添加一个帐户。,
"For comparison, use >5, <10 or =324. For ranges, use 5:10 (for values between 5 & 10).",为了进行比较，请使用&gt; 5，&lt;10或= 324。对于范围，请使用5:10（对于5到10之间的值）。,
No default Address Template found. Please create a new one from Setup > Printing and Branding > Address Template.,找不到默认的地址模板。请从设置&gt;打印和商标&gt;地址模板中创建一个新地址。,
Please setup default Email Account from Setup > Email > Email Account,请从设置&gt;电子邮件&gt;电子邮件帐户设置默认的电子邮件帐户,
Email Account not setup. Please create a new Email Account from Setup > Email > Email Account,电子邮件帐户未设置。请从设置&gt;电子邮件&gt;电子邮件帐户创建一个新的电子邮件帐户,
Attach file,附加档案,
Contribution Status,贡献状态,
Contribution Document Name,贡献文件名称,
Extends,延伸,
Extends Another Page,扩展另一页,
Please select target language for translation,请选择目标语言进行翻译,
Select Language,选择语言,
Confirm Translations,确认翻译,
Contributed Translations,贡献翻译,
Show Tags,显示标签,
Do not have permission to access {0} bucket.,没有访问{0}存储桶的权限。,
Allow document creation via Email,允许通过电子邮件创建文档,
Sender Field,发件人字段,
Logout All Sessions on Password Reset,注销密码重置后的所有会话,
Logout From All Devices After Changing Password,更改密码后从所有设备注销,
Send Notifications For Documents Followed By Me,发送关于我关注的文档的通知,
Send Notifications For Email Threads,发送电子邮件主题的通知,
Bypass Restricted IP Address Check If Two Factor Auth Enabled,绕过受限IP地址检查是否启用了两个因素验证,
Reset LDAP Password,重置LDAP密码,
Confirm New Password,确认新密码,
Logout All Sessions,注销所有会话,
Passwords do not match!,密码不匹配！,
Dashboard Manager,资讯主页管理员,
Dashboard Settings,资讯主页设定,
Chart Configuration,图表配置,
No Permitted Charts on this Dashboard,此仪表板上没有允许的图表,
No Permitted Charts,没有允许的图表,
Reset Chart,重置图表,
via {0},通过{0},
{0} is not a valid Phone Number,{0}不是有效的电话号码,
Failed Transactions,交易失败,
Value for field {0} is too long in {1}. Length should be lesser than {2} characters,字段{0}的值在{1}中太长。长度应小于{2}个字符,
Data Too Long,数据太长,
via Notification,通过通知,
Log in to access this page.,登录访问此页面。,
Report Document Error,报告文件错误,
{0} is an invalid Data field.,{0}是无效的数据字段。,
Only Options allowed for Data field are:,只有“数据”字段允许的选项是：,
Select a valid Subject field for creating documents from Email,选择一个有效的主题字段以通过电子邮件创建文档,
"Subject Field type should be Data, Text, Long Text, Small Text, Text Editor",主题字段类型应为数据，文本，长文本，小文本，文本编辑器,
Select a valid Sender Field for creating documents from Email,选择一个有效的发件人字段以通过电子邮件创建文档,
Sender Field should have Email in options,发件人字段中应有电子邮件选项,
Password changed successfully.,密码更换成功。,
Failed to change password.,修改密码失败。,
No Entry for the User {0} found within LDAP!,在LDAP中找不到用户{0}的条目！,
No LDAP User found for email: {0},找不到电子邮件的LDAP用户：{0},
Prepared Report User,准备的报告用户,
Scheduler Event,调度事件,
Select Event Type,选择事件类型,
Schedule Script,排程脚本,
Duration,持续时间,
Donut,甜甜圈,
Custom Options,自订选项,
"Ex: ""colors"": [""#d1d8dd"", ""#ff5858""]",例如：“颜色”：[“＃d1d8dd”，“＃ff5858”],
Confirmation Email Template,确认电子邮件模板,
Welcome Email Template,欢迎电子邮件模板,
Schedule Send,安排发送,
Do you really want to send this email newsletter?,您是否真的要发送此电子邮件通讯？,
Advanced Settings,高级设置,
Disable Comments,禁用评论,
Comments on this blog post will be disabled if checked.,如果选中此博客文章的评论将被禁用。,
CSS Class,CSS类,
Full Width,全屏宽度,
Page Builder,页面生成器,
Page Building Blocks,页面构建块,
Header and Breadcrumbs,标头和面包屑,
Add Custom Tags,添加自定义标签,
Web Page Block,网页块,
Web Template,网页范本,
Edit Values,编辑值,
Web Template Values,Web模板值,
Add Container,添加容器,
Web Page View,网页浏览,
Path,路径,
Referrer,推荐人,
Browser,浏览器,
Browser Version,浏览器版本,
Web Template Field,Web模板字段,
Section,部分,
Hide,隐藏,
Enable In App Website Tracking,启用应用内网站跟踪,
Enable Google Indexing,启用Google索引,
"To use Google Indexing, enable <a href=""#Form/Google Settings"">Google Settings</a>.","要使用Google索引，请启用<a href=""#Form/Google Settings"">Google设置</a>。",
Authorize API Indexing  Access,授权API索引访问,
Indexing Refresh Token,索引刷新令牌,
Indexing Authorization Code,索引授权码,
Theme Configuration,主题配置,
Font Properties,字体属性,
Button Rounded Corners,按钮圆角,
Button Shadows,按钮阴影,
Button Gradients,按钮渐变,
Light Color,浅色,
Stylesheet,样式表,
Custom SCSS,自定义SCSS,
Navbar,导航栏,
Source Message,源消息,
Translated Message,翻译的讯息,
Using this console may allow attackers to impersonate you and steal your information. Do not enter or paste code that you do not understand.,使用此控制台可能使攻击者冒充您并窃取您的信息。不要输入或粘贴您不理解的代码。,
{0} m,{0}米,
{0} h,{0}小时,
{0} d,{0} d,
{0} w,{0} w,
{0} M,{0} M,
{0} y,{0} y,
yesterday,昨天,
{0} years ago,{0}年前,
New Chart,新图表,
New Shortcut,新捷径,
Edit Chart,编辑图表,
Edit Shortcut,编辑捷径,
Couldn't Load Desk,无法加载办公桌,
"Something went wrong while loading Desk. <b>Please relaod the page</b>. If the problem persists, contact the Administrator",加载Desk时出了点问题。<b>请重新整理页面</b>。如果问题仍然存在，请与管理员联系,
Customize Workspace,自定义工作区,
Customizations Saved Successfully,定制成功保存,
Something went wrong while saving customizations,保存自定义设置时出了点问题,
{} Dashboard,{} 仪表板,
No changes in document,文件无变化,
by Role,按角色,
Document is only editable by users with role,只有具有角色的用户才能编辑文档,
{0}: Other permission rules may also apply,{0}：其他许可规则也可能适用,
{0} Web page views,{0}浏览量,
Expand,扩大,
Collapse,坍方,
"Invalid Bearer token, please provide a valid access token with prefix 'Bearer'.",无效的承载令牌，请提供带有前缀“承载”的有效访问令牌。,
"Failed to decode token, please provide a valid base64-encoded token.",无法解码令牌，请提供有效的base64编码令牌。,
"Invalid token, please provide a valid token with prefix 'Basic' or 'Token'.",无效的令牌，请提供带有前缀“基本”或“令牌”的有效令牌。,
{0} is not a valid Name,{0}不是有效的名称,
Your system is being updated. Please refresh again after a few moments.,您的系统正在更新。请稍后再刷新。,
{0} {1}: Submitted Record cannot be deleted. You must {2} Cancel {3} it first.,{0} {1}：无法删除已提交的记录。您必须先{2}取消{3}。,
Error has occurred in {0},{0}中发生错误,
Status Updated,状态已更新,
You can also copy-paste this {0} to your browser,您也可以将此{0}复制粘贴到浏览器中,
Enabled scheduled execution for script {0},为脚本{0}启用了计划执行,
Scheduled execution for script {0} has updated,脚本{0}的计划执行已更新,
The Link specified has either been used before or Invalid,指定的链接之前已使用过或无效,
Options for {0} must be set before setting the default value.,必须在设置默认值之前设置{0}的选项。,
Default value for {0} must be in the list of options.,{0}的默认值必须在选项列表中。,
Google Indexing has been configured.,Google索引已配置。,
Allow API Indexing Access,允许API索引访问,
Allow Google Indexing Access,允许Google索引访问,
Custom Documents,定制文件,
Could not save customization,无法保存自定义,
Transgender,变性人,
Genderqueer,性别酷儿,
Non-Conforming,不合格,
Prefer not to say,不想说,
Is Billing Contact,帐单联络人,
Address And Contacts,地址和联系方式,
Lead Conversion Time,商机转换时间,
Due Date Based On,到期日基于,
Linked Documents,链接文件,
Steps,脚步,
email,电子邮件,
Component,薪资构成,
Subtitle,字幕,
Prefix,字首,
Is Public,是公开的,
This chart will be available to all Users if this is set,如果设置此图表，则所有用户均可使用,
Number Card,号码卡,
Function,功能,
Minimum,最低要求,
Maximum,最大值,
This card will be available to all Users if this is set,如果设置了此卡，则所有用户都可以使用此卡,
Stats,统计资料,
Show Percentage Stats,显示百分比统计,
Stats Time Interval,统计时间间隔,
Show percentage difference according to this time interval,根据此时间间隔显示百分比差异,
Filters Section,过滤器部分,
Number Card Link,号码卡链接,
Card,卡,
API Access,API访问,
Access Key Secret,访问密钥秘密,
S3 Bucket Details,S3铲斗详细信息,
Bucket Name,桶名,
Backup Details,备份详细资料,
Backup Files,备份文件,
Backup public and private files along with the database.,备份公共和私有文件以及数据库。,
Set to 0 for no limit on the number of backups taken,设置为0表示不限制备份数量,
Meta Description,元描述,
Meta Image,元图像,
Google Snippet Preview,Google Snippet预览,
This is an example Google SERP Preview.,这是Google SERP预览的示例。,
Add Gray Background,添加灰色背景,
Hide Block,隐藏块,
This Week,本星期,
This Month,这个月,
This Quarter,本季度,
This Year,今年,
All Time,整天,
Select From Date,从日期选择,
since yesterday,从昨天开始,
since last week,自从上周以来,
since last month,自上个月以来,
since last year,从去年开始,
Show,显示,
New Number Card,新号码卡,
Your Shortcuts,您的捷径,
You haven't added any Dashboard Charts or Number Cards yet.,您尚未添加任何仪表盘图表或数字卡。,
Click On Customize to add your first widget,单击“自定义”以添加您的第一个小部件,
Are you sure you want to reset all customizations?,您确定要重置所有自定义设置吗？,
"Couldn't save, please check the data you have entered",无法保存，请检查您输入的数据,
Validation Error,验证错误,
"You can only upload JPG, PNG, PDF, or Microsoft documents.",您只能上传JPG，PNG，PDF或Microsoft文档。,
Reverting length to {0} for '{1}' in '{2}'. Setting the length as {3} will cause truncation of data.,将“ {2}”中“ {1}”的长度恢复为{0}。将长度设置为{3}将导致数据截断。,
'{0}' not allowed for type {1} in row {2},第{2}行的类型{1}不允许使用&#39;{0}&#39;,
Option {0} for field {1} is not a child table,字段{1}的选项{0}不是子表,
Invalid Option,无效的选项,
Request Body consists of an invalid JSON structure,请求正文包含无效的JSON结构,
Invalid JSON,无效的JSON,
Party GSTIN,GSTIN派对,
GST State,消费税国家,
Andaman and Nicobar Islands,安达曼和尼科巴群岛,
Andhra Pradesh,安德拉邦,
Arunachal Pradesh,阿鲁纳恰尔邦,
Assam,阿萨姆邦,
Bihar,比哈尔,
Chandigarh,昌迪加尔,
Chhattisgarh,恰蒂斯加尔邦,
Dadra and Nagar Haveli,达德拉和纳加尔·哈维里,
Daman and Diu,达曼和丢,
Delhi,新德里,
Goa,果阿,
Gujarat,古吉拉特邦,
Haryana,哈里亚纳邦,
Himachal Pradesh,喜马al尔邦,
Jammu and Kashmir,查mu和克什米尔,
Jharkhand,贾坎德邦,
Karnataka,卡纳塔克邦,
Kerala,喀拉拉邦,
Lakshadweep Islands,拉克肖普群岛,
Madhya Pradesh,中央邦,
Maharashtra,马哈拉施特拉邦,
Manipur,马尼布尔,
Meghalaya,梅加拉亚邦,
Mizoram,咪唑仑,
Nagaland,那加兰邦,
Odisha,奥迪沙,
Other Territory,其他地区,
Pondicherry,朋迪榭里,
Punjab,旁遮普语,
Rajasthan,拉贾斯坦邦,
Sikkim,锡金,
Tamil Nadu,泰米尔纳德邦,
Telangana,Telangana,
Tripura,特里普拉,
Uttar Pradesh,北方邦,
Uttarakhand,北阿坎德邦,
West Bengal,西孟加拉邦,
GST State Number,消费税国家编号,
Import from Google Sheets,从Google表格导入,
Must be a publicly accessible Google Sheets URL,必须是可公开访问的Google表格网址,
Refresh Google Sheet,刷新Google表格,
Import File Errors and Warnings,导入文件错误和警告,
"Successfully imported {0} records out of {1}. Click on Export Errored Rows, fix the errors and import again.",已成功导入{0}条记录中的{0}条记录。单击导出错误行，修复错误，然后再次导入。,
"Successfully imported {0} record out of {1}. Click on Export Errored Rows, fix the errors and import again.",已成功导入{1}中的{0}条记录。单击导出错误行，修复错误，然后再次导入。,
"Successfully updated {0} records out of {1}. Click on Export Errored Rows, fix the errors and import again.",已成功更新{1}中的{0}条记录。单击导出错误行，修复错误，然后再次导入。,
"Successfully updated {0} record out of {1}. Click on Export Errored Rows, fix the errors and import again.",已成功更新{1}中的{0}条记录。单击导出错误行，修复错误，然后再次导入。,
Data Import Legacy,数据导入传统,
Documents restored successfully,文件还原成功,
Documents that were already restored,已经还原的文件,
Documents that failed to restore,无法还原的文件,
Document Restoration Summary,文件还原摘要,
Hide Days,隐藏的日子,
Hide Seconds,隐藏秒,
Hide Border,隐藏边框,
Index Web Pages for Search,索引搜索网页,
Action / Route,动作/路线,
Document Naming Rule,文件命名规则,
Rule Conditions,规则条件,
Digits,位数,
Example: 00001,例如：00001,
Counter,计数器,
Document Naming Rule Condition,文件命名规则条件,
Installed Application,已安装的应用程序,
Application Name,应用名称,
Application Version,应用版本,
Git Branch,git分支,
Installed Applications,已安装的应用程序,
Navbar Item,导航栏项目,
Item Label,物品标签,
Item Type,物品种类,
Separator,分隔器,
Navbar Settings,导航栏设置,
Application Logo,应用徽标,
Logo Width,徽标宽度,
Dropdowns,下拉菜单,
Settings Dropdown,设置下拉菜单,
Help Dropdown,帮助下拉菜单,
Query / Script,查询/脚本,
"Filters will be accessible via <code>filters</code>. <br><br>Send output as <code>result = [result]</code>, or for old style <code>data = [columns], [result]</code>","可以通过<code>filters</code>访问<code>filters</code> 。<br><br>以<code>result = [result]</code>发送输出，或以旧式<code>data = [columns], [result]</code>",
Client Code,客户代码,
Report Column,报告栏,
Report Filter,报告过滤器,
Wildcard Filter,通配符过滤器,
"Will add ""%"" before and after the query",查询前后将添加“％”,
"Route: Example ""/desk""",路线：示例“ / desk”,
Enable Onboarding,启用入职,
Password Reset Link Generation Limit,密码重置链接生成限制,
Hourly rate limit for generating password reset links,每小时生成密码重置链接的速率限制,
Include Web View Link in Email,通过电子邮件发送文档Web视图链接,
Enable Auto-deletion of Prepared Reports,启用自动删除准备好的报告,
Prepared Report Expiry Period (Days),准备好的报告有效期限（天）,
System will automatically delete Prepared Reports after these many days since creation,自创建以来的许多天后，系统将自动删除“准备的报告”,
Package Document Type,包装文件类型,
Include Attachments,包括附件,
Overwrite,覆写,
Package Publish Target,包发布目标,
Site URL,网站网址,
Package Publish Tool,包发布工具,
Click on the row for accessing filters.,单击该行以访问过滤器。,
Sites,网站,
Last Deployed On,上次部署时间,
Console Log,控制台日志,
"Set Default Options for all charts on this Dashboard (Ex: ""colors"": [""#d1d8dd"", ""#ff5858""])",为该仪表板上的所有图表设置默认选项（例如：“颜色”：[“＃d1d8dd”，“＃ff5858”]）,
Use Report Chart,使用报告表,
Heatmap,热图,
Dynamic Filters,动态滤镜,
Dynamic Filters JSON,动态过滤器JSON,
Set Dynamic Filters,设置动态过滤器,
Click to Set Dynamic Filters,单击以设置动态过滤器,
Hide Custom DocTypes and Reports,隐藏自定义DocType和报告,
Checking this will hide custom doctypes and reports cards in Links section,选中此选项将在“链接”部分中隐藏自定义文档类型和报告卡,
DocType View,DocType视图,
Which view of the associated DocType should this shortcut take you to?,该快捷方式应该带您到关联的DocType的哪个视图？,
List View Settings,列表视图设置,
Maximum Number of Fields,最大字段数,
Module Onboarding,模块入职,
System managers are allowed by default,默认情况下允许系统管理员,
Documentation URL,文档网址,
Is Complete,已经完成,
Alert,警报,
Document Link,文件连结,
Attached File,附件文件,
Attachment Link,附件链接,
Open Reference Document,打开参考文件,
Custom Configuration,自定义配置,
Filters Configuration,过滤器配置,
Dynamic Filters Section,动态过滤器部分,
Please create Card first,请先创建卡,
Onboarding Permission,入职许可,
Onboarding Step,入职步骤,
Is Skipped,被跳过,
Create Entry,创建条目,
Update Settings,更新设定,
Show Form Tour,表演表格之旅,
View Report,查看报告,
Go to Page,转到页面,
Watch Video,看视频,
Show Full Form?,显示完整表格？,
Show full form instead of a quick entry modal,显示完整表格，而不是快速输入模式,
Report Reference Doctype,报告参考文档类型,
Report Description,报告说明,
This will be shown to the user in a dialog after routing to the report,路由到报告后，这将在对话框中显示给用户,
Example: #Tree/Account,示例：＃Tree /帐户,
Callback Title,回调标题,
Callback Message,回叫留言,
This will be shown in a modal after routing,路由后将在模式中显示,
Validate Field,验证字段,
Value to Validate,验证价值,
Use % for any non empty value.,将％用作任何非空值。,
Video URL,影片网址,
Onboarding Step Map,入职步骤图,
Step,步,
System Console,系统控制台,
Console,安慰,
To print output use <code>log(text)</code>,要打印输出，请使用<code>log(text)</code>,
Commit,承诺,
Execute Console script,执行控制台脚本,
Execute,执行,
Create Contacts from Incoming Emails,通过传入电子邮件创建联系人,
Inbox User,收件箱用户,
Disabled Auto Reply,禁用自动回复,
Schedule Sending,计划发送,
Message (Markdown),讯息（降价）,
Message (HTML),讯息（HTML）,
Send Attachments,发送附件,
Testing,测验,
System Notification,系统通知,
Twilio Number,特维里奥数,
"To use WhatsApp for Business, initialize <a href=""#Form/Twilio Settings"">Twilio Settings</a>.","要使用WhatsApp for Business，请初始化<a href=""#Form/Twilio Settings"">Twilio设置</a>。",
"To use Slack Channel, add a <a href=""#List/Slack%20Webhook%20URL/List"">Slack Webhook URL</a>.","要使用Slack Channel，请添加<a href=""#List/Slack%20Webhook%20URL/List"">Slack Webhook URL</a> 。",
Send System Notification,发送系统通知,
"If enabled, the notification will show up in the notifications dropdown on the top right corner of the navigation bar.",如果启用，则通知将显示在导航栏右上角的通知下拉列表中。,
Send To All Assignees,发送给所有受让人,
Receiver By Document Field,接收方按凭证字段,
Receiver By Role,接收者按角色,
Child Table,子表,
Remote Value Filters,远程值过滤器,
API Key of the user(Event Subscriber) on the producer site,生产者站点上用户（事件订阅者）的API密钥,
API Secret of the user(Event Subscriber) on the producer site,生产者站点上用户（事件订阅者）的API秘密,
Paytm Settings,Paytm设置,
Merchant Key,商户密码,
Staging,分期,
Industry Type ID,行业类型ID,
See https://docs.aws.amazon.com/general/latest/gr/s3.html for details.,有关详细信息，请参见https://docs.aws.amazon.com/general/latest/gr/s3.html。,
af-south-1,af-south-1,
ap-east-1,ap-east-1,
eu-south-1,eu-south-1,
me-south-1,我南1,
Twilio Number Group,Twilio号码组,
Twilio Settings,Twilio设置,
Auth Token,验证令牌,
Read Time,阅读时间,
in minutes,在几分钟内,
Featured,精选,
Hide CTA,隐藏号召性文字,
"Description for listing page, in plain text, only a couple of lines. (max 200 characters)",列表页面的描述，以纯文本形式，只有几行。 （最多200个字符）,
Meta Title,元标题,
Enable Social Sharing,启用社交分享,
Show CTA in Blog,在博客中显示CTA,
CTA,CTA,
CTA Label,CTA标签,
CTA URL,CTA网址,
Default Portal Home,默认门户主页,
"Example: ""/desk""",示例：“ / desk”,
Social Link Settings,社交链接设置,
Social Link Type,社交链接类型,
facebook,脸书,
linkedin,行人,
twitter,推特,
"If Icon is set, it will be shown instead of Label",如果设置了图标，它将显示而不是标签,
Apply Document Permissions,套用文件权限,
"For help see <a href=""https://frappeframework.com/docs/user/en/guides/portal-development/web-forms"" target=""_blank"">Client Script API and Examples</a>","如需帮助，请参阅<a href=""https://frappeframework.com/docs/user/en/guides/portal-development/web-forms"" target=""_blank"">客户端脚本API和示例</a>",
Dynamic Route,动态路线,
Map route parameters into form variables. Example <code>/project/&lt;name&gt;</code>,将路线参数映射到表单变量中。示例<code>/project/&lt;name&gt;</code>,
Context Script,上下文脚本,
"<p>Set context before rendering a template. Example:</p><p>\n</p><div><pre><code>\ncontext.project = frappe.get_doc(""Project"", frappe.form_dict.name)\n</code></pre></div>","<p>在渲染模板之前设置上下文。例：</p><p></p><div><pre> <code>context.project = frappe.get_doc(&quot;Project&quot;, frappe.form_dict.name)</code></pre></div>",
Title of the page,页面标题,
This title will be used as the title of the webpage as well as in meta tags,该标题将用作网页以及元标记的标题,
Makes the page public,公开页面,
Checking this will publish the page on your website and it'll be visible to everyone.,选中此复选框将在您的网站上发布该页面，并且所有人都可以看到该页面。,
URL of the page,页面网址,
"This will be automatically generated when you publish the page, you can also enter a route yourself if you wish",当您发布页面时，它将自动生成，也可以根据需要自己输入路线,
Content type for building the page,用于构建页面的内容类型,
"You can select one from the following,",您可以从以下选项中选择一个，,
Standard rich text editor with controls,带控件的标准RTF编辑器,
Github flavoured markdown syntax,Github风格的Markdown语法,
HTML with jinja support,带有Jinja支持的HTML,
Frappe page builder using components,使用组件的Frappe页面构建器,
Checking this will show a text area where you can write custom javascript that will run on this page.,选中此复选框将显示一个文本区域，您可以在其中编写将在此页面上运行的自定义JavaScript。,
Meta title for SEO,SEO的元标题,
"By default the title is used as meta title, adding a value here will override it.",默认情况下，标题用作元标题，在此处添加值将覆盖它。,
"The meta description is an HTML attribute that provides a brief summary of a web page. Search engines such as Google often display the meta description in search results, which can influence click-through rates.",元描述是HTML属性，可提供网页的简要摘要。诸如Google之类的搜索引擎经常在搜索结果中显示元描述，这可能会影响点击率。,
"The meta image is unique image representing the content of the page. Images for this Card should be at least 280px in width, and at least 150px in height.",元图像是代表页面内容的唯一图像。此卡片的图片宽度至少应为280px，高度至少应为150px。,
Add Space on Top,在顶部添加空间,
Add Space on Bottom,在底部添加空间,
Is Unique,是独特的,
User Agent,用户代理,
Table Break,餐桌休息,
Hide Login,隐藏登入,
Navbar Template,导航栏模板,
Navbar Template Values,导航栏模板值,
Call To Action,呼吁采取行动,
Call To Action URL,号召性用语URL,
Footer Logo,页脚徽标,
Footer Template,页脚模板,
Footer Template Values,页脚模板值,
Enable Tracking Page Views,启用跟踪页面浏览量,
"Checking this will enable tracking page views for blogs, web pages, etc.",选中此选项将启用跟踪博客，网页等的页面浏览量。,
Disable Signup for your site,禁用网站注册,
Check this if you don't want users to sign up for an account on your site. Users won't get desk access unless you explicitly provide it.,如果您不希望用户在您的网站上注册帐户，请选中此复选框。除非您明确提供，否则用户将无法获得桌面访问权限。,
URL to go to on clicking the slideshow image,单击幻灯片图像时要转到的URL,
Custom Overrides,自定义替代,
Ignored Apps,忽略的应用,
Include Theme from Apps,包含来自应用程序的主题,
Website Theme Ignore App,网站主题忽略应用,
Are you sure you want to save this document?,您确定要保存此文档吗？,
Refresh All,全部刷新,
"Level 0 is for document level permissions, higher levels for field level permissions.",级别0用于文档级别权限，更高级别用于字段级别权限。,
Website Analytics,网站分析,
d,d,Days (Field: Duration)
h,H,Hours (Field: Duration)
m,米,Minutes (Field: Duration)
s,s,Seconds (Field: Duration)
Less,减,
Not a valid DocType view:,无效的DocType视图：,
Unknown View,未知视图,
Go Back,回去,
Let's take you back to onboarding,让我们回到入门,
Great Job,很好,
Looks Great,看起来很棒,
Looks like you didn't change the value,看起来您没有更改值,
Oops,哎呀,
Skip Step,跳过步骤,
"You're doing great, let's take you back to the onboarding page.",您做的很棒，让我们回到入门页面。,
Good Work 🎉,做得好🎉,
Submit this document to complete this step.,提交此文档以完成此步骤。,
Great,大,
You may continue with onboarding,您可以继续入职,
You seem good to go!,你看起来不错！,
Onboarding Complete,入职完成,
{0} Settings,{0}设置,
{0} Fields,{0}字段,
Reset Fields,重新设置领域,
Select Fields,选择字段,
Warning: Unable to find {0} in any table related to {1},警告：在与{1}相关的任何表中找不到{0},
Tree view is not available for {0},树视图不适用于{0},
Create Card,创建卡,
Card Label,卡标签,
Reports already in Queue,报表已在队列中,
Proceed Anyway,仍要继续,
Delete and Generate New,删除并生成新的,
1 Report,1份报告,
{0} ({1}) (1 row mandatory),{0}（{1}）（必填1行）,
Select Fields To Insert,选择要插入的字段,
Select Fields To Update,选择要更新的字段,
"This document is already amended, you cannot ammend it again",该文档已被修改，您无法再次对其进行修改,
Add to ToDo,添加到待办事项,
{0} is currently {1},{0}当前为{1},
{0} are currently {1},{0}当前为{1},
Currently Replying,目前回覆,
created {0},已创建{0},
Change,更改,Coins
Too Many Requests,请求太多,
"Invalid Authorization headers, add a token with a prefix from one of the following: {0}.",无效的授权标头，请添加带有以下任一前缀的令牌：{0}。,
"Invalid Authorization Type {0}, must be one of {1}.",无效的授权类型{0}，必须是{1}之一。,
{} is not a valid date string.,{}不是有效的日期字符串。,
Invalid Date,失效日期,
Please select a valid date filter,请选择一个有效的日期过滤器,
Value {0} must be in the valid duration format: d h m s,值{0}必须采用有效的持续时间格式：dhms,
Google Sheets URL is invalid or not publicly accessible.,Google表格网址无效或无法公开访问。,
"Google Sheets URL must end with ""gid={number}"". Copy and paste the URL from the browser address bar and try again.",Google表格网址必须以“ gid = {number}”结尾。从浏览器地址栏中复制并粘贴URL，然后重试。,
Incorrect URL,网址错误,
"""{0}"" is not a valid Google Sheets URL",“ {0}”不是有效的Google表格网址,
Duplicate Name,名称重复,
"Please check the value of ""Fetch From"" set for field {0}",请检查为字段{0}设置的“提取自”的值,
Wrong Fetch From value,从价值中提取错误,
A field with the name '{}' already exists in doctype {}.,文档类型{}中已经存在名称为“ {}”的字段。,
Custom Field {0} is created by the Administrator and can only be deleted through the Administrator account.,自定义字段{0}由管理员创建，只能通过管理员帐户删除。,
Failed to send {0} Auto Email Report,无法发送{0}自动电子邮件报告,
Test email sent to {0},测试发送到{0}的电子邮件,
Email queued to {0} recipients,电子邮件已排队{0}个收件人,
Newsletter should have at least one recipient,时事通讯应至少有一位收件人,
Please enable Twilio settings to send WhatsApp messages,请启用Twilio设置以发送WhatsApp消息,
"Not allowed to attach {0} document, please enable Allow Print For {0} in Print Settings",不允许附加{0}文档，请在“打印设置”中启用“允许{0}打印”,
Signup Disabled,注册已禁用,
Signups have been disabled for this website.,该网站的注册已被禁用。,
Open Document,打开文件,
The comment cannot be empty,评论不能为空,
Hourly comment limit reached for: {0},已达到每小时评论的限制：{0},
Please add a valid comment.,请添加有效的评论。,
Document {0} Already Restored,文档{0}已恢复,
Restoring Deleted Document,恢复已删除的文档,
{function} of {fieldlabel},{function} of {fieldlabel},
Invalid template file for import,无效的导入模板文件,
Invalid or corrupted content for import,导入的内容无效或损坏,
Value {0} must in {1} format,值{0}必须为{1}格式,
{0} is a mandatory field asdadsf,{0}是必填字段asdadsf,
Could not map column {0} to field {1},无法将列{0}映射到字段{1},
Skipping Duplicate Column {0},跳过重复的列{0},
The column {0} has {1} different date formats. Automatically setting {2} as the default format as it is the most common. Please change other values in this column to this format.,列{0}具有{1}不同的日期格式。自动将{2}设置为默认格式，因为它是最常见的格式。请将此列中的其他值更改为此格式。,
You have reached the hourly limit for generating password reset links. Please try again later.,您已达到生成密码重置链接的小时限制。请稍后再试。,
Please hide the standard navbar items instead of deleting them,请隐藏标准导航栏项目，而不要删除它们,
DocType's name should not start or end with whitespace,DocType的名称不应以空格开头或结尾,
File name cannot have {0},文件名不能为{0},
{0} is not a valid file url,{0}不是有效的文件网址,
Error Attaching File,附加文件时出错,
Please generate keys for the Event Subscriber User {0} first.,请首先为事件订阅者用户{0}生成密钥。,
Please set API Key and Secret on the producer and consumer sites first.,请首先在生产者和消费者网站上设置API密钥和密钥。,
User {0} not found on the producer site,在生产者站点上找不到用户{0},
Event Subscriber has to be a System Manager.,事件订阅者必须是系统管理员。,
Row #{0}: Invalid Local Fieldname,行＃{0}：无效的本地字段名,
Row #{0}: Please set Mapping or Default Value for the field {1} since its a dependency field,行＃{0}：请为字段{1}设置“映射”或“默认值”，因为它是一个依赖项字段,
Row #{0}: Please set remote value filters for the field {1} to fetch the unique remote dependency document,第＃{0}行：请为字段{1}设置远程值过滤器，以获取唯一的远程依赖关系文档,
Paytm payment gateway settings,Paytm付款网关设置,
"Company, Fiscal Year and Currency defaults",公司，会计年度和货币默认值,
Razorpay Signature Verification Failed,Razorpay签名验证失败,
Google Drive - Could not locate - {0},Google云端硬盘-找不到-{0},
"Sync token was invalid and has been resetted, Retry syncing.",同步令牌无效，并且已重置，请重试同步。,
Please select another payment method. Paytm does not support transactions in currency '{0}',请选择其他付款方式。 Paytm不支持货币“ {0}”的交易,
Invalid Account SID or Auth Token.,无效的帐户SID或身份验证令牌。,
Please enable twilio settings before sending WhatsApp messages,请在发送WhatsApp消息之前启用twilio设置,
Delivery Failed,运送失败,
Twilio WhatsApp Message Error,Twilio WhatsApp消息错误,
A featured post must have a cover image,精选帖子必须有封面图片,
Load More,装载更多,
Published on,发表于,
Enable developer mode to create a standard Web Template,启用开发人员模式以创建标准的Web模板,
Was this article helpful?,本文是否有帮助？,
Thank you for your feedback!,感谢您的反馈意见！,
New Mention on {0},关于{0}的新提及,
Assignment Update on {0},{0}上的作业更新,
New Document Shared {0},共享新文档{0},
Energy Point Update on {0},{0}的能源点更新,
You cannot create a dashboard chart from single DocTypes,您不能从单个DocType创建仪表盘图表,
Invalid json added in the custom options: {0},自定义选项中添加了无效的json：{0},
Invalid JSON in card links for {0},卡链接中{0}的JSON无效,
Standard Not Set,未设定标准,
Please set the following documents in this Dashboard as standard first.,请首先在此仪表盘中设置以下文档为标准。,
Shared with the following Users with Read access:{0},与具有读取权限的以下用户共享：{0},
Already in the following Users ToDo list:{0},在以下“用户待办事项”列表中：{0},
Your assignment on {0} {1} has been removed by {2},您在{0} {1}上的分配已被{2}删除,
Invalid Credentials,无效证件,
Print UOM after Quantity,数量后打印UOM,
Uncaught Server Exception,未捕获的服务器异常,
There was an error building this page,建立此页面时发生错误,
Hide Traceback,隐藏回溯,
Value from this field will be set as the due date in the ToDo,来自此字段的值将在待办事项中设置为截止日期,
New module created {0},创建了新模块{0},
"Report has no numeric fields, please change the Report Name",报告没有数字字段，请更改报告名称,
There are documents which have workflow states that do not exist in this Workflow. It is recommended that you add these states to the Workflow and change their states before removing these states.,有些文档的工作流程状态在此工作流程中不存在。建议您将这些状态添加到工作流中，并在删除这些状态之前更改其状态。,
Worflow States Don't Exist,蠕变状态不存在,
Save Anyway,仍然保存,
Energy Points:,能量点：,
Review Points:,审查要点：,
Rank:,秩：,
Monthly Rank:,每月排名：,
Invalid expression set in filter {0} ({1}),在过滤器{0}（{1}）中设置了无效的表达式,
Invalid expression set in filter {0},过滤器{0}中设置的表达式无效,
{0} {1} added to Dashboard {2},{0} {1}已添加到仪表板{2},
Set Filters for {0},为{0}设置过滤器,
Not permitted to view {0},不允许查看{0},
Camera,相机,
Invalid filter: {0},无效的过滤器：{0},
Let's Get Started,让我们开始吧,
Reports & Masters,报告和大师,
New {0} {1} added to Dashboard {2},新的{0} {1}已添加到仪表板{2},
New {0} {1} created,创建了新的{0} {1},
New {0} Created,新创建的{0},
"Invalid ""depends_on"" expression set in filter {0}",在过滤器{0}中设置了无效的“ depends_on”表达式,
{0} Reports,{0}个报告,
There is {0} with the same filters already in the queue:,队列中已经有{0}个具有相同过滤条件的过滤器：,
There are {0} with the same filters already in the queue:,队列中已经有{0}个具有相同过滤条件的过滤器：,
Are you sure you want to generate a new report?,您确定要生成新报告吗？,
{0}: {1} vs {2},{0}：{1}与{2},
Add a {0} Chart,添加一个{0}图表,
Currently you have {0} review points,目前您有{0}个评论点,
{0} is not a valid DocType for Dynamic Link,{0}不是动态链接的有效DocType,
via Assignment Rule,通过分配规则,
Based on Field,基于现场,
Assign to the user set in this field,分配给该字段中的用户集,
Log Setting User,日志设置用户,
Log Settings,日志设定,
Error Log Notification,错误日志通知,
Users To Notify,用户通知,
Log Cleanup,日志清理,
Clear Error log After,之后清除错误日志,
Clear Activity Log After,之后清除活动日志,
Clear Email Queue After,之后清除电子邮件队列,
Please save to edit the template.,请保存以编辑模板。,
Google Analytics Anonymize IP,Google Analytics（分析）匿名IP,
Incorrect email or password. Please check your login credentials.,错误的邮箱帐号或密码。请检查您的登录凭据。,
Incorrect Configuration,配置错误,
You are not allowed to delete Standard Report,您无权删除标准报告,
You have unseen {0},您看不见{0},
Log cleanup and notification configuration,日志清理和通知配置,
State/Province,州/省,
Document Actions,文件动作,
Document Links,文件连结,
List Settings,清单设定,
Cannot delete standard link. You can hide it if you want,无法删除标准链接。你可以藏起来,
Cannot delete standard action. You can hide it if you want,无法删除标准操作。你可以藏起来,
Applied On,应用于,
Row Name,行名,
For DocType Link / DocType Action,对于DocType链接/ DocType操作,
Cannot edit filters for standard charts,无法编辑标准图表的过滤器,
Event Producer Last Update,活动制作人最后更新,
Default for 'Check' type of field {0} must be either '0' or '1',字段{0}的“检查”类型的默认值必须为“ 0”或“ 1”,
Non Negative,非负数,
Rules with higher priority number will be applied first.,优先级较高的规则将首先应用。,
Open URL in a New Tab,在新标签页中打开URL,
Align Right,右对齐,
Loading Filters...,正在加载过滤器...,
Count Customizations,计数自定义,
For Example: {} Open,例如：{}打开,
Choose Existing Card or create New Card,选择现有卡或创建新卡,
Number Cards,号码卡,
Function Based On,基于功能,
Add Filters,添加过滤器,
Skip,跳跃,
Dismiss,解雇,
Value cannot be negative for,值不能为负,
Value cannot be negative for {0}: {1},{0}的值不能为负：{1},
Negative Value,负值,
Authentication failed while receiving emails from Email Account: {0}.,从电子邮件帐户{0}接收电子邮件时，身份验证失败。,
Message from server: {0},来自服务器的消息：{0},
Add Row,添加行,
Analytics,Analytics（分析）,
Anonymous,匿名,
Author,作者,
Basic,基本,
Billing,账单,
Contact Details,联系人信息,
Datetime,时间日期,
Enable,启用,
Event,事件,
Full,充分,
Insert,插入,
Interests,兴趣,
Language Name,语言名称,
License,执照,
Limit,限制,
Log,日志,
Meeting,会议,
My Account,我的账户,
Newsletters,内部通讯,
Password,密码,
Pincode,PIN代码,
Please select prefix first,请先选择前缀,
Please set Email Address,请设置电子邮件地址,
Please set the series to be used.,请设置要使用的系列。,
Portal Settings,门户网站设置,
Reference Owner,参考者,
Region,区域,
Report Builder,报表生成器,
Sample,样本,
Saved,已保存,
Series {0} already used in {1},系列{0}已经被{1}使用,
Set as Default,设置为默认,
Shipping,运输中,
Standard,标准,
Test,测试,
Traceback,回溯,
Unable to find DocType {0},无法找到DocType {0},
Weekdays,工作日,
Workflow,工作流程,
You need to be logged in to access this page,您需要登录才能访问该页面,
County,县,
Images,图片,
Office,办公室,
Passive,被动,
Permanent,常驻,
Plant,厂,
Postal,邮政,
Previous,以前,
Shop,商店,
Subsidiary,子机构,
There is some problem with the file url: {0},有一些问题与文件的URL：{0},
Export Type,导出类型,
Last Sync On,上次同步开启,
Webhook Secret,Webhook的秘密,
Back to Home,回到主页,
Customize,定制,
Edit Profile,编辑个人资料,
File Manager,文件管理器,
Invite as User,邀请成为用户,
Newsletter,通讯,
Printing,打印,
Publish,发布,
Refreshing,正在刷新...,
Select All,全选,
Set,设置,
Setup Wizard,设置向导,
Update Details,更新详情,
You,你,
{0} Name,{0}名称,
Bold,胆大,
Center,中心,
Comment,评论,
Not Found,未找到,
User Id,用户身份,
Position,位置,
Crop,作物,
Topic,话题,
Public Transport,公共交通,
Request Data,请求数据,
Steps,脚步,
Reference DocType,参考文档类型,
Select Transaction,选择交易,
Help HTML,HTML帮助,
Series List for this Transaction,此交易的系列列表,
User must always select,用户必须始终选择,
Check this if you want to force the user to select a series before saving. There will be no default if you check this.,要用户手动选择序列的话请勾选。勾选此项后将不会选择默认序列。,
Prefix,字首,
This is the number of the last created transaction with this prefix,这就是以这个前缀的最后一个创建的事务数,
Update Series Number,更新序列号,
Validation Error,验证错误,
Andaman and Nicobar Islands,安达曼和尼科巴群岛,
Andhra Pradesh,安德拉邦,
Arunachal Pradesh,阿鲁纳恰尔邦,
Assam,阿萨姆邦,
Bihar,比哈尔,
Chandigarh,昌迪加尔,
Chhattisgarh,恰蒂斯加尔邦,
Dadra and Nagar Haveli,达德拉和纳加尔·哈维里,
Daman and Diu,达曼和丢,
Delhi,新德里,
Goa,果阿,
Gujarat,古吉拉特邦,
Haryana,哈里亚纳邦,
Himachal Pradesh,喜马al尔邦,
Jammu and Kashmir,查mu和克什米尔,
Jharkhand,贾坎德邦,
Karnataka,卡纳塔克邦,
Kerala,喀拉拉邦,
Lakshadweep Islands,拉克肖普群岛,
Madhya Pradesh,中央邦,
Maharashtra,马哈拉施特拉邦,
Manipur,马尼布尔,
Meghalaya,梅加拉亚邦,
Mizoram,咪唑仑,
Nagaland,那加兰邦,
Odisha,奥迪沙,
Other Territory,其他地区,
Pondicherry,朋迪榭里,
Punjab,旁遮普语,
Rajasthan,拉贾斯坦邦,
Sikkim,锡金,
Tamil Nadu,泰米尔纳德邦,
Telangana,Telangana,
Tripura,特里普拉,
Uttar Pradesh,北方邦,
Uttarakhand,北阿坎德邦,
West Bengal,西孟加拉邦,
Published on,发表于,
Bottom,底部,
Top,最佳,
