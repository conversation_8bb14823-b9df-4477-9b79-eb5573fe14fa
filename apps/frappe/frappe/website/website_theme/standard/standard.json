{"button_gradients": 0, "button_rounded_corners": 1, "button_shadows": 0, "creation": "2015-02-19 13:37:33.925909", "custom": 0, "custom_overrides": "", "docstatus": 0, "doctype": "Website Theme", "font_properties": "wght:400;500;600;700;800", "idx": 28, "modified": "2020-06-15 13:10:16.618476", "modified_by": "Administrator", "module": "Website", "name": "Standard", "owner": "Administrator", "theme": "Standard", "theme_scss": "$enable-shadows: false;\n$enable-gradients: false;\n$enable-rounded: true;\n\n// Bootstrap Variable Overrides\n\n\n@import \"frappe/public/scss/website\";\n\n\n\n// Custom Theme\n", "theme_url": "/assets/css/standard_style.css"}