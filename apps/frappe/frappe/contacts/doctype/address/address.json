{"actions": [], "allow_import": 1, "allow_rename": 1, "creation": "2013-01-10 16:34:32", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["address_details", "address_title", "address_type", "address_line1", "address_line2", "city", "county", "state", "country", "pincode", "column_break0", "email_id", "phone", "fax", "is_primary_address", "is_shipping_address", "disabled", "linked_with", "links"], "fields": [{"fieldname": "address_details", "fieldtype": "Section Break", "options": "fa fa-map-marker"}, {"fieldname": "address_title", "fieldtype": "Data", "label": "Address Title"}, {"fieldname": "address_type", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Address Type", "options": "Billing\nShipping\nOffice\nPersonal\nPlant\nPostal\nShop\nSubsidiary\nWarehouse\nCurrent\nPermanent\nOther", "reqd": 1}, {"fieldname": "address_line1", "fieldtype": "Data", "label": "Address Line 1", "length": 240, "reqd": 1}, {"fieldname": "address_line2", "fieldtype": "Data", "label": "Address Line 2", "length": 240}, {"fieldname": "city", "fieldtype": "Data", "in_global_search": 1, "in_list_view": 1, "label": "City/Town", "reqd": 1, "search_index": 1}, {"fieldname": "county", "fieldtype": "Data", "label": "County"}, {"fieldname": "state", "fieldtype": "Data", "label": "State/Province"}, {"fieldname": "country", "fieldtype": "Link", "in_global_search": 1, "in_standard_filter": 1, "label": "Country", "options": "Country", "reqd": 1, "search_index": 1}, {"fieldname": "pincode", "fieldtype": "Data", "label": "Postal Code", "search_index": 1}, {"fieldname": "column_break0", "fieldtype": "Column Break", "width": "50%"}, {"fieldname": "email_id", "fieldtype": "Data", "label": "Email Address", "options": "Email"}, {"fieldname": "phone", "fieldtype": "Data", "label": "Phone"}, {"fieldname": "fax", "fieldtype": "Data", "label": "Fax"}, {"default": "0", "fieldname": "is_primary_address", "fieldtype": "Check", "label": "Preferred Billing Address"}, {"default": "0", "fieldname": "is_shipping_address", "fieldtype": "Check", "label": "Preferred Shipping Address"}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"fieldname": "linked_with", "fieldtype": "Section Break", "label": "Reference", "options": "fa fa-pushpin"}, {"fieldname": "links", "fieldtype": "Table", "label": "Links", "options": "Dynamic Link"}], "icon": "fa fa-map-marker", "idx": 5, "links": [], "modified": "2023-11-20 17:28:41.698356", "modified_by": "Administrator", "module": "Contacts", "name": "Address", "owner": "Administrator", "permissions": [{"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase User", "share": 1, "write": 1}, {"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Maintenance User", "share": 1, "write": 1}, {"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "export": 1, "if_owner": 1, "print": 1, "read": 1, "role": "All", "write": 1}], "search_fields": "country, state", "sort_field": "modified", "sort_order": "DESC", "states": []}