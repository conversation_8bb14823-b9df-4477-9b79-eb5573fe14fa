{"actions": [], "allow_events_in_timeline": 1, "allow_import": 1, "allow_rename": 1, "creation": "2013-01-10 16:34:32", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["contact_section", "first_name", "middle_name", "last_name", "full_name", "email_id", "user", "address", "sync_with_google_contacts", "cb00", "status", "salutation", "designation", "gender", "phone", "mobile_no", "company_name", "image", "sb_00", "google_contacts", "google_contacts_id", "cb_00", "pulled_from_google_contacts", "sb_01", "email_ids", "phone_nos", "contact_details", "links", "is_primary_contact", "more_info", "department", "unsubscribed"], "fields": [{"fieldname": "contact_section", "fieldtype": "Section Break", "options": "fa fa-user"}, {"fieldname": "first_name", "fieldtype": "Data", "in_global_search": 1, "label": "First Name", "oldfieldname": "first_name", "oldfieldtype": "Data"}, {"bold": 1, "fieldname": "last_name", "fieldtype": "Data", "label": "Last Name", "oldfieldname": "last_name", "oldfieldtype": "Data"}, {"bold": 1, "fieldname": "email_id", "fieldtype": "Data", "in_global_search": 1, "in_list_view": 1, "label": "Email Address", "oldfieldname": "email_id", "oldfieldtype": "Data", "options": "Email", "read_only": 1, "search_index": 1}, {"fieldname": "user", "fieldtype": "Link", "in_global_search": 1, "label": "User Id", "options": "User"}, {"fieldname": "cb00", "fieldtype": "Column Break"}, {"default": "Passive", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Passive\nOpen\nReplied"}, {"fieldname": "salutation", "fieldtype": "Link", "label": "Salutation", "options": "Salutation"}, {"fieldname": "gender", "fieldtype": "Link", "label": "Gender", "options": "Gender"}, {"bold": 1, "fieldname": "phone", "fieldtype": "Data", "in_list_view": 1, "label": "Phone", "oldfieldname": "contact_no", "oldfieldtype": "Data", "options": "Phone", "read_only": 1}, {"fieldname": "image", "fieldtype": "Attach Image", "hidden": 1, "label": "Image", "print_hide": 1}, {"fieldname": "contact_details", "fieldtype": "Section Break", "label": "Reference", "options": "fa fa-pushpin"}, {"default": "0", "fieldname": "is_primary_contact", "fieldtype": "Check", "label": "Is Primary Contact", "oldfieldname": "is_primary_contact", "oldfieldtype": "Select"}, {"fieldname": "links", "fieldtype": "Table", "label": "Links", "options": "Dynamic Link"}, {"fieldname": "more_info", "fieldtype": "Section Break", "label": "More Information", "options": "fa fa-file-text"}, {"fieldname": "department", "fieldtype": "Data", "label": "Department"}, {"fieldname": "designation", "fieldtype": "Data", "label": "Designation"}, {"default": "0", "fieldname": "unsubscribed", "fieldtype": "Check", "label": "Unsubscribed"}, {"fieldname": "middle_name", "fieldtype": "Data", "label": "Middle Name"}, {"collapsible": 1, "depends_on": "eval:doc.sync_with_google_contacts || doc.pulled_from_google_contacts", "fieldname": "sb_00", "fieldtype": "Section Break", "label": "Google Contacts"}, {"fieldname": "email_ids", "fieldtype": "Table", "label": "Email IDs", "options": "Contact Email"}, {"fieldname": "address", "fieldtype": "Link", "label": "Address", "options": "Address"}, {"fieldname": "phone_nos", "fieldtype": "Table", "label": "Contact Numbers", "options": "Contact Phone"}, {"fieldname": "mobile_no", "fieldtype": "Data", "label": "Mobile No", "options": "Phone", "read_only": 1}, {"default": "0", "fieldname": "pulled_from_google_contacts", "fieldtype": "Check", "label": "Pulled from Google Contacts", "read_only": 1}, {"default": "0", "fieldname": "sync_with_google_contacts", "fieldtype": "Check", "label": "Sync with Google Contacts"}, {"fieldname": "google_contacts", "fieldtype": "Link", "label": "Google Contacts", "options": "Google Contacts"}, {"fieldname": "cb_00", "fieldtype": "Column Break"}, {"fieldname": "sb_01", "fieldtype": "Section Break", "label": "Contact Details"}, {"fieldname": "google_contacts_id", "fieldtype": "Data", "label": "Google Contacts Id", "read_only": 1}, {"fieldname": "company_name", "fieldtype": "Data", "label": "Company Name"}, {"fieldname": "full_name", "fieldtype": "Data", "hidden": 1, "label": "Full Name", "read_only": 1}], "icon": "fa fa-user", "idx": 1, "image_field": "image", "index_web_pages_for_search": 1, "links": [], "modified": "2023-12-08 15:52:37.525003", "modified_by": "Administrator", "module": "Contacts", "name": "Contact", "name_case": "Title Case", "naming_rule": "By script", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Master Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase Master Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Maintenance Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase User", "share": 1, "write": 1}, {"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Maintenance User", "share": 1, "write": 1}, {"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "if_owner": 1, "read": 1, "report": 1, "role": "All", "write": 1}], "show_title_field_in_link": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "full_name"}