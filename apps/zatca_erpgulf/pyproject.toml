[project]
name = "zatca_erpgulf"
authors = [
    { name = "ERPGulf", email = "<EMAIL>"}
]
description = "Implementaiton of Saudi E-Invoicing Phase-2 on Frappe ERPNext"
#requires-python = ">=3.80"
readme = "README.md"
dynamic = ["version"]
dependencies = [
    "asn1~=2.7.0",
    "pikepdf"
    # "frappe~=15.0.0" # Installed and managed by bench.
]

[build-system]
requires = ["flit_core >=3.4,<4"]
build-backend = "flit_core.buildapi"

# These dependencies are only installed when developer mode is enabled
[tool.bench.dev-dependencies]
# package_name = "~=1.1.0"
