[{"add_total_row": 0, "columns": [], "disabled": 0, "docstatus": 0, "doctype": "Report", "filters": [], "is_standard": "No", "javascript": "frappe.query_reports['Zatca Status Report'] = {\r\n\t\"filters\": [\r\n\t\t{\r\n\t\t\tfieldname: \"company\",\r\n\t\t\tlabel: __(\"Company\"),\r\n\t\t\tfieldtype: \"Link\",\r\n\t\t\toptions: \"Company\",\r\n\t\t\tdefault: frappe.defaults.get_user_default(\"Company\"),\r\n\t\t\treqd: 1,\r\n\t\t},\r\n\t\t\t\t{\r\n\t\t\tfieldname: \"dt_from\",\r\n\t\t\tlabel: __(\"From\"),\r\n\t\t\tfieldtype: \"Date\",\r\n\t\t\tdefault: frappe.datetime.add_months(frappe.datetime.get_today(), -12),\r\n\t\t},\t\t\r\n\r\n\t\t{\r\n\t\t\t\"fieldname\": \"dt_to\",\r\n\t\t\t\"label\": __(\"To\"),\r\n\t\t\t\"fieldtype\": \"Date\",\r\n\t\t    default: frappe.datetime.get_today(),\r\n\t\r\n\t\t\r\n\t\t},\t\t\r\n\t\t{\r\n\t\t\tfieldname: \"status\",\r\n\t\t\tlabel: __(\"Status\"),\r\n\t\t\t\"fieldtype\": \"Select\",\r\n            \"options\": \"\\nReported\\nCleared\\nCleared With Warning\\nPENDING\\nFAILED\", \r\n            \"default\": \"Reported\",\r\n\t\t\t\r\n\r\n\t\t},\t\t\r\n\r\n\t\t\r\n\t]\r\n};", "json": null, "letter_head": "Sample letterhead", "modified": "2024-12-26 11:11:43.320682", "module": "Zatca Erpgulf", "name": "Zatca Status Report", "prepared_report": 0, "query": "", "ref_doctype": "Sales Invoice", "reference_report": null, "report_name": "Zatca Status Report", "report_script": "def get_columns():\r\n    return [\r\n        {'fieldname': 'name', 'label': _('Inv.Number'), 'fieldtype': 'Link', 'options': 'Sales Invoice', 'width': 120},\r\n        {'fieldname': 'posting_date', 'label': _('Date'), 'fieldtype': 'Date', 'width': 110},\r\n        {'fieldname': 'customer_name', 'label': _('Customer'), 'fieldtype': 'Data', 'width': 200},\r\n        {'fieldname': 'grand_total', 'label': _('Total'), 'fieldtype': 'Currency', 'width': 100},\r\n        {'fieldname': 'custom_zatca_status', 'label': _('Status'), 'fieldtype': 'Data', 'width': 100}\r\n    ]\r\n\r\ndef get_data_and_chart(filters):\r\n    conditions = \"AND 1=1 \"\r\n    if(filters.get('status')):\r\n        conditions = f\"AND custom_zatca_status = '{filters.get('status')}'\"\r\n    dt_from = filters.get('dt_from')\r\n    dt_to = filters.get('dt_to')\r\n    conditions =conditions +  f\" AND posting_date between '{dt_from}' and '{dt_to}'\"\r\n\r\n    query = f\"\"\"\r\n    SELECT name ,\r\n               customer_name ,posting_date,\r\n               grand_total ,\r\n               custom_zatca_status \r\n        FROM `tabSales Invoice` where 1=1 {conditions} \r\n    \"\"\"\r\n   \r\n    data = frappe.db.sql(query, as_dict=True)\r\n\r\n    return data\r\n\r\ncolumns = get_columns()\r\ndata = get_data_and_chart(filters)\r\n\r\ndata = columns, data, None, None\r\n\r\n", "report_type": "Script Report", "roles": [{"parent": "Zatca Status Report", "parentfield": "roles", "parenttype": "Report", "role": "Accounts Manager"}, {"parent": "Zatca Status Report", "parentfield": "roles", "parenttype": "Report", "role": "Accounts User"}, {"parent": "Zatca Status Report", "parentfield": "roles", "parenttype": "Report", "role": "Sales User"}]}]