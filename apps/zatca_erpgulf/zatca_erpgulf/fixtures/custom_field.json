[{"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "ksa_einv_qr", "fieldtype": "Attach Image", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": null, "is_system_generated": 1, "is_virtual": 0, "label": "KSA E-Invoicing QR", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-11 15:44:50.045549", "module": "Zatca Erpgulf", "name": "Sales Invoice-ksa_einv_qr", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "ksa_einv_qr", "fieldtype": "Attach Image", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": null, "is_system_generated": 1, "is_virtual": 0, "label": "KSA E-Invoicing QR", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-15 14:36:33.074846", "module": "Zatca Erpgulf", "name": "POS Invoice-ksa_einv_qr", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_linked_doctype", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_section_break_7wg9o", "is_system_generated": 0, "is_virtual": 0, "label": "linked doctype", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:49:57.217027", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_linked_doctype", "no_copy": 0, "non_negative": 0, "options": "Company", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_column_break_jpb0r", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "ksa_einv_qr", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-02-20 10:55:05.505876", "module": "Zatca Erpgulf", "name": "POS Invoice-custom_column_break_jpb0r", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_ksa_einvoicing_xml", "fieldtype": "Data", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "ksa_einv_qr", "is_system_generated": 0, "is_virtual": 0, "label": "KSA E-Invoicing XML", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-24 09:44:42.409956", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_ksa_einvoicing_xml", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_send_pos_invoices_to_zatca_on_background", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_linked_doctype", "is_system_generated": 0, "is_virtual": 0, "label": "Send POS Invoices to Zatca on background", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-30 23:46:26.470106", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_send_pos_invoices_to_zatca_on_background", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_status_notification", "fieldtype": "HTML", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_column_break_jpb0r", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Status notification", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-02-19 17:26:56.751238", "module": "Zatca Erpgulf", "name": "POS Invoice-custom_zatca_status_notification", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_csr_config", "fieldtype": "Long Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_send_pos_invoices_to_zatca_on_background", "is_system_generated": 0, "is_virtual": 0, "label": "Csr Config", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:53:35.659499", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_csr_config", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_create_csr", "fieldtype": "<PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_csr_config", "is_system_generated": 0, "is_virtual": 0, "label": "Create CSR", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:53:12.894849", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_create_csr", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "zatca_customer_name_in_arabic", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "customer_name", "is_system_generated": 0, "is_virtual": 0, "label": "Customer Name Arabic", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-28 12:24:48.626054", "module": "Zatca Erpgulf", "name": "Customer-custom_customer_name_arabic", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Address", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_building_number", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "address_line2", "is_system_generated": 0, "is_virtual": 0, "label": "Building Number", "length": 4, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-10 20:50:57.202495", "module": "Zatca Erpgulf", "name": "Address-custom_building_number", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_b2c", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "zatca_customer_name_in_arabic", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca type Simplified ( B2C ) ", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-28 12:24:48.998060", "module": "Zatca Erpgulf", "name": "Customer-custom_b2c", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_csr_data", "fieldtype": "Long Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_create_csr", "is_system_generated": 0, "is_virtual": 0, "label": "CSR data", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:51:45.419629", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_csr_data", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "CRN", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Cost Center", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca__registration_type", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "company", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca - Registration Type", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-02-13 19:04:45.707617", "module": "Zatca Erpgulf", "name": "Cost Center-custom_zatca__registration_type", "no_copy": 0, "non_negative": 0, "options": "CRN\nMOM\nMLS\n700\nSAG\nOTH", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_third_party_invoice", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "naming_series", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca 3rd party invoice", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-18 10:01:32.100689", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_zatca_third_party_invoice", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Item Tax Template", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_exemption_reason_code", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "taxes", "is_system_generated": 0, "is_virtual": 0, "label": "Exemption Reason Code", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-05-31 11:24:07.523408", "module": "Zatca Erpgulf", "name": "Item Tax Template-custom_exemption_reason_code", "no_copy": 0, "non_negative": 0, "options": "Standard 15%\nVATEX-SA-29\nVATEX-SA-29-7\nVATEX-SA-30\nVATEX-SA-32\nVATEX-SA-33\nVATEX-SA-34-1\nVATEX-SA-34-2\nVATEX-SA-34-3\nVATEX-SA-34-4\nVATEX-SA-34-5\nVATEX-SA-35\nVATEX-SA-36\nVATEX-SA-EDU\nVATEX-SA-HEA\nVATEX-SA-MLTRY\nVATEX-SA-OOS", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "IQA", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_buyer_id_type", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_b2c", "is_system_generated": 0, "is_virtual": 0, "label": "Customer ID Type for Zatca", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-14 14:13:33.886152", "module": "Zatca Erpgulf", "name": "Customer-custom_buyer_id_type", "no_copy": 0, "non_negative": 0, "options": "TIN\nCRN\nMOM\nMLS\n700\nSAG\nNAT\nGCC\nIQA\nPAS\nOTH", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_column_break_kxlrk", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_csr_data", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:51:30.006811", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_column_break_kxlrk", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Cost Center", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca__registration_number", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca__registration_type", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca - Registration Number", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-02-13 19:04:45.651158", "module": "Zatca Erpgulf", "name": "Cost Center-custom_zatca__registration_number", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_nominal_invoice", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca_third_party_invoice", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca NOMINAL Invoice", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-18 10:01:32.858536", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_zatca_nominal_invoice", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Item Tax Template", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_tax_category", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_exemption_reason_code", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Tax Category", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-05-31 11:24:19.315563", "module": "Zatca Erpgulf", "name": "Item Tax Template-custom_zatca_tax_category", "no_copy": 0, "non_negative": 0, "options": "Standard\nZero Rated\nExempted\nServices outside scope of tax / Not subject to VAT", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_buyer_id", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_buyer_id_type", "is_system_generated": 0, "is_virtual": 0, "label": "Customer ID Number for Zatca", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-14 14:13:33.790879", "module": "Zatca Erpgulf", "name": "Customer-custom_buyer_id", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_pih", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_column_break_kxlrk", "is_system_generated": 0, "is_virtual": 0, "label": "pih", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:51:22.334154", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_pih", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Cost Center", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_branch_address", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca__registration_number", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Branch Address", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-02-13 19:04:45.590803", "module": "Zatca Erpgulf", "name": "Cost Center-custom_zatca_branch_address", "no_copy": 0, "non_negative": 0, "options": "Address", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom__company_name_in_arabic__", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "default_holiday_list", "is_system_generated": 0, "is_virtual": 0, "label": " Company name in Arabic  ", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-11 15:44:58.262110", "module": "Zatca Erpgulf", "name": "Company-custom__company_name_in_arabic__", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_export_invoice", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca_nominal_invoice", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Export Invoice", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-18 10:01:33.033864", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_zatca_export_invoice", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_certficate", "fieldtype": "Long Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_pih", "is_system_generated": 0, "is_virtual": 0, "label": "Certficate", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:50:25.321043", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_certficate", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_summary_invoice", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca_export_invoice", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Summary Invoice", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-18 10:01:32.472114", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_summary_invoice", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_private_key", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_certficate", "is_system_generated": 0, "is_virtual": 0, "label": "Private Key", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:51:12.734555", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_private_key", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "Not submitted", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_uuid", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "tax_id", "is_system_generated": 0, "is_virtual": 0, "label": "UUID", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-12 13:57:42.047846", "module": "Zatca Erpgulf", "name": "POS Invoice-custom_uuid", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "CRN", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_registration_type", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "cb0", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca - Registration Type", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-01-13 22:55:08.528347", "module": "Zatca Erpgulf", "name": "Company-custom_registration_type", "no_copy": 0, "non_negative": 0, "options": "CRN\nMOM\nMLS\n700\nSAG\nOTH", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_self_billed_invoice", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_summary_invoice", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Self billed Invoice", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-18 10:01:32.680633", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_self_billed_invoice", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_public_key", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_private_key", "is_system_generated": 0, "is_virtual": 0, "label": "Public key", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:51:04.725880", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_public_key", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_company_registration", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_registration_type", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca - Registration Number", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:43.219522", "module": "Zatca Erpgulf", "name": "Company-custom_company_registration", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca__location_for_csr_configuratoin", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_company_registration", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca - Location for CSR Configuratoin", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-02-24 12:38:04.765476", "module": "Zatca Erpgulf", "name": "Company-custom_zatca__location_for_csr_configuratoin", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": "customer.custom_b2c", "fetch_if_empty": 0, "fieldname": "custom_b2c", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "customer", "is_system_generated": 0, "is_virtual": 0, "label": "B2C", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-18 10:01:32.295950", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_b2c", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_section_break_7wg9o", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_otp", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:49:47.111252", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_section_break_7wg9o", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_otp", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "section_break_4qzz", "is_system_generated": 0, "is_virtual": 0, "label": "OTP", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:49:35.903361", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_otp", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_generate_compliance_csid", "fieldtype": "<PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_otp", "is_system_generated": 0, "is_virtual": 0, "label": "Generate Compliance CSID", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:49:28.011489", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_generate_compliance_csid", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca__company_category_for_csr_configuration", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca__location_for_csr_configuratoin", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca - Company category for CSR Configuration", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-02-24 12:37:57.714834", "module": "Zatca Erpgulf", "name": "Company-custom_zatca__company_category_for_csr_configuration", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_compliance_request_id_", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_generate_compliance_csid", "is_system_generated": 0, "is_virtual": 0, "label": "Compliance request id ", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:45:18.141191", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_compliance_request_id_", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "Not submitted", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_uuid", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "tax_id", "is_system_generated": 0, "is_virtual": 0, "label": "UUID", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-07-25 15:41:00.182668", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_uuid", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_basic_auth_from_csid", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_compliance_request_id_", "is_system_generated": 0, "is_virtual": 0, "label": "Basic Auth from CSID", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:45:05.320665", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_basic_auth_from_csid", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_user_invoice_number", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_uuid", "is_system_generated": 0, "is_virtual": 0, "label": "User Invoice Number", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-15 14:36:33.559987", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_user_invoice_number", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_column_break_dskhb", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_basic_auth_from_csid", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:44:46.856573", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_column_break_dskhb", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_generate_final_csids", "fieldtype": "<PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_column_break_dskhb", "is_system_generated": 0, "is_virtual": 0, "label": "Generate Final CSIDs", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:43:59.766650", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_generate_final_csids", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Zatca Multiple Setting", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_final_auth_csid", "fieldtype": "Long Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_generate_final_csids", "is_system_generated": 0, "is_virtual": 0, "label": "Final Auth CSID", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:43:45.268568", "module": "Zatca Erpgulf", "name": "Zatca Multiple Setting-custom_final_auth_csid", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "Not Submitted", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_status", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "due_date", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Status", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-07-25 15:41:00.392810", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_zatca_status", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "Not Submitted", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_status", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "return_against", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Status", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-12 13:57:42.544225", "module": "Zatca Erpgulf", "name": "POS Invoice-custom_zatca_status", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_tax_category", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca_status", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Tax Category", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-07-25 15:41:00.613645", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_zatca_tax_category", "no_copy": 0, "non_negative": 0, "options": "Standard\nZero Rated\nExempted\nServices outside scope of tax / Not subject to VAT", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_tax_category", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca_status", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Tax Category", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-12 13:57:42.386469", "module": "Zatca Erpgulf", "name": "POS Invoice-custom_zatca_tax_category", "no_copy": 0, "non_negative": 0, "options": "Standard\nZero Rated\nExempted\nServices outside scope of tax / Not subject to VAT", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_exemption_reason_code", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca_tax_category", "is_system_generated": 0, "is_virtual": 0, "label": "Exemption Reason Code", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-07-25 15:41:00.837511", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_exemption_reason_code", "no_copy": 0, "non_negative": 0, "options": "Standard 15%\nVATEX-SA-29\nVATEX-SA-29-7\nVATEX-SA-30\nVATEX-SA-32\nVATEX-SA-33\nVATEX-SA-34-1\nVATEX-SA-34-2\nVATEX-SA-34-3\nVATEX-SA-34-4\nVATEX-SA-34-5\nVATEX-SA-35\nVATEX-SA-36\nVATEX-SA-EDU\nVATEX-SA-HEA\nVATEX-SA-MLTRY\nVATEX-SA-OOS", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_exemption_reason_code", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca_tax_category", "is_system_generated": 0, "is_virtual": 0, "label": "Exemption Reason Code", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-12 13:57:42.237615", "module": "Zatca Erpgulf", "name": "POS Invoice-custom_exemption_reason_code", "no_copy": 0, "non_negative": 0, "options": "VATEX-SA-29\nVATEX-SA-29-7\nVATEX-SA-30\nVATEX-SA-32\nVATEX-SA-33\nVATEX-SA-34-1\nVATEX-SA-34-2\nVATEX-SA-34-3\nVATEX-SA-34-4\nVATEX-SA-34-5\nVATEX-SA-35\nVATEX-SA-36 \nVATEX-SA-EDU\nVATEX-SA-HEA\nVATEX-SA-MLTRY\nVATEX-SA-OOS", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "95", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_discount_reason_code", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_exemption_reason_code", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Discount reason code", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-19 11:19:44.844675", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_zatca_discount_reason_code", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_integrations", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_exemption_reason_code", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Details", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-01-27 17:07:01.906638", "module": "Zatca Erpgulf", "name": "POS Invoice-custom_integrations", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "Discount", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_discount_reason", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca_discount_reason_code", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Discount reason", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-19 11:19:44.664553", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_zatca_discount_reason", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_column_break_8zybs", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_integrations", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-01-27 17:07:00.589185", "module": "Zatca Erpgulf", "name": "POS Invoice-custom_column_break_8zybs", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_submit_line_item_discount_to_zatca", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca_discount_reason", "is_system_generated": 0, "is_virtual": 0, "label": "Submit line item discount to Zatca.", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-19 11:19:44.504178", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_submit_line_item_discount_to_zatca", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_pos_name", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_column_break_8zybs", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca POS Name", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-30 23:46:26.205348", "module": "Zatca Erpgulf", "name": "POS Invoice-custom_zatca_pos_name", "no_copy": 1, "non_negative": 0, "options": "Zatca Multiple Setting", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_status_notification", "fieldtype": "HTML", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "column_break_14", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Status Notification", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-02-19 17:20:57.902341", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_zatca_status_notification", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_setting", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "dashboard_tab", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Setting", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-28 23:35:06.464838", "module": "Zatca Erpgulf", "name": "Company-custom_zatca_setting", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_details_and_otp", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca_setting", "is_system_generated": 0, "is_virtual": 0, "label": "Details and OTP", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:42.963846", "module": "Zatca Erpgulf", "name": "Company-custom_details_and_otp", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_invoice_enabled", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_details_and_otp", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca invoice enabled", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:42.836584", "module": "Zatca Erpgulf", "name": "Company-custom_zatca_invoice_enabled", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_costcenter", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca_invoice_enabled", "is_system_generated": 0, "is_virtual": 0, "label": "Conside Cost-Center as branch. ( If checked- Need to add Branch CRN and Address in Cost Center )", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-02-14 10:00:04.156493", "module": "Zatca Erpgulf", "name": "Company-custom_costcenter", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_submit_line_item_discount_to_zatca", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_costcenter", "is_system_generated": 0, "is_virtual": 0, "label": "POS Submit line item discount to Zatca ( Applicable for POS Only ) ", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-01-15 10:46:45.053636", "module": "Zatca Erpgulf", "name": "Company-custom_submit_line_item_discount_to_zatca", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "Phase-1", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_phase_1_or_2", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_submit_line_item_discount_to_zatca", "is_system_generated": 0, "is_virtual": 0, "label": "Phase 1 or 2 ?", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-11 12:39:08.266876", "module": "Zatca Erpgulf", "name": "Company-custom_phase_1_or_2", "no_copy": 0, "non_negative": 0, "options": "Phase-1\nPhase-2", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_send_einvoice_background", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_phase_1_or_2", "is_system_generated": 0, "is_virtual": 0, "label": "Display ZATCA response on the screen ( Untick for silent submission ) ", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:42.711021", "module": "Zatca Erpgulf", "name": "Company-custom_send_einvoice_background", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_select", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_send_einvoice_background", "is_system_generated": 0, "is_virtual": 0, "label": "Select", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:42.454484", "module": "Zatca Erpgulf", "name": "Company-custom_select", "no_copy": 0, "non_negative": 0, "options": "Simulation\nSandbox\nProduction", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "Live", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_send_invoice_to_zatca", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_select", "is_system_generated": 0, "is_virtual": 0, "label": "Send POS invoice to zatca", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:42.583050", "module": "Zatca Erpgulf", "name": "Company-custom_send_invoice_to_zatca", "no_copy": 0, "non_negative": 0, "options": "Live\nBatches\nBackground", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_submit_or_not", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_send_invoice_to_zatca", "is_system_generated": 0, "is_virtual": 0, "label": "If Invoice still in Draft status, submit and send to Zatca ( Applicable for B2C/POS Invoice only ) ", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-02-18 10:30:45.925114", "module": "Zatca Erpgulf", "name": "Company-custom_submit_or_not", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_start_time", "fieldtype": "Time", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_submit_or_not", "is_system_generated": 0, "is_virtual": 0, "label": "Background submission Start Time", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-02-11 17:40:43.439547", "module": "Zatca Erpgulf", "name": "Company-custom_start_time", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_end_time", "fieldtype": "Time", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_start_time", "is_system_generated": 0, "is_virtual": 0, "label": "Background submission End Time", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-02-11 17:40:43.310175", "module": "Zatca Erpgulf", "name": "Company-custom_end_time", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_create_csr_configuration", "fieldtype": "<PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_end_time", "is_system_generated": 0, "is_virtual": 0, "label": "Create CSR Configuration", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-02-24 12:38:42.529449", "module": "Zatca Erpgulf", "name": "Company-custom_create_csr_configuration", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "csr.common.name=TST-*********-399999999900003\ncsr.serial.number=1-TST|2-TST|3-(8digit)-(4digit)-(4digit)-(4digit)-(12digit)\ncsr.organization.identifier= 399999999900003\ncsr.organization.unit.name= 399999999900003\ncsr.organization.name=Your Company name\ncsr.country.name=SA\ncsr.invoice.type=1100\ncsr.location.address=RIYADH\ncsr.industry.business.category=Your company activities\n", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_csr_config", "fieldtype": "Long Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_create_csr_configuration", "is_system_generated": 0, "is_virtual": 0, "label": "CSR Config", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-02-13 12:54:12.750832", "module": "Zatca Erpgulf", "name": "Company-custom_csr_config", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_create_csr", "fieldtype": "<PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_csr_config", "is_system_generated": 0, "is_virtual": 0, "label": "Create CSR", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:42.196869", "module": "Zatca Erpgulf", "name": "Company-custom_create_csr", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_csr_data", "fieldtype": "Long Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_create_csr", "is_system_generated": 0, "is_virtual": 0, "label": "CSR Data", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:42.064107", "module": "Zatca Erpgulf", "name": "Company-custom_csr_data", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_attach_xml_with_invoice", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_csr_data", "is_system_generated": 0, "is_virtual": 0, "label": "Attach XML with invoice", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:41.934232", "module": "Zatca Erpgulf", "name": "Company-custom_attach_xml_with_invoice", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_attach_xml_with_qr_code", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_attach_xml_with_invoice", "is_system_generated": 0, "is_virtual": 0, "label": "Attach XML with QR code", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:41.807210", "module": "Zatca Erpgulf", "name": "Company-custom_attach_xml_with_qr_code", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_attach_qr_code_doctype", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_attach_xml_with_qr_code", "is_system_generated": 0, "is_virtual": 0, "label": "Attach QR code doctype", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:41.681820", "module": "Zatca Erpgulf", "name": "Company-custom_attach_qr_code_doctype", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_attach_e_invoice_send_status_with_invoice", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_attach_qr_code_doctype", "is_system_generated": 0, "is_virtual": 0, "label": "Attach e_invoice send status with invoice", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:41.554417", "module": "Zatca Erpgulf", "name": "Company-custom_attach_e_invoice_send_status_with_invoice", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "6G6AZCE7wl6Ahy9mQWLu7Euo43Qmf6uHceioxUpnmx8=", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_pih", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_attach_e_invoice_send_status_with_invoice", "is_system_generated": 0, "is_virtual": 0, "label": "pih", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:41.426292", "module": "Zatca Erpgulf", "name": "Company-custom_pih", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_keys__certificate_for_zatca", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_pih", "is_system_generated": 0, "is_virtual": 0, "label": "Keys & Certificate For Zatca", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:41.298696", "module": "Zatca Erpgulf", "name": "Company-custom_keys__certificate_for_zatca", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_private_key", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_keys__certificate_for_zatca", "is_system_generated": 0, "is_virtual": 0, "label": "Private Key", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:41.173346", "module": "Zatca Erpgulf", "name": "Company-custom_private_key", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_public_key", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_private_key", "is_system_generated": 0, "is_virtual": 0, "label": "public key", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:41.044123", "module": "Zatca Erpgulf", "name": "Company-custom_public_key", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_certificate", "fieldtype": "Long Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_public_key", "is_system_generated": 0, "is_virtual": 0, "label": "certificate", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:40.917616", "module": "Zatca Erpgulf", "name": "Company-custom_certificate", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_urls__api_endpoints", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_certificate", "is_system_generated": 0, "is_virtual": 0, "label": "URLs / API EndPoints", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:40.789923", "module": "Zatca Erpgulf", "name": "Company-custom_urls__api_endpoints", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_sandbox_url", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_urls__api_endpoints", "is_system_generated": 0, "is_virtual": 0, "label": "Sandbox URL", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:40.663303", "module": "Zatca Erpgulf", "name": "Company-custom_sandbox_url", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_simulation_url", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_sandbox_url", "is_system_generated": 0, "is_virtual": 0, "label": "Simulation URL", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:40.522892", "module": "Zatca Erpgulf", "name": "Company-custom_simulation_url", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": " https://gw-fatoora.zatca.gov.sa/e-invoicing/core/", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_production_url", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_simulation_url", "is_system_generated": 0, "is_virtual": 0, "label": "Production URL", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:40.393933", "module": "Zatca Erpgulf", "name": "Company-custom_production_url", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_compliance_csid_generation", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_production_url", "is_system_generated": 0, "is_virtual": 0, "label": "Compliance CSID Generation", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:40.265913", "module": "Zatca Erpgulf", "name": "Company-custom_compliance_csid_generation", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_otp", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_compliance_csid_generation", "is_system_generated": 0, "is_virtual": 0, "label": "OTP", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:40.139365", "module": "Zatca Erpgulf", "name": "Company-custom_otp", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_generate_compliance_csid", "fieldtype": "<PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_otp", "is_system_generated": 0, "is_virtual": 0, "label": "Generate Compliance CSID", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:40.006377", "module": "Zatca Erpgulf", "name": "Company-custom_generate_compliance_csid", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_basic_auth_from_csid", "fieldtype": "Long Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_generate_compliance_csid", "is_system_generated": 0, "is_virtual": 0, "label": "Basic Auth from CSID", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:39.881630", "module": "Zatca Erpgulf", "name": "Company-custom_basic_auth_from_csid", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_compliance_request_id_", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_basic_auth_from_csid", "is_system_generated": 0, "is_virtual": 0, "label": "Compliance request id ", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:39.754725", "module": "Zatca Erpgulf", "name": "Company-custom_compliance_request_id_", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_compliance_check_check_all_options_below", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_compliance_request_id_", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Compliance Check. Check all options below.", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:39.627128", "module": "Zatca Erpgulf", "name": "Company-custom_zatca_compliance_check_check_all_options_below", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_validation_type", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_zatca_compliance_check_check_all_options_below", "is_system_generated": 0, "is_virtual": 0, "label": "Validation Type", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:39.498064", "module": "Zatca Erpgulf", "name": "Company-custom_validation_type", "no_copy": 0, "non_negative": 0, "options": "Simplified Invoice\nStandard Invoice\nSimplified Credit Note\nStandard Credit Note\nSimplified Debit Note\nStandard Debit Note", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_sample_invoice_number_to_test", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_validation_type", "is_system_generated": 0, "is_virtual": 0, "label": "Sample Invoice Number to Test", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:39.372003", "module": "Zatca Erpgulf", "name": "Company-custom_sample_invoice_number_to_test", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_check_compliance", "fieldtype": "<PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_sample_invoice_number_to_test", "is_system_generated": 0, "is_virtual": 0, "label": "Check Compliance", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:39.247188", "module": "Zatca Erpgulf", "name": "Company-custom_check_compliance", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_production__csid__generation", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_check_compliance", "is_system_generated": 0, "is_virtual": 0, "label": "Final  CSID  Generation", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:39.119725", "module": "Zatca Erpgulf", "name": "Company-custom_production__csid__generation", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_generate_production_csids", "fieldtype": "<PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_production__csid__generation", "is_system_generated": 0, "is_virtual": 0, "label": "Generate Final CSIDs", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:38.988649", "module": "Zatca Erpgulf", "name": "Company-custom_generate_production_csids", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_basic_auth_from_production", "fieldtype": "Long Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_generate_production_csids", "is_system_generated": 0, "is_virtual": 0, "label": "Final Auth CSID", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-24 20:19:38.575241", "module": "Zatca Erpgulf", "name": "Company-custom_basic_auth_from_production", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_offline_machines", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_section_break_hwvcd", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Offline Machines", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-12-26 10:44:30.072084", "module": "Zatca Erpgulf", "name": "Company-custom_zatca_offline_machines", "no_copy": 0, "non_negative": 0, "options": "Zatca POS table", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_reponse_from_zatca", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "against_income_account", "is_system_generated": 0, "is_virtual": 0, "label": "<PERSON><PERSON><PERSON> from Zatca", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-07 11:04:09.505771", "module": "Zatca Erpgulf", "name": "POS Invoice-custom_reponse_from_zatca", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_full_response", "fieldtype": "Long Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 1, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_reponse_from_zatca", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Full Response", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-07 11:04:09.370588", "module": "Zatca Erpgulf", "name": "POS Invoice-custom_zatca_full_response", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_full_response", "fieldtype": "Long Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "customer_group", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca Full Response", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-07 11:04:09.658208", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_zatca_full_response", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_integrations", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "connections_tab", "is_system_generated": 0, "is_virtual": 0, "label": "Offline-Integrations", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-01-27 17:07:02.344002", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_integrations", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_zatca_pos_name", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_integrations", "is_system_generated": 0, "is_virtual": 0, "label": "Zatca POS Machine name", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-01-27 17:07:02.949809", "module": "Zatca Erpgulf", "name": "Sales Invoice-custom_zatca_pos_name", "no_copy": 1, "non_negative": 0, "options": "Zatca Multiple Setting", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}]