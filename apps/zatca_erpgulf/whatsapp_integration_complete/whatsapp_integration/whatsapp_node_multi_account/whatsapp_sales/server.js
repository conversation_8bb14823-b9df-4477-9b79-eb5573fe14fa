const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const express = require('express');
const fileUpload = require('express-fileupload');

const app = express();
app.use(express.json());
app.use(fileUpload());

const client = new Client({ authStrategy: new LocalAuth({ clientId: 'sales' }) });

client.on('qr', qr => qrcode.generate(qr, { small: true }));
client.on('ready', () => console.log('WhatsApp Web (sales) is ready!'));
client.initialize();

app.post('/send', async (req, res) => {
    const { phone, message } = req.body;
    try {
        await client.sendMessage(`${phone}@c.us`, message);
        res.send({ status: 'success' });
    } catch (err) {
        res.status(500).send({ status: 'error', message: err.message });
    }
});

app.listen(3000, () => console.log('WhatsApp Node server running on port 3000 for sales'));
