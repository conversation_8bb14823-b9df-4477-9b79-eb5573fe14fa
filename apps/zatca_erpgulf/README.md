Saudi Arabian E-Invoicing Zatca Phase-2
A Frappe ERPNext App for users in Saudi Arabia, to be compliant with country tax laws

Features:

Creates CSR by sending API to Zatca server

Creates Compliance token

Do system compliance test as per Zatca UBL formats

Send API Compliance test

Get Production token

Send Standard invoices for Clearance to Zatca

Send Simplified invoices for Reporting

Send Credit notes, debit notes for both Clearance and Reporting

Get QR Code from Zatca

Attach QR COde from zatca on the invoices.

Keep success_log for Zatca for future reference

Error logs in case of error

Reports to compare with Zatca portal statitics.

Installation and Troubleshooting

Frappe Cloud users can install it from Marketplace

Others can follow standard Frappe methods for installing apps. Please see it here below bench

get-app https://github.com/ERPGulf/zatca_erpgulf.git

bench --site yoursite.erpgulf.com install-app zatca_erpgulf

bench --site yoursite.erpgulf.com migrate

Goto Help->About and make sure you have Zatca app installaed.

We have published a video tutorial on how to use this . ( https://www.youtube.com/watch?v=P0ChplXoKYg )

<NAME_EMAIL> for implementation support or customization.

Husna M
