<div class="card address-card h-100">
	<div class="check" style="position: absolute; right: 15px; top: 15px;">
		<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check"><polyline points="20 6 9 17 4 12"></polyline></svg>
	</div>
	<div class="card-body">
		<h5 class="card-title">{{ address.title }}</h5>
		<p class="card-text text-muted">
			{{ address.display }}
		</p>
		<a href="/address/{{address.name}}" class="card-link">{{ _('Edit') }}</a>
	</div>
</div>
