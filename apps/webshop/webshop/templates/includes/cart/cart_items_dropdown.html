{% from "webshop/templates/includes/order/order_macros.html" import item_name_and_description_cart %}

{% for d in doc.items %}
<div class="row cart-dropdown">
    <div class="col-sm-8 col-xs-8 col-name-description">
        {{ item_name_and_description_cart(d) }}
    </div>
    <div class="col-sm-4 col-xs-4 text-right col-amount">
        {{ d.get_formatted("amount") }}
    </div>
</div>
{% endfor %}
