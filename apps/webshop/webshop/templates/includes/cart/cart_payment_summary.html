<!-- Payment -->
{% if cart_settings.enable_checkout or cart_settings.show_price_in_quotation %}
<h6>
	{{ _("Payment Summary") }}
</h6>
{% endif %}

<div class="card h-100">
	<div class="card-body p-0">
		{% if cart_settings.enable_checkout or cart_settings.show_price_in_quotation %}
			<table class="table w-100">
				<tr>
					{% set total_items = frappe.utils.cstr(frappe.utils.flt(doc.total_qty, 0)) %}
					<td class="bill-label">{{ _("Net Total (") + total_items + _(" Items)") }}</td>
					<td class="bill-content net-total text-right">{{ doc.get_formatted("net_total") }}</td>
				</tr>

				<!-- taxes -->
				{% for d in doc.taxes %}
					{% if d.tax_amount %}
						<tr>
							<td class="bill-label">
								{{ d.description }}
							</td>
							<td class="bill-content text-right">
								{{ d.get_formatted("tax_amount") }}
							</td>
						</tr>
					{% endif %}
				{% endfor %}
			</table>

			<!-- TODO: Apply Coupon Dialog-->
			<!-- {% set show_coupon_code = cart_settings.show_apply_coupon_code_in_website and cart_settings.enable_checkout %}
			{% if show_coupon_code %}
				<button class="btn btn-coupon-code w-100 text-left">
					<svg width="24" height="24" viewBox="0 0 24 24" stroke="var(--gray-600)" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M19 15.6213C19 15.2235 19.158 14.842 19.4393 14.5607L20.9393 13.0607C21.5251 12.4749 21.5251 11.5251 20.9393 10.9393L19.4393 9.43934C19.158 9.15804 19 8.7765 19 8.37868V6.5C19 5.67157 18.3284 5 17.5 5H15.6213C15.2235 5 14.842 4.84196 14.5607 4.56066L13.0607 3.06066C12.4749 2.47487 11.5251 2.47487 10.9393 3.06066L9.43934 4.56066C9.15804 4.84196 8.7765 5 8.37868 5H6.5C5.67157 5 5 5.67157 5 6.5V8.37868C5 8.7765 4.84196 9.15804 4.56066 9.43934L3.06066 10.9393C2.47487 11.5251 2.47487 12.4749 3.06066 13.0607L4.56066 14.5607C4.84196 14.842 5 15.2235 5 15.6213V17.5C5 18.3284 5.67157 19 6.5 19H8.37868C8.7765 19 9.15804 19.158 9.43934 19.4393L10.9393 20.9393C11.5251 21.5251 12.4749 21.5251 13.0607 20.9393L14.5607 19.4393C14.842 19.158 15.2235 19 15.6213 19H17.5C18.3284 19 19 18.3284 19 17.5V15.6213Z" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
						<path d="M15 9L9 15" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
						<path d="M10.5 9.5C10.5 10.0523 10.0523 10.5 9.5 10.5C8.94772 10.5 8.5 10.0523 8.5 9.5C8.5 8.94772 8.94772 8.5 9.5 8.5C10.0523 8.5 10.5 8.94772 10.5 9.5Z" fill="white" stroke-linecap="round" stroke-linejoin="round"/>
						<path d="M15.5 14.5C15.5 15.0523 15.0523 15.5 14.5 15.5C13.9477 15.5 13.5 15.0523 13.5 14.5C13.5 13.9477 13.9477 13.5 14.5 13.5C15.0523 13.5 15.5 13.9477 15.5 14.5Z" fill="white" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
					<span class="ml-2">Apply Coupon</span>
				</button>
			{% endif %} -->

			<table class="table w-100 grand-total mt-6">
				<tr>
					<td class="bill-content net-total">{{ _("Grand Total") }}</td>
					<td class="bill-content net-total text-right">{{ doc.get_formatted("grand_total") }}</td>
				</tr>
			</table>
		{% endif %}
	</div>
</div>

<!-- TODO: Apply Coupon Dialog-->
<!-- <script>
	frappe.ready(() => {
		$('.btn-coupon-code').click((e) => {
			const $btn = $(e.currentTarget);
			const d = new frappe.ui.Dialog({
				title: __('Coupons'),
				fields: [
					{
						fieldname: 'coupons_area',
						fieldtype: 'HTML'
					}
				]
			});
			d.show();
		});
	});
</script> -->