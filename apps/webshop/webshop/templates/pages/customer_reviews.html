{% extends "templates/web.html" %}
{% from "webshop/templates/includes/macros.html" import user_review, ratings_summary %}

{% block title %} {{ _("Customer Reviews") }} {% endblock %}

{% block page_content %}
<div class="product-container reviews-full-page col-md-12">
	{% if enable_reviews %}
		<!-- Title and Action -->
		<div class="w-100 mb-6 d-flex">
			<div class="reviews-header col-9">
				{{ _("Customer Reviews") }}
			</div>

			<div class="write-a-review-btn col-3">
				<!-- Write a Review for legitimate users -->
				{% if frappe.session.user != "Guest" and user_is_customer %}
					<button class="btn btn-write-review"
						data-web-item="{{ web_item }}">
						{{ _("Write a Review") }}
					</button>
				{% endif %}
			</div>
		</div>

		<!-- Summary -->
		{{ ratings_summary(reviews, reviews_per_rating, average_rating, average_whole_rating, for_summary=True, total_reviews=total_reviews) }}


		<!-- Reviews and Comments -->
		<div class="mt-8">
			{% if reviews %}
				{{ user_review(reviews) }}

				{% if not reviews | len >= total_reviews %}
					<button class="btn btn-light btn-view-more mr-2 mt-4 mb-4 w-30"
						data-web-item="{{ web_item }}">
						{{ _("View More") }}
					</button>
				{% endif %}

			{% else %}
				<h6 class="text-muted mt-6">
					{{ _("No Reviews") }}
				</h6>
			{% endif %}
		</div>
	{% else %}
		<!-- If reviews are disabled -->
		<div class="text-center">
			<h3 class="text-muted mt-8">
				{{ _("No Reviews") }}
			</h3>
		</div>
	{% endif %}
</div>

{% endblock %}

{% block base_scripts %}
<!-- js should be loaded in body! -->
<script type="text/javascript" src="/assets/frappe/js/lib/jquery/jquery.min.js"></script>
<script type="text/javascript" src="/assets/js/frappe-web.min.js"></script>
<script type="text/javascript" src="/assets/js/control.min.js"></script>
<script type="text/javascript" src="/assets/js/dialog.min.js"></script>
<script type="text/javascript" src="/assets/js/bootstrap-4-web.min.js"></script>
{% endblock %}
