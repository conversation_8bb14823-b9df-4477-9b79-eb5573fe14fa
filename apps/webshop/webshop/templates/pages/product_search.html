{% extends "templates/web.html" %}

{% block title %} {{ _("Product Search") }} {% endblock %}

{% block header %}<h2>{{ _("Product Search") }}</h2>{% endblock %}

{% block page_content %}
<script>{% include "templates/includes/product_list.js" %}</script>

<script>
frappe.ready(function() {
	var txt = frappe.utils.get_url_arg("search");
	$(".search-results").html('{{ _("Search results for") + ": " + html2text(frappe.form_dict.search or "") | e | trim }}');
	window.search = txt;
	window.start = 0;
	window.get_product_list();
});
</script>

<div class="product-search-content">
    <h3 class="search-results">{{ _("Search Results") }}</h3>
	<div id="search-list" class="row">

	</div>
	<div style="text-align: center;">
		<div class="more-btn"
			style="display: none; text-align: center;">
            <button class="btn btn-light">{{ _("More...") }}</button>
		</div>
	</div>
</div>
{% endblock %}
