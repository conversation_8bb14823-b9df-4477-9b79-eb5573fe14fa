<!-- Is reused to render within tabs as well as independently -->
{% if website_specifications %}
<div class="{{ 'mt-2' if not show_tabs else 'mt-5'}} item-website-specification">
	<div class="col-md-11">
		{% if not show_tabs %}
			<div class="product-title mb-5 mt-4">
				{{ _("Product Details") }}
			</div>
		{% endif %}
		<table class="table">
		{% for d in website_specifications -%}
			<tr>
				<td class="spec-label">{{ _(d.label) }}</td>
				<td class="spec-content">{{ _(d.description) }}</td>
			</tr>
		{%- endfor %}
		</table>
	</div>
</div>
{% endif %}
