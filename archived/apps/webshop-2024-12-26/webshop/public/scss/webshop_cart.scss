@import "frappe/public/scss/common/mixins";

:root {
	--green-info: #38A160;
	--product-bg-color: white;
	--body-bg-color:  var(--gray-50);
}

body.product-page {
	background: var(--body-bg-color);
}

.item-breadcrumbs {
	.breadcrumb-container {
		a {
			color: var(--gray-900);
		}
	}
}

.carousel-control {
	height: 42px;
	width: 42px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: white;
	box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.08), 0px 1px 2px 1px rgba(0, 0, 0, 0.06);
	border-radius: 100px;
}

.carousel-control-prev,
.carousel-control-next {
	opacity: 1;
	width: 8%;

	@media (max-width: 1200px) {
		width: 10%;
	}
	@media (max-width: 768px) {
		width: 15%;
	}
}

.carousel-body {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.carousel-content {
	max-width: 400px;
	margin-left: 5rem;
	margin-right: 5rem;
}

.card {
	border: none;
}

.product-category-section {
	.card:hover {
		box-shadow: 0px 16px 45px 6px rgba(0, 0, 0, 0.08), 0px 8px 10px -10px rgba(0, 0, 0, 0.04);
	}

	.card-grid {
		display: grid;
		grid-gap: 15px;
		grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
	}
}

.no-image-item {
	height: 340px;
	width: 340px;
	background: var(--gray-100);
	border-radius: var(--border-radius);
	font-size: 2rem;
	color: var(--gray-500);
	display: flex;
	align-items: center;
	justify-content: center;
}

.item-card-group-section {
	.card {
		height: 100%;
		align-items: center;
		justify-content: center;

		&:hover {
			box-shadow: 0px 16px 60px rgba(0, 0, 0, 0.08), 0px 8px 30px -20px rgba(0, 0, 0, 0.04);
			transition: box-shadow 400ms;
		}
	}

	.card:hover, .card:focus-within {
		.btn-add-to-cart-list {
			visibility: visible;
		}
		.like-action {
			visibility: visible;
		}
		.btn-explore-variants {
			visibility: visible;
		}
	}


	.card-img-container {
		height: 210px;
		width: 100%;
	}

	.card-img {
		max-height: 210px;
		object-fit: contain;
		margin-top: 1.25rem;
	}

	.no-image {
		@include flex(flex, center, center, null);
		height: 220px;
		background: var(--gray-100);
		width: 100%;
		border-radius: var(--border-radius) var(--border-radius) 0 0;
		font-size: 2rem;
		color: var(--gray-500);
	}

	.no-image-list {
		@include flex(flex, center, center, null);
		height: 150px;
		background: var(--gray-100);
		border-radius: var(--border-radius);
		font-size: 2rem;
		color: var(--gray-500);
		margin-top: 15px;
		margin-bottom: 15px;
	}

	.card-body-flex {
		display: flex;
		flex-direction: column;
	}

	.product-title {
		font-size: 14px;
		color: var(--gray-800);
		font-weight: 500;
	}

	.product-description {
		font-size: 12px;
		color: var(--text-color);
		margin: 20px 0;
		display: -webkit-box;
		-webkit-line-clamp: 6;
		-webkit-box-orient: vertical;

		p {
			margin-bottom: 0.5rem;
		}
	}

	.product-category {
		font-size: 13px;
		color: var(--text-muted);
		margin: var(--margin-sm) 0;
	}

	.product-price {
		font-size: 18px;
		font-weight: 600;
		color: var(--text-color);
		margin: var(--margin-sm) 0;
		margin-bottom: auto !important;

		.striked-price {
			font-weight: 500;
			font-size: 15px;
			color: var(--gray-500);
		}
	}

	.product-info-green {
		color: var(--green-info);
		font-weight: 600;
	}

	.item-card {
		padding: var(--padding-sm);
		min-width: 300px;
	}

	.wishlist-card {
		padding: var(--padding-sm);
		min-width: 260px;
		.card-body-flex {
			display: flex;
			flex-direction: column;
		}
	}
}

#products-list-area, #products-grid-area {
	padding: 0 5px;
}

.list-row {
	background-color: white;
	padding-bottom: 1rem;
	padding-top: 1.5rem !important;
	border-radius: 8px;
	border-bottom: 1px solid var(--gray-50);

	&:hover, &:focus-within {
		box-shadow: 0px 16px 60px rgba(0, 0, 0, 0.08), 0px 8px 30px -20px rgba(0, 0, 0, 0.04);
		transition: box-shadow 400ms;

		.btn-add-to-cart-list {
			visibility: visible;
		}
		.like-action-list {
			visibility: visible;
		}
		.btn-explore-variants {
			visibility: visible;
		}
	}

	.product-code {
		padding-top: 0 !important;
	}

	.btn-explore-variants {
		min-width: 135px;
		max-height: 30px;
		float: right;
		padding: 0.25rem 1rem;
	}
}

[data-doctype="Item Group"],
#page-index {
	.page-header {
		font-size: 20px;
		font-weight: 700;
		color: var(--text-color);
	}

	.filters-section {
		.title-section {
			border-bottom: 1px solid var(--table-border-color);
		}

		.filter-title {
			font-weight: 500;
		}

		.clear-filters {
			font-size: 13px;
		}

		.filter-lookup-input {
			background-color: white;
			border: 1px solid var(--gray-300);

			&:focus {
				border: 1px solid var(--primary);
			}
		}

		.filter-label {
			font-size: 11px;
			font-weight: 600;
			color: var(--gray-700);
			text-transform: uppercase;
		}

		.filter-block {
			border-bottom: 1px solid var(--table-border-color);
		}

		.checkbox {
			.label-area {
				font-size: 13px;
				color: var(--gray-800);
			}
		}
	}
}

.product-filter {
	width: 14px !important;
	height: 14px !important;
}

.discount-filter {
	&:before {
		width: 14px !important;
		height: 14px !important;
	}
}

.list-image {
	border: none !important;
	overflow: hidden;
	max-height: 200px;
	background-color: white;
}

.product-container {
	@include card($padding: var(--padding-md));
	background-color: var(--product-bg-color) !important;
	min-height: fit-content;

	.product-details {
		max-width: 50%;

		.btn-add-to-cart {
			font-size: 14px;
		}
	}

	&.item-main {
		.product-image {
			width: 100%;
		}
	}

	.expand {
		max-width: 100% !important; // expand in absence of slideshow
	}

	@media (max-width: 789px) {
		.product-details {
			max-width: 90% !important;

			.btn-add-to-cart {
				font-size: 14px;
			}
		}
	}

	.btn-add-to-wishlist {
		svg use {
			--icon-stroke: #F47A7A;
		}
	}

	.btn-view-in-wishlist {
		svg use {
			fill: #F47A7A;
			--icon-stroke: none;
		}
	}

	.product-title {
		font-size: 16px;
		font-weight: 600;
		color: var(--text-color);
		padding: 0 !important;
	}

	.product-description {
		font-size: 13px;
		color: var(--gray-800);
	}

	.product-image {
		border-color: var(--table-border-color) !important;
		padding: 15px;

		@media (max-width: var(--md-width)) {
			height: 300px;
			width: 300px;
		}

		@media (min-width: var(--lg-width)) {
			height: 350px;
			width: 350px;
		}

	 	img {
			object-fit: contain;
		}
	}

	.item-slideshow {

		@media (max-width: var(--md-width)) {
			max-height: 320px;
		}

		@media (min-width: var(--lg-width)) {
			max-height: 430px;
		}

		overflow: auto;
	}

	.item-slideshow-image {
		height: 4rem;
		width: 6rem;
		object-fit: contain;
		padding: 0.5rem;
		border: 1px solid var(--table-border-color);
		border-radius: 4px;
		cursor: pointer;

		&:hover, &.active {
			border-color: var(--primary);
		}
	}

	.item-cart {
		.product-price {
			font-size: 22px;
			color: var(--text-color);
			font-weight: 600;

			.formatted-price {
				color: var(--text-muted);
				font-size: 14px;
			}
		}

		.no-stock {
			font-size: var(--text-base);
		}

		.offers-heading {
			font-size: 16px !important;
			color: var(--text-color);
			.tag-icon {
				--icon-stroke: var(--gray-500);
			}
		}

		.w-30-40 {
			width: 30%;

			@media (max-width: 992px) {
				width: 40%;
			}
		}
	}

	.tab-content {
		font-size: 14px;
	}
}

// Item Recommendations
.recommended-item-section {
	padding-right: 0;

	.recommendation-header {
		font-size: 16px;
		font-weight: 500
	}

	.recommendation-container {
		padding: .5rem;
		min-height: 0px;

		.r-item-image {
			min-height: 100px;
			width: 40%;

			.r-product-image {
				padding: 2px 15px;
			}

			.no-image-r-item {
				display: flex; justify-content: center;
				background-color: var(--gray-200);
				align-items: center;
				color: var(--gray-400);
				margin-top: .15rem;
				border-radius: 6px;
				height: 100%;
				font-size: 24px;
			}
		}

		.r-item-info {
			font-size: 14px;
			padding-right: 0;
			padding-left: 10px;
			width: 60%;

			a {
				color: var(--gray-800);
				font-weight: 400;
			}

			.item-price {
				font-size: 15px;
				font-weight: 600;
				color: var(--text-color);
			}

			.striked-item-price {
				font-weight: 500;
				color: var(--gray-500);
			}
		}
	}
}

.product-code {
	padding: .5rem 0;
	color: var(--text-muted);
	font-size: 14px;
	.product-item-group {
		padding-right: .25rem;
		border-right: solid 1px var(--text-muted);
	}

	.product-item-code {
		padding-left: .5rem;
	}
}

.item-configurator-dialog {
	.modal-body {
		padding-bottom: var(--padding-xl);

		.status-area {
			.alert {
				padding: var(--padding-xs) var(--padding-sm);
				font-size: var(--text-sm);
			}
		}

		.form-layout {
			max-height: 50vh;
			overflow-y: auto;
		}

		.section-body {
			.form-column {
				.form-group {
					.control-label {
						font-size: var(--text-md);
						color: var(--gray-700);
					}

					.help-box {
						margin-top: 2px;
						font-size: var(--text-sm);
					}
				}
			}
		}
	}
}

.item-group-slideshow {

	.carousel-inner.rounded-carousel {
		border-radius: var(--card-border-radius);
	}
}

.sub-category-container {
	padding-bottom: .5rem;
	margin-bottom: 1.25rem;
	border-bottom: 1px solid var(--table-border-color);

	.heading {
		color: var(--gray-500);
	}
}

.scroll-categories {
	.category-pill {
		display: inline-block;
		width: fit-content;
		padding: 6px 12px;
		margin-bottom: 8px;
		background-color: #ecf5fe;
		font-size: 14px;
		border-radius: 18px;
		color: var(--blue-500);
	}
}


.shopping-badge {
	position: relative;
	top: -10px;
	left: -12px;
	background: var(--red-600);
	align-items: center;
	height: 16px;
	font-size: 10px;
	border-radius: 50%;
}


.cart-animate {
	animation: wiggle 0.5s linear;
}
@keyframes wiggle {
	8%,
	41% {
		transform: translateX(-10px);
	}
	25%,
	58% {
		transform: translate(10px);
	}
	75% {
		transform: translate(-5px);
	}
	92% {
		transform: translate(5px);
	}
	0%,
	100% {
		transform: translate(0);
	}
}

.total-discount {
	font-size: 14px;
	color: var(--primary-color) !important;
}

#page-cart {
	.shopping-cart-header {
		font-weight: bold;
	}

	.cart-container {
		color: var(--text-color);

		.frappe-card {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			height: fit-content;
		}

		.cart-items-header {
			font-weight: 600;
		}

		.cart-table {
			tr {
				margin-bottom: 1rem;
			}

			th, tr, td {
				border-color: var(--border-color);
				border-width: 1px;
			}

			th {
				font-weight: normal;
				font-size: 13px;
				color: var(--text-muted);
				padding: var(--padding-sm) 0;
			}

			td {
				padding: var(--padding-sm) 0;
				color: var(--text-color);
			}

			.cart-item-image {
				width: 20%;
				min-width: 100px;
				img {
					max-height: 112px;
				}
			}

			.cart-items {
				.item-title {
					width: 80%;
					font-size: 14px;
					font-weight: 500;
					color: var(--text-color);
				}

				.item-subtitle {
					color: var(--text-muted);
					font-size: 13px;
				}

				.item-subtotal {
					font-size: 14px;
					font-weight: 500;
				}

				.sm-item-subtotal {
					font-size: 14px;
					font-weight: 500;
					display: none;

					@media (max-width: 992px) {
						display: unset !important;
					}
				}

				.item-rate {
					font-size: 13px;
					color: var(--text-muted);
				}

				.free-tag {
					padding: 4px 8px;
					border-radius: 4px;
					background-color: var(--dark-green-50);
				}

				textarea {
					width: 70%;
					height: 30px;
					font-size: 14px;
				}

			}

			.cart-tax-items {
				.item-grand-total {
					font-size: 16px;
					font-weight: 700;
					color: var(--text-color);
				}
			}

			.column-sm-view {
				@media (max-width: 992px) {
					display: none !important;
				}
			}

			.item-column {
				width: 50%;
				@media (max-width: 992px) {
					width: 70%;
				}
			}

			.remove-cart-item {
				border-radius: 6px;
				border: 1px solid var(--gray-100);
				width: 28px;
				height: 28px;
				font-weight: 300;
				color: var(--gray-700);
				background-color: var(--gray-100);
				float: right;
				cursor: pointer;
				margin-top: .25rem;
				justify-content: center;
			}

			.remove-cart-item-logo {
				margin-top: 2px;
				margin-left: 2.2px;
				fill: var(--gray-700) !important;
			}
		}

		.cart-payment-addresses {
			hr {
				border-color: var(--border-color);
			}
		}

		.payment-summary {
			h6 {
				padding-bottom: 1rem;
				border-bottom: solid 1px var(--gray-200);
			}

			table {
				font-size: 14px;
				td {
					padding: 0;
					padding-top: 0.35rem !important;
					border: none !important;
				}

				&.grand-total {
					border-top: solid 1px var(--gray-200);
				}
			}

			.bill-label {
				color: var(--gray-600);
			}

			.bill-content {
				font-weight: 500;
				&.net-total {
					font-size: 16px;
					font-weight: 600;
				}
			}

			.btn-coupon-code {
				font-size: 14px;
				border: dashed 1px var(--gray-400);
				box-shadow: none;
			}
		}

		.number-spinner {
			width: 75%;
			min-width: 105px;
			.cart-btn {
				border: none;
				background: var(--gray-100);
				box-shadow: none;
				width: 24px;
				height: 28px;
				align-items: center;
				justify-content: center;
				display: flex;
				font-size: 20px;
				font-weight: 300;
				color: var(--gray-700);
			}

			.cart-qty {
				height: 28px;
				font-size: 13px;
				&:disabled {
					background: var(--gray-100);
					opacity: 0.65;
				}
			}
		}

		.place-order-container {
			.btn-place-order {
				float: right;
			}
		}
	}

	.t-and-c-container {
		padding: 1.5rem;
	}

	.t-and-c-terms {
		font-size: 14px;
	}
}

.no-image-cart-item {
	max-height: 112px;
	display: flex; justify-content: center;
	background-color: var(--gray-200);
	align-items: center;
	color: var(--gray-400);
	margin-top: .15rem;
	border-radius: 6px;
	height: 100%;
	font-size: 24px;
}

.cart-empty.frappe-card {
	min-height: 76vh;
	@include flex(flex, center, center, column);

	.cart-empty-message {
		font-size: 18px;
		color: var(--text-color);
		font-weight: bold;
	}
}

.address-card {
	.card-title {
		font-size: 14px;
		font-weight: 500;
	}

	.card-body {
		max-width: 80%;
	}

	.card-text {
		font-size: 13px;
		color: var(--gray-700);
	}

	.card-link {
		font-size: 13px;

		svg use {
			stroke: var(--primary-color);
		}
	}

	.btn-change-address {
		border: 1px solid var(--primary-color);
		color: var(--primary-color);
		box-shadow: none;
	}
}

.address-header {
	margin-top: .15rem;padding: 0;
}

.btn-new-address {
	float: right;
	font-size: 15px !important;
	color: var(--primary-color) !important;
}

.btn-new-address:hover, .btn-change-address:hover {
	color: var(--primary-color) !important;
}

.modal .address-card {
	.card-body {
		padding: var(--padding-sm);
		border-radius: var(--border-radius);
		border: 1px solid var(--dark-border-color);
	}
}

.cart-indicator {
	position: absolute;
	text-align: center;
	width: 22px;
	height: 22px;
	left: calc(100% - 40px);
	top: 22px;

	border-radius: 66px;
	box-shadow: 0px 2px 6px rgba(17, 43, 66, 0.08), 0px 1px 4px rgba(17, 43, 66, 0.1);
	background: white;
	color: var(--primary-color);
	font-size: 14px;

	&.list-indicator {
		position: unset;
		margin-left: auto;
	}
}


.like-action {
	visibility: hidden;
	text-align: center;
	position: absolute;
	cursor: pointer;
	width: 28px;
	height: 28px;
	left: 20px;
	top: 20px;

	/* White */
	background: white;
	box-shadow: 0px 2px 6px rgba(17, 43, 66, 0.08), 0px 1px 4px rgba(17, 43, 66, 0.1);
	border-radius: 66px;

	&.like-action-wished {
		visibility: visible !important;
	}

	@media (max-width: 992px) {
		visibility: visible !important;
	}
}

.like-action-list {
	visibility: hidden;
	text-align: center;
	position: absolute;
	cursor: pointer;
	width: 28px;
	height: 28px;
	left: 20px;
	top: 0;

	/* White */
	background: white;
	box-shadow: 0px 2px 6px rgba(17, 43, 66, 0.08), 0px 1px 4px rgba(17, 43, 66, 0.1);
	border-radius: 66px;

	&.like-action-wished {
		visibility: visible !important;
	}

	@media (max-width: 992px) {
		visibility: visible !important;
	}
}

.like-action-item-fp {
	visibility: visible !important;
	position: unset;
	float: right;
}

.like-animate {
	animation: expand cubic-bezier(0.04, 0.4, 0.5, 0.95) 1.6s forwards 1;
}

@keyframes expand {
	30% {
	  transform: scale(1.3);
	}
	50% {
	  transform: scale(0.8);
	}
	70% {
		transform: scale(1.1);
	}
	100% {
	  transform: scale(1);
	}
  }

.not-wished {
	cursor: pointer;
	--icon-stroke: #F47A7A !important;

	&:hover {
		fill: #F47A7A;
	}
}

.wished {
	--icon-stroke: none;
	fill: #F47A7A !important;
}

.list-row-checkbox {
	&:before {
		display: none;
	}

	&:checked:before {
		display: block;
		z-index: 1;
	}
}

#pay-for-order {
	padding: .5rem 1rem; // Pay button in SO
}

.btn-explore-variants {
	visibility: hidden;
	box-shadow: none;
	margin: var(--margin-sm) 0;
	width: 90px;
	max-height: 50px; // to avoid resizing on window resize
	flex: none;
	transition: 0.3s ease;

	color: white;
	background-color: var(--orange-500);
	border: 1px solid var(--orange-500);
	font-size: 13px;

	&:hover {
		color: white;
	}
}

.btn-add-to-cart-list{
	visibility: hidden;
	box-shadow: none;
	margin: var(--margin-sm) 0;
	// margin-top: auto !important;
	max-height: 50px; // to avoid resizing on window resize
	flex: none;
	transition: 0.3s ease;

	font-size: 13px;

	&:hover {
		color: white;
	}

	@media (max-width: 992px) {
		visibility: visible !important;
	}
}

.go-to-cart-grid {
	max-height: 30px;
	margin-top: 1rem !important;
}

.go-to-cart {
	max-height: 30px;
	float: right;
}

.remove-wish {
	background-color: white;
	position: absolute;
	cursor: pointer;
	top:10px;
	right: 20px;
	width: 32px;
	height: 32px;

	border-radius: 50%;
	border: 1px solid var(--gray-100);
	box-shadow: 0px 2px 6px rgba(17, 43, 66, 0.08), 0px 1px 4px rgba(17, 43, 66, 0.1);
}

.wish-removed {
	display: none;
}

.item-website-specification {
	font-size: .875rem;
	.product-title {
		font-size: 18px;
	}

	.table {
		width: 70%;
	}

	td {
		border: none !important;
	}

	.spec-label {
		color: var(--gray-600);
	}

	.spec-content {
		color: var(--gray-800);
	}
}

.reviews-full-page {
	padding: 1rem 2rem;
}

.ratings-reviews-section {
	border-top: 1px solid #E2E6E9;
	padding: .5rem 1rem;
}

.reviews-header {
	font-size: 20px;
	font-weight: 600;
	color: var(--gray-800);
	display: flex;
	align-items: center;
	padding: 0;
}

.btn-write-review {
	float: right;
	padding: .5rem 1rem;
	font-size: 14px;
	font-weight: 400;
	border: none !important;
	box-shadow: none;

	color: var(--gray-900);
	background-color: var(--gray-100);

	&:hover {
		box-shadow: var(--btn-shadow);
	}
}

.btn-view-more {
	font-size: 14px;
}

.rating-summary-section {
	display: flex;
}

.rating-summary-title {
	margin-top: 0.15rem;
	font-size: 18px;
}

.rating-summary-numbers {
	display: flex;
	flex-direction: column;
	align-items: center;

	border-right: solid 1px var(--gray-100);
}

.user-review-title {
	margin-top: 0.15rem;
	font-size: 15px;
	font-weight: 600;
}

.rating {
	--star-fill: var(--gray-300);
	.star-hover {
		--star-fill: var(--yellow-100);
	}
	.star-click {
		--star-fill: var(--yellow-300);
	}
}

.ratings-pill {
	background-color: var(--gray-100);
	padding: .5rem 1rem;
	border-radius: 66px;
}

.review {
	max-width: 80%;
	line-height: 1.6;
	padding-bottom: 0.5rem;
	border-bottom: 1px solid #E2E6E9;
}

.review-signature {
	display: flex;
	font-size: 13px;
	color: var(--gray-500);
	font-weight: 400;

	.reviewer {
		padding-right: 8px;
		color: var(--gray-600);
	}
}

.rating-progress-bar-section {
	padding-bottom: 2rem;

	.rating-bar-title {
		margin-left: -15px;
	}

	.rating-progress-bar {
		margin-bottom: 4px;
		height: 7px;
		margin-top: 6px;

		.progress-bar-cosmetic {
			background-color: var(--gray-600);
			border-radius: var(--border-radius);
		}
	}
}

.offer-container {
	font-size: 14px;
}

#search-results-container {
	border: 1px solid var(--gray-200);
	padding: .25rem 1rem;

	.category-chip {
		background-color: var(--gray-100);
		border: none !important;
		box-shadow: none;
	}

	.recent-search {
		padding: .5rem .5rem;
		border-radius: var(--border-radius);

		&:hover {
			background-color: var(--gray-100);
		}
	}
}

#search-box {
	background-color: white;
	height: 100%;
	padding-left: 2.5rem;
	border: 1px solid var(--gray-200);
}

.search-icon {
	position: absolute;
	left: 0;
	top: 0;
	width: 2.5rem;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	padding-bottom: 1px;
}

#toggle-view {
	float: right;

	.btn-primary {
		background-color: var(--gray-600);
		box-shadow: 0 0 0 0.2rem var(--gray-400);
	}
}

.placeholder-div {
	height:80%;
	width: -webkit-fill-available;
	padding: 50px;
	text-align: center;
	background-color: #F9FAFA;
	border-top-left-radius: calc(0.75rem - 1px);
	border-top-right-radius: calc(0.75rem - 1px);
}
.placeholder {
	font-size: 72px;
}

[data-path="cart"] {
	.modal-backdrop {
		background-color: var(--gray-50); // lighter backdrop only on cart freeze
	}
}

.item-thumb {
	height: 50px;
	max-width: 80px;
	min-width: 80px;
	object-fit: cover;
}

.brand-line {
	color: gray;
}

.btn-next, .btn-prev {
	font-size: 14px;
}

.alert-error {
	color: #e27a84;
	background-color: #fff6f7;
	border-color: #f5c6cb;
}

.font-md {
	font-size: 14px !important;
}

.in-green {
	color: var(--green-info) !important;
	font-weight: 500;
}

.has-stock {
	font-weight: 400 !important;
}

.out-of-stock {
	font-weight: 400;
	font-size: 14px;
	line-height: 20px;
	color: #F47A7A;
}

.mt-minus-2 {
	margin-top: -2rem;
}

.mt-minus-1 {
	margin-top: -1rem;
}

.tooltip-content {
	position: absolute;
	bottom: 100%;
	left: 0;
	z-index: 9999;
	padding: 2px 6px;
	border-radius: var(--border-radius-sm);
	background-color: var(--bg-dark-gray);
	color: var(--text-dark);
	font-size: var(--text-xs);
	opacity: 0;
	cursor: copy;
	transition: opacity 0.3s, transform 3s;
	pointer-events: none;
}

.show-tooltip .frappe-control:hover .tooltip-content {
	opacity: 1;
	transform: translate3d(0, 0, 0);
	pointer-events: auto;
}
