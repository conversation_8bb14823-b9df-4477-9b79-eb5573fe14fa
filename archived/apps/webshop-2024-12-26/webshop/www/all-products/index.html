{% from "webshop/templates/includes/macros.html" import attribute_filter_section, field_filter_section, discount_range_filters %}
{% extends "templates/web.html" %}

{% block title %}{{ _('All Products') }}{% endblock %}
{% block header %}
<div class="mb-6">{{ _('All Products') }}</div>
{% endblock header %}

{% block page_content %}
<div class="row">
	<!-- Items section -->
	<div id="product-listing" class="col-12 order-2 col-md-9 order-md-2 item-card-group-section">
		<!-- Rendered via JS -->
	</div>

	<!-- Filters Section -->
	<div class="col-12 order-1 col-md-3 order-md-1">
		<div class="collapse d-md-block mr-4 filters-section" id="product-filters">
			<div class="d-flex justify-content-between align-items-center mb-5 title-section">
				<div class="mb-4 filters-title" > {{ _('Filters') }} </div>
				<a class="mb-4 clear-filters" href="/all-products">{{ _('Clear All') }}</a>
			</div>
			<!-- field filters -->
			{% if field_filters %}
				{{ field_filter_section(field_filters) }}
			{% endif %}

			<!-- attribute filters -->
			{% if attribute_filters %}
				{{ attribute_filter_section(attribute_filters) }}
			{% endif %}
		</div>

	</div>
</div>

<script>
	frappe.ready(() => {
		$('.btn-prev, .btn-next').click((e) => {
			const $btn = $(e.target);
			$btn.prop('disabled', true);
			const start = $btn.data('start');
			let query_params = frappe.utils.get_query_params();
			query_params.start = start;
			let path = window.location.pathname + '?' + frappe.utils.get_url_from_dict(query_params);
			window.location.href = path;
		});
	});
</script>

{% endblock %}
