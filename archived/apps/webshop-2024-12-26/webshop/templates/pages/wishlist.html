{% extends "templates/web.html" %}

{% block title %} {{ _("Wishlist") }} {% endblock %}

{% block header %}<h3 class="shopping-cart-header mt-2 mb-6">{{ _("Wishlist") }}</h1>{% endblock %}

{% block page_content %}
{% if items %}
	<div class="row">
		<div class="col-md-12 item-card-group-section">
			<div class="row products-list">
					{% from "webshop/templates/includes/macros.html" import wishlist_card %}
					{% for item in items %}
						{{ wishlist_card(item, settings) }}
					{% endfor %}
			</div>
		</div>
	</div>
{% else %}
	<div class="cart-empty frappe-card">
		<div class="cart-empty-state">
			<img src="/assets/webshop/images/cart-empty-state.png" alt="Empty Cart">
		</div>
		<div class="cart-empty-message mt-4">{{ _('Wishlist is empty!') }}</p>
	</div>
{% endif %}

{% endblock %}
