{"actions": [], "beta": 1, "creation": "2021-03-23 16:47:26.542226", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["website_item", "user", "customer", "column_break_3", "item", "published_on", "reviews_section", "review_title", "rating", "comment"], "fields": [{"fieldname": "website_item", "fieldtype": "Link", "label": "Website Item", "options": "Website Item", "read_only": 1, "reqd": 1}, {"fieldname": "user", "fieldtype": "Link", "in_list_view": 1, "label": "User", "options": "User", "read_only": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fetch_from": "website_item.item_code", "fieldname": "item", "fieldtype": "Link", "in_list_view": 1, "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>", "read_only": 1}, {"fieldname": "reviews_section", "fieldtype": "Section Break", "label": "Reviews"}, {"fieldname": "rating", "fieldtype": "Rating", "in_list_view": 1, "label": "Rating"}, {"fieldname": "comment", "fieldtype": "Small Text", "label": "Comment", "read_only": 1}, {"fieldname": "review_title", "fieldtype": "Data", "label": "Review Title", "read_only": 1}, {"fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customer", "read_only": 1}, {"fieldname": "published_on", "fieldtype": "Data", "label": "Published on", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2023-10-13 17:35:32.281964", "modified_by": "Administrator", "module": "Webshop", "name": "Item Review", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Website Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "report": 1, "role": "Customer", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}