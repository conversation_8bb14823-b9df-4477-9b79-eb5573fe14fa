{"actions": [], "allow_rename": 1, "creation": "2023-12-14 22:14:23.853797", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "view", "column_break_nxmx", "item_name", "section_break_qlos", "description", "column_break_jxff", "image", "thumbnail", "route"], "fields": [{"fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "<PERSON><PERSON>", "options": "Website Item", "reqd": 1}, {"fieldname": "column_break_nxmx", "fieldtype": "Column Break"}, {"fetch_from": "item_code.item_name", "fieldname": "item_name", "fieldtype": "Data", "in_list_view": 1, "label": "Item Name"}, {"fieldname": "view", "fieldtype": "<PERSON><PERSON>", "in_list_view": 1, "label": "View"}, {"fieldname": "section_break_qlos", "fieldtype": "Section Break"}, {"fetch_from": "item_code.web_long_description", "fieldname": "description", "fieldtype": "Text Editor", "in_list_view": 1, "label": "Description"}, {"fieldname": "column_break_jxff", "fieldtype": "Column Break"}, {"fetch_from": "item_code.website_image", "fetch_if_empty": 1, "fieldname": "image", "fieldtype": "Attach Image", "label": "Image"}, {"fetch_from": "item_code.thumbnail", "fieldname": "thumbnail", "fieldtype": "Attach Image", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"fetch_from": "item_code.route", "fieldname": "route", "fieldtype": "Small Text", "label": "Route", "read_only": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2023-12-14 22:33:25.457721", "modified_by": "Administrator", "module": "Webshop", "name": "Homepage Featured Product", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}