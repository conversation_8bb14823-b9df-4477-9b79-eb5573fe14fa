{"actions": [], "creation": "2021-03-10 19:03:00.662714", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "website_item", "web_item_name", "column_break_3", "item_name", "item_group", "item_details_section", "description", "column_break_7", "route", "image", "image_view", "section_break_8", "warehouse_section", "warehouse"], "fields": [{"fetch_from": "website_item.item_code", "fetch_if_empty": 1, "fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>", "reqd": 1}, {"fieldname": "website_item", "fieldtype": "Link", "in_list_view": 1, "label": "Website Item", "options": "Website Item", "read_only": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fetch_from": "item_code.item_name", "fetch_if_empty": 1, "fieldname": "item_name", "fieldtype": "Data", "label": "Item Name", "read_only": 1}, {"collapsible": 1, "fieldname": "item_details_section", "fieldtype": "Section Break", "label": "<PERSON><PERSON>", "read_only": 1}, {"fetch_from": "item_code.description", "fetch_if_empty": 1, "fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "read_only": 1}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fetch_from": "item_code.image", "fetch_if_empty": 1, "fieldname": "image", "fieldtype": "Attach", "hidden": 1, "label": "Image"}, {"fetch_from": "item_code.image", "fetch_if_empty": 1, "fieldname": "image_view", "fieldtype": "Image", "hidden": 1, "label": "Image View", "options": "image", "print_hide": 1}, {"fieldname": "warehouse_section", "fieldtype": "Section Break", "label": "Warehouse"}, {"fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Warehouse", "options": "Warehouse", "read_only": 1}, {"fieldname": "section_break_8", "fieldtype": "Section Break"}, {"fetch_from": "item_code.item_group", "fetch_if_empty": 1, "fieldname": "item_group", "fieldtype": "Link", "label": "Item Group", "options": "Item Group", "read_only": 1}, {"fetch_from": "website_item.route", "fetch_if_empty": 1, "fieldname": "route", "fieldtype": "Small Text", "label": "Route", "read_only": 1}, {"fetch_from": "website_item.web_item_name", "fetch_if_empty": 1, "fieldname": "web_item_name", "fieldtype": "Data", "label": "Website Item Name", "read_only": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2021-08-09 10:30:41.964802", "modified_by": "Administrator", "module": "Webshop", "name": "Wishlist Item", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}