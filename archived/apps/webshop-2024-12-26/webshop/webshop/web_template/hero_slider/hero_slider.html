{%- macro slide(image, title, subtitle, action, label, index, align="Left", theme="Dark") -%}
{%- set align_class = resolve_class({
	'text-right': align == 'Right',
	'text-centre': align == 'Centre',
	'text-left': align == 'Left',
}) -%}

{%- set heading_class = resolve_class({
	'text-white': theme == 'Dark',
	'': theme == 'Light',
}) -%}
<div class="carousel-item {{ 'active' if index=='1' else ''}}" style="height: 450px;">
	<img class="d-block h-100 w-100" style="object-fit: cover;" src="{{ image }}" alt="{{ title }}">
	{%- if title or subtitle -%}
	<div class="carousel-body container d-flex {{ align_class }}">
		<div class="carousel-content align-self-center">
			{%- if title -%}<h1 class="{{ heading_class }}">{{ title }}</h1>{%- endif -%}
			{%- if subtitle -%}<p class="{{ heading_class }} mt-2">{{ subtitle }}</p>{%- endif -%}
			{%- if action -%}
			<a href="{{ action }}" class="btn btn-primary mt-3">
				{{ label }}
			</a>
			{%- endif -%}
		</div>
	</div>
	{%- endif -%}
</div>
{%- endmacro -%}

{%- set hero_slider_id = 'id-' + frappe.utils.generate_hash('HeroSlider', 12) -%}

<div id="{{ hero_slider_id }}" class="section-carousel carousel slide" data-ride="carousel">
	{%- if show_indicators -%}
	<ol class="carousel-indicators">
		{%- for index in ['1', '2', '3', '4', '5'] -%}
		{%- if values['slide_' + index + '_image'] -%}
			<li data-target="#{{ hero_slider_id }}" data-slide-to="{{ frappe.utils.cint(index) - 1 }}" class="{{ 'active' if index=='1' else ''}}"></li>
		{%- endif -%}
		{%- endfor -%}
	</ol>
	{%- endif -%}
	<div class="carousel-inner {{ resolve_class({'rounded-carousel': rounded }) }}">
		{%- for index in ['1', '2', '3', '4', '5'] -%}
			{%- set image = values['slide_' + index + '_image'] -%}
			{%- set title = values['slide_' + index + '_title'] -%}
			{%- set subtitle = values['slide_' + index + '_subtitle'] -%}
			{%- set primary_action = values['slide_' + index + '_primary_action'] -%}
			{%- set primary_action_label = values['slide_' + index + '_primary_action_label'] -%}
			{%- set align = values['slide_' + index + '_content_align'] -%}
			{%- set theme = values['slide_' + index + '_theme'] -%}

			{%- if image -%}
				{{ slide(image, title, subtitle, primary_action, primary_action_label, index, align, theme) }}
			{%- endif -%}

		{%- endfor -%}
	</div>
	{%- if show_controls -%}
	<a class="carousel-control-prev" href="#{{ hero_slider_id }}" role="button" data-slide="prev">
		<div class="carousel-control">
			<svg class="mr-1" width="20" height="20" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path d="M11.625 3.75L6.375 9L11.625 14.25" stroke="#4C5A67" stroke-linecap="round" stroke-linejoin="round"/>
			</svg>
		</div>
		<span class="sr-only">Previous</span>
	</a>
	<a class="carousel-control-next" href="#{{ hero_slider_id }}" role="button" data-slide="next">
		<div class="carousel-control">
			<svg class="ml-1" width="20" height="20" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path d="M6.375 14.25L11.625 9L6.375 3.75" stroke="#4C5A67" stroke-linecap="round" stroke-linejoin="round"/>
			</svg>
		</div>
		<span class="sr-only">Next</span>
	</a>
	{%- endif -%}
</div>

<script>
	frappe.ready(function () {
		$('.carousel').carousel({
			interval: false,
			pause: "hover",
			wrap: true
		})
	});
</script>
