name: CI

on:
  pull_request:
    paths-ignore:
      - "**.css"
      - "**.js"
      - "**.md"
      - "**.html"
      - "**.csv"
  schedule:
    # Run everday at midnight UTC / 5:30 IST
    - cron: "0 0 * * *"
env:
    WEBSHOP_BRANCH: ${{ github.base_ref || github.ref_name }}

concurrency:
  group: develop-${{ github.event.number }}
  cancel-in-progress: true

jobs:
  tests:
    runs-on: ubuntu-latest
    timeout-minutes: 60

    strategy:
      fail-fast: false

    name: Server

    services:
      mysql:
        image: mariadb:10.6
        env:
          MARIADB_ROOT_PASSWORD: 'root'
        ports:
          - 3306:3306
        options: --health-cmd="mariadb-admin ping" --health-interval=5s --health-timeout=2s --health-retries=3

    steps:
      - name: <PERSON><PERSON>
        uses: actions/checkout@v2

      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.10'

      - name: Setup Node
        uses: actions/setup-node@v2
        with:
          node-version: 20
          check-latest: true

      - name: Add to Hosts
        run: echo "127.0.0.1 test_site" | sudo tee -a /etc/hosts

      - name: Cache pip
        uses: actions/cache@v2
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/*requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-
            ${{ runner.os }}-
      - name: Cache node modules
        uses: actions/cache@v2
        env:
          cache-name: cache-node-modules
        with:
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - uses: actions/cache@v2
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install
        run: |
          bash ${GITHUB_WORKSPACE}/.github/helper/install.sh
        env:
          BRANCH_TO_CLONE: ${{ env.WEBSHOP_BRANCH }}


      - name: Run Tests
        run: cd ~/frappe-bench/ && bench --site test_site run-tests --app webshop --coverage
        env:
          TYPE: server
          CI_BUILD_ID: ${{ github.run_id }}
          ORCHESTRATOR_URL: http://test-orchestrator.frappe.io

      - name: Upload coverage data
        uses: actions/upload-artifact@v3
        with:
          name: coverage-${{ matrix.container }}
          path: /home/<USER>/frappe-bench/sites/coverage.xml