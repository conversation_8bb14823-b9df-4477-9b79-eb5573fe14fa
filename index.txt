<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أبديت تكنولوجي - Update Technology</title>

    <!-- PWA Meta Tags (إضافات التطبيق التقدمي) -->
    <meta name="theme-color" content="#4a2c2a"/>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="أبديت تكنولوجي">
    <link rel="apple-touch-icon" href="https://placehold.co/192x192/4a2c2a/ffffff?text=UT">
    
    <!-- Web App Manifest (ملف تعريف التطبيق) -->
    <link rel="manifest" href="data:application/manifest+json;base64,ewogICJuYW1lIjogIuGNquGIAdC/2YXYsSDYp9mE2YPZhNmK2YLYpyAtIFVwZGF0ZSBUZWNobm9sb2d5IiwKICAic2hvcnRfbmFtZSI6ICLQjdiM2YfYsSDYp9mE2YoiLAogICJpY29ucyI6IFsKICAgIHsKICAgICAgInNyYyI6ICJodHRwczovL3BsYWNlaG9sZC5jby8xOTJ4MTkyLzRhMmMyYS9mZmZmZmY/dGV4dD1VVCIsCiAgICAgICJ0eXBlIjogImltYWdlL3BuZyIsCiAgICAgICJzaXplcyI6ICIxOTJ4MTkyIgogICAgfSwKICAgIHsKICAgICAgInNyYyI6ICJodHRwczovL3BsYWNlaG9sZC5jby81MTJ4NTEyLzRhMmMyYS9mZmZmZmY/dGV4dD1VVCIsCiAgICAgICJ0eXBlIjogImltYWdlL3BuZyIsCiAgICAgICJzaXplcyI6ICI1MTJ4NTEyIgogICAgfQogIF0sCiAgInN0YXJ0X3VybCI6ICIuIiwKICAiZGlzcGxheSI6ICJzdGFuZGFsb25lIiwKICAidGhlbWVfY29sb3IiOiAiIzRhMmMyYSIsCiAgImJhY2tncm91bmRfY29sb3IiOiAiI2ZmZmZmZiIKfQ==">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts (Cairo) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700;900&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <style>
        body { font-family: 'Cairo', sans-serif; }
        .bg-brand-dark-brown { background-color: #4a2c2a; }
        .bg-brand-orange { background-color: #f97316; }
        .text-brand-orange { color: #f97316; }
        .border-brand-orange { border-color: #f97316; }
        .ring-brand-orange { --tw-ring-color: #f97316; }
        input[type='number']::-webkit-inner-spin-button,
        input[type='number']::-webkit-outer-spin-button { -webkit-appearance: none; margin: 0; }
        input[type='number'] { -moz-appearance: textfield; }
        .filters-mobile-open { transform: translateX(0%); }
    </style>
</head>
<body class="bg-gray-100">

    <div id="app" class="text-gray-800">

        <!-- ======== Header (الرأس) ======== -->
        <header class="bg-brand-dark-brown text-white shadow-lg sticky top-0 z-50">
            <div class="container mx-auto px-4">
                <div class="flex items-center justify-between h-20">
                    <a href="#" onclick="navigateTo('home')" class="flex items-center space-x-3 rtl:space-x-reverse">
                        <img src="https://placehold.co/150x50/4a2c2a/ffffff?text=Update+Tech&font=cairo" alt="شعار أبديت تكنولوجي" class="h-10">
                    </a>
                    <nav class="hidden lg:flex items-center space-x-8 rtl:space-x-reverse text-lg">
                        <a href="#" onclick="navigateTo('home')" class="hover:text-brand-orange transition">الرئيسية</a>
                        <a href="#" onclick="navigateTo('products')" class="hover:text-brand-orange transition">المنتجات</a>
                        <a href="#" onclick="navigateTo('home', '#brands-section')" class="hover:text-brand-orange transition">الماركات</a>
                        <a href="#" onclick="navigateTo('home', '#offers-section')" class="hover:text-brand-orange transition">العروض</a>
                    </nav>
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <div class="relative">
                            <a href="#" onclick="navigateTo('cart')" class="hover:text-brand-orange transition relative">
                                <i class="fas fa-shopping-cart fa-lg"></i>
                                <span id="cart-count" class="absolute -top-2 -right-2 bg-brand-orange text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">0</span>
                            </a>
                        </div>
                        <a href="#" class="hover:text-brand-orange transition hidden sm:block">
                            <i class="fas fa-user fa-lg"></i>
                        </a>
                        <button id="mobile-menu-button" class="lg:hidden text-2xl">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div id="mobile-menu" class="hidden lg:hidden bg-brand-dark-brown/95 absolute w-full">
                <a href="#" onclick="navigateTo('home')" class="block text-center py-3 px-4 text-lg hover:bg-brand-orange transition">الرئيسية</a>
                <a href="#" onclick="navigateTo('products')" class="block text-center py-3 px-4 text-lg hover:bg-brand-orange transition">المنتجات</a>
                <a href="#" onclick="navigateTo('home', '#brands-section')" class="block text-center py-3 px-4 text-lg hover:bg-brand-orange transition">الماركات</a>
                <a href="#" onclick="navigateTo('home', '#offers-section')" class="block text-center py-3 px-4 text-lg hover:bg-brand-orange transition">العروض</a>
            </div>
        </header>

        <!-- ======== Main Content (المحتوى الرئيسي) ======== -->
        <main>
            <!-- Home Page (الصفحة الرئيسية) -->
            <section id="home-page">
                <div class="bg-white">
                    <div class="container mx-auto px-4 py-8 md:py-16 flex flex-col md:flex-row items-center">
                        <div class="md:w-1/2 text-center md:text-right mb-8 md:mb-0">
                            <h1 class="text-4xl md:text-6xl font-extrabold text-brand-dark-brown mb-4">أحدث التقنيات بين يديك</h1>
                            <p class="text-lg text-gray-600 mb-8">اكتشف مجموعتنا الحصرية من الجوالات والإكسسوارات بأفضل الأسعار.</p>
                            <a href="#" onclick="navigateTo('products')" class="bg-brand-orange text-white px-8 py-3 rounded-lg font-bold text-lg hover:bg-opacity-90 transition shadow-lg">تسوق الآن</a>
                        </div>
                        <div class="md:w-1/2">
                            <img src="https://placehold.co/600x400/f0f0f0/333333?text=New+Phones" alt="[صورة هواتف جديدة]" class="rounded-lg shadow-2xl">
                        </div>
                    </div>
                </div>
                <div id="offers-section" class="container mx-auto px-4 py-16">
                    <h2 class="text-3xl font-bold text-center mb-2 text-brand-dark-brown">الأكثر مبيعاً</h2>
                    <div class="w-24 h-1 bg-brand-orange mx-auto mb-8"></div>
                    <div id="best-sellers-container" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8"></div>
                </div>
                <div id="brands-section" class="bg-white py-16">
                    <div class="container mx-auto px-4">
                        <h2 class="text-3xl font-bold text-center mb-2 text-brand-dark-brown">تسوق حسب الماركة</h2>
                        <div class="w-24 h-1 bg-brand-orange mx-auto mb-8"></div>
                        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center">
                            <div class="flex justify-center"><img src="https://placehold.co/150x80/f0f0f0/333333?text=Apple" alt="Apple" class="h-12 grayscale hover:grayscale-0 transition"></div>
                            <div class="flex justify-center"><img src="https://placehold.co/150x80/f0f0f0/333333?text=Samsung" alt="Samsung" class="h-12 grayscale hover:grayscale-0 transition"></div>
                            <div class="flex justify-center"><img src="https://placehold.co/150x80/f0f0f0/333333?text=Xiaomi" alt="Xiaomi" class="h-12 grayscale hover:grayscale-0 transition"></div>
                            <div class="flex justify-center"><img src="https://placehold.co/150x80/f0f0f0/333333?text=Google" alt="Google" class="h-12 grayscale hover:grayscale-0 transition"></div>
                            <div class="flex justify-center"><img src="https://placehold.co/150x80/f0f0f0/333333?text=Anker" alt="Anker" class="h-12 grayscale hover:grayscale-0 transition"></div>
                            <div class="flex justify-center"><img src="https://placehold.co/150x80/f0f0f0/333333?text=JBL" alt="JBL" class="h-12 grayscale hover:grayscale-0 transition"></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Products Page (صفحة المنتجات) -->
            <section id="products-page" class="hidden">
                <div class="container mx-auto px-4 py-8">
                    <div class="flex flex-col lg:flex-row gap-8">
                        <aside id="filters-sidebar" class="w-full lg:w-1/4 bg-white p-6 rounded-lg shadow-lg lg:sticky top-28 h-fit transition-transform duration-300 ease-in-out transform -translate-x-full lg:transform-none fixed inset-y-0 right-0 z-40 overflow-y-auto">
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-2xl font-bold text-brand-dark-brown">الفلاتر</h3>
                                <button id="close-filters-button" class="lg:hidden text-2xl"><i class="fas fa-times"></i></button>
                            </div>
                            <div class="mb-6">
                                <h4 class="font-bold mb-3">الماركة</h4>
                                <div id="brand-filters" class="space-y-2"></div>
                            </div>
                            <div class="mb-6">
                                <h4 class="font-bold mb-3">السعر (ر.س)</h4>
                                <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                    <input type="number" id="min-price" placeholder="الأدنى" class="w-full border-gray-300 rounded-md shadow-sm focus:border-brand-orange focus:ring-brand-orange">
                                    <span>-</span>
                                    <input type="number" id="max-price" placeholder="الأعلى" class="w-full border-gray-300 rounded-md shadow-sm focus:border-brand-orange focus:ring-brand-orange">
                                </div>
                            </div>
                            <div class="mb-6">
                                <h4 class="font-bold mb-3">الرام</h4>
                                <div id="ram-filters" class="space-y-2"></div>
                            </div>
                            <button id="apply-filters" class="w-full bg-brand-orange text-white py-2 rounded-lg font-bold hover:bg-opacity-90 transition">تطبيق الفلاتر</button>
                        </aside>
                        <div class="w-full lg:w-3/4">
                            <div class="flex justify-between items-center mb-6">
                                <h2 class="text-3xl font-bold text-brand-dark-brown">كل المنتجات</h2>
                                <button id="open-filters-button" class="lg:hidden bg-brand-dark-brown text-white px-4 py-2 rounded-lg"><i class="fas fa-filter mr-2"></i> الفلاتر</button>
                            </div>
                            <div id="products-container" class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-8"></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Product Detail Page (صفحة تفاصيل المنتج) -->
            <section id="product-detail-page" class="hidden bg-white py-12">
                <div id="product-detail-container" class="container mx-auto px-4"></div>
            </section>

            <!-- Cart Page (صفحة سلة التسوق) -->
            <section id="cart-page" class="hidden">
                 <div class="container mx-auto px-4 py-12">
                    <h2 class="text-3xl font-bold text-center mb-8 text-brand-dark-brown">سلة التسوق</h2>
                    <div id="cart-container" class="bg-white p-6 rounded-lg shadow-lg"></div>
                </div>
            </section>
            
            <!-- Comparison Page (صفحة المقارنة) -->
            <section id="comparison-page" class="hidden">
                <div class="container mx-auto px-4 py-12">
                    <h2 class="text-3xl font-bold text-center mb-8 text-brand-dark-brown">مقارنة المنتجات</h2>
                    <div id="comparison-container" class="bg-white p-6 rounded-lg shadow-lg overflow-x-auto"></div>
                </div>
            </section>
        </main>

        <!-- ======== Footer (التذييل) ======== -->
        <footer class="bg-brand-dark-brown text-white pt-12 pb-6">
            <div class="container mx-auto px-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
                    <div>
                        <h3 class="text-xl font-bold mb-4 border-b-2 border-brand-orange pb-2">أبديت تكنولوجي</h3>
                        <p class="text-gray-300">متجركم الأول للحصول على أحدث الجوالات والإكسسوارات الأصلية بأفضل الأسعار.</p>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-4 border-b-2 border-brand-orange pb-2">روابط سريعة</h3>
                        <ul class="space-y-2">
                            <li><a href="#" onclick="navigateTo('home')" class="hover:text-brand-orange transition">الرئيسية</a></li>
                            <li><a href="#" onclick="navigateTo('products')" class="hover:text-brand-orange transition">المنتجات</a></li>
                            <li><a href="#" class="hover:text-brand-orange transition">سياسة الخصوصية</a></li>
                            <li><a href="#" class="hover:text-brand-orange transition">شروط الاستخدام</a></li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-4 border-b-2 border-brand-orange pb-2">تواصل معنا</h3>
                        <ul class="space-y-2 text-gray-300">
                            <li><i class="fas fa-map-marker-alt text-brand-orange ml-2"></i> الرياض، المملكة العربية السعودية</li>
                            <li><i class="fas fa-phone text-brand-orange ml-2"></i> +966 12 345 6789</li>
                            <li><i class="fas fa-envelope text-brand-orange ml-2"></i> <EMAIL></li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-4 border-b-2 border-brand-orange pb-2">تابعنا</h3>
                        <div class="flex space-x-4 rtl:space-x-reverse text-2xl">
                            <a href="#" class="hover:text-brand-orange transition"><i class="fab fa-facebook"></i></a>
                            <a href="#" class="hover:text-brand-orange transition"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="hover:text-brand-orange transition"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="hover:text-brand-orange transition"><i class="fab fa-snapchat"></i></a>
                        </div>
                    </div>
                </div>
                <div class="border-t border-gray-700 pt-6 text-center text-gray-400">
                    <p>&copy; 2024 أبديت تكنولوجي. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </footer>
        
        <div id="comparison-widget" class="hidden fixed bottom-5 left-5 z-40">
            <button onclick="navigateTo('comparison')" class="bg-brand-orange text-white rounded-lg shadow-lg px-6 py-3 font-bold flex items-center space-x-2 rtl:space-x-reverse">
                <i class="fas fa-exchange-alt"></i>
                <span>قارن (<span id="comparison-count">0</span>)</span>
            </button>
        </div>

    </div>

    <script>
    // ===================================================================================
    // ||                     Service Worker Registration (تسجيل عامل الخدمة)              ||
    // ===================================================================================
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('./sw.js')
                .then(registration => {
                    console.log('ServiceWorker registration successful with scope: ', registration.scope);
                })
                .catch(error => {
                    console.log('ServiceWorker registration failed: ', error);
                });
        });
    }

    // ===================================================================================
    // ||                                  قاعدة البيانات                                 ||
    // ===================================================================================
    const products = [
        { id: 1, name: 'آيفون 15 برو', brand: 'Apple', price: 4500, image: 'https://placehold.co/400x400/e0e0e0/555555?text=iPhone+15+Pro', category: 'phone', bestSeller: true, specs: { ram: '8GB', storage: '256GB', camera: '48MP', battery: '3274mAh' } },
        { id: 2, name: 'سامسونج جالاكسي S24 ألترا', brand: 'Samsung', price: 5200, image: 'https://placehold.co/400x400/e0e0e0/555555?text=Galaxy+S24', category: 'phone', bestSeller: true, specs: { ram: '12GB', storage: '512GB', camera: '200MP', battery: '5000mAh' } },
        { id: 3, name: 'جوجل بيكسل 8', brand: 'Google', price: 3800, image: 'https://placehold.co/400x400/e0e0e0/555555?text=Pixel+8', category: 'phone', specs: { ram: '8GB', storage: '128GB', camera: '50MP', battery: '4575mAh' } },
        { id: 4, name: 'شاومي 14', brand: 'Xiaomi', price: 3200, image: 'https://placehold.co/400x400/e0e0e0/555555?text=Xiaomi+14', category: 'phone', bestSeller: true, specs: { ram: '12GB', storage: '256GB', camera: '50MP', battery: '4610mAh' } },
        { id: 5, name: 'سماعات Anker Soundcore', brand: 'Anker', price: 350, image: 'https://placehold.co/400x400/e0e0e0/555555?text=Anker+Earbuds', category: 'accessory', bestSeller: true, specs: { ram: 'N/A', storage: 'N/A', camera: 'N/A', battery: '40-hour playtime' } },
        { id: 6, name: 'شاحن سريع 65 واط', brand: 'Anker', price: 180, image: 'https://placehold.co/400x400/e0e0e0/555555?text=Anker+Charger', category: 'accessory', specs: { ram: 'N/A', storage: 'N/A', camera: 'N/A', battery: '65W GaN' } },
        { id: 7, name: 'سماعة JBL Flip 6', brand: 'JBL', price: 550, image: 'https://placehold.co/400x400/e0e0e0/555555?text=JBL+Speaker', category: 'accessory', specs: { ram: 'N/A', storage: 'N/A', camera: 'N/A', battery: '12-hour playtime' } },
        { id: 8, name: 'كفر حماية شفاف للآيفون', brand: 'Apple', price: 150, image: 'https://placehold.co/400x400/e0e0e0/555555?text=iPhone+Case', category: 'accessory', specs: { ram: 'N/A', storage: 'N/A', camera: 'N/A', battery: 'N/A' } },
        { id: 9, name: 'سامسونج جالاكسي A55', brand: 'Samsung', price: 1600, image: 'https://placehold.co/400x400/e0e0e0/555555?text=Galaxy+A55', category: 'phone', specs: { ram: '8GB', storage: '128GB', camera: '50MP', battery: '5000mAh' } },
    ];

    let cart = [];
    let comparisonList = [];

    // ===================================================================================
    // ||                                  وظائف العرض                                   ||
    // ===================================================================================

    function createProductCard(product) {
        const isPhone = product.category === 'phone';
        const inComparison = comparisonList.includes(product.id);
        return `<div class="bg-white rounded-lg shadow-md overflow-hidden transform hover:-translate-y-2 transition duration-300 group"><div class="relative"><img src="${product.image}" alt="${product.name}" class="w-full h-56 object-cover cursor-pointer" onclick="navigateTo('product-detail', ${product.id})"><div class="absolute top-3 right-3 bg-brand-orange text-white px-3 py-1 rounded-full text-sm font-bold">${product.brand}</div></div><div class="p-4"><h3 class="text-lg font-bold truncate cursor-pointer" onclick="navigateTo('product-detail', ${product.id})">${product.name}</h3><p class="text-2xl font-black text-brand-dark-brown my-2">${product.price} <span class="text-sm">ر.س</span></p><div class="flex justify-between items-center mt-4"><button onclick="addToCart(${product.id})" class="bg-brand-dark-brown text-white px-4 py-2 rounded-lg hover:bg-opacity-90 transition text-sm font-bold"><i class="fas fa-cart-plus ml-2"></i>أضف للسلة</button>${isPhone ? `<button onclick="toggleCompare(${product.id})" class="text-2xl ${inComparison ? 'text-brand-orange' : 'text-gray-300'} hover:text-brand-orange transition"><i class="fas fa-exchange-alt"></i></button>` : ''}</div></div></div>`;
    }

    function renderProducts(filteredProducts = products) {
        const container = document.getElementById('products-container');
        if (!container) return;
        container.innerHTML = filteredProducts.map(createProductCard).join('');
    }
    
    function renderBestSellers() {
        const container = document.getElementById('best-sellers-container');
        if (!container) return;
        const bestSellers = products.filter(p => p.bestSeller).slice(0, 4);
        container.innerHTML = bestSellers.map(createProductCard).join('');
    }

    function renderProductDetail(productId) {
        const product = products.find(p => p.id === productId);
        const container = document.getElementById('product-detail-container');
        if (!product || !container) return;
        container.innerHTML = `<div class="flex flex-col lg:flex-row gap-12"><div class="lg:w-1/2"><img src="${product.image}" alt="${product.name}" class="w-full rounded-lg shadow-lg"></div><div class="lg:w-1/2"><span class="bg-brand-orange text-white px-3 py-1 rounded-full text-sm font-bold">${product.brand}</span><h1 class="text-4xl font-extrabold my-4 text-brand-dark-brown">${product.name}</h1><p class="text-4xl font-black text-brand-dark-brown mb-6">${product.price} <span class="text-lg">ر.س</span></p><div class="mb-8"><h3 class="text-xl font-bold mb-4 border-b-2 border-gray-200 pb-2">أهم المواصفات</h3><ul class="space-y-2 text-gray-700">${Object.entries(product.specs).map(([key, value]) => `${value !== 'N/A' ? `<li class="flex justify-between"><strong class="capitalize">${key === 'ram' ? 'الرام' : key === 'storage' ? 'التخزين' : key === 'camera' ? 'الكاميرا' : 'البطارية'}:</strong><span>${value}</span></li>` : ''}`).join('')}</ul></div><button onclick="addToCart(${product.id})" class="w-full bg-brand-orange text-white py-4 rounded-lg font-bold text-lg hover:bg-opacity-90 transition shadow-lg"><i class="fas fa-cart-plus ml-2"></i> أضف إلى السلة</button></div></div>`;
    }

    function renderCart() {
        const container = document.getElementById('cart-container');
        if (!container) return;
        if (cart.length === 0) {
            container.innerHTML = `<div class="text-center py-12"><i class="fas fa-shopping-cart fa-4x text-gray-300 mb-4"></i><h3 class="text-2xl font-bold text-gray-700">سلتك فارغة!</h3><p class="text-gray-500 mt-2">أضف بعض المنتجات لتبدأ التسوق.</p><button onclick="navigateTo('products')" class="mt-6 bg-brand-orange text-white px-6 py-2 rounded-lg font-bold">تصفح المنتجات</button></div>`;
            return;
        }
        const cartItemsHTML = cart.map(item => { const product = products.find(p => p.id === item.id); return `<div class="flex items-center justify-between py-4 border-b"><div class="flex items-center gap-4"><img src="${product.image}" alt="${product.name}" class="w-20 h-20 object-cover rounded-md"><div><h4 class="font-bold">${product.name}</h4><p class="text-gray-600">${product.price} ر.س</p></div></div><div class="flex items-center gap-4"><div class="flex items-center border rounded-md"><button onclick="updateQuantity(${item.id}, -1)" class="px-3 py-1 text-lg">-</button><input type="number" value="${item.quantity}" class="w-12 text-center border-none focus:ring-0" readonly><button onclick="updateQuantity(${item.id}, 1)" class="px-3 py-1 text-lg">+</button></div><p class="font-bold w-24 text-center">${item.quantity * product.price} ر.س</p><button onclick="removeFromCart(${item.id})" class="text-red-500 hover:text-red-700 text-xl"><i class="fas fa-trash"></i></button></div></div>`; }).join('');
        const subtotal = cart.reduce((sum, item) => { const product = products.find(p => p.id === item.id); return sum + (item.quantity * product.price); }, 0);
        container.innerHTML = `<div>${cartItemsHTML}</div><div class="mt-6 text-left"><p class="text-2xl font-bold">الإجمالي: <span class="text-brand-orange">${subtotal} ر.س</span></p><p class="text-gray-500">الشحن والضرائب سيتم حسابها عند الدفع.</p><div class="mt-6"><h3 class="font-bold mb-2">طرق الدفع المتاحة:</h3><div class="flex items-center gap-4"><span class="flex items-center gap-2 border p-2 rounded-md"><i class="fas fa-money-bill-wave text-green-500"></i> الدفع عند الاستلام</span><span class="flex items-center gap-2 border p-2 rounded-md"><i class="fab fa-cc-visa text-blue-700"></i><i class="fab fa-cc-mastercard text-red-600"></i> البطاقات الائتمانية</span></div></div><button class="mt-6 w-full md:w-auto float-left bg-brand-orange text-white px-8 py-3 rounded-lg font-bold hover:bg-opacity-90 transition">إتمام عملية الدفع</button></div>`;
    }
    
    function renderComparison() {
        const container = document.getElementById('comparison-container');
        if (!container) return;
        if (comparisonList.length < 2) {
            container.innerHTML = `<div class="text-center py-12"><i class="fas fa-exchange-alt fa-4x text-gray-300 mb-4"></i><h3 class="text-2xl font-bold text-gray-700">اختر منتجين على الأقل للمقارنة</h3><p class="text-gray-500 mt-2">انتقل إلى صفحة المنتجات وحدد المنتجات التي تريد مقارنتها.</p><button onclick="navigateTo('products')" class="mt-6 bg-brand-orange text-white px-6 py-2 rounded-lg font-bold">العودة للمنتجات</button></div>`;
            return;
        }
        const comparedProducts = products.filter(p => comparisonList.includes(p.id));
        const specsKeys = ['ram', 'storage', 'camera', 'battery'];
        const specsLabels = {'ram': 'الرام', 'storage': 'التخزين', 'camera': 'الكاميرا', 'battery': 'البطارية'};
        const tableHead = `<th class="p-4 border-b-2 border-gray-200 bg-gray-50 text-right font-bold">المواصفات</th>${comparedProducts.map(p => `<th class="p-4 border-b-2 border-gray-200"><img src="${p.image}" class="w-24 h-24 mx-auto object-cover rounded-md"><p class="font-bold mt-2">${p.name}</p></th>`).join('')}`;
        const tableBody = `<tr class="bg-gray-50"><td class="p-4 border-b font-bold">السعر</td>${comparedProducts.map(p => `<td class="p-4 border-b font-bold text-brand-orange text-center">${p.price} ر.س</td>`).join('')}</tr>${specsKeys.map(key => `<tr><td class="p-4 border-b font-bold">${specsLabels[key]}</td>${comparedProducts.map(p => `<td class="p-4 border-b text-center">${p.specs[key] || 'N/A'}</td>`).join('')}</tr>`).join('')}<tr><td class="p-4 font-bold"></td>${comparedProducts.map(p => `<td class="p-4 text-center"><button onclick="addToCart(${p.id}); navigateTo('cart');" class="bg-brand-orange text-white px-4 py-2 rounded-lg text-sm font-bold">أضف للسلة</button></td>`).join('')}</tr>`;
        container.innerHTML = `<table class="w-full border-collapse text-center"><thead><tr>${tableHead}</tr></thead><tbody>${tableBody}</tbody></table>`;
    }
    
    function renderFilters() {
        const brands = [...new Set(products.map(p => p.brand))];
        const rams = [...new Set(products.filter(p => p.specs.ram && p.specs.ram !== 'N/A').map(p => p.specs.ram))];
        const brandFiltersContainer = document.getElementById('brand-filters');
        brandFiltersContainer.innerHTML = brands.map(brand => `<label class="flex items-center"><input type="checkbox" name="brand" value="${brand}" class="rounded border-gray-300 text-brand-orange shadow-sm focus:border-brand-orange focus:ring focus:ring-offset-0 focus:ring-brand-orange focus:ring-opacity-50"><span class="mr-2">${brand}</span></label>`).join('');
        const ramFiltersContainer = document.getElementById('ram-filters');
        ramFiltersContainer.innerHTML = rams.map(ram => `<label class="flex items-center"><input type="checkbox" name="ram" value="${ram}" class="rounded border-gray-300 text-brand-orange shadow-sm focus:border-brand-orange focus:ring focus:ring-offset-0 focus:ring-brand-orange focus:ring-opacity-50"><span class="mr-2">${ram}</span></label>`).join('');
    }

    // ===================================================================================
    // ||                               وظائف التفاعل والمنطق                             ||
    // ===================================================================================

    function navigateTo(pageId, extra) {
        document.querySelectorAll('main > section').forEach(section => section.classList.add('hidden'));
        document.getElementById(`${pageId}-page`).classList.remove('hidden');
        window.scrollTo(0, 0);
        document.getElementById('mobile-menu').classList.add('hidden');
        switch(pageId) {
            case 'home': renderBestSellers(); if (extra) { document.querySelector(extra).scrollIntoView({ behavior: 'smooth' }); } break;
            case 'products': renderProducts(); renderFilters(); break;
            case 'product-detail': renderProductDetail(extra); break;
            case 'cart': renderCart(); break;
            case 'comparison': renderComparison(); break;
        }
    }

    function addToCart(productId) {
        const existingItem = cart.find(item => item.id === productId);
        if (existingItem) { existingItem.quantity++; } else { cart.push({ id: productId, quantity: 1 }); }
        updateCartCount();
    }
    
    function removeFromCart(productId) {
        cart = cart.filter(item => item.id !== productId);
        updateCartCount();
        renderCart();
    }

    function updateQuantity(productId, change) {
        const item = cart.find(item => item.id === productId);
        if (item) { item.quantity += change; if (item.quantity <= 0) { removeFromCart(productId); } else { renderCart(); } }
    }

    function updateCartCount() {
        document.getElementById('cart-count').textContent = cart.reduce((sum, item) => sum + item.quantity, 0);
    }
    
    function toggleCompare(productId) {
        const index = comparisonList.indexOf(productId);
        if (index > -1) { comparisonList.splice(index, 1); } else { if (comparisonList.length < 4) { comparisonList.push(productId); } else { alert('يمكنك مقارنة 4 منتجات كحد أقصى.'); } }
        updateComparisonWidget();
        renderProducts();
    }
    
    function updateComparisonWidget() {
        const widget = document.getElementById('comparison-widget');
        const countSpan = document.getElementById('comparison-count');
        const count = comparisonList.length;
        countSpan.textContent = count;
        if (count > 0) { widget.classList.remove('hidden'); } else { widget.classList.add('hidden'); }
    }

    function applyFilters() {
        const selectedBrands = Array.from(document.querySelectorAll('input[name="brand"]:checked')).map(el => el.value);
        const selectedRams = Array.from(document.querySelectorAll('input[name="ram"]:checked')).map(el => el.value);
        const minPrice = parseFloat(document.getElementById('min-price').value) || 0;
        const maxPrice = parseFloat(document.getElementById('max-price').value) || Infinity;
        const filtered = products.filter(p => { const brandMatch = selectedBrands.length === 0 || selectedBrands.includes(p.brand); const ramMatch = selectedRams.length === 0 || (p.specs.ram && selectedRams.includes(p.specs.ram)); const priceMatch = p.price >= minPrice && p.price <= maxPrice; return brandMatch && ramMatch && priceMatch; });
        renderProducts(filtered);
        document.getElementById('filters-sidebar').classList.remove('filters-mobile-open');
    }

    document.addEventListener('DOMContentLoaded', () => {
        navigateTo('home');
        updateCartCount();
        document.getElementById('mobile-menu-button').addEventListener('click', () => { document.getElementById('mobile-menu').classList.toggle('hidden'); });
        document.getElementById('apply-filters').addEventListener('click', applyFilters);
        document.getElementById('open-filters-button').addEventListener('click', () => { document.getElementById('filters-sidebar').classList.add('filters-mobile-open'); });
        document.getElementById('close-filters-button').addEventListener('click', () => { document.getElementById('filters-sidebar').classList.remove('filters-mobile-open'); });
    });
    </script>
    
    <!-- Service Worker File Content (محتوى ملف عامل الخدمة) -->
    <!-- This script is not executed directly. It's used to create the sw.js file. -->
    <script id="sw-script" type="text/javascript">
        const CACHE_NAME = 'update-tech-cache-v1';
        const urlsToCache = [
            '/',
            '/index.html',
            'https://cdn.tailwindcss.com',
            'https://fonts.googleapis.com/css2?family=Cairo:wght@400;700;900&display=swap',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css',
            // Add main product images to cache for offline experience
            'https://placehold.co/400x400/e0e0e0/555555?text=iPhone+15+Pro',
            'https://placehold.co/400x400/e0e0e0/555555?text=Galaxy+S24',
            'https://placehold.co/400x400/e0e0e0/555555?text=Pixel+8',
            'https://placehold.co/400x400/e0e0e0/555555?text=Xiaomi+14',
            'https://placehold.co/400x400/e0e0e0/555555?text=Anker+Earbuds'
        ];

        self.addEventListener('install', event => {
            event.waitUntil(
                caches.open(CACHE_NAME)
                    .then(cache => {
                        console.log('Opened cache');
                        return cache.addAll(urlsToCache);
                    })
            );
        });

        self.addEventListener('fetch', event => {
            event.respondWith(
                caches.match(event.request)
                    .then(response => {
                        if (response) {
                            return response; // Serve from cache
                        }
                        return fetch(event.request); // Fetch from network
                    })
            );
        });
    </script>
    <script>
        // This part registers the service worker from the script tag above
        if ('serviceWorker' in navigator) {
            const swScriptContent = document.getElementById('sw-script').textContent;
            const swBlob = new Blob([swScriptContent], {type: 'application/javascript'});
            const swUrl = URL.createObjectURL(swBlob);

            navigator.serviceWorker.register(swUrl)
                .then(registration => console.log('Service Worker registered with scope:', registration.scope))
                .catch(error => console.log('Service Worker registration failed:', error));
        }
    </script>
</body>
</html>
