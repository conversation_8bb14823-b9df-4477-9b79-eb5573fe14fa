Metadata-Version: 2.1
Name: croniter
Version: 2.0.7
Summary: croniter provides iteration for datetime object with cron like format
Home-page: http://github.com/kiorky/croniter
Author: <PERSON><PERSON><PERSON>, kiorky
Author-email: taichi<PERSON>@gmail.com, kior<PERSON>@cryptelium.net
License: MIT License
Keywords: datetime,iterator,cron
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: POSIX
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.6
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=2.6, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*
License-File: LICENSE
Requires-Dist: python-dateutil
Requires-Dist: pytz (>2021.1)

Introduction
============

.. contents::


croniter provides iteration for the datetime object with a cron like format.

::

                          _ _
      ___ _ __ ___  _ __ (_) |_ ___ _ __
     / __| '__/ _ \| '_ \| | __/ _ \ '__|
    | (__| | | (_) | | | | | ||  __/ |
     \___|_|  \___/|_| |_|_|\__\___|_|


Website: https://github.com/kiorky/croniter

Build Badge
===========
.. image:: https://github.com/kiorky/croniter/actions/workflows/cicd.yml/badge.svg
    :target: https://github.com/kiorky/croniter/actions/workflows/cicd.yml


Usage
============

A simple example::

    >>> from croniter import croniter
    >>> from datetime import datetime
    >>> base = datetime(2010, 1, 25, 4, 46)
    >>> iter = croniter('*/5 * * * *', base)  # every 5 minutes
    >>> print(iter.get_next(datetime))   # 2010-01-25 04:50:00
    >>> print(iter.get_next(datetime))   # 2010-01-25 04:55:00
    >>> print(iter.get_next(datetime))   # 2010-01-25 05:00:00
    >>>
    >>> iter = croniter('2 4 * * mon,fri', base)  # 04:02 on every Monday and Friday
    >>> print(iter.get_next(datetime))   # 2010-01-26 04:02:00
    >>> print(iter.get_next(datetime))   # 2010-01-30 04:02:00
    >>> print(iter.get_next(datetime))   # 2010-02-02 04:02:00
    >>>
    >>> iter = croniter('2 4 1 * wed', base)  # 04:02 on every Wednesday OR on 1st day of month
    >>> print(iter.get_next(datetime))   # 2010-01-27 04:02:00
    >>> print(iter.get_next(datetime))   # 2010-02-01 04:02:00
    >>> print(iter.get_next(datetime))   # 2010-02-03 04:02:00
    >>>
    >>> iter = croniter('2 4 1 * wed', base, day_or=False)  # 04:02 on every 1st day of the month if it is a Wednesday
    >>> print(iter.get_next(datetime))   # 2010-09-01 04:02:00
    >>> print(iter.get_next(datetime))   # 2010-12-01 04:02:00
    >>> print(iter.get_next(datetime))   # 2011-06-01 04:02:00
    >>>
    >>> iter = croniter('0 0 * * sat#1,sun#2', base)  # 1st Saturday, and 2nd Sunday of the month
    >>> print(iter.get_next(datetime))   # 2010-02-06 00:00:00
    >>>
    >>> iter = croniter('0 0 * * 5#3,L5', base)  # 3rd and last Friday of the month
    >>> print(iter.get_next(datetime))   # 2010-01-29 00:00:00
    >>> print(iter.get_next(datetime))   # 2010-02-19 00:00:00


All you need to know is how to use the constructor and the ``get_next``
method, the signature of these methods are listed below::

    >>> def __init__(self, cron_format, start_time=time.time(), day_or=True)

croniter iterates along with ``cron_format`` from ``start_time``.
``cron_format`` is **min hour day month day_of_week**, you can refer to
http://en.wikipedia.org/wiki/Cron for more details. The ``day_or``
switch is used to control how croniter handles **day** and **day_of_week**
entries. Default option is the cron behaviour, which connects those
values using **OR**. If the switch is set to False, the values are connected
using **AND**. This behaves like fcron and enables you to e.g. define a job that
executes each 2nd Friday of a month by setting the days of month and the
weekday.
::

    >>> def get_next(self, ret_type=float)

get_next calculates the next value according to the cron expression and
returns an object of type ``ret_type``. ``ret_type`` should be a ``float`` or a
``datetime`` object.

Supported added for ``get_prev`` method. (>= 0.2.0)::

    >>> base = datetime(2010, 8, 25)
    >>> itr = croniter('0 0 1 * *', base)
    >>> print(itr.get_prev(datetime))  # 2010-08-01 00:00:00
    >>> print(itr.get_prev(datetime))  # 2010-07-01 00:00:00
    >>> print(itr.get_prev(datetime))  # 2010-06-01 00:00:00

You can validate your crons using ``is_valid`` class method. (>= 0.3.18)::

    >>> croniter.is_valid('0 0 1 * *')  # True
    >>> croniter.is_valid('0 wrong_value 1 * *')  # False

About DST
=========
Be sure to init your croniter instance with a TZ aware datetime for this to work!

Example using pytz::

    >>> import pytz
    >>> tz = pytz.timezone("Europe/Paris")
    >>> local_date = tz.localize(datetime(2017, 3, 26))
    >>> val = croniter('0 0 * * *', local_date).get_next(datetime)

Example using python_dateutil::

    >>> import dateutil.tz
    >>> tz = dateutil.tz.gettz('Asia/Tokyo')
    >>> local_date = datetime(2017, 3, 26, tzinfo=tz)
    >>> val = croniter('0 0 * * *', local_date).get_next(datetime)

Example using python built in module::

    >>> from datetime import datetime, timezone
    >>> local_date = datetime(2017, 3, 26, tzinfo=timezone.utc)
    >>> val = croniter('0 0 * * *', local_date).get_next(datetime)

About second repeats
=====================
Croniter is able to do second repetition crontabs form::

    >>> croniter('* * * * * 1', local_date).get_next(datetime)
    >>> base = datetime(2012, 4, 6, 13, 26, 10)
    >>> itr = croniter('* * * * * 15,25', base)
    >>> itr.get_next(datetime) # 4/6 13:26:15
    >>> itr.get_next(datetime) # 4/6 13:26:25
    >>> itr.get_next(datetime) # 4/6 13:27:15

You can also note that this expression will repeat every second from the start datetime.::

    >>> croniter('* * * * * *', local_date).get_next(datetime)

You can also use seconds as first slot::

    >>> croniter('* * * * * *', local_date, second_at_beginning=True).get_next(datetime)


Support for start_time shifts
==============================
See https://github.com/kiorky/croniter/pull/76,
You can set start_time=, then expand_from_start_time=True for your generations to be computed from start_time instead of calendar days::

    >>> from pprint import pprint
    >>> iter = croniter('0 0 */7 * *', start_time=datetime(2024, 7, 11), expand_from_start_time=True);pprint([iter.get_next(datetime) for a in range(10)])
    [datetime.datetime(2024, 7, 18, 0, 0),
     datetime.datetime(2024, 7, 25, 0, 0),
     datetime.datetime(2024, 8, 4, 0, 0),
     datetime.datetime(2024, 8, 11, 0, 0),
     datetime.datetime(2024, 8, 18, 0, 0),
     datetime.datetime(2024, 8, 25, 0, 0),
     datetime.datetime(2024, 9, 4, 0, 0),
     datetime.datetime(2024, 9, 11, 0, 0),
     datetime.datetime(2024, 9, 18, 0, 0),
     datetime.datetime(2024, 9, 25, 0, 0)]
    >>> # INSTEAD OF THE DEFAULT BEHAVIOR:
    >>> iter = croniter('0 0 */7 * *', start_time=datetime(2024, 7, 11), expand_from_start_time=False);pprint([iter.get_next(datetime) for a in range(10)])
    [datetime.datetime(2024, 7, 15, 0, 0),
     datetime.datetime(2024, 7, 22, 0, 0),
     datetime.datetime(2024, 7, 29, 0, 0),
     datetime.datetime(2024, 8, 1, 0, 0),
     datetime.datetime(2024, 8, 8, 0, 0),
     datetime.datetime(2024, 8, 15, 0, 0),
     datetime.datetime(2024, 8, 22, 0, 0),
     datetime.datetime(2024, 8, 29, 0, 0),
     datetime.datetime(2024, 9, 1, 0, 0),
     datetime.datetime(2024, 9, 8, 0, 0)]


Testing if a date matches a crontab
===================================
Test for a match with (>=0.3.32)::

    >>> croniter.match("0 0 * * *", datetime(2019, 1, 14, 0, 0, 0, 0))
    True
    >>> croniter.match("0 0 * * *", datetime(2019, 1, 14, 0, 2, 0, 0))
    False
    >>>
    >>> croniter.match("2 4 1 * wed", datetime(2019, 1, 1, 4, 2, 0, 0)) # 04:02 on every Wednesday OR on 1st day of month
    True
    >>> croniter.match("2 4 1 * wed", datetime(2019, 1, 1, 4, 2, 0, 0), day_or=False) # 04:02 on every 1st day of the month if it is a Wednesday
    False

Testing if a crontab matches in datetime range
==============================================
Test for a match_range with (>=2.0.3)::

    >>> croniter.match_range("0 0 * * *", datetime(2019, 1, 13, 0, 59, 0, 0), datetime(2019, 1, 14, 0, 1, 0, 0))
    True
    >>> croniter.match_range("0 0 * * *", datetime(2019, 1, 13, 0, 1, 0, 0), datetime(2019, 1, 13, 0, 59, 0, 0))
    False
    >>> croniter.match_range("2 4 1 * wed", datetime(2019, 1, 1, 3, 2, 0, 0), datetime(2019, 1, 1, 5, 1, 0, 0))
    # 04:02 on every Wednesday OR on 1st day of month
    True
    >>> croniter.match_range("2 4 1 * wed", datetime(2019, 1, 1, 3, 2, 0, 0), datetime(2019, 1, 1, 5, 2, 0, 0), day_or=False)
    # 04:02 on every 1st day of the month if it is a Wednesday
    False

Gaps between date matches
=========================
For performance reasons, croniter limits the amount of CPU cycles spent attempting to find the next match.
Starting in v0.3.35, this behavior is configurable via the ``max_years_between_matches`` parameter, and the default window has been increased from 1 year to 50 years.

The defaults should be fine for many use cases.
Applications that evaluate multiple cron expressions or handle cron expressions from untrusted sources or end-users should use this parameter.
Iterating over sparse cron expressions can result in increased CPU consumption or a raised ``CroniterBadDateError`` exception which indicates that croniter has given up attempting to find the next (or previous) match.
Explicitly specifying ``max_years_between_matches`` provides a way to limit CPU utilization and simplifies the iterable interface by eliminating the need for ``CroniterBadDateError``.
The difference in the iterable interface is based on the reasoning that whenever ``max_years_between_matches`` is explicitly agreed upon, there is no need for croniter to signal that it has given up; simply stopping the iteration is preferable.

This example matches 4 AM Friday, January 1st.
Since January 1st isn't often a Friday, there may be a few years between each occurrence.
Setting the limit to 15 years ensures all matches::

    >>> it = croniter("0 4 1 1 fri", datetime(2000,1,1), day_or=False, max_years_between_matches=15).all_next(datetime)
    >>> for i in range(5):
    ...     print(next(it))
    ...
    2010-01-01 04:00:00
    2016-01-01 04:00:00
    2021-01-01 04:00:00
    2027-01-01 04:00:00
    2038-01-01 04:00:00

However, when only concerned with dates within the next 5 years, simply set ``max_years_between_matches=5`` in the above example.
This will result in no matches found, but no additional cycles will be wasted on unwanted matches far in the future.

Iterating over a range using cron
=================================
Find matches within a range using the ``croniter_range()`` function.  This is much like the builtin ``range(start,stop,step)`` function, but for dates.  The `step` argument is a cron expression.
Added in (>=0.3.34)

List the first Saturday of every month in 2019::

    >>> from croniter import croniter_range
    >>> for dt in croniter_range(datetime(2019, 1, 1), datetime(2019, 12, 31), "0 0 * * sat#1"):
    >>>     print(dt)


Hashed expressions
==================

croniter supports Jenkins-style hashed expressions, using the "H" definition keyword and the required hash_id keyword argument.
Hashed expressions remain consistent, given the same hash_id, but different hash_ids will evaluate completely different to each other.
This allows, for example, for an even distribution of differently-named jobs without needing to manually spread them out.

    >>> itr = croniter("H H * * *", hash_id="hello")
    >>> itr.get_next(datetime)
    datetime.datetime(2021, 4, 10, 11, 10)
    >>> itr.get_next(datetime)
    datetime.datetime(2021, 4, 11, 11, 10)
    >>> itr = croniter("H H * * *", hash_id="hello")
    >>> itr.get_next(datetime)
    datetime.datetime(2021, 4, 10, 11, 10)
    >>> itr = croniter("H H * * *", hash_id="bonjour")
    >>> itr.get_next(datetime)
    datetime.datetime(2021, 4, 10, 20, 52)


Random expressions
==================

Random "R" definition keywords are supported, and remain consistent only within their croniter() instance.

    >>> itr = croniter("R R * * *")
    >>> itr.get_next(datetime)
    datetime.datetime(2021, 4, 10, 22, 56)
    >>> itr.get_next(datetime)
    datetime.datetime(2021, 4, 11, 22, 56)
    >>> itr = croniter("R R * * *")
    >>> itr.get_next(datetime)
    datetime.datetime(2021, 4, 11, 4, 19)


Keyword expressions
===================

Vixie cron-style "@" keyword expressions are supported.
What they evaluate to depends on whether you supply hash_id: no hash_id corresponds to Vixie cron definitions (exact times, minute resolution), while with hash_id corresponds to Jenkins definitions (hashed within the period, second resolution).

    ============ ============ ================
    Keyword      No hash_id   With hash_id
    ============ ============ ================
    @midnight    0 0 * * *    H H(0-2) * * * H
    @hourly      0 * * * *    H * * * * H
    @daily       0 0 * * *    H H * * * H
    @weekly      0 0 * * 0    H H * * H H
    @monthly     0 0 1 * *    H H H * * H
    @yearly      0 0 1 1 *    H H H H * H
    @annually    0 0 1 1 *    H H H H * H
    ============ ============ ================


Upgrading
==========

To 2.0.0
---------

- Install or upgrade pytz by using version specified  requirements/base.txt if you have it installed `<=2021.1`.

Develop this package
====================

::

    git clone https://github.com/kiorky/croniter.git
    cd croniter
    virtualenv --no-site-packages venv
    . venv/bin/activate
    pip install --upgrade -r requirements/test.txt
    py.test src


Make a new release
====================
We use zest.fullreleaser, a great release infrastructure.

Do and follow these instructions
::

    . venv/bin/activate
    pip install --upgrade -r requirements/release.txt
    ./release.sh


Contributors
===============
Thanks to all who have contributed to this project!
If you have contributed and your name is not listed below please let me know.

    - mrmachine
    - Hinnack
    - shazow
    - kiorky
    - jlsandell
    - mag009
    - djmitche
    - GreatCombinator
    - chris-baynes
    - ipartola
    - yuzawa-san
    - lowell80 (Kintyre)
    - scop
    - zed2015
    - Ryan Finnie (rfinnie)
    - salitaba



Changelog
==============

2.0.7 (2024-07-16)
------------------

- fix doc


2.0.6 (2024-07-16)
------------------

- Implement second_at_beginning [zhouyizhen, kiorky]
- Support question mark as wildcard [kiorky]
- Support to start a cron from a reference start time [mghextreme, kiorky]


2.0.5 (2024-04-20)
------------------

- No changes, fix lint [kiorky]


2.0.4 (2024-04-20)
------------------

- Support hashid strings in is_valid [george-kuanli-peng, kiorky]
- Avoid over-optimization in crontab expansions [Cherie0125, liqirui <<EMAIL>>, kiorky]


2.0.3 (2024-03-19)
------------------

- Add match_range function [salitaba]


2.0.2 (2024-02-29)
------------------

- fix leap year (29 days in February) [zed2015]


2.0.1 (2023-10-11)
------------------

- Fix release issue [kiorky]


2.0.0 (2023-10-10)
------------------

- Add Python 3.12 support [rafsaf]
- Make major release instructions [kiorky]


1.4.1 (2023-06-15)
------------------

- Make a retrocompatible version of 1.4.0 change about supporting VIXIECRON bug. (fix #47)
  [kiorky]


1.4.0 (2023-06-15)
------------------

- Added "implement_cron_bug" flag to make the cron parser compatible with a bug in Vixie/ISC Cron
  [kiorky, David White <<EMAIL>>]
  *WARNING*: EXPAND METHOD CHANGES RETURN VALUE


1.3.15 (2023-05-25)
-------------------

- Fix hashed expressions omitting some entries
  [@waltervos/Walter Vos <<EMAIL>>]
- Enhance .match() precision for 6 position expressions
  [@szpol/szymon <<EMAIL>>]

1.3.14 (2023-04-12)
-------------------

- Lint


1.3.13 (2023-04-12)
-------------------

- Add check for range begin/end



1.3.12 (2023-04-12)
-------------------

- restore py2 compat


1.3.11 (2023-04-12)
-------------------

-  Do not expose `i` into global namespace


1.3.10 (2023-04-07)
-------------------

- Fix DOW hash parsing [kiorky]
- better error handling on py3 [kiorky]

1.3.8 (2022-11-22)
------------------

- Add Python 3.11 support and move docs files to main folder [rafsaf]


1.3.7 (2022-09-06)
------------------

- fix tests
- Fix croniter_range infinite loop  [Shachar Snapiri <<EMAIL>>]


1.3.5 (2022-05-14)
------------------

- Add Python 3.10 support [eelkevdbos]


1.3.4 (2022-02-18)
------------------

- Really fix compat for tests under py27
  [kiorky]


1.3.3 (2022-02-18)
------------------

- Fix compat for tests under py27
  [kiorky]


1.3.2 (2022-02-18)
------------------

- Fix #12: regressions with set_current
  [kiorky, agateblue]


1.3.1 (2022-02-15)
------------------

- Restore compat with python2
  [kiorky]


1.3.0 (2022-02-15)
------------------

- Add a way to make next() easier to use. This fixes #11
  [kiorky]


1.2.0 (2022-01-14)
------------------

- Enforce validation for day=1. Before this release we used to support day=0 and it was silently glided to day=1 to support having both day in day in 4th field when it came to have 6fields cron forms (second repeat). It will now raises a CroniterBadDateError. See https://github.com/kiorky/croniter/issues/6
  [kiorky]

1.1.0 (2021-12-03)
------------------

- Enforce validation for month=1. Before this release we used to support month=0 and it was silently glided to month=1 to support having both day in month in 4th field when it came to have 6fields cron forms (second repeat). It will now raises a CroniterBadDateError. See https://github.com/kiorky/croniter/issues/6
  [kiorky]

1.0.15 (2021-06-25)
-------------------

- restore py2 [kiorky]


1.0.14 (2021-06-25)
-------------------

- better type checks [kiorky]


1.0.13 (2021-05-06)
-------------------

- Fix ZeroDivisionError with ``* * R/0 * *``
  [cuu508]

1.0.12 (2021-04-13)
-------------------

- Add support for hashed/random/keyword expressions
  Ryan Finnie (rfinnie)
- Review support support for hashed/random/keyword expression and add expanders reactor
  [ kiorky ]


1.0.11 (2021-04-07)
-------------------

- fix bug: bad case:``0 6 30 3 *``
  [zed2015(zhangchi)]
- Add support for ``L`` in the day_of_week component.  This enable expressions like ``* * * * L4``, which means last Thursday of the month.  This resolves #159.
  [Kintyre]
- Create ``CroniterUnsupportedSyntaxError`` exception for situations where CRON syntax may be valid but some combinations of features is not supported.
  Currently, this is used when the ``day_of_week`` component has a combination of literal values and nth/last syntax at the same time.
  For example, ``0 0 * * 1,L6`` or ``0 0 * * 15,sat#1`` will both raise this exception because of mixing literal days of the week with nth-weekday or last-weekday syntax.
  This *may* impact existing cron expressions in prior releases, because ``0 0 * * 15,sat#1`` was previously allowed but incorrectly handled.
  [Kintyre]

- Update ``croniter_range()`` to allow an alternate ``croniter`` class to be used.  Helpful when using a custom class derived from croniter.
  [Kintyre]


1.0.10 (2021-03-25)
-------------------

- Remove external library ``natsort``.
  Sorting of cron expression components now handled with ``sorted()`` with a custom ``key`` function.
  [Kintyre]



1.0.9 (2021-03-23)
------------------

- Remove futures dependency
  [kiorky]


1.0.8 (2021-03-06)
------------------

- Update `_expand` to lowercase each component of the expression.
  This is in relation to #157. With this change, croniter accepts and correctly handles `* * 10-L * *`.
  [cuu508]


1.0.7 (2021-03-02)
------------------

- Fix _expand to reject int literals with underscores
  [cuu508]
- Remove a debug statement to make flake8 happy
  [cuu508]

1.0.6 (2021-02-01)
------------------

- Fix combination of star and invalid expression bugs
  [kiorky]


1.0.5 (2021-01-29)
------------------

- Security fix: fix overflow when using cron ranges
  [kiorky]

1.0.4 (2021-01-29)
------------------

- Spelling fix release


1.0.3 (2021-01-29)
------------------

- Fix #155: raise CroniterBadCronError when error syntax
  [kiorky]


1.0.2 (2021-01-19)
------------------

- Fix match when datetime has microseconds
  [kiorky]

1.0.1 (2021-01-06)
------------------
- no changes, just to make sense with new semver2 (making croniter on a stable state)
  [kiorky]


0.3.37 (2020-12-31)
-------------------

- Added Python 3.8 and 3.9 support
  [eumiro]


0.3.36 (2020-11-02)
-------------------

- Updated docs section regarding ``max_years_between_matches`` to be more shorter and hopefully more relevant.
  [Kintyre]
- Don't install tests
  [scop]


0.3.35 (2020-10-11)
-------------------

- Handle L in ranges. This fixes #142.
  [kiorky]
- Add a new initialization parameter ``max_years_between_matches`` to support finding the next/previous date beyond the default 1 year window, if so desired.  Updated README to include additional notes and example of this usage.  Fixes #145.
  [Kintyre]
- The ``croniter_range()`` function was updated to automatically determines the appropriate ``max_years_between_matches`` value, this preventing handling of the ``CroniterBadDateError`` exception.
  [Kintyre]
- Updated exception handling classes:  ``CroniterBadDateError`` now only
  applies during date finding operations (next/prev), and all parsing errors can now be caught using ``CroniterBadCronError``.  The ``CroniterNotAlphaError`` exception is now a subclass of ``CroniterBadCronError``.  A brief description of each exception class was added as an inline docstring.
  [Kintyre]
- Updated iterable interfaces to replace the ``CroniterBadDateError`` with ``StopIteration`` if (and only if) the ``max_years_between_matches`` argument is provided.  The rationale here is that if the user has specified the max tolerance between matches, then there's no need to further inform them of no additional matches.  Just stop the iteration.  This also keeps backwards compatibility.
  [Kintyre]
- Minor docs update
  [Kintyre]


0.3.34 (2020-06-19)
-------------------

- Feat ``croniter_range(start, stop, cron)``
  [Kintyre]
- Optimization for poorly written cron expression
  [Kintyre]

0.3.33 (2020-06-15)
-------------------

- Make dateutil tz support more official
  [Kintyre]
- Feat/support for day or
  [田口信元]

0.3.32 (2020-05-27)
-------------------

- document seconds repeats, fixes #122
  [kiorky]
- Implement match method, fixes #54
  [kiorky]
- Adding tests for #127 (test more DSTs and croniter behavior around)
  [kiorky]
- Changed lag_hours comparison to absolute to manage dst boundary when getting previous
  [Sokkka]

0.3.31 (2020-01-02)
-------------------

- Fix get_next() when start_time less then 1s before next instant
  [AlexHill]


0.3.30 (2019-04-20)
-------------------

- credits


0.3.29 (2019-03-26)
-------------------

- credits
- history stripping (security)
- Handle -Sun notation, This fixes `#119 <https://github.com/taichino/croniter/issues/119>`_.
  [kiorky]
- Handle invalid ranges correctly,  This fixes `#114 <https://github.com/taichino/croniter/issues/114>`_.
  [kiorky]

0.3.25 (2018-08-07)
-------------------
- Pypi hygiene
  [hugovk]


0.3.24 (2018-06-20)
-------------------
- fix `#107 <https://github.com/taichino/croniter/issues/107>`_: microsecond threshold
  [kiorky]


0.3.23 (2018-05-23)
-------------------

- fix ``get_next`` while preserving the fix of ``get_prev`` in 7661c2aaa
  [Avikam Agur <<EMAIL>>]


0.3.22 (2018-05-16)
-------------------
- Don't count previous minute if now is dynamic
  If the code is triggered from 5-asterisk based cron
  ``get_prev`` based on ``datetime.now()`` is expected to return
  current cron iteration and not previous execution.
  [Igor Khrol <<EMAIL>>]

0.3.20 (2017-11-06)
-------------------

- More DST fixes
  [Kevin Rose <kbrose@github>]


0.3.19 (2017-08-31)
-------------------

- fix #87: backward dst changes
  [kiorky]


0.3.18 (2017-08-31)
-------------------

- Add is valid method, refactor errors
  [otherpirate, Mauro Murari <<EMAIL>>]


0.3.17 (2017-05-22)
-------------------
- DOW occurrence sharp style support.
  [kiorky, Kengo Seki <<EMAIL>>]


0.3.16 (2017-03-15)
-------------------

- Better test suite [mrcrilly@github]
- DST support [kiorky]

0.3.15 (2017-02-16)
-------------------

- fix bug around multiple conditions and range_val in
  _get_prev_nearest_diff.
  [abeja-yuki@github]

0.3.14 (2017-01-25)
-------------------

- issue #69: added day_or option to change behavior when day-of-month and
  day-of-week is given
  [Andreas Vogl <<EMAIL>>]



0.3.13 (2016-11-01)
-------------------

- `Real fix for #34 <https://github.com/taichino/croniter/pull/73>`_
  [kiorky@github]
- `Modernize test infra <https://github.com/taichino/croniter/pull/72>`_
  [kiorky@github]
- `Release as a universal wheel <https://github.com/kiorky/croniter/pull/16>`_
  [adamchainz@github]
- `Raise ValueError on negative numbers <https://github.com/taichino/croniter/pull/63>`_
  [josegonzalez@github]
- `Compare types using "issubclass" instead of exact match <https://github.com/taichino/croniter/pull/70>`_
  [darkk@github]
- `Implement step cron with a variable base <https://github.com/taichino/croniter/pull/60>`_
  [josegonzalez@github]

0.3.12 (2016-03-10)
-------------------
- support setting ret_type in __init__ [Brent Tubbs <<EMAIL>>]

0.3.11 (2016-01-13)
-------------------

- Bug fix: The get_prev API crashed when last day of month token was used. Some
  essential logic was missing.
  [Iddo Aviram <<EMAIL>>]


0.3.10 (2015-11-29)
-------------------

- The functionality of 'l' as day of month was broken, since the month variable
  was not properly updated
  [Iddo Aviram <<EMAIL>>]

0.3.9 (2015-11-19)
------------------

- Don't use datetime functions python 2.6 doesn't support
  [petervtzand]

0.3.8 (2015-06-23)
------------------
- Truncate microseconds by setting to 0
  [Corey Wright]


0.3.7 (2015-06-01)
------------------

- converting sun in range sun-thu transforms to int 0 which is
  recognized as empty string; the solution was to convert sun to string "0"

0.3.6 (2015-05-29)
------------------

- Fix default behavior when no start_time given
  Default value for ``start_time`` parameter is calculated at module init time rather than call time.
- Fix timezone support and stop depending on the system time zone



0.3.5 (2014-08-01)
------------------

- support for 'l' (last day of month)


0.3.4 (2014-01-30)
------------------

- Python 3 compat
- QA Release


0.3.3 (2012-09-29)
------------------
- proper packaging



