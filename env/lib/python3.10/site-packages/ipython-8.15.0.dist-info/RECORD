../../../bin/ipython,sha256=waVjuXCo4ReEptlyICX-NXOsxZFl7VaCKG8F50_bHwk,250
../../../bin/ipython3,sha256=waVjuXCo4ReEptlyICX-NXOsxZFl7VaCKG8F50_bHwk,250
../../../share/man/man1/ipython.1,sha256=PVdQP2hHmHyUEwzLOPcgavnCe9jTDVrM1jKZt4cnF_Q,2058
IPython/__init__.py,sha256=HQ63Jbr5vTF1_WtGAl0mCueYOhp1Q2HX-z1uTq7LPTI,6551
IPython/__main__.py,sha256=hoP6JcHBVEVF5jB_MgVRTmMYQkCNhVbO0<PERSON><PERSON>_<PERSON>,488
IPython/__pycache__/__init__.cpython-310.pyc,,
IPython/__pycache__/__main__.cpython-310.pyc,,
IPython/__pycache__/conftest.cpython-310.pyc,,
IPython/__pycache__/consoleapp.cpython-310.pyc,,
IPython/__pycache__/display.cpython-310.pyc,,
IPython/__pycache__/paths.cpython-310.pyc,,
IPython/conftest.py,sha256=SKDzfbcU9GKnTt6rGDUPIYSDl9h4lFsPg69V-jGM9mg,2618
IPython/consoleapp.py,sha256=JykFsZuW_qOIARJHknArAzc9wyC1HGUD2HvmVhzPc_0,415
IPython/core/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/core/__pycache__/__init__.cpython-310.pyc,,
IPython/core/__pycache__/alias.cpython-310.pyc,,
IPython/core/__pycache__/application.cpython-310.pyc,,
IPython/core/__pycache__/async_helpers.cpython-310.pyc,,
IPython/core/__pycache__/autocall.cpython-310.pyc,,
IPython/core/__pycache__/builtin_trap.cpython-310.pyc,,
IPython/core/__pycache__/compilerop.cpython-310.pyc,,
IPython/core/__pycache__/completer.cpython-310.pyc,,
IPython/core/__pycache__/completerlib.cpython-310.pyc,,
IPython/core/__pycache__/crashhandler.cpython-310.pyc,,
IPython/core/__pycache__/debugger.cpython-310.pyc,,
IPython/core/__pycache__/display.cpython-310.pyc,,
IPython/core/__pycache__/display_functions.cpython-310.pyc,,
IPython/core/__pycache__/display_trap.cpython-310.pyc,,
IPython/core/__pycache__/displayhook.cpython-310.pyc,,
IPython/core/__pycache__/displaypub.cpython-310.pyc,,
IPython/core/__pycache__/error.cpython-310.pyc,,
IPython/core/__pycache__/events.cpython-310.pyc,,
IPython/core/__pycache__/excolors.cpython-310.pyc,,
IPython/core/__pycache__/extensions.cpython-310.pyc,,
IPython/core/__pycache__/formatters.cpython-310.pyc,,
IPython/core/__pycache__/getipython.cpython-310.pyc,,
IPython/core/__pycache__/guarded_eval.cpython-310.pyc,,
IPython/core/__pycache__/history.cpython-310.pyc,,
IPython/core/__pycache__/historyapp.cpython-310.pyc,,
IPython/core/__pycache__/hooks.cpython-310.pyc,,
IPython/core/__pycache__/inputsplitter.cpython-310.pyc,,
IPython/core/__pycache__/inputtransformer.cpython-310.pyc,,
IPython/core/__pycache__/inputtransformer2.cpython-310.pyc,,
IPython/core/__pycache__/interactiveshell.cpython-310.pyc,,
IPython/core/__pycache__/latex_symbols.cpython-310.pyc,,
IPython/core/__pycache__/logger.cpython-310.pyc,,
IPython/core/__pycache__/macro.cpython-310.pyc,,
IPython/core/__pycache__/magic.cpython-310.pyc,,
IPython/core/__pycache__/magic_arguments.cpython-310.pyc,,
IPython/core/__pycache__/oinspect.cpython-310.pyc,,
IPython/core/__pycache__/page.cpython-310.pyc,,
IPython/core/__pycache__/payload.cpython-310.pyc,,
IPython/core/__pycache__/payloadpage.cpython-310.pyc,,
IPython/core/__pycache__/prefilter.cpython-310.pyc,,
IPython/core/__pycache__/profileapp.cpython-310.pyc,,
IPython/core/__pycache__/profiledir.cpython-310.pyc,,
IPython/core/__pycache__/prompts.cpython-310.pyc,,
IPython/core/__pycache__/pylabtools.cpython-310.pyc,,
IPython/core/__pycache__/release.cpython-310.pyc,,
IPython/core/__pycache__/shellapp.cpython-310.pyc,,
IPython/core/__pycache__/splitinput.cpython-310.pyc,,
IPython/core/__pycache__/ultratb.cpython-310.pyc,,
IPython/core/__pycache__/usage.cpython-310.pyc,,
IPython/core/alias.py,sha256=S7q-haVF8diea8Fre7mo2N7IqsjfxTd2zA4fI3rZmkM,10034
IPython/core/application.py,sha256=nzXXjwBboMIhawWtSE4B4bKVyjgEaYtn2C2ho2Hlw7k,18937
IPython/core/async_helpers.py,sha256=HGcohjkgpnha39Ec12YgW0eB7F0Cz2Zc0Mj8rX8qHgc,4297
IPython/core/autocall.py,sha256=109_9e0zV5jcK1ax8SZxp9vsIAu0m_KHbZINEnDSyv8,1991
IPython/core/builtin_trap.py,sha256=2kYS2hoQQ0eDFQAJE9XTp_sQrBVyadDFO2rdPXZbv2I,3009
IPython/core/compilerop.py,sha256=BPie6q8NtBWd08bZ4hiHUfPi3wyhakhCkO1wpFRl1r0,7730
IPython/core/completer.py,sha256=eiEzYdb35b-SgEWc5_FjMSDUgC6FzKVTnaZN61Hd93M,117850
IPython/core/completerlib.py,sha256=Bqw_JGN7Uh_yjO96ynZsvRq-jA1_YBw1gIHkN4L5URg,12259
IPython/core/crashhandler.py,sha256=QMtLXKtrtCkRzLoMU7vaCNvC3k7F7JoV1EUZ2I1q1tg,8508
IPython/core/debugger.py,sha256=IG1i_Coh43PHQQ0nppqrciq_W3afOm3CqwxwX6bH9i0,38262
IPython/core/display.py,sha256=1FEVoLOYXWbL6PiaHO576n2g5o1miIezSM6RGy2WmVU,42506
IPython/core/display_functions.py,sha256=UkI057nIpcOYynQATKtNzwWXpM8MK2GMICzma8BVAp4,12918
IPython/core/display_trap.py,sha256=31jM26B3PhNSLJhpyRHuTWscObwVMOlH9PDC2v8HmsU,2098
IPython/core/displayhook.py,sha256=hNatKE_7sMairKUuXWrPF8YpivFyS-faru2e5H8__m8,12962
IPython/core/displaypub.py,sha256=4r-xfGXX0yqhYZ8TtfjLndqLrbYFr3TRFFUm1b-pPzE,4944
IPython/core/error.py,sha256=lL5WP3E0sGNWkBTQL0u795wLQJCSrDCf328ft8TfCjk,1734
IPython/core/events.py,sha256=Mv5hjV9CrDoFewb7tgNnD0cZwRXIukdquKjbNpiSrMQ,5586
IPython/core/excolors.py,sha256=IzILJEC0xPcBbcQx6aFNC280xAUX8Ixbt8MEOibdZs0,4928
IPython/core/extensions.py,sha256=QQwyuzrtBvn5A27uyvJJSliq8o-tgtQd34Dfwfnij7I,5780
IPython/core/formatters.py,sha256=sjJmbV29RoXNwZIJMlNXY09ODQ1zfl1qwPJaICt51Ww,34981
IPython/core/getipython.py,sha256=znwXeOUdFNiyfnJuN6BGmHEk94KjV9mQasXc60QPGF0,912
IPython/core/guarded_eval.py,sha256=ewUvLh-Zd-ZYkje-VaI3mQomfcdR8TWGv7WFH4V7P9I,23855
IPython/core/history.py,sha256=HFUZ4RmooejeY_fFfB-ct8aag81GECjpJ8NSjvMWQDc,34475
IPython/core/historyapp.py,sha256=Vba0W2a5K-J8WXPurjLahedJt_Erho_p7bCohRLolQU,5909
IPython/core/hooks.py,sha256=AqFSlbYxpwosX5gvIpr7l1dtNRgvorD8D9_dagwqdVQ,5663
IPython/core/inputsplitter.py,sha256=5cBSeKO5_HhslKIOxrfn_QyCARjrwIvZpNgXVB5BQTc,28558
IPython/core/inputtransformer.py,sha256=Hy-8glqUbH8scw-Xh2Ccm3OSNpJyKjDDxo2M3ZyOzeQ,18007
IPython/core/inputtransformer2.py,sha256=Pb7M0OY1tdzlLlAvyarcBgmkMjkl2DWJvabXALVNcFM,29393
IPython/core/interactiveshell.py,sha256=uxJhNA7uxetHrpixeAHq31-CzJdsi59peg6C_ETg_xM,153176
IPython/core/latex_symbols.py,sha256=YiIWrYezmqutOkwydHg7VVWyNVjPaL6uqfF_iyGEhbk,31288
IPython/core/logger.py,sha256=t1MEuwcDMJ5SshE36n2KUtcs2LPNWKniIFsKB2_nCCk,8441
IPython/core/macro.py,sha256=83KmGTzviEMXNLAzfdL-Mwi47zAGmbpFjDSnYPNYpfM,1734
IPython/core/magic.py,sha256=Z9Ntw-43GZKz6XNJBzBUYYbB1xPa3GTZ34ZBmPh3H5c,28899
IPython/core/magic_arguments.py,sha256=JWgPRHXdckllfkHq8mk30xXLJKmoB9mABadd8z4_uyk,9734
IPython/core/magics/__init__.py,sha256=pkd-UfzjDGp5UHuFKjw192vZnigpTP9ftXzG3oLdiS8,1619
IPython/core/magics/__pycache__/__init__.cpython-310.pyc,,
IPython/core/magics/__pycache__/ast_mod.cpython-310.pyc,,
IPython/core/magics/__pycache__/auto.cpython-310.pyc,,
IPython/core/magics/__pycache__/basic.cpython-310.pyc,,
IPython/core/magics/__pycache__/code.cpython-310.pyc,,
IPython/core/magics/__pycache__/config.cpython-310.pyc,,
IPython/core/magics/__pycache__/display.cpython-310.pyc,,
IPython/core/magics/__pycache__/execution.cpython-310.pyc,,
IPython/core/magics/__pycache__/extension.cpython-310.pyc,,
IPython/core/magics/__pycache__/history.cpython-310.pyc,,
IPython/core/magics/__pycache__/logging.cpython-310.pyc,,
IPython/core/magics/__pycache__/namespace.cpython-310.pyc,,
IPython/core/magics/__pycache__/osm.cpython-310.pyc,,
IPython/core/magics/__pycache__/packaging.cpython-310.pyc,,
IPython/core/magics/__pycache__/pylab.cpython-310.pyc,,
IPython/core/magics/__pycache__/script.cpython-310.pyc,,
IPython/core/magics/ast_mod.py,sha256=2NII5ahWDJGTkns-IcL1qOigbDNiV0zn0WxQ9JcuU-Q,10179
IPython/core/magics/auto.py,sha256=ZQnw51feGjsxK1KOjRAyY_WQPMwRkrqBa4vzV4X-j-E,4816
IPython/core/magics/basic.py,sha256=pDhBvzfB61w8HHa7WNWMLIFcPEKtOxptq6VVNOw920c,23138
IPython/core/magics/code.py,sha256=CB7jt4XSRyCsOrm7r371l_y3ya9-AN2wF7BItXXQaCU,28051
IPython/core/magics/config.py,sha256=QBL5uY7m-Q7C46mO3q1Yio9s73w1TnI9y__j5E-j44Y,4881
IPython/core/magics/display.py,sha256=STRq66GlZwcvFyBxbkqslclpP_s9LnqD0ew9Z3S4-Jo,3130
IPython/core/magics/execution.py,sha256=99jI56S8RaW6RHiPnBBR-j5l8pNwb6lPScXFfGDtO_4,60894
IPython/core/magics/extension.py,sha256=Jj6OlkM71PS0j1HfEMDc-jU2Exwo9Ff_K0nD7e_W4N0,2477
IPython/core/magics/history.py,sha256=ASgJ4Do2acCfaB8nPSuOGKORRVPFNNtBNTgosmhrMpk,12628
IPython/core/magics/logging.py,sha256=eUWzo8aRqmhmbxsjmJYoGdKpf1PtTvyNxafrrDZJSmg,6859
IPython/core/magics/namespace.py,sha256=enU3JDllAYXVsaSMixrtziXq4bonrCBcxDz0lj23W3o,24823
IPython/core/magics/osm.py,sha256=qfqUa1qr3BEnQ1KEFGizAYWI15HNxC5sESi-kR1woUw,30696
IPython/core/magics/packaging.py,sha256=LdT-KwUm-KVCp-MaK9GzmtngqXxhKWkl7MZfSuo2SDA,3916
IPython/core/magics/pylab.py,sha256=wwyGr-CVpnX_UkB8pFUaBILo4PXCIMwZNmXEw0tPT6Q,6626
IPython/core/magics/script.py,sha256=K1fIlBCjHUVBM7oYrt-OioZD5TL2kC6qfjAwge4IGQY,12543
IPython/core/oinspect.py,sha256=3gFr3sLh4R9T8N59voNcM-jmV-PKrqbGio7dJNI-WKs,39844
IPython/core/page.py,sha256=3ekRpUguRgRq9C8gX-uFy3Yk5px_dw5MG7dMuirXCAU,11753
IPython/core/payload.py,sha256=SOvmxRbZgCI_fq4ZoOeMKsblk8rYsuHumUefk-3TaTI,1758
IPython/core/payloadpage.py,sha256=SFhobOhkr9w_69c8oWVcfZGexDciU4i9Lltztqg6fco,1431
IPython/core/prefilter.py,sha256=APwfql3v4wIRB7HpCifqLVyWweqe8Gi05k2eMlQqRUI,25588
IPython/core/profile/README_STARTUP,sha256=Y47GtYQkWy6pdzkqylUNL6eBSZMUIRGwTxXPUF_BBBc,371
IPython/core/profileapp.py,sha256=3BHKEknttcY_T9rPzk9GeMS_OhIn3n46TeDPvJb-zK8,10631
IPython/core/profiledir.py,sha256=V2_b4pAR4A8ndjyJdn5xf7F5IRhwsONfbNo4tu6dJvI,8029
IPython/core/prompts.py,sha256=3icFefY5NVob6-JeUORH_uRK1h4JTygC3oH1HbN4Q0Q,607
IPython/core/pylabtools.py,sha256=3L26ZLCcrLkAuAr-vZH2ry60T-Lna89Gi_kKTnMh8ac,14018
IPython/core/release.py,sha256=LgDc6y7ZIiqeLBZ3ZtOPbsK5cs6PMRTecCaeemU4aiM,2178
IPython/core/shellapp.py,sha256=I1diBBMEub6JWrEK0LXKQS1Y5VfT1qtsvKFHP3UXOtM,17845
IPython/core/splitinput.py,sha256=rj9MWhSQK6fw3Nx4b58h0pfQoJlLeOYeQzxwqzMC8zI,4836
IPython/core/tests/2x2.jpg,sha256=YF88a1SklCHZeEbuKL9H13TJM8t8myvSqgffxh7LgkY,331
IPython/core/tests/2x2.png,sha256=ZB6RfzGIikj1RojC3sbJNIeiMAjbcfur9IvYVcfi2-I,71
IPython/core/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/core/tests/__pycache__/__init__.cpython-310.pyc,,
IPython/core/tests/__pycache__/bad_all.cpython-310.pyc,,
IPython/core/tests/__pycache__/nonascii.cpython-310.pyc,,
IPython/core/tests/__pycache__/nonascii2.cpython-310.pyc,,
IPython/core/tests/__pycache__/print_argv.cpython-310.pyc,,
IPython/core/tests/__pycache__/refbug.cpython-310.pyc,,
IPython/core/tests/__pycache__/simpleerr.cpython-310.pyc,,
IPython/core/tests/__pycache__/tclass.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_alias.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_application.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_async_helpers.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_autocall.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_compilerop.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_completer.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_completerlib.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_debugger.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_display.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_displayhook.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_events.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_exceptiongroup_tb.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_extension.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_formatters.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_guarded_eval.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_handlers.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_history.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_hooks.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_imports.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_inputsplitter.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_inputtransformer.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_inputtransformer2.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_inputtransformer2_line.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_interactiveshell.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_iplib.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_logger.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_magic.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_magic_arguments.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_magic_terminal.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_oinspect.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_page.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_paths.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_prefilter.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_profile.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_prompts.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_pylabtools.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_run.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_shellapp.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_splitinput.cpython-310.pyc,,
IPython/core/tests/__pycache__/test_ultratb.cpython-310.pyc,,
IPython/core/tests/bad_all.py,sha256=GARnHj1buvbHLjoKLiEy7XTqvo91Xb7ckOrNqh8YsnY,206
IPython/core/tests/daft_extension/__pycache__/daft_extension.cpython-310.pyc,,
IPython/core/tests/daft_extension/daft_extension.py,sha256=P1-QHT4a71h0vbRRPHItoFSTLySzH03tjXvBuEHdJFc,285
IPython/core/tests/nonascii.py,sha256=dLpEo2Q49dEMmo13f49ac4Hy1zixiDsVXehazBJfi54,135
IPython/core/tests/nonascii2.py,sha256=D81kLhC1wGgYv9XjY89FKZSyKRz_rTAsKY9dOZ507cs,155
IPython/core/tests/print_argv.py,sha256=hZW-UX2m6WLotOLClNc3Bt8Anik3UjSbli5bp6OnT2A,32
IPython/core/tests/refbug.py,sha256=A9Usb4msz_SlT4MMjDoySvoJp-02ucc1mZ12IfmLKqw,1494
IPython/core/tests/simpleerr.py,sha256=WXX2k9CSDEKY7G7zXXYdZcpcavmFi42e-0JTXWeqDEw,583
IPython/core/tests/tclass.py,sha256=SqfWzMaQVWHQFPn7_DFI04yvATZcaxJlXAwf9aSoNE8,921
IPython/core/tests/test_alias.py,sha256=sfvm14mp0lbzFvr374h1j6bfWk9gk7t6XKArGrWIX8I,2007
IPython/core/tests/test_application.py,sha256=IZZXoWDg4A4QJTqECd7-mdBENzfXnlLcj_r8aKvl8fI,2245
IPython/core/tests/test_async_helpers.py,sha256=8_KYiA5geXa-53Y-Cm2wPetSvQtt9owv4LEcIYneQCY,8647
IPython/core/tests/test_autocall.py,sha256=JLFN1yM8D8D9ebdmkioalSmTGDeJayLMzKzFEnZxDpE,1425
IPython/core/tests/test_compilerop.py,sha256=6Uz1MtsruRp0GL_k8j6tYFR9c77jxEvTOavwya5sMZI,2174
IPython/core/tests/test_completer.py,sha256=Ti3heuyzD1osB45pN2xB1b-9ED-of86XeW1U9hqb4n0,60803
IPython/core/tests/test_completerlib.py,sha256=OVEWgAxfoxGGkyDix2pD1L9Gime1Abk3ljccFCJH__A,6507
IPython/core/tests/test_debugger.py,sha256=bV5RCMBHxQj8Qdu_i-zeOzu2PkzVUgMUOLz6AgCFxHE,14501
IPython/core/tests/test_display.py,sha256=O_5xtc3wd0j4etmDDsVrk9e10QgTROblHPkSsQdPaFk,15946
IPython/core/tests/test_displayhook.py,sha256=qpnjIMNxvoXKB-Cc4rUK-1epbZJIJiyAvpB5ecB0UKY,3593
IPython/core/tests/test_events.py,sha256=Ao2kwwNsAyhuTF-7pTRjVBbeltvvvZOw1lJGEO-LPmo,2799
IPython/core/tests/test_exceptiongroup_tb.py,sha256=1Hf-2_yGiLzbc6NTO1bvoh1AuPx4jr-1MRHqX6wIZLQ,3599
IPython/core/tests/test_extension.py,sha256=MsaNMVltHZf_wVfbDR64lz681HDBPa7BHi56KeFh6og,3026
IPython/core/tests/test_formatters.py,sha256=kRzxuGwRubUSpzPd5jCyEBbntbxLSQjgKGmjunH0sV4,14249
IPython/core/tests/test_guarded_eval.py,sha256=7T9zj63ZvjqnzcKI8lHX6Sqh9KSbQm8BkOa4NSVf8UM,15160
IPython/core/tests/test_handlers.py,sha256=o9oxD0UFYVCb7pazgbn7-PuqU1QK93Cb8pw9s1l6xoM,3286
IPython/core/tests/test_history.py,sha256=-DNmLket-C0T1nhGBY8uiit1Z_xA6JEmmS952JFCk38,11698
IPython/core/tests/test_hooks.py,sha256=TXgjnflaoteTBR79n8P4pBkN9yllGclX42a5ewjmdA4,2316
IPython/core/tests/test_imports.py,sha256=lR-_hRQC-AIe750c4k3AmCbN3spE-KaZevP2Cx8mzNY,1153
IPython/core/tests/test_inputsplitter.py,sha256=QO1OW0gG4lCkEQmyJXUlFpzoZK5vGW2b9fjcu0nLSp4,23159
IPython/core/tests/test_inputtransformer.py,sha256=zQRaBtWZg4Thjc9ZJFdO4inJZJ1FyYL7bpHerDuT7-o,15755
IPython/core/tests/test_inputtransformer2.py,sha256=zSLLUlWOROdU-l3J7b9NmiR6wjL-J2JjIFS9xUFsR3o,11408
IPython/core/tests/test_inputtransformer2_line.py,sha256=4-Jpebco_LW8Yzpf6OmGryugqYTOV9-MHWl6bp9QFrc,2971
IPython/core/tests/test_interactiveshell.py,sha256=4f9PNeUAkepohol_Pj4jwg7ANW63lpEDGjX0cny-Yg4,39660
IPython/core/tests/test_iplib.py,sha256=wDI6uHw5MoRcgh8pZ4wnttm-d7tnYZPe5UNBMdtgchQ,6677
IPython/core/tests/test_logger.py,sha256=MPLAcvIq_ggtO3M0RP62TNUrVVjG7rvCQ0-pSm3MMrk,776
IPython/core/tests/test_magic.py,sha256=cBLxACFdBk_qkOjtBj_IR6_vok7VuNOoD01SpHQfw8Y,44359
IPython/core/tests/test_magic_arguments.py,sha256=_wOAJNFzaaFCOb4-0i-55jvz4PYR0f4YDc3NEAvxk5Y,4778
IPython/core/tests/test_magic_terminal.py,sha256=LiHprrkhNSf_3HRsAu7XKUSPir7xAoIVVCIOCkJBNPE,5996
IPython/core/tests/test_oinspect.py,sha256=oTbFsVy4hJC3V47xIf-fOdXkGo62p3c-JIkzUrA6iS0,15674
IPython/core/tests/test_page.py,sha256=dA1r_xWG90NNDvtHpr11JxNUCc_E7QoIEgoUmMLvrdc,772
IPython/core/tests/test_paths.py,sha256=76KV2HnzOMaulYOq4Q-HzQY3hX1PwW-69L9pai9ojug,7081
IPython/core/tests/test_prefilter.py,sha256=2jMCKei2EtYDhMFRq0nnRwThITJVqEG9mCt8d705pko,4214
IPython/core/tests/test_profile.py,sha256=dqVTOo5lQGNa88liGgmRg_XLaO26PqmVH-i_9P-V1Cg,5064
IPython/core/tests/test_prompts.py,sha256=jbga48K0Fg92Fx92k9JuKz5o74yhhoKP2dzrMKZ8Gy8,845
IPython/core/tests/test_pylabtools.py,sha256=BWwqDONyLq8nXWpvBkQqh1gMJKIR5KitCxt1XjaDoRg,7880
IPython/core/tests/test_run.py,sha256=GTHUc_zA2Nht6znLnhjVTstnBLl2PMvqyW4DxaCx4hQ,19369
IPython/core/tests/test_shellapp.py,sha256=fVIPW5-E87td88SI-aVGtrO_KHEZ-MCxsR3vsHihXvc,1892
IPython/core/tests/test_splitinput.py,sha256=VTHJGd0RKLHC795BlB9QK58DNRU4ISgORN-EPTEb4eU,1227
IPython/core/tests/test_ultratb.py,sha256=mnQ5yzgBRUNNPNarp0Is0Ogkapt8CAHjR3U3YdLneWk,12026
IPython/core/ultratb.py,sha256=VG-ypR5KPJN9TyeUUj3O7_L9yP7GdglNGlY89UXHMt4,55803
IPython/core/usage.py,sha256=agrZE5eZIvJnXoqI8VV9e-oWZx5LbLxUq9MdQpEyts4,13542
IPython/display.py,sha256=Ypudso89kWK-sdxU2j2L1kZwudtYIqwB7w577dehWZ8,1119
IPython/extensions/__init__.py,sha256=V4vSllFd18CVUvDTbXLYqfwTFmPXUiQZGCs5LX2IliE,78
IPython/extensions/__pycache__/__init__.cpython-310.pyc,,
IPython/extensions/__pycache__/autoreload.cpython-310.pyc,,
IPython/extensions/__pycache__/storemagic.cpython-310.pyc,,
IPython/extensions/autoreload.py,sha256=PeGRjenxcA4Aw3Ast8izUhhZUNlJ9xLMmT3AqhdIxJU,23463
IPython/extensions/storemagic.py,sha256=8kdpgnamB2zYoP8n_Tawd3zrzjahg2w7Iva9QFOgEVc,8161
IPython/extensions/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/extensions/tests/__pycache__/__init__.cpython-310.pyc,,
IPython/extensions/tests/__pycache__/test_autoreload.cpython-310.pyc,,
IPython/extensions/tests/__pycache__/test_storemagic.cpython-310.pyc,,
IPython/extensions/tests/test_autoreload.py,sha256=RRoGCGdSQ9WtGJXEZ3tFj4Um2lVz22Vvqtmt_8-qfSg,22649
IPython/extensions/tests/test_storemagic.py,sha256=ToLU7rBwOwBU-63KEWEpwT8cGQJ7Rr8RyOE4z9Ys__4,2081
IPython/external/__init__.py,sha256=-EQHbuUnBe1RS1_CwaLGzNSZQsCJsrxHW_r15smvVW0,126
IPython/external/__pycache__/__init__.cpython-310.pyc,,
IPython/external/__pycache__/qt_for_kernel.cpython-310.pyc,,
IPython/external/__pycache__/qt_loaders.cpython-310.pyc,,
IPython/external/qt_for_kernel.py,sha256=NHamODujSAq1IrbtFJ5HuEDcuFpjNDtztYjCXlZjyYQ,3443
IPython/external/qt_loaders.py,sha256=JLtgi7BPSXo4KtMDSRxj5T_Amzf98P7mdDgwn9l7O9A,11510
IPython/external/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/external/tests/__pycache__/__init__.cpython-310.pyc,,
IPython/external/tests/__pycache__/test_qt_loaders.cpython-310.pyc,,
IPython/external/tests/test_qt_loaders.py,sha256=EIsNOsGlfMeOWT1OLKDvAAkxWFs_ltO2V1tGKBa4mz0,367
IPython/lib/__init__.py,sha256=O_m_I8hczvUsgl01oAZd_TelugfR-2oKTXhxqbXHcwk,409
IPython/lib/__pycache__/__init__.cpython-310.pyc,,
IPython/lib/__pycache__/backgroundjobs.cpython-310.pyc,,
IPython/lib/__pycache__/clipboard.cpython-310.pyc,,
IPython/lib/__pycache__/deepreload.cpython-310.pyc,,
IPython/lib/__pycache__/demo.cpython-310.pyc,,
IPython/lib/__pycache__/display.cpython-310.pyc,,
IPython/lib/__pycache__/editorhooks.cpython-310.pyc,,
IPython/lib/__pycache__/guisupport.cpython-310.pyc,,
IPython/lib/__pycache__/latextools.cpython-310.pyc,,
IPython/lib/__pycache__/lexers.cpython-310.pyc,,
IPython/lib/__pycache__/pretty.cpython-310.pyc,,
IPython/lib/backgroundjobs.py,sha256=9uj1cPtseoXsYYn0gNC1cadPHPz8MvYCa9HpFz3v-_A,17738
IPython/lib/clipboard.py,sha256=izILyYHBH892cwcz6YlPdujBd6EO8CgI9-yUlLvCnFw,3050
IPython/lib/deepreload.py,sha256=iymTEcJpP_hpcO1H5GRgb2aODGUtiUFksodoIe9738E,9431
IPython/lib/demo.py,sha256=hKJousFvJQ8wm2a_tlwqnN_e4V8V-gzXS8UPfoo6-O0,24518
IPython/lib/display.py,sha256=igyK1cNwqYiHkmvI7oMNdhm6p1qrHT_2R_rx5gYVOtg,24566
IPython/lib/editorhooks.py,sha256=KmQaAEZawUhHqy9CvRkbmc8BNXx4b1pqqLFhTEBf1CY,3982
IPython/lib/guisupport.py,sha256=e3gQE2-rYF2Mj9EFqSYOKOjMDmys8o-yae7SEjiOeEs,6300
IPython/lib/latextools.py,sha256=tpBvrBqWCBAp-qnUKIlmhGswWJdyUxzDmFjoMEHoP8Y,8177
IPython/lib/lexers.py,sha256=Lr2y_1iXGbyc2TG11_LeyBg7IXFR--0nDUj72KMNaaU,20968
IPython/lib/pretty.py,sha256=MFrrOYopMUQCjVM6v2CKl9iGADKSx3Ow7vfJhnylL4Y,30886
IPython/lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/lib/tests/__pycache__/__init__.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_backgroundjobs.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_clipboard.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_deepreload.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_display.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_editorhooks.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_imports.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_latextools.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_lexers.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_pretty.cpython-310.pyc,,
IPython/lib/tests/__pycache__/test_pygments.cpython-310.pyc,,
IPython/lib/tests/test.wav,sha256=y6O86Ch8OfzBfXicO8yG31DyYifGpYMPJgn-NTj1OS4,44144
IPython/lib/tests/test_backgroundjobs.py,sha256=8_rca5-mEeBAiFW_vhgricTkWoZsVbgSgDy4jP7M_YU,2698
IPython/lib/tests/test_clipboard.py,sha256=B4u-RAGkcWd5MhoPeYOlmS7SvGQaQrVi0SwrEcOKFCM,630
IPython/lib/tests/test_deepreload.py,sha256=SQcT4vnTdT2d5-1Fzj4ssCC6h6v-f2xguO01zAnJTCI,1854
IPython/lib/tests/test_display.py,sha256=gYAAjYV_MJeJf_hEZzs_karq7e3eOiF3tFuZ02sAhic,9313
IPython/lib/tests/test_editorhooks.py,sha256=dgl3FrxKrYNtRFO9n3hTho5KnmTUiPlwSSXQAZB14fo,884
IPython/lib/tests/test_imports.py,sha256=59til_pfq_0PFq7-Uhnao05ILUQeqUKNSZe1QCZmWD0,273
IPython/lib/tests/test_latextools.py,sha256=rh2xReu5pt0v7YZXHY1J7y-UQ8bJxQNs8QxgxZNIpOI,5736
IPython/lib/tests/test_lexers.py,sha256=2mo5prbF8fTJAx_Ris_zpOKsWrhVHU2lD-XV5tSgjig,6017
IPython/lib/tests/test_pretty.py,sha256=s9fY-L5U774CP7oRh_udRLgtkyBrR8xGVoKre5dwLZ4,14890
IPython/lib/tests/test_pygments.py,sha256=PBsPc3BIx1IEJRUaxocBcK741MuQJsASA1yscepMDO8,824
IPython/paths.py,sha256=iOW1ArUYyCQRXwVZfvHOFB-AcqVAQqb2eLvGOe98xfc,4335
IPython/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/sphinxext/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/sphinxext/__pycache__/__init__.cpython-310.pyc,,
IPython/sphinxext/__pycache__/custom_doctests.cpython-310.pyc,,
IPython/sphinxext/__pycache__/ipython_console_highlighting.cpython-310.pyc,,
IPython/sphinxext/__pycache__/ipython_directive.cpython-310.pyc,,
IPython/sphinxext/custom_doctests.py,sha256=6Z078UoaYUlniKTFv4K61LiJe6zt-hvo6ESWb-Y8_gE,4615
IPython/sphinxext/ipython_console_highlighting.py,sha256=D60MJnG-Dsv4B_Byd9FmA3ow9PJBx2Q1B2BWMQB9XYg,970
IPython/sphinxext/ipython_directive.py,sha256=-K3216EiyQZV8qMWD4vfLnaek0Qg3bX1G9fcsnQVDjQ,45149
IPython/terminal/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/terminal/__pycache__/__init__.cpython-310.pyc,,
IPython/terminal/__pycache__/console.cpython-310.pyc,,
IPython/terminal/__pycache__/debugger.cpython-310.pyc,,
IPython/terminal/__pycache__/embed.cpython-310.pyc,,
IPython/terminal/__pycache__/interactiveshell.cpython-310.pyc,,
IPython/terminal/__pycache__/ipapp.cpython-310.pyc,,
IPython/terminal/__pycache__/magics.cpython-310.pyc,,
IPython/terminal/__pycache__/prompts.cpython-310.pyc,,
IPython/terminal/__pycache__/ptutils.cpython-310.pyc,,
IPython/terminal/console.py,sha256=LhKpLPNW6KCk2fPssrMNjxb3dP4tfxuVb75NtjENwN0,691
IPython/terminal/debugger.py,sha256=rGZVKZmFDxJbC5P3s4XweaH9H0o9bOQv_H_WpOA-8FY,7106
IPython/terminal/embed.py,sha256=o4AY2QqeuKyGkflWCcGjs3ymwA55_9mojJFDxQKw_9o,15786
IPython/terminal/interactiveshell.py,sha256=M1DC4G7KVKtrWdqDEMmRsVu6ApDwFarQO950IUqKq1s,37158
IPython/terminal/ipapp.py,sha256=Fd6qxBRZ7C66ywj51xLAfBp8LiNf9O7OViOWYSmOxQ8,12407
IPython/terminal/magics.py,sha256=49ZVJzbAUkG_EFpebxIBEqm3tEClvqefoeM6QnxGrrk,7705
IPython/terminal/prompts.py,sha256=0LCiFqB6fWXpgni3cGaUfkYG-TPnTQMC5-oq4hZui3g,3360
IPython/terminal/pt_inputhooks/__init__.py,sha256=0vP53fcZZ4Occ3jEMuoLdku_VVOODH8h3qcX2YSfIX8,3542
IPython/terminal/pt_inputhooks/__pycache__/__init__.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/asyncio.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/glut.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/gtk.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/gtk3.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/gtk4.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/osx.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/pyglet.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/qt.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/tk.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/wx.cpython-310.pyc,,
IPython/terminal/pt_inputhooks/asyncio.py,sha256=sHXYhKqzXmPQMzerxqBRheFqOetx8P4-z7k-J_b3TAE,1844
IPython/terminal/pt_inputhooks/glut.py,sha256=ATdBrX7p904Y0zXQazeLYSxRD0T9dUNLBuRFOQDXyH4,4997
IPython/terminal/pt_inputhooks/gtk.py,sha256=qx0OTtDdinx39bD9Fbf6m0JZWBXmuoypr56yUITFtfE,2427
IPython/terminal/pt_inputhooks/gtk3.py,sha256=eTzGx-SkT1wlplTtuRSPNzPdnnrJQRQ9FmZ_nvmKmP0,280
IPython/terminal/pt_inputhooks/gtk4.py,sha256=r_MxCT7a0nTHZtqyuZpPgCW2Cl7iuomC0PjgFteSL9c,557
IPython/terminal/pt_inputhooks/osx.py,sha256=TnndyR_SPbmWC_A--0ORB06rhH7IS2_7kjphfPcRqXo,4448
IPython/terminal/pt_inputhooks/pyglet.py,sha256=_zyQkcOUGpBoMQ5DGTHbIEIIwtMjN2Ycq5hdfkPE9nQ,2368
IPython/terminal/pt_inputhooks/qt.py,sha256=VerEsr3ivUcQXB60Slc1pd9pTfGBshq1LWkSQ2i-WC0,3287
IPython/terminal/pt_inputhooks/tk.py,sha256=1aSYCec97FQpk7HyvIKHFiWBrJrJN7zgXHENQ3DjMEE,3648
IPython/terminal/pt_inputhooks/wx.py,sha256=RZC045DMr17sMSWS63VZlI_7vkg_evOBZilxJuc6uZQ,7132
IPython/terminal/ptutils.py,sha256=12mXHNFJT-qaI-KiI_8B5m8rQphAMGDXQiy4IRYjvjA,7481
IPython/terminal/shortcuts/__init__.py,sha256=4_lbTK3IWIvStupzlwYStRBMxVBooiNcXtAXw0rjrbc,18452
IPython/terminal/shortcuts/__pycache__/__init__.cpython-310.pyc,,
IPython/terminal/shortcuts/__pycache__/auto_match.cpython-310.pyc,,
IPython/terminal/shortcuts/__pycache__/auto_suggest.cpython-310.pyc,,
IPython/terminal/shortcuts/__pycache__/filters.cpython-310.pyc,,
IPython/terminal/shortcuts/auto_match.py,sha256=6jlu7xrOofUXSccb6A2e8q-Y8li2GyfiRsGvgLuUQFg,3065
IPython/terminal/shortcuts/auto_suggest.py,sha256=iOnjSSh8dkYZkakRqhley4rmD8OYWdsL24UayDPzW-8,13449
IPython/terminal/shortcuts/filters.py,sha256=7WESijHpfQjEr9Or19vUel6skw9BSj0oJkdUzc-T0FI,10997
IPython/terminal/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/terminal/tests/__pycache__/__init__.cpython-310.pyc,,
IPython/terminal/tests/__pycache__/test_debug_magic.cpython-310.pyc,,
IPython/terminal/tests/__pycache__/test_embed.cpython-310.pyc,,
IPython/terminal/tests/__pycache__/test_help.cpython-310.pyc,,
IPython/terminal/tests/__pycache__/test_interactivshell.cpython-310.pyc,,
IPython/terminal/tests/__pycache__/test_pt_inputhooks.cpython-310.pyc,,
IPython/terminal/tests/__pycache__/test_shortcuts.cpython-310.pyc,,
IPython/terminal/tests/test_debug_magic.py,sha256=vYFqECY6WyrNNcdgme0k6XGQ4DqNzH8OB73dxifVIXI,2448
IPython/terminal/tests/test_embed.py,sha256=cSElGIEfvrwqK1i6tgEu3ZEhbanqqVrYm5ddeFferds,4804
IPython/terminal/tests/test_help.py,sha256=aPcn8TmY1sey8wl09fmXyPZR3bUO8WYUzd61VPEjr44,722
IPython/terminal/tests/test_interactivshell.py,sha256=OPeC4P0Zdm1faZiFwoK3ZLyA96KL3TCmaxhMEXk5vu0,8276
IPython/terminal/tests/test_pt_inputhooks.py,sha256=Nx-7XeCLr7nSjLRoXnmcRYZO8S4U1boDBOiyCWLCjZ0,1599
IPython/terminal/tests/test_shortcuts.py,sha256=6N9PlysuqmgdkBzMQJyYzbV9lD1OI8KEGyxz5rR7JwM,14936
IPython/testing/__init__.py,sha256=9t97XO03Ez9GdZA5FWZYmfyHZt2c3AqQe2dj_0AiPJY,784
IPython/testing/__pycache__/__init__.cpython-310.pyc,,
IPython/testing/__pycache__/decorators.cpython-310.pyc,,
IPython/testing/__pycache__/globalipapp.cpython-310.pyc,,
IPython/testing/__pycache__/ipunittest.cpython-310.pyc,,
IPython/testing/__pycache__/skipdoctest.cpython-310.pyc,,
IPython/testing/__pycache__/tools.cpython-310.pyc,,
IPython/testing/decorators.py,sha256=aOGYnPwCnAhjAE0uAtVTbrspTWrEYA-60jOwdp-s_oc,6777
IPython/testing/globalipapp.py,sha256=F-RD6xLFJ2R1jNPycA9zk116GfH8qTsgsYFECWCeDRY,3948
IPython/testing/ipunittest.py,sha256=lmCOgPk1HuQnfIM4bdYX-eC7X5NmwQON-Rx_PuVPOSI,6540
IPython/testing/plugin/README.txt,sha256=9Gkx2cWar5iNgkBoZY2HGvEvSEek3tj_MJIDbqNt5Ug,1043
IPython/testing/plugin/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/testing/plugin/__pycache__/__init__.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/dtexample.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/ipdoctest.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/pytest_ipdoctest.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/setup.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/simple.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/simplevars.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/test_ipdoctest.cpython-310.pyc,,
IPython/testing/plugin/__pycache__/test_refs.cpython-310.pyc,,
IPython/testing/plugin/dtexample.py,sha256=yedUX2aIUpFLkeDyshT5fqaODJg0iQzU_zvGOSr4JLc,2921
IPython/testing/plugin/ipdoctest.py,sha256=e7X2SCKNvJhiOFcN2LMMgzswIKNnO5emLdM8ZtcO5Qc,11881
IPython/testing/plugin/pytest_ipdoctest.py,sha256=hn5L3tovmiBkDADvSWYmmGDex80HVRSnWVQaNFSQLdE,29689
IPython/testing/plugin/setup.py,sha256=945a09Zm2HWWvukd5IVZ4v5p1czQPJfVlr5_Idey2AA,539
IPython/testing/plugin/simple.py,sha256=YjVhrNz_312zpW3C-U7FiyFG0A49FkE33A8kLXmcUf8,727
IPython/testing/plugin/simplevars.py,sha256=YZnDvFqQuFcrgzkgmm-koVRJKDnHCf0278Y5S_tyI3g,24
IPython/testing/plugin/test_combo.txt,sha256=rrXjdOlRh9DltFu3GpuWuD0Hojtj4QQcEBOm52Z3-dE,923
IPython/testing/plugin/test_example.txt,sha256=CGM8aZIYHlePDdAnR1yX3MfDGu0OceZpUiI_Y4tZGaU,730
IPython/testing/plugin/test_exampleip.txt,sha256=5gLcj8iCk-WCOGz0ObpQpuZMhGwS1jUMyH3mouGxQJI,814
IPython/testing/plugin/test_ipdoctest.py,sha256=Lc3qQdZ3amXf9EKA7JlXf30b3BzP8RwdNS9-SMRe2P0,1907
IPython/testing/plugin/test_refs.py,sha256=y-Y2Q8niRIbaanbwpIzvEwwaHkJfAq10HYfb4bAXHBc,715
IPython/testing/skipdoctest.py,sha256=haSEhd8EJr2Y0EbXXxv3pGvK6AQ8Lb7SyqkX5O8EU6s,717
IPython/testing/tests/__init__.py,sha256=J4sAH04HH8RQnex-9174r9l7O15gIPzYUBXCcn6oAC0,558
IPython/testing/tests/__pycache__/__init__.cpython-310.pyc,,
IPython/testing/tests/__pycache__/test_decorators.cpython-310.pyc,,
IPython/testing/tests/__pycache__/test_ipunittest.cpython-310.pyc,,
IPython/testing/tests/__pycache__/test_tools.cpython-310.pyc,,
IPython/testing/tests/test_decorators.py,sha256=xL30832viv2PxN-7yt7HVElc5vA1OOqWVZuXfqubtT8,3955
IPython/testing/tests/test_ipunittest.py,sha256=7-1iCczPW2mH7aqcZA6F_yGpV7EBVYz8s_3rI1mNqsc,3301
IPython/testing/tests/test_tools.py,sha256=3dtqZhP7KiUBaeK-TG7tuRbtpajTXi5BZ2j1JYhdr0M,4157
IPython/testing/tools.py,sha256=jg2ONzFzRsGq4ZyxcTkXs2eXVTjJic1GHK6Wn2q131g,14105
IPython/utils/PyColorize.py,sha256=VpRFScmCJxcMpk_mpcFTkeTqbfASBuHtfXt1ruFM7Ww,10875
IPython/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/utils/__pycache__/PyColorize.cpython-310.pyc,,
IPython/utils/__pycache__/__init__.cpython-310.pyc,,
IPython/utils/__pycache__/_process_cli.cpython-310.pyc,,
IPython/utils/__pycache__/_process_common.cpython-310.pyc,,
IPython/utils/__pycache__/_process_posix.cpython-310.pyc,,
IPython/utils/__pycache__/_process_win32.cpython-310.pyc,,
IPython/utils/__pycache__/_process_win32_controller.cpython-310.pyc,,
IPython/utils/__pycache__/_sysinfo.cpython-310.pyc,,
IPython/utils/__pycache__/capture.cpython-310.pyc,,
IPython/utils/__pycache__/colorable.cpython-310.pyc,,
IPython/utils/__pycache__/coloransi.cpython-310.pyc,,
IPython/utils/__pycache__/contexts.cpython-310.pyc,,
IPython/utils/__pycache__/daemonize.cpython-310.pyc,,
IPython/utils/__pycache__/data.cpython-310.pyc,,
IPython/utils/__pycache__/decorators.cpython-310.pyc,,
IPython/utils/__pycache__/dir2.cpython-310.pyc,,
IPython/utils/__pycache__/docs.cpython-310.pyc,,
IPython/utils/__pycache__/encoding.cpython-310.pyc,,
IPython/utils/__pycache__/eventful.cpython-310.pyc,,
IPython/utils/__pycache__/frame.cpython-310.pyc,,
IPython/utils/__pycache__/generics.cpython-310.pyc,,
IPython/utils/__pycache__/importstring.cpython-310.pyc,,
IPython/utils/__pycache__/io.cpython-310.pyc,,
IPython/utils/__pycache__/ipstruct.cpython-310.pyc,,
IPython/utils/__pycache__/jsonutil.cpython-310.pyc,,
IPython/utils/__pycache__/localinterfaces.cpython-310.pyc,,
IPython/utils/__pycache__/log.cpython-310.pyc,,
IPython/utils/__pycache__/module_paths.cpython-310.pyc,,
IPython/utils/__pycache__/openpy.cpython-310.pyc,,
IPython/utils/__pycache__/path.cpython-310.pyc,,
IPython/utils/__pycache__/process.cpython-310.pyc,,
IPython/utils/__pycache__/py3compat.cpython-310.pyc,,
IPython/utils/__pycache__/sentinel.cpython-310.pyc,,
IPython/utils/__pycache__/shimmodule.cpython-310.pyc,,
IPython/utils/__pycache__/signatures.cpython-310.pyc,,
IPython/utils/__pycache__/strdispatch.cpython-310.pyc,,
IPython/utils/__pycache__/sysinfo.cpython-310.pyc,,
IPython/utils/__pycache__/syspathcontext.cpython-310.pyc,,
IPython/utils/__pycache__/tempdir.cpython-310.pyc,,
IPython/utils/__pycache__/terminal.cpython-310.pyc,,
IPython/utils/__pycache__/text.cpython-310.pyc,,
IPython/utils/__pycache__/timing.cpython-310.pyc,,
IPython/utils/__pycache__/tokenutil.cpython-310.pyc,,
IPython/utils/__pycache__/traitlets.cpython-310.pyc,,
IPython/utils/__pycache__/tz.cpython-310.pyc,,
IPython/utils/__pycache__/ulinecache.cpython-310.pyc,,
IPython/utils/__pycache__/version.cpython-310.pyc,,
IPython/utils/__pycache__/wildcard.cpython-310.pyc,,
IPython/utils/_process_cli.py,sha256=4B5tCcHgR9_njzdrA069QP3xM78GRnfnbF_YchaVuyU,2020
IPython/utils/_process_common.py,sha256=5T2LhdNL2ba9rN1lsGLfrx1QV_y852xsBKvttb1gJ7g,7003
IPython/utils/_process_posix.py,sha256=DWUOgQz4imRJe80iT4mDc-RkX20hcmnrPDuInQYDvrY,8666
IPython/utils/_process_win32.py,sha256=FpNOzrjU0JlhCXNx3hlxJ_MeVQJhKQYq-9U_3D4VkEk,6132
IPython/utils/_process_win32_controller.py,sha256=KKqpJW6voLs7kp0A_wRfM03EcAI_iv5BJxglR011qLI,21329
IPython/utils/_sysinfo.py,sha256=tKJlsf5k4hQnyIPou0UximnbfaIE6lznpJ36xuCuW-g,45
IPython/utils/capture.py,sha256=seIkDR1LnmlsapRuvon5oiJhUc0pkUA-r_cCDHcH4AI,5161
IPython/utils/colorable.py,sha256=SCaeLJJlW14DZvoyaYPL-I6jLs78GA10xh0jCuSw8IY,786
IPython/utils/coloransi.py,sha256=25RGDkF_JYIz7nNkTaBch9rIqqyA3C7-2TWABP6cKpE,6972
IPython/utils/contexts.py,sha256=9fViIf4GWOp0-5sfUdjYwDSYdRnFTOMTGMTphqOpiO8,1619
IPython/utils/daemonize.py,sha256=91wAJwuFXA1FM8wGlbBJCPPF906jFJ-rdJOUp4b_5ZE,200
IPython/utils/data.py,sha256=36VVGY1b0JG7_zSdbVSy8IzLqM0uT-uB12TBYWgd1lI,1015
IPython/utils/decorators.py,sha256=qsYLskFlT2bB_Q-87ttBA56lAf-knWLOe5WiOwPdXFE,2680
IPython/utils/dir2.py,sha256=D8w5dlmaTLY6yyA9AEBDC1nqx4po4Ol6i2tGG9eXuO0,2232
IPython/utils/docs.py,sha256=QY8n0cFrTS6gePmT3Tppv9Tzgc3Pzlf33XuzAwiE_wE,86
IPython/utils/encoding.py,sha256=jcvinrdvt1o_5FmQFlfJciB3U_asdkYnG0KogaS5rUQ,2843
IPython/utils/eventful.py,sha256=idbcT7GyZ43hrX9e6cI2bz7kKr389yMBE9hos6amMzE,138
IPython/utils/frame.py,sha256=4w8h7PqTC63akE-vyQoTRgDWy3cS_TpblVrIizlWS6s,3048
IPython/utils/generics.py,sha256=vRjgikNsLi0iZUtTTMN-l7rcZBPOG2uc7dyYFRHLUgs,706
IPython/utils/importstring.py,sha256=aBtB6LJz7Tsa5kIsi9HprC7g2pTc-a0Jx2SwxkqxPqQ,1050
IPython/utils/io.py,sha256=QVse5--imYBpBDRdGjGLzg3214t2QeJ_d_-PLLlCs9o,4631
IPython/utils/ipstruct.py,sha256=XPfl1b0rRjISwyG3P1e2kJLSFHawzcPY71UBoa5v7qo,11856
IPython/utils/jsonutil.py,sha256=2QsAXueCIbbmjyz37Im9yXU41XK_XGJ9aGA7NGilDIs,148
IPython/utils/localinterfaces.py,sha256=7Wt6gcjgHX4mcWbx_k3Z1GL4iR388ZRqM3Hhhh4_Uwg,169
IPython/utils/log.py,sha256=3tYJLqZEa40zTX0tgKnpb-bvj4NhI64PijiRV6mnc78,123
IPython/utils/module_paths.py,sha256=lBlarunvhPIzQ0cUoZ6yIIYRYYIqMaaGTZ8IIK5A0IY,2327
IPython/utils/openpy.py,sha256=qnS9T2kfyVw5CLXvTkNivnbMzLZkaZ2v5SVJC2YCWVQ,3417
IPython/utils/path.py,sha256=vhIlIVAsdmkNQI9Y0YeO-kbKkYsVyjszudfp6g3_93o,11937
IPython/utils/process.py,sha256=Id0XV9-CzfUInpEWQJTcjJUePte4sQuaJ-pmjgPIxEA,1878
IPython/utils/py3compat.py,sha256=E3jOq-RL0Bx4eEszX2Rr2igOV8YoT1XHFFRr2CdWb08,1601
IPython/utils/sentinel.py,sha256=pRzWbnR7gjM5R3g0ZqjSo94QNJpxgKWOLXT-yY18FmU,421
IPython/utils/shimmodule.py,sha256=Sf0hohTA4a6zu7lxyKL_CFOSbMi4Y4D530lejwdaAXQ,2669
IPython/utils/signatures.py,sha256=dIrPwSPou7WQWs5018lzs3-dEKwG-dX3g4_2_OCvdg4,474
IPython/utils/strdispatch.py,sha256=LM8jEQo7G77PxpjusJ7EqDEQW4YHYpEyb7xnE6RrEaw,1838
IPython/utils/sysinfo.py,sha256=5lkCpPwJBosubtOYzc6m-hF9fl1mboblpjJMaESNkEE,4324
IPython/utils/syspathcontext.py,sha256=BEEEuRIjnDO63aDtCkmE631SoxbTvHewmtPQwviU00Y,1952
IPython/utils/tempdir.py,sha256=tHwshFbHXzS-QhDoJX61apoGmQ7k0HmPGvjUvqtZDps,1867
IPython/utils/terminal.py,sha256=W2Ii4DMjVAwxGsHlXmb5DJ0tO3tu7tfM9z-x40mIGmg,3206
IPython/utils/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/utils/tests/__pycache__/__init__.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_capture.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_decorators.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_deprecated.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_dir2.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_imports.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_importstring.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_io.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_module_paths.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_openpy.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_path.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_process.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_pycolorize.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_shimmodule.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_sysinfo.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_tempdir.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_text.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_tokenutil.cpython-310.pyc,,
IPython/utils/tests/__pycache__/test_wildcard.cpython-310.pyc,,
IPython/utils/tests/test_capture.py,sha256=VfRuGBA7n521oa_uIN9_c_Lmyo0Vm912wuLV9xtDlCE,5319
IPython/utils/tests/test_decorators.py,sha256=y_Hp5tjT8YbXe87HLe0-b5qhU9BcEzKMUdzAJdy-OWg,170
IPython/utils/tests/test_deprecated.py,sha256=5QUNHDR5re_BowvOQKvM4d7vrstS-IPIQB6YNjqw6_o,183
IPython/utils/tests/test_dir2.py,sha256=XDnqXd1kWF30KeInbzvAqotst7KgbxE-2nC3HbTJBNg,1446
IPython/utils/tests/test_imports.py,sha256=6SWziRNPdGfA47SpcjXDJG7B6173EHT6vT39juEfBd0,439
IPython/utils/tests/test_importstring.py,sha256=3ga_tyi0vkn-WbUylEI8EixFsB45NVBH8MR_-e8orWY,1213
IPython/utils/tests/test_io.py,sha256=WcQb7j_cCE_3Pv4SQI_Gtlyy6fFYYXRZfNXBHvngyCo,1410
IPython/utils/tests/test_module_paths.py,sha256=S4v2D8Lh9EQImTo8bXWNVxad21-i1-FEYGJs4r_ULlY,3272
IPython/utils/tests/test_openpy.py,sha256=M_IYXSZPabQQJl3SwXmZCwju4vezIHlcNT9OqRDOQ4w,1219
IPython/utils/tests/test_path.py,sha256=NzFbghnrUziB7a4-SP9F6wGILxvkgXTWPACTL_yq8ec,16104
IPython/utils/tests/test_process.py,sha256=RBEWt4efV9UJjO3JnihFuuA6ZFAJj4n4G5nckhOINiU,6134
IPython/utils/tests/test_pycolorize.py,sha256=T9qQuv3PoGAtUkOopqZYX398L9_i90wrHn6obe7EmN4,1761
IPython/utils/tests/test_shimmodule.py,sha256=JEFbjzpXdDE-QUzgitFpVtnb0ve5NBjaacgz1uil3Tg,387
IPython/utils/tests/test_sysinfo.py,sha256=omDs63QvyPXSlJT2jqDihBfVXPx5zLyhnG-25-2M6HE,464
IPython/utils/tests/test_tempdir.py,sha256=sf_g4uJBGELf7nGZsWQwQlN8-X2PVOv9j2_Zs8-P-oY,1108
IPython/utils/tests/test_text.py,sha256=woVkc3eflGX8ZpGjNThZsoV6MKXath8vyZKa09fmnGY,7114
IPython/utils/tests/test_tokenutil.py,sha256=7tXFsKesEBxe_5rkhU5msbkk_fRMRlCo0MwmAV6jH88,3918
IPython/utils/tests/test_wildcard.py,sha256=VJVK7LxaEYJKv6Tynlj4ye_GRyMHEI_3iT80i3PJuZk,4729
IPython/utils/text.py,sha256=0gfiAuVprZxvxr4L3hUsgpsZTHQZXeGGSgVsCW_8iuM,22794
IPython/utils/timing.py,sha256=5cdn0uxRYA6_PwTJMBYHl53liiYNW4p4f28Chasix9k,4238
IPython/utils/tokenutil.py,sha256=CH0FWKCt65FaEDyFrPY7zLe3IyvvOjiYTDNqfvL1ejg,4940
IPython/utils/traitlets.py,sha256=SnRNQ20hSnvceaWsTIjZRe2R90NAC58PVJl9bMt-41A,143
IPython/utils/tz.py,sha256=G6fZmfvj_0FjKSgZGuWGs1gKwU3uuHYgE3cmiZUwbNY,1380
IPython/utils/ulinecache.py,sha256=WySMSMJDRsm1Z5CGY24KdmoA2eLDZEayqWR6V_sIuik,684
IPython/utils/version.py,sha256=9SuuYeztj_lwbL_o-RLjfC4vJfel3HCde6NWY4KLt60,1223
IPython/utils/wildcard.py,sha256=6EEc3OEYp-IuSoidL6nwpaHg--GxnzbAJTmFiz77CNE,4612
ipython-8.15.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
ipython-8.15.0.dist-info/LICENSE,sha256=4OOQdI7UQKuJPKHxNaiKkgqvVAnbuQpbQnx1xeUSaPs,1720
ipython-8.15.0.dist-info/METADATA,sha256=I1BP-AalfkDTibsCx5xWTp8J5K4RypvGctVzi7kNzSo,5872
ipython-8.15.0.dist-info/RECORD,,
ipython-8.15.0.dist-info/WHEEL,sha256=yQN5g4mg4AybRjkgi-9yy4iQEFibGQmlz78Pik5Or-A,92
ipython-8.15.0.dist-info/entry_points.txt,sha256=1078eGaXVuhZyDuwlmp1pWDc3HtDrYO9CZoXGAcGCw8,244
ipython-8.15.0.dist-info/top_level.txt,sha256=PKjvHtNCBZ9EHTmd2mwJ1J_k3j0F6D1lTFzIcJFFPEU,8
