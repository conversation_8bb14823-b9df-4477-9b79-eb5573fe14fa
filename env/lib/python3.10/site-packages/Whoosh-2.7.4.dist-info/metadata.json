{"generator": "bdist_wheel (0.26.0)", "summary": "Fast, pure-Python full text indexing, search, and spell checking library.", "classifiers": ["Development Status :: 5 - Production/Stable", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: BSD License", "Natural Language :: English", "Operating System :: OS Independent", "Programming Language :: Python :: 2.5", "Programming Language :: Python :: 3", "Topic :: Software Development :: Libraries :: Python Modules", "Topic :: Text Processing :: Indexing"], "extensions": {"python.details": {"project_urls": {"Home": "http://bitbucket.org/mchaput/whoosh"}, "contacts": [{"email": "<EMAIL>", "name": "<PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}}}, "keywords": ["index", "search", "text", "spell"], "license": "Two-clause BSD license", "metadata_version": "2.0", "name": "Whoosh", "version": "2.7.4", "test_requires": [{"requires": ["pytest"]}]}