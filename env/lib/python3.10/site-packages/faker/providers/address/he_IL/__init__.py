from .. import Provider as AddressProvider


class Provider(AddressProvider):
    city_formats = ("{{city_name}}",)
    street_name_formats = ("{{street_title}}",)
    street_address_formats = ("{{street_name}} {{building_number}}",)
    address_formats = ("{{street_address}}, {{city}}, {{postcode}}",)
    postcode_formats = ("#######",)

    # Data sourced from data.gov.il
    # https://data.gov.il/dataset/321

    street_titles = (
        "אביב",
        "אביגיל",
        "אבן מסעוד",
        "אברבנאל",
        "אברהם ברזילי",
        "אגוז",
        "אדמון",
        "אהרון מאיר מזיא",
        "אהרונוביץ",
        "אולפן",
        "אורנים",
        "אזור בית הקברות",
        "אזור תעשיה א'",
        "אזור תעשיה הר יונה",
        "אזור תעשייה",
        "אזו<PERSON> תעשייה מזרח",
        'אח"י אילת',
        "אייזיק ניוטון",
        "איילת השחר )מ ק(",
        "אייר",
        "אילניה",
        "אימבר",
        "אירוס",
        "אירוס",
        "אל הודא סמ3",
        "אלוורוד",
        "אלול",
        "אלומה",
        "אלזאבוד",
        "אל-זהרא'",
        "אל זיתון סמ2",
        "אלזיתונה סמ7",
        "אל חגאג בן יוסף",
        "אל-חראיק סמ3",
        "אלחרש",
        "אל-ט'הרה סמ7",
        "אלישר",
        "אלכנסת",
        "אלכסנדר ינאי",
        "אלכרום",
        "אלכתאב",
        "אל-לימון",
        "אלמזדלפה",
        "אל-מחאג'ר סמ3",
        "אל-מחאג'ר סמ4",
        "אלמנשיה-מושירפה",
        "אל-מקפה סמ9",
        "אל-סביל סמ6",
        "אלסלילמה",
        "אלסריס",
        "אלעמשקה",
        "אלעקבה",
        "אל-פארוק סמ2",
        "אלפג'ר",
        "אלרשיד",
        "אלתין",
        "אלתרמן",
        "אסא המלך",
        "אפעל",
        "ארבל",
        "אשדוד",
        "אשל",
        "אתגר",
        "אתר חפץ חיים",
        "בועז",
        "בורסת היהלומים",
        "ביכורים",
        'ביל"ו',
        "בילינסון",
        "בית אבות",
        "בית היוצר",
        "בית יצחק-שער חפר",
        "בית ראשון במולדת",
        "בן יהודה",
        "בן ישי",
        "בן לברט",
        "בן צבי יצחק",
        "בן צבי יצחק",
        "בן צבי שמעון",
        "בקעת הירח",
        "ברגמן אליעזר",
        "ברוריה",
        "ברזיל",
        "ברקת",
        "בשמת",
        "בשמת",
        "גבע",
        "גבע",
        "גבעת חיים )מאוחד(",
        "גובר רבקה",
        "גוטמכר",
        "גולדה מאיר",
        "ג'ו עמר",
        "גיבתון חנוך",
        "גינוסר",
        "גפן",
        "גפן",
        "גרטרוד קראוס",
        "גרינבוים",
        "דבורה",
        "דודו דותן",
        "דולב",
        "דולצ'ין אריה",
        "דחי",
        "דיה",
        "דימיטר פשב",
        "דרב אלברג'",
        "דרומית-מג'ד אלכרום",
        "דריפוס",
        "דרך הארץ",
        "דרך הגן",
        "דרך חברון",
        "דרך חלמית",
        "דרך שועפאט סמ4",
        "האדמו\"ר מויז'ניץ",
        "האודם",
        "האורן",
        "האורנים",
        "האחים בז'רנו",
        "האילן",
        "האילנות",
        "האילתית",
        "האלונים",
        "האמוראים",
        "האצטדיון",
        'האצ"ל',
        "הברדלס",
        "הברוש",
        "הבריגדה",
        "הגבורה",
        "הגפן",
        "הגפן",
        "הדגניות",
        "הדולב",
        "הדייגים",
        "הדרך האמריקאית סמ12",
        "ההגנה",
        "ההגנה",
        "הולצברג שמחה",
        "הופרט יעקב",
        "הורדים",
        "הורקנוס יוחנן",
        "הזיתים",
        "הזמיר",
        "החבל",
        "החותרים",
        "החלוצים",
        "החליל",
        "החמנית",
        "החסידה",
        "החצב",
        "החצב",
        "החרוב",
        "החרובים",
        "החרמון",
        "החשמל",
        "היוזם",
        "הינשוף",
        "היקינטון",
        'הל"ה',
        "המאה ואחד",
        "המבריא",
        "המברק",
        "המגינים",
        "המגינים",
        "המורד",
        "המייסדים",
        "המלאכה",
        "המלאכה",
        "המלכים",
        "הממונה",
        "המנוע",
        "המסגר",
        "המעיין",
        "המפרש",
        "המצודה",
        "המרגנית",
        "המשור",
        "הנוטר",
        "הנורית",
        "הנורית",
        "הנקר",
        "הנרד",
        "הסיגלית",
        "הסיפון",
        "העבודה",
        "העבודה",
        "העצמון",
        "הפעמון",
        "הפרדס",
        "הפרדס",
        "הפרדס",
        "הפרדס",
        "הצאלון",
        "הצבעוני",
        "הקישון",
        "הראשונים",
        "הרב בידאני עובדיה",
        "הרב וולף",
        "הרב חכם שמעון",
        "הרבי מליובאוויטש",
        "הרב ניסים",
        "הרב עוזיאל",
        "הרב רפאל עבו",
        "הרדוף",
        "הרדוף",
        "הרדוף",
        "הרותם",
        "הרי גולן",
        "הר יהל",
        "הרימון",
        "הר כנען",
        "הרליץ יוסף",
        "הר סיני",
        "הר עצמון",
        "הר צרור",
        "הרקפת",
        "הרשקו אברהם",
        "הרשת",
        "השדות",
        "השחר",
        "השיזף",
        "השיח",
        "השיטה",
        "השעורה",
        "השר ברזילי",
        "התאנה",
        "התבור",
        "התקוה",
        "ויקטור ויוליוס",
        "וערת סעד",
        "ז'בוטינסקי",
        "זגגי",
        "זיגורד",
        "זיו",
        "ז'ילבר",
        "זית",
        "זכרון יעקב",
        "חוחית",
        "חוף הים",
        "חושן",
        "חזון איש",
        "חזן יעקב",
        "חיטה",
        "חיים וייצמן",
        "חלמיש",
        "חצב",
        "חרת א בוס",
        "חתוכה יורם",
        "טאבליא",
        "טאחונת אלראהיב",
        "טביב",
        "טופז",
        'י"א באדר',
        "יאפא",
        "יד העפלה ממרוקו",
        "ידידה",
        "יהודה הלוי",
        "יהודה המכבי",
        "יהודה המכבי",
        "יואב",
        "יונה",
        "יזרעאל",
        "יחזקאל הנביא",
        "יכין",
        "ירושלים",
        "ירקון",
        "ישועת דוד",
        "יששכר",
        "כאבול",
        "כהן אלי",
        "כהנא",
        "כוכב הצפון",
        "כזיב",
        "כיסופים",
        "ככר ירדן",
        "ככר נחשון",
        "כנרת",
        "כפר ילדים נרדים",
        "כרם חמד",
        "לב הקריה",
        "לביא אריק",
        "לבקוביץ",
        "לוד הצעירה",
        "לוטם",
        "לוין מיכאל וחנה",
        "לוין שמריהו",
        "לוריא",
        'לח"י',
        "לילינבלום",
        "לכיש",
        "לסקוב חיים",
        "מבוא הדס",
        "מבוא הזיתים",
        "מבוא חיים מקובנה",
        "מבוא חמה",
        "מבצע הראל",
        "מבצע חירם",
        "מבצע עובדה",
        "מגלן",
        "מוסיוף שלמה",
        "מופק דיאב",
        "מוצא",
        "מורדי הגטאות",
        "מורן",
        "מזל שור",
        "מזרחי יוסף",
        "מיכה",
        "מירון",
        "מישאל",
        "מלון רויאל פארק",
        "מנזר המארונים",
        "מעבר לים",
        "מעוז חיים",
        "מעונות ים",
        "מעלה כגן הלנה",
        "מענית",
        "מצדה",
        "מצפה גילה",
        "מרגיל מחוור",
        "מרווה",
        "מרחביה )מושב(",
        "מרכז",
        "משה דיין",
        "משואות יצחק",
        "משעול אבוקדו",
        "משעול האלה",
        "משעול המחתרות",
        "משעול הסיפן",
        "משעול הצופית",
        "משעול התפוח",
        "משעול מוריה",
        "משעול נקר",
        "משעול פארן",
        "נאות אביבים",
        "נאות אשכול",
        "נאות הדקל",
        "נדב יצחק",
        "נהריה",
        "נוה עוז",
        "נוף כנרת",
        "נורית",
        "נחל נחשון",
        "נחל סרפד",
        'נחל ערוגות מ"ר',
        "נחל פארן",
        "נחלת צדוק",
        "ניר עם",
        'נעמ"ת',
        "נצרת עילית",
        "נשר",
        "נתיב הפורצים",
        "נתן",
        "סביונים מכבים רעות",
        "סומך עובדיה",
        "סיתוונית",
        "סלא איירין",
        "סלעית",
        "סמ 20 20",
        "סמבורסקי דניאל",
        "סמ בני ברית",
        "סמ הבוסתן",
        "סמ הרכבת",
        "סמ השחף",
        "סמטת השחר",
        "סמ מאלה",
        "סמ מסילה א",
        "סמ עין גנים",
        "סמ עינב",
        "סמ שפיפון",
        "סנט הלנה",
        "עבד אל-גני",
        "עגור",
        "ע הלל",
        "עובדי הנמל",
        "עוגן",
        "עולש מצוי",
        "עומר",
        "עידו הנביא",
        "עין שביב",
        "עירית",
        "עמוס",
        "עמוס הנביא",
        "עמנואל )רינגלבלום(",
        "ענזה",
        "עפולה",
        "עקבת א תות",
        "פדויים",
        "פטדה",
        "פנינה",
        "פקוד מרכז",
        "פרומקין גד",
        "פרופ' בירק יהודית",
        "פרופס",
        "פרי חדש",
        "צדוק הכהן",
        "צובה",
        "צופית",
        "צוקית",
        "צור",
        "צמחי היהודים",
        "צפרירים",
        "צפת",
        "צפת",
        "קבועה )שבט(",
        "קדמת צבי",
        "קישון אפרים",
        "קנין הארץ",
        "קרית עקרון",
        "קרל נטר",
        "קרן היסוד",
        "רביבים",
        "רבנו תם",
        "רבקה אמנו",
        "רח 101",
        "רח 1043",
        "רח 1060",
        "רח 12",
        "רח 1238",
        "רח 124",
        "רח 135",
        "רח 14",
        "רח 16",
        "רח 16",
        "רח 2001",
        "רח 2306",
        "רח 5041",
        "רח 6020",
        "רח 6073",
        "רח 6087",
        "רח 68",
        "רח 7035",
        "רח 7038",
        "רח 7069",
        "רח 71",
        "רחבת פנינה",
        "רח ה",
        "רח מו כ שלם",
        "רח רז",
        "ריחאניה",
        'רלב"ג',
        'רמב"ם',
        'רמב"ן',
        "רמת האירוסים",
        "רמת כרמים",
        "רקפת",
        'רש"י',
        "ש אסבסטונים",
        "ש אסבסט צפון",
        "שאר ישוב",
        "ש בבלי",
        "שבזי",
        "שבזי",
        "שבטי ישראל",
        "שבט ראובן",
        "שביל הרקפות",
        "שביל קליפות התפוזים",
        "שד גאולים",
        "שד גת",
        "שד העצמאות",
        'שד ח"ן',
        "שד יוספטל גיורא",
        "ש הפועלים",
        "שוהם",
        "שומרון",
        "שושנה דמארי",
        "שושנת הכרמל",
        'שז"ר זלמן',
        "שיזף",
        "שכ 14",
        "שכ החלוצים",
        "שכ היובל",
        "שכ הפועל המזרחי ג'",
        "שכ הרכבת",
        "שכ זאב",
        "שכ חפצי בה",
        "שכ מחניים",
        "שכ נווה הדקל",
        "שכ עראק אלשבאב",
        "שכ קחאוש",
        "שכ רסקו",
        "שלדג",
        "שמחוני",
        "שמחוני אסף",
        "שמעון המכבי",
        "שני",
        "ש סלע חדש",
        "ש פועלים",
        'ש"ץ גרשון',
        "ש ציונים כלליים",
        "שקד",
        "ש קואפרטיבים",
        "שריג",
        "ש רמת אביב",
        "תאנה",
        "תל חי",
        "תפארת ישראל",
        'תרס"ח',
        'תרצ"ו',
    )

    city_names = (
        "אבו רובייעה )שבט(",
        "אביבים",
        "אביחיל",
        "אודם",
        "אור הנר",
        "אורטל",
        "אטרש )שבט(",
        "אליקים",
        "אל סייד",
        "באר מילכה",
        "בית ברל",
        "בית הלוי",
        "בית חנן",
        "בית חנניה",
        "בית חשמונאי",
        "בני ציון",
        "ברקאי",
        "ברקת",
        "גבעת השלושה",
        'גבעת ח"ן',
        'גבעת כ"ח',
        "גדות",
        "גונן",
        "גינתון",
        "גיתית",
        "גן שורק",
        "גנות הדר",
        "גני מודיעין",
        "גרופית",
        'דוב"ב',
        "דולב",
        "האון",
        "הסוללים",
        "העוגן",
        "הר אדר",
        "ורד יריחו",
        "זוהר",
        "חיננית",
        "חצור-אשדוד",
        "חצור הגלילית",
        "חשמונאים",
        "טל-אל",
        'יד רמב"ם',
        "כסלון",
        "כפר אחים",
        "כפר הנוער הדתי",
        "כפר יונה",
        "כפר מסריק",
        "כפר סירקין",
        "לוזית",
        "לקיה",
        "מגאר",
        "מגן",
        "מזכרת בתיה",
        "מירון",
        "מכמורת",
        "מלאה",
        "מסד",
        "מעונה",
        "מרחביה )מושב(",
        "משמר העמק",
        "נווה חריף",
        "נוקדים",
        "נורדיה",
        "נחלה",
        "נטע",
        "נירן",
        "נתיב השיירה",
        "סגולה",
        "סער",
        "עדי",
        "עזר",
        "עין אל-אסד",
        "עין השופט",
        "עין צורים",
        "עלי זהב",
        "עמוקה",
        "עמיר",
        "עמקה",
        "עספיא",
        "עצמון שגב",
        "פוריה - נווה עובד",
        "פוריידיס",
        "פקיעין חדשה",
        "צורית",
        "צפרירים",
        "רגבה",
        "רחוב",
        "ריינה",
        "רימונים",
        "רמות מנשה",
        "שדה אליהו",
        "שדות מיכה",
        "שדי תרומות",
        "שומרה",
        "שיטים",
        "שעב",
        "שפר",
        "שתולים",
        "תלמי אליהו",
    )

    def city_name(self) -> str:
        return self.random_element(self.city_names)

    def street_title(self) -> str:
        return self.random_element(self.street_titles)
