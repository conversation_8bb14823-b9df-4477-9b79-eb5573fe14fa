from .. import Provider as PersonProvider


class Provider(PersonProvider):
    formats = (
        "{{first_name}} {{last_name}}",
        "{{first_name}} {{last_name}}",
        "{{last_name}}, {{first_name}}",
    )

    first_names = (
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>š<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "Artis",
        "Arturs",
        "Artūrs",
        "<PERSON>r<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "Bērends",
        "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON>ā<PERSON>",
        "Dzin<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON><PERSON>",
        "Ēriks",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "Ēvalds",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "Ģederts",
        "Ģirts",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "Indriķis",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "Krišjānis",
        "Krišs",
        "Laimonis",
        "Lauris",
        "Leons",
        "Macs",
        "Mareks",
        "Māris",
        "Mārtiņš",
        "Matīss",
        "Mihels",
        "Mikels",
        "Miķelis",
        "Modris",
        "Nikolajs",
        "Niks",
        "Normunds",
        "Oļģerts",
        "Oskars",
        "Osvalds",
        "Oto",
        "Pauls",
        "Pēteris",
        "Raimonds",
        "Raivis",
        "Reinis",
        "Ričards",
        "Rihards",
        "Roberts",
        "Rolands",
        "Rūdolfs",
        "Sandis",
        "Staņislavs",
        "Tenis",
        "Teodors",
        "Toms",
        "Uldis",
        "Valdis",
        "Viesturs",
        "Viktors",
        "Vilis",
        "Vilnis",
        "Viļums",
        "Visvaldis",
        "Vladislavs",
        "Voldemārs",
        "Ziedonis",
        "Žanis",
        "Agnese",
        "Aiga",
        "Aija",
        "Aina",
        "Alīda",
        "Alise",
        "Alma",
        "Alvīne",
        "Amālija",
        "Anete",
        "Anita",
        "Anna",
        "Annija",
        "Antoņina",
        "Antra",
        "Ārija",
        "Ausma",
        "Austra",
        "Baba",
        "Baiba",
        "Berta",
        "Biruta",
        "Broņislava",
        "Dace",
        "Daiga",
        "Daina",
        "Dārta",
        "Diāna",
        "Doroteja",
        "Dzidra",
        "Dzintra",
        "Eda",
        "Edīte",
        "Elīna",
        "Elita",
        "Elizabete",
        "Elvīra",
        "Elza",
        "Emīlija",
        "Emma",
        "Ērika",
        "Erna",
        "Eva",
        "Evija",
        "Evita",
        "Gaida",
        "Genovefa",
        "Grēta",
        "Grieta",
        "Gunita",
        "Gunta",
        "Helēna",
        "Ieva",
        "Ilga",
        "Ilona",
        "Ilze",
        "Ina",
        "Ināra",
        "Indra",
        "Inese",
        "Ineta",
        "Inga",
        "Ingrīda",
        "Inguna",
        "Inta",
        "Irēna",
        "Irma",
        "Iveta",
        "Jana",
        "Janina",
        "Jūle",
        "Jūla",
        "Jūlija",
        "Karina",
        "Karlīna",
        "Katarīna",
        "Katrīna",
        "Krista",
        "Kristiāna",
        "Laila",
        "Laura",
        "Lavīze",
        "Leontīne",
        "Lība",
        "Lidija",
        "Liene",
        "Līga",
        "Ligita",
        "Lilija",
        "Lilita",
        "Līna",
        "Linda",
        "Līza",
        "Lizete",
        "Lūcija",
        "Madara",
        "Made",
        "Maija",
        "Māra",
        "Mare",
        "Margareta",
        "Margrieta",
        "Marija",
        "Mārīte",
        "Marta",
        "Maža",
        "Milda",
        "Minna",
        "Mirdza",
        "Monika",
        "Natālija",
        "Olga",
        "Otīlija",
        "Paula",
        "Paulīna",
        "Rasma",
        "Regīna",
        "Rita",
        "Rudīte",
        "Ruta",
        "Rute",
        "Samanta",
        "Sandra",
        "Sanita",
        "Santa",
        "Sapa",
        "Sarmīte",
        "Silvija",
        "Sintija",
        "Skaidrīte",
        "Solvita",
        "Tekla",
        "Trīne",
        "Valda",
        "Valentīna",
        "Valija",
        "Velta",
        "Veneranda",
        "Vera",
        "Veronika",
        "Vija",
        "Vilma",
        "Vineta",
        "Vita",
        "Zane",
        "Zelma",
        "Zenta",
        "Zigrīda",
    )

    last_names = (
        "Ābele",
        "Āboliņš",
        "Ābols",
        "Alksnis",
        "Apinis",
        "Apsītis",
        "Auniņš",
        "Auziņš",
        "Avotiņš",
        "Balodis",
        "Baltiņš",
        "Bērziņš",
        "Birznieks",
        "Bite",
        "Briedis",
        "Caune",
        "Celmiņš",
        "Celms",
        "Cīrulis",
        "Dzenis",
        "Dūmiņš",
        "Eglītis",
        "Jaunzems",
        "Kalējs",
        "Kalniņš",
        "Kaņeps",
        "Kārkliņš",
        "Kauliņš",
        "Kļaviņš",
        "Krastiņš",
        "Krēsliņš",
        "Krieviņš",
        "Krievs",
        "Krūmiņš",
        "Krūze",
        "Kundziņš",
        "Lācis",
        "Lagzdiņš",
        "Lapsa",
        "Līcis",
        "Liepa",
        "Liepiņš",
        "Lukstiņš",
        "Lūsis",
        "Paegle",
        "Pērkons",
        "Podnieks",
        "Polis",
        "Priede",
        "Priedītis",
        "Puriņš",
        "Purmals",
        "Riekstiņš",
        "Roze",
        "Rozītis",
        "Rubenis",
        "Rudzītis",
        "Saulītis",
        "Siliņš",
        "Skuja",
        "Skujiņš",
        "Sproģis",
        "Strazdiņš",
        "Turiņš",
        "Vanags",
        "Vīksna",
        "Vilciņš",
        "Vilks",
        "Vītoliņš",
        "Vītols",
        "Zaķis",
        "Zālītis",
        "Zariņš",
        "Zeltiņš",
        "Ziemelis",
        "Zirnis",
        "Zvaigzne",
        "Zvirbulis",
    )
