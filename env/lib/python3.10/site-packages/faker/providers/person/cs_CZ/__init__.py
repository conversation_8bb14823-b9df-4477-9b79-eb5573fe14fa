from collections import OrderedDict

from .. import Provider as PersonProvider


class Provider(PersonProvider):
    formats_female = OrderedDict(
        (
            ("{{first_name_female}} {{last_name_female}}", 0.97),
            ("{{prefix_female}} {{first_name_female}} {{last_name_female}}", 0.015),
            ("{{first_name_female}} {{last_name_female}} {{suffix}}", 0.02),
            (
                "{{prefix_female}} {{first_name_female}} {{last_name_female}} {{suffix}}",
                0.005,
            ),
        )
    )

    formats_male = OrderedDict(
        (
            ("{{first_name_male}} {{last_name_male}}", 0.97),
            ("{{prefix_male}} {{first_name_male}} {{last_name_male}}", 0.015),
            ("{{first_name_male}} {{last_name_male}} {{suffix}}", 0.02),
            (
                "{{prefix_male}} {{first_name_male}} {{last_name_male}} {{suffix}}",
                0.005,
            ),
        )
    )

    formats = formats_male.copy()
    formats.update(formats_female)

    first_names_male = (
        "<PERSON>",
        "<PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON>aroslav",
        "Jindřich",
        "Jiří",
        "Josef",
        "Jozef",
        "Ján",
        "Kamil",
        "Karel",
        "Kryštof",
        "Ladislav",
        "Leoš",
        "Libor",
        "Lubomír",
        "Luboš",
        "Ludvík",
        "Luděk",
        "Lukáš",
        "Marcel",
        "Marek",
        "Marian",
        "Martin",
        "Matyáš",
        "Matěj",
        "Michael",
        "Michal",
        "Milan",
        "Miloslav",
        "Miloš",
        "Miroslav",
        "Oldřich",
        "Ondřej",
        "Otakar",
        "Patrik",
        "Pavel",
        "Peter",
        "Petr",
        "Přemysl",
        "Radek",
        "Radim",
        "Radomír",
        "Radovan",
        "René",
        "Richard",
        "Robert",
        "Robin",
        "Roman",
        "Rostislav",
        "Rudolf",
        "Samuel",
        "Stanislav",
        "Tadeáš",
        "Tomáš",
        "Vasyl",
        "Viktor",
        "Vilém",
        "Vladimír",
        "Vladislav",
        "Vlastimil",
        "Vojtěch",
        "Vratislav",
        "Václav",
        "Vít",
        "Vítězslav",
        "Zbyněk",
        "Zdeněk",
        "Šimon",
        "Štefan",
        "Štěpán",
    )

    first_names_female = (
        "Adéla",
        "Alena",
        "Alexandra",
        "Alice",
        "Alžběta",
        "Andrea",
        "Aneta",
        "Anežka",
        "Anna",
        "Barbora",
        "Blanka",
        "Blažena",
        "Bohumila",
        "Božena",
        "Dagmar",
        "Dana",
        "Daniela",
        "Danuše",
        "Denisa",
        "Dominika",
        "Drahomíra",
        "Eliška",
        "Emilie",
        "Eva",
        "Františka",
        "Gabriela",
        "Hana",
        "Helena",
        "Ilona",
        "Irena",
        "Iva",
        "Ivana",
        "Iveta",
        "Jana",
        "Jarmila",
        "Jaroslava",
        "Jindřiška",
        "Jitka",
        "Jiřina",
        "Julie",
        "Kamila",
        "Karolína",
        "Kateřina",
        "Klára",
        "Kristina",
        "Kristýna",
        "Květa",
        "Květoslava",
        "Ladislava",
        "Lenka",
        "Libuše",
        "Lucie",
        "Ludmila",
        "Magdalena",
        "Magdaléna",
        "Marcela",
        "Marie",
        "Markéta",
        "Marta",
        "Martina",
        "Michaela",
        "Milada",
        "Milena",
        "Miloslava",
        "Miluše",
        "Miroslava",
        "Monika",
        "Mária",
        "Naděžda",
        "Natálie",
        "Nela",
        "Nikol",
        "Nikola",
        "Olga",
        "Pavla",
        "Pavlína",
        "Petra",
        "Radka",
        "Renata",
        "Renáta",
        "Romana",
        "Růžena",
        "Sabina",
        "Simona",
        "Soňa",
        "Stanislava",
        "Sára",
        "Tereza",
        "Vendula",
        "Veronika",
        "Viktorie",
        "Vladimíra",
        "Vlasta",
        "Věra",
        "Zdenka",
        "Zdeňka",
        "Zuzana",
        "Štěpánka",
        "Šárka",
        "Žaneta",
    )

    first_names = first_names_male + first_names_female

    last_names_male = (
        "Bartoš",
        "Beneš",
        "Blažek",
        "Bláha",
        "Doležal",
        "Dušek",
        "Dvořák",
        "Fiala",
        "Holub",
        "Horák",
        "Hájek",
        "Jelínek",
        "Kadlec",
        "Kolář",
        "Kopecký",
        "Kratochvíl",
        "Krejčí",
        "Král",
        "Kučera",
        "Kříž",
        "Malý",
        "Marek",
        "Mareš",
        "Mašek",
        "Moravec",
        "Novotný",
        "Novák",
        "Němec",
        "Pokorný",
        "Polák",
        "Pospíšil",
        "Procházka",
        "Růžička",
        "Sedláček",
        "Soukup",
        "Svoboda",
        "Urban",
        "Vaněk",
        "Veselý",
        "Vlček",
        "Zeman",
        "Čermák",
        "Černý",
        "Říha",
        "Šimek",
        "Štěpánek",
        "Šťastný",
    )

    last_names_female = (
        "Bartošová",
        "Benešová",
        "Beranová",
        "Blažková",
        "Bláhová",
        "Doležalová",
        "Dušková",
        "Dvořáková",
        "Fialová",
        "Holubová",
        "Horáková",
        "Hájková",
        "Jandová",
        "Jelínková",
        "Kadlecová",
        "Kolářová",
        "Kopecká",
        "Kratochvílová",
        "Krejčová",
        "Králová",
        "Kučerová",
        "Křížová",
        "Machová",
        "Malá",
        "Marešová",
        "Marková",
        "Mašková",
        "Moravcová",
        "Novotná",
        "Nováková",
        "Němcová",
        "Pokorná",
        "Poláková",
        "Pospíšilová",
        "Procházková",
        "Růžičková",
        "Sedláčková",
        "Soukupová",
        "Svobodová",
        "Tichá",
        "Urbanová",
        "Vacková",
        "Vaňková",
        "Veselá",
        "Vlčková",
        "Vávrová",
        "Zemanová",
        "Čermáková",
        "Černá",
        "Říhová",
        "Šimková",
        "Štěpánková",
        "Šťastná",
    )

    last_names = last_names_male + last_names_female

    degrees = ("JUDr.", "Ing.", "Bc.", "Mgr.", "MUDr.", "RNDr.")

    prefixes_male = ("pan",) + degrees

    prefixes_female = ("paní", "slečna") + degrees

    suffixes = ("CSc.", "DiS.", "Ph.D.", "Th.D.")
