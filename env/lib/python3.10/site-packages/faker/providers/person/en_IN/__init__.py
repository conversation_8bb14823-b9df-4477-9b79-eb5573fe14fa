from .. import Provider as PersonProvider


class Provider(PersonProvider):
    formats = ("{{first_name}} {{last_name}}",)

    # First names are from
    #   https://www.babycenter.in/a25010193/modern-indian-baby-names
    #   https://en.wikipedia.org/wiki/Category:Male_actors_in_Malayalam_cinema (not used exhaustively)
    #   https://en.wikipedia.org/wiki/List_of_Tamil_film_actors (not used exhaustively)
    # Last names are from https://www.familyeducation.com/baby-names/browse-origin/surname/indian

    first_names = (
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "Manikya",
        "Mehul",
        "Miraan",
        "Nakul",
        "Nirvaan",
        "Nishith",
        "Ojas",
        "Onkar",
        "Pranay",
        "Prerak",
        "Priyansh",
        "Purab",
        "Raghav",
        "Ranbir",
        "Raunak",
        "Reyansh",
        "Riaan",
        "Ritvik",
        "Rohan",
        "Romil",
        "Ryan",
        "Sahil",
        "Saksham",
        "Samar",
        "Samarth",
        "Shaan",
        "Shalv",
        "Shamik",
        "Shayak",
        "Shlok",
        "Shray",
        "Stuvan",
        "Sumer",
        "Taimur",
        "Taran",
        "Tejas",
        "Tushar",
        "Umang",
        "Uthkarsh",
        "Vaibhav",
        "Veer",
        "Vidur",
        "Vihaan",
        "Vivaan",
        "Yakshit",
        "Yuvaan",
        "Yuvraj ",
        "Zain",
        "Zeeshan",
        "Aaina",
        "Aarna",
        "Aaryahi",
        "Adah",
        "Adira",
        "Advika",
        "Ahana ",
        "Alia",
        "Alisha",
        "Amani",
        "Amira",
        "Anahi",
        "Anahita",
        "Anaya",
        "Anika",
        "Anvi",
        "Anya",
        "Aradhya",
        "Ayesha",
        "Bhamini",
        "Charvi",
        "Damini",
        "Dishani",
        "Diya",
        "Drishya",
        "Ela",
        "Elakshi",
        "Eshani",
        "Eva",
        "Hazel",
        "Heer",
        "Hrishita",
        "Inaaya ",
        "Ira",
        "Ishita",
        "Ivana",
        "Jhanvi",
        "Jivika",
        "Jiya",
        "Kaira",
        "Kashvi",
        "Kavya",
        "Keya",
        "Khushi",
        "Kiara",
        "Kimaya",
        "Kismat",
        "Lavanya",
        "Mahika",
        "Manjari",
        "Mannat",
        "Miraya",
        "Misha",
        "Mishti",
        "Mohanlal",
        "Myra",
        "Navya",
        "Nayantara",
        "Neelofar",
        "Nehmat",
        "Neysa",
        "Nirvi",
        "Nitara",
        "Nitya",
        "Oorja",
        "Pari",
        "Parinaaz",
        "Pihu",
        "Piya",
        "Prisha",
        "Rania",
        "Rasha",
        "Rati",
        "Renee",
        "Rhea",
        "Riya",
        "Saanvi",
        "Saira",
        "Samaira",
        "Samiha",
        "Sana",
        "Sara",
        "Seher",
        "Shanaya",
        "Siya",
        "Suhana",
        "Tanya",
        "Tara",
        "Tarini",
        "Tiya",
        "Trisha",
        "Urvi",
        "Vanya",
        "Vardaniya",
        "Vedika",
        "Vritika",
        "Yashvi",
        "Yasmin",
        "Zaina",
        "Zara",
        "Zoya",
    )

    last_names = (
        "Acharya",
        "Agarwal",
        "Agate",
        "Aggarwal",
        "Agrawal",
        "Ahluwalia",
        "Ahuja",
        "Amble",
        "Anand",
        "Andra",
        "Anne",
        "Apte",
        "Arora",
        "Arya",
        "Atwal",
        "Aurora",
        "Babu",
        "Badal",
        "Badami",
        "Bahl",
        "Bahri",
        "Bail",
        "Bains",
        "Bajaj",
        "Bajwa",
        "Bakshi",
        "Bal",
        "Bala",
        "Bala",
        "Balakrishnan",
        "Balan",
        "Balasubramanian",
        "Balay",
        "Bali",
        "Bandi",
        "Banerjee",
        "Banik",
        "Bansal",
        "Barad",
        "Barad",
        "Baral",
        "Baria",
        "Barman",
        "Basak",
        "Bassi",
        "Basu",
        "Bath",
        "Batra",
        "Batta",
        "Bava",
        "Bawa",
        "Bedi",
        "Behl",
        "Ben",
        "Bera",
        "Bhagat",
        "Bhakta",
        "Bhalla",
        "Bhandari",
        "Bhardwaj",
        "Bhargava",
        "Bhasin",
        "Bhat",
        "Bhatia",
        "Bhatnagar",
        "Bhatt",
        "Bhattacharyya",
        "Bhatti",
        "Bhavsar",
        "Bir",
        "Biswas",
        "Boase",
        "Bobal",
        "Bora",
        "Bora",
        "Borah",
        "Borde",
        "Borra",
        "Bose",
        "Brahmbhatt",
        "Brar",
        "Buch",
        "Buch",
        "Bumb",
        "Butala",
        "Chacko",
        "Chad",
        "Chada",
        "Chadha",
        "Chahal",
        "Chakrabarti",
        "Chakraborty",
        "Chana",
        "Chand",
        "Chanda",
        "Chander",
        "Chandra",
        "Chandran",
        "Char",
        "Chatterjee",
        "Chaudhari",
        "Chaudhary",
        "Chaudhry",
        "Chaudhuri",
        "Chaudry",
        "Chauhan",
        "Chawla",
        "Cheema",
        "Cherian",
        "Chhabra",
        "Chokshi",
        "Chopra",
        "Choudhary",
        "Choudhry",
        "Choudhury",
        "Chowdhury",
        "Comar",
        "Contractor",
        "D’Alia",
        "Dada",
        "Dalal",
        "Dani",
        "Dar",
        "Dara",
        "Dara",
        "Das",
        "Dasgupta",
        "Dash",
        "Dass",
        "Date",
        "Datta",
        "Dave",
        "Dayal",
        "De",
        "Deep",
        "Deo",
        "Deol",
        "Desai",
        "Deshmukh",
        "Deshpande",
        "Devan",
        "Devi",
        "Dewan",
        "Dey",
        "Dhaliwal",
        "Dhar",
        "Dhar",
        "Dhawan",
        "Dhillon",
        "Dhingra",
        "Din",
        "Divan",
        "Dixit",
        "Doctor",
        "Dora",
        "Doshi",
        "Dua",
        "Dube",
        "Dubey",
        "Dugal",
        "Dugar",
        "Dugar",
        "Dutt",
        "Dutta",
        "Dyal",
        "Edwin",
        "Gaba",
        "Gade",
        "Gala",
        "Gandhi",
        "Ganesan",
        "Ganesh",
        "Ganguly",
        "Gara",
        "Garde",
        "Garg",
        "Gera",
        "Ghose",
        "Ghosh",
        "Gill",
        "Goda",
        "Goel",
        "Gokhale",
        "Gola",
        "Gole",
        "Golla",
        "Gopal",
        "Goswami",
        "Gour",
        "Goyal",
        "Grewal",
        "Grover",
        "Guha",
        "Gulati",
        "Gupta",
        "Halder",
        "Handa",
        "Hans",
        "Hari",
        "Hayer",
        "Hayre",
        "Hegde",
        "Hora",
        "Issac",
        "Iyengar",
        "Iyer",
        "Jaggi",
        "Jain",
        "Jani",
        "Jayaraman",
        "Jha",
        "Jhaveri",
        "Johal",
        "Joshi",
        "Kadakia",
        "Kade",
        "Kakar",
        "Kala",
        "Kala",
        "Kala",
        "Kale",
        "Kalita",
        "Kalla",
        "Kamdar",
        "Kanda",
        "Kannan",
        "Kant",
        "Kapadia",
        "Kapoor",
        "Kapur",
        "Kar",
        "Kara",
        "Karan",
        "Kari",
        "Karnik",
        "Karpe",
        "Kashyap",
        "Kata",
        "Kaul",
        "Kaur",
        "Keer",
        "Keer",
        "Khalsa",
        "Khanna",
        "Khare",
        "Khatri",
        "Khosla",
        "Khurana",
        "Kibe",
        "Kohli",
        "Konda",
        "Korpal",
        "Koshy",
        "Kota",
        "Kothari",
        "Krish",
        "Krishna",
        "Krishnamurthy",
        "Krishnan",
        "Kulkarni",
        "Kumar",
        "Kumer",
        "Kunda",
        "Kurian",
        "Kuruvilla",
        "Lad",
        "Lad",
        "Lal",
        "Lala",
        "Lall",
        "Lalla",
        "Lanka",
        "Lata",
        "Loke",
        "Loyal",
        "Luthra",
        "Madan",
        "Madan",
        "Magar",
        "Mahajan",
        "Mahal",
        "Maharaj",
        "Majumdar",
        "Malhotra",
        "Mall",
        "Mallick",
        "Mammen",
        "Mand",
        "Manda",
        "Mandal",
        "Mander",
        "Mane",
        "Mangal",
        "Mangat",
        "Mani",
        "Mani",
        "Mann",
        "Mannan",
        "Manne",
        "Master",
        "Raj",
        "Raja",
        "Rajagopal",
        "Rajagopalan",
        "Rajan",
        "Raju",
        "Ram",
        "Rama",
        "Ramachandran",
        "Ramakrishnan",
        "Raman",
        "Ramanathan",
        "Ramaswamy",
        "Ramesh",
        "Rana",
        "Randhawa",
        "Ranganathan",
        "Rao",
        "Rastogi",
        "Ratta",
        "Rattan",
        "Ratti",
        "Rau",
        "Raval",
        "Ravel",
        "Ravi",
        "Ray",
        "Reddy",
        "Rege",
        "Rout",
        "Roy",
        "Sabharwal",
        "Sachar",
        "Sachdev",
        "Sachdeva",
        "Sagar",
        "Saha",
        "Sahni",
        "Sahota",
        "Saini",
        "Salvi",
        "Sama",
        "Sami",
        "Sampath",
        "Samra",
        "Sandal",
        "Sandhu",
        "Sane",
        "Sangha",
        "Sanghvi",
        "Sani",
        "Sankar",
        "Sankaran",
        "Sant",
        "Saraf",
        "Saran",
        "Sarin",
        "Sarkar",
        "Sarma",
        "Sarna",
        "Sarraf",
        "Sastry",
        "Sathe",
        "Savant",
        "Sawhney",
        "Saxena",
        "Sehgal",
        "Sekhon",
        "Sem",
        "Sen",
        "Sengupta",
        "Seshadri",
        "Seth",
        "Sethi",
        "Setty",
        "Sha",
        "Shah",
        "Shan",
        "Shankar",
        "Shanker",
        "Sharaf",
        "Sharma",
        "Shenoy",
        "Shere",
        "Sheth",
        "Shetty",
        "Shroff",
        "Shukla",
        "Sibal",
        "Sidhu",
        "Singh",
        "Singhal",
        "Sinha",
        "Sodhi",
        "Solanki",
        "Som",
        "Soman",
        "Soni",
        "Sood",
        "Sridhar",
        "Srinivas",
        "Srinivasan",
        "Srivastava",
        "Subramaniam",
        "Subramanian",
        "Sule",
        "Sundaram",
        "Sunder",
        "Sur",
        "Sura",
        "Suresh",
        "Suri",
        "Swaminathan",
        "Swamy",
        "Tailor",
        "Tak",
        "Talwar",
        "Tandon",
        "Taneja",
        "Tank",
        "Tara",
        "Tata",
        "Tella",
        "Thaker",
        "Thakkar",
        "Thakur",
        "Thaman",
        "Tiwari",
        "Toor",
        "Tripathi",
        "Trivedi",
        "Upadhyay",
        "Uppal",
        "Vaidya",
        "Vala",
        "Varghese",
        "Varkey",
        "Varma",
        "Varty",
        "Varughese",
        "Vasa",
        "Venkataraman",
        "Venkatesh",
        "Verma",
        "Vig",
        "Virk",
        "Viswanathan",
        "Vohra",
        "Vora",
        "Vyas",
        "Wable",
        "Wadhwa",
        "Wagle",
        "Wali",
        "Wali",
        "Walia",
        "Walla",
        "Warrior",
        "Wason",
        "Yadav",
        "Yogi",
        "Yohannan",
        "Zacharia",
        "Zachariah",
    )
