Metadata-Version: 2.1
Name: PyQRCode
Version: 1.2.1
Summary: A QR code generator written purely in Python with SVG, EPS, PNG and terminal output.
Home-page: https://github.com/mnooner256/pyqrcode
Author: <PERSON>
Author-email: <EMAIL>
License: BSD
Keywords: qrcode,qr
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Operating System :: POSIX
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 2.6
Classifier: Programming Language :: Python :: 2.7
Provides-Extra: png
Requires-Dist: pypng >=0.0.13 ; extra == 'png'

UNKNOWN

