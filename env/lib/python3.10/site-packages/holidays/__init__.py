#  python-holidays
#  ---------------
#  A fast, efficient Python library for generating country, province and state
#  specific sets of holidays on the fly. It aims to make determining whether a
#  specific date is a holiday as fast and flexible as possible.
#
# <AUTHOR> <EMAIL> (c) 2017-2023
# <AUTHOR> <EMAIL> (c) 2014-2017
#  Website: https://github.com/dr-prodigy/python-holidays
#  License: MIT (see LICENSE file)

# flake8: noqa: F403

from holidays.constants import *
from holidays.holiday_base import *
from holidays.registry import EntityLoader
from holidays.utils import *

__version__ = "0.44"


EntityLoader.load("countries", globals())
EntityLoader.load("financial", globals())
