#  python-holidays
#  ---------------
#  A fast, efficient Python library for generating country, province and state
#  specific sets of holidays on the fly. It aims to make determining whether a
#  specific date is a holiday as fast and flexible as possible.
#
# <AUTHOR> <EMAIL> (c) 2017-2023
# <AUTHOR> <EMAIL> (c) 2014-2017
#  Website: https://github.com/dr-prodigy/python-holidays
#  License: MIT (see LICENSE file)

# flake8: noqa: F401

from holidays.calendars.gregorian import (
    JAN,
    FEB,
    MAR,
    APR,
    MAY,
    JUN,
    JUL,
    AUG,
    SEP,
    OCT,
    NOV,
    DEC,
    MON,
    TUE,
    WED,
    THU,
    FRI,
    SAT,
    SUN,
    WEEKEND,
)

HOLIDAY_NAME_DELIMITER = "; "  # Holiday names separator.

# Supported holiday categories.
ARMED_FORCES = "armed_forces"
BANK = "bank"
GOVERNMENT = "government"
HALF_DAY = "half_day"
OPTIONAL = "optional"
PUBLIC = "public"
SCHOOL = "school"
UNOFFICIAL = "unofficial"
WORKDAY = "workday"

CHINESE = "chinese"
CHRISTIAN = "christian"
HEBREW = "hebrew"
HINDU = "hindu"
ISLAMIC = "islamic"
