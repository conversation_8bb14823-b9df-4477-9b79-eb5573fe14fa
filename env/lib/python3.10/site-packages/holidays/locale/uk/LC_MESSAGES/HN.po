# Honduras holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.25\n"
"POT-Creation-Date: 2023-03-05 12:23+0200\n"
"PO-Revision-Date: 2023-03-05 13:31+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "Новий рік"

#. Maundy Thursday.
msgid "Ju<PERSON>"
msgstr "Великий четвер"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Страсна пʼятниця"

#. Holy Saturday.
msgid "Sábado de Gloria"
msgstr "Велика субота"

#. Panamerican Day.
msgid "Día de las Américas"
msgstr "День Америки"

#. Labor Day.
msgid "Día del Trabajo"
msgstr "День праці"

#. Morazan's Day.
msgid "Día de Morazán"
msgstr "День Морасана"

#. Columbus Day.
msgid "Día de la Raza"
msgstr "День Колумба"

#. Army Day.
msgid "Día de las Fuerzas Armadas"
msgstr "День збройних сил"

#. Morazan Weekend.
msgid "Semana Morazánica"
msgstr "Тиждень Морасана"

#. Christmas Day.
msgid "Navidad"
msgstr "Різдво Христове"

#. Independence Day.
msgid "Día de la Independencia"
msgstr "День незалежності"
