# Denmark holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.37\n"
"POT-Creation-Date: 2023-04-09 13:45+0300\n"
"PO-Revision-Date: 2023-11-12 16:36+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Nytårsdag"
msgstr "Новий рік"

#. Maundy Thursday.
msgid "Skærtorsdag"
msgstr "Великий четвер"

#. Good Friday.
msgid "Langfredag"
msgstr "Страсна пʼятниця"

#. Easter Sunday.
msgid "Påskedag"
msgstr "Великдень"

#. Easter Monday.
msgid "Anden påskedag"
msgstr "Великодній понеділок"

#. Great Day of Prayers.
msgid "Store bededag"
msgstr "День загальної молитви"

#. Ascension Day.
msgid "Kristi himmelfartsdag"
msgstr "Вознесіння Господнє"

#. Whit Sunday.
msgid "Pinsedag"
msgstr "Трійця"

#. Whit Monday.
msgid "Anden pinsedag"
msgstr "День Святого Духа"

#. Christmas Day.
msgid "Juledag"
msgstr "Різдво Христове"

#. Second Day of Christmas.
msgid "Anden juledag"
msgstr "Другий день Різдва"

#. International Workers' Day.
msgid "Arbejdernes kampdag"
msgstr "День праці"

#. Constitution Day.
msgid "Grundlovsdag"
msgstr "День Конституції"

#. Christmas Eve.
msgid "Juleaftensdag"
msgstr "Святий вечір"

#. New Year's Eve.
msgid "Nytårsaften"
msgstr "Переддень Нового року"
