# Romania holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.22\n"
"POT-Creation-Date: 2023-03-22 18:35+0200\n"
"PO-Revision-Date: 2023-03-22 21:28+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Anul Nou"
msgstr "Новий рік"

#. Epiphany.
msgid "Bobotează"
msgstr "Богоявлення"

#. <PERSON> <PERSON> the Baptist.
msgid "Sfântul Ion"
msgstr "День Івана Хрестителя"

#. Unification of the Romanian Principalities Day.
msgid "Ziua Unirii Principatelor Române"
msgstr "День обʼєднання Дунайських князівств"

#. Easter.
msgid "Paștele"
msgstr "Великдень"

#. Labor Day.
msgid "Ziua Muncii"
msgstr "День праці"

#. Children's Day.
msgid "Ziua Copilului"
msgstr "День захисту дітей"

#. Pentecost.
msgid "Rusaliile"
msgstr "Трійця"

#. Dormition of the Mother of God.
msgid "Adormirea Maicii Domnului"
msgstr "Успіння Пресвятої Богородиці"

#. Saint Andrew's Day.
msgid "Sfantul Apostol Andrei cel Intai chemat"
msgstr "День святого Андрія Первозваного"

#. National Day.
msgid "Ziua Națională a României"
msgstr "Національний день Румунії"

#. Christmas Day.
msgid "Crăciunul"
msgstr "Різдво Христове"
