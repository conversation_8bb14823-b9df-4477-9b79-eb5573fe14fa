# Germany holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.23\n"
"POT-Creation-Date: 2023-04-04 16:13+0300\n"
"PO-Revision-Date: 2023-04-09 18:42+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Neujahr"
msgstr "Новий рік"

#. Good Friday.
msgid "Karfreitag"
msgstr "Страсна пʼятниця"

#. Easter Monday.
msgid "Ostermontag"
msgstr "Великодній понеділок"

#. Labor Day.
msgid "Erster Mai"
msgstr "День праці"

#. Ascension Day.
msgid "Christi Himmelfahrt"
msgstr "Вознесіння Господнє"

#. Whit Monday.
msgid "Pfingstmontag"
msgstr "День Святого Духа"

#. German Unity Day.
msgid "Tag der Deutschen Einheit"
msgstr "День німецької єдності"

#. Reformation Day.
msgid "Reformationstag"
msgstr "День Реформації"

#. Repentance and Prayer Day.
msgid "Buß- und Bettag"
msgstr "День молитви та покаяння"

#. Christmas Day.
msgid "Erster Weihnachtstag"
msgstr "Перший день Різдва"

#. Second Day of Christmas.
msgid "Zweiter Weihnachtstag"
msgstr "Другий день Різдва"

#. Easter Sunday.
msgid "Ostersonntag"
msgstr "Великдень"

#. Whit Sunday.
msgid "Pfingstsonntag"
msgstr "Трійця"

#. International Women's Day.
msgid "Internationaler Frauentag"
msgstr "Міжнародний жіночий день"

#. 75th anniversary of the liberation from Nazism and the end of the Second
#. World War in Europe.
msgid ""
"75. Jahrestag der Befreiung vom Nationalsozialismus und der Beendigung des "
"Zweiten Weltkriegs in Europa"
msgstr ""
"75-та річниця визволення від націонал-соціалізму та завершення Другої "
"світової війни в Європі"

#. Epiphany.
msgid "Heilige Drei Könige"
msgstr "Богоявлення"

#. Corpus Christi.
msgid "Fronleichnam"
msgstr "Свято Тіла і Крові Христових"

#. All Saints' Day.
msgid "Allerheiligen"
msgstr "День усіх святих"

#. Assumption Day.
msgid "Mariä Himmelfahrt"
msgstr "Внебовзяття Пресвятої Діви Марії"

#. World Children's Day.
msgid "Weltkindertag"
msgstr "Всесвітній день дітей"
