# Peru holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.21\n"
"POT-Creation-Date: 2023-03-01 15:30-0800\n"
"PO-Revision-Date: 2023-03-05 13:24+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "Новий рік"

#. Maundy Thursday.
msgid "Ju<PERSON> Santo"
msgstr "Великий четвер"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Страсна пʼятниця"

#. Easter Sunday.
msgid "Domingo de Resurrección"
msgstr "Великдень"

#. Labor Day.
msgid "Día del Trabajo"
msgstr "День праці"

#. Saint Peter and Saint Paul.
msgid "San Pedro y San Pablo"
msgstr "День Святих Петра і Павла"

#. Independence Day.
msgid "Día de la Independencia"
msgstr "День незалежності"

#. Great Military Parade Day.
msgid "Día de la Gran Parada Militar"
msgstr "День Великого військового параду"

#. Battle of Junín.
msgid "Batalla de Junín"
msgstr "День битви під Хуніном"

#. Rose of Lima Day.
msgid "Santa Rosa de Lima"
msgstr "День Святої Рози Лімської"

#. Battle of Angamos.
msgid "Combate de Angamos"
msgstr "День битви під Ангамосом"

#. All Saints' Day.
msgid "Todos Los Santos"
msgstr "День усіх святих"

#. Immaculate Conception.
msgid "Inmaculada Concepción"
msgstr "Непорочне зачаття Діви Марії"

#. Battle of Ayacucho.
msgid "Batalla de Ayacucho"
msgstr "День битви при Аякучо"

#. Christmas Day.
msgid "Navidad del Señor"
msgstr "Різдво Христове"
