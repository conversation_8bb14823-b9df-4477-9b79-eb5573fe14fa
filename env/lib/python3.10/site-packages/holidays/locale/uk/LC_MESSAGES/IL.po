# Israel holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.39\n"
"POT-Creation-Date: 2023-07-18 17:31+0300\n"
"PO-Revision-Date: 2023-07-18 17:57+0300\n"
"Last-Translator: Arkadii <PERSON> <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. Rosh <PERSON> (New Year).
msgid "ראש השנה"
msgstr "Рош га-Шана"

#. Yom Kippur (Day of Atonement).
msgid "יום כיפור"
msgstr "Йом Кіпур"

#. Sukkot (Feast of Tabernacles).
msgid "סוכות"
msgstr "Суккот"

#. Simchat Torah / Shemini Atzeret.
msgid "שמחת תורה/שמיני עצרת"
msgstr "Сімхат Тора / Шміні Ацерет"

#. Pesach (Passover).
msgid "פסח"
msgstr "Песах"

#. Shvi'i shel Pesach (Seventh day of Passover)
msgid "שביעי של פסח"
msgstr "Сьомий день Песаха"

#. Yom Ha-Atzmaut (Independence Day).
msgid "יום העצמאות"
msgstr "День незалежності"

#. Shavuot.
msgid "שבועות"
msgstr "Шавуот"

#. Chol HaMoed Sukkot (Feast of Tabernacles holiday).
msgid "חול המועד סוכות"
msgstr "Свято Суккот"

#. Sigd.
msgid "סיגד"
msgstr "Сігд"

#. Purim.
msgid "פורים"
msgstr "Пурім"

#. Chol HaMoed Pesach (Passover holiday).
msgid "חול המועד פסח"
msgstr "Свято Песах"

#. Yom Hazikaron (Fallen Soldiers and Victims of Terrorism Remembrance Day).
msgid "יום הזיכרון לחללי מערכות ישראל ונפגעי פעולות האיבה"
msgstr "День памʼяті"

#. Yom Yerushalayim (Jerusalem Day).
msgid "יום ירושלים"
msgstr "День Єрусалиму"

#. Tisha B'Av (Tisha B'Av, fast).
msgid "תשעה באב"
msgstr "Тиша Бе-Ав"

#. Hanukkah.
msgid "חנוכה"
msgstr "Ханука"

#. Ta`anit Ester (Fast of Esther).
msgid "תענית אסתר"
msgstr "Тааніт-Естер"

#. Lag Ba'omer (Lag BaOmer).
msgid "ל\"ג בעומר"
msgstr "Лаг ба-Омер"

#. %s (observed).
#, c-format
msgid "(נצפה) %s"
msgstr "%s (вихідний)"
