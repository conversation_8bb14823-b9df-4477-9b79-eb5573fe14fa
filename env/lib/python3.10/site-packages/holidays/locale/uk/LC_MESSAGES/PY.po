# Paraguay holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.41\n"
"POT-Creation-Date: 2023-03-05 12:33+0200\n"
"PO-Revision-Date: 2024-01-05 12:45+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. Public holiday.
msgid "Asueto adicionale"
msgstr "Додатковий вихідний"

#. Public sector holiday.
msgid "Asueto de la Administración Pública"
msgstr "Вихідний державних установ"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "Новий рік"

#. Patriots Day.
msgid "Día de los Héroes de la Patria"
msgstr "День національних героїв"

#. Maundy Thursday.
msgid "Jueves Santo"
msgstr "Великий четвер"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Страсна пʼятниця"

#. Easter Sunday.
msgid "Domingo de Resurrección"
msgstr "Великдень"

#. Labor Day.
msgid "Día del Trabajador"
msgstr "День трудящих"

#. Independence Day.
msgid "Día de la Independencia Nacional"
msgstr "День незалежності"

#. Chaco Armistice Day.
msgid "Día de la Paz del Chaco"
msgstr "День мирного договору в Чако"

#. Asuncion Foundation's Day.
msgid "Día de la Fundación de Asunción"
msgstr "День заснування Асунсьйона"

#. Boqueron Battle Day.
msgid "Día de la Batalla de Boquerón"
msgstr "День битви за Бокерон"

#. Caacupe Virgin Day.
msgid "Día de la Virgen de Caacupé"
msgstr "Успіння Пресвятої Богородиці"

#. Christmas Day.
msgid "Navidad"
msgstr "Різдво Христове"
