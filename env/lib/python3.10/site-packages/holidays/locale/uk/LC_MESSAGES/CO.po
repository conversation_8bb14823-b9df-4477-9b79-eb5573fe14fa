# Colombia holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.41\n"
"POT-Creation-Date: 2023-03-01 15:30-0800\n"
"PO-Revision-Date: 2023-05-03 18:38-0700\n"
"Last-Translator: Arkadii Ya<PERSON> <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.3\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "Новий рік"

#. Epiphany.
msgid "<PERSON>ía de los <PERSON> Magos"
msgstr "Богоявлення"

#. Saint Joseph's Day.
msgid "Día de San José"
msgstr "День Святого Йосипа"

#. Labor Day.
msgid "Día del Trabajo"
msgstr "День праці"

#. Saint Peter and Saint Paul's Day.
msgid "San Pedro y San Pablo"
msgstr "День Святих Петра і Павла"

#. Independence Day.
msgid "Día de la Independencia"
msgstr "День незалежності"

#. Battle of Boyaca.
msgid "Batalla de Boyacá"
msgstr "Річниця перемоги при Бояка"

#. Assumption Day.
msgid "La Asunción"
msgstr "Внебовзяття Пресвятої Діви Марії"

#. Columbus Day.
msgid "Día de la Raza"
msgstr "День Колумба"

#. All Saints' Day.
msgid "Día de Todos los Santos"
msgstr "День усіх святих"

#. Independence of Cartagena.
msgid "Independencia de Cartagena"
msgstr "День незалежності Картахени"

#. Immaculate Conception.
msgid "La Inmaculada Concepción"
msgstr "Непорочне зачаття Діви Марії"

#. Christmas Day.
msgid "Navidad"
msgstr "Різдво Христове"

#. Maundy Thursday.
msgid "Jueves Santo"
msgstr "Великий четвер"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Страсна пʼятниця"

#. Ascension Day.
msgid "Ascensión del señor"
msgstr "Вознесіння Господнє"

#. Corpus Christi.
msgid "Corpus Christi"
msgstr "Свято Тіла і Крові Христових"

#. Sacred Heart.
msgid "Sagrado Corazón"
msgstr "Свято Найсвятішого Серця Ісуса"

#. %s (observed).
#, c-format
msgid "%s (observado)"
msgstr "%s (вихідний)"
