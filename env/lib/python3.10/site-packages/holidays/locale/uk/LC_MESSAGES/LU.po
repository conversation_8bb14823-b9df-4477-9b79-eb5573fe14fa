# Luxembourg holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.28\n"
"POT-Creation-Date: 2023-06-27 13:17+0300\n"
"PO-Revision-Date: 2023-06-27 17:06+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Neijoerschdag"
msgstr "Новий рік"

#. Easter Monday.
msgid "Ouschterméindeg"
msgstr "Великодній понед<PERSON><PERSON>ок"

#. Labor Day.
msgid "Dag vun der <PERSON>"
msgstr "День праці"

#. Europe Day.
msgid "Europadag"
msgstr "День Європи"

#. Ascension Day.
msgid "Christi Himmelfaart"
msgstr "Вознесіння Господнє"

#. Whit Monday.
msgid "Péngschtméindeg"
msgstr "День Святого Духа"

#. National Day.
msgid "Nationalfeierdag"
msgstr "Національне свято"

#. Assumption Day.
msgid "Léiffrawëschdag"
msgstr "Внебовзяття Пресвятої Діви Марії"

#. All Saints' Day.
msgid "Allerhellgen"
msgstr "День усіх святих"

#. Christmas Day.
msgid "Chrëschtdag"
msgstr "Різдво Христове"

#. St. Stephen's Day.
msgid "Stiefesdag"
msgstr "День Святого Стефана"
