# Estonia holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.28\n"
"POT-Creation-Date: 2023-06-27 13:17+0300\n"
"PO-Revision-Date: 2023-06-27 16:16+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "uusaasta"
msgstr "Новий рік"

#. Independence Day.
msgid "iseseisvuspäev"
msgstr "День незалежності"

#. Good Friday.
msgid "suur reede"
msgstr "Страсна пʼятниця"

#. Easter Sunday.
msgid "ülestõusmispühade 1. püha"
msgstr "Великдень"

#. Spring Day.
msgid "kevadpüha"
msgstr "День весни"

#. Whit Sunday.
msgid "nelipühade 1. püha"
msgstr "Трійця"

#. Victory Day.
msgid "võidupüha"
msgstr "День перемоги"

#. Midsummer Day.
msgid "jaanipäev"
msgstr "День літнього сонцестояння"

#. Independence Restoration Day.
msgid "taasiseseisvumispäev"
msgstr "День відновлення незалежності"

#. Christmas Eve.
msgid "jõululaupäev"
msgstr "Святий вечір"

#. Christmas Day.
msgid "esimene jõulupüha"
msgstr "Різдво Христове"

#. Second Day of Christmas.
msgid "teine jõulupüha"
msgstr "Другий день Різдва"
