# Venezuela holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.22\n"
"POT-Creation-Date: 2023-03-05 12:23+0200\n"
"PO-Revision-Date: 2023-03-05 13:38+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "Новий рік"

#. Monday of Carnival.
msgid "Lunes de Carnaval"
msgstr "Карнавальний понеділок"

#. Tuesday of Carnival.
msgid "Martes de Carnaval"
msgstr "Карнавальний вівторок"

#. Maundy Thursday.
msgid "Jueves Santo"
msgstr "Великий четвер"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Страсна пʼятниця"

#. Declaration of Independence.
msgid "Declaración de la Independencia"
msgstr "День проголошення незалежності"

#. International Worker's Day.
msgid "Dia Mundial del Trabajador"
msgstr "Міжнародний день трудящих"

#. Battle of Carabobo.
msgid "Batalla de Carabobo"
msgstr "День битви при Карабобо"

#. Independence Day.
msgid "Día de la Independencia"
msgstr "День незалежності"

#. Birthday of Simon Bolivar.
msgid "Natalicio de Simón Bolívar"
msgstr "Річниця Сімона Болівара"

#. Day of Indigenous Resistance.
msgid "Día de la Resistencia Indígena"
msgstr "День спротиву корінних народів"

#. Columbus Day.
msgid "Día de la Raza"
msgstr "День Колумба"

#. Unknown Holiday.
msgid "Día Festivo Desconocido"
msgstr "Невідоме свято"

#. Christmas Eve.
msgid "Nochebuena"
msgstr "Святий вечір"

#. Christmas Day.
msgid "Día de Navidad"
msgstr "Різдво Христове"

#. New Year's Eve.
msgid "Fiesta de Fin de Año"
msgstr "Переддень Нового року"
