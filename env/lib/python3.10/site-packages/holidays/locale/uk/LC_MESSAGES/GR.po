# Greece holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.30\n"
"POT-Creation-Date: 2023-07-27 18:57+0300\n"
"PO-Revision-Date: 2023-07-27 19:16+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Πρωτοχρονιά"
msgstr "Новий рік"

#. Epiphany.
msgid "Θεοφάνεια"
msgstr "Богоявлення"

#. Green Monday.
msgid "Καθαρά Δευτέρα"
msgstr "Чистий понеділок"

#. Independence Day.
msgid "Εικοστή Πέμπτη Μαρτίου"
msgstr "День незалежності"

#. Good Friday.
msgid "Μεγάλη Παρασκευή"
msgstr "Страсна пʼятниця"

#. Easter Monday.
msgid "Δευτέρα του Πάσχα"
msgstr "Великодній понеділок"

#. Whit Monday.
msgid "Δευτέρα του Αγίου Πνεύματος"
msgstr "День Святого Духа"

#. Labor Day.
msgid "Εργατική Πρωτομαγιά"
msgstr "День праці"

#. %s (observed).
#, c-format
msgid "%s (παρατηρήθηκε)"
msgstr "%s (вихідний)"

#. Dormition of the Mother of God.
msgid "Κοίμηση της Θεοτόκου"
msgstr "Успіння Пресвятої Богородиці"

#. Ochi Day.
msgid "Ημέρα του Όχι"
msgstr "День Охі"

#. Christmas Day.
msgid "Χριστούγεννα"
msgstr "Різдво Христове"

#. Glorifying of the Mother of God.
msgid "Σύναξη της Υπεραγίας Θεοτόκου"
msgstr "Собор Пресвятої Богородиці"

#. Christmas Eve.
msgid "Παραμονή Χριστουγέννων"
msgstr "Святий вечір"

#. New Year's Eve.
msgid "Παραμονή Πρωτοχρονιάς"
msgstr "Переддень Нового року"
