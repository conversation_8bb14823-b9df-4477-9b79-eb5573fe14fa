# Aruba holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.27\n"
"POT-Creation-Date: 2023-06-09 23:12+0700\n"
"PO-Revision-Date: \n"
"Last-Translator: ~<PERSON>hell<PERSON> <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.3.1\n"

#. New Year's Day
msgid "Aña Nobo"
msgstr "Новий рік"

#. Betico Day
msgid "Dia di Betico"
msgstr "День Бетіко"

#. Carnival Monday
msgid "Dialuna despues di Carnaval Grandi"
msgstr "Карнавальний понеділок"

#. Monday before Ash Wednesday
msgid "Dialuna prome cu diaranson di shinish"
msgstr "Понеділок перед Попільною середою"

#. National Anthem and Flag Day
msgid "Dia di Himno y Bandera"
msgstr "День державного гімну та прапора"

#. Good Friday
msgid "Bierna Santo"
msgstr "Страсна пʼятниця"

#. Easter Monday
msgid "Di dos dia di Pasco di Resureccion"
msgstr "Великодній понеділок"

#. King's Day.
msgid "Dia di Rey"
msgstr "День короля"

#. King's Day.
msgid "Aña di Rey"
msgstr "День короля"

#. Queen's Day.
msgid "Aña di La Reina"
msgstr "День королеви"

#. Labor Day
msgid "Dia di Obrero"
msgstr "День праці"

#. Ascension Day
msgid "Dia di Asuncion"
msgstr "Вознесіння Господнє"

#. Christmas Day
msgid "Pasco di Nacemento"
msgstr "Різдво Христове"

#. Second Day of Christmas
msgid "Di dos dia di Pasco di Nacemento"
msgstr "Другий день Різдва"
