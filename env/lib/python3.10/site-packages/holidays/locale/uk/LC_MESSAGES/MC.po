# Monaco holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.41\n"
"POT-Creation-Date: 2023-02-20 11:58+0200\n"
"PO-Revision-Date: 2024-01-02 18:34+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4\n"

#. Public holiday.
msgid "Jour férié"
msgstr "Держа<PERSON>не свято"

#. %s (observed).
#, c-format
msgid "%s (observé)"
msgstr "%s (вихідний)"

#. New Year's Day.
msgid "Le jour de l'An"
msgstr "Новий рік"

#. Saint Devote's Day.
msgid "La Sainte Dévote"
msgstr "День святої Девоти"

#. Easter Monday.
msgid "Le lundi de Pâques"
msgstr "Великодній понеділок"

#. Labor Day.
msgid "Fête de la Travaille"
msgstr "День праці"

#. Ascension Day.
msgid "L'Ascension"
msgstr "Вознесіння Господнє"

#. Whit Monday.
msgid "Le lundi de Pentecôte"
msgstr "День Святого Духа"

#. Corpus Christi.
msgid "La Fête Dieu"
msgstr "Свято Тіла і Крові Христових"

#. Assumption Day.
msgid "L'Assomption de Marie"
msgstr "Внебовзяття Пресвятої Діви Марії"

#. All Saints' Day.
msgid "La Toussaint"
msgstr "День усіх святих"

#. Prince's Day.
msgid "La Fête du Prince"
msgstr "День Князя"

#. Immaculate Conception.
msgid "L'Immaculée Conception"
msgstr "Непорочне зачаття Діви Марії"

#. Christmas Day.
msgid "Noël"
msgstr "Різдво Христове"
