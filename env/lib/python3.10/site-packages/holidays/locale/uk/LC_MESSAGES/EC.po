# Ecuador holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.41\n"
"POT-Creation-Date: 2023-04-02 18:54+0300\n"
"PO-Revision-Date: 2023-04-02 19:02+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "Новий рік"

#. Carnival.
msgid "Carnaval"
msgstr "Карнавал"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Страсна пʼятниця"

#. Labor Day.
msgid "Día del Trabajo"
msgstr "День праці"

#. The Battle of Pichincha.
msgid "Batalla de Pichincha"
msgstr "День битви біля Пічинча"

#. Declaration of Independence of Quito.
msgid "Primer Grito de Independencia"
msgstr "День незалежності Кіто"

#. Independence of Guayaquil.
msgid "Independencia de Guayaquil"
msgstr "День незалежності Гуаякіля"

#. All Souls' Day.
msgid "Día de los Difuntos"
msgstr "День усіх померлих"

#. Independence of Cuenca.
msgid "Independencia de Cuenca"
msgstr "День незалежності Куенки"

#. Christmas Day.
msgid "Día de Navidad"
msgstr "Різдво Христове"

#. %s (observed).
#, c-format
msgid "%s (observado)"
msgstr "%s (вихідний)"
