# Dominican Republic holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.22\n"
"POT-Creation-Date: 2023-03-05 12:33+0200\n"
"PO-Revision-Date: 2023-03-05 14:05+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "Новий рік"

#. Epiphany.
msgid "<PERSON><PERSON> de los Santos Reyes"
msgstr "Богоявлення"

#. Lady of Altagracia.
msgid "Día de la Altagracia"
msgstr "День Богоматері Альтаграсія"

#. Juan Pablo Duarte Day.
msgid "Día de Duarte"
msgstr "День Дуарте"

#. Independence Day.
msgid "Día de Independencia"
msgstr "День незалежності"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Страсна пʼятниця"

#. Labor Day.
msgid "Día del Trabajo"
msgstr "День праці"

#. Corpus Christi.
msgid "Corpus Christi"
msgstr "Свято Тіла і Крові Христових"

#. Restoration Day.
msgid "Día de la Restauración"
msgstr "День реставрації"

#. Our Lady of Mercedes Day.
msgid "Día de las Mercedes"
msgstr "День Богоматері Милосердя"

#. Constitution Day.
msgid "Día de la Constitución"
msgstr "День Конституції"

#. Christmas Day.
msgid "Día de Navidad"
msgstr "Різдво Христове"
