# Hungary holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.37\n"
"POT-Creation-Date: 2023-06-12 19:06+0300\n"
"PO-Revision-Date: 2023-11-10 22:16+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Újév"
msgstr "Новий рік"

#. National Day.
msgid "Nemzeti ünnep"
msgstr "Національне свято"

#. Good Friday.
msgid "Nagypéntek"
msgstr "Страсна пʼятниця"

#. Easter.
msgid "Húsvét"
msgstr "Великдень"

#. Easter Monday.
msgid "Húsvét Hétfő"
msgstr "Великодній понеділок"

#. Whit Sunday.
msgid "Pünkösd"
msgstr "Трійця"

#. Whit Monday.
msgid "Pünkösdhétfő"
msgstr "День Святого Духа"

#. Labor Day.
msgid "A Munka ünnepe"
msgstr "День праці"

#. Bread Day.
msgid "A kenyér ünnepe"
msgstr "День хліба"

#. State Foundation Day.
msgid "Az államalapítás ünnepe"
msgstr "День заснування держави"

#. All Saints' Day.
msgid "Mindenszentek"
msgstr "День усіх святих"

#. Christmas Day.
msgid "Karácsony"
msgstr "Різдво Христове"

#. Second Day of Christmas.
msgid "Karácsony másnapja"
msgstr "Другий день Різдва"

#. Proclamation of Soviet Republic Day.
msgid "A Tanácsköztársaság kikiáltásának ünnepe"
msgstr "День проголошення радянської республіки"

#. Liberation Day.
msgid "A felszabadulás ünnepe"
msgstr "День визволення"

#. Great October Socialist Revolution Day.
msgid "A nagy októberi szocialista forradalom ünnepe"
msgstr "День Великої Жовтневої соціалістичної революції"

#. Substituted date format.
msgid "%Y. %m. %d."
msgstr "%d.%m.%Y"

#. Day off (substituted from %s).
#, c-format
msgid "Pihenőnap (%s-től helyettesítve)"
msgstr "Вихідний день (перенесено з %s)"
