# Lithuania holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.44\n"
"POT-Creation-Date: 2023-06-27 13:17+0300\n"
"PO-Revision-Date: 2024-03-01 15:04+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Nauj<PERSON>j<PERSON> metų diena"
msgstr "Новий рік"

#. Day of Restoration of the State of Lithuania.
msgid "Lietuvos valstybės atkūrimo diena"
msgstr "День відновлення Литовської держави"

#. Day of Restoration of Independence of Lithuania.
msgid "Lietuvos nepriklausomybės atkūrimo diena"
msgstr "День відновлення незалежності Литви"

#. Easter Sunday.
msgid "Šv. Velykos"
msgstr "Великдень"

#. Easter Monday.
msgid "Antroji šv. Velykų diena"
msgstr "Великодній понеділок"

#. International Workers' Day.
msgid "Tarptautinė darbo diena"
msgstr "Міжнародний день трудящих"

#. Day of Dew and Saint John.
msgid "Rasos ir Joninių diena"
msgstr "День роси та День Івана Купала"

#. Statehood Day.
msgid ""
"Valstybės (Lietuvos karaliaus Mindaugo karūnavimo) ir Tautiškos giesmės "
"diena"
msgstr "День державності та День національного гімну"

#. Assumption Day.
msgid "Žolinė (Švč. Mergelės Marijos ėmimo į dangų diena)"
msgstr "Внебовзяття Пресвятої Діви Марії"

#. All Saints' Day.
msgid "Visų Šventųjų diena"
msgstr "День усіх святих"

#. All Souls' Day.
msgid "Mirusiųjų atminimo (Vėlinių) diena"
msgstr "День памʼяті (День всіх померлих)"

#. Christmas Eve.
msgid "Kūčių diena"
msgstr "Святий вечір"

#. Christmas Day.
msgid "Šv. Kalėdų pirma diena"
msgstr "Різдво Христове"

#. Second Day of Christmas.
msgid "Šv. Kalėdų antra diena"
msgstr "Другий день Різдва"

#. Mother's Day.
msgid "Motinos diena"
msgstr "День матері"

#. Father's Day.
msgid "Tėvo diena"
msgstr "День батька"
