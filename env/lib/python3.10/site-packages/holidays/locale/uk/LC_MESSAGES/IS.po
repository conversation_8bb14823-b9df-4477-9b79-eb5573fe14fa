# Iceland holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.23\n"
"POT-Creation-Date: 2023-04-09 14:31+0300\n"
"PO-Revision-Date: 2023-04-09 14:32+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Nýársdagur"
msgstr "Новий рік"

#. Maundy Thursday.
msgid "Skírdagur"
msgstr "Великий четвер"

#. Good Friday.
msgid "Föstudagurinn langi"
msgstr "Страсна пʼятниця"

#. Easter Sunday.
msgid "Páskadagur"
msgstr "Великдень"

#. Easter Monday.
msgid "Annar í páskum"
msgstr "Великодній понеділок"

#. First Day of Summer.
msgid "Sumardagurinn fyrsti"
msgstr "Перший день літа"

#. Labor Day.
msgid "Verkalýðsdagurinn"
msgstr "День праці"

#. Ascension Day.
msgid "Uppstigningardagur"
msgstr "Вознесіння Господнє"

#. Whit Sunday.
msgid "Hvítasunnudagur"
msgstr "Трійця"

#. Whit Monday.
msgid "Annar í hvítasunnu"
msgstr "День Святого Духа"

#. National Day.
msgid "Þjóðhátíðardagurinn"
msgstr "Національне свято"

#. Commerce Day.
msgid "Frídagur verslunarmanna"
msgstr "День торгівлі"

#. Christmas Eve.
msgid "Aðfangadagur"
msgstr "Святий вечір"

#. Christmas Day.
msgid "Jóladagur"
msgstr "Різдво Христове"

#. Second Day of Christmas.
msgid "Annar í jólum"
msgstr "Другий день Різдва"

#. New Year's Eve.
msgid "Gamlársdagur"
msgstr "Переддень Нового року"
