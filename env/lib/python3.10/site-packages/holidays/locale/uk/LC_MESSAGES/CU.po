# Cuba holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.22\n"
"POT-Creation-Date: 2023-03-04 14:20+0200\n"
"PO-Revision-Date: 2023-03-05 13:11+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. %s (observed).
#, c-format
msgid "%s (observado)"
msgstr "%s (вихідний)"

#. Liberation Day.
msgid "Triunfo de la Revolución"
msgstr "Тріумф революції"

#. Victory Day.
msgid "Día de la Victoria"
msgstr "День перемоги"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Страсна пʼятниця"

#. International Worker's Day.
msgid "Día Internacional de los Trabajadores"
msgstr "Міжнародний день трудящих"

#. Commemoration of the Assault of the Moncada garrison.
msgid "Conmemoración del asalto a Moncada"
msgstr "Вшанування памʼяті штурму Монкади"

#. Day of the National Rebellion.
msgid "Día de la Rebeldía Nacional"
msgstr "День національного повстання"

#. Independence Day.
msgid "Inicio de las Guerras de Independencia"
msgstr "Початок війни за незалежність"

#. Christmas Day.
msgid "Día de Navidad"
msgstr "Різдво Христове"

#. New Year's Eve.
msgid "Fiesta de Fin de Año"
msgstr "Переддень Нового року"
