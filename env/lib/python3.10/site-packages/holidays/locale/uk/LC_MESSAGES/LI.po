# Liechtenstein holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-04-01 18:49+0300\n"
"PO-Revision-Date: 2024-01-25 21:17+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Neujahr"
msgstr "Новий рік"

#. <PERSON>ld's Day.
msgid "Berchtoldstag"
msgstr "День Святого Бертольда"

#. Epiphany.
msgid "Heilige Drei Könige"
msgstr "Богоявлення"

#. Candlemas.
msgid "Mariä Lichtmess"
msgstr "Стрітення"

#. Shrove Tuesday.
msgid "Fasnachtsdienstag"
msgstr "Масний вівторок"

#. Saint Joseph's Day.
msgid "Josefstag"
msgstr "День Святого Йосипа"

#. Good Friday.
msgid "Karfreitag"
msgstr "Страсна пʼятниця"

#. Easter Sunday.
msgid "Ostersonntag"
msgstr "Великдень"

#. Easter Monday.
msgid "Ostermontag"
msgstr "Великодній понеділок"

#. Labor Day.
msgid "Tag der Arbeit"
msgstr "День праці"

#. Ascension Day.
msgid "Auffahrt"
msgstr "Вознесіння Господнє"

#. Whit Sunday.
msgid "Pfingstsonntag"
msgstr "Трійця"

#. Whit Monday.
msgid "Pfingstmontag"
msgstr "День Святого Духа"

#. Corpus Christi.
msgid "Fronleichnam"
msgstr "Свято Тіла і Крові Христових"

#. National Day.
msgid "Staatsfeiertag"
msgstr "Національне свято"

#. Nativity of Mary.
msgid "Mariä Geburt"
msgstr "Різдво Пресвятої Богородиці"

#. All Saints' Day.
msgid "Allerheiligen"
msgstr "День усіх святих"

#. Immaculate Conception.
msgid "Mariä Empfängnis"
msgstr "Непорочне зачаття Діви Марії"

#. Christmas Eve.
msgid "Heiligabend"
msgstr "Святий вечір"

#. Christmas Day.
msgid "Weihnachten"
msgstr "Різдво Христове"

#. St. Stephen's Day.
msgid "Stephanstag"
msgstr "День Святого Стефана"

#. New Year's Eve.
msgid "Silvester"
msgstr "Переддень Нового року"
