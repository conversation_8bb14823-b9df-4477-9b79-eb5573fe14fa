# Belgium holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.33\n"
"POT-Creation-Date: 2023-06-27 13:17+0300\n"
"PO-Revision-Date: 2023-09-06 20:53+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Nieuwjaar"
msgstr "Новий рік"

#. Easter Sunday.
msgid "Pasen"
msgstr "Великдень"

#. Easter Monday.
msgid "Paasmaandag"
msgstr "Великодній понеділок"

#. Labor Day.
msgid "<PERSON><PERSON>"
msgstr "День праці"

#. Ascension Day.
msgid "O. L. H. Hemelvaart"
msgstr "Вознесіння Господнє"

#. Whit Sunday.
msgid "Pinksteren"
msgstr "Трійця"

#. Whit Monday.
msgid "Pinkstermaandag"
msgstr "День Святого Духа"

#. National Day.
msgid "Nationale feestdag"
msgstr "Національне свято"

#. Assumption Day.
msgid "O. L. V. Hemelvaart"
msgstr "Внебовзяття Пресвятої Діви Марії"

#. All Saints' Day.
msgid "Allerheiligen"
msgstr "День усіх святих"

#. Armistice Day.
msgid "Wapenstilstand"
msgstr "День перемирʼя"

#. Christmas Day.
msgid "Kerstmis"
msgstr "Різдво Христове"

#. Good Friday.
msgid "Goede Vrijdag"
msgstr "Страсна пʼятниця"

#. Friday after Ascension Day.
msgid "Vrijdag na O. L. H. Hemelvaart"
msgstr "Пʼятниця після Вознесіння Господнього"

#. Bank Holiday.
msgid "Banksluitingsdag"
msgstr "Банківський вихідний"
