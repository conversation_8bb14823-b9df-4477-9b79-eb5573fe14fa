# Finland holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.23\n"
"POT-Creation-Date: 2023-04-08 19:30+0300\n"
"PO-Revision-Date: 2023-04-08 19:47+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Uudenvuodenpä<PERSON><PERSON>"
msgstr "Новий рік"

#. Epiphany.
msgid "Loppiainen"
msgstr "Богоявлення"

#. Good Friday.
msgid "Pitkäperjantai"
msgstr "Страсна пʼятниця"

#. Easter Sunday.
msgid "Pääsiäispäivä"
msgstr "Великдень"

#. Easter Monday.
msgid "2. pääsiäispäivä"
msgstr "Великодній понеділок"

#. May Day.
msgid "Vappu"
msgstr "Ваппу"

#. Ascension Day.
msgid "Helatorstai"
msgstr "Вознесіння Господнє"

#. Whit Sunday.
msgid "Helluntaipäivä"
msgstr "Трійця"

#. Midsummer Eve.
msgid "Juhannusaatto"
msgstr "Переддень літнього сонцестояння"

#. Midsummer Day.
msgid "Juhannuspäivä"
msgstr "День літнього сонцестояння"

#. All Saints' Day.
msgid "Pyhäinpäivä"
msgstr "День усіх святих"

#. Independence Day.
msgid "Itsenäisyyspäivä"
msgstr "День незалежності"

#. Christmas Eve.
msgid "Jouluaatto"
msgstr "Святий вечір"

#. Christmas Day.
msgid "Joulupäivä"
msgstr "Різдво Христове"

#. Second Day of Christmas.
msgid "Tapaninpäivä"
msgstr "Другий день Різдва"
