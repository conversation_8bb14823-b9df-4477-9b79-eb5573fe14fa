# Moldova holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.22\n"
"POT-Creation-Date: 2023-03-22 21:58+0200\n"
"PO-Revision-Date: 2023-03-22 22:00+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Anul Nou"
msgstr "Новий рік"

#. Christmas Day (by old style).
msgid "Naşterea lui Iisus <PERSON> (Crăciunul pe stil vechi)"
msgstr "Різдво Христове (за старим стилем)"

#. Christmas Day.
msgid "Naşterea lui Iisus Hristos (Crăciunul)"
msgstr "Різдво Христове"

#. International Women's Day.
msgid "Ziua internatională a femeii"
msgstr "Міжнародний жіночий день"

#. Easter.
msgid "Paştele"
msgstr "Великдень"

#. Day of Rejoicing.
msgid "Paştele blajinilor"
msgstr "Проводи"

#. International Workers' Solidarity Day.
msgid "Ziua internaţională a solidarităţii oamenilor muncii"
msgstr "День міжнародної солідарності трудящих"

#. Victory Day and Commemoration of the heroes fallen for Independence of
#. Fatherland.
msgid ""
"Ziua Victoriei şi a comemorării eroilor căzuţi pentru Independenţa Patriei"
msgstr ""
"День Перемоги та вшанування памʼяті героїв, полеглих за незалежність "
"Батьківщини"

#. Europe Day.
msgid "Ziua Europei"
msgstr "День Європи"

#. International Children's Day.
msgid "Ziua Ocrotirii Copilului"
msgstr "День захисту дітей"

#. Republic of Moldova Independence Day.
msgid "Ziua independenţei Republicii Moldova"
msgstr "День незалежності Республіки Молдова"

#. National Language Day.
msgid "Limba noastră"
msgstr "День рідної мови"

#. Christmas Day (by new style).
msgid "Naşterea lui Iisus Hristos (Crăciunul pe stil nou)"
msgstr "Різдво Христове (за новим стилем)"
