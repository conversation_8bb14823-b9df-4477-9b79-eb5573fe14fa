# Norway holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.23\n"
"POT-Creation-Date: 2023-04-09 13:25+0300\n"
"PO-Revision-Date: 2023-04-09 13:33+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "<PERSON>ørste nyttårsdag"
msgstr "Новий рік"

#. Maundy Thursday.
msgid "Skjærtorsdag"
msgstr "Великий четвер"

#. Good Friday.
msgid "Langfredag"
msgstr "Страсна пʼятниця"

#. Easter Sunday.
msgid "Første påskedag"
msgstr "Великдень"

#. Easter Monday.
msgid "Andre påskedag"
msgstr "Великодній понеділок"

#. Labor Day.
msgid "Arbeidernes dag"
msgstr "День праці"

#. Constitution Day.
msgid "Grunnlovsdag"
msgstr "День Конституції"

#. Ascension Day.
msgid "Kristi himmelfartsdag"
msgstr "Вознесіння Господнє"

#. Whit Sunday.
msgid "Første pinsedag"
msgstr "Трійця"

#. Whit Monday.
msgid "Andre pinsedag"
msgstr "День Святого Духа"

#. Christmas Day.
msgid "Første juledag"
msgstr "Різдво Христове"

#. Second Day of Christmas.
msgid "Andre juledag"
msgstr "Другий день Різдва"

#. Sunday.
msgid "Søndag"
msgstr "Неділя"
