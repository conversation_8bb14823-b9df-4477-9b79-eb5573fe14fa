# Netherlands holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.23\n"
"POT-Creation-Date: 2023-04-09 18:11+0300\n"
"PO-Revision-Date: 2023-04-09 18:19+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Nieuwjaarsdag"
msgstr "Новий рік"

#. Good Friday.
msgid "Goede Vrijdag"
msgstr "Страсна пʼятниця"

#. Easter Sunday.
msgid "Eerste paasdag"
msgstr "Великдень"

#. Easter Monday.
msgid "Tweede paasdag"
msgstr "Великодній понеділок"

#. King's Day.
msgid "Koningsdag"
msgstr "День короля"

#. Queen's Day.
msgid "Koninginnedag"
msgstr "День королеви"

#. Liberation Day.
msgid "Bevrijdingsdag"
msgstr "День визволення"

#. Ascension Day.
msgid "Hemelvaartsdag"
msgstr "Вознесіння Господнє"

#. Whit Sunday.
msgid "Eerste Pinksterdag"
msgstr "Трійця"

#. Whit Monday.
msgid "Tweede Pinksterdag"
msgstr "День Святого Духа"

#. Christmas Day.
msgid "Eerste Kerstdag"
msgstr "Різдво Христове"

#. Second Day of Christmas.
msgid "Tweede Kerstdag"
msgstr "Другий день Різдва"
