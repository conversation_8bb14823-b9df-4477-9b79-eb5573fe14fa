# Sweden holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.23\n"
"POT-Creation-Date: 2023-04-08 20:17+0300\n"
"PO-Revision-Date: 2023-04-08 20:27+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Nyårsdagen"
msgstr "Новий рік"

#. Epiphany.
msgid "Trettondedag jul"
msgstr "Богоявлення"

#. Feast of the Annunciation.
msgid "Jungfru Marie bebådelsedag"
msgstr "Благовіщення"

#. Good Friday.
msgid "Långfredagen"
msgstr "Страсна пʼятниця"

#. Easter Sunday.
msgid "Påskdagen"
msgstr "Великдень"

#. Easter Monday.
msgid "Annandag påsk"
msgstr "Великодній понеділок"

#. May Day.
msgid "Första maj"
msgstr "Перше травня"

#. Ascension Day.
msgid "Kristi himmelsfärdsdag"
msgstr "Вознесіння Господнє"

#. National Day of Sweden.
msgid "Sveriges nationaldag"
msgstr "Національний день Швеції"

#. Whit Sunday.
msgid "Pingstdagen"
msgstr "Трійця"

#. Whit Monday.
msgid "Annandag pingst"
msgstr "День Святого Духа"

#. Midsummer Eve.
msgid "Midsommarafton"
msgstr "Переддень літнього сонцестояння"

#. Midsummer Day.
msgid "Midsommardagen"
msgstr "День літнього сонцестояння"

#. All Saints' Day.
msgid "Alla helgons dag"
msgstr "День усіх святих"

#. Christmas Eve.
msgid "Julafton"
msgstr "Святий вечір"

#. Christmas Day.
msgid "Juldagen"
msgstr "Різдво Христове"

#. Second Day of Christmas.
msgid "Annandag jul"
msgstr "Другий день Різдва"

#. New Year's Eve.
msgid "Nyårsafton"
msgstr "Переддень Нового року"

#. Sunday.
msgid "Söndag"
msgstr "Неділя"
