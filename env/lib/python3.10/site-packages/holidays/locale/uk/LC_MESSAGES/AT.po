# Austria holidays uk localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-04-01 17:13+0300\n"
"PO-Revision-Date: 2023-04-01 17:27+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Neujahr"
msgstr "Новий рік"

#. Epiphany.
msgid "Heilige Drei Könige"
msgstr "Богоявлення"

#. Easter Monday.
msgid "Ostermontag"
msgstr "Великодній понеділок"

#. Labor Day.
msgid "Staatsfeiertag"
msgstr "День праці"

#. Ascension Day.
msgid "Christi Himmelfahrt"
msgstr "Вознесіння Господнє"

#. Whit Monday.
msgid "Pfingstmontag"
msgstr "День Святого Духа"

#. Corpus Christi.
msgid "Fronleichnam"
msgstr "Свято Тіла і Крові Христових"

#. Assumption Day.
msgid "Mariä Himmelfahrt"
msgstr "Внебовзяття Пресвятої Діви Марії"

#. National Day.
msgid "Nationalfeiertag"
msgstr "Національне свято"

#. All Saints' Day.
msgid "Allerheiligen"
msgstr "День усіх святих"

#. Immaculate Conception.
msgid "Mariä Empfängnis"
msgstr "Непорочне зачаття Діви Марії"

#. Christmas Day.
msgid "Christtag"
msgstr "Різдво Христове"

#. St. Stephen's Day.
msgid "Stefanitag"
msgstr "День Святого Стефана"

#. Good Friday.
msgid "Karfreitag"
msgstr "Страсна пʼятниця"

#. Christmas Eve.
msgid "Heiliger Abend"
msgstr "Святий вечір"

#. New Year's Eve.
msgid "Silvester"
msgstr "Переддень Нового року"

#. St. Martin's Day.
msgid "Hl. Martin"
msgstr "День Святого Мартина"

#. St. Joseph's Day.
msgid "Hl. Josef"
msgstr "День Святого Йосипа"

#. 1920 Carinthian plebiscite.
msgid "Tag der Volksabstimmung"
msgstr "Річниця референдуму 1920 року в Карінтії"

#. St. Leopold's Day.
msgid "Hl. Leopold"
msgstr "День Святого Леопольда"

#. St. Florian's Day.
msgid "Hl. Florian"
msgstr "День Святого Флоріана"

#. St. Rupert's Day.
msgid "Hl. Rupert"
msgstr "День Святого Руперта"
