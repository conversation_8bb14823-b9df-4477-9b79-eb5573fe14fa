# Sweden holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.23\n"
"POT-Creation-Date: 2023-04-08 20:17+0300\n"
"PO-Revision-Date: 2023-04-08 20:22+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Nyårsdagen"
msgstr ""

#. Epiphany.
msgid "Trettondedag jul"
msgstr ""

#. Feast of the Annunciation.
msgid "<PERSON><PERSON><PERSON> <PERSON> beb<PERSON>ag"
msgstr ""

#. Good Friday.
msgid "Långfredagen"
msgstr ""

#. Easter Sunday.
msgid "Påskdagen"
msgstr ""

#. Easter Monday.
msgid "Annandag påsk"
msgstr ""

#. May Day.
msgid "Första maj"
msgstr ""

#. Ascension Day.
msgid "Kristi himmelsfärdsdag"
msgstr ""

#. National Day of Sweden.
msgid "Sveriges nationaldag"
msgstr ""

#. Whit Sunday.
msgid "Pingstdagen"
msgstr ""

#. Whit Monday.
msgid "Annandag pingst"
msgstr ""

#. Midsummer Eve.
msgid "Midsommarafton"
msgstr ""

#. Midsummer Day.
msgid "Midsommardagen"
msgstr ""

#. All Saints' Day.
msgid "Alla helgons dag"
msgstr ""

#. Christmas Eve.
msgid "Julafton"
msgstr ""

#. Christmas Day.
msgid "Juldagen"
msgstr ""

#. Second Day of Christmas.
msgid "Annandag jul"
msgstr ""

#. New Year's Eve.
msgid "Nyårsafton"
msgstr ""

#. Sunday.
msgid "Söndag"
msgstr ""
