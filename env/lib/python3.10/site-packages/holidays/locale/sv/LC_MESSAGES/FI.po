# Finland holidays sv localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.23\n"
"POT-Creation-Date: 2023-04-08 19:30+0300\n"
"PO-Revision-Date: 2023-04-08 19:46+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Uudenvuodenpäivä"
msgstr "Nyårsdagen"

#. Epiphany.
msgid "Loppiainen"
msgstr "Trettondedagen"

#. Good Friday.
msgid "Pitkäperjantai"
msgstr "Långfredagen"

#. Easter Sunday.
msgid "Pääsiäispäivä"
msgstr "Påskdagen"

#. Easter Monday.
msgid "2. pääsiäispäivä"
msgstr "Annandag påsk"

#. May Day.
msgid "Vappu"
msgstr "Vappen"

#. Ascension Day.
msgid "Helatorstai"
msgstr "Kristi himmelfärdsdag"

#. Whit Sunday.
msgid "Helluntaipäivä"
msgstr "Pingst"

#. Midsummer Eve.
msgid "Juhannusaatto"
msgstr "Midsommarafton"

#. Midsummer Day.
msgid "Juhannuspäivä"
msgstr "Midsommardagen"

#. All Saints' Day.
msgid "Pyhäinpäivä"
msgstr "Alla helgons dag"

#. Independence Day.
msgid "Itsenäisyyspäivä"
msgstr "Självständighetsdagen"

#. Christmas Eve.
msgid "Jouluaatto"
msgstr "Julafton"

#. Christmas Day.
msgid "Joulupäivä"
msgstr "Juldagen"

#. Second Day of Christmas.
msgid "Tapaninpäivä"
msgstr "Annandag jul"
