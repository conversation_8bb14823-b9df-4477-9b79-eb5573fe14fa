# Chinese holidays zh_TW localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-09-28 19:23+0700\n"
"PO-Revision-Date: 2024-01-18 11:43+0700\n"
"Last-Translator: PPsyrius <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4.2\n"

#. %s (observed).
#, c-format
msgid "%s（观察日）"
msgstr "%s（觀察日）"

#. New Year's Day.
msgid "元旦"
msgstr "元旦"

#. National Day.
msgid "国庆节"
msgstr "國慶日"

#. Mid-Autumn Festival.
msgid "中秋节"
msgstr "中秋節"

#. Chinese New Year (Spring Festival).
msgid "春节"
msgstr "春節"

#. Chinese New Year's Eve.
msgid "农历除夕"
msgstr "農曆除夕"

#. Labor Day.
msgid "劳动节"
msgstr "勞動節"

#. Tomb-Sweeping Day.
msgid "清明节"
msgstr "清明節"

#. Dragon Boat Festival.
msgid "端午节"
msgstr "端午節"

#. International Women's Day.
msgid "国际妇女节"
msgstr "國際婦女節"

#. Youth Day.
msgid "五四青年节"
msgstr "五四青年節"

#. Children's Day.
msgid "六一儿童节"
msgstr "六一兒童節"

#. Army Day.
msgid "建军节"
msgstr "建軍節"

#. Date format (see strftime() Format Codes).
msgid "%Y-%m-%d"
msgstr "%Y-%m-%d"

#. Day off (substituted from %s).
#, c-format
msgid "休息日（%s日起取代）"
msgstr "休息日（%s日起取代）"

#. 70th Anniversary of the Victory of the Chinese People’s War of Resistance
#. against Japanese
#. Aggression and the World Anti-Fascist War.
msgid "中国人民抗日战争暨世界反法西斯战争胜利70周年纪念日"
msgstr "中國人民抗日戰爭暨世界反法西斯戰爭勝利70週年紀念日"

#. Chinese New Year (Spring Festival) Extended Holiday.
msgid "春节延长假期"
msgstr "春節延長假期"
