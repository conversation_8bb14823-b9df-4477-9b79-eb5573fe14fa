# Belgium holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.33\n"
"POT-Creation-Date: 2023-06-27 13:17+0300\n"
"PO-Revision-Date: 2023-09-06 20:46+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Nieuwjaar"
msgstr ""

#. Easter Sunday.
msgid "Pasen"
msgstr ""

#. Easter Monday.
msgid "Paasmaandag"
msgstr ""

#. Labor Day.
msgid "<PERSON><PERSON> <PERSON> de Arbeid"
msgstr ""

#. Ascension Day.
msgid "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>"
msgstr ""

#. Whit Sunday.
msgid "Pinksteren"
msgstr ""

#. Whit Monday.
msgid "Pinkstermaandag"
msgstr ""

#. National Day.
msgid "Nationale feestdag"
msgstr ""

#. Assumption Day.
msgid "O. L. V. Hemelvaart"
msgstr ""

#. All Saints' Day.
msgid "Allerheiligen"
msgstr ""

#. Armistice Day.
msgid "Wapenstilstand"
msgstr ""

#. Christmas Day.
msgid "Kerstmis"
msgstr ""

#. Good Friday.
msgid "Goede Vrijdag"
msgstr ""

#. Friday after Ascension Day.
msgid "Vrijdag na O. L. H. Hemelvaart"
msgstr ""

#. Bank Holiday.
msgid "Banksluitingsdag"
msgstr ""
