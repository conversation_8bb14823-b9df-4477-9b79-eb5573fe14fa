# Netherlands holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.23\n"
"POT-Creation-Date: 2023-04-09 18:11+0300\n"
"PO-Revision-Date: 2023-04-09 18:13+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Nieuwjaarsdag"
msgstr ""

#. Good Friday.
msgid "Goede Vrijdag"
msgstr ""

#. Easter Sunday.
msgid "Eerste paasdag"
msgstr ""

#. Easter Monday.
msgid "Tweede paasdag"
msgstr ""

#. King's Day.
msgid "Koningsdag"
msgstr ""

#. Queen's Day.
msgid "Koninginnedag"
msgstr ""

#. Liberation Day.
msgid "Bevrijdingsdag"
msgstr ""

#. Ascension Day.
msgid "Hemelvaartsdag"
msgstr ""

#. Whit Sunday.
msgid "Eerste Pinksterdag"
msgstr ""

#. Whit Monday.
msgid "Tweede Pinksterdag"
msgstr ""

#. Christmas Day.
msgid "Eerste Kerstdag"
msgstr ""

#. Second Day of Christmas.
msgid "Tweede Kerstdag"
msgstr ""
