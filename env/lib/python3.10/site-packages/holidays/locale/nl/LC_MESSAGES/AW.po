# Aruba holidays nl localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.27\n"
"POT-Creation-Date: 2023-06-09 23:12+0700\n"
"PO-Revision-Date: \n"
"Last-Translator: PPsyrius <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.3.1\n"

#. New Year's Day
msgid "Aña Nobo"
msgstr "Nieuwjaarsdag"

#. Betico Day
msgid "Dia di Betico"
msgstr "Beticodag"

#. Carnival Monday
msgid "Dialuna despues di Carnaval Grandi"
msgstr "Carnavalsmaandag"

#. Monday before Ash Wednesday
msgid "Dialuna prome cu diaranson di shinish"
msgstr "Maandag voor Aswoensdag"

#. National Anthem and Flag Day
msgid "Dia di Himno y Bandera"
msgstr "Nationale vlag en volkslied"

#. Good Friday
msgid "Bierna Santo"
msgstr "Goede vrijdag"

#. Easter Monday
msgid "Di dos dia di Pasco di Resureccion"
msgstr "Tweede paasdag"

#. King's Day.
msgid "Dia di Rey"
msgstr "Koningsdag"

#. King's Day.
msgid "Aña di Rey"
msgstr "Koningsdag"

#. Queen's Day.
msgid "Aña di La Reina"
msgstr "Koninginnedag"

#. Labor Day
msgid "Dia di Obrero"
msgstr "Dag van de arbeid"

#. Ascension Day
msgid "Dia di Asuncion"
msgstr "Hemelvaartsdag"

#. Christmas Day
msgid "Pasco di Nacemento"
msgstr "Kerst"

#. Second Day of Christmas
msgid "Di dos dia di Pasco di Nacemento"
msgstr "Tweede kerstdag"
