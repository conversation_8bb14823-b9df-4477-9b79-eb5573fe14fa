# Lithuania holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.28\n"
"POT-Creation-Date: 2023-06-27 13:17+0300\n"
"PO-Revision-Date: 2023-06-27 13:39+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: lt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && (n%100<11 || n%100>19) ? 0 : n%10>=2 && n%10<=9 && (n%100<11 || n%100>19) ? 1 : 2);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Nauj<PERSON><PERSON><PERSON> metų diena"
msgstr ""

#. Day of Restoration of the State of Lithuania.
msgid "Lietuvos valstybės atkūrimo diena"
msgstr ""

#. Day of Restoration of Independence of Lithuania.
msgid "Lietuvos nepriklausomybės atkūrimo diena"
msgstr ""

#. Easter Sunday.
msgid "Šv. Velykos"
msgstr ""

#. Easter Monday.
msgid "Antroji šv. Velykų diena"
msgstr ""

#. International Workers' Day.
msgid "Tarptautinė darbo diena"
msgstr ""

#. Day of Dew and Saint John.
msgid "Rasos ir Joninių diena"
msgstr ""

#. Statehood Day.
msgid ""
"Valstybės (Lietuvos karaliaus Mindaugo karūnavimo) ir Tautiškos giesmės "
"diena"
msgstr ""

#. Assumption Day.
msgid "Žolinė (Švč. Mergelės Marijos ėmimo į dangų diena)"
msgstr ""

#. All Saints' Day.
msgid "Visų Šventųjų diena"
msgstr ""

#. All Souls' Day.
msgid "Mirusiųjų atminimo (Vėlinių) diena"
msgstr ""

#. Christmas Eve.
msgid "Kūčių diena"
msgstr ""

#. Christmas Day.
msgid "Šv. Kalėdų pirma diena"
msgstr ""

#. Second Day of Christmas.
msgid "Šv. Kalėdų antra diena"
msgstr ""

#. Mother's Day.
msgid "Motinos diena"
msgstr ""

#. Father's Day.
msgid "Tėvo diena"
msgstr ""
