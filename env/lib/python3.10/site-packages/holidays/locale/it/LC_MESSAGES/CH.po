# Switzerland holidays it localization.
# Authors: <AUTHORS>
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-04-10 15:41+0300\n"
"PO-Revision-Date: 2024-01-21 14:48+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Neujahrestag"
msgstr "Capodanno"

#. Ascension Day.
msgid "Auffahrt"
msgstr "Ascensione di Gesù"

#. National Day.
msgid "Nationalfeiertag"
msgstr "Festa nazionale"

#. Christmas Day.
msgid "Weihnachten"
msgstr "Natale"

#. Good Friday.
msgid "Karfreitag"
msgstr "Venerdì Santo"

#. Easter Monday.
msgid "Ostermontag"
msgstr "Lunedì dell'Angelo"

#. Whit Monday.
msgid "Pfingstmontag"
msgstr "Lunedì di Pentecoste"

#. St. Stephen's Day.
msgid "Stephanstag"
msgstr "Giorno di Santo Stefano"

#. Berchtold's Day.
msgid "Berchtoldstag"
msgstr "Giorno di Bertoldo"

#. Corpus Christi.
msgid "Fronleichnam"
msgstr "Corpus Domini"

#. Assumption Day.
msgid "Mariä Himmelfahrt"
msgstr "Assunzione di Maria"

#. All Saints' Day.
msgid "Allerheiligen"
msgstr "Ognissanti"

#. Immaculate Conception.
msgid "Mariä Empfängnis"
msgstr "Immacolata Concezione"

#. Labor Day.
msgid "Tag der Arbeit"
msgstr "Festa del lavoro"

#. Genevan Fast.
msgid "Genfer Bettag"
msgstr "Jeûne genevois"

#. Restoration Day.
msgid "Wiederherstellung der Republik"
msgstr "Restauration genevoise"

#. Battle of Naefels Victory Day.
msgid "Näfelser Fahrt"
msgstr "Battaglia di Näfels"

#. Independence Day.
msgid "Fest der Unabhängigkeit"
msgstr "Festa dell'Indipendenza"

#. Republic Day.
msgid "Jahrestag der Ausrufung der Republik"
msgstr "Giorno della Repubblica"

#. St. Joseph's Day.
msgid "Josefstag"
msgstr "San Giuseppe"

#. St. Nicholas of Flüe.
msgid "Bruder Klaus"
msgstr "Nicolao della Flüe"

#. Epiphany.
msgid "Heilige Drei Könige"
msgstr "Epifania"

#. Saints Peter and Paul.
msgid "Peter und Paul"
msgstr "Santi Pietro e Paolo"

#. Prayer Monday.
msgid "Bettagsmontag"
msgstr "Digiuno Ginevrino"
