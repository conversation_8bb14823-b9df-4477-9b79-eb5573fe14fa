# Malta holidays.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.28\n"
"POT-Creation-Date: 2023-06-27 19:10+0700\n"
"PO-Revision-Date: \n"
"Last-Translator: PPsyrius <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: mt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : n==0 || (n%100>=2 && n%100<=10) ? 1 : n%100>=11 && n%100<=19 ? 2 : 3);\n"
"X-Generator: Poedit 3.3.2\n"

#. New Year's Day.
msgid "L-Ewwel tas-Sena"
msgstr ""

#. Feast of St. Paul's Shipwreck.
msgid "Il-Festa tan-Nawfraġju ta' San Pawl"
msgstr ""

#. Feast of St. Joseph.
msgid "Il-Festa ta' San Ġużepp"
msgstr ""

#. Freedom Day.
msgid "Jum il-Ħelsien"
msgstr ""

#. Good Friday.
msgid "Il-Ġimgħa l-Kbira"
msgstr ""

#. Worker's Day.
msgid "Jum il-Ħaddiem"
msgstr ""

#. Sette Giugno.
msgid "Sette Giugno"
msgstr ""

#. Feast of St. Peter and St. Paul.
msgid "Il-Festa ta' San Pietru u San Pawl"
msgstr ""

#. Feast of the Assumption.
msgid "Il-Festa ta' Santa Marija"
msgstr ""

#. Feast of Our Lady of Victories.
msgid "Jum il-Vitorja"
msgstr ""

#. Independence Day.
msgid "Jum l-Indipendenza"
msgstr ""

#. Feast of the Immaculate Conception
msgid "Il-Festa tal-Immakulata Kunċizzjoni"
msgstr ""

#. Republic Day.
msgid "Jum ir-Repubblika"
msgstr ""

#. Christmas Day.
msgid "Il-Milied"
msgstr ""
