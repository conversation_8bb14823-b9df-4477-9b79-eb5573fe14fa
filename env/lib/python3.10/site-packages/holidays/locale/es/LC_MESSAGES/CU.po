# Cuba holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.22\n"
"POT-Creation-Date: 2023-03-04 14:20+0200\n"
"PO-Revision-Date: 2023-03-05 12:42+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. %s (observed).
#, c-format
msgid "%s (observado)"
msgstr ""

#. Liberation Day.
msgid "Triunfo de la Revolución"
msgstr ""

#. Victory Day.
msgid "Día de la Victoria"
msgstr ""

#. Good Friday.
msgid "Viernes Santo"
msgstr ""

#. International Worker's Day.
msgid "Día Internacional de los Trabajadores"
msgstr ""

#. Commemoration of the Assault of the Moncada garrison.
msgid "Conmemoración del asalto a Moncada"
msgstr ""

#. Day of the National Rebellion.
msgid "Día de la Rebeldía Nacional"
msgstr ""

#. Independence Day.
msgid "Inicio de las Guerras de Independencia"
msgstr ""

#. Christmas Day.
msgid "Día de Navidad"
msgstr ""

#. New Year's Eve.
msgid "Fiesta de Fin de Año"
msgstr ""
