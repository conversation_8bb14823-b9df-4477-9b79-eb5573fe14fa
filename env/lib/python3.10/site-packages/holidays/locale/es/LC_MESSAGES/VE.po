# Venezuela holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.22\n"
"POT-Creation-Date: 2023-03-05 12:23+0200\n"
"PO-Revision-Date: 2023-03-05 12:44+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr ""

#. Monday of Carnival.
msgid "Lunes de Carnaval"
msgstr ""

#. Tuesday of Carnival.
msgid "Martes de Carnaval"
msgstr ""

#. Maundy Thursday.
msgid "Jueves Santo"
msgstr ""

#. Good Friday.
msgid "Viernes Santo"
msgstr ""

#. Declaration of Independence.
msgid "Declaración de la Independencia"
msgstr ""

#. International Worker's Day.
msgid "Dia Mundial del Trabajador"
msgstr ""

#. Battle of Carabobo.
msgid "Batalla de Carabobo"
msgstr ""

#. Independence Day.
msgid "Día de la Independencia"
msgstr ""

#. Birthday of Simon Bolivar.
msgid "Natalicio de Simón Bolívar"
msgstr ""

#. Day of Indigenous Resistance.
msgid "Día de la Resistencia Indígena"
msgstr ""

#. Columbus Day.
msgid "Día de la Raza"
msgstr ""

#. Unknown Holiday.
msgid "Día Festivo Desconocido"
msgstr ""

#. Christmas Eve.
msgid "Nochebuena"
msgstr ""

#. Christmas Day.
msgid "Día de Navidad"
msgstr ""

#. New Year's Eve.
msgid "Fiesta de Fin de Año"
msgstr ""
