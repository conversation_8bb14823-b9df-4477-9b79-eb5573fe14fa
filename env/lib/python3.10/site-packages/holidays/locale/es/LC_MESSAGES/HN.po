# Honduras holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.25\n"
"POT-Creation-Date: 2023-03-05 12:23+0200\n"
"PO-Revision-Date: 2023-03-05 12:44+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr ""

#. Maundy Thursday.
msgid "Jueves Santo"
msgstr ""

#. Good Friday.
msgid "Viernes Santo"
msgstr ""

#. Holy Saturday.
msgid "Sábado de Gloria"
msgstr ""

#. Panamerican Day.
msgid "Día de las Américas"
msgstr ""

#. Labor Day.
msgid "Día del Trabajo"
msgstr ""

#. Morazan's Day.
msgid "Día de Morazán"
msgstr ""

#. Columbus Day.
msgid "Día de la Raza"
msgstr ""

#. Army Day.
msgid "Día de las Fuerzas Armadas"
msgstr ""

#. Morazan Weekend.
msgid "Semana Morazánica"
msgstr ""

#. Christmas Day.
msgid "Navidad"
msgstr ""

#. Independence Day.
msgid "Día de la Independencia"
msgstr ""
