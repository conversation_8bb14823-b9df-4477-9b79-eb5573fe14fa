# Ecuador holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.41\n"
"POT-Creation-Date: 2023-04-02 18:54+0300\n"
"PO-Revision-Date: 2023-04-02 18:55+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr ""

#. Carnival.
msgid "Carnaval"
msgstr ""

#. Good Friday.
msgid "Viernes Santo"
msgstr ""

#. Labor Day.
msgid "Día del Trabajo"
msgstr ""

#. The Battle of Pichincha.
msgid "Batalla de Pichincha"
msgstr ""

#. Declaration of Independence of Quito.
msgid "Primer Grito de Independencia"
msgstr ""

#. Independence of Guayaquil.
msgid "Independencia de Guayaquil"
msgstr ""

#. All Souls' Day.
msgid "Día de los Difuntos"
msgstr ""

#. Independence of Cuenca.
msgid "Independencia de Cuenca"
msgstr ""

#. Christmas Day.
msgid "Día de Navidad"
msgstr ""

#. %s (observed).
#, c-format
msgid "%s (observado)"
msgstr ""
