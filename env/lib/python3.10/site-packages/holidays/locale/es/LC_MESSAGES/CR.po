# Costa Rica holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.41\n"
"POT-Creation-Date: 2023-03-22 22:55+0200\n"
"PO-Revision-Date: 2023-05-03 18:39-0700\n"
"Last-Translator: Arkadii Yakov<PERSON> <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.3\n"

#. %s (observed).
#, c-format
msgid "%s (observado)"
msgstr ""

#. New Year's Day.
msgid "Año Nuevo"
msgstr ""

#. Maundy Thursday.
msgid "Jueves Santo"
msgstr ""

#. Good Friday.
msgid "Viernes Santo"
msgstr ""

#. Juan <PERSON> Day.
msgid "Día de Juan Santamaría"
msgstr ""

#. International Labor Day.
msgid "Día Internacional del Trabajo"
msgstr ""

#. Annexation of the Party of Nicoya to Costa Rica.
msgid "Anexión del Partido de Nicoya a Costa Rica"
msgstr ""

#. Feast of Our Lady of the Angels.
msgid "Fiesta de Nuestra Señora de los Ángeles"
msgstr ""

#. Mother's Day.
msgid "Día de la Madre"
msgstr ""

#. Day of the Black Person and Afro-Costa Rican Culture.
msgid "Día de la Persona Negra y la Cultura Afrocostarricense"
msgstr ""

#. Independence Day.
msgid "Día de la Independencia"
msgstr ""

#. Cultures Day.
msgid "Día de las Culturas"
msgstr ""

#. Army Abolition Day.
msgid "Día de la Abolición del Ejército"
msgstr ""

#. Christmas Day.
msgid "Navidad"
msgstr ""
