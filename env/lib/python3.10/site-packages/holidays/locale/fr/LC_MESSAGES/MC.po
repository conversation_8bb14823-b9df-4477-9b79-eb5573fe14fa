# Monaco holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.41\n"
"POT-Creation-Date: 2023-02-20 11:58+0200\n"
"PO-Revision-Date: 2023-02-20 12:00+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. Public holiday.
msgid "Jour férié"
msgstr ""

#. %s (observed).
#, c-format
msgid "%s (observé)"
msgstr ""

#. New Year's Day.
msgid "Le jour de l'An"
msgstr ""

#. <PERSON> Devote's Day.
msgid "La Sainte Dévote"
msgstr ""

#. Easter Monday.
msgid "Le lundi de Pâques"
msgstr ""

#. Labor Day.
msgid "Fête de la Travaille"
msgstr ""

#. Ascension Day.
msgid "L'Ascension"
msgstr ""

#. Whit Monday.
msgid "Le lundi de Pentecôte"
msgstr ""

#. Corpus Christi.
msgid "La Fête Dieu"
msgstr ""

#. Assumption Day.
msgid "L'Assomption de Marie"
msgstr ""

#. All Saints' Day.
msgid "La Toussaint"
msgstr ""

#. Prince's Day.
msgid "La Fête du Prince"
msgstr ""

#. Immaculate Conception.
msgid "L'Immaculée Conception"
msgstr ""

#. Christmas Day.
msgid "Noël"
msgstr ""
