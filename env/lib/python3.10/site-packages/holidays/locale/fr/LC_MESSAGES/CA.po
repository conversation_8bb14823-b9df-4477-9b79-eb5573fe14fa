# Canada holidays fr localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.32\n"
"POT-Creation-Date: 2023-04-10 14:10+0300\n"
"PO-Revision-Date: 2023-08-26 18:23+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. %s (observed).
#, c-format
msgid "%s (observed)"
msgstr "%s (Observé)"

#. New Year's Day.
msgid "New Year's Day"
msgstr "Jour de l'an"

#. Good Friday.
msgid "Good Friday"
msgstr "Vendredi saint"

#. Easter Monday.
msgid "Easter Monday"
msgstr "Lundi de Pâ<PERSON>"

#. Dominion Day.
msgid "Dominion Day"
msgstr "Jour du dominion"

#. Labor Day.
msgid "Labour Day"
msgstr "Fête du Travail"

#. Christmas Day.
msgid "Christmas Day"
msgstr "Jour de Noël"

#. Boxing Day.
msgid "Boxing Day"
msgstr "Boxing Day"

#. Family Day.
msgid "Family Day"
msgstr "Fête de la famille"

#. Thanksgiving Day.
msgid "Thanksgiving Day"
msgstr "Action de grâce"

#. Funeral of Queen Elizabeth II.
msgid "Funeral of Her Majesty the Queen Elizabeth II"
msgstr "Funéraire de sa majesté la reine Elizabeth II"

#. Memorial Day.
msgid "Memorial Day"
msgstr "Jour de mémorial"

#. Canada Day.
msgid "Canada Day"
msgstr "Fête du Canada"

#. Victoria Day.
msgid "Victoria Day"
msgstr "Fête de la Reine"

#. Heritage Day.
msgid "Heritage Day"
msgstr "Fête du Patrimoine"

#. Remembrance Day.
msgid "Remembrance Day"
msgstr "Jour du Souvenir"

#. British Columbia Day.
msgid "British Columbia Day"
msgstr "Jour de la Colombie Britannique"

#. National Day for Truth and Reconciliation.
msgid "National Day for Truth and Reconciliation"
msgstr "Journée nationale de la vérité et de la réconciliation"

#. Louis Riel Day.
msgid "Louis Riel Day"
msgstr "Journée Louis Riel"

#. Terry Fox Day.
msgid "Terry Fox Day"
msgstr "Journée Terry Fox"

#. Civic Holiday.
msgid "Civic Holiday"
msgstr "Premier lundi d'août"

#. New Brunswick Day.
msgid "New Brunswick Day"
msgstr "Jour du Nouveau Brunswick"

#. St. Patrick's Day.
msgid "St. Patrick's Day"
msgstr "Fête de la Saint-Patrick"

#. St. George's Day.
msgid "St. George's Day"
msgstr "Fête de la Saint-Georges"

#. Discovery Day.
msgid "Discovery Day"
msgstr "Jour de la Découverte"

#. National Aboriginal Day.
msgid "National Aboriginal Day"
msgstr "Journée nationale des Autochtones"

#. Nunavut Day.
msgid "Nunavut Day"
msgstr "Jour du Nunavut"

#. Islander Day.
msgid "Islander Day"
msgstr "Fête des Insulaires"

#. National Patriots' Day.
msgid "National Patriots' Day"
msgstr "Journée nationale des patriotes"

#. St. Jean Baptiste Day.
msgid "St. Jean Baptiste Day"
msgstr "Fête nationale du Québec"

#. Saskatchewan Day.
msgid "Saskatchewan Day"
msgstr "Jour du Saskatchewan"

#. Orangemen's Day.
msgid "Orangemen's Day"
msgstr "Journée des Orangistes"

#. Natal Day.
msgid "Natal Day"
msgstr "Jour de la Fondation"
