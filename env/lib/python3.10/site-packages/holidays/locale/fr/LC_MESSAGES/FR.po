# France holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.22\n"
"POT-Creation-Date: 2023-03-23 14:44+0200\n"
"PO-Revision-Date: 2023-03-23 14:45+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Jour de l'an"
msgstr ""

#. Labor Day.
msgid "Fête du Travail"
msgstr ""

#. Labor and Social Concord Day.
msgid "Fête du Travail et de la Concorde sociale"
msgstr ""

#. Victory Day.
msgid "Fête de la Victoire"
msgstr ""

#. National Day.
msgid "Fête nationale"
msgstr ""

#. Armistice Day.
msgid "Armistice"
msgstr ""

#. Good Friday.
msgid "Vendredi saint"
msgstr ""

#. Mi-Careme.
msgid "Mi-Carême"
msgstr ""

#. Easter Monday.
msgid "Lundi de Pâques"
msgstr ""

#. Whit Monday.
msgid "Lundi de Pentecôte"
msgstr ""

#. Ascension Day.
msgid "Ascension"
msgstr ""

#. Assumption Day.
msgid "Assomption"
msgstr ""

#. All Saints' Day.
msgid "Toussaint"
msgstr ""

#. Christmas Day.
msgid "Noël"
msgstr ""

#. Feast of Victor Schoelcher.
msgid "Fête de Victor Schoelcher"
msgstr ""

#. Abolition of slavery.
msgid "Abolition de l'esclavage"
msgstr ""

#. Saint Stephen's Day.
msgid "Saint Étienne"
msgstr ""

#. Citizenship Day.
msgid "Fête de la Citoyenneté"
msgstr ""

#. Missionary Day.
msgid "Arrivée de l'Évangile"
msgstr ""

#. Internal Autonomy Day.
msgid "Fête de l'autonomie"
msgstr ""

#. Feast of Saint Peter Chanel.
msgid "Saint Pierre Chanel"
msgstr ""

#. Festival of the territory.
msgid "Fête du Territoire"
msgstr ""
