# Israel holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.39\n"
"POT-Creation-Date: 2023-07-18 17:31+0300\n"
"PO-Revision-Date: 2023-07-18 17:57+0300\n"
"Last-Translator: Arkadii Yakovets <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: he\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. Rosh <PERSON> (New Year).
msgid "ראש השנה"
msgstr ""

#. Yom <PERSON> (Day of Atonement).
msgid "יום כיפור"
msgstr ""

#. Sukkot (Feast of Tabernacles).
msgid "סוכות"
msgstr ""

#. <PERSON>m<PERSON><PERSON> Torah / Shemini Atzeret.
msgid "שמחת תורה/שמיני עצרת"
msgstr ""

#. P<PERSON>ach (Passover).
msgid "פסח"
msgstr ""

#. Shvi'i shel Pesach (Seventh day of Passover)
msgid "שביעי של פסח"
msgstr ""

#. Yom Ha-Atzmaut (Independence Day).
msgid "יום העצמאות"
msgstr ""

#. Shavuot.
msgid "שבועות"
msgstr ""

#. Chol HaMoed Sukkot (Feast of Tabernacles holiday).
msgid "חול המועד סוכות"
msgstr ""

#. Sigd.
msgid "סיגד"
msgstr ""

#. Purim.
msgid "פורים"
msgstr ""

#. Chol HaMoed Pesach (Passover holiday).
msgid "חול המועד פסח"
msgstr ""

#. Yom Hazikaron (Fallen Soldiers and Victims of Terrorism Remembrance Day).
msgid "יום הזיכרון לחללי מערכות ישראל ונפגעי פעולות האיבה"
msgstr ""

#. Yom Yerushalayim (Jerusalem Day).
msgid "יום ירושלים"
msgstr ""

#. Tisha B'Av (Tisha B'Av, fast).
msgid "תשעה באב"
msgstr ""

#. Hanukkah.
msgid "חנוכה"
msgstr ""

#. Ta`anit Ester (Fast of Esther).
msgid "תענית אסתר"
msgstr ""

#. Lag Ba'omer (Lag BaOmer).
msgid "ל\"ג בעומר"
msgstr ""

#. %s (observed).
#, c-format
msgid "(נצפה) %s"
msgstr ""
