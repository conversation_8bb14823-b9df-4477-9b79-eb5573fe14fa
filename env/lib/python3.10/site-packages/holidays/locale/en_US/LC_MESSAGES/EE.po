# Estonia holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.28\n"
"POT-Creation-Date: 2023-06-27 13:17+0300\n"
"PO-Revision-Date: 2023-06-27 13:47+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "uusaasta"
msgstr "New Year's Day"

#. Independence Day.
msgid "iseseisvuspäev"
msgstr "Independence Day"

#. Good Friday.
msgid "suur reede"
msgstr "Good Friday"

#. Easter Sunday.
msgid "ülestõusmispühade 1. püha"
msgstr "Easter Sunday"

#. Spring Day.
msgid "kevadpüha"
msgstr "Spring Day"

#. Whit Sunday.
msgid "nelipühade 1. püha"
msgstr "Whit Sunday"

#. Victory Day.
msgid "võidupüha"
msgstr "Victory Day"

#. Midsummer Day.
msgid "jaanipäev"
msgstr "Midsummer Day"

#. Independence Restoration Day.
msgid "taasiseseisvumispäev"
msgstr "Independence Restoration Day"

#. Christmas Eve.
msgid "jõululaupäev"
msgstr "Christmas Eve"

#. Christmas Day.
msgid "esimene jõulupüha"
msgstr "Christmas Day"

#. Second Day of Christmas.
msgid "teine jõulupüha"
msgstr "Second Day of Christmas"
