# Cuba holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-03-04 14:20+0200\n"
"PO-Revision-Date: 2024-01-23 21:01+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.4\n"

#. %s (observed).
#, c-format
msgid "%s (observado)"
msgstr "%s (observed)"

#. Liberation Day.
msgid "Triunfo de la Revolución"
msgstr "Liberation Day"

#. Victory Day.
msgid "Día de la Victoria"
msgstr "Victory Day"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Good Friday"

#. International Worker's Day.
msgid "Día Internacional de los Trabajadores"
msgstr "International Worker's Day"

#. Commemoration of the Assault of the Moncada garrison.
msgid "Conmemoración del asalto a Moncada"
msgstr "Commemoration of the Assault of the Moncada garrison"

#. Day of the National Rebellion.
msgid "Día de la Rebeldía Nacional"
msgstr "Day of the National Rebellion"

#. Independence Day.
msgid "Inicio de las Guerras de Independencia"
msgstr "Independence Day"

#. Christmas Day.
msgid "Día de Navidad"
msgstr "Christmas Day"

#. New Year's Eve.
msgid "Fiesta de Fin de Año"
msgstr "New Year's Eve"
