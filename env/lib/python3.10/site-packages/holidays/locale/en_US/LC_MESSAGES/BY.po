# Belarus holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.34\n"
"POT-Creation-Date: 2023-02-15 20:06-0800\n"
"PO-Revision-Date: 2023-09-27 18:50+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. Date format (see strftime() Format Codes)
msgid "%d.%m.%Y"
msgstr "%m/%d/%Y"

#. Day off (substituted from %s).
#, c-format
msgid "Выходны (перанесены з %s)"
msgstr "Day off (substituted from %s)"

#. New Year's Day.
msgid "Новы год"
msgstr "New Year's Day"

#. Orthodox Christmas Day.
msgid "Нараджэнне Хрыстова (праваслаўнае Раство)"
msgstr "Orthodox Christmas Day"

#. Women's Day.
msgid "Дзень жанчын"
msgstr "Women's Day"

#. Radunitsa (Day of Rejoicing).
msgid "Радаўніца"
msgstr "Radunitsa"

#. Labor Day.
msgid "Свята працы"
msgstr "Labor Day"

#. Victory Day.
msgid "Дзень Перамогі"
msgstr "Victory Day"

#. Independence Day.
msgid "Дзень Незалежнасці Рэспублікі Беларусь (Дзень Рэспублікі)"
msgstr "Independence Day (Republic Day)"

#. October Revolution Day.
msgid "Дзень Кастрычніцкай рэвалюцыі"
msgstr "October Revolution Day"

#. Catholic Christmas Day.
msgid "Нараджэнне Хрыстова (каталіцкае Раство)"
msgstr "Catholic Christmas Day"
