# United Arab Emirates holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-06-28 00:13+0100\n"
"PO-Revision-Date: 2024-01-25 15:04+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4\n"

#. %s (estimated).
#, c-format
msgid "(تقدير) %s"
msgstr "%s (estimated)"

#. New Year's Day.
msgid "رأس السنة الميلادية"
msgstr "New Year's Day"

#. Commemoration Day.
msgid "يوم الشهيد"
msgstr "Commemoration Day"

#. National Day.
msgid "اليوم الوطني"
msgstr "National Day"

#. Eid al-Fitr.
msgid "عيد الفطر"
msgstr "Eid al-Fitr"

#. Eid al-Fitr Holiday.
msgid "عطلة عيد الفطر"
msgstr "Eid al-Fitr Holiday"

#. Arafat Day.
msgid "وقفة عرفة"
msgstr "Arafat Day"

#. Eid al-Adha.
msgid "عيد الأضحى"
msgstr "Eid al-Adha"

#. Eid al-Adha Holiday.
msgid "عطلة عيد الأضحى"
msgstr "Eid al-Adha Holiday"

#. Islamic New Year.
msgid "رأس السنة الهجرية"
msgstr "Islamic New Year"

#. Isra' and Mi'raj.
msgid "ليلة المعراج"
msgstr "Isra' and Mi'raj"

#. Prophet's Birthday.
msgid "عيد المولد النبوي"
msgstr "Prophet's Birthday"
