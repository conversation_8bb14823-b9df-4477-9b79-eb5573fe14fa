# Belgium holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.41\n"
"POT-Creation-Date: 2023-06-27 13:17+0300\n"
"PO-Revision-Date: 2024-01-07 15:45+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Nieuwjaar"
msgstr "New Year's Day"

#. Easter Sunday.
msgid "Pasen"
msgstr "Easter Sunday"

#. Easter Monday.
msgid "Paasmaandag"
msgstr "Easter Monday"

#. Labor Day.
msgid "<PERSON><PERSON> <PERSON>"
msgstr "Labor Day"

#. Ascension Day.
msgid "O<PERSON> <PERSON><PERSON> <PERSON><PERSON>"
msgstr "Ascension Day"

#. Whit Sunday.
msgid "Pinksteren"
msgstr "Whit Sunday"

#. Whit Monday.
msgid "Pinkstermaandag"
msgstr "Whit Monday"

#. National Day.
msgid "Nationale feestdag"
msgstr "National Day"

#. Assumption Day.
msgid "O. L. V. Hemelvaart"
msgstr "Assumption Day"

#. All Saints' Day.
msgid "Allerheiligen"
msgstr "All Saints' Day"

#. Armistice Day.
msgid "Wapenstilstand"
msgstr "Armistice Day"

#. Christmas Day.
msgid "Kerstmis"
msgstr "Christmas Day"

#. Good Friday.
msgid "Goede Vrijdag"
msgstr "Good Friday"

#. Friday after Ascension Day.
msgid "Vrijdag na O. L. H. Hemelvaart"
msgstr "Friday after Ascension Day"

#. Bank Holiday.
msgid "Banksluitingsdag"
msgstr "Bank Holiday"
