# Denmark holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.37\n"
"POT-Creation-Date: 2023-02-15 08:13-0800\n"
"PO-Revision-Date: 2023-11-12 16:36+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Nytårsdag"
msgstr "New Year's Day"

#. Maundy Thursday.
msgid "Skærtorsdag"
msgstr "Maundy Thursday"

#. Good Friday.
msgid "Langfredag"
msgstr "Good Friday"

#. Easter Sunday.
msgid "Påskedag"
msgstr "Easter Sunday"

#. Easter Monday.
msgid "Anden påskedag"
msgstr "Easter Monday"

#. Great Day of Prayers.
msgid "Store bededag"
msgstr "Great Prayer Day"

#. Ascension Day.
msgid "Kristi himmelfartsdag"
msgstr "Ascension Day"

#. Whit Sunday.
msgid "Pinsedag"
msgstr "Whit Sunday"

#. Whit Monday.
msgid "Anden pinsedag"
msgstr "Whit Monday"

#. Christmas Day.
msgid "Juledag"
msgstr "Christmas Day"

#. Second Day of Christmas.
msgid "Anden juledag"
msgstr "Second Day of Christmas"

#. International Workers' Day.
msgid "Arbejdernes kampdag"
msgstr "International Workers' Day"

#. Constitution Day.
msgid "Grundlovsdag"
msgstr "Constitution Day"

#. Christmas Eve.
msgid "Juleaftensdag"
msgstr "Christmas Eve"

#. New Year's Eve.
msgid "Nytårsaften"
msgstr "New Year's Eve"
