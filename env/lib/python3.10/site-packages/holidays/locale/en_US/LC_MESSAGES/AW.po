# Aruba holidays en_US localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.27\n"
"POT-Creation-Date: 2023-06-09 23:12+0700\n"
"PO-Revision-Date: \n"
"Last-Translator: PPsyrius <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.3.1\n"

#. New Year's Day
msgid "Aña Nobo"
msgstr "New Year's Day"

#. Betico Day
msgid "Dia di Betico"
msgstr "Betico Day"

#. Carnival Monday
msgid "Dialuna despues di Carnaval Grandi"
msgstr "Carnival Monday"

#. Monday before Ash Wednesday
msgid "Dialuna prome cu diaranson di shinish"
msgstr "Monday before Ash Wednesday"

#. National Anthem and Flag Day
msgid "Dia di Himno y Bandera"
msgstr "National Anthem and Flag Day"

#. Good Friday
msgid "Bierna Santo"
msgstr "Good Friday"

#. Easter Monday
msgid "Di dos dia di Pasco di Resureccion"
msgstr "Easter Monday"

#. King's Day.
msgid "Dia di Rey"
msgstr "King's Day"

#. King's Day.
msgid "Aña di Rey"
msgstr "King's Day"

#. Queen's Day.
msgid "Aña di La Reina"
msgstr "Queen's Day"

#. Labor Day
msgid "Dia di Obrero"
msgstr "Labor Day"

#. Ascension Day
msgid "Dia di Asuncion"
msgstr "Ascension Day"

#. Christmas Day
msgid "Pasco di Nacemento"
msgstr "Christmas Day"

#. Second Day of Christmas
msgid "Di dos dia di Pasco di Nacemento"
msgstr "Second Day of Christmas"
