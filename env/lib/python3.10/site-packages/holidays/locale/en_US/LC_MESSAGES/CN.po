# Chinese holidays en_US localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-09-28 19:23+0700\n"
"PO-Revision-Date: 2024-01-18 11:42+0700\n"
"Last-Translator: PPsyrius <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4.2\n"

#. %s (observed).
#, c-format
msgid "%s（观察日）"
msgstr "%s (observed)"

#. New Year's Day.
msgid "元旦"
msgstr "New Year's Day"

#. National Day.
msgid "国庆节"
msgstr "National Day"

#. Mid-Autumn Festival.
msgid "中秋节"
msgstr "Mid-Autumn Festival"

#. Chinese New Year (Spring Festival).
msgid "春节"
msgstr "Chinese New Year (Spring Festival)"

#. Chinese New Year's Eve.
msgid "农历除夕"
msgstr "Chinese New Year's Eve"

#. Labor Day.
msgid "劳动节"
msgstr "Labor Day"

#. Tomb-Sweeping Day.
msgid "清明节"
msgstr "Tomb-Sweeping Day"

#. Dragon Boat Festival.
msgid "端午节"
msgstr "Dragon Boat Festival"

#. International Women's Day.
msgid "国际妇女节"
msgstr "International Women's Day"

#. Youth Day.
msgid "五四青年节"
msgstr "Youth Day"

#. Children's Day.
msgid "六一儿童节"
msgstr "Children's Day"

#. Army Day.
msgid "建军节"
msgstr "Army Day"

#. Date format (see strftime() Format Codes).
msgid "%Y-%m-%d"
msgstr "%m/%d/%Y"

#. Day off (substituted from %s).
#, c-format
msgid "休息日（%s日起取代）"
msgstr "Day off (substituted from %s)"

#. 70th Anniversary of the Victory of the Chinese People’s War of Resistance
#. against Japanese
#. Aggression and the World Anti-Fascist War.
msgid "中国人民抗日战争暨世界反法西斯战争胜利70周年纪念日"
msgstr ""
"70th Anniversary of the Victory of the Chinese People’s War of Resistance "
"against Japanese Aggression and the World Anti-Fascist War"

#. Chinese New Year (Spring Festival) Extended Holiday.
msgid "春节延长假期"
msgstr "Chinese New Year Extended Holiday"
