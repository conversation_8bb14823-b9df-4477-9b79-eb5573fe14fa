# Czechia holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.28\n"
"POT-Creation-Date: 2023-06-27 13:17+0300\n"
"PO-Revision-Date: 2023-06-27 13:46+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.2.2\n"

#. Independent Czech State Restoration Day.
msgid "Den obnovy samostatného českého státu"
msgstr "Independent Czech State Restoration Day"

#. New Year's Day.
msgid "Nový rok"
msgstr "New Year's Day"

#. Good Friday.
msgid "Velk<PERSON> pátek"
msgstr "Good Friday"

#. Easter Monday.
msgid "Velikonoční pondělí"
msgstr "Easter Monday"

#. Labor Day.
msgid "Svátek práce"
msgstr "Labor Day"

#. Victory Day.
msgid "Den vítězství"
msgstr "Victory Day"

#. Day of Victory over Fascism.
msgid "Den vítězství nad hitlerovským fašismem"
msgstr "Day of Victory over Fascism"

#. Saints Cyril and Methodius Day.
msgid "Den slovanských věrozvěstů Cyrila a Metoděje"
msgstr "Saints Cyril and Methodius Day"

#. Jan Hus Day.
msgid "Den upálení mistra Jana Husa"
msgstr "Jan Hus Day"

#. Statehood Day.
msgid "Den české státnosti"
msgstr "Statehood Day"

#. Independent Czechoslovak State Day.
msgid "Den vzniku samostatného československého státu"
msgstr "Independent Czechoslovak State Day"

#. Struggle for Freedom and Democracy Day.
msgid "Den boje za svobodu a demokracii"
msgstr "Struggle for Freedom and Democracy Day"

#. Christmas Eve.
msgid "Štědrý den"
msgstr "Christmas Eve"

#. Christmas Day.
msgid "1. svátek vánoční"
msgstr "Christmas Day"

#. Second Day of Christmas.
msgid "2. svátek vánoční"
msgstr "Second Day of Christmas"
