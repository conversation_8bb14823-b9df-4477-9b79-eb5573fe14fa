# Costa Rica holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-03-22 22:55+0200\n"
"PO-Revision-Date: 2024-01-23 17:38+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.4\n"

#. %s (observed).
#, c-format
msgid "%s (observado)"
msgstr "%s (observed)"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "New Year's Day"

#. Maundy Thursday.
msgid "Jueves Santo"
msgstr "Maundy Thursday"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Good Friday"

#. <PERSON>ía Day.
msgid "Día de Juan Santamaría"
msgstr "Juan Santamaría Day"

#. International Labor Day.
msgid "Día Internacional del Trabajo"
msgstr "International Labor Day"

#. Annexation of the Party of Nicoya to Costa Rica.
msgid "Anexión del Partido de Nicoya a Costa Rica"
msgstr "Annexation of the Party of Nicoya to Costa Rica"

#. Feast of Our Lady of the Angels.
msgid "Fiesta de Nuestra Señora de los Ángeles"
msgstr "Feast of Our Lady of the Angels"

#. Mother's Day.
msgid "Día de la Madre"
msgstr "Mother's Day"

#. Day of the Black Person and Afro-Costa Rican Culture.
msgid "Día de la Persona Negra y la Cultura Afrocostarricense"
msgstr "Day of the Black Person and Afro-Costa Rican Culture"

#. Independence Day.
msgid "Día de la Independencia"
msgstr "Independence Day"

#. Cultures Day.
msgid "Día de las Culturas"
msgstr "Cultures Day"

#. Army Abolition Day.
msgid "Día de la Abolición del Ejército"
msgstr "Army Abolition Day"

#. Christmas Day.
msgid "Navidad"
msgstr "Christmas Day"
