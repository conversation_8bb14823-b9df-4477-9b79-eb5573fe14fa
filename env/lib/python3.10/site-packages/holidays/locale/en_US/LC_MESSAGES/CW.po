# Curacao holidays en_US localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.27\n"
"POT-Creation-Date: 2023-06-13 16:35+0700\n"
"PO-Revision-Date: \n"
"Last-Translator: PPsyrius <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.3.1\n"

#. New Year's Day
msgid "Aña Nobo"
msgstr "New Year's Day"

#. Carnival Monday
msgid "Dialuna despues di Carnaval Grandi"
msgstr "Carnival Monday"

#. Good Friday
msgid "Bièrnèsantu"
msgstr "Good Friday"

#. Easter Sunday
msgid "Pasku di Resurekshon"
msgstr "Easter Sunday"

#. Easter Monday
msgid "Di dos dia di Pasku di Resurekshon"
msgstr "Easter Monday"

#. King's Day.
msgid "Dia di Rey"
msgstr "King's Day"

#. Queen's Day.
msgid "Dia di la Reina"
msgstr "Queen's Day"

#. Labor Day
msgid "Dia di Obrero"
msgstr "Labor Day"

#. Ascension Day
msgid "Dia di Asenshon"
msgstr "Ascension Day"

#. National Anthem and Flag Day
msgid "Dia di Himno i Bandera"
msgstr "National Anthem and Flag Day"

#. Curaçao Day
msgid "Dia di Pais Kòrsou"
msgstr "Curaçao Day"

#. Christmas Day
msgid "Pasku di Nasementu"
msgstr "Christmas Day"

#. Second Day of Christmas
msgid "Di dos dia di Pasku di Nasementu"
msgstr "Second Day of Christmas"
