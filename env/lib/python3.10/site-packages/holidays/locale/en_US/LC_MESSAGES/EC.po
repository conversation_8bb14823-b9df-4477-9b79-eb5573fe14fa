# Ecuador holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-04-02 18:54+0300\n"
"PO-Revision-Date: 2024-01-23 21:03+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.4\n"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "New Year's Day"

#. Carnival.
msgid "Carnaval"
msgstr "Carnival"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Good Friday"

#. Labor Day.
msgid "Día del Trabajo"
msgstr "Labor Day"

#. The Battle of Pichincha.
msgid "Batalla de Pichincha"
msgstr "The Battle of Pichincha"

#. Declaration of Independence of Quito.
msgid "Primer Grito de Independencia"
msgstr "Declaration of Independence of Quito"

#. Independence of Guayaquil.
msgid "Independencia de Guayaquil"
msgstr "Independence of Guayaquil"

#. All Souls' Day.
msgid "Día de los Difuntos"
msgstr "All Souls' Day"

#. Independence of Cuenca.
msgid "Independencia de Cuenca"
msgstr "Independence of Cuenca"

#. Christmas Day.
msgid "Día de Navidad"
msgstr "Christmas Day"

#. %s (observed).
#, c-format
msgid "%s (observado)"
msgstr "%s (observed)"
