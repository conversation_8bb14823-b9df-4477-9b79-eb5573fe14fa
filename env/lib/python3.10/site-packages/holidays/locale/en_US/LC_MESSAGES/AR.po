# Argentina holidays en_US localization.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.41\n"
"POT-Creation-Date: 2023-03-02 00:39+0700\n"
"PO-Revision-Date: 2024-01-05 12:31+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.4\n"

#. Bridge Public Holiday.
msgid "Feriado con fines turísticos"
msgstr "Bridge Public Holiday"

#. Bicentenary of the creation and first oath of the national flag.
msgid "Bicentenario de la creación y primera jura de la bandera nacional"
msgstr "Bicentenary of the creation and first oath of the national flag"

#. Bicentenary of the Battle of Tucuman.
msgid "Bicentenario de la Batalla de Tucumán"
msgstr "Bicentenary of the Battle of Tucumán"

#. Bicentenary of the inaugural session of the National Constituent Assembly
#. of the year 1813.
msgid ""
"Bicentenario de la sesión inaugural de la Asamblea Nacional Constituyente "
"del año 1813"
msgstr ""
"Bicentenary of the inaugural session of the National Constituent Assembly of"
" the year 1813"

#. Bicentenary of the Battle of Salta.
msgid "Bicentenario de la Batalla de Salta"
msgstr "Bicentenary of the Battle of Salta"

#. National Census Day 2022.
msgid "Censo nacional 2022"
msgstr "National Census Day 2022"

#. New Year's Day.
msgid "Año Nuevo"
msgstr "New Year's Day"

#. Carnival Day.
msgid "Día de Carnaval"
msgstr "Carnival Day"

#. Memory's National Day for the Truth and Justice.
msgid "Día Nacional de la Memoria por la Verdad y la Justicia"
msgstr "Memory's National Day for the Truth and Justice"

#. War Veterans Day.
msgid "Día del Veterano de Guerra"
msgstr "War Veterans Day"

#. Veterans Day and the Fallen in the Malvinas War.
msgid "Día del Veterano y de los Caidos en la Guerra de Malvinas"
msgstr "Veterans Day and the Fallen in the Malvinas War"

#. Good Friday.
msgid "Viernes Santo"
msgstr "Good Friday"

#. Labor Day.
msgid "Día del Trabajo"
msgstr "Labor Day"

#. May Revolution Day.
msgid "Día de la Revolución de Mayo"
msgstr "May Revolution Day"

#. Day of Argentine Sovereignty over the Malvinas, Sandwich and South Atlantic
#. Islands.
msgid ""
"Día de los Derechos Argentinos sobre las Islas Malvinas, Sandwich y del "
"Atlántico Sur"
msgstr ""
"Day of Argentine Sovereignty over the Malvinas, Sandwich and South Atlantic "
"Islands"

#. Pass to the Immortality of General Don Manuel Belgrano.
msgid "Paso a la Inmortalidad del General Don Manuel Belgrano"
msgstr "Pass to the Immortality of General Don Manuel Belgrano"

#. Independence Day.
msgid "Día de la Independencia"
msgstr "Independence Day"

#. Immaculate Conception.
msgid "Inmaculada Concepción de María"
msgstr "Immaculate Conception"

#. Christmas Day.
msgid "Navidad"
msgstr "Christmas Day"

#. Pass to the Immortality of General Don Martin Miguel de Guemes.
msgid "Paso a la Inmortalidad del General Don Martín Miguel de Güemes"
msgstr "Pass to the Immortality of General Don Martín Miguel de Güemes"

#. Pass to the Immortality of General Don Jose de San Martin.
msgid "Paso a la Inmortalidad del General Don José de San Martin"
msgstr "Pass to the Immortality of General Don José de San Martin"

#. Respect for Cultural Diversity Day.
msgid "Día del Respeto a la Diversidad Cultural"
msgstr "Respect for Cultural Diversity Day"

#. Columbus Day.
msgid "Día de la Raza"
msgstr "Columbus day"

#. National Sovereignty Day.
msgid "Día de la Soberanía Nacional"
msgstr "National Sovereignty Day"

#. %s (observed).
#, c-format
msgid "%s (observado)"
msgstr "%s (observed)"
