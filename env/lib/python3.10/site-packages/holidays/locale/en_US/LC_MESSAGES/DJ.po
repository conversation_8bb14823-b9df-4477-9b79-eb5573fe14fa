# Djibouti holidays en_US localization.
# Authors: <AUTHORS>
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.30\n"
"POT-Creation-Date: 2023-07-15 20:58+0300\n"
"PO-Revision-Date: 2024-01-02 15:49+0200\n"
"Last-Translator: ~Jhell<PERSON> <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4\n"

#. %s (estimated).
#, c-format
msgid "%s (estimé)"
msgstr "%s (estimated)"

#. New Year's Day.
msgid "Nouvel an"
msgstr "New Year's Day"

#. Labor Day.
msgid "Fête du travail"
msgstr "Labor Day"

#. Independence Day.
msgid "Fête de l'indépendance"
msgstr "Independence Day"

#. Independence Day Holiday.
msgid "Fête de l'indépendance deuxième jour"
msgstr "Independence Day Holiday"

#. Christmas Day.
msgid "Noël"
msgstr "Christmas Day"

#. Isra and Miraj.
msgid "Al Isra et Al Mirague"
msgstr "Isra and Miraj"

#. Eid al-Fitr.
msgid "Eid al-Fitr"
msgstr "Eid al-Fitr"

#. Eid al-Fitr Holiday.
msgid "Eid al-Fitr deuxième jour"
msgstr "Eid al-Fitr Holiday"

#. Arafat Day.
msgid "Arafat"
msgstr "Arafat Day"

#. Eid al-Adha.
msgid "Eid al-Adha"
msgstr "Eid al-Adha"

#. Eid al-Adha Holiday.
msgid "Eid al-Adha deuxième jour"
msgstr "Eid al-Adha Holiday"

#. Islamic New Year.
msgid "Nouvel an musulman"
msgstr "Islamic New Year"

#. Prophet Muhammad's Birthday.
msgid "Anniversaire du prophète Muhammad"
msgstr "Prophet Muhammad's Birthday"
