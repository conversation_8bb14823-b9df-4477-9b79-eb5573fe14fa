# Germany holidays en_US localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.23\n"
"POT-Creation-Date: 2023-04-04 16:13+0300\n"
"PO-Revision-Date: 2023-04-09 18:42+0300\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Neujahr"
msgstr "New Year's Day"

#. Good Friday.
msgid "Karfreitag"
msgstr "Good Friday"

#. Easter Monday.
msgid "Ostermontag"
msgstr "Easter Monday"

#. Labor Day.
msgid "<PERSON><PERSON><PERSON>"
msgstr "Labor Day"

#. Ascension Day.
msgid "Christi Him<PERSON>fahrt"
msgstr "Ascension Day"

#. Whit Monday.
msgid "Pfingstmontag"
msgstr "Whit Monday"

#. German Unity Day.
msgid "Tag der Deutschen Einheit"
msgstr "German Unity Day"

#. Reformation Day.
msgid "Reformationstag"
msgstr "Reformation Day"

#. Repentance and Prayer Day.
msgid "Buß- und Bettag"
msgstr "Repentance and Prayer Day"

#. Christmas Day.
msgid "Erster Weihnachtstag"
msgstr "Christmas Day"

#. Second Day of Christmas.
msgid "Zweiter Weihnachtstag"
msgstr "Second Day of Christmas"

#. Easter Sunday.
msgid "Ostersonntag"
msgstr "Easter Sunday"

#. Whit Sunday.
msgid "Pfingstsonntag"
msgstr "Whit Sunday"

#. International Women's Day.
msgid "Internationaler Frauentag"
msgstr "International Women's Day"

#. 75th anniversary of the liberation from Nazism and the end of the Second
#. World War in Europe.
msgid ""
"75. Jahrestag der Befreiung vom Nationalsozialismus und der Beendigung des "
"Zweiten Weltkriegs in Europa"
msgstr ""
"75th anniversary of the liberation from Nazism and the end of the Second "
"World War in Europe"

#. Epiphany.
msgid "Heilige Drei Könige"
msgstr "Epiphany"

#. Corpus Christi.
msgid "Fronleichnam"
msgstr "Corpus Christi"

#. All Saints' Day.
msgid "Allerheiligen"
msgstr "All Saints' Day"

#. Assumption Day.
msgid "Mariä Himmelfahrt"
msgstr "Assumption Day"

#. World Children's Day.
msgid "Weltkindertag"
msgstr "World Children's Day"
