# Chinese holidays th localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.42\n"
"POT-Creation-Date: 2023-09-28 19:23+0700\n"
"PO-Revision-Date: 2024-01-18 11:42+0700\n"
"Last-Translator: PPsyrius <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: th\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4.2\n"

#. %s (observed).
#, c-format
msgid "%s（观察日）"
msgstr "ชดเชย%s"

#. New Year's Day.
msgid "元旦"
msgstr "วันปีใหม่สากล"

#. National Day.
msgid "国庆节"
msgstr "วันชาติจีน"

#. Mid-Autumn Festival.
msgid "中秋节"
msgstr "วันไหว้พระจันทร์"

#. Chinese New Year (Spring Festival).
msgid "春节"
msgstr "วันตรุษจีน"

#. Chinese New Year's Eve.
msgid "农历除夕"
msgstr "วันก่อนวันตรุษจีน"

#. Labor Day.
msgid "劳动节"
msgstr "วันแรงงาน"

#. Tomb-Sweeping Day.
msgid "清明节"
msgstr "วันเช็งเม้ง"

#. Dragon Boat Festival.
msgid "端午节"
msgstr "วันไหว้บ๊ะจ่าง"

#. International Women's Day.
msgid "国际妇女节"
msgstr "วันสตรีสากล"

#. Youth Day.
msgid "五四青年节"
msgstr "วันเยาวชนห่งชาติจีน"

#. Children's Day.
msgid "六一儿童节"
msgstr "วันเด็กสากล"

#. Army Day.
msgid "建军节"
msgstr "วันสถาปนากองทัพปลดปล่อยประชาชนจีน"

#. Date format (see strftime() Format Codes).
msgid "%Y-%m-%d"
msgstr "%d/%m/%Y"

#. Day off (substituted from %s).
#, c-format
msgid "休息日（%s日起取代）"
msgstr "วันหยุด (แทน %s)"

#. 70th Anniversary of the Victory of the Chinese People’s War of Resistance
#. against Japanese
#. Aggression and the World Anti-Fascist War.
msgid "中国人民抗日战争暨世界反法西斯战争胜利70周年纪念日"
msgstr ""
"ครบรอบ 70 ปีแห่งการได้รับชัยชนะจากสงครามต่อต้านญี่ปุ่นและลัทธิฟาสซิสต์โลก"

#. Chinese New Year (Spring Festival) Extended Holiday.
msgid "春节延长假期"
msgstr "หยุดพิเศษวันตรุษจีน"
