# Taiwan holidays th localization.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.43\n"
"POT-Creation-Date: 2023-11-24 16:16+0700\n"
"PO-Revision-Date: 2024-02-07 19:37+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: th\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"Generated-By: Lingua 4.15.0\n"
"X-Generator: Poedit 3.4.2\n"

#. %s (observed).
#, c-format
msgid "%s（慶祝）"
msgstr "ชดเชย%s"

#. Founding Day of the Republic of China.
msgid "中華民國開國紀念日"
msgstr "วันสถาปนาสาธารณรัฐจีน(ไต้หวัน)"

#. Chinese New Year's Eve.
msgid "農曆除夕"
msgstr "วันก่อนวันตรุษจีน"

#. Chinese New Year.
msgid "春節"
msgstr "วันตรุษจีน"

#. Peace Memorial Day.
msgid "和平紀念日"
msgstr "วันรำลึกสันติภาพ"

#. Children's Day.
msgid "兒童節"
msgstr "วันเด็กแห่งชาติ"

#. Tomb Sweeping Day.
msgid "清明節"
msgstr "วันเช็งเม้ง"

#. Dragon Boat Festival.
msgid "端午節"
msgstr "วันไหว้บ๊ะจ่าง"

#. Mid-Autumn Festival.
msgid "中秋節"
msgstr "วันไหว้พระจันทร์"

#. National Day.
msgid "中華民國國慶日"
msgstr "วันชาติสาธารณรัฐจีน(ไต้หวัน)"

#. Date format (see strftime() Format Codes).
msgid "%Y-%m-%d"
msgstr "%d/%m/%Y"

#. Day off (substituted from %s).
#, c-format
msgid "休息日（%s日起取代）"
msgstr "วันหยุด (แทน %s)"
