# Aruba holidays.
# <AUTHOR> <EMAIL>, (c) 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.27\n"
"POT-Creation-Date: 2023-06-09 23:12+0700\n"
"PO-Revision-Date: \n"
"Last-Translator: PPsyrius <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: pap\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.3.1\n"

#. New Year's Day
msgid "Aña Nobo"
msgstr ""

#. Betico Day
msgid "Dia di Betico"
msgstr ""

#. Carnival Monday
msgid "Dialuna despues di Carnaval Grandi"
msgstr ""

#. Monday before Ash Wednesday
msgid "Dialuna prome cu diaranson di shinish"
msgstr ""

#. National Anthem and Flag Day
msgid "Dia di Himno y Bandera"
msgstr ""

#. Good Friday
msgid "Bierna Santo"
msgstr ""

#. Easter Monday
msgid "Di dos dia di Pasco di Resureccion"
msgstr ""

#. King's Day.
msgid "Dia di Rey"
msgstr ""

#. King's Day.
msgid "Aña di Rey"
msgstr ""

#. Queen's Day.
msgid "Aña di La Reina"
msgstr ""

#. Labor Day
msgid "Dia di Obrero"
msgstr ""

#. Ascension Day
msgid "Dia di Asuncion"
msgstr ""

#. Christmas Day
msgid "Pasco di Nacemento"
msgstr ""

#. Second Day of Christmas
msgid "Di dos dia di Pasco di Nacemento"
msgstr ""
