# Romania holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.22\n"
"POT-Creation-Date: 2023-03-22 18:35+0200\n"
"PO-Revision-Date: 2023-03-22 21:28+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: ro\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n==0 || (n!=1 && n%100>=1 && n%100<=19) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Anul Nou"
msgstr ""

#. Epiphany.
msgid "Bobotează"
msgstr ""

#. <PERSON> the Baptist.
msgid "Sfântul Ion"
msgstr ""

#. Unification of the Romanian Principalities Day.
msgid "Ziua Unirii Principatelor Române"
msgstr ""

#. Easter.
msgid "Paștele"
msgstr ""

#. Labor Day.
msgid "Ziua Muncii"
msgstr ""

#. Children's Day.
msgid "Ziua Copilului"
msgstr ""

#. Pentecost.
msgid "Rusaliile"
msgstr ""

#. Dormition of the Mother of God.
msgid "Adormirea Maicii Domnului"
msgstr ""

#. Saint Andrew's Day.
msgid "Sfantul Apostol Andrei cel Intai chemat"
msgstr ""

#. National Day.
msgid "Ziua Națională a României"
msgstr ""

#. Christmas Day.
msgid "Crăciunul"
msgstr ""
