# Moldova holidays.
# Authors: <AUTHORS>
#
msgid ""
msgstr ""
"Project-Id-Version: Python Holidays 0.22\n"
"POT-Creation-Date: 2023-03-22 21:58+0200\n"
"PO-Revision-Date: 2023-03-22 21:59+0200\n"
"Last-Translator: ~Jhellico <<EMAIL>>\n"
"Language-Team: Python Holidays localization team\n"
"Language: ro\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n==0 || (n!=1 && n%100>=1 && n%100<=19) ? 1 : 2);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.2.2\n"

#. New Year's Day.
msgid "Anul Nou"
msgstr ""

#. Christmas Day (by old style).
msgid "Naşterea lui Iisus <PERSON>s (Crăciunul pe stil vechi)"
msgstr ""

#. Christmas Day.
msgid "Naşterea lui Iisus Hristos (Crăciunul)"
msgstr ""

#. International Women's Day.
msgid "Ziua internatională a femeii"
msgstr ""

#. Easter.
msgid "Paştele"
msgstr ""

#. Day of Rejoicing.
msgid "Paştele blajinilor"
msgstr ""

#. International Workers' Solidarity Day.
msgid "Ziua internaţională a solidarităţii oamenilor muncii"
msgstr ""

#. Victory Day and Commemoration of the heroes fallen for Independence of
#. Fatherland.
msgid ""
"Ziua Victoriei şi a comemorării eroilor căzuţi pentru Independenţa Patriei"
msgstr ""

#. Europe Day.
msgid "Ziua Europei"
msgstr ""

#. International Children's Day.
msgid "Ziua Ocrotirii Copilului"
msgstr ""

#. Republic of Moldova Independence Day.
msgid "Ziua independenţei Republicii Moldova"
msgstr ""

#. National Language Day.
msgid "Limba noastră"
msgstr ""

#. Christmas Day (by new style).
msgid "Naşterea lui Iisus Hristos (Crăciunul pe stil nou)"
msgstr ""
