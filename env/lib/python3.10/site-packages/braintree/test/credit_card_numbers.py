class CreditCardNumbers(object):
    class CardTypeIndicators(object):
        Commercial = "****************"
        DurbinRegulated = "****************"
        Debit = "****************"
        Healthcare = "****************"
        Payroll  = "****************"
        Prepaid = "****************"
        IssuingBank = "****************"
        CountryOfIssuance = "****************"

        No  = "****************"
        Unknown = "****************"

    Maestro = "****************" # :nodoc:
    MasterCard = "****************"
    MasterCardInternational = "****************" # :nodoc:

    Visa = "****************"
    VisaInternational = "****************" # :nodoc:
    VisaPrepaid = "****************"

    Discover = "****************"
    Elo = "****************"

    Hiper = "****************"
    Hipercard = "****************"
    Amex = "***************"

    class FailsSandboxVerification(object):
        AmEx       = "***************"
        Discover   = "****************"
        MasterCard = "****************"
        Visa       = "****************"

    class AmexPayWithPoints(object):
        Success            = "***************"
        IneligibleCard     = "***************"
        InsufficientPoints = "***************"

    class Disputes(object):
        Chargeback = "****************"
