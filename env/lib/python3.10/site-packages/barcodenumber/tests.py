#This file is part of barcodenumber. The COPYRIGHT file at the top level of
#this repository contains the full copyright notices and license terms.
'''
Unit test for barcodenumber
'''

import unittest
import barcodenumber

CODES = [
    ('ean13', '*************', True),
    ('ean13', '*************', False),
    ('gs1_datamatrix', '**************2815051231', True),
    ('gs1_datamatrix', '**************', False),
    ]

class BarcodeNumberTest(unittest.TestCase):
    '''
    Test Case for barcodenumber
    '''

    def test_codes_numbers(self):
        '''
        Test Bank codes
        '''
        for code, number, result in CODES:
            if result:
                test = self.assertTrue
            else:
                test = self.assertFalse
            test(barcodenumber.check_code(code, number))

if __name__ == '__main__':
    unittest.main()
