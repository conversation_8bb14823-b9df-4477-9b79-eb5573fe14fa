{"pagination": {"ListDomains": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "domains"}, "ListPackageVersionAssets": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "assets"}, "ListPackageVersions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "versions"}, "ListPackages": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "packages"}, "ListRepositories": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "repositories"}, "ListRepositoriesInDomain": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "repositories"}, "ListAllowedRepositoriesForGroup": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "allowedRepositories"}, "ListAssociatedPackages": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "packages"}, "ListPackageGroups": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "packageGroups"}, "ListSubPackageGroups": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "packageGroups"}}}