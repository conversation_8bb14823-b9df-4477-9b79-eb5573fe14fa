{"pagination": {"GetWorkflowExecutionHistory": {"limit_key": "maximumPageSize", "input_token": "nextPageToken", "output_token": "nextPageToken", "result_key": "events"}, "ListActivityTypes": {"limit_key": "maximumPageSize", "input_token": "nextPageToken", "output_token": "nextPageToken", "result_key": "typeInfos"}, "ListClosedWorkflowExecutions": {"limit_key": "maximumPageSize", "input_token": "nextPageToken", "output_token": "nextPageToken", "result_key": "executionInfos"}, "ListDomains": {"limit_key": "maximumPageSize", "input_token": "nextPageToken", "output_token": "nextPageToken", "result_key": "domainInfos"}, "ListOpenWorkflowExecutions": {"limit_key": "maximumPageSize", "input_token": "nextPageToken", "output_token": "nextPageToken", "result_key": "executionInfos"}, "ListWorkflowTypes": {"limit_key": "maximumPageSize", "input_token": "nextPageToken", "output_token": "nextPageToken", "result_key": "typeInfos"}, "PollForDecisionTask": {"limit_key": "maximumPageSize", "input_token": "nextPageToken", "output_token": "nextPageToken", "result_key": "events", "non_aggregate_keys": ["taskToken", "startedEventId", "workflowExecution", "workflowType", "previousStartedEventId"]}}}