{"pagination": {"ListAgents": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Agents"}, "ListLocations": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Locations"}, "ListTagsForResource": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Tags"}, "ListTaskExecutions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "TaskExecutions"}, "ListTasks": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Tasks"}, "DescribeStorageSystemResourceMetrics": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Metrics"}, "ListDiscoveryJobs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DiscoveryJobs"}, "ListStorageSystems": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "StorageSystems"}}}