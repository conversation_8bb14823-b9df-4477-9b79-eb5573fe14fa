from typing import Any

DEBUG: int
INFO: int
WARN: int
ERROR: int
FATAL: int

class Log:
    def __init__(self, threshold: int = ...) -> None: ...
    def log(self, level: int, msg: str, *args: Any) -> None: ...
    def debug(self, msg: str, *args: Any) -> None: ...
    def info(self, msg: str, *args: Any) -> None: ...
    def warn(self, msg: str, *args: Any) -> None: ...
    def error(self, msg: str, *args: Any) -> None: ...
    def fatal(self, msg: str, *args: Any) -> None: ...

def log(level: int, msg: str, *args: Any) -> None: ...
def debug(msg: str, *args: Any) -> None: ...
def info(msg: str, *args: Any) -> None: ...
def warn(msg: str, *args: Any) -> None: ...
def error(msg: str, *args: Any) -> None: ...
def fatal(msg: str, *args: Any) -> None: ...
def set_threshold(level: int) -> int: ...
def set_verbosity(v: int) -> None: ...
