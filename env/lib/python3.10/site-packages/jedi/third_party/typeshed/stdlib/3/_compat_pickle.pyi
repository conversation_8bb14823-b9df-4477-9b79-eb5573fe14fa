from typing import Dict, <PERSON><PERSON>

IMPORT_MAPPING: Dict[str, str]
NAME_MAPPING: Dict[<PERSON><PERSON>[str, str], <PERSON><PERSON>[str, str]]
PYTHON2_EXCEPTIONS: Tuple[str, ...]
MULTIPROCESSING_EXCEPTIONS: Tuple[str, ...]
REVERSE_IMPORT_MAPPING: Dict[str, str]
REVERSE_NAME_MAPPING: Dict[<PERSON><PERSON>[str, str], <PERSON><PERSON>[str, str]]
PYTHON3_OSERROR_EXCEPTIONS: Tuple[str, ...]
PYTHON3_IMPORTERROR_EXCEPTIONS: Tuple[str, ...]
