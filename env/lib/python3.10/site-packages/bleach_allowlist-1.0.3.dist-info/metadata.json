{"classifiers": ["Environment :: Web Environment", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: BSD License", "Operating System :: OS Independent", "Programming Language :: Python", "Topic :: Internet :: WWW/HTTP", "Topic :: Internet :: WWW/HTTP :: Dynamic Content"], "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "<PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "https://github.com/yourcelf/bleach-allowlist.git"}}}, "generator": "bdist_wheel (0.30.0)", "license": "BSD License", "metadata_version": "2.0", "name": "bleach-allowlist", "summary": "Curated lists of tags and attributes for sanitizing html", "version": "1.0.3"}