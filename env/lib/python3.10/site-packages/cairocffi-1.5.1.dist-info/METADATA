Metadata-Version: 2.1
Name: cairocffi
Version: 1.5.1
Summary: cffi-based cairo bindings for Python
Author-email: <PERSON> <<EMAIL>>
License: BSD 3-Clause License
        
        Copyright (c) 2013-2019, <PERSON> and contributors.
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        
        * Redistributions of source code must retain the above copyright notice, this
          list of conditions and the following disclaimer.
        
        * Redistributions in binary form must reproduce the above copyright notice,
          this list of conditions and the following disclaimer in the documentation
          and/or other materials provided with the distribution.
        
        * Neither the name of the copyright holder nor the names of its
          contributors may be used to endorse or promote products derived from
          this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
        AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
        IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
        DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
        FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
        DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
        SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
        OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
        
Project-URL: Documentation, https://cairocffi.readthedocs.io/
Project-URL: Code, https://github.com/Kozea/cairocffi/
Project-URL: Issues, https://github.com/Kozea/cairocffi/issues
Project-URL: Donation, https://opencollective.com/courtbouillon
Keywords: cairo,cffi,binding
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Multimedia :: Graphics
Requires-Python: >=3.7
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: cffi >=1.1.0
Provides-Extra: doc
Requires-Dist: sphinx ; extra == 'doc'
Requires-Dist: sphinx-rtd-theme ; extra == 'doc'
Provides-Extra: test
Requires-Dist: pytest ; extra == 'test'
Requires-Dist: flake8 ; extra == 'test'
Requires-Dist: isort ; extra == 'test'
Requires-Dist: numpy ; extra == 'test'
Requires-Dist: pikepdf ; extra == 'test'
Provides-Extra: xcb
Requires-Dist: xcffib >=0.3.2 ; extra == 'xcb'

cairocffi is a `CFFI`_-based drop-in replacement for Pycairo_,
a set of Python bindings and object-oriented API for cairo_.
Cairo is a 2D vector graphics library with support for multiple backends
including image buffers, PNG, PostScript, PDF, and SVG file output.

Additionally, the ``cairocffi.pixbuf`` module uses GDK-PixBuf_
to decode various image formats for use in cairo.

.. _CFFI: https://cffi.readthedocs.org/
.. _Pycairo: https://pycairo.readthedocs.io/
.. _cairo: http://cairographics.org/
.. _GDK-PixBuf: https://gitlab.gnome.org/GNOME/gdk-pixbuf

* Free software: BSD license
* For Python 3.7+, tested on CPython and PyPy
* Documentation: https://cairocffi.readthedocs.io
* Changelog: https://cairocffi.readthedocs.io/en/stable/changelog.html
* Code, issues, tests: https://github.com/Kozea/cairocffi
* Code of conduct: https://www.courtbouillon.org/code-of-conduct
* Professional support: https://www.courtbouillon.org
* Donation: https://opencollective.com/courtbouillon
* API partially compatible with Pycairo.
* Works with any version of cairo.

cairocffi has been created and developed by Kozea (https://kozea.fr).
Professional support, maintenance and community management is provided by
CourtBouillon (https://www.courtbouillon.org).

Copyrights are retained by their contributors, no copyright assignment is
required to contribute to cairocffi. Unless explicitly stated otherwise, any
contribution intentionally submitted for inclusion is licensed under the BSD
3-clause license, without any additional terms or conditions. For full
authorship information, see the version control history.
