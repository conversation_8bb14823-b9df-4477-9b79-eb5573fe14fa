# -*- coding: utf-8 -*-
# Auto-generated by <PERSON>, do not modify.
# @generated
# flake8: noqa
# pylint: skip-file
from __future__ import unicode_literals
from stone.backends.python_rsrc import stone_base as bb
from stone.backends.python_rsrc import stone_validators as bv

from dropbox import common

class SecondaryEmail(bb.Struct):
    """
    :ivar secondary_emails.SecondaryEmail.email: Secondary email address.
    :ivar secondary_emails.SecondaryEmail.is_verified: Whether or not the
        secondary email address is verified to be owned by a user.
    """

    __slots__ = [
        '_email_value',
        '_is_verified_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 email=None,
                 is_verified=None):
        self._email_value = bb.NOT_SET
        self._is_verified_value = bb.NOT_SET
        if email is not None:
            self.email = email
        if is_verified is not None:
            self.is_verified = is_verified

    # Instance attribute type: str (validator is set below)
    email = bb.Attribute("email")

    # Instance attribute type: bool (validator is set below)
    is_verified = bb.Attribute("is_verified")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(SecondaryEmail, self)._process_custom_annotations(annotation_type, field_path, processor)

SecondaryEmail_validator = bv.Struct(SecondaryEmail)

SecondaryEmail.email.validator = common.EmailAddress_validator
SecondaryEmail.is_verified.validator = bv.Boolean()
SecondaryEmail._all_field_names_ = set([
    'email',
    'is_verified',
])
SecondaryEmail._all_fields_ = [
    ('email', SecondaryEmail.email.validator),
    ('is_verified', SecondaryEmail.is_verified.validator),
]

ROUTES = {
}

