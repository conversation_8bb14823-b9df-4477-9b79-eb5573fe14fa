# -*- coding: utf-8 -*-
# Auto-generated by <PERSON>, do not modify.
# @generated
# flake8: noqa
# pylint: skip-file
from __future__ import unicode_literals
from stone.backends.python_rsrc import stone_base as bb
from stone.backends.python_rsrc import stone_validators as bv

class CameraUploadsPolicyState(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.CameraUploadsPolicyState.disabled: Background camera
        uploads are disabled.
    :ivar team_policies.CameraUploadsPolicyState.enabled: Background camera
        uploads are allowed.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    enabled = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(CameraUploadsPolicyState, self)._process_custom_annotations(annotation_type, field_path, processor)

CameraUploadsPolicyState_validator = bv.Union(CameraUploadsPolicyState)

class ComputerBackupPolicyState(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.ComputerBackupPolicyState.disabled: Computer Backup
        feature is disabled.
    :ivar team_policies.ComputerBackupPolicyState.enabled: Computer Backup
        feature is enabled.
    :ivar team_policies.ComputerBackupPolicyState.default: Computer Backup
        defaults to ON for SSB teams, and OFF for Enterprise teams.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    enabled = None
    # Attribute is overwritten below the class definition
    default = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_default(self):
        """
        Check if the union tag is ``default``.

        :rtype: bool
        """
        return self._tag == 'default'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(ComputerBackupPolicyState, self)._process_custom_annotations(annotation_type, field_path, processor)

ComputerBackupPolicyState_validator = bv.Union(ComputerBackupPolicyState)

class EmmState(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.EmmState.disabled: Emm token is disabled.
    :ivar team_policies.EmmState.optional: Emm token is optional.
    :ivar team_policies.EmmState.required: Emm token is required.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    optional = None
    # Attribute is overwritten below the class definition
    required = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_optional(self):
        """
        Check if the union tag is ``optional``.

        :rtype: bool
        """
        return self._tag == 'optional'

    def is_required(self):
        """
        Check if the union tag is ``required``.

        :rtype: bool
        """
        return self._tag == 'required'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(EmmState, self)._process_custom_annotations(annotation_type, field_path, processor)

EmmState_validator = bv.Union(EmmState)

class ExternalDriveBackupPolicyState(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.ExternalDriveBackupPolicyState.disabled: External Drive
        Backup feature is disabled.
    :ivar team_policies.ExternalDriveBackupPolicyState.enabled: External Drive
        Backup feature is enabled.
    :ivar team_policies.ExternalDriveBackupPolicyState.default: External Drive
        Backup default value based on team tier.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    enabled = None
    # Attribute is overwritten below the class definition
    default = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_default(self):
        """
        Check if the union tag is ``default``.

        :rtype: bool
        """
        return self._tag == 'default'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(ExternalDriveBackupPolicyState, self)._process_custom_annotations(annotation_type, field_path, processor)

ExternalDriveBackupPolicyState_validator = bv.Union(ExternalDriveBackupPolicyState)

class FileLockingPolicyState(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.FileLockingPolicyState.disabled: File locking feature is
        disabled.
    :ivar team_policies.FileLockingPolicyState.enabled: File locking feature is
        allowed.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    enabled = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(FileLockingPolicyState, self)._process_custom_annotations(annotation_type, field_path, processor)

FileLockingPolicyState_validator = bv.Union(FileLockingPolicyState)

class FileProviderMigrationPolicyState(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.FileProviderMigrationPolicyState.disabled: Team admin
        has opted out of File Provider Migration for team members.
    :ivar team_policies.FileProviderMigrationPolicyState.enabled: Team admin has
        not opted out of File Provider Migration for team members.
    :ivar team_policies.FileProviderMigrationPolicyState.default: Team admin has
        default value based on team tier.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    enabled = None
    # Attribute is overwritten below the class definition
    default = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_default(self):
        """
        Check if the union tag is ``default``.

        :rtype: bool
        """
        return self._tag == 'default'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(FileProviderMigrationPolicyState, self)._process_custom_annotations(annotation_type, field_path, processor)

FileProviderMigrationPolicyState_validator = bv.Union(FileProviderMigrationPolicyState)

class GroupCreation(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.GroupCreation.admins_and_members: Team admins and
        members can create groups.
    :ivar team_policies.GroupCreation.admins_only: Only team admins can create
        groups.
    """

    _catch_all = None
    # Attribute is overwritten below the class definition
    admins_and_members = None
    # Attribute is overwritten below the class definition
    admins_only = None

    def is_admins_and_members(self):
        """
        Check if the union tag is ``admins_and_members``.

        :rtype: bool
        """
        return self._tag == 'admins_and_members'

    def is_admins_only(self):
        """
        Check if the union tag is ``admins_only``.

        :rtype: bool
        """
        return self._tag == 'admins_only'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(GroupCreation, self)._process_custom_annotations(annotation_type, field_path, processor)

GroupCreation_validator = bv.Union(GroupCreation)

class OfficeAddInPolicy(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.OfficeAddInPolicy.disabled: Office Add-In is disabled.
    :ivar team_policies.OfficeAddInPolicy.enabled: Office Add-In is enabled.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    enabled = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(OfficeAddInPolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

OfficeAddInPolicy_validator = bv.Union(OfficeAddInPolicy)

class PaperDefaultFolderPolicy(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.PaperDefaultFolderPolicy.everyone_in_team: Everyone in
        team will be the default option when creating a folder in Paper.
    :ivar team_policies.PaperDefaultFolderPolicy.invite_only: Invite only will
        be the default option when creating a folder in Paper.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    everyone_in_team = None
    # Attribute is overwritten below the class definition
    invite_only = None
    # Attribute is overwritten below the class definition
    other = None

    def is_everyone_in_team(self):
        """
        Check if the union tag is ``everyone_in_team``.

        :rtype: bool
        """
        return self._tag == 'everyone_in_team'

    def is_invite_only(self):
        """
        Check if the union tag is ``invite_only``.

        :rtype: bool
        """
        return self._tag == 'invite_only'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PaperDefaultFolderPolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

PaperDefaultFolderPolicy_validator = bv.Union(PaperDefaultFolderPolicy)

class PaperDeploymentPolicy(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.PaperDeploymentPolicy.full: All team members have access
        to Paper.
    :ivar team_policies.PaperDeploymentPolicy.partial: Only whitelisted team
        members can access Paper. To see which user is whitelisted, check
        'is_paper_whitelisted' on 'account/info'.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    full = None
    # Attribute is overwritten below the class definition
    partial = None
    # Attribute is overwritten below the class definition
    other = None

    def is_full(self):
        """
        Check if the union tag is ``full``.

        :rtype: bool
        """
        return self._tag == 'full'

    def is_partial(self):
        """
        Check if the union tag is ``partial``.

        :rtype: bool
        """
        return self._tag == 'partial'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PaperDeploymentPolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

PaperDeploymentPolicy_validator = bv.Union(PaperDeploymentPolicy)

class PaperDesktopPolicy(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.PaperDesktopPolicy.disabled: Do not allow team members
        to use Paper Desktop.
    :ivar team_policies.PaperDesktopPolicy.enabled: Allow team members to use
        Paper Desktop.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    enabled = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PaperDesktopPolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

PaperDesktopPolicy_validator = bv.Union(PaperDesktopPolicy)

class PaperEnabledPolicy(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.PaperEnabledPolicy.disabled: Paper is disabled.
    :ivar team_policies.PaperEnabledPolicy.enabled: Paper is enabled.
    :ivar team_policies.PaperEnabledPolicy.unspecified: Unspecified policy.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    enabled = None
    # Attribute is overwritten below the class definition
    unspecified = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_unspecified(self):
        """
        Check if the union tag is ``unspecified``.

        :rtype: bool
        """
        return self._tag == 'unspecified'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PaperEnabledPolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

PaperEnabledPolicy_validator = bv.Union(PaperEnabledPolicy)

class PasswordControlMode(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.PasswordControlMode.disabled: Password is disabled.
    :ivar team_policies.PasswordControlMode.enabled: Password is enabled.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    enabled = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PasswordControlMode, self)._process_custom_annotations(annotation_type, field_path, processor)

PasswordControlMode_validator = bv.Union(PasswordControlMode)

class PasswordStrengthPolicy(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.PasswordStrengthPolicy.minimal_requirements: User
        passwords will adhere to the minimal password strength policy.
    :ivar team_policies.PasswordStrengthPolicy.moderate_password: User passwords
        will adhere to the moderate password strength policy.
    :ivar team_policies.PasswordStrengthPolicy.strong_password: User passwords
        will adhere to the very strong password strength policy.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    minimal_requirements = None
    # Attribute is overwritten below the class definition
    moderate_password = None
    # Attribute is overwritten below the class definition
    strong_password = None
    # Attribute is overwritten below the class definition
    other = None

    def is_minimal_requirements(self):
        """
        Check if the union tag is ``minimal_requirements``.

        :rtype: bool
        """
        return self._tag == 'minimal_requirements'

    def is_moderate_password(self):
        """
        Check if the union tag is ``moderate_password``.

        :rtype: bool
        """
        return self._tag == 'moderate_password'

    def is_strong_password(self):
        """
        Check if the union tag is ``strong_password``.

        :rtype: bool
        """
        return self._tag == 'strong_password'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PasswordStrengthPolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

PasswordStrengthPolicy_validator = bv.Union(PasswordStrengthPolicy)

class RolloutMethod(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.RolloutMethod.unlink_all: Unlink all.
    :ivar team_policies.RolloutMethod.unlink_most_inactive: Unlink devices with
        the most inactivity.
    :ivar team_policies.RolloutMethod.add_member_to_exceptions: Add member to
        Exceptions.
    """

    _catch_all = None
    # Attribute is overwritten below the class definition
    unlink_all = None
    # Attribute is overwritten below the class definition
    unlink_most_inactive = None
    # Attribute is overwritten below the class definition
    add_member_to_exceptions = None

    def is_unlink_all(self):
        """
        Check if the union tag is ``unlink_all``.

        :rtype: bool
        """
        return self._tag == 'unlink_all'

    def is_unlink_most_inactive(self):
        """
        Check if the union tag is ``unlink_most_inactive``.

        :rtype: bool
        """
        return self._tag == 'unlink_most_inactive'

    def is_add_member_to_exceptions(self):
        """
        Check if the union tag is ``add_member_to_exceptions``.

        :rtype: bool
        """
        return self._tag == 'add_member_to_exceptions'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(RolloutMethod, self)._process_custom_annotations(annotation_type, field_path, processor)

RolloutMethod_validator = bv.Union(RolloutMethod)

class SharedFolderJoinPolicy(bb.Union):
    """
    Policy governing which shared folders a team member can join.

    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.SharedFolderJoinPolicy.from_team_only: Team members can
        only join folders shared by teammates.
    :ivar team_policies.SharedFolderJoinPolicy.from_anyone: Team members can
        join any shared folder, including those shared by users outside the
        team.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    from_team_only = None
    # Attribute is overwritten below the class definition
    from_anyone = None
    # Attribute is overwritten below the class definition
    other = None

    def is_from_team_only(self):
        """
        Check if the union tag is ``from_team_only``.

        :rtype: bool
        """
        return self._tag == 'from_team_only'

    def is_from_anyone(self):
        """
        Check if the union tag is ``from_anyone``.

        :rtype: bool
        """
        return self._tag == 'from_anyone'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(SharedFolderJoinPolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

SharedFolderJoinPolicy_validator = bv.Union(SharedFolderJoinPolicy)

class SharedFolderMemberPolicy(bb.Union):
    """
    Policy governing who can be a member of a folder shared by a team member.

    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.SharedFolderMemberPolicy.team: Only a teammate can be a
        member of a folder shared by a team member.
    :ivar team_policies.SharedFolderMemberPolicy.anyone: Anyone can be a member
        of a folder shared by a team member.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    team = None
    # Attribute is overwritten below the class definition
    anyone = None
    # Attribute is overwritten below the class definition
    other = None

    def is_team(self):
        """
        Check if the union tag is ``team``.

        :rtype: bool
        """
        return self._tag == 'team'

    def is_anyone(self):
        """
        Check if the union tag is ``anyone``.

        :rtype: bool
        """
        return self._tag == 'anyone'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(SharedFolderMemberPolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

SharedFolderMemberPolicy_validator = bv.Union(SharedFolderMemberPolicy)

class SharedLinkCreatePolicy(bb.Union):
    """
    Policy governing the visibility of shared links. This policy can apply to
    newly created shared links, or all shared links.

    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.SharedLinkCreatePolicy.default_public: By default,
        anyone can access newly created shared links. No login will be required
        to access the shared links unless overridden.
    :ivar team_policies.SharedLinkCreatePolicy.default_team_only: By default,
        only members of the same team can access newly created shared links.
        Login will be required to access the shared links unless overridden.
    :ivar team_policies.SharedLinkCreatePolicy.team_only: Only members of the
        same team can access all shared links. Login will be required to access
        all shared links.
    :ivar team_policies.SharedLinkCreatePolicy.default_no_one: Only people
        invited can access newly created links. Login will be required to access
        the shared links unless overridden.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    default_public = None
    # Attribute is overwritten below the class definition
    default_team_only = None
    # Attribute is overwritten below the class definition
    team_only = None
    # Attribute is overwritten below the class definition
    default_no_one = None
    # Attribute is overwritten below the class definition
    other = None

    def is_default_public(self):
        """
        Check if the union tag is ``default_public``.

        :rtype: bool
        """
        return self._tag == 'default_public'

    def is_default_team_only(self):
        """
        Check if the union tag is ``default_team_only``.

        :rtype: bool
        """
        return self._tag == 'default_team_only'

    def is_team_only(self):
        """
        Check if the union tag is ``team_only``.

        :rtype: bool
        """
        return self._tag == 'team_only'

    def is_default_no_one(self):
        """
        Check if the union tag is ``default_no_one``.

        :rtype: bool
        """
        return self._tag == 'default_no_one'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(SharedLinkCreatePolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

SharedLinkCreatePolicy_validator = bv.Union(SharedLinkCreatePolicy)

class ShowcaseDownloadPolicy(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.ShowcaseDownloadPolicy.disabled: Do not allow files to
        be downloaded from Showcases.
    :ivar team_policies.ShowcaseDownloadPolicy.enabled: Allow files to be
        downloaded from Showcases.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    enabled = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(ShowcaseDownloadPolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

ShowcaseDownloadPolicy_validator = bv.Union(ShowcaseDownloadPolicy)

class ShowcaseEnabledPolicy(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.ShowcaseEnabledPolicy.disabled: Showcase is disabled.
    :ivar team_policies.ShowcaseEnabledPolicy.enabled: Showcase is enabled.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    enabled = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(ShowcaseEnabledPolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

ShowcaseEnabledPolicy_validator = bv.Union(ShowcaseEnabledPolicy)

class ShowcaseExternalSharingPolicy(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.ShowcaseExternalSharingPolicy.disabled: Do not allow
        showcases to be shared with people not on the team.
    :ivar team_policies.ShowcaseExternalSharingPolicy.enabled: Allow showcases
        to be shared with people not on the team.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    enabled = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(ShowcaseExternalSharingPolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

ShowcaseExternalSharingPolicy_validator = bv.Union(ShowcaseExternalSharingPolicy)

class SmartSyncPolicy(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.SmartSyncPolicy.local: The specified content will be
        synced as local files by default.
    :ivar team_policies.SmartSyncPolicy.on_demand: The specified content will be
        synced as on-demand files by default.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    local = None
    # Attribute is overwritten below the class definition
    on_demand = None
    # Attribute is overwritten below the class definition
    other = None

    def is_local(self):
        """
        Check if the union tag is ``local``.

        :rtype: bool
        """
        return self._tag == 'local'

    def is_on_demand(self):
        """
        Check if the union tag is ``on_demand``.

        :rtype: bool
        """
        return self._tag == 'on_demand'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(SmartSyncPolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

SmartSyncPolicy_validator = bv.Union(SmartSyncPolicy)

class SmarterSmartSyncPolicyState(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.SmarterSmartSyncPolicyState.disabled: Smarter Smart Sync
        feature is disabled.
    :ivar team_policies.SmarterSmartSyncPolicyState.enabled: Smarter Smart Sync
        feature is enabled.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    enabled = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(SmarterSmartSyncPolicyState, self)._process_custom_annotations(annotation_type, field_path, processor)

SmarterSmartSyncPolicyState_validator = bv.Union(SmarterSmartSyncPolicyState)

class SsoPolicy(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.SsoPolicy.disabled: Users will be able to sign in with
        their Dropbox credentials.
    :ivar team_policies.SsoPolicy.optional: Users will be able to sign in with
        either their Dropbox or single sign-on credentials.
    :ivar team_policies.SsoPolicy.required: Users will be required to sign in
        with their single sign-on credentials.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    optional = None
    # Attribute is overwritten below the class definition
    required = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_optional(self):
        """
        Check if the union tag is ``optional``.

        :rtype: bool
        """
        return self._tag == 'optional'

    def is_required(self):
        """
        Check if the union tag is ``required``.

        :rtype: bool
        """
        return self._tag == 'required'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(SsoPolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

SsoPolicy_validator = bv.Union(SsoPolicy)

class SuggestMembersPolicy(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.SuggestMembersPolicy.disabled: Suggest members is
        disabled.
    :ivar team_policies.SuggestMembersPolicy.enabled: Suggest members is
        enabled.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    enabled = None
    # Attribute is overwritten below the class definition
    other = None

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(SuggestMembersPolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

SuggestMembersPolicy_validator = bv.Union(SuggestMembersPolicy)

class TeamMemberPolicies(bb.Struct):
    """
    Policies governing team members.

    :ivar team_policies.TeamMemberPolicies.sharing: Policies governing sharing.
    :ivar team_policies.TeamMemberPolicies.emm_state: This describes the
        Enterprise Mobility Management (EMM) state for this team. This
        information can be used to understand if an organization is integrating
        with a third-party EMM vendor to further manage and apply restrictions
        upon the team's Dropbox usage on mobile devices. This is a new feature
        and in the future we'll be adding more new fields and additional
        documentation.
    :ivar team_policies.TeamMemberPolicies.office_addin: The admin policy around
        the Dropbox Office Add-In for this team.
    :ivar team_policies.TeamMemberPolicies.suggest_members_policy: The team
        policy on if teammembers are allowed to suggest users for admins to
        invite to the team.
    """

    __slots__ = [
        '_sharing_value',
        '_emm_state_value',
        '_office_addin_value',
        '_suggest_members_policy_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 sharing=None,
                 emm_state=None,
                 office_addin=None,
                 suggest_members_policy=None):
        self._sharing_value = bb.NOT_SET
        self._emm_state_value = bb.NOT_SET
        self._office_addin_value = bb.NOT_SET
        self._suggest_members_policy_value = bb.NOT_SET
        if sharing is not None:
            self.sharing = sharing
        if emm_state is not None:
            self.emm_state = emm_state
        if office_addin is not None:
            self.office_addin = office_addin
        if suggest_members_policy is not None:
            self.suggest_members_policy = suggest_members_policy

    # Instance attribute type: TeamSharingPolicies (validator is set below)
    sharing = bb.Attribute("sharing", user_defined=True)

    # Instance attribute type: EmmState (validator is set below)
    emm_state = bb.Attribute("emm_state", user_defined=True)

    # Instance attribute type: OfficeAddInPolicy (validator is set below)
    office_addin = bb.Attribute("office_addin", user_defined=True)

    # Instance attribute type: SuggestMembersPolicy (validator is set below)
    suggest_members_policy = bb.Attribute("suggest_members_policy", user_defined=True)

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(TeamMemberPolicies, self)._process_custom_annotations(annotation_type, field_path, processor)

TeamMemberPolicies_validator = bv.Struct(TeamMemberPolicies)

class TeamSharingPolicies(bb.Struct):
    """
    Policies governing sharing within and outside of the team.

    :ivar team_policies.TeamSharingPolicies.shared_folder_member_policy: Who can
        join folders shared by team members.
    :ivar team_policies.TeamSharingPolicies.shared_folder_join_policy: Which
        shared folders team members can join.
    :ivar team_policies.TeamSharingPolicies.shared_link_create_policy: Who can
        view shared links owned by team members.
    :ivar team_policies.TeamSharingPolicies.group_creation_policy: Who can
        create groups.
    """

    __slots__ = [
        '_shared_folder_member_policy_value',
        '_shared_folder_join_policy_value',
        '_shared_link_create_policy_value',
        '_group_creation_policy_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 shared_folder_member_policy=None,
                 shared_folder_join_policy=None,
                 shared_link_create_policy=None,
                 group_creation_policy=None):
        self._shared_folder_member_policy_value = bb.NOT_SET
        self._shared_folder_join_policy_value = bb.NOT_SET
        self._shared_link_create_policy_value = bb.NOT_SET
        self._group_creation_policy_value = bb.NOT_SET
        if shared_folder_member_policy is not None:
            self.shared_folder_member_policy = shared_folder_member_policy
        if shared_folder_join_policy is not None:
            self.shared_folder_join_policy = shared_folder_join_policy
        if shared_link_create_policy is not None:
            self.shared_link_create_policy = shared_link_create_policy
        if group_creation_policy is not None:
            self.group_creation_policy = group_creation_policy

    # Instance attribute type: SharedFolderMemberPolicy (validator is set below)
    shared_folder_member_policy = bb.Attribute("shared_folder_member_policy", user_defined=True)

    # Instance attribute type: SharedFolderJoinPolicy (validator is set below)
    shared_folder_join_policy = bb.Attribute("shared_folder_join_policy", user_defined=True)

    # Instance attribute type: SharedLinkCreatePolicy (validator is set below)
    shared_link_create_policy = bb.Attribute("shared_link_create_policy", user_defined=True)

    # Instance attribute type: GroupCreation (validator is set below)
    group_creation_policy = bb.Attribute("group_creation_policy", user_defined=True)

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(TeamSharingPolicies, self)._process_custom_annotations(annotation_type, field_path, processor)

TeamSharingPolicies_validator = bv.Struct(TeamSharingPolicies)

class TwoStepVerificationPolicy(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.TwoStepVerificationPolicy.require_tfa_enable: Enabled
        require two factor authorization.
    :ivar team_policies.TwoStepVerificationPolicy.require_tfa_disable: Disabled
        require two factor authorization.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    require_tfa_enable = None
    # Attribute is overwritten below the class definition
    require_tfa_disable = None
    # Attribute is overwritten below the class definition
    other = None

    def is_require_tfa_enable(self):
        """
        Check if the union tag is ``require_tfa_enable``.

        :rtype: bool
        """
        return self._tag == 'require_tfa_enable'

    def is_require_tfa_disable(self):
        """
        Check if the union tag is ``require_tfa_disable``.

        :rtype: bool
        """
        return self._tag == 'require_tfa_disable'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(TwoStepVerificationPolicy, self)._process_custom_annotations(annotation_type, field_path, processor)

TwoStepVerificationPolicy_validator = bv.Union(TwoStepVerificationPolicy)

class TwoStepVerificationState(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar team_policies.TwoStepVerificationState.required: Enabled require two
        factor authorization.
    :ivar team_policies.TwoStepVerificationState.optional: Optional require two
        factor authorization.
    :ivar team_policies.TwoStepVerificationState.disabled: Disabled require two
        factor authorization.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    required = None
    # Attribute is overwritten below the class definition
    optional = None
    # Attribute is overwritten below the class definition
    disabled = None
    # Attribute is overwritten below the class definition
    other = None

    def is_required(self):
        """
        Check if the union tag is ``required``.

        :rtype: bool
        """
        return self._tag == 'required'

    def is_optional(self):
        """
        Check if the union tag is ``optional``.

        :rtype: bool
        """
        return self._tag == 'optional'

    def is_disabled(self):
        """
        Check if the union tag is ``disabled``.

        :rtype: bool
        """
        return self._tag == 'disabled'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(TwoStepVerificationState, self)._process_custom_annotations(annotation_type, field_path, processor)

TwoStepVerificationState_validator = bv.Union(TwoStepVerificationState)

CameraUploadsPolicyState._disabled_validator = bv.Void()
CameraUploadsPolicyState._enabled_validator = bv.Void()
CameraUploadsPolicyState._other_validator = bv.Void()
CameraUploadsPolicyState._tagmap = {
    'disabled': CameraUploadsPolicyState._disabled_validator,
    'enabled': CameraUploadsPolicyState._enabled_validator,
    'other': CameraUploadsPolicyState._other_validator,
}

CameraUploadsPolicyState.disabled = CameraUploadsPolicyState('disabled')
CameraUploadsPolicyState.enabled = CameraUploadsPolicyState('enabled')
CameraUploadsPolicyState.other = CameraUploadsPolicyState('other')

ComputerBackupPolicyState._disabled_validator = bv.Void()
ComputerBackupPolicyState._enabled_validator = bv.Void()
ComputerBackupPolicyState._default_validator = bv.Void()
ComputerBackupPolicyState._other_validator = bv.Void()
ComputerBackupPolicyState._tagmap = {
    'disabled': ComputerBackupPolicyState._disabled_validator,
    'enabled': ComputerBackupPolicyState._enabled_validator,
    'default': ComputerBackupPolicyState._default_validator,
    'other': ComputerBackupPolicyState._other_validator,
}

ComputerBackupPolicyState.disabled = ComputerBackupPolicyState('disabled')
ComputerBackupPolicyState.enabled = ComputerBackupPolicyState('enabled')
ComputerBackupPolicyState.default = ComputerBackupPolicyState('default')
ComputerBackupPolicyState.other = ComputerBackupPolicyState('other')

EmmState._disabled_validator = bv.Void()
EmmState._optional_validator = bv.Void()
EmmState._required_validator = bv.Void()
EmmState._other_validator = bv.Void()
EmmState._tagmap = {
    'disabled': EmmState._disabled_validator,
    'optional': EmmState._optional_validator,
    'required': EmmState._required_validator,
    'other': EmmState._other_validator,
}

EmmState.disabled = EmmState('disabled')
EmmState.optional = EmmState('optional')
EmmState.required = EmmState('required')
EmmState.other = EmmState('other')

ExternalDriveBackupPolicyState._disabled_validator = bv.Void()
ExternalDriveBackupPolicyState._enabled_validator = bv.Void()
ExternalDriveBackupPolicyState._default_validator = bv.Void()
ExternalDriveBackupPolicyState._other_validator = bv.Void()
ExternalDriveBackupPolicyState._tagmap = {
    'disabled': ExternalDriveBackupPolicyState._disabled_validator,
    'enabled': ExternalDriveBackupPolicyState._enabled_validator,
    'default': ExternalDriveBackupPolicyState._default_validator,
    'other': ExternalDriveBackupPolicyState._other_validator,
}

ExternalDriveBackupPolicyState.disabled = ExternalDriveBackupPolicyState('disabled')
ExternalDriveBackupPolicyState.enabled = ExternalDriveBackupPolicyState('enabled')
ExternalDriveBackupPolicyState.default = ExternalDriveBackupPolicyState('default')
ExternalDriveBackupPolicyState.other = ExternalDriveBackupPolicyState('other')

FileLockingPolicyState._disabled_validator = bv.Void()
FileLockingPolicyState._enabled_validator = bv.Void()
FileLockingPolicyState._other_validator = bv.Void()
FileLockingPolicyState._tagmap = {
    'disabled': FileLockingPolicyState._disabled_validator,
    'enabled': FileLockingPolicyState._enabled_validator,
    'other': FileLockingPolicyState._other_validator,
}

FileLockingPolicyState.disabled = FileLockingPolicyState('disabled')
FileLockingPolicyState.enabled = FileLockingPolicyState('enabled')
FileLockingPolicyState.other = FileLockingPolicyState('other')

FileProviderMigrationPolicyState._disabled_validator = bv.Void()
FileProviderMigrationPolicyState._enabled_validator = bv.Void()
FileProviderMigrationPolicyState._default_validator = bv.Void()
FileProviderMigrationPolicyState._other_validator = bv.Void()
FileProviderMigrationPolicyState._tagmap = {
    'disabled': FileProviderMigrationPolicyState._disabled_validator,
    'enabled': FileProviderMigrationPolicyState._enabled_validator,
    'default': FileProviderMigrationPolicyState._default_validator,
    'other': FileProviderMigrationPolicyState._other_validator,
}

FileProviderMigrationPolicyState.disabled = FileProviderMigrationPolicyState('disabled')
FileProviderMigrationPolicyState.enabled = FileProviderMigrationPolicyState('enabled')
FileProviderMigrationPolicyState.default = FileProviderMigrationPolicyState('default')
FileProviderMigrationPolicyState.other = FileProviderMigrationPolicyState('other')

GroupCreation._admins_and_members_validator = bv.Void()
GroupCreation._admins_only_validator = bv.Void()
GroupCreation._tagmap = {
    'admins_and_members': GroupCreation._admins_and_members_validator,
    'admins_only': GroupCreation._admins_only_validator,
}

GroupCreation.admins_and_members = GroupCreation('admins_and_members')
GroupCreation.admins_only = GroupCreation('admins_only')

OfficeAddInPolicy._disabled_validator = bv.Void()
OfficeAddInPolicy._enabled_validator = bv.Void()
OfficeAddInPolicy._other_validator = bv.Void()
OfficeAddInPolicy._tagmap = {
    'disabled': OfficeAddInPolicy._disabled_validator,
    'enabled': OfficeAddInPolicy._enabled_validator,
    'other': OfficeAddInPolicy._other_validator,
}

OfficeAddInPolicy.disabled = OfficeAddInPolicy('disabled')
OfficeAddInPolicy.enabled = OfficeAddInPolicy('enabled')
OfficeAddInPolicy.other = OfficeAddInPolicy('other')

PaperDefaultFolderPolicy._everyone_in_team_validator = bv.Void()
PaperDefaultFolderPolicy._invite_only_validator = bv.Void()
PaperDefaultFolderPolicy._other_validator = bv.Void()
PaperDefaultFolderPolicy._tagmap = {
    'everyone_in_team': PaperDefaultFolderPolicy._everyone_in_team_validator,
    'invite_only': PaperDefaultFolderPolicy._invite_only_validator,
    'other': PaperDefaultFolderPolicy._other_validator,
}

PaperDefaultFolderPolicy.everyone_in_team = PaperDefaultFolderPolicy('everyone_in_team')
PaperDefaultFolderPolicy.invite_only = PaperDefaultFolderPolicy('invite_only')
PaperDefaultFolderPolicy.other = PaperDefaultFolderPolicy('other')

PaperDeploymentPolicy._full_validator = bv.Void()
PaperDeploymentPolicy._partial_validator = bv.Void()
PaperDeploymentPolicy._other_validator = bv.Void()
PaperDeploymentPolicy._tagmap = {
    'full': PaperDeploymentPolicy._full_validator,
    'partial': PaperDeploymentPolicy._partial_validator,
    'other': PaperDeploymentPolicy._other_validator,
}

PaperDeploymentPolicy.full = PaperDeploymentPolicy('full')
PaperDeploymentPolicy.partial = PaperDeploymentPolicy('partial')
PaperDeploymentPolicy.other = PaperDeploymentPolicy('other')

PaperDesktopPolicy._disabled_validator = bv.Void()
PaperDesktopPolicy._enabled_validator = bv.Void()
PaperDesktopPolicy._other_validator = bv.Void()
PaperDesktopPolicy._tagmap = {
    'disabled': PaperDesktopPolicy._disabled_validator,
    'enabled': PaperDesktopPolicy._enabled_validator,
    'other': PaperDesktopPolicy._other_validator,
}

PaperDesktopPolicy.disabled = PaperDesktopPolicy('disabled')
PaperDesktopPolicy.enabled = PaperDesktopPolicy('enabled')
PaperDesktopPolicy.other = PaperDesktopPolicy('other')

PaperEnabledPolicy._disabled_validator = bv.Void()
PaperEnabledPolicy._enabled_validator = bv.Void()
PaperEnabledPolicy._unspecified_validator = bv.Void()
PaperEnabledPolicy._other_validator = bv.Void()
PaperEnabledPolicy._tagmap = {
    'disabled': PaperEnabledPolicy._disabled_validator,
    'enabled': PaperEnabledPolicy._enabled_validator,
    'unspecified': PaperEnabledPolicy._unspecified_validator,
    'other': PaperEnabledPolicy._other_validator,
}

PaperEnabledPolicy.disabled = PaperEnabledPolicy('disabled')
PaperEnabledPolicy.enabled = PaperEnabledPolicy('enabled')
PaperEnabledPolicy.unspecified = PaperEnabledPolicy('unspecified')
PaperEnabledPolicy.other = PaperEnabledPolicy('other')

PasswordControlMode._disabled_validator = bv.Void()
PasswordControlMode._enabled_validator = bv.Void()
PasswordControlMode._other_validator = bv.Void()
PasswordControlMode._tagmap = {
    'disabled': PasswordControlMode._disabled_validator,
    'enabled': PasswordControlMode._enabled_validator,
    'other': PasswordControlMode._other_validator,
}

PasswordControlMode.disabled = PasswordControlMode('disabled')
PasswordControlMode.enabled = PasswordControlMode('enabled')
PasswordControlMode.other = PasswordControlMode('other')

PasswordStrengthPolicy._minimal_requirements_validator = bv.Void()
PasswordStrengthPolicy._moderate_password_validator = bv.Void()
PasswordStrengthPolicy._strong_password_validator = bv.Void()
PasswordStrengthPolicy._other_validator = bv.Void()
PasswordStrengthPolicy._tagmap = {
    'minimal_requirements': PasswordStrengthPolicy._minimal_requirements_validator,
    'moderate_password': PasswordStrengthPolicy._moderate_password_validator,
    'strong_password': PasswordStrengthPolicy._strong_password_validator,
    'other': PasswordStrengthPolicy._other_validator,
}

PasswordStrengthPolicy.minimal_requirements = PasswordStrengthPolicy('minimal_requirements')
PasswordStrengthPolicy.moderate_password = PasswordStrengthPolicy('moderate_password')
PasswordStrengthPolicy.strong_password = PasswordStrengthPolicy('strong_password')
PasswordStrengthPolicy.other = PasswordStrengthPolicy('other')

RolloutMethod._unlink_all_validator = bv.Void()
RolloutMethod._unlink_most_inactive_validator = bv.Void()
RolloutMethod._add_member_to_exceptions_validator = bv.Void()
RolloutMethod._tagmap = {
    'unlink_all': RolloutMethod._unlink_all_validator,
    'unlink_most_inactive': RolloutMethod._unlink_most_inactive_validator,
    'add_member_to_exceptions': RolloutMethod._add_member_to_exceptions_validator,
}

RolloutMethod.unlink_all = RolloutMethod('unlink_all')
RolloutMethod.unlink_most_inactive = RolloutMethod('unlink_most_inactive')
RolloutMethod.add_member_to_exceptions = RolloutMethod('add_member_to_exceptions')

SharedFolderJoinPolicy._from_team_only_validator = bv.Void()
SharedFolderJoinPolicy._from_anyone_validator = bv.Void()
SharedFolderJoinPolicy._other_validator = bv.Void()
SharedFolderJoinPolicy._tagmap = {
    'from_team_only': SharedFolderJoinPolicy._from_team_only_validator,
    'from_anyone': SharedFolderJoinPolicy._from_anyone_validator,
    'other': SharedFolderJoinPolicy._other_validator,
}

SharedFolderJoinPolicy.from_team_only = SharedFolderJoinPolicy('from_team_only')
SharedFolderJoinPolicy.from_anyone = SharedFolderJoinPolicy('from_anyone')
SharedFolderJoinPolicy.other = SharedFolderJoinPolicy('other')

SharedFolderMemberPolicy._team_validator = bv.Void()
SharedFolderMemberPolicy._anyone_validator = bv.Void()
SharedFolderMemberPolicy._other_validator = bv.Void()
SharedFolderMemberPolicy._tagmap = {
    'team': SharedFolderMemberPolicy._team_validator,
    'anyone': SharedFolderMemberPolicy._anyone_validator,
    'other': SharedFolderMemberPolicy._other_validator,
}

SharedFolderMemberPolicy.team = SharedFolderMemberPolicy('team')
SharedFolderMemberPolicy.anyone = SharedFolderMemberPolicy('anyone')
SharedFolderMemberPolicy.other = SharedFolderMemberPolicy('other')

SharedLinkCreatePolicy._default_public_validator = bv.Void()
SharedLinkCreatePolicy._default_team_only_validator = bv.Void()
SharedLinkCreatePolicy._team_only_validator = bv.Void()
SharedLinkCreatePolicy._default_no_one_validator = bv.Void()
SharedLinkCreatePolicy._other_validator = bv.Void()
SharedLinkCreatePolicy._tagmap = {
    'default_public': SharedLinkCreatePolicy._default_public_validator,
    'default_team_only': SharedLinkCreatePolicy._default_team_only_validator,
    'team_only': SharedLinkCreatePolicy._team_only_validator,
    'default_no_one': SharedLinkCreatePolicy._default_no_one_validator,
    'other': SharedLinkCreatePolicy._other_validator,
}

SharedLinkCreatePolicy.default_public = SharedLinkCreatePolicy('default_public')
SharedLinkCreatePolicy.default_team_only = SharedLinkCreatePolicy('default_team_only')
SharedLinkCreatePolicy.team_only = SharedLinkCreatePolicy('team_only')
SharedLinkCreatePolicy.default_no_one = SharedLinkCreatePolicy('default_no_one')
SharedLinkCreatePolicy.other = SharedLinkCreatePolicy('other')

ShowcaseDownloadPolicy._disabled_validator = bv.Void()
ShowcaseDownloadPolicy._enabled_validator = bv.Void()
ShowcaseDownloadPolicy._other_validator = bv.Void()
ShowcaseDownloadPolicy._tagmap = {
    'disabled': ShowcaseDownloadPolicy._disabled_validator,
    'enabled': ShowcaseDownloadPolicy._enabled_validator,
    'other': ShowcaseDownloadPolicy._other_validator,
}

ShowcaseDownloadPolicy.disabled = ShowcaseDownloadPolicy('disabled')
ShowcaseDownloadPolicy.enabled = ShowcaseDownloadPolicy('enabled')
ShowcaseDownloadPolicy.other = ShowcaseDownloadPolicy('other')

ShowcaseEnabledPolicy._disabled_validator = bv.Void()
ShowcaseEnabledPolicy._enabled_validator = bv.Void()
ShowcaseEnabledPolicy._other_validator = bv.Void()
ShowcaseEnabledPolicy._tagmap = {
    'disabled': ShowcaseEnabledPolicy._disabled_validator,
    'enabled': ShowcaseEnabledPolicy._enabled_validator,
    'other': ShowcaseEnabledPolicy._other_validator,
}

ShowcaseEnabledPolicy.disabled = ShowcaseEnabledPolicy('disabled')
ShowcaseEnabledPolicy.enabled = ShowcaseEnabledPolicy('enabled')
ShowcaseEnabledPolicy.other = ShowcaseEnabledPolicy('other')

ShowcaseExternalSharingPolicy._disabled_validator = bv.Void()
ShowcaseExternalSharingPolicy._enabled_validator = bv.Void()
ShowcaseExternalSharingPolicy._other_validator = bv.Void()
ShowcaseExternalSharingPolicy._tagmap = {
    'disabled': ShowcaseExternalSharingPolicy._disabled_validator,
    'enabled': ShowcaseExternalSharingPolicy._enabled_validator,
    'other': ShowcaseExternalSharingPolicy._other_validator,
}

ShowcaseExternalSharingPolicy.disabled = ShowcaseExternalSharingPolicy('disabled')
ShowcaseExternalSharingPolicy.enabled = ShowcaseExternalSharingPolicy('enabled')
ShowcaseExternalSharingPolicy.other = ShowcaseExternalSharingPolicy('other')

SmartSyncPolicy._local_validator = bv.Void()
SmartSyncPolicy._on_demand_validator = bv.Void()
SmartSyncPolicy._other_validator = bv.Void()
SmartSyncPolicy._tagmap = {
    'local': SmartSyncPolicy._local_validator,
    'on_demand': SmartSyncPolicy._on_demand_validator,
    'other': SmartSyncPolicy._other_validator,
}

SmartSyncPolicy.local = SmartSyncPolicy('local')
SmartSyncPolicy.on_demand = SmartSyncPolicy('on_demand')
SmartSyncPolicy.other = SmartSyncPolicy('other')

SmarterSmartSyncPolicyState._disabled_validator = bv.Void()
SmarterSmartSyncPolicyState._enabled_validator = bv.Void()
SmarterSmartSyncPolicyState._other_validator = bv.Void()
SmarterSmartSyncPolicyState._tagmap = {
    'disabled': SmarterSmartSyncPolicyState._disabled_validator,
    'enabled': SmarterSmartSyncPolicyState._enabled_validator,
    'other': SmarterSmartSyncPolicyState._other_validator,
}

SmarterSmartSyncPolicyState.disabled = SmarterSmartSyncPolicyState('disabled')
SmarterSmartSyncPolicyState.enabled = SmarterSmartSyncPolicyState('enabled')
SmarterSmartSyncPolicyState.other = SmarterSmartSyncPolicyState('other')

SsoPolicy._disabled_validator = bv.Void()
SsoPolicy._optional_validator = bv.Void()
SsoPolicy._required_validator = bv.Void()
SsoPolicy._other_validator = bv.Void()
SsoPolicy._tagmap = {
    'disabled': SsoPolicy._disabled_validator,
    'optional': SsoPolicy._optional_validator,
    'required': SsoPolicy._required_validator,
    'other': SsoPolicy._other_validator,
}

SsoPolicy.disabled = SsoPolicy('disabled')
SsoPolicy.optional = SsoPolicy('optional')
SsoPolicy.required = SsoPolicy('required')
SsoPolicy.other = SsoPolicy('other')

SuggestMembersPolicy._disabled_validator = bv.Void()
SuggestMembersPolicy._enabled_validator = bv.Void()
SuggestMembersPolicy._other_validator = bv.Void()
SuggestMembersPolicy._tagmap = {
    'disabled': SuggestMembersPolicy._disabled_validator,
    'enabled': SuggestMembersPolicy._enabled_validator,
    'other': SuggestMembersPolicy._other_validator,
}

SuggestMembersPolicy.disabled = SuggestMembersPolicy('disabled')
SuggestMembersPolicy.enabled = SuggestMembersPolicy('enabled')
SuggestMembersPolicy.other = SuggestMembersPolicy('other')

TeamMemberPolicies.sharing.validator = TeamSharingPolicies_validator
TeamMemberPolicies.emm_state.validator = EmmState_validator
TeamMemberPolicies.office_addin.validator = OfficeAddInPolicy_validator
TeamMemberPolicies.suggest_members_policy.validator = SuggestMembersPolicy_validator
TeamMemberPolicies._all_field_names_ = set([
    'sharing',
    'emm_state',
    'office_addin',
    'suggest_members_policy',
])
TeamMemberPolicies._all_fields_ = [
    ('sharing', TeamMemberPolicies.sharing.validator),
    ('emm_state', TeamMemberPolicies.emm_state.validator),
    ('office_addin', TeamMemberPolicies.office_addin.validator),
    ('suggest_members_policy', TeamMemberPolicies.suggest_members_policy.validator),
]

TeamSharingPolicies.shared_folder_member_policy.validator = SharedFolderMemberPolicy_validator
TeamSharingPolicies.shared_folder_join_policy.validator = SharedFolderJoinPolicy_validator
TeamSharingPolicies.shared_link_create_policy.validator = SharedLinkCreatePolicy_validator
TeamSharingPolicies.group_creation_policy.validator = GroupCreation_validator
TeamSharingPolicies._all_field_names_ = set([
    'shared_folder_member_policy',
    'shared_folder_join_policy',
    'shared_link_create_policy',
    'group_creation_policy',
])
TeamSharingPolicies._all_fields_ = [
    ('shared_folder_member_policy', TeamSharingPolicies.shared_folder_member_policy.validator),
    ('shared_folder_join_policy', TeamSharingPolicies.shared_folder_join_policy.validator),
    ('shared_link_create_policy', TeamSharingPolicies.shared_link_create_policy.validator),
    ('group_creation_policy', TeamSharingPolicies.group_creation_policy.validator),
]

TwoStepVerificationPolicy._require_tfa_enable_validator = bv.Void()
TwoStepVerificationPolicy._require_tfa_disable_validator = bv.Void()
TwoStepVerificationPolicy._other_validator = bv.Void()
TwoStepVerificationPolicy._tagmap = {
    'require_tfa_enable': TwoStepVerificationPolicy._require_tfa_enable_validator,
    'require_tfa_disable': TwoStepVerificationPolicy._require_tfa_disable_validator,
    'other': TwoStepVerificationPolicy._other_validator,
}

TwoStepVerificationPolicy.require_tfa_enable = TwoStepVerificationPolicy('require_tfa_enable')
TwoStepVerificationPolicy.require_tfa_disable = TwoStepVerificationPolicy('require_tfa_disable')
TwoStepVerificationPolicy.other = TwoStepVerificationPolicy('other')

TwoStepVerificationState._required_validator = bv.Void()
TwoStepVerificationState._optional_validator = bv.Void()
TwoStepVerificationState._disabled_validator = bv.Void()
TwoStepVerificationState._other_validator = bv.Void()
TwoStepVerificationState._tagmap = {
    'required': TwoStepVerificationState._required_validator,
    'optional': TwoStepVerificationState._optional_validator,
    'disabled': TwoStepVerificationState._disabled_validator,
    'other': TwoStepVerificationState._other_validator,
}

TwoStepVerificationState.required = TwoStepVerificationState('required')
TwoStepVerificationState.optional = TwoStepVerificationState('optional')
TwoStepVerificationState.disabled = TwoStepVerificationState('disabled')
TwoStepVerificationState.other = TwoStepVerificationState('other')

ROUTES = {
}

