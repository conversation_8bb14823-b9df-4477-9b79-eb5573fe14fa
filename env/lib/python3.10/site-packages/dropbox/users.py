# -*- coding: utf-8 -*-
# Auto-generated by <PERSON>, do not modify.
# @generated
# flake8: noqa
# pylint: skip-file
"""
This namespace contains endpoints and data types for user management.
"""

from __future__ import unicode_literals
from stone.backends.python_rsrc import stone_base as bb
from stone.backends.python_rsrc import stone_validators as bv

from dropbox import common
from dropbox import team_common
from dropbox import team_policies
from dropbox import users_common

class Account(bb.Struct):
    """
    The amount of detail revealed about an account depends on the user being
    queried and the user making the query.

    :ivar users.Account.account_id: The user's unique Dropbox ID.
    :ivar users.Account.name: Details of a user's name.
    :ivar users.Account.email: The user's email address. Do not rely on this
        without checking the ``email_verified`` field. Even then, it's possible
        that the user has since lost access to their email.
    :ivar users.Account.email_verified: Whether the user has verified their
        email address.
    :ivar users.Account.profile_photo_url: URL for the photo representing the
        user, if one is set.
    :ivar users.Account.disabled: Whether the user has been disabled.
    """

    __slots__ = [
        '_account_id_value',
        '_name_value',
        '_email_value',
        '_email_verified_value',
        '_profile_photo_url_value',
        '_disabled_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 account_id=None,
                 name=None,
                 email=None,
                 email_verified=None,
                 disabled=None,
                 profile_photo_url=None):
        self._account_id_value = bb.NOT_SET
        self._name_value = bb.NOT_SET
        self._email_value = bb.NOT_SET
        self._email_verified_value = bb.NOT_SET
        self._profile_photo_url_value = bb.NOT_SET
        self._disabled_value = bb.NOT_SET
        if account_id is not None:
            self.account_id = account_id
        if name is not None:
            self.name = name
        if email is not None:
            self.email = email
        if email_verified is not None:
            self.email_verified = email_verified
        if profile_photo_url is not None:
            self.profile_photo_url = profile_photo_url
        if disabled is not None:
            self.disabled = disabled

    # Instance attribute type: str (validator is set below)
    account_id = bb.Attribute("account_id")

    # Instance attribute type: Name (validator is set below)
    name = bb.Attribute("name", user_defined=True)

    # Instance attribute type: str (validator is set below)
    email = bb.Attribute("email")

    # Instance attribute type: bool (validator is set below)
    email_verified = bb.Attribute("email_verified")

    # Instance attribute type: str (validator is set below)
    profile_photo_url = bb.Attribute("profile_photo_url", nullable=True)

    # Instance attribute type: bool (validator is set below)
    disabled = bb.Attribute("disabled")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(Account, self)._process_custom_annotations(annotation_type, field_path, processor)

Account_validator = bv.Struct(Account)

class BasicAccount(Account):
    """
    Basic information about any account.

    :ivar users.BasicAccount.is_teammate: Whether this user is a teammate of the
        current user. If this account is the current user's account, then this
        will be ``True``.
    :ivar users.BasicAccount.team_member_id: The user's unique team member id.
        This field will only be present if the user is part of a team and
        ``is_teammate`` is ``True``.
    """

    __slots__ = [
        '_is_teammate_value',
        '_team_member_id_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 account_id=None,
                 name=None,
                 email=None,
                 email_verified=None,
                 disabled=None,
                 is_teammate=None,
                 profile_photo_url=None,
                 team_member_id=None):
        super(BasicAccount, self).__init__(account_id,
                                           name,
                                           email,
                                           email_verified,
                                           disabled,
                                           profile_photo_url)
        self._is_teammate_value = bb.NOT_SET
        self._team_member_id_value = bb.NOT_SET
        if is_teammate is not None:
            self.is_teammate = is_teammate
        if team_member_id is not None:
            self.team_member_id = team_member_id

    # Instance attribute type: bool (validator is set below)
    is_teammate = bb.Attribute("is_teammate")

    # Instance attribute type: str (validator is set below)
    team_member_id = bb.Attribute("team_member_id", nullable=True)

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(BasicAccount, self)._process_custom_annotations(annotation_type, field_path, processor)

BasicAccount_validator = bv.Struct(BasicAccount)

class FileLockingValue(bb.Union):
    """
    The value for ``UserFeature.file_locking``.

    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar bool users.FileLockingValue.enabled: When this value is True, the user
        can lock files in shared directories. When the value is False the user
        can unlock the files they have locked or request to unlock files locked
        by others.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    other = None

    @classmethod
    def enabled(cls, val):
        """
        Create an instance of this class set to the ``enabled`` tag with value
        ``val``.

        :param bool val:
        :rtype: FileLockingValue
        """
        return cls('enabled', val)

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def get_enabled(self):
        """
        When this value is True, the user can lock files in shared directories.
        When the value is False the user can unlock the files they have locked
        or request to unlock files locked by others.

        Only call this if :meth:`is_enabled` is true.

        :rtype: bool
        """
        if not self.is_enabled():
            raise AttributeError("tag 'enabled' not set")
        return self._value

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(FileLockingValue, self)._process_custom_annotations(annotation_type, field_path, processor)

FileLockingValue_validator = bv.Union(FileLockingValue)

class FullAccount(Account):
    """
    Detailed information about the current user's account.

    :ivar users.FullAccount.country: The user's two-letter country code, if
        available. Country codes are based on `ISO 3166-1
        <http://en.wikipedia.org/wiki/ISO_3166-1>`_.
    :ivar users.FullAccount.locale: The language that the user specified. Locale
        tags will be `IETF language tags
        <http://en.wikipedia.org/wiki/IETF_language_tag>`_.
    :ivar users.FullAccount.referral_link: The user's `referral link
        <https://www.dropbox.com/referrals>`_.
    :ivar users.FullAccount.team: If this account is a member of a team,
        information about that team.
    :ivar users.FullAccount.team_member_id: This account's unique team member
        id. This field will only be present if ``team`` is present.
    :ivar users.FullAccount.is_paired: Whether the user has a personal and work
        account. If the current account is personal, then ``team`` will always
        be None, but ``is_paired`` will indicate if a work account is linked.
    :ivar users.FullAccount.account_type: What type of account this user has.
    :ivar users.FullAccount.root_info: The root info for this account.
    """

    __slots__ = [
        '_country_value',
        '_locale_value',
        '_referral_link_value',
        '_team_value',
        '_team_member_id_value',
        '_is_paired_value',
        '_account_type_value',
        '_root_info_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 account_id=None,
                 name=None,
                 email=None,
                 email_verified=None,
                 disabled=None,
                 locale=None,
                 referral_link=None,
                 is_paired=None,
                 account_type=None,
                 root_info=None,
                 profile_photo_url=None,
                 country=None,
                 team=None,
                 team_member_id=None):
        super(FullAccount, self).__init__(account_id,
                                          name,
                                          email,
                                          email_verified,
                                          disabled,
                                          profile_photo_url)
        self._country_value = bb.NOT_SET
        self._locale_value = bb.NOT_SET
        self._referral_link_value = bb.NOT_SET
        self._team_value = bb.NOT_SET
        self._team_member_id_value = bb.NOT_SET
        self._is_paired_value = bb.NOT_SET
        self._account_type_value = bb.NOT_SET
        self._root_info_value = bb.NOT_SET
        if country is not None:
            self.country = country
        if locale is not None:
            self.locale = locale
        if referral_link is not None:
            self.referral_link = referral_link
        if team is not None:
            self.team = team
        if team_member_id is not None:
            self.team_member_id = team_member_id
        if is_paired is not None:
            self.is_paired = is_paired
        if account_type is not None:
            self.account_type = account_type
        if root_info is not None:
            self.root_info = root_info

    # Instance attribute type: str (validator is set below)
    country = bb.Attribute("country", nullable=True)

    # Instance attribute type: str (validator is set below)
    locale = bb.Attribute("locale")

    # Instance attribute type: str (validator is set below)
    referral_link = bb.Attribute("referral_link")

    # Instance attribute type: FullTeam (validator is set below)
    team = bb.Attribute("team", nullable=True, user_defined=True)

    # Instance attribute type: str (validator is set below)
    team_member_id = bb.Attribute("team_member_id", nullable=True)

    # Instance attribute type: bool (validator is set below)
    is_paired = bb.Attribute("is_paired")

    # Instance attribute type: users_common.AccountType (validator is set below)
    account_type = bb.Attribute("account_type", user_defined=True)

    # Instance attribute type: common.RootInfo (validator is set below)
    root_info = bb.Attribute("root_info", user_defined=True)

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(FullAccount, self)._process_custom_annotations(annotation_type, field_path, processor)

FullAccount_validator = bv.Struct(FullAccount)

class Team(bb.Struct):
    """
    Information about a team.

    :ivar users.Team.id: The team's unique ID.
    :ivar users.Team.name: The name of the team.
    """

    __slots__ = [
        '_id_value',
        '_name_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 id=None,
                 name=None):
        self._id_value = bb.NOT_SET
        self._name_value = bb.NOT_SET
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name

    # Instance attribute type: str (validator is set below)
    id = bb.Attribute("id")

    # Instance attribute type: str (validator is set below)
    name = bb.Attribute("name")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(Team, self)._process_custom_annotations(annotation_type, field_path, processor)

Team_validator = bv.Struct(Team)

class FullTeam(Team):
    """
    Detailed information about a team.

    :ivar users.FullTeam.sharing_policies: Team policies governing sharing.
    :ivar users.FullTeam.office_addin_policy: Team policy governing the use of
        the Office Add-In.
    """

    __slots__ = [
        '_sharing_policies_value',
        '_office_addin_policy_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 id=None,
                 name=None,
                 sharing_policies=None,
                 office_addin_policy=None):
        super(FullTeam, self).__init__(id,
                                       name)
        self._sharing_policies_value = bb.NOT_SET
        self._office_addin_policy_value = bb.NOT_SET
        if sharing_policies is not None:
            self.sharing_policies = sharing_policies
        if office_addin_policy is not None:
            self.office_addin_policy = office_addin_policy

    # Instance attribute type: team_policies.TeamSharingPolicies (validator is set below)
    sharing_policies = bb.Attribute("sharing_policies", user_defined=True)

    # Instance attribute type: team_policies.OfficeAddInPolicy (validator is set below)
    office_addin_policy = bb.Attribute("office_addin_policy", user_defined=True)

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(FullTeam, self)._process_custom_annotations(annotation_type, field_path, processor)

FullTeam_validator = bv.Struct(FullTeam)

class GetAccountArg(bb.Struct):
    """
    :ivar users.GetAccountArg.account_id: A user's account identifier.
    """

    __slots__ = [
        '_account_id_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 account_id=None):
        self._account_id_value = bb.NOT_SET
        if account_id is not None:
            self.account_id = account_id

    # Instance attribute type: str (validator is set below)
    account_id = bb.Attribute("account_id")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(GetAccountArg, self)._process_custom_annotations(annotation_type, field_path, processor)

GetAccountArg_validator = bv.Struct(GetAccountArg)

class GetAccountBatchArg(bb.Struct):
    """
    :ivar users.GetAccountBatchArg.account_ids: List of user account
        identifiers.  Should not contain any duplicate account IDs.
    """

    __slots__ = [
        '_account_ids_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 account_ids=None):
        self._account_ids_value = bb.NOT_SET
        if account_ids is not None:
            self.account_ids = account_ids

    # Instance attribute type: list of [str] (validator is set below)
    account_ids = bb.Attribute("account_ids")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(GetAccountBatchArg, self)._process_custom_annotations(annotation_type, field_path, processor)

GetAccountBatchArg_validator = bv.Struct(GetAccountBatchArg)

class GetAccountBatchError(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar str users.GetAccountBatchError.no_account: The value is an account ID
        specified in :field:`GetAccountBatchArg.account_ids` that does not
        exist.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    other = None

    @classmethod
    def no_account(cls, val):
        """
        Create an instance of this class set to the ``no_account`` tag with
        value ``val``.

        :param str val:
        :rtype: GetAccountBatchError
        """
        return cls('no_account', val)

    def is_no_account(self):
        """
        Check if the union tag is ``no_account``.

        :rtype: bool
        """
        return self._tag == 'no_account'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def get_no_account(self):
        """
        The value is an account ID specified in
        ``GetAccountBatchArg.account_ids`` that does not exist.

        Only call this if :meth:`is_no_account` is true.

        :rtype: str
        """
        if not self.is_no_account():
            raise AttributeError("tag 'no_account' not set")
        return self._value

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(GetAccountBatchError, self)._process_custom_annotations(annotation_type, field_path, processor)

GetAccountBatchError_validator = bv.Union(GetAccountBatchError)

class GetAccountError(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar users.GetAccountError.no_account: The specified
        ``GetAccountArg.account_id`` does not exist.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    no_account = None
    # Attribute is overwritten below the class definition
    other = None

    def is_no_account(self):
        """
        Check if the union tag is ``no_account``.

        :rtype: bool
        """
        return self._tag == 'no_account'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(GetAccountError, self)._process_custom_annotations(annotation_type, field_path, processor)

GetAccountError_validator = bv.Union(GetAccountError)

class IndividualSpaceAllocation(bb.Struct):
    """
    :ivar users.IndividualSpaceAllocation.allocated: The total space allocated
        to the user's account (bytes).
    """

    __slots__ = [
        '_allocated_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 allocated=None):
        self._allocated_value = bb.NOT_SET
        if allocated is not None:
            self.allocated = allocated

    # Instance attribute type: int (validator is set below)
    allocated = bb.Attribute("allocated")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(IndividualSpaceAllocation, self)._process_custom_annotations(annotation_type, field_path, processor)

IndividualSpaceAllocation_validator = bv.Struct(IndividualSpaceAllocation)

class Name(bb.Struct):
    """
    Representations for a person's name to assist with internationalization.

    :ivar users.Name.given_name: Also known as a first name.
    :ivar users.Name.surname: Also known as a last name or family name.
    :ivar users.Name.familiar_name: Locale-dependent name. In the US, a person's
        familiar name is their ``given_name``, but elsewhere, it could be any
        combination of a person's ``given_name`` and ``surname``.
    :ivar users.Name.display_name: A name that can be used directly to represent
        the name of a user's Dropbox account.
    :ivar users.Name.abbreviated_name: An abbreviated form of the person's name.
        Their initials in most locales.
    """

    __slots__ = [
        '_given_name_value',
        '_surname_value',
        '_familiar_name_value',
        '_display_name_value',
        '_abbreviated_name_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 given_name=None,
                 surname=None,
                 familiar_name=None,
                 display_name=None,
                 abbreviated_name=None):
        self._given_name_value = bb.NOT_SET
        self._surname_value = bb.NOT_SET
        self._familiar_name_value = bb.NOT_SET
        self._display_name_value = bb.NOT_SET
        self._abbreviated_name_value = bb.NOT_SET
        if given_name is not None:
            self.given_name = given_name
        if surname is not None:
            self.surname = surname
        if familiar_name is not None:
            self.familiar_name = familiar_name
        if display_name is not None:
            self.display_name = display_name
        if abbreviated_name is not None:
            self.abbreviated_name = abbreviated_name

    # Instance attribute type: str (validator is set below)
    given_name = bb.Attribute("given_name")

    # Instance attribute type: str (validator is set below)
    surname = bb.Attribute("surname")

    # Instance attribute type: str (validator is set below)
    familiar_name = bb.Attribute("familiar_name")

    # Instance attribute type: str (validator is set below)
    display_name = bb.Attribute("display_name")

    # Instance attribute type: str (validator is set below)
    abbreviated_name = bb.Attribute("abbreviated_name")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(Name, self)._process_custom_annotations(annotation_type, field_path, processor)

Name_validator = bv.Struct(Name)

class PaperAsFilesValue(bb.Union):
    """
    The value for ``UserFeature.paper_as_files``.

    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar bool users.PaperAsFilesValue.enabled: When this value is true, the
        user's Paper docs are accessible in Dropbox with the .paper extension
        and must be accessed via the /files endpoints.  When this value is
        false, the user's Paper docs are stored separate from Dropbox files and
        folders and should be accessed via the /paper endpoints.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    other = None

    @classmethod
    def enabled(cls, val):
        """
        Create an instance of this class set to the ``enabled`` tag with value
        ``val``.

        :param bool val:
        :rtype: PaperAsFilesValue
        """
        return cls('enabled', val)

    def is_enabled(self):
        """
        Check if the union tag is ``enabled``.

        :rtype: bool
        """
        return self._tag == 'enabled'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def get_enabled(self):
        """
        When this value is true, the user's Paper docs are accessible in Dropbox
        with the .paper extension and must be accessed via the /files endpoints.
        When this value is false, the user's Paper docs are stored separate from
        Dropbox files and folders and should be accessed via the /paper
        endpoints.

        Only call this if :meth:`is_enabled` is true.

        :rtype: bool
        """
        if not self.is_enabled():
            raise AttributeError("tag 'enabled' not set")
        return self._value

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(PaperAsFilesValue, self)._process_custom_annotations(annotation_type, field_path, processor)

PaperAsFilesValue_validator = bv.Union(PaperAsFilesValue)

class SpaceAllocation(bb.Union):
    """
    Space is allocated differently based on the type of account.

    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar IndividualSpaceAllocation SpaceAllocation.individual: The user's space
        allocation applies only to their individual account.
    :ivar TeamSpaceAllocation SpaceAllocation.team: The user shares space with
        other members of their team.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    other = None

    @classmethod
    def individual(cls, val):
        """
        Create an instance of this class set to the ``individual`` tag with
        value ``val``.

        :param IndividualSpaceAllocation val:
        :rtype: SpaceAllocation
        """
        return cls('individual', val)

    @classmethod
    def team(cls, val):
        """
        Create an instance of this class set to the ``team`` tag with value
        ``val``.

        :param TeamSpaceAllocation val:
        :rtype: SpaceAllocation
        """
        return cls('team', val)

    def is_individual(self):
        """
        Check if the union tag is ``individual``.

        :rtype: bool
        """
        return self._tag == 'individual'

    def is_team(self):
        """
        Check if the union tag is ``team``.

        :rtype: bool
        """
        return self._tag == 'team'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def get_individual(self):
        """
        The user's space allocation applies only to their individual account.

        Only call this if :meth:`is_individual` is true.

        :rtype: IndividualSpaceAllocation
        """
        if not self.is_individual():
            raise AttributeError("tag 'individual' not set")
        return self._value

    def get_team(self):
        """
        The user shares space with other members of their team.

        Only call this if :meth:`is_team` is true.

        :rtype: TeamSpaceAllocation
        """
        if not self.is_team():
            raise AttributeError("tag 'team' not set")
        return self._value

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(SpaceAllocation, self)._process_custom_annotations(annotation_type, field_path, processor)

SpaceAllocation_validator = bv.Union(SpaceAllocation)

class SpaceUsage(bb.Struct):
    """
    Information about a user's space usage and quota.

    :ivar users.SpaceUsage.used: The user's total space usage (bytes).
    :ivar users.SpaceUsage.allocation: The user's space allocation.
    """

    __slots__ = [
        '_used_value',
        '_allocation_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 used=None,
                 allocation=None):
        self._used_value = bb.NOT_SET
        self._allocation_value = bb.NOT_SET
        if used is not None:
            self.used = used
        if allocation is not None:
            self.allocation = allocation

    # Instance attribute type: int (validator is set below)
    used = bb.Attribute("used")

    # Instance attribute type: SpaceAllocation (validator is set below)
    allocation = bb.Attribute("allocation", user_defined=True)

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(SpaceUsage, self)._process_custom_annotations(annotation_type, field_path, processor)

SpaceUsage_validator = bv.Struct(SpaceUsage)

class TeamSpaceAllocation(bb.Struct):
    """
    :ivar users.TeamSpaceAllocation.used: The total space currently used by the
        user's team (bytes).
    :ivar users.TeamSpaceAllocation.allocated: The total space allocated to the
        user's team (bytes).
    :ivar users.TeamSpaceAllocation.user_within_team_space_allocated: The total
        space allocated to the user within its team allocated space (0 means
        that no restriction is imposed on the user's quota within its team).
    :ivar users.TeamSpaceAllocation.user_within_team_space_limit_type: The type
        of the space limit imposed on the team member (off, alert_only,
        stop_sync).
    :ivar users.TeamSpaceAllocation.user_within_team_space_used_cached: An
        accurate cached calculation of a team member's total space usage
        (bytes).
    """

    __slots__ = [
        '_used_value',
        '_allocated_value',
        '_user_within_team_space_allocated_value',
        '_user_within_team_space_limit_type_value',
        '_user_within_team_space_used_cached_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 used=None,
                 allocated=None,
                 user_within_team_space_allocated=None,
                 user_within_team_space_limit_type=None,
                 user_within_team_space_used_cached=None):
        self._used_value = bb.NOT_SET
        self._allocated_value = bb.NOT_SET
        self._user_within_team_space_allocated_value = bb.NOT_SET
        self._user_within_team_space_limit_type_value = bb.NOT_SET
        self._user_within_team_space_used_cached_value = bb.NOT_SET
        if used is not None:
            self.used = used
        if allocated is not None:
            self.allocated = allocated
        if user_within_team_space_allocated is not None:
            self.user_within_team_space_allocated = user_within_team_space_allocated
        if user_within_team_space_limit_type is not None:
            self.user_within_team_space_limit_type = user_within_team_space_limit_type
        if user_within_team_space_used_cached is not None:
            self.user_within_team_space_used_cached = user_within_team_space_used_cached

    # Instance attribute type: int (validator is set below)
    used = bb.Attribute("used")

    # Instance attribute type: int (validator is set below)
    allocated = bb.Attribute("allocated")

    # Instance attribute type: int (validator is set below)
    user_within_team_space_allocated = bb.Attribute("user_within_team_space_allocated")

    # Instance attribute type: team_common.MemberSpaceLimitType (validator is set below)
    user_within_team_space_limit_type = bb.Attribute("user_within_team_space_limit_type", user_defined=True)

    # Instance attribute type: int (validator is set below)
    user_within_team_space_used_cached = bb.Attribute("user_within_team_space_used_cached")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(TeamSpaceAllocation, self)._process_custom_annotations(annotation_type, field_path, processor)

TeamSpaceAllocation_validator = bv.Struct(TeamSpaceAllocation)

class UserFeature(bb.Union):
    """
    A set of features that a Dropbox User account may have configured.

    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar users.UserFeature.paper_as_files: This feature contains information
        about how the user's Paper files are stored.
    :ivar users.UserFeature.file_locking: This feature allows users to lock
        files in order to restrict other users from editing them.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    paper_as_files = None
    # Attribute is overwritten below the class definition
    file_locking = None
    # Attribute is overwritten below the class definition
    other = None

    def is_paper_as_files(self):
        """
        Check if the union tag is ``paper_as_files``.

        :rtype: bool
        """
        return self._tag == 'paper_as_files'

    def is_file_locking(self):
        """
        Check if the union tag is ``file_locking``.

        :rtype: bool
        """
        return self._tag == 'file_locking'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(UserFeature, self)._process_custom_annotations(annotation_type, field_path, processor)

UserFeature_validator = bv.Union(UserFeature)

class UserFeatureValue(bb.Union):
    """
    Values that correspond to entries in :class:`UserFeature`.

    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    other = None

    @classmethod
    def paper_as_files(cls, val):
        """
        Create an instance of this class set to the ``paper_as_files`` tag with
        value ``val``.

        :param PaperAsFilesValue val:
        :rtype: UserFeatureValue
        """
        return cls('paper_as_files', val)

    @classmethod
    def file_locking(cls, val):
        """
        Create an instance of this class set to the ``file_locking`` tag with
        value ``val``.

        :param FileLockingValue val:
        :rtype: UserFeatureValue
        """
        return cls('file_locking', val)

    def is_paper_as_files(self):
        """
        Check if the union tag is ``paper_as_files``.

        :rtype: bool
        """
        return self._tag == 'paper_as_files'

    def is_file_locking(self):
        """
        Check if the union tag is ``file_locking``.

        :rtype: bool
        """
        return self._tag == 'file_locking'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def get_paper_as_files(self):
        """
        Only call this if :meth:`is_paper_as_files` is true.

        :rtype: PaperAsFilesValue
        """
        if not self.is_paper_as_files():
            raise AttributeError("tag 'paper_as_files' not set")
        return self._value

    def get_file_locking(self):
        """
        Only call this if :meth:`is_file_locking` is true.

        :rtype: FileLockingValue
        """
        if not self.is_file_locking():
            raise AttributeError("tag 'file_locking' not set")
        return self._value

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(UserFeatureValue, self)._process_custom_annotations(annotation_type, field_path, processor)

UserFeatureValue_validator = bv.Union(UserFeatureValue)

class UserFeaturesGetValuesBatchArg(bb.Struct):
    """
    :ivar users.UserFeaturesGetValuesBatchArg.features: A list of features in
        :class:`UserFeature`. If the list is empty, this route will return
        :class:`UserFeaturesGetValuesBatchError`.
    """

    __slots__ = [
        '_features_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 features=None):
        self._features_value = bb.NOT_SET
        if features is not None:
            self.features = features

    # Instance attribute type: list of [UserFeature] (validator is set below)
    features = bb.Attribute("features")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(UserFeaturesGetValuesBatchArg, self)._process_custom_annotations(annotation_type, field_path, processor)

UserFeaturesGetValuesBatchArg_validator = bv.Struct(UserFeaturesGetValuesBatchArg)

class UserFeaturesGetValuesBatchError(bb.Union):
    """
    This class acts as a tagged union. Only one of the ``is_*`` methods will
    return true. To get the associated value of a tag (if one exists), use the
    corresponding ``get_*`` method.

    :ivar users.UserFeaturesGetValuesBatchError.empty_features_list: At least
        one :class:`UserFeature` must be included in the
        :class:`UserFeaturesGetValuesBatchArg`.features list.
    """

    _catch_all = 'other'
    # Attribute is overwritten below the class definition
    empty_features_list = None
    # Attribute is overwritten below the class definition
    other = None

    def is_empty_features_list(self):
        """
        Check if the union tag is ``empty_features_list``.

        :rtype: bool
        """
        return self._tag == 'empty_features_list'

    def is_other(self):
        """
        Check if the union tag is ``other``.

        :rtype: bool
        """
        return self._tag == 'other'

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(UserFeaturesGetValuesBatchError, self)._process_custom_annotations(annotation_type, field_path, processor)

UserFeaturesGetValuesBatchError_validator = bv.Union(UserFeaturesGetValuesBatchError)

class UserFeaturesGetValuesBatchResult(bb.Struct):

    __slots__ = [
        '_values_value',
    ]

    _has_required_fields = True

    def __init__(self,
                 values=None):
        self._values_value = bb.NOT_SET
        if values is not None:
            self.values = values

    # Instance attribute type: list of [UserFeatureValue] (validator is set below)
    values = bb.Attribute("values")

    def _process_custom_annotations(self, annotation_type, field_path, processor):
        super(UserFeaturesGetValuesBatchResult, self)._process_custom_annotations(annotation_type, field_path, processor)

UserFeaturesGetValuesBatchResult_validator = bv.Struct(UserFeaturesGetValuesBatchResult)

GetAccountBatchResult_validator = bv.List(BasicAccount_validator)
Account.account_id.validator = users_common.AccountId_validator
Account.name.validator = Name_validator
Account.email.validator = bv.String()
Account.email_verified.validator = bv.Boolean()
Account.profile_photo_url.validator = bv.Nullable(bv.String())
Account.disabled.validator = bv.Boolean()
Account._all_field_names_ = set([
    'account_id',
    'name',
    'email',
    'email_verified',
    'profile_photo_url',
    'disabled',
])
Account._all_fields_ = [
    ('account_id', Account.account_id.validator),
    ('name', Account.name.validator),
    ('email', Account.email.validator),
    ('email_verified', Account.email_verified.validator),
    ('profile_photo_url', Account.profile_photo_url.validator),
    ('disabled', Account.disabled.validator),
]

BasicAccount.is_teammate.validator = bv.Boolean()
BasicAccount.team_member_id.validator = bv.Nullable(bv.String())
BasicAccount._all_field_names_ = Account._all_field_names_.union(set([
    'is_teammate',
    'team_member_id',
]))
BasicAccount._all_fields_ = Account._all_fields_ + [
    ('is_teammate', BasicAccount.is_teammate.validator),
    ('team_member_id', BasicAccount.team_member_id.validator),
]

FileLockingValue._enabled_validator = bv.Boolean()
FileLockingValue._other_validator = bv.Void()
FileLockingValue._tagmap = {
    'enabled': FileLockingValue._enabled_validator,
    'other': FileLockingValue._other_validator,
}

FileLockingValue.other = FileLockingValue('other')

FullAccount.country.validator = bv.Nullable(bv.String(min_length=2, max_length=2))
FullAccount.locale.validator = bv.String(min_length=2)
FullAccount.referral_link.validator = bv.String()
FullAccount.team.validator = bv.Nullable(FullTeam_validator)
FullAccount.team_member_id.validator = bv.Nullable(bv.String())
FullAccount.is_paired.validator = bv.Boolean()
FullAccount.account_type.validator = users_common.AccountType_validator
FullAccount.root_info.validator = common.RootInfo_validator
FullAccount._all_field_names_ = Account._all_field_names_.union(set([
    'country',
    'locale',
    'referral_link',
    'team',
    'team_member_id',
    'is_paired',
    'account_type',
    'root_info',
]))
FullAccount._all_fields_ = Account._all_fields_ + [
    ('country', FullAccount.country.validator),
    ('locale', FullAccount.locale.validator),
    ('referral_link', FullAccount.referral_link.validator),
    ('team', FullAccount.team.validator),
    ('team_member_id', FullAccount.team_member_id.validator),
    ('is_paired', FullAccount.is_paired.validator),
    ('account_type', FullAccount.account_type.validator),
    ('root_info', FullAccount.root_info.validator),
]

Team.id.validator = bv.String()
Team.name.validator = bv.String()
Team._all_field_names_ = set([
    'id',
    'name',
])
Team._all_fields_ = [
    ('id', Team.id.validator),
    ('name', Team.name.validator),
]

FullTeam.sharing_policies.validator = team_policies.TeamSharingPolicies_validator
FullTeam.office_addin_policy.validator = team_policies.OfficeAddInPolicy_validator
FullTeam._all_field_names_ = Team._all_field_names_.union(set([
    'sharing_policies',
    'office_addin_policy',
]))
FullTeam._all_fields_ = Team._all_fields_ + [
    ('sharing_policies', FullTeam.sharing_policies.validator),
    ('office_addin_policy', FullTeam.office_addin_policy.validator),
]

GetAccountArg.account_id.validator = users_common.AccountId_validator
GetAccountArg._all_field_names_ = set(['account_id'])
GetAccountArg._all_fields_ = [('account_id', GetAccountArg.account_id.validator)]

GetAccountBatchArg.account_ids.validator = bv.List(users_common.AccountId_validator, min_items=1)
GetAccountBatchArg._all_field_names_ = set(['account_ids'])
GetAccountBatchArg._all_fields_ = [('account_ids', GetAccountBatchArg.account_ids.validator)]

GetAccountBatchError._no_account_validator = users_common.AccountId_validator
GetAccountBatchError._other_validator = bv.Void()
GetAccountBatchError._tagmap = {
    'no_account': GetAccountBatchError._no_account_validator,
    'other': GetAccountBatchError._other_validator,
}

GetAccountBatchError.other = GetAccountBatchError('other')

GetAccountError._no_account_validator = bv.Void()
GetAccountError._other_validator = bv.Void()
GetAccountError._tagmap = {
    'no_account': GetAccountError._no_account_validator,
    'other': GetAccountError._other_validator,
}

GetAccountError.no_account = GetAccountError('no_account')
GetAccountError.other = GetAccountError('other')

IndividualSpaceAllocation.allocated.validator = bv.UInt64()
IndividualSpaceAllocation._all_field_names_ = set(['allocated'])
IndividualSpaceAllocation._all_fields_ = [('allocated', IndividualSpaceAllocation.allocated.validator)]

Name.given_name.validator = bv.String()
Name.surname.validator = bv.String()
Name.familiar_name.validator = bv.String()
Name.display_name.validator = bv.String()
Name.abbreviated_name.validator = bv.String()
Name._all_field_names_ = set([
    'given_name',
    'surname',
    'familiar_name',
    'display_name',
    'abbreviated_name',
])
Name._all_fields_ = [
    ('given_name', Name.given_name.validator),
    ('surname', Name.surname.validator),
    ('familiar_name', Name.familiar_name.validator),
    ('display_name', Name.display_name.validator),
    ('abbreviated_name', Name.abbreviated_name.validator),
]

PaperAsFilesValue._enabled_validator = bv.Boolean()
PaperAsFilesValue._other_validator = bv.Void()
PaperAsFilesValue._tagmap = {
    'enabled': PaperAsFilesValue._enabled_validator,
    'other': PaperAsFilesValue._other_validator,
}

PaperAsFilesValue.other = PaperAsFilesValue('other')

SpaceAllocation._individual_validator = IndividualSpaceAllocation_validator
SpaceAllocation._team_validator = TeamSpaceAllocation_validator
SpaceAllocation._other_validator = bv.Void()
SpaceAllocation._tagmap = {
    'individual': SpaceAllocation._individual_validator,
    'team': SpaceAllocation._team_validator,
    'other': SpaceAllocation._other_validator,
}

SpaceAllocation.other = SpaceAllocation('other')

SpaceUsage.used.validator = bv.UInt64()
SpaceUsage.allocation.validator = SpaceAllocation_validator
SpaceUsage._all_field_names_ = set([
    'used',
    'allocation',
])
SpaceUsage._all_fields_ = [
    ('used', SpaceUsage.used.validator),
    ('allocation', SpaceUsage.allocation.validator),
]

TeamSpaceAllocation.used.validator = bv.UInt64()
TeamSpaceAllocation.allocated.validator = bv.UInt64()
TeamSpaceAllocation.user_within_team_space_allocated.validator = bv.UInt64()
TeamSpaceAllocation.user_within_team_space_limit_type.validator = team_common.MemberSpaceLimitType_validator
TeamSpaceAllocation.user_within_team_space_used_cached.validator = bv.UInt64()
TeamSpaceAllocation._all_field_names_ = set([
    'used',
    'allocated',
    'user_within_team_space_allocated',
    'user_within_team_space_limit_type',
    'user_within_team_space_used_cached',
])
TeamSpaceAllocation._all_fields_ = [
    ('used', TeamSpaceAllocation.used.validator),
    ('allocated', TeamSpaceAllocation.allocated.validator),
    ('user_within_team_space_allocated', TeamSpaceAllocation.user_within_team_space_allocated.validator),
    ('user_within_team_space_limit_type', TeamSpaceAllocation.user_within_team_space_limit_type.validator),
    ('user_within_team_space_used_cached', TeamSpaceAllocation.user_within_team_space_used_cached.validator),
]

UserFeature._paper_as_files_validator = bv.Void()
UserFeature._file_locking_validator = bv.Void()
UserFeature._other_validator = bv.Void()
UserFeature._tagmap = {
    'paper_as_files': UserFeature._paper_as_files_validator,
    'file_locking': UserFeature._file_locking_validator,
    'other': UserFeature._other_validator,
}

UserFeature.paper_as_files = UserFeature('paper_as_files')
UserFeature.file_locking = UserFeature('file_locking')
UserFeature.other = UserFeature('other')

UserFeatureValue._paper_as_files_validator = PaperAsFilesValue_validator
UserFeatureValue._file_locking_validator = FileLockingValue_validator
UserFeatureValue._other_validator = bv.Void()
UserFeatureValue._tagmap = {
    'paper_as_files': UserFeatureValue._paper_as_files_validator,
    'file_locking': UserFeatureValue._file_locking_validator,
    'other': UserFeatureValue._other_validator,
}

UserFeatureValue.other = UserFeatureValue('other')

UserFeaturesGetValuesBatchArg.features.validator = bv.List(UserFeature_validator)
UserFeaturesGetValuesBatchArg._all_field_names_ = set(['features'])
UserFeaturesGetValuesBatchArg._all_fields_ = [('features', UserFeaturesGetValuesBatchArg.features.validator)]

UserFeaturesGetValuesBatchError._empty_features_list_validator = bv.Void()
UserFeaturesGetValuesBatchError._other_validator = bv.Void()
UserFeaturesGetValuesBatchError._tagmap = {
    'empty_features_list': UserFeaturesGetValuesBatchError._empty_features_list_validator,
    'other': UserFeaturesGetValuesBatchError._other_validator,
}

UserFeaturesGetValuesBatchError.empty_features_list = UserFeaturesGetValuesBatchError('empty_features_list')
UserFeaturesGetValuesBatchError.other = UserFeaturesGetValuesBatchError('other')

UserFeaturesGetValuesBatchResult.values.validator = bv.List(UserFeatureValue_validator)
UserFeaturesGetValuesBatchResult._all_field_names_ = set(['values'])
UserFeaturesGetValuesBatchResult._all_fields_ = [('values', UserFeaturesGetValuesBatchResult.values.validator)]

features_get_values = bb.Route(
    'features/get_values',
    1,
    False,
    UserFeaturesGetValuesBatchArg_validator,
    UserFeaturesGetValuesBatchResult_validator,
    UserFeaturesGetValuesBatchError_validator,
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)
get_account = bb.Route(
    'get_account',
    1,
    False,
    GetAccountArg_validator,
    BasicAccount_validator,
    GetAccountError_validator,
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)
get_account_batch = bb.Route(
    'get_account_batch',
    1,
    False,
    GetAccountBatchArg_validator,
    GetAccountBatchResult_validator,
    GetAccountBatchError_validator,
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)
get_current_account = bb.Route(
    'get_current_account',
    1,
    False,
    bv.Void(),
    FullAccount_validator,
    bv.Void(),
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)
get_space_usage = bb.Route(
    'get_space_usage',
    1,
    False,
    bv.Void(),
    SpaceUsage_validator,
    bv.Void(),
    {'auth': 'user',
     'host': 'api',
     'style': 'rpc'},
)

ROUTES = {
    'features/get_values': features_get_values,
    'get_account': get_account,
    'get_account_batch': get_account_batch,
    'get_current_account': get_current_account,
    'get_space_usage': get_space_usage,
}

